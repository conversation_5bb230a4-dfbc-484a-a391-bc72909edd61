<?php
require '/var/www/nginx/php/vendor/autoload.php';
use Aws\Ses\SesClient;
$client = SesClient::factory(array(
    'profile' => 'ses',
    'region'  => 'us-east-1'
));
header('Content-Type: application/json');
$srcIp = $_SERVER['REMOTE_ADDR'];
$ret = array(
  'ok' => 0,
  'ip' => $srcIp,
  // 'sub' => $_POST["subject"],
  // 'from' => $_POST["from"],
  // 'to' => $_POST["to"]
);
// echo json_encode($ret);
// exit(0);
// TODO:$_SERVER['HTTP_X_FORWARDED_FOR'] fake ip
// rainIp, testServer, Server, chinaServer
// TODO: update ips for new test servers
// rain: *************, *************
$ips = array("*************", "************", "************");
if (!in_array($srcIp, $ips)) {
    $ret['err'] = "Not Supported";
    echo json_encode($ret);
    exit(1);
}
if (!isset($_POST["subject"]) || !isset($_POST["to"]) || !isset($_POST["html"]) || !isset($_POST["from"])) {
  $ret['err'] = "No data!";
  echo json_encode($ret);
  exit(1);
}
$replyTo = '<EMAIL>';
if (isset($_POST["replyTo"])){
  $replyTo = $_POST["replyTo"];
}
$result = $client->sendEmail(array(
    // Source is required
    'Source' => $_POST["from"],
    // Destination is required  or '<EMAIL>'
    'Destination' => array(
        'ToAddresses' => array($_POST["to"])
    ),
    // Message is required
    'Message' => array(
        // Subject is required
        'Subject' => array(
            // Data is required
            'Data' => $_POST["subject"],
            'Charset' => 'utf-8',
        ),
        // Body is required
        'Body' => array(
          'Text' => array(
                // Data is required
                'Data' => $_POST["text"],
                'Charset' => 'utf-8',
              ),
          'Html' => array(
              // Data is required
              'Data' => $_POST["html"],
              'Charset' => 'utf-8',
          ),
      ),
  ),
  'ReplyToAddresses' => array($replyTo),
  'ConfigurationSetName' => 'RM_SES',
));
$ret['ok'] = 1;
$ret['ret'] = $result;
echo json_encode($ret);
exit();
?>
