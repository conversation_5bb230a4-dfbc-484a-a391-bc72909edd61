CREATE TABLE `stigmatized` (
  `id` int(11) NOT NULL,
  `addr` varchar(256) NOT NULL,
  `city` varchar(128) DEFAULT NULL,
  `prov` varchar(128) DEFAULT NULL,
  `zip` varchar(12) DEFAULT NULL,
  `cnty` varchar(128) DEFAULT NULL,
  `ptp` varchar(128) DEFAULT NULL COMMENT 'Property Type',
  `thumb` varchar(256) DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL,
  `title` varchar(256) DEFAULT NULL,
  `m` text,
  `ref1` varchar(256) DEFAULT NULL,
  `ref2` varchar(256) DEFAULT NULL,
  `dt` varchar(45) DEFAULT NULL,
  `lat` decimal(11,7) DEFAULT NULL,
  `lng` decimal(11,7) DEFAULT NULL,
  `llq` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `latlng` (`lat`,`lng`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


LOAD DATA LOCAL INFILE '/Users/<USER>/Documents/RealMaster/社区数据/HouseCreep20160326.csv' INTO TABLE stigmatized  FIELDS TERMINATED BY ',' ENCLOSED BY '"' LINES TERMINATED BY 'NEWLINE,';
UPDATE IGNORE stigmatized SET dt = STR_TO_DATE( SUBSTRING(m,1, LOCATE(' ', m, (LOCATE(', ',m) + 2) )),'%M %D, %Y') where LENGTH(SUBSTRING( m,1, LOCATE(' ', m, (LOCATE(', ',m) + 2) ))) >= 12 and LENGTH(SUBSTRING( m,1, LOCATE(' ', m, (LOCATE(', ',m) + 2) ))) <= 21;
UPDATE IGNORE stigmatized SET dt = STR_TO_DATE( CONCAT(SUBSTRING(m,1, LOCATE(' ', m)),'-01-01'),'%Y-%m-%d') where LOCATE(' ', m) = 5 AND SUBSTRING(m,1,4) REGEXP '[0-9]{4}';

# remove duplicated records
DELETE a FROM stigmatized a LEFT JOIN (
 SELECT max(id) maxid, dt, title, addr, city, zip, lat, lng, ptp, m
 FROM stigmatized GROUP BY dt, title, addr, city, zip, lat, lng, ptp, m ) b
 ON a.id = b.maxid AND a.dt = b.dt AND a.title = b.title AND a.addr = b.addr AND a.city = b.city AND
 a.zip = b.zip AND a.ptp = b.ptp AND a.m = b.m
 WHERE b.maxid IS NULL;
