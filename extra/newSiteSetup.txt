Translation
('Buy House') -> 买房
('Rent House') -> 租房
('Asking Price','prop') -> 房价
('Sold Price','prop') -> 成交价

Nginx Route for nodejs
chat|www|login|logout|1.5|projects|prop|wpnews|privateSchools|app-download|admin|wepage|mylisting|setting|file|forum|edm|stigma|resetPasswd|verifyEmail|mp|sitemap.xml|sitemap.txt|sitemap-|for-|sold|search|html|event

location ~* ^/(js|img|css|fonts|web|libs) {
    root {path}/realmaster-appweb/src/webroot/public;
}

Wordpress Setup
1. Git clone wordpress repo
2. Setup wordpress with basic Mysql
3. Go to https://d6w.realmaster.cn and login to dashboard
4. Go to plugin All-in-One WP Migration and export database only
5. Import database to new WP

Sync posts from nodejs to Wordpress
1-1. Login to wp, create one category APP, one tag APP
1-2. <PERSON><PERSON> to APP as admin, edit any post, add a tag APP (uppercase) to the post
2. Run command ./start.sh batch/forum/pushPostToWordpress.coffee {{host}} {{wpusername}} {{wppassword}}

Sync post from wp to APP
Add API to Setting
https://www.realmaster.com/1.5/forum/wordPressEcho
https://www.realmaster.com/1.5/forum/wordPressCmntEcho

Sync post from APP to wp
1. Login APP as admin, go to profile and click Edit Other Profile
2. Fill in host, username, password
3. Open post from forum in APP, click control on bottom right, 'refresh wordpress tags'/'push to wordpress'