mongoimport -d chomeca -u chomeca_u -p chomeca_p -c news --file news.json  --json<PERSON><PERSON>y

mongorestore --ssl --sslCAFile=/Users/<USER>/Dropbox/RealMaster/RM_Dev/FredX/appweb/src/profiles/china/ca.crt --sslPEMKeyFile=/Users/<USER>/Dropbox/RealMaster/RM_Dev/FredX/appweb/src/profiles/china/client.pem --sslAllowInvalidCertificates -d vow vow/

use admin
db.createUser(
  {
    user: "dbadm",
    pwd: "dbadm",
    roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
  }
)


use chomeca
db.createUser(
    {
      user: "chomeca_u",
      pwd: "chomeca_p",
      roles: [
         { role: "readWrite", db: "chomeca" }
      ]
    }
)

use vow
db.createUser(
    {
      user: "vow_u",
      pwd: "vow_p",
      roles: [
         { role: "readWrite", db: "vow" }
      ]
    }
)

mongodump -d db -u user --gzip --archive=db.gz
mongorestore -d db --drop --noIndexRestore --numParallelCollections 1 --gzip --archive=db.gz

mongodump --db d3listing --collection properties -u d -p d --gzip -q '{mt:{"$gt":{"$date":"2018-01-01T01:00:00Z"}}}' --out properties