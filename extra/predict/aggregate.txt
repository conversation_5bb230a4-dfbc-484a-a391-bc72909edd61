db.properties.aggregate([
    {$match:{pred_sp:{$exists:1},lst:'Sld',sp:{$gt:10000},src:'TRB',pred_sp:{$ne:0}}},
    {$group:{
        _id:{city:"$city",prov:"$prov",date:new Date()},
        count:{$sum:1},
        stdp:{$avg:{$divide:['$pred_spsd','$pred_sp']}},
        diffp:{$avg:{$divide:[{$abs:{$subtract:['$sp','$pred_sp']}},'$sp']}}
    }},
    {$sort:{count:-1}},
    {$limit:100},
    {$out:'predicts'}
]
)