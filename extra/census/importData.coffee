# coffee me.coffe [folder]
db = require('../../batch/db')
exit = db.exit
puts = console.log
fs = require 'fs'
path = require 'path'
util = require 'util'
csv = require 'fast-csv'
if process.argv.length > 2 and process.argv[0] is 'coffee'
  avgs = process.argv.slice(2)

coll = null
collKeys = null
keys = null
notes = null

findError = (e)->
  console.log e
  exit 1

updateKeys = (cb)->
  collKeys.updateOne {_id:'keys'},{$set:keys},{upsert:true,w:1},(err)->
    if err then return findError err
    cb()

strToKey = (str)->
  return null unless str
  keys.str ?= {}
  keys.ids ?= {}
  keys.col ?= {}
  strShort = str.trim().replace(/[\.|\$|\%]/g,'_')
  return id if id = keys.str[strShort]
  col = strShort.substr(0,1) # get first c
  c = keys.col[col] or {n:0} # get c struncture {n: counter}
  k = col + c.n++
  keys.col[col] = c
  keys.ids[k] = str
  keys.str[strShort] = k
  puts "#{k}:#{str}"
  k

importFile = (file,cb)->
  # -Metadata.CSV notes file
  queue = []
  headers = null
  cTodo = 0
  cDone = 0
  chkToSave = null
  saveObj = null
  toSaveObj = {}
  toDrain = true
  saveObjNotes = (next)->
    puts notes
    collKeys.updateOne {_id:'notes'},{$set:notes},{upsert:true,w:1},(err,ret)->
      if err then return findError err
      next()
  saveObjProv = (next)->
    return next() unless toSaveObj?.geo_code
    process.stdout.write " " + toSaveObj.geo_code
    updateKeys ->
      coll.updateOne {ctuid:toSaveObj.geo_code},{$set:census:toSaveObj.census},{w:1},(err,ret)->
        if err then return findError err
        cDone++
        toSaveObj = {}
        next()
  addFields = (obj)->
    toSaveObj.geo_code = obj.geo_code
    toSaveObj.census ?= {}
    topic = strToKey obj.topic
    character = strToKey obj.characteristic
    if topic and character
      unless toSaveObj.curTopic and toSaveObj.curTopic is topic
        toSaveObj.curTopic = topic
        toSaveObj.census[topic] = {}
      toSaveObj.census[topic][character] = [obj.total,obj.male,obj.female,obj.note]
    #else
      # puts obj
    toDrain = true
    chkToSaveProv()
  _fnFinal = null
  chkToSaveProv = (obj,next)->
    queue.push obj if obj
    if toDrain
      if obj = queue.shift()
        toDrain = false
        if toSaveObj.geo_code isnt obj.geo_code
          #puts "#{toSaveObj.geo_code} <> #{obj.geo_code}"
          return saveObj ->
            addFields obj
            next() if next
        #puts obj
        addFields obj
      else
        _fnFinal() if _fnFinal
  chkToSaveNote = (obj)->
    if obj?.notes and obj.detail
      if /^\d+$/.test obj.notes
        notes[obj.notes] = obj.detail
      else if ["..","...","x"].indexOf obj.notes
        notes.symbols ?= {}
        notes.symbols[obj.notes.replace(/\./g,'_')] = obj.detail
    _fnFinal() if _fnFinal
  puts "Import: #{file}"
  check_done = ->
    _fnFinal = ->
      saveObj ->
        puts ""
        puts "Line:#{cTodo} Object:#{cDone}"
        if fn = cb
          cb = null
          fn()
        else
          throw new Error "Called check_done twice"
    chkToSave()
  dryRun = false
  stream = fs.createReadStream file,{encoding:'ascii'}
  parser = csv.fromStream(stream).on 'data',(d,index)->
    if not headers? # 1st line
      headers = []
      for k in d
        if k
          headers.push k.trim().toLowerCase()
      if headers[0] is 'notes:'
        chkToSave = chkToSaveNote
        saveObj = saveObjNotes
        headers[0] = 'notes'
        headers[1] = "detail"
      else
        # dryRun
        #dryRun = true
        #chkToSave = ->
        #saveObj = (next)-> next() if next
        #return cb()
        chkToSave = chkToSaveProv
        saveObj = saveObjProv
    else
      cTodo++
      o = {}
      for k,i in headers
        if (v = d[i])? and (('string' isnt typeof v) or v.length > 0) # either not string or not an empty string
          o[k] = v
      chkToSave o
  parser.on 'end',(count)->
    return if dryRun
    counter = count
    check_done()

finish = ->
  collKeys.insertOne keys,(err)->
    if err then return findError err
    collKeys.insertOne notes,(err)->
      if err then return findError err
      exit 0

importData = (dir)->
  db.mongo (err, MongoDB)->
    console.log "Start Importing"
    if err then return errorNotify err,-> exit 1,err
    db = 'vow'
    coll = new MongoDB(db,'census')
    collKeys = new MongoDB(db,'census_keys')
    collKeys.findOne {_id:'keys'},(err,k)->
      keys = k or {_id:'keys'}
      puts keys
      collKeys.findOne {_id:'notes'},(err,k)->
        notes = k or {_id:'notes'}
        puts notes
        # start read dir
        files = fs.readdirSync dir
        #puts files
        total = 0
        doOne = ->
          if f = files.shift()
            total++
            importFile path.join(dir,f),->
              doOne()
          else
            console.log "Done: #{total}"
            finish()
        doOne()
importData avgs[0]
