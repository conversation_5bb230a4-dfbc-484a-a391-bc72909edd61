db = require('../../batch/db')
exit = db.exit

fs = require 'fs'
util = require 'util'
xml2js = require 'xml2js'
parser = new xml2js.Parser()
if process.argv.length > 2 and process.argv[0] is 'coffee'
  avgs = process.argv.slice(2)
fs.readFile avgs[0],(err,data)->
  parser.parseString data,(err,result)->
    #console.dir result
    boundedBy = result['gml:FeatureCollection']['gml:boundedBy']
    console.log util.inspect boundedBy,false,null
    featureMember = result['gml:FeatureCollection']['gml:featureMember']
    #console.log util.inspect featureMember,false,null
    console.log featureMember.length
    importData featureMember

findError = (d,idx)->
  console.log idx
  console.log util.inspect d,false,null
  exit 1

getBounds = (bnobj)->
  obj = {}
  lat1 = null
  lat2 = null
  lng1 = null
  lng2 = null
  if polygon = bnobj?['gml:Surface']?[0]?['gml:patches']?[0]?['gml:PolygonPatch']?[0]?['gml:exterior']?[0]?['gml:LinearRing']?[0]?['gml:posList']?[0]
    bn = []
    i = 0
    for p in polygon.split(/\s/)
      pp = parseFloat p
      if (i % 2) is 0
        if not lat1? or lat1 > pp
          lat1 = pp
        if not lat2? or lat2 < pp
          lat2 = pp
      else
        if not lng1 or lng1 > pp
          lng1 = pp
        if not lng2 or lng2 < pp
          lng2 = pp
      i++
      bn.push pp
    return {sw:[lat1,lng1],ne:[lat2,lng2],bn:bn}
  throw new Error "No Bounds"
getPrnm = (prnm)->
  p.trim() for p in prnm.split(' / ')
getCorners = (bns)->
  lat1 = null
  lat2 = null
  lng1 = null
  lng2 = null
  for bn in bns
    if not lat1? or lat1 > bn.sw[0]
      lat1 = bn.sw[0]
    if not lat2? or lat2 < bn.ne[0]
      lat2 = bn.ne[0]
    if not lng1? or lng1 > bn.sw[1]
      lng1 = bn.sw[1]
    if not lng2? or lng2 < bn.ne[1]
      lng2 = bn.ne[1]
  {sw:[lat1,lng1],ne:[lat2,lng2]}
importData = (data)->
  db.mongo (err, MongoDB)->
    console.log "Start Importing"
    if err then return errorNotify err,-> exit 1,err
    db = 'vow'
    coll = new MongoDB(db,'census')
    total = 0
    doOne = ->
      if d = data.shift()
        total++
        unless dd = d['fme:gct_000b11g_e']?[0]
          return findError d,total
        bns = []
        try
          if p = dd['gml:surfaceProperty']?[0]
            bns.push getBounds p
          else if p = dd['gml:multiSurfaceProperty']?[0]
            for bn in p['gml:MultiSurface']?[0]?['gml:surfaceMember']
              bns.push getBounds(bn)
          else
            return findError d,total
        catch e
          return findError d,total
        swne = getCorners bns
        prnms = getPrnm dd['fme:PRNAME'][0]
        obj =
          gmlid:  dd['$']['gml:id']
          ctuid:  dd['fme:CTUID'][0]
          ctnm:   dd['fme:CTNAME'][0]
          cmauid: dd['fme:CMAUID'][0]
          cmanm:  dd['fme:CMANAME'][0]
          cmatp:  dd['fme:CMATYPE'][0]
          pruid:  dd['fme:PRUID'][0]
          prnm:   prnms[0]
          prnmfr: prnms[1]
          sw:     swne.sw
          ne:     swne.ne
          bns:    bns
        obj._id = obj.pruid + obj.ctuid
        coll.insertOne obj,(err)->
          if err
            console.error err
            return findError obj,total
          #console.log obj
          doOne()
      else
        console.log "Done: #{total}"
        exit 0
    doOne()
###
boundedBy:
[ { 'gml:Envelope':
     [ { '$': { srsName: 'EPSG:4269', srsDimension: '2' },
         'gml:lowerCorner': [ '42.017788071 -124.699244844' ],
         'gml:upperCorner': [ '54.552759456 -52.6194085039999' ] } ] } ]

Quest: CMATYPE: B/K?
featureMember array object:
{ 'fme:gct_000b11g_e':
     [ { '$': { 'gml:id': 'idf16af23f-461b-4da0-a90d-ff5ac77659cd' },
         'fme:CTUID': [ '4620222.00' ],
         'fme:CTNAME': [ '0222.00' ],
         'fme:CMAUID': [ '462' ],
         'fme:CMANAME': [ 'Montréal' ],
         'fme:CMATYPE': [ 'B' ],
         'fme:CMAPUID': [ '24462' ],
         'fme:PRUID': [ '24' ],
         'fme:PRNAME': [ 'Quebec / Québec' ],
         'gml:surfaceProperty':
          [ { 'gml:Surface':
               [ { '$': { srsName: 'EPSG:4269', srsDimension: '2' },
                   'gml:patches': [ { 'gml:PolygonPatch': [ { 'gml:exterior': [ { 'gml:LinearRing': [ { 'gml:posList': [ '45.5290316080001 -73.6297121829999 45.5285220740001 -73.6300062859999 45.5279856970001 -73.6303138219999 45.5274136250001 -73.6306478939999 45.5268772480001 -73.6309554139999 45.5264214030001 -73.6312226129999 45.5258882320001 -73.6315492869999 45.5264001350001 -73.6332814669999 45.526462624 -73.6335371739999 45.5265077720001 -73.6337219369999 45.5265083770001 -73.6337243499999 45.5267797650001 -73.6346347729999 45.5270789420001 -73.6356394929999 45.5273880970001 -73.6367703259999 45.527629699 -73.6376651219999 45.5276706050001 -73.6378165799999 45.5282335800001 -73.6374920959999 45.528698461 -73.6372247399999 45.529198857 -73.6369181039999 45.529806932 -73.6365832529999 45.530334479 -73.6362887359999 45.530892464 -73.6360041049999 45.5299356980001 -73.6328006169999 45.5292742400001 -73.6305683989999 45.5290316080001 -73.6297121829999' ]
                    } ] } ] } ] } ] } ] } ] } ] }
###
