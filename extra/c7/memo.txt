
#to allow
chcon -Rt httpd_sys_content_t /path/to/www
or
semanage fcontext -a -t httpd_sys_content_t /var/www/*
restorecon -R -v /var/www
or
semanage fcontext -a -t httpd_sys_rw_content_t "/etc/nginx/fastcgi_temp(/.*)?"
restorecon -R -v /etc/nginx/fastcgi_temp


#for upstream
setsebool httpd_can_network_connect on -P
or
cat /var/log/audit/audit.log | grep nginx | grep denied | audit2allow -m nginxlocalconf > nginxlocalconf.te
cat /var/log/audit/audit.log | grep nginx | grep denied | audit2allow -M nginxlocalconf
semodule -i nginxlocalconf.pp

# to allow different incoming port
semanage port -a -t http_port_t -p tcp 8081
