 school_scores | CREATE TABLE `school_scores` (
  `school_id` varchar(45) NOT NULL,
  `yearmark` varchar(45) NOT NULL,
  `name` varchar(128) DEFAULT NULL,
  `g3stu` int(11) DEFAULT NULL,
  `g3ar` smallint(6) DEFAULT NULL,
  `g3aw` smallint(6) DEFAULT NULL,
  `g3am` smallint(6) DEFAULT NULL,
  `g3rank` int(11) DEFAULT NULL,
  `g3total` int(11) DEFAULT NULL,
  `g6stu` int(11) DEFAULT NULL,
  `g6ar` smallint(6) DEFAULT NULL,
  `g6aw` smallint(6) DEFAULT NULL,
  `g6am` smallint(6) DEFAULT NULL,
  `g6rank` int(11) DEFAULT NULL,
  `g6total` int(11) DEFAULT NULL,
  `g9acstu` int(11) DEFAULT NULL,
  `g9acm` smallint(6) DEFAULT NULL,
  `g9acrank` int(11) DEFAULT NULL,
  `g9actotal` int(11) DEFAULT NULL,
  `g9apstu` int(11) DEFAULT NULL,
  `g9apm` smallint(6) DEFAULT NULL,
  `g9aprank` int(11) DEFAULT NULL,
  `g9aptotal` int(11) DEFAULT NULL,
  `g9rank` int(11) DEFAULT NULL,
  `g9total` int(11) DEFAULT NULL,
  `city` varchar(45) DEFAULT NULL,
  `zip` varchar(10) DEFAULT NULL,
  `sbid` varchar(45) DEFAULT NULL,
  `firank` int(11) DEFAULT NULL,
  `fitotal` int(11) DEFAULT NULL,
  `firate` float DEFAULT NULL,
  `filast5rank` int(11) DEFAULT NULL,
  `filast5total` int(11) DEFAULT NULL,
  `fiesl` float DEFAULT NULL,
  `fispecial` float DEFAULT NULL,
  `fiincome` int(11) DEFAULT NULL,
  `fivs` float DEFAULT NULL,
  PRIMARY KEY (`school_id`,`yearmark`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1