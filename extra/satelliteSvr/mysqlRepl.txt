Rebuild replication
1. master: mysqldump db > aaa
2. slave:
    > slave stop;
    > reset master;
    > reset slave;
    mysql db < aaa
    > slave start;

Filter db:
CHANGE REPLICATION FILTER REPLICATE_WILD_DO_TABLE = ('vow.%');
STOP/START SLAVE
SHOW SLAVE STATUS\G

Setup Replication:
CHANGE MASTER TO
MASTER_HOST = '************',
MASTER_PORT = 3306,
MASTER_USER = 'repl',
MASTER_PASSWORD = 'Repl567@',
MASTER_AUTO_POSITION = 1;
START SLAVE;
SET @@global.read_only = OFF;
SHOW slave status\G;
show slave hosts;
show processlist;
show databases;
