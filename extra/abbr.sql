CREATE TABLE `properties` (
  `id` varchar(45) NOT NULL,
  `src` varchar(15) DEFAULT NULL COMMENT 'Source: TREB,DDF,RM...',
  `sid` varchar(45) DEFAULT NULL COMMENT 'Extra Source ID',
  `uid` varchar(45) DEFAULT NULL COMMENT 'Owner or Listing Agent ID',
  `bid` varchar(45) DEFAULT NULL COMMENT 'Listing Brokerage ID',
  `ts` datetime DEFAULT NULL COMMENT 'Created Timestamp',
  `mt` datetime DEFAULT NULL COMMENT 'Modified Timestamp',
  `lat` float DEFAULT NULL,
  `lng` float DEFAULT NULL,
  `lp` decimal(10,2) DEFAULT NULL COMMENT 'listing price',
  `lpunt` varchar(45) DEFAULT NULL COMMENT 'listing price unit: sf/square meter/month...',
  `ltp` varchar(45) DEFAULT NULL COMMENT 'Listing Type: Brokeraged/Landlord Direct...',
  `stp` varchar(16) DEFAULT NULL COMMENT 'Sales Type: For Sale/For Lease/Sub-Lease...',
  `pcls` varchar(45) DEFAULT NULL COMMENT 'Property Class: Freehold, Condo, Commercial...',
  `ptp` varchar(45) DEFAULT NULL COMMENT 'Building Type: Detached/Semi ...\n',
  `pstyl` varchar(45) DEFAULT NULL COMMENT 'Building Style: 2-Stories; 3-Stories; Bungalow...',
  `bdrms` tinyint(4) DEFAULT NULL COMMENT 'Above Ground BedRooms\n',
  `tbdrms` tinyint(4) DEFAULT NULL COMMENT 'Total Bedrooms',
  `bthrms` tinyint(4) DEFAULT NULL,
  `kch` tinyint(4) DEFAULT NULL,
  `gr` tinyint(4) DEFAULT NULL COMMENT 'garage',
  `tax` float DEFAULT NULL,
  `addr` varchar(255) DEFAULT NULL,
  `unt` varchar(45) DEFAULT NULL,
  `cnty` varchar(127) DEFAULT NULL COMMENT 'Country',
  `prov` varchar(127) DEFAULT NULL,
  `city` varchar(127) DEFAULT NULL,
  `cmty` varchar(127) DEFAULT NULL COMMENT 'Community/SubDivision...',
  `fce` varchar(16) DEFAULT NULL COMMENT 'Facing/Exposure',
  `area` float DEFAULT NULL COMMENT 'Square Footage: 1-499(499.5),500-599(599.5)...,3500-5000(5000.5),5000+(5001.5), or exact sq without decimal\n',
  `age` float DEFAULT NULL COMMENT '0:new, 5.5:0-5,15.5:6-15,.. or exact building year without decimal',
  `ac` varchar(16) DEFAULT NULL,
  `pool` varchar(15) DEFAULT NULL,
  `bsmt` varchar(45) DEFAULT NULL COMMENT 'Basement Type    ',
  `pho` int(11) DEFAULT NULL COMMENT 'Photo Numbers',
  `phomt` datetime DEFAULT NULL COMMENT 'Photo Last Update Timestamp',
  `exp` datetime DEFAULT NULL COMMENT 'Expiry Date',
  `psn` varchar(45) DEFAULT NULL COMMENT 'Possession Date',
  `mfee` float DEFAULT NULL COMMENT 'Maintenance Fee    ',
  `status` varchar(45) DEFAULT NULL COMMENT 'Status',
  `daddr` varchar(8) DEFAULT NULL COMMENT 'Display Address: Y/N',
  `jdoc` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sids` (`src`,`sid`),
  KEY `uid` (`uid`),
  KEY `bid` (`bid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;