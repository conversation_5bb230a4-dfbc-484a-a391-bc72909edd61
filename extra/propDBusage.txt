[x]Round 1: find usages
[X]Round 2: combine processes, eg. listingPicUrl, forLoops/inject user
[]Round 3: move coll.ops -> model

30_init_prov_cities,propCommons->build ProvCityCmtyMap/PopularCityMap->propertiesAggregate
//dataImport, colls->bcre.importMany->insertOne
//x_bingv7geocode, saveDataWithGeoCache->coll, find->saveToMaster.updateOne


Test/mod/content
[?][X]chat, findOne by id/sid
[?][X]showing.coffee, findToArray by id -> modify orig list
[X][X]predict, updatepredict(pred_ok/pred_error)/aggragate, sort by pred_diff

[X][X]propPrediction. simple findOne->translate
[X][X]x_apiShare, simple update->inc shr(r/c)
[X][X]sysAdmin, find by aid/aid2/bid
[X][X]realtorVerify, findOne -> topUser(find top user by _id)
[X][X]wesite, findtoarray/count by la(2).agnt._id
[X][X]htmltoimg, findById -> l10n/imgs -> html
[X][X]forum, injectPropDetail find->set img and l10n;
[X][X]evaluation, find/toArray/count, propsByAddress->translate
[X][X]index, propSearch.getRecentPropsByProvAndCity, coll
[X][X]rm0Mylisting, findOneAndUpdate, promote/revokeRMPropRecord
[X][X]propTopUp, initTopListings/gen->update topTs,importRMPropRecord
[X][O][findProperty]propDetail, simple find(for page data)/updateOne/updateBoth(yturl/ohs/refreshpic/phodl/updateloc)
[X][O][Colls]resources, update/delete/findToarray/findOne, fav/favList, simple ops
[X][O]03_propFunctions, getPropDetails->findOne->l10n/img/p_ab...   /updateOne(vc)
[X][O]40_search_prop_api_sql,query db with opt.Coll->findToArray


