
###
This is mongoDB rely config file, main porsose is reformatting incoming json to achieve custom relied database update. 
For more details, see batch/dbScript/mongoWatch.coffee
expected input and output (in json format):
{
  optp:'update/insert/delete...',
  ns:{db:'..',call:'...'},
  key:{_id:'...'},
  up:{},
  fd:{}
}

optp: operationType
fd: fullDocument, unavailable when delete
call: collection

###

exports.connectionString = 'mongodb://localhost:27017/relay'

exports.formatter = (json)->
  {optp, ns:{ coll }, fd, key:{_id}} = json
  # custom code start here

  # example
  # convert delete to update and set isDeleted:ture
  convertedObject = {...json}
  if optp is 'delete' and coll is 'listing'
    convertedObject.optp = 'update'
    convertedObject.fd = { name:'delete'; isDeleted:true }
  console.log(1,convertedObject)
  return convertedObject