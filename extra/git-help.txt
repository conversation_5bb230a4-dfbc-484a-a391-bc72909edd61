git remote add bob ~bob/folder
git fetch bob
git branch -a # list all remote
gitk --all &
git merge bob/master
git commit -m ""
git push origin master

# Create and checkbout branch 1.5.1
git checkout -b b1.5.1
or
git branch b1.5.1
git checkout b1.5.1

# push branch to center repository
git push -u origin b1.5.1


# sync to repository
git pull -a
git checkout b1.5.1

# Merge b1.5.1 back to master
git checkout master
git merge b1.5.1



git stash show -p stash@{0}
git stash apply stash@{0}


https://confluence.atlassian.com/display/BITBUCKET/Use+repo+tags




<preference name="fullscreen" value="false" />
<preference name="android-windowSoftInputMode" value="stateVisible" />
