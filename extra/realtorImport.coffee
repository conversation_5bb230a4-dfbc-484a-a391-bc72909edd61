#Realtor Import
###
DROP TABLE `realtors`;
delimiter $$

CREATE TABLE `realtors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(125),
  `title` varchar(63),
  `tel` varchar(63) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `avt` varchar(256) DEFAULT NULL,
  `brkg_logo` varchar(255) DEFAULT NULL,
  `brkg_name` varchar(255) DEFAULT NULL,
  `brkg_addr` varchar(255) DEFAULT NULL,
  `brkg_tel` varchar(125) DEFAULT NULL,
  `brkg_fax` varchar(125) DEFAULT NULL,
  `brkg_url` varchar(255) DEFAULT NULL,
  `mt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=latin1$$

update realtors set brkg_addr = REPLACE(brkg_addr,'<br>',', ');
###

puts = console.log
mysql = require 'mysql'
#client = mysql.createClient {user:'vow_u',password:'vow_p'}
client = mysql.createConnection {insecureAuth:true,user:'vow_u',password:'vow_p',database:'vow',host:'127.0.0.1'}
client.query 'USE vow'
sql = "INSERT INTO realtors (`avt`,`name`,`title`,`tel`,`url`,`brkg_logo`,`brkg_name`,`brkg_addr`,`brkg_tel`,`brkg_fax`,`brkg_url`) VALUES (?,?,?,?,?,?,?,?,?,?,?)"
total = 0
done = 0
ts = Date.now()
# TODO: use fast-csv
csv = require 'csv'
parser = csv.parse()
process.stdin.pipe parser
parser.on 'readable',->
  while data = parser.read()
    if data[0] is 'RealtorPhoto'
      puts "Skip header"
      return
    #puts "#{total++}: #{data}"
    total++
    data[7] = data[7].replace(/[^\x00-\x7F]/g, "").replace(/\<br\>/ig,", ")
    if (p = data[9].indexOf('Fax:')) >= 0
      data[9] = data[9].substr(p + 4).trim()
    else if (p = data[9].indexOf('Toll Free:')) >= 0
      data[9] = data[9].substr(p + 10).trim()
    client.query sql,data,(err)->
      if err then console.error err
      done++
      checkFinished()

parser.on 'error',(err)->
  console.error err
  process.exit 3

parser.on 'finish',()->
  checkFinished()

checkFinished = ->
  if done >= total
    console.log "Total: #{total} in #{Date.now() - ts}ms"
    process.exit 0
