# User specific aliases and functions
#############################################################################
# path shutcuts
__append_to() {
  if [ -f $1 ]; then
    tmp=`head -1 $1`
    if [ "$2" = "$tmp" ]; then
       return 0
    fi
    mv $1 $1..$$
    echo $2 > $1
    cat $1..$$ >> $1
    rm -f $1..$$
    return 0
  else
    echo $2 > $1
    return 1
  fi
}

__rm_last() {
  if [ -s $1 ]; then
    ret=`head -1 $1`
    sed -e '1d' $1 > $1..$$
    mv $1..$$ $1
    echo $ret
  elif [ -f $1 ]; then
    rm -f $1
  fi
}



go() {
  ret=10
  cf="$HOME/.go.paths"
  tmpf="$HOME/.go.tmp"
  backf="/tmp/.go.back.$$"
  forwardf="/tmp/.go.forward.$$"

  if [ $# -eq 0 ]; then
    cd
    return 0
  fi

  cmd=$1
  if [ $# -eq 1 -a -d $cmd ]; then
    __append_to $backf $PWD
    cd $1
    rm -f $forwardf
    return 0
  fi

  shift

  if [ $cmd = "h" ]; then
    echo "Usage: go a/l/rm [label]"
    echo " [empty] - go home."
    echo " {path|shortcuts} - go there."
    echo " h - show this help."
    echo " a - add current path. Need label."
    echo " s - show the full path. Need label."
    echo " l [s|b|f] - list all labeled pathes. "
    echo " rm - remove a label. Need label."
    echo " b [step]- go back. "
    echo " f [step]- go forward."
    return 0
  elif [ $cmd = "a" ]; then
# Add a new path label
    label=$1
    if [ -z $label ]; then
      echo "Please specify a label for $PWD"
      return 2
    fi
    if [ -f $cf ]; then
       echo "$label $PWD" >> $cf
    else
       echo "$label $PWD" > $cf
    fi
  elif [ $cmd = "b" ]; then
# back
     if [ -s $backf ]; then
       step=$1
       if [ -z "$step" ]; then
         __append_to $forwardf $PWD
         cd `__rm_last $backf`
         return 0
       else
         pt=`head -n $step $backf | tail -1`
         if [ -n "$pt" ]; then
            cd $pt
            return 0
         else
            echo "Line $step does not exist in history"
            return 1
         fi
       fi
     else
       echo "No history"
       return 1
     fi
  elif [ $cmd = "f" ]; then
# forward
    if [ -s $forwardf ]; then
      step=$1
      if [ -z "$step" ]; then
         __append_to $backf $PWD
         cd `__rm_last $forwardf`
         return 0
      else
         pt=`head -n $step $forwardf | tail -1`
         if [ -n "$pt" ]; then
           cd $pt
           return 0
         else
           echo "List $step does not exist in history"
           return 1
         fi
      fi
    else
      echo "No history"
      return 1
    fi
  elif [ $cmd = "l" ]; then
# List
    list=$1
    if [ -z $list ]; then
      echo "------- Shortcuts ------"
      cat $cf 2>/dev/null
      echo "------- Backs ------"
      cat -n $backf 2>/dev/null
      echo "------- Forwards ------"
      cat -n $forwardf 2>/dev/null
    elif [ $list = "s" ]; then
      [ -s $cf ] && cat $cf
    elif [ $list = "b" ]; then
      [ -s $backf ] && cat -n $backf
    elif [ $list = "f" ]; then
      [ -s $forwardf ] && cat -n $forwardf
    else
      echo "Please specify list name b | f | [empty]"
      return 1
    fi
  elif [ $cmd = "s" ]; then
# Search and show a full path for a label
    label=$1
    if [ -z $label ]; then
      return 0
    fi
    grep "^$label " $cf > $tmpf
    read unused pt < $tmpf
    echo "$pt"
  elif [ $cmd = "rm" ]; then
# Remove a label
    label=$1
    if [ -z $label ]; then
      echo "Please specify a label"
      return 2
    fi
    sed /^$label\ /d $cf > $tmpf
    rm -f $cf
    mv $tmpf $cf
  else
# Go to a label
    label=$cmd
    if [ -z $label ]; then
      echo "Please specify a label to go"
      return 2
    fi
    grep "^$label " $cf > $tmpf
#    grep "^$label[:space:] " $cf > $tmpf
    read unused pt < $tmpf
    if [ -z $pt ]; then
      echo "Cann't find label '$label'."
      return 3
    fi
    __append_to $backf $PWD
    cd $pt
  fi
  return $ret
}


