@baseUrl = www.findschool.ca/web/Api/GetV20

### POST GetAllActiveSchoolIDs
POST {{baseUrl}} HTTP/1.1
content-type: application/json

{
    "ReqType": "GetAllActiveSchoolIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-969912",
    "ReqParas": "[]"
}

### POST GetUpdatedSchoolIDs
POST {{baseUrl}} HTTP/1.1
content-type: application/json

{
    "ReqType": "GetUpdatedSchoolIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-526435",
    "ReqParas": "[\"2021-03-01\"]"
}

### POST GetObjectByIDs - SchoolExtra
POST {{baseUrl}} HTTP/1.1

content-type: application/json

{
    "ReqType": "GetObjectByIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-32455654",
    "ReqParas": "{\"ObjectType\":\"SchoolExtra\",\"IDs\":[\"1020255\"]}"
}

### POST GetObjectByIDs - SchoolBoard
POST {{baseUrl}} HTTP/1.1
content-type: application/json

{
    "ReqType": "GetObjectByIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-9699132fewr",
    "ReqParas": "{\"ObjectType\":\"SchoolBoard\",\"IDs\":[\"44d78cff-2f5f-4950-8e5c-020dc178da09\",\"4e261bb6-a929-47d5-8dd1-67135639eff6\",\"a58e9043-1e16-4fb6-9c31-8488c8513f18\",\"cc092dae-6966-4599-b9a3-e60cb93fd86c\",\"91eaa31a-0af5-4b71-86b0-31af4a89edf4\",\"72e6539c-6986-44fa-a603-735de06b58f1\",\"092be798-6935-4334-ba59-a26c96d1a359\",\"2531da17-29ae-48f4-bc08-25e42945a29d\",\"d8114e93-d726-41cf-ab0e-1b772dd135f1\",\"9b64a6ee-57ab-4756-925b-553cc6afd28b\",\"fee393d9-ac90-4de3-a1a8-b62f4e71ad80\",\"43e8a523-6f04-4661-bc4c-0392bcbbb778\",\"ee4e7aea-073f-44ee-85ae-9843f2dfb5de\",\"f86c2fe2-f6ae-4195-979a-d7d2172843f7\",\"9f71045e-b998-451d-a031-171ff511ec6e\"]}"
}

### POST GetEQAOBySchoolIDs
POST {{baseUrl}} HTTP/1.1
content-type: application/json

{
    "ReqType": "GetEQAOBySchoolIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-324364365",
    "ReqParas": "{\"ObjectType\":\"EQAO\",\"IDs\":[\"1020255\",\"1022390\"]}"
}


### POST GetFIRankBySchoolIDs
POST {{baseUrl}} HTTP/1.1
content-type: application/json

{
    "ReqType": "GetFIRankBySchoolIDs",
    "Email": "<EMAIL>",
    "Password": "TQQ-oa9-HcB-Q92",
    "ReqID": "RM-324364365",
    "ReqParas": "{\"ObjectType\":\"FIRank\",\"IDs\":[\"1020255\"]}"
}