puts = console.log
rowIn = 0
rowOut = 0

mysql = require('mysql')
pool = mysql.createPool(
  host: 'localhost'
  user: 'vow_u'
  password: 'VowDb123!'
  database: 'vow')

MongoClient = require('mongodb').MongoClient
MongoDB = null
Properties = null
# Connection URL
url = '*****************************************'
# Use connect method to connect to the server
MongoClient.connect url, (err, db) ->
  MongoDb = db
  Properties = db.collection('properties_test');
  puts "Mongo Connected"
  pool.getConnection (err,connection)->
    puts "Mysql Connected"
    doTest()
