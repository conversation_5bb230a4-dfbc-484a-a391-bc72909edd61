puts = console.log
rowIn = 0
rowOut = 0

mysql = require('mysql')
pool = mysql.createPool(
  host: 'localhost'
  user: 'vow_u'
  password: 'VowDb123!'
  database: 'vow')

MongoClient = require('mongodb').MongoClient
MongoDB = null
Properties = null
# Connection URL
url = '*****************************************'
# Use connect method to connect to the server
MongoClient.connect url, (err, db) ->
  MongoDb = db
  Properties = db.collection('properties_test');
  puts "Mongo Connected"
  pool.getConnection (err,connection)->
    puts "Mysql Connected"
    fields = null
    query = connection.query 'SELECT * FROM properties'
    query.on('error', (err) ->
      # Handle error, an 'end' event will be emitted after this as well
      puts err
      connection.release()
      return
    ).on('fields', (_fields) ->
      #puts _fields
      # the field packets for the rows to follow
      fields = _fields
      return
    ).on('result', (row) ->
      rowIn++
      #puts row
      # Pausing the connnection is useful if your processing involves I/O
      if (rowIn - rowOut) > 100
        connection.pause()
        puts "pause #{rowIn}:#{rowOut}"
      processRow fields,row, ->
        rowOut++
        if (rowIn - rowOut) < 3
          puts "resume #{rowIn}:#{rowOut}"
          connection.resume()
        return
      return
    ).on 'end', ->
      puts "Done"
      connection.release()
      setTimeout (-> process.exit 0),5000



processRow = (fields,row,cb)->
  obj = _id:row.id
  jdoc = JSON.parse row.jdoc
  for k,v of row
    if row[k]? and row[k] isnt ''
      obj[k] = row[k]
  for k,v of jdoc
    obj[k] = v
  if row.jdoc2?.length > 2
    jdoc2 = JSON.parse row.jdoc2
    for k,v of jdoc2
      obj[k] = v
  delete obj.jdoc
  delete obj.jdoc2
  if 'string' is typeof obj.his
    obj.his = JSON.parse obj.his
  for k,v of obj
    unless v
      delete obj[k]
  Properties.insertOne obj,{w:1},(err)->
    if err
      puts err
      return process.exit 1
    if 0 is (rowOut % 1000)
      puts "in:#{rowIn / 1000}k Out:#{rowOut / 1000}k"
    cb()
