mongoexport -u rmuser -d data -q '{_id:{$lt:ObjectId("568607d00000000000000000")},$and:[{roles:"realtor"},{roles:{$nin:["vip","vip_plus"]}}]}' -c user -o realtors.csv --type csv -f nm_zh,nm_en,eml,mbl,roles,lts,cpny_zh,cpny_en

https://steveridout.github.io/mongo-object-time/

db.user.count({_id:{$lt:ObjectId("5bd928c00000000000000000")},$or:[{lts:{$exists:1}},{src:{$exists:1}}]})

mongoexport -u rmuser -d data -q '{roles:{$in:["vip","vip_plus"]}}' -c user -o realtors.csv --type csv -f nm_zh,nm_en,eml,mbl,roles,lts,cpny_zh,cpny_en

mongoexport -u dbRur -d data -q '{"roles":"realtor"}' --port 27027 --authenticationDatabase admin -c user -o realtors.csv --type csv -f nm_zh,nm_en,eml,mbl,roles,lts,cpny_zh,cpny_en