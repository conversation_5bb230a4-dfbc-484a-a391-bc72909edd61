###
 open db
 read folder
 read file
 read record
 reduce to simple json
 put to db
###
dbSrv =
  verbose: 0
  vow:
    host:'127.0.0.1'
    port:27017
    name:'realtor'
    pool_min:1
    pool_max:3
    #models: './model', // file name without '.coffee' or '.js', the "models" variable should be exposed as models defintions
    user:'realtor_u'
    password:'realtor_p'
    authdb: 'admin'

MongoClient = require('mongodb').MongoClient
test = require('assert')
JSONStream = require 'JSONStream'
fs = require 'fs'
path = require 'path'
puts = console.log
colRealtor = null
findError = (err)->
  console.error err
  process.exit 1

getColl2 = (cb)->
  # Connection url
  dburl = 'mongodb://localhost:27017/realtors'
  # Connect using MongoClient
  MongoClient.connect dburl, (err, db)->
    if err then return cb err
    # Use the admin database for the operation
    #adminDb = db.admin()
    # List all the available databases
    colRealtor = db.collection('trebRealtor');
    cb null,colRealtor

getColl = (cb)->
  MongoDb = require '../src/lib/mongo_db'
  MongoDb.setup {dbs:dbSrv}, (err,db)->
        if err then return findError err
        console.log "MongoDb Connected."
        colRealtor = coll = new MongoDb.Database 'vow','trebRealtor'
        cb null,coll
  null


readFolder = (pathname,cb)->
  puts pathname
  getColl (err,coll)->
    if err then return findError err
    files = fs.readdirSync pathname
    doOne = (err)->
      if err then return findError err
      if f = files.pop()
        if f[0] is '.'
          return process.nextTick doOne
        readFile path.join(pathname,f),doOne
      else
        cb() if cb
    doOne()

readFile = (file,cb)->
  puts file
  rs = fs.createReadStream file
  objs = {}
  str = ''
  rs.setEncoding 'utf8'
  rs.on 'data',(chunk)->
    str += chunk
  rs.on 'end',->
    processLines str,cb

processLines = (str,cb)->
  ss = str.split '\n'
  doOne = ->
    unless s = ss.shift()
      return cb()
    if s.length < 10
      return doOne()
    try
      objs = JSON.parse s,(k,v)->
        #if v and k and not /^__/.test k #and 'object' isnt typeof v
        #  puts "#{k} => #{v}"
        if v #and not /^__/.test k #and 'object' isnt typeof v
          return v
        null
    catch e
      puts "ERROR:"
      puts "Strlen: #{str.length}"
      puts e
      return doOne()
    records = []
    for o in objs.d
      oo = {}
      for k,v of o
        if k is 'attributes' and Array.isArray v
          for n in v
            if n.name is 'municipalityCode'
              oo.municipalityCode = n.value
        else if v and (not v.__deferred?) and (not /^__/.test k)
          oo[k] = v
      records.push oo
    #puts records
    processRecords records,doOne
  doOne()


processRecords = (objs,cb)->
  puts "Objects:" + objs.length
  done = 0
  doOne = ->
    if r = objs.shift()
      r._id = r.contactRef.replace 'User','TREB'
      colRealtor.findOne {_id:r._id},(err,old)->
        if err then return findError err
        r.mt = new Date()
        unless old?
          r.created = new Date()
        r.ts = old?.ts or new Date()
        r.chg = old?.chg or []
        if old?.organizationName
          if r.organizationName isnt old.organizationName
            r.chg.push {o:old.organizationName,n:r.organizationName,tp:'organizationName'}
        if old?.email
          if r.email isnt old.email
            r.chg.push {o:old.email,n:r.email,tp:'email'}
        colRealtor.updateOne {_id:r._id},{$set:r},{w:1,upsert:true},(err)->
          if err then return findError err
          done++
          process.nextTick doOne
    else
      puts "Saved: #{done}"
      process.nextTick cb
  doOne()

if process.argv.length > 2 and process.argv[0] is 'coffee'
  avgs = process.argv.slice(2)
  readFolder avgs[0],->
    console.log "Finished"
    process.exit 0
#setTimeout (-> puts "End"),5000
