listing db remove:
  mls_treb
  mls_treb_emails
  mls_treb_search
  my_collection_keys
  school_0920
  cmty_crime_rate_bk
  cmty_crime_rate_unused
  area

rni:
  mongohouse_sold
  mls_crea_ddf_availables (clear after 60 days)
  mls_treb_availables: (remove all ts:$lt:60 days)
  mls_crea_ddf_records
  mls_treb_master_records
  mls_crea_keys
  mls_treb_keys
  properties (both rni & listing)
  data_download
  geo_cache
  popular_cities
  prov_city_cmty

listing:
  mls_treb_commu: data from other site total 1158, bn: 496, _bn: 470
  treb_comm2: findschool 470
  private_schools
  schools
  school
  school_board
  school_scores
  stigmatized
  census_keys
  census
  brkg_cpny: 4767
  agent: 3358 (remove _id:null)
  cmty_crime_rate
  properties (both rni & listing. remove: {status:'U',mt:{$lt:(t-180),keep:{$exists:0}}} )
  stats_daily
  stats_mw


data remove:
  account
  app_logs
  file_folder
  i18ntodo
  listing_stats
  mailqueue
  mailtemp_send
  mandrill
  old_login
  old_user
  sp_events

data keep(not used, used by websites):
data_maynotused
  app_user
  areas
  cnt_4f3b1c0234f7ee577a000002
  contact
  entry
  file: for website
  forminput
  gnl_4f3b1c0234f7ee577a000002
  interact
  mailtemp
  page
  pageDraft
  profile
  tasks
  template
  user_site
  user_contact

data no need to sync(write only or need primary node read):
rni:
  email_template: no need to sync
  error_log: no need to sync
  inter_hist: no need to sync
  sms:

data:
  abbr
  ads
  chat: may need move to separate system
  file_app:
  i18n: remove _id:/^writeerror/, no zh nor zh-cn field
  lang
  login
  media
  news: remove regularly
  rtoken: remove after 1 day {dt:$lt:-1day}
  site: (for old website, still used)
  sysdata
  user
  user_file: (remove _id:null)
  user_listing: 3034
  user_profile: 20387
  vcode
  wecardtemplate
  wecard
  wechat_user


dbs:
  udb 28 user db
  vdb 27 volumn db
  rni: retire & import
  impdb: remote db
  data_maynotused:


migration procedure:
  1. stop njj/bjj(2)/gzcn/dh cmate/cron
  2. mongodump dbs
  3. run scripts for 2 dbs
    - restore colls to destination dbs
    - change old db password
    - trim colls
  4. start new crontabs
    - wechat update to both prod/test *
    - EDM sendMail
    - hourly import
      - DLA/IDX/VOW
      - pictures
      - Geo
      - mail download (update lat/lng/sp/lp/lsp?/lst/ctrdt/slddt/his?) *
      - import (based on source)
      - update news *
    - 4 hours import
      - DDF
      - picture
      - Geo
      - import
    - 2 daily notify
    - daily
      - mongohouse
      - clearCounters/statistics
      - school update
      - weekly stat
      - monthly stat
      - build prov/city/cmty list
      - retire data *
      - remove DDFImage (6m)
      - remove TREB images (1y) *
      - remove news logs

  5. start cmates
