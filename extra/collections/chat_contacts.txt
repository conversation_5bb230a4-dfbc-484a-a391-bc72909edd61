group collection
  _id: auto
  uids: [] users' uid


chat intro:
  1. follow: /chat/u/user-id (in app only)
  2. event application /chat/w/wp-id
  3. start chat: /chat/u/user-id or /chat/g/group-id(auto id)

user_contact collection:
  _id: auto
  oid: owner uid
  eml: real email
  nm: nickname from owner or user
  tel: telephone
  mbl: phone
  ts: created time
  mt: changed time
  m: memo by owner
  avt: if has avt
  uid: uid if is app user


chat intro:
  1. follow: /chat/u/user-id (app only)
      - find both users
      - create both contacts
      - create a chat, send hello message
  2. event application /chat/wepage/wp-id
      - find event owner user
      - create/find contact for event owner
      - create/find a chat, send form filled message.
        if existing chat, push new message to both users
  3. start chat: /chat/u/user-id or /chat/g/group-id(auto id) (in app only)
      - find both users/group
      - create or find chat
      -
  4. forum: /chat/f/f-id mls:#, or communit

chat collection
  _id: auto
  usrs: [] # max 100
    *eml: lower case
    *pn: push id (immutable for send pn)
    *_id: user._id (immutable for search)
    del: deleted, not show
    nm: self defined nickname here
    lvts: last viewed ts
    nts: notified ts
    mute: true or false/null
  msg: [] max length 1000
    *f: from users index
    *tp: msg type /lstApp/lstCmt/text/cmt(comment from events or mylisting, )
    *m: message. max length 2000 byte
    *ts:
    ?(*)eml
    ?wxid
    ?mbl
    ?nm
    url: listing, wepage, others
    img:
    thumb:
    ref: refer previous msg index
    del: delete flag
    wpid: wpid if from event form, wepage id
  *ts:
  mt: last update timestamp
  lm: last message -> last chat msg
  nm: chat name
  m: chat description
  wpid:  wpid
  rmid: rmprop id
  sid: ml sid
