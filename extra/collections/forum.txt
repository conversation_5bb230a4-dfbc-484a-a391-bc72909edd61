forum collection:
  _id: auto // wecard._id, mlsid._id, news._id
  uid: uid if is app user
  ts: created time
  mt: modify time
  vt: owner last visit time
  m: post content
  nm: fullname or nickname from owner or user
  avt: avt of owner
  tl: title,
  city: city,
  prov: province,
  ncity:ture/false (no city)
  cnty: country,
  src:  “news ||  qna || post || wecard“
  sticky: “global||city||false, （置顶）
  sticky_ts: date
  tp： topic string unique
  tpbl: [ids],  （topic belong）
  tags: [String],
  cmnts:[ {
        ts: date,
        uid: id,
        nm: fullname or nickname from owner or user
        m: String,
        del: true || false,
        del_des: delete reason
        mt: date
        sticky: true||false,
        referIdx: refered comments id.
   }]
   thumb: thumb_picture_url
   photos:[{
             url: string,
   name: string
   }]
   lang: ‘en||zh'
   fav : [usersid],
   vc: number,
   vcd: dailynumber,
   cc: cmnts number,
   history: [{
        uid
        ts: date,
        diff_m: [{
        c: count,
        a: added,
        r: removed,
        v: value;
        }],
        diff_tl: String,
        tags: [String],
        city
        prov
        lang
       }],
  del: true||false,
  del_des: delete reason
  mt: changed time,
  shr: share number
  shrd: daily share number
// from news
   news_url : url.
   news_chnl:
   news_logo:
   news_src:


  sysdata meaning:



   updateConfig = {
     stickyVcLow :1000  for new post.  for sticky post,  view from 1000 - 1500.
     stickyVcHigh : 1500
     nStickyVcLow : 700   for new post.  for non sticky post, view from 700 - 800
     nStickyVcHigh : 800
     updateStickyVcLow : 200  for update200 for sticky post, view from 200 - 300
     updateStickyVcHigh : 300
     updatenStickyVcLow : 150  for update200 for non sticky post, view from 150 - 250
     updatenStickyVcHigh : 250
     hours :40   new post in 40 hours.
     updateHours :10  updated post in 10 hours.
     updateInterval :5 run every 5 mins
   }
