{
  _id:default
  lts: ISODate() -> last record fts
  mt: ISODate()
  prov:
  area:
  city:
  cmty:
  record:[ #t:theft, m:murder, r:robbery
    {t:0, r:0, m:0, fts:ISODate() }
    {t:0, r:0, m:0, fts:ISODate() }
  ]
}
accress:
  _crime_editor
  _admin

if input date !== ltf
   create record
edit last record


1. select distinct prov, area,city, cmty from properties table where src = 'treb'
2. select * from properties where src = 'TREB' and cmty is not NULL and prov = 'ON';
date from:   #date to:
input search

ul
  li matched/all input * 3, 0,0,0, lost focus auto save
