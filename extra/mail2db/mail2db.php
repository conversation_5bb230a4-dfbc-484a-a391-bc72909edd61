<?php
date_default_timezone_set("America/Toronto");
require "vendor/autoload.php";
use PHPHtmlParser\Dom;
//http://php.net/manual/en/mongo.connecting.ssl.php
$mongo        = new MongoClient("********************************************");
// ,array(
//   'username' => 'vow_u',
//   'password' => 'vow_p',
//   'authSource' => 'vow'
// ));
//$mTrebKey     = $mongo->selectDB("vow")->selectCollection("mls_treb_keys");
//$mTrebRecord  = $mongo->selectDB("vow")->selectCollection("mls_treb_master_records");
$mTrebEmail  = $mongo->selectDB("impdb")->selectCollection("mails");
$mSysData  = $mongo->selectDB("impdb")->selectCollection("sysdata");
$mListings  = $mongo->selectDB("impdb")->selectCollection("listings");
$mAgents  = $mongo->selectDB("impdb")->selectCollection("agents");
$mOffices  = $mongo->selectDB("impdb")->selectCollection("offices");

$mSysData->save(array("_id"=>"ReadMailStart","ts" => new MongoDate()));



$keyLabels = array(
  "ml_num" => "MLS#:",
  "sp" => "Sold:",
  "lsddt" => "Leased Date:", // leased date(for client)?
  "lsdp" => "Leased:", // leased price & leased date(for client)?
  "lp" => "List:",
  "lpr" => "Lease:",
  "tax" => "Taxes:",
  "lst" => "Last Status:",
  "slddt" => "Sold Date:",
  "bm" => "Brkage Remks:",
  "comm" => "CB Comm:",
  "ctrdt" => "Contract Date:",
  "exp" => "Expiry Date:",
  "seller" => "Seller:",
  "sellers" => "Sellers:",
  "rltrtel" => "Ph:",
  "rltrfax" => "Fax:",
  "ad_text" => "Client Remks:",
  "extras" => "Extras:",
  "stp" => "For:" // Sale/Lease
  //"rltr" => "Listing Contracted With:"
);
// $keyLabelsClient = array(
//   "ml_num" => "MLS#:",
//   "sp" => "Sold:",
//   "lsddt" => "Leased Date:", // leased date(for client)?
//   "lsdp" => "Leased:", // leased price &
//   "lp" => "List:",
//   "lpr" => "Lease:",
//   "tax" => "Taxes:",
//   "lst" => "Last Status:",
//   "slddt" => "Sold Date:",
//   "ctrdt" => "Contract:",
//   "ad_text" => "Client Remks:",
//   "extras" => "Extras:",
//   "stp" => "For:" // Sale/Lease
// );


/*
la/coop:[]
  _id:
  nm:
  agent:[]
    id:
    nm:
    tel:
*/

function getValues($dom){
  global $keyLabels,$keyLabelsClient,$mOffices,$mAgents;
  $clientView = false;
  $kl = $keyLabels;
  // not working
  // if (count($dom->find("label:contains('Listing Contracted With:')")) > 0 ){
  //   $clientView = true;
  //   $kl = $keyLabelsClient;
  //   echo "Client View\n";
  // }
  $ret = array();
  foreach($dom->find("label") as $label){
    //echo " => " . $label->innerHtml . " > " . $label->nextSibling()->getTag()->name() . "\n";
    foreach($kl as $key => $l){
      if (($label->innerHtml == $l) && ($label->nextSibling()->tag->name() == 'span')){
        if (!empty($ret['lsddt']) && ($key == 'lsdp') && ($label->nextSibling()->innerHtml == $ret['lsddt'])){
          //echo "Skip Leased for date.\n";
          continue;
        }
        if (!empty($ret[$key])){
          echo "Overwrite " . $key . " old:" . $ret[$key] . " with " . $label->nextSibling()->innerHtml . "\n";
        }
        //if ($key == 'lsdp'){
        //  echo "lsdp => " . $label->nextSibling()->innerHtml . "\n";
        //}
        $ret[$key] = $label->nextSibling()->innerHtml; //innerHtml;
      }
    }
  }
  $type = "unknown";
  $la_addr = null;
  foreach($dom->find("a") as $a){
    $href = $a->href;
    $href = str_replace("&#39;","'",$href);
    #echo $href . "\n";
    if (preg_match('/showOfficePopup\(.(\d+).\)/',$href,$matches) == 1){
      #echo "Find Office:" . $matches[1] . "\n";
      //print_r($a);
      if ($a->getParent()->firstChild() == $a){
        $type = "la";
        #$ret['rltraddr'] = $a->getParent()->getParent()->nextSibling()->innerHtml;
        try{
          $next = $a->getParent()->getParent()->nextSibling();
          //print_r($next->tag->name());
        }catch(Exception $e){
          $next = null;
        }
        if (empty($next) || $next->tag->name() != 'div'){
          try{
            $next = $a->getParent()->getParent()->getParent()->nextSibling();
            //print_r($next->tag->name());
          }catch(Exception $e){
            $next = null;
          }
        }
        while($next){
          if ($next->tag->name() == 'div'){
            $addr = [];
            foreach($next->find("span.value") as $v){
                $addr[] = $v->innerHtml;
            }
            $ret['rltraddr'] = join(", ",$addr);
            $next = null;
          }else{
            try{
              $next = $next->nextSibling();
            }catch(Exception $e){
              $next = null;
            }
          }
        }
      }else if ($a->previousSibling()->innerHtml == 'List:'){
        $type = "la";
        //print_r($a->getParent());
      }else if ($a->previousSibling()->innerHtml == 'Co-Op:'){
        $type = "coop";
      }else if ($a->previousSibling()->innerHtml == 'Listing Contracted With:'){
        // Client View
        $ret['rltr'] = $a->innerHtml;
        if (empty($ret['rltrtel'])){
          $pp = $a->getParent();
          $p = $pp->getParent();
          if ($p->lastChild() == $pp){ // div> span
            $ret['rltrtel'] = $p->getParent()->lastChild()->lastChild()->lastChild()->innerHtml;
          }else{
            $ret['rltrtel'] = $p->lastChild()->lastChild()->innerHtml;
          }
        }
        return $ret;
      }else{
        echo "Error Office Type:" . $a->previousSibling()->innerHtml . "\n";
        exit(1);
      }
      $office = array();
      $office['id'] = $matches[1];
      $office['_id'] = "TRB" . $matches[1];
      $office['nm'] = $a->innerHtml;
      if ($type == 'la'){
        $office['tel'] = $ret['rltrtel'];
        $office['fax'] = $ret['rltrfax'];
        if (!empty($ret['rltraddr'])){
          $office['addr'] = $ret['rltraddr'];
        }
        $ret['bid'] = $office['_id'];
        unset($ret['rltrtel']);
        unset($ret['rltrfax']);
        unset($ret['rltraddr']);
      }
      $ret[$type] = $office;
      $office['mt'] = new MongoDate();
      $mOffices->update(array('_id'=>$office['_id']),array('$set' => $office),array('upsert'=>true));
      #print_r($office);
    }else if (preg_match('/showMemberPopup\(.(\d+).\)/',$href,$matches) == 1){
      #echo "Find Realtor:\n";
      if (empty($ret[$type]['agnt'])){
        $ret[$type]['agnt'] = array();
      }
      $agent = array( 'id' => $matches[1],'_id' => ('TRB' . $matches[1]),'nm' => $a->innerHtml);
      if ($type == 'la'){
        $next = $a->getParent()->nextSibling();
        $i = 0;
        while ($i++ <= 2){
          if (strpos($next->class,'formfield')){
            $agent['tel'] = $next->firstChild()->innerHtml;
            //print_r($next);
          }else{
            $next = $next->nextSibling();
          }
        }
        if (count($ret[$type]['agnt']) == 0){
          $ret['aid'] = $agent['_id'];
        }else{
          $ret['aid2'] = $agent['_id'];
        }
      }
      $ret[$type]['agnt'][] = $agent;
      $agent['bid'] = $ret[$type]['_id'];
      $agent['mt'] = new MongoDate();
      #print_r($agent);
      $mAgents->update(array('_id'=>$agent['_id']),array('$set'=> $agent),array('upsert'=>true));
    }
  }
  return $ret;
}

function str_replace_first($from, $to, $subject){
    $from = '/'.preg_quote($from, '/').'/';
    return preg_replace($from, $to, $subject, 1);
}
function parseRoot($str){
  $str = str_replace_first('</a></li></ul></span><DIV','</a></li></ul><DIV',$str); // treb bug
  $ret = array();
  $dom = new Dom;
  $dom->load($str);
  $pages = $dom->find("div.report-container > div.report-container");
  foreach($pages as $page){
    $r = getValues($page);
    //print_r($ret);
    echo $r['ml_num'] . " ";
    if (!empty($r['lsdp']) && empty($r['lst'])){
      $r['lst'] = 'Lsd';
    }
    if (!empty($r['sp']) && empty($r['lst'])){
      $r['lst'] = 'Sld';
    }
    $ret[] = $r;
  }
  return $ret;
}



/* connect to gmail */
$hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
$username = '<EMAIL>';
$password = 'RealMasterCOM***';

//$pattern = "/\<a\shref\=\"(http\:.*?)\"/i";
$pattern = "/(http\:\/\/v3\.torontomls\.net[^>\"]*)/i";

/* try to connect */
$inbox = imap_open($hostname,$username,$password) or die('Cannot connect to Gmail: ' . imap_last_error());

/* grab emails */
$emails = imap_search($inbox,'UNSEEN SUBJECT "Properties"',SE_UID);
if(!$emails){
  $emails = imap_search($inbox,'UNSEEN SUBJECT "Property"',SE_UID);
}

echo date("Y-m-d h:i:sa") . "\n";

function getContent($url){
  $options = array(
    'http'=>array(
      'method'=>"GET",
      'header'=>"Accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\n" .
                "Accept-Encoding:gzip, deflate\r\n" .
                "Accept-Language:en-US,en\r\n" .
                "User-Agent:Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2228.0 Safari/537.36\r\n" // i.e. An iPad
    )
  );

  $context = stream_context_create($options);
  return file_get_contents($url, false, $context);
}

/* if emails are returned, cycle through each... */
if($emails) {
  //echo "Got Mails";

  /* put the newest emails on top */
  //rsort($emails);

  /* for every email... */
  foreach($emails as $email_number) {

    //echo "Mail" . $email_number;
    /* get information specific to this email */
    $overview = imap_fetch_overview($inbox,$email_number,FT_UID);//0);
    $message = imap_fetchbody($inbox,$email_number,2,FT_UID);
    //echo $message;
    if (strpos($message,'href=3D')){
      $message = imap_fetchbody($inbox,$email_number,1,FT_UID);
      //echo $message;
    }
    //$message = imap_body($inbox,$email_number,FT_UID);
    if (!strpos($message,'http:')){
      $message = base64_decode($message);
      //echo $message;
    }
    //echo $message;
    //$message = preg_replace( "/\r|\n/", "", $message );
    if (preg_match($pattern, $message,$matches)){
      /* begin output var */
      $output = '';

      //print_r($matches);
      $url = rawurldecode($matches[1]);
      echo $url . "\n";
      $contents = getContent($url);
      $contents = str_replace(array("\t","\n","\r"),'',$contents);
      #echo $contents . "\n";

      $popUp = array();
      $popUpDef = "/data\-pop\-up\=\"(\{.*?\})\"/";
      if (preg_match_all($popUpDef,$contents,$matches3,PREG_PATTERN_ORDER)){
        foreach($matches3[1] as $p){
          $p = html_entity_decode($p);
          //echo $p . "\n";
          $popUp[] = json_decode($p,true);
        }
      }
      //print_r($popUp);

      $record = array('_id'=>$url,"ts" => new MongoDate(),"m" => $contents, "data" => $popUp);
      $mTrebEmail->save($record);
      $listingUrl = $url;

      // parse html
      $listings = parseRoot($contents);
      echo "in mail " . count($listings) . "\n";

      $deferedUrls = array();
      //data-deferred-load=\"http://v3.torontomls.net/Live/Pages/Public/Link.aspx?Key=3b00f1cf712d4389958c845e6a6ac4d1&amp;App=TREB&amp;linkItem=476306779&amp;hint=A&amp;t=\"
      $deferedUrlsDef = "/data\-deferred\-load\=\"(http\:\/\/v3\..*?)\"/i";
      if (preg_match_all($deferedUrlsDef,$contents,$matches2,PREG_PATTERN_ORDER)){
        $deferedUrls1 = $matches2[1];
        foreach($deferedUrls1 as $url){
          $deferedUrls[] = rawurldecode(html_entity_decode($url));
        }
      }
      //print_r($deferedUrls);
      $deferedUrlsCopy = $deferedUrls;

      // load deferedUrls
      $deferedContents = array();
      while (count($deferedUrlsCopy) > 0){
        $toRead = array_splice($deferedUrlsCopy,0,rand(50,100));
        foreach ($toRead as $url){
          #$url = rawurldecode(html_entity_decode($url));
          echo $url . "\n";
          $m = getContent($url);
          $m = str_replace(array("\t","\n","\r"),'',$m);
          $deferedContents[] = $m;
          //echo $m . "\n";
          $listings = array_merge($listings,parseRoot($m));
        }
        $waitFor = rand(2,60);
        echo "Page Wait:" . $waitFor . "s\n";
        sleep($waitFor);
      }
      $record = array('_id'=>$listingUrl,"ts" => new MongoDate(),"m" => $contents,"dms"=>$deferedContents,"durls"=> $deferedUrls,"data"=>$popUp,"listings"=>$listings); //,'hd'=>$overview,'body'=>$message);
      $mTrebEmail->save($record);

      // merge objects
      $mergedListings = array();
      foreach($listings as $l){
        foreach($popUp as $p){
          if ($p['ml_num'] == $l['ml_num']){
            $mergedObj = array_merge((array) $l, (array) $p);
            $mergedListings[] = $mergedObj;
            // save to listings
            $mergedObj["_id"] = "TRB" . $l['ml_num'];
            $mergedObj['mt'] = new MongoDate();
            $mListings->update(array('_id'=>$mergedObj["_id"]),array('$set'=>$mergedObj),array('upsert'=>true));
            break;
          }
        }
      }


      $record = array('_id'=>$listingUrl,"ts" => new MongoDate(),"m" => $contents,"dms"=>$deferedContents,"durls"=> $deferedUrls,"data"=>$popUp,"listings"=>$listings,"fulldata"=>$mergedListings); //,'hd'=>$overview,'body'=>$message);
      $mTrebEmail->save($record);
      $waitFor = rand(5,60);
      #echo "Listing Wait:" . $waitFor . "s\n";
      sleep($waitFor);
    }
    $mSysData->save(array("_id" => "HadMail","ts"=> new MongoDate()));
    //imap_delete($inbox,$email_number);
  }

}

/* close the connection */
imap_close($inbox);

$mSysData->save(array("_id" => "ReadMailEnd","ts"=> new MongoDate()));

?>
