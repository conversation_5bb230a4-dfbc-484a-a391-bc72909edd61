[{"name": "paquettg/string-encode", "version": "0.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paquettg/string-encoder.git", "reference": "cf08343649701979f581c1601d01247fa3782439"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paquettg/string-encoder/zipball/cf08343649701979f581c1601d01247fa3782439", "reference": "cf08343649701979f581c1601d01247fa3782439", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "time": "2014-05-29 18:38:09", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"stringEncode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "description": "Facilitating the process of altering string encoding in PHP.", "homepage": "https://github.com/paquettg/string-encoder", "keywords": ["charset", "encoding", "string"]}, {"name": "paquettg/php-html-parser", "version": "1.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paquettg/php-html-parser.git", "reference": "18845e09831dd0772b88b51e788a4f74c701224c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paquettg/php-html-parser/zipball/18845e09831dd0772b88b51e788a4f74c701224c", "reference": "18845e09831dd0772b88b51e788a4f74c701224c", "shasum": ""}, "require": {"paquettg/string-encode": "~0.1.0", "php": ">=5.4"}, "require-dev": {"mockery/mockery": "~0.9.0", "phpunit/phpunit": "~4.8.0", "satooshi/php-coveralls": "~0.6.0"}, "time": "2016-04-06 15:24:40", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"PHPHtmlParser": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "description": "An HTML DOM parser. It allows you to manipulate HTML. Find tags on an HTML page with selectors just like jQuery.", "homepage": "https://github.com/paquettg/php-html-parser", "keywords": ["dom", "html", "parser"]}]