{"name": "paquettg/string-encode", "type": "library", "description": "Facilitating the process of altering string encoding in PHP.", "version": "0.1.1", "keywords": ["encoding", "charset", "string"], "homepage": "https://github.com/paquettg/string-encoder", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "autoload": {"psr-0": {"stringEncode": "src/"}}, "minimum-stability": "dev"}