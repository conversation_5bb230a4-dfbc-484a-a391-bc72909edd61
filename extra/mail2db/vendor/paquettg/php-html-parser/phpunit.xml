<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
	backupStaticAttributes="false"
	bootstrap="phpunit.php"
	colors="true"
	convertErrorsToExceptions="true"
	convertNoticesToExceptions="true"
	convertWarningsToExceptions="true"
	processIsolation="false"
	stopOnFailure="false"
	syntaxCheck="false"
>
	<testsuites>
		<testsuite name="Repository Test Suite">
			<directory>./tests/</directory>
		</testsuite>
	</testsuites>

	<filter>
		<whitelist addUncoveredFilesFromWhitelist="false">
			<directory suffix=".php">src</directory>
			<exclude>
				<directory suffix=".php">vendor</directory>
			</exclude>
		</whitelist>
	</filter>
</phpunit>
