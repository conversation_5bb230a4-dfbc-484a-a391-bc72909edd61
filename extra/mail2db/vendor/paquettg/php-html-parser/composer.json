{"name": "paquettg/php-html-parser", "type": "library", "version": "1.7.0", "description": "An HTML DOM parser. It allows you to manipulate HTML. Find tags on an HTML page with selectors just like jQuery.", "keywords": ["html", "dom", "parser"], "homepage": "https://github.com/paquettg/php-html-parser", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "require": {"php": ">=5.4", "paquettg/string-encode": "~0.1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.0", "satooshi/php-coveralls": "~0.6.0", "mockery/mockery": "~0.9.0"}, "autoload": {"psr-0": {"PHPHtmlParser": "src/"}}, "minimum-stability": "dev", "prefer-stable": true}