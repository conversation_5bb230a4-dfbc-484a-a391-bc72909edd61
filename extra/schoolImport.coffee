#for f in ~/work/data/school/*.csv; do coffee schoolImport.coffee $f; done
SchoolCollName = 'sch_rm_manual'
MongoClient = require('mongodb').MongoClient
ObjectId = require('mongodb').ObjectId
url = '*****************************************'
Converter = require('csvtojson').Converter
converter = new Converter(constructResult: false)
readStream = fs = require('fs')
path = require('path')
parseRank = (fieldRank,fieldTotal)->
  (v,obj)->
    if (p = v?.indexOf('/')) > 0 and v isnt 'n/a'
      vv = v.split('/')
      obj[fieldRank]  = parseInt vv[0]
      obj[fieldTotal] = parseInt vv[1]

getFloat = (field)->
  (v,obj)->
    if v?
      if /^\d*\.?\d*$/.test v
        obj[field] = parseFloat v
      else
        obj[field] = v

dic =
  School_Name: 'nm'
  School_Province: 'prov'
  School_URL: 'url'
  School_Url: 'url'
  School_District: 'board'
  School_Street: 'addr'
  School_Zip: 'zip'
  School_City: 'city'
  School_Tel: 'tel'
  School_Grade: 'grd'
  School_Type: 'tp'
  _id: '_id'
  Info_FrenchImmersion: getFloat 'fi'
dic2 =
  Rank: parseRank 'firank','fitotal'
  Rating: getFloat 'firate'
  Rank_5Y: parseRank 'filast5rank','filast5total'
  Rating_5Y: getFloat 'filast5rate'
  Info_ESL: getFloat 'fiesl'
  Info_SpecialNeeds: getFloat 'fispecial'
  Info_Income: getFloat 'fiincome'   #mark
dicReplace = (target,orig,dictionary)->
  for key,kk of dictionary
    if (v = orig[key])?
      if 'function' is typeof kk
        kk v,target
      else
        target[kk] = v
  target

MongoClient.connect url, (err, db) ->
  if err
    console.error err
    throw err
  Coll = db.collection(SchoolCollName)
  console.log process.argv[2]
  readStream.createReadStream(process.argv[2]).pipe converter
  insertDocument Coll,(err)->
    if err
      console.error err
      throw err
    console.log 'end'
    db.close (err)->
      process.exit()

schoolID = (sch)->
  nm   = sch.nm.replace(/School/ig,'').trim().replace(/\s+/g,'-').replace(/Elementary/ig,' E').replace(/Secondary/ig,' S').replace(/Public/ig,'P').replace(/[^A-Z0-9\-]/ig,'')
  prov = sch.prov.replace(/[^A-Z0-9]/ig,'')
  city = sch.city.replace(/[^A-Z0-9]/ig,'')
  ret = ("S-" + prov + "-" + city + "-" + nm).toUpperCase()
  #console.log (sch.nm + "=>" + ret)
  ret

schoolGrade = (sch)->
  switch sch.grd
    when 'Elementary'
      sch.ele = 1
    when 'Secondary'
      sch.hgh = 1
    when 'Middle'
      sch.mid = 1

insertDocument = (Coll,callback)->

  checkDone = ->
    if done >= todo
      console.log "#{todo}/#{done}"
      return callback()
  converter.on 'end_parsed', ->
    console.log('end_parsed')
    checkDone()

  done = 0
  todo = 0
  savedOne = (err)->
    if err
      console.error err
      throw err
    done++
    checkDone()
  converter.on 'record_parsed', (sch, rawRow, rowIndex) ->
    #console.log('record_parsed')
    todo++


    schoolObj = {}
    dicReplace schoolObj,sch,dic
    schoolObj.zip = schoolObj.zip.replace(/\s/g,'').toUpperCase()
    schoolGrade schoolObj
    schoolObj.eng = 1
    schoolObj._id = schoolID schoolObj
    Coll.findOne {zip:schoolObj.zip,prov:'ON'},(err,old)-> # if has provider school
      if err
        console.error err
        throw err

      if old?
        if schoolObj.nm.toUpperCase() isnt old.nm.toUpperCase()
          console.log "Skip #{old.zip}:#{old._id}:#{schoolObj._id} #{old.nm}:#{schoolObj.nm}"
        return Coll.updateOne {_id:old._id},{$set:{oID:schoolObj._id}},{w:1},savedOne

      Coll.findOne {_id:schoolObj._id},(err,old)-> # if update old
        if err
          console.error err
          throw err

        has = false
        for fi in (schoolObj.fraser = old?.fraser or [])
          if fi.yearmark is sch.Year
            dicReplace fi, sch,dic2
            has = true
        if !has and sch.Year?
          schoolObj.fraser.unshift dicReplace({ yearmark: sch.Year}, sch,dic2)
        unless old?.bns
          schoolObj.bns = [] # make sure we have bns field
        schoolObj.mt = new Date()
        unless old
          schoolObj.ts = schoolObj.ms
        Coll.updateOne {_id:schoolObj._id},{$set:schoolObj},{w:1,upsert:true},savedOne
