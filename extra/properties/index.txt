Properties

{status:1,saletp:1,ptype:1,lat:1,lng:1,ts:-1},{sparse:true} # map search

{status:1,saletp:1,ptype:1,prov:1,city:1,ts:-1} # list search

{favUsr:1},{ sparse: true }
{sid:1},{ sparse: true }

//{'loc.coordinates':'2d',status:1,saletp:1,ptype:1,ts:-1}
->createIndex(array('src'=>1,'ddfID'=>1,'_id'=>1));


var bulkInsert = db.properties_u.initializeUnorderedBulkOp();
var bulkRemove = db.properties.initializeUnorderedBulkOp();
var x = 1000;
var counter = 0;
db.properties.find({status:'U'}).forEach(
    function(doc){
      bulkInsert.insert(doc);
      bulkRemove.find({_id:doc._id}).deleteOne();
      counter ++;
      if( counter % x == 0){
        bulkInsert.execute();
        bulkRemove.execute();
        bulkInsert = db.properties_u.initializeUnorderedBulkOp();
        bulkRemove = db.properties.initializeUnorderedBulkOp();
      }
    }
  )
bulkInsert.execute();

// Add sbid to schools
db.school_board.find({_id:/BOARD$/}).forEach(
  function(board){
    var boardname = board.nm.replace(' School Board','');
    db.school.update({sbid:{$exists:0},board:boardname},{$set:{sbid:board._id}},{multi:1});
  }
)
