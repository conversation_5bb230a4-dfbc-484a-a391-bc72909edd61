# coffee me.coffe [folder]
db = require('../../batch/dblocal')
exit = db.exit
puts = console.log
fs = require 'fs'
path = require 'path'
util = require 'util'
csv = require 'fast-csv'
if process.argv.length > 2 and process.argv[0] is 'coffee'
  avgs = process.argv.slice(2)

dryRun = false
if avgs[0] is 'dryrun'
  dryRun = true
  avgs = avgs.slice 1

coll = null

findError = (e)->
  console.log e
  exit 1

keymap =
  Name: 'nm'
  URL: 'url'
  Phone: 'tel'
  Email: 'eml'
  Gender: 'sex'
  'Size': 'sz'
  Grades: 'grd'
  'Boarding':'boarding'
  Founded: 'fndd'
  'Religion': 'religion'
  'EnrollmentStudentsBoarding':'enrlBoarding'
  'EnrollmentStudentsBoardingGrades':'enrlBoardingGrd'
  'EnrollmentStudentsDay':'enrlDay'
  'EnrollmentStudentsDayGrades':'enrlDayGrd'
  'TuitionFeeDay':'tuitn'
  'TuitionFeeBoarding':'tuitnBoarding'
  'AdmissionRate':'admRt'
  AdmissionInterview: 'admIntvw'
  AdmissionSSAT:  'admSSAT'
  SourceURL: 'surl'
  Hot: 'hot'

importFile = (file,cb)->
  puts "Import: #{file}"
  headers = null
  cTodo = 0
  cDone = 0
  inputDone = false
  convertObj = (orig)->
    obj = {}
    addrs = orig.Addr.split ':'
    #obj.schname = addrs[0]
    obj.addr = addrs[1].trim()
    if obj.addr?.length < 1
      delete obj.addr
    obj.city = addrs[2].replace(',','').trim()
    obj.prov = addrs[3].trim()
    obj.zip = orig.AddrZip or addrs[4].trim()
    for k,v of orig
      if nk = keymap[k]
        obj[nk] = v
    obj._id = obj.surl.substr(obj.surl.indexOf('school_id=') + 10)
    puts obj if obj.hot
    obj

  saveObj = (obj)->
    obj = convertObj obj
    if dryRun
      cDone++
      return checkDone()
    coll.updateOne {_id:obj._id},{$set:obj},{upsert:true,w:1},(err,ret)->
      if err then return findError err
      cDone++
      checkDone()
  checkDone = ->
    if inputDone and (cDone >= cTodo)
      puts "Line:#{cTodo} Object:#{cDone}"
      if fn = cb
        cb = null
        fn()
      else
        throw new Error "Called check_done twice"
  stream = fs.createReadStream file,{encoding:'utf8'} # 'ascii'}
  parser = csv.fromStream(stream).on 'data',(d,index)->
    if not headers? # 1st line
      headers = []
      for k in d
        if k
          headers.push k.trim().replace /^School/,''
      puts headers
    else
      cTodo++
      o = {}
      for k,i in headers
        if (v = d[i])? and (('string' isnt typeof v) or v.length > 0) # either not string or not an empty string
          o[k] = v
      saveObj o
  parser.on 'end',(count)->
    inputDone = true
    checkDone()

finish = ->
  exit 0

importData = (file)->
  db.mongo (err, MongoDB)->
    console.log "Start Importing"
    if err then return errorNotify err,-> exit 1,err
    db = 'vow'
    coll = new MongoDB(db,'private_schools')
    importFile file,->
      finish()
importData avgs[0]
