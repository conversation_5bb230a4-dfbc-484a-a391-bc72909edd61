### TODO: add apis, change this file name

POST http://d5.realmaster.com/1.5/props/autocompleteGetNext HTTP/1.1
content-type: application/json
cookie: apsv=appDebug; appmode=mls; locale=zh; cmate.sid=eRITcycNatMiPNhu6omIEtk84FnOLcTKwSdJ2ew796Vlo35gNJ2mDylZm864j8k9;

{
    "s": "20 bruyeres",
    "ts": 1654022632763
}

###

POST http://app.test:8080/1.5/props/search HTTP/1.1
content-type: application/json

{
    "bbox": [-79.49290374661784, 43.6183363693533, -79.36842775338549, 43.745323552017936],
    "hasWechat": false,
    "label": 1,
    "no_mfee": false,
    "oh": false,
    "page": 210,
    "ptype": "Residential",
    "saleDesc": "Sale",
    "saletp": "sale",
    "soldOnly": true,
    "sort": "auto-ts",
    "src": "mls",
    "ts": 1654022781242
}

###

POST http://app.test:8080/1.5/props/search HTTP/1.1
content-type: application/json

{
    "city": "Toronto",
    "hasWechat": true,
    "label": 1,
    "no_mfee": false,
    "oh": false,
    "page": 0,
    "prov": "ON",
    "ptype": "Residential",
    "saleDesc": "Sale",
    "saletp": "sale",
    "soldOnly": true,
    "sort": "auto-ts",
    "src": "mls",
    "ts": 1654022978458
}

###

POST https://d1.realmaster.com/1.5/props/detail HTTP/1.1
content-type: application/json

{
    "_id": "TRBN6699572", 
    "locale": "en"
}


###

POST http://app.test:8080/1.5/props/search HTTP/1.1
content-type: application/json

{
    "centerLat": 43.6183363693533, 
    "centerLng": -79.49290374661784,
    "dist": 2000,
    "label": 0,
    "page": 0,
    "ptype": "Residential",
    "saleDesc": "Rent",
    "saletp": "rent",
    "sort": "dist-asc",
    "src": "mls",
    "ts": 1654022781242
}

###

#######
POST http://d1.realmaster.com/1.5/props/addrSuggest HTTP/1.1
content-type: application/json

{
  "s": "lower simcoe"
}