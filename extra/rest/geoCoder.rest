### geocoding service rest file, detail see:  Dev_geocoding/geocoding.md
### depending on SITE file setting, change www.test/app.test in cfg and header file
POST http://www.test:8080/geocodingService/edit HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY

{
  "geoCodingResult": {
    "mt": "2023-04-17T18:24:19.542Z",
    "addr": "51 Acadia Ave",
    "city": "Markham",
    "cnty": "CA",
    "faddr": "51 Acadia Avenue, Markham, Ontario L3R 5Z2, Canada",
    "lat": 43.820305,
    "lng": -79.33042,
    "prov": "ON",
    "q": 100,
    "qaddr": "51 Acadia Ave, Markham, ON, CA",
    "src": "mapbox",
    "st": "Acadia",
    "st_num": "51",
    "st_sfx": "Ave",
    "zip": "L3R5Z2",
    "_id": "CA:ON:MARKHAM:51 ACADIA AVE",
    "doCoding": true
  },
  "uaddrAfterFormat": "CA:ON:MARKHAM:51 ACADIA AVE",
  "forceUpdate": false
}


### test geocoding(mapbox), use addr only
POST https://www.realmaster.com/geocodingService/geocoding HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY

{
  "cnty": "Canada",
  "prov": "ON",
  "city": "Markham",
  "addr": "51 Acadia Avenue", 
  "engineType": "mapbox"
}

### test geocoding(mapbox), use st + st_num
POST http://www.test:8080/geocodingService/geocoding HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY

{
  "cnty": "Canada",
  "prov": "ON",
  "city": "Markham",
  "st_num": "50",
  "st": "Acadia Avenue", 
  "engineType": "mapbox"
}


### send rever geocoding request(bing)
GET http://www.test:8080/geocodingService/reverseGeocoding?lat=43.821146&lng=-79.3290404&engineType=bing HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY


### send rever geocoding request(bing)
GET http://www.realmaster.com/geocodingService/reverseGeocoding?lat=43.821146&lng=-79.3290404&engineType=mapbox HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY


### send geo req to /1.5/geocoding used in native
POST https://d1w.realmaster.com/1.5/geocoding HTTP/1.1
content-type: application/json
authorization: Bearer aUIh0DMLQY
Cookie: apsv=appDebug;

{
  "addr": "51 Castan Avenue, Unionville, ON, Canada"
}