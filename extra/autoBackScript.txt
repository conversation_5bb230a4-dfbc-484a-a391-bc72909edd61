#rsync -avz -e "ssh -p $portNumber" user@remoteip:/path/to/files/ /local/path/

#rsync -avz -e "ssh -p 27" schimgs20150519 apptest@njj:/var/www/node/appweb/imgs

http://troy.jdmz.net/rsync/index.html

#!/bin/sh

RSYNC=/usr/bin/rsync
SSH=/usr/bin/ssh
KEY=/home/<USER>/cron/thishost-rsync-key
RUSER=remoteuser
RHOST=remotehost
RPATH=/remote/dir
LPATH=/this/dir/

$RSYNC -az -e "$SSH -i $KEY" $RUSER@$RHOST:$RPATH $LPATH

--remove-source-files
--dry-run


ls -al * | mail -s "Test Subject" me@email


backup: net1os @ aken  rmbkup
Weekly fullbackup
Daily chomeca backup

Procedure:
 Server Create backup and zip/tar, remove folders
 aken fetch backup, remove source
 email folder list to admin


time mysqldump -u u --password=p vow > #DATE#_vow.sql      1m
time gzip #DATE#_vow.sql	1m40s

mongodump -u u -p p -d chomeca -o #DATE#_mongo
mongodump -u u -p p -d vow -o #DATE#_mongo
tar czf #DATE#_mongo.tgz #DATE#_mongo/


to omit from full backup:
  mongo:
    mls_treb in vow
  mysql:


fullbackup: bkjob.sh
>>>
#!/bin/sh

DATE=`date +%Y-%m-%d`
BKPATH=/home/<USER>/bk

time mysqldump -u vow_u --password=vow_p vow > $BKPATH/$DATE-vow.sql
time gzip $BKPATH/$DATE-vow.sql

time mongodump -u chomeca_u -p chomeca_p -d chomeca -o $BKPATH/$DATE-mongo
time mongodump -u vow_u -p vow_p -d vow -o $BKPATH/$DATE-mongo

cd $BKPATH
time tar czf $DATE-mongo.tgz $DATE-mongo/
rm -r -f $DATE-mongo/

ls -al * | mail -s "Full backup file list" <EMAIL>
<<<

incremental backup:
>>>
#!/bin/sh

DATE=`date +%Y-%m-%d`
BKPATH=/home/<USER>/bk

time mysqldump -u vow_u --password=vow_p --ignore-table=vow.mls_treb_old vow > $BKPATH/$DATE-vow-i.sql
time gzip $BKPATH/$DATE-vow-i.sql

time mongodump -u chomeca_u -p chomeca_p -d chomeca -o $BKPATH/$DATE-mongo-i
#time mongodump -u vow_u -p vow_p -d vow -o $BKPATH/$DATE-mongo

cd $BKPATH
time tar czf $DATE-mongo-i.tgz $DATE-mongo-i/
rm -r -f $DATE-mongo-i/

ls -al * | mail -s "Incremental backup file list" <EMAIL>
<<<
