semanage port -a -t mongod_port_t -p tcp PORT
https://liquidat.wordpress.com/2016/05/04/howto-workaround-failing-mongodb-on-rhelcentos-7/
grep mongod /var/log/audit/audit.log | audit2allow -M mongod
semodule -i mongod.pp

split in parts

fields =
  ml_num:
    nm:'mls#'
    reg: "<label>MLS#:</label><span class=\"value\">([A-Z][0-9]+)</span>"
  lat:
  lng:
  addr:
  sp:
    nm: 'Sold Price'
    reg: "<label>Sold:</label><span class=\"value\" style=\"font-weight:bold\">$([0-9\,\.]+)</span>"
  lp:
    nm: "List Price"
    reg "<label>List:</label><span class=\"value\" style=\"font-weight:bold\">$([0-9\,\.]+)</span>"
  lst:(lsc)
    nm: 'Last Status'
  slddt:
    nm: 'Sold Date'
  bm:
    nm: 'Brokerage Remarks'
    reg: "<label style="\&quot;text-align:left;;display:inline;\&quot;">Brkage Remks:</label><span class="\&quot;value\&quot;" style="\&quot;display:inline;\&quot;">(.*?)</span>"
  comm:
    nm: 'Commission'
    reg: "<label>CB Comm:</label>"
  ctrdt:
    nm: "Contract Date"
    reg: "Contract Date:"
  exp:
    nm: "Expiry Date"
    reg: "Expiry Date:"
  seller:
    nm: "Sellers"
    reg: "Seller:"
  bid:
    nm: "Listing Brokerage ID"
    reg: "<a href="\&quot;Javascript:showOfficePopup('102300');\&quot;" class="\&quot;formlink" value\"="" style="\&quot;display:inline;\&quot;">SUTTON GROUP QUANTUM REALTY INC., BROKERAGE</a>"
  rltr:
    nm: "Listing Brokerage"
    reg: "<span class="\&quot;formitem" formfield\"=""><label style="\&quot;text-align:left;;display:inline;;font-weight:bold\&quot;">List:</label><a href="\&quot;Javascript:showOfficePopup('102300');\&quot;" class="\&quot;formlink" value\"="" style="\&quot;display:inline;\&quot;">SUTTON GROUP QUANTUM REALTY INC., BROKERAGE</a></span>"
  rltrtel:
    nm: "Listing Brokerage Tel"
    reg: "Ph:"
  rltrfax:
    nm: "Listing Brokerage Fax"
    reg: "Fax:"
  rltraddr:
    nm: "Listing Brokerage Address"


  la:[]
    id:
      nm: "Treb ID"
      reg: "showMemberPopup('9528393')"
    nm:
      nm: "Listing Agent Name"
      reg: "<a href="\&quot;Javascript:showMemberPopup('9528393');\&quot;" class="\&quot;formlink" value\"="">LINDSEY DE MELO, Broker</a>"
    tl: "Listing Agent Title"
    tel: "Listing Agent Tel"
  coopbrkg:
    nm: "Co-Op Brokerage"
    reg: "<span class="\&quot;formitem" formfield\"=""><label style="\&quot;text-align:left;;display:inline;;font-weight:bold\&quot;">Co-Op:</label><a href="\&quot;Javascript:showOfficePopup('142500');\&quot;" class="\&quot;formlink" value\"="" style="\&quot;display:inline;\&quot;">ROYAL LIFE REALTY INC., BROKERAGE</a></span>"
  coop:[]
    nm: "Co-Op Realtor Name"
    tl: "Co-Op Realtor Title"
    reg: "<a href="\&quot;Javascript:showMemberPopup('9532566');\&quot;" class="\&quot;formlink" value\"="">T.C. Zhang, Salesperson</a>"
