Mongo Collection
Name: image_to_process
fields:
  _id: auto
  ts: job created timestamp
  wpid: WePage._id    # check existing
  uid: user._id       # check vip/vvip roles
  src: source url     # check valid, and in wepage
  dstn: 's3' or others
  url: upload url     # assign id before creating job
  st: job starting timestamp
  dt: done timestamp  # when done
  err: Error message

Job:
  - Grouping by wpid, sort by ts
  - do each file one by one, and check before doing
  - update wepage when each group done


Job queued. Process after editing finished.
