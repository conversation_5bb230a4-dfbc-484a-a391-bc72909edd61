server:
webroot/public
  /ap
    /h: compiled html entry files
    /js
    /img
    /css

client side:
  rmreq 'route',[data,](data)->
  rmon 'event-name',(data)-> # optional

  return from server:
    {
      e: error message (l10n-ed)
      ec: error code
      xtr:[]{} extra event (not related with original request) # optional
        evt:'event-name'
        d: {} event data feed
    }

  common procedure:
    ec: 1 - need login, other - error code
    xtr: # optional
