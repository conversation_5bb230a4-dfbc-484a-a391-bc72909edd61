BranchName: [release_import_delete]
Author: [<PERSON> <PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]


1. run evowDeleteIds 
   nohup ./start.sh lib/batchBase.coffee batch/prop/evowDeleteIds.coffee > ./logs/evowDeleteIds.log 2>&1 &
2. run watch with  -t deleteEvow init (to generate init token)
   ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t deleteEvow init -h 24
  
   kill the process after watch is started.

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta -t deleteEvow force > ./logs/watchImportToProp.log 2>&1 &


3. fix Status For Commercial
   ./start.sh lib/batchBase.coffee batch/correctData/20220614FixStatusForCommercial.coffee [dryRun]

4. fix poss date
   ./start.sh lib/batchBase.coffee batch/correctData/20220614FixPossDate.coffee