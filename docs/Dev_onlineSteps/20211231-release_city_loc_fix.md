BranchName: [release_city_loc_fix]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch: 
Detail: 
  fix popular city, change limit to 50 to show all cities under prov
  fix geo coding manual fix bug, fix lat,lng bug in import

## step
   
  1. restart server
  2. no need to restart watch, saveMater is auto reload
  3. admin to fix the lat lng of TRBS5460194 using app Edit Loc or by db command

    db.getCollection('properties').updateOne({_id:'TRBS5460194'},{$set:{lat:44.5000001, lng:-79.48642989999999,Q:130}})
    db.getCollection('geo_cache').updateOne({aUaddr:'CA:ON:ORO-MEDONTE:135 10 LINE S'},{$set:{lat:44.5000001, lng:-79.48642989999999, q:130, src:'manual'}})

Online Date:[(2022-01-04 15:48)]
- [X] restart server

