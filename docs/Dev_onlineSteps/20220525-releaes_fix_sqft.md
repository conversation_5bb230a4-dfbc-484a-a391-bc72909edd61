BranchName: [releaes_fix_sqft]
Author: [<PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/prop/addRmSqft.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

2. buildPropSqft
  nohup ./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee init > ./logs/buildPropSqft.log 2>&1 &
   40min


3. run batch, clean old data.
   nohup ./start.sh lib/batchBase.coffee batch/prop/addRmSqft.coffee > ./logs/addRmSqft.log 2>&1 &
   1 hour
