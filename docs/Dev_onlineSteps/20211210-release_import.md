BranchName: [release_import]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch:

Detail: 
  add limit to geocoding 
  add more log to photodownload
## step

1 check rni 
db.getCollection('properties_import_log').createIndex({'geocoding.doCoding':1,'_mt':-1})
db.getCollection('properties_import_log').count( {'geocoding.doCoding':true, fixed:null,_id:/property$/})
12603 (can fix in one round)

1. run add fixGeocodingAfterProvClean   
  screen -r
  ./start.sh lib/batchBase.coffee batch/correctData/fixGeocodingAfterProvClean.coffee > logs/fixGeo.log 2>&1 &

2. restart download photo and check log. should print log after 10 second.
  kill photoDownload
  screen -r
  nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1 &
  nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1 & 

Online Date:[(2021-12-15 14:37)]
- [X] run batch
- [X] restart photo download
