BranchName: [student_rental]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

## Update banner for app
db.getCollection('ads').insertOne({
   "p" : "banner",
   "src" : "/img/student_rental/app_banner_en.jpg",
   "st" : "A",
   "lang" : [ 
      "en"
   ],
   "tgt" : "/student_rental?",
   "inapp" : true,
   "cAndR" : false,
   "rOnly" : false,
   "cOnly" : false,
   "noend" : true,
   "cipOnly" : true,
   "ts": ISODate("2022-06-20T18:55:56Z")
})
db.getCollection('ads').insertOne({
   "p" : "banner",
   "src" : "/img/student_rental/app_banner_cn.jpg",
   "st" : "A",
   "lang" : [ 
      "zh", 
      "zh-cn"
   ],
   "tgt" : "/student_rental?",
   "inapp" : true,
   "cAndR" : false,
   "rOnly" : false,
   "cipOnly" : true,
   "noend" : true,
   "ts": ISODate("2022-06-20T18:55:56Z")
})