BranchName: [release_import]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

fix idx set status U bug.
# steps: 
clean db function：
under rni:
db.getCollection('mls_treb_master_ids').find({lSrc:'vow',src:{$nin:['idx','dta']}}).forEach(function(doc){
    print(doc._id)
    db.getCollection('mls_treb_master_records').updateOne({_id:'TRB'+doc._id},{$set:{status:'U'}})
})

 db.getCollection('mls_treb_master_ids').deleteMany({lSrc:'vow',src:{$nin:['idx','dta']}})

- kill and restart treb download 
  
   ```
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow EV20ywa 'C8p6\$39' > logs/trebEVOW.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx D20ywa 'H9p3\$63' > logs/trebIDX.log 2>&1 &
   ```
