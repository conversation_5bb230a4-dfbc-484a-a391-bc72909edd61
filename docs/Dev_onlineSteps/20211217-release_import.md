BranchName: [release_import]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch: 
Detail: 
  fix null in check pricechange

## step
  1. kill watchAndImportToProperties (try not use -9, it will do graceful exit)
   
  2. restart watch
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcre -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

  
Online Date:[]
- [X] restart watch
