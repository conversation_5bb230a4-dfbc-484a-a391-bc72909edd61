BranchName: [geoconflict]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch: 
Detail: 
  1. Add refineGeocoding batch to group lat and lng of condo and townhouse from properties.And adjust geo_cache.
  2. Add geoConflict tag for record in geo cache which need manually resolve 
  3. Add admin page to search and resolve conflict, fix geo cache to 130, fix all properties if needed.

## step
   
  1. run batch
  nohup ./start.sh lib/batchBase.coffee batch/prop/refineGeocoding.coffee > logs/refineGeocoding.log 2>&1 &

  1. restart server
  
Online Date:[]
- [] run batch
- [] restart server
- [] Notify admin to resolve Conflict.
  
