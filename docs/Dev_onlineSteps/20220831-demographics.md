BranchName: [Dmographics]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

# for local environment
## if need to update all census bbox, go to 1.5/census/generateMapImage, then run on db
### build new collection with bbox
db.getCollection('census2016').find({bbox:{$ne:null}}).forEach(function(doc){
    if (doc._id) {
        db.getCollection('census2016_bbox').insertOne({_id:doc._id,bbox:doc.bbox});
    }
})
### dump censusu2016_bbox
mongodump -d tdlisting -c census2016_bbox -u tdlisting_u -p tdlisting_p --query ''
### push to server
# end of for local environment


1. make sure rmlfs has /imgstocks/census.tar.gz
    go src
    ./bin/imgstocks.sh

2. make sure rmlfs has fileStorage/census2016_bbox.bson
    cd fileStorage
    mongorestore --drop -u d1 -p d1 --authenticationDatabase 'admin' --port 27018 -d listing2 -c census2016_bbox census2016_bbox.bson
    # set bbox to census2016
    db.getCollection('census2016_bbox').find({bbox:{$ne:null}}).forEach(function(doc){
        if (doc._id) {
            db.getCollection('census2016').updateOne({_id:doc._id},{$set:{bbox:doc.bbox}});
        }
    })