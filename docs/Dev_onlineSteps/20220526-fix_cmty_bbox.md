BranchName: [imgstocks]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: add imgstock.sh to compress/uncompress imgstocks/cmtys imagets

## fix db data
db.getCollection('stat_uname').updateOne({_id:'CA:ON:BARRIE:INNIS-SHORE'},{$set:{"bbox" : [ -79.633443, 44.349073,-79.603595,44.372118]}})

db.getCollection('cmty_image').updateOne({_id:'CA:ON:BARRIE:INNIS-SHORE'},{$set:{o:{
      "zoom" : 13.0358918392812,
      "bbox" : [ 
          -79.6453099463254, 
          44.3478756094763, 
          -79.5917280536778, 
          44.3733148951874
      ],
      "fn" : "ca_on_barrie_innis_shore"
  },z:{
      "zoom" : 14.0358918392812,
      "bbox" : [ 
          -79.6453099463254, 
          44.3478756094763, 
          -79.5917280536778, 
          44.3733148951874
      ],
      "fn" : "ca_on_barrie_innis_shore_z"
  }}})

## clone rmlfs repository
<NAME_EMAIL>:fredxiang/rmlfs.git

## uncompress cmty images
go src
./bin/imgstocks.sh
cd webroot/public/img/imgstocks/cmtys/
ls | wc -l

check webroot/public/img/imgstocks/cmtys has 9396 images


Online Date:[] ca8/ca1/shf1
- [] batch