BranchName: [get_sqft]
Author: [<PERSON> & <PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. buildPropSqft
   nohup ./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee init > ./logs/buildPropSqft.log 2>&1 &
   40min results:

   ```
   FINISHED { ptype: 'r', m: /\d{3,}/ } {
   gProcessed: 4900590,
   gFound: 789811,
   gNoAddress: 433,
   gInvalid: 400280
   } proccessed:91.48K/m,found:16.05K/m,Possible year number:839.2/m,Other:1.192K/m,Possible highway:52.12/m,original:33.54K/m
   ```

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

3. addRmSqftToProperties
   nohup ./start.sh lib/batchBase.coffee batch/prop/addRmSqft.coffee > ./logs/addRmSqft.log 2>&1 &
   1 hour
   expected:
   db.getCollection('properties').count({rmSqft:{$ne:null}}) : ~3,676,970
   ```
   FINISHED { ptype: 'r' } { gProcessed: 7458384, gNoAddress: 344 } found:121.4K/m,unit:71.74K/m,notFound:124.9K/m,self:38.03K/m,updated:5.604K/m,unset:4.751K/m,ending:11.68K/m
   ```
4. addBuiltYear
   nohup ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee force > ./logs/addBuiltYear.log 2>&1 &
   1hour

   ```
   FINISHED { ptype: 'r', uaddr: { '$ne': null } } {
     gProcessed: 7733424,
     gNoAddress: 0,
     gFound: 2470731,
     gNotFound: 5262693
   } NotFound:67.46K/m,Found:31.67K/m
   ```

5. add addBuiltYear.coffee in crontab, once a month

   ```
   0 1 1 * * /cfg/start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee >> /cfg/logs/addBuiltYear-monthly.log 2>&1
   ```
