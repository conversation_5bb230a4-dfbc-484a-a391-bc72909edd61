上线说明：


ca8(batch):
migrate user data, keep existing fields under user for now;
#手动
dont run ./start.sh to restart server
./start.sh batch/user/userProfileAndLog.coffee dry
./start.sh batch/user/userProfileAndLog.coffee > logs/userModelLog.txt 2>&1

TEST before migrate

ca2(app): git pull
#自动auto migrate:
user_profile_log.coffee

TEST after migrate

./start.sh batch/user/userProfileAndLog.coffee unset
migrate user new operations, unset fields


#手动
db.user_profile.runCommand( { compact : 'user_profile' } )
db.user_log.createIndex {exp:1},{expireAfterSeconds:0}