上线说明：
1. import boundary collection. dump/listing2/geo_cache.bson.gz
mongorestore --port 27018 --authenticationDatabase admin -u d1 -p d1 --db listing2 -c geo_cache geo_cache.bson.gz --gzip


2 ./start.sh batch/correctData/addBboxToBoundary.coffee (也可以直接dump 3号服的数据库)

3. nohup ./start.sh batch/correctData/fixPropertyUaddr.coffee  > ./logs/fixPropertyUaddr.log &
d2listing约40分钟。

4. 跑proptaginit 10+Hours
nohup ./start.sh batch/tag/addTagToProperty.coffee init school prop census census2016 transit boundary force > ./logs/proptag.log &

5. proptag routine 1H
nohup ./start.sh batch/tag/addTagToProperty.coffee routine school prop census census2016 transit boundary > ./logs/proptagRoutine.log &


========  先不做===

6. 跑statUname
./start.sh statUname.py

7. 跑statUnameTag
./start.sh batch/tag/addTagToStatUname.coffee stat
大约1小时。

8. 跑statMain
nohup ./start.sh statMain.py init all all all > ./logs/stat.log 2>&1 & 
nohup ./start.sh statMain.py routine all all all > ./logs/stat.log 2>&1 & 


