
1. kill mongo/sql watch on all server
2. ca3 修改oh-》ohz，ohv-》ohzv，remove ohvids

./start.sh batch/correctData/changePropOhType.coffee
3. update src, wait batch done
nohup ./start.sh batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchPropMongo.log 2>&1 &


4. update src, restart watch with init on all server
screen -r
nohup ./start.sh batch/prop/watchPropAndUpdatePostSql.coffee init > logs/watchPropSql.log 2>&1 &






1. alert table on all server
#or restart watch with init
ALTER TABLE props_oh ADD COLUMN "tp" varchar(32) , ADD COLUMN "l" varchar(1024)





