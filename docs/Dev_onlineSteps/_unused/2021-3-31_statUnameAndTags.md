
1. 重做tag init，定期跑tag，先上线tag后台代码，跑tag，然后上线社区代码
  check app_config propTagFilePath, default: '/tmp'
  nohup ./start.sh batch/tag/addTagToProperty.coffee init school prop census census2016 transit boundary force >> ./logs/proptag.log &

routine 增加tag，每小时一次,add to crontab
  nohup ./start.sh batch/tag/addTagToProperty.coffee school prop census census2016 transit boundary force >> ./logs/proptag.log &


2. checkout python stat branch. folder location same with cfglocal.
3. update cfglocal master
4. run coffee ./setup
5. add job_daily.sh in crontab.


-----------------
6. If need to run stat tag separate.
  1. 跑statUname，run every day or week (3年数据，45min)
    nohup ./start.sh statUname.py > ./logs/stat.log 2>&1 & 
  2. addTagToStatUname, 给statunmae 增加tag。需要statUname 生成之后在跑。间隔2小时。
    nohup ./start.sh lib/batchBase.coffee batch/tag/addTagToStatUname.coffee stat > ./logs/addTagToStatUname.log 2>&1 & 

7.建议先上tags，数据运转一个星期然后在上社区的修改。
