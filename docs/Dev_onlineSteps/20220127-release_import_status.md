BranchName: [release_import]
Author: [<PERSON>]
BatchName: [20220127fixPropertiesStatus]
DB to Restore: [N/A]
Sales Doc: [N/A]

fix mt null bug

# steps: 
1. restart watch
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta routine> ./logs/watchImportToProp.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta routine force -h 0.1 >> ./logs/watchImportToProp.log 2>&1 &

2. run batch
./start.sh lib/batchBase.coffee batch/correctData/20220127fixPropertiesStatus.coffee
   ```
