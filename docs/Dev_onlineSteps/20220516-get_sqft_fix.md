BranchName: [get_sqft]
Author: [<PERSON> <PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]


1. buildPropSqft
  nohup ./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee init > ./logs/buildPropSqft.log 2>&1 &
   40min

   FINISHED { ptype: 'r', m: /\d{3,}/ } {
  gProcessed: 4776249,
  gFound: 1010429,
  gNoAddress: 216,
  gInvalid: 385951
}

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

3. addRmSqftToProperties
   nohup ./start.sh lib/batchBase.coffee batch/prop/addRmSqft.coffee > ./logs/addRmSqft.log 2>&1 &
   1 hour
   expected:
   db.getCollection('properties').find({rmSqft:{$ne:null}})

4. addBuiltYear
  nohup ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee > ./logs/addBuiltYear.log 2>&1 &
  1hour
  