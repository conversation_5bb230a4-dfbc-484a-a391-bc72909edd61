BranchName: [fix_rmlisting_id]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

# fix duplidate rmlisting
error: one listing _id is ObjectId, another _id is rm-listing


## delete old one
db.properties.deleteOne({_id:ObjectId('62847571f6a1b3182eddde76')});

## fix _id with new one
rmProp = db.properties.findOne({_id:'RM1-52777'});
rmProp._id = ObjectId('62847571f6a1b3182eddde76');
db.properties.insert(rmProp);
db.properties.remove({_id:'RM1-52777'},{$set:{_id:ObjectId('62847571f6a1b3182eddde76')}});

