- BranchName: `rahb-download-import`
- Author: Charlie
- BatchName: `rahbDownloadOfficeAndMember.coffee`, `rahbDownload.coffee` and `watchAndImportToProperties.coffee`
- DB to Restore:
  - `vow.agents`
  - `vow.offices`
  - `rni.mls_import_keys`
  - `rni.mls_rahb_records`

## Restore DB
RAHB downloader and import is running on `appd8@ca5`, dump from there and restore to production server to skip run from scratch.

Dump db on d8:
```
mongodump --username d1 --password d1 --authenticationDatabase admin --host localhost --port 27018 -d=listing -c=offices -q='{"src":"RHB"}' --gzip -o ./dump

mongodump --username d1 --password d1 --authenticationDatabase admin --host localhost --port 27018 -d=listing -c=agents -q='{"src":"RHB"}' --gzip -o ./dump

mongodump --username rni --password rni --authenticationDatabase admin --host localhost --port 27017 -d=rni -c=mls_import_keys -q='{"_id":{"$in":["RahbPropertyDownload","RahbOfficeAndMemberDownload"]}}' --gzip -o ./dump

mongodump --username rni --password rni --authenticationDatabase admin --host localhost --port 27017 -d=rni -c=mls_rahb_records --gzip -o ./dump
```

copy those db dump file to production server, and restore DB.
`vow.agents`, `vow.offices` and `rni.mls_import_keys` contain old data still using, DO **NOT** drop whole collections while restore.

## Start Downloader
@fred
You will need Server Token get from https://bridgedataoutput.com/myApplication/overview
@ca8
```
screen -r

nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownload.coffee -t [token] force > logs/rahbDownload.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownloadOfficeAndMember.coffee -t [token] force > logs/rahbOfficeDownload.log 2>&1 &
```

## watchAndImportToProperties
start `watchAndImportToProperties` batch with `-t rahb` or restart old running `watchAndImportToProperties` and add new parameter `-t rahb`.
for first time run, use `init` and `-h` to specify back hours, because `watchAndImportToProperties` doesn't support start import from scratch, you can simply use like `-h 2160` (2160 hours, 3 months) or maybe longer hours.
1. start import rahb to properties 
```
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t rahb -h 2160 init force > logs/rahbInit.log 2>&1 &
```
2. when done, kill watchAndImportToProperties 2160, kill old watchAndImportToProperties
3. start watchAndImportToProperties -t treb -t rabh etc... routine force
4. fix processStatus if necessary