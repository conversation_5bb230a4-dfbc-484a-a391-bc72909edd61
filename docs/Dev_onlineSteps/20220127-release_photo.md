BranchName: [release_photo]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [Sales_featureDescription/listingPhoto/refreshPhoto.md]


# steps: 
1. restart photo download
nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1 & 

2. restart server
3. in app find E5475684， in edit page， click download max photo numbers(50)
