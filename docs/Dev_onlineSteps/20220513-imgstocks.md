BranchName: [imgstocks]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: add imgstock.sh to compress/uncompress imgstocks/cmtys imagets

# steps:
mongodump -d tdlisting -c cmty_image -u tdlisting_u -p tdlisting_p --query '' --authenticationDatabase 'admin' --port 27018
## restore cmty_image collection

cd fileStorage

### in test server
mongorestore --drop -u d1 -p d1 --authenticationDatabase 'admin' --port 27018 -d listing2 -c cmty_image cmty_image.bson

### in production server
mongorestore -d listing -c cmty_image cmty_image.bson

checkout cmty_image has 4277 records

## uncompress cmty images
go src
./bin/imgstocks.sh
cd webroot/public/img/imgstocks/cmtys/
ls | wc -l

check webroot/public/img/imgstocks/cmtys has 9396 images


Online Date:[] ca8/ca1/shf1
- [] batch