BranchName: [es_update]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

change ddf q = 90
## add rmPsn, rmDate to prop
./start.sh lib/batchBase.coffee batch/correctData/addRmPsnField.coffee dryRun


## restart watch import
  kill watchAndImportToProperties (try not use -9, it will do graceful exit)

  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &


## add extra fields to elasticsearch, need to re run
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force > ./logs/watchEs.log 2>&1 &


## rerun mongo
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init force > ./logs/watchPropMongo.log 2>&1 &