BranchName: [idx_website]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

create db

db.createUser({user:'dAvion',pwd:'dAvion',roles:[{db:'dataAvion',role:'readWrite'},{db:'listingAvion',role:'readWrite'},{db:'rniAvion',role:'rniWrite'}]})

generate site setting, setting file is under src/webSettingDatas/*.json

add the following to config.coffee
srcPath: "<%this.srcPath%>"

./start.sh lib/batchBase.coffee batch/webSetting/addNewSite.coffee 


1. start download 
nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx 'D22jxu' '9A3p2\$6' > ./logs/idxDownload.log 2>&1 &


2. restart watch

nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb force init nogeo -h 100000> ./logs/watchImportToProp.log 2>&1 &

if only idx 
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee  -t treb force init -h 100000 > ./logs/watchImportToProp.log 2>&1 &

## added new field rltr to display in web list page, need to do migration for mongo/ES

### for mongo
#nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee routine > ./logs/watchPropMongo.log 2>&1 &

### for ES
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force> ./logs/watchEs.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee routine > ./logs/watchPropMongo.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb > logs/trebPhoto.log 2>&1 & 
3. web setting
cfg/tpls/vhost.yml,  add settings.www.setting and wwwbc
remove settings.www from vhost.yml
```

```
bc now @d3

avion server: apc2@ca7
4salebc server: apc1@ca7

base.yaml
```
wwwHost: www.\avionhome.\com
appHost: www.\avionhome.\com
shareHost: https://www.avionhome.com
wwwDomain: www.avionhome.com

tpls/config.coffee
change to 

```
app_config:
  defaultEmail: '<EMAIL>'
  defaultRTEmail: '<EMAIL>'


keys needed for new domain:
openexchangeratesKey: ********************************
Recaptcha:
 recaptchaSiteKey: 6Lf_-uUfAAAAAKQmObyVfdleXZ9Asucz-IxOkLNt
 recaptchaSecretKey: 6Lf_-uUfAAAAAA4dNKSnscNdGDrgx6bPL1AJqC7x

recapcha的key注册时需要注意选择v2的key

google: 
   geocoded: AIzaSyDQux-3YSMcIQ4TDCGVH-7DQWdZlc73C48
ses:
  defaultEmail: <EMAIL>
googleOauth:
  clientId: '890385624128-68tpkve4u48k8vgkg8o1bsh3qhfgbjfv.apps.googleusercontent.com'
  clientSecret: '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'
  redirect: '/oauth/google'

Make sure avionhome domain has setup google oauth redirect url