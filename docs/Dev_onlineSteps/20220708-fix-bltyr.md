BranchName: [fix_release_bltyr]
Author: [<PERSON>]
Batch<PERSON><PERSON>: []
DB to Restore: [N/A]
Sales Doc: [N/A]

# 要有比如有15个listing了再计算rmBltYr。太少了，结果差异太大。
# db.getCollection('sqfts_aggregate').count({bltCnt:{$gte:15}}) 5147

1. addBuiltYear
  nohup ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee force> ./logs/addBuiltYear.log 2>&1 &
  60min

2. add addBuiltYear to cron tab, run once a month
 0 1 2 * * ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee >> /cfg/logs/addBuiltYear.log 2>&1