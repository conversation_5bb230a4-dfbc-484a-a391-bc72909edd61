BranchName: [release_storey]
Author: [<PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]


1. buildPropSqft
  nohup ./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee > ./logs/buildPropSqft.log 2>&1 &
  60min

1. addBuiltYear
  nohup ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee > ./logs/addBuiltYear.log 2>&1 &
  60min

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta -t deleteEvow force > ./logs/watchImportToProp.log 2>&1 &
