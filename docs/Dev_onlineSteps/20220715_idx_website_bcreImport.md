BranchName: [idx_website]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]


avion server: apc2@ca7
4salebc server: apc1@ca7

bc import move to @ca8

1. add the following lines to $config/tpls/base.coffee file

```
module.exports.bcre_base_dir = bcre_base_dir = '<%this.bcre.basedir%>'
module.exports.bcre_image_dir = bcre_image_dir = bcre_base_dir + 'img'
module.exports.bcre_image2_dirs = bcre_image2_dirs = '<%this.bcre.imagedirs%>'
```

2. add the following lines to $config/base.yml and change locations accordingly

```
bcre:
  basedir: /opt/data/bcre/
  imagedirs: /opt/data/bcre2/img
```

3. generate website site setting

add the following line to tpls/config.coffee
```
srcPath: "<%this.srcPath%>"
```

./start.sh lib/batchBase.coffee batch/webSetting/addNewSite.coffee 


4. run coffee setup.coffee, then check built/base.coffee. It shall have correct paths there. Check the paths exist in system.

5. in src folder, run npm uninstall node-rets, npm install github:fxfred/node-rets, then check (npm ls | grep node-rets) and confirm the version is 0.2.9 or above.


6. start download 
nohup ./start.sh lib/batchBase.coffee mlsImport/bcreDownload.coffee  RETSFREDXIANG 5Qau4xAM4dUtuLn force> ./logs/bcreDownload.log 2>&1 &



6. restart watch

nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre > ./logs/watchImportToProp.log 2>&1 &

if only bcre 
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee  -t bcre force init -h 100000 > ./logs/watchImportToProp.log 2>&1 &

routine:
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee  -t bcre force  > ./logs/watchImportToProp.log 2>&1 &


nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee force routine > ./logs/watchPropMongo.log 2>&1 &

7. web setting

cfg/tpls/vhost.coffee
remove settings.www from vhost.coffee
```

```

base.yaml
```
wwwBCHost: www\.4SaleBC\.ca
wwwDomain: www.4salebc.ca

tpls/config.coffee
change to 

```
srcPath: "<%this.srcPath%>"
defaultEmail: '<EMAIL>'
defaultRTEmail: '<EMAIL>'

keys needed for new domain:
openexchangeratesKey: ********************************
recaptchaSiteKey: 6Ldr0CEfAAAAAO8r8MDRIGFFPWzlMjpO70sK_Je1
recaptchaSecretKey: 6Ldr0CEfAAAAACx22GxZfTreTns1tCYMby2q1IHK
google: 
  geocoderServer:AIzaSyCTL2DvA3sEtAWcFTNSen0gVWCceKqPFts
ses:
  defaultEmail: '<EMAIL>'
googleOauth:
  clientId: '**********************************************.apps.googleusercontent.com'
  clientSecret: '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'
  redirect: '/oauth/google'
```


{"web":{"client_id":"**********************************************.apps.googleusercontent.com","project_id":"macro-authority-345317","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_secret":"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","redirect_uris":["http://www.4salebc.ca/oauth/google"],"javascript_origins":["http://www.4salebc.ca"]}}


run generate_vhost_db to generate site collection under data db
```
  ./start.sh lib/batchBase.coffee batch/webSetting/generate_vhost_db.coffee

```