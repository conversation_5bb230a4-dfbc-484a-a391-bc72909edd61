BranchName: [release_fix_offerD]
Author: [<PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]


1. set initial school bndMt equal to school mt
  nohup ./start.sh lib/batchBase.coffee batch/correctData/20220704addInitialSchoolBndMt.coffee > ./logs/addInitialSchoolBndMt.log 2>&1 &
  
2. dry run processSchoolBndChange
process schools updated from 2022-06-20
nohup ./start.sh lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -d 2022-01-01 dryRun > ./logs/processSchoolBndChange.log 2>&1 &
expected:
school updated cnt 328 total props updated 54076
5min (build index takes time)

if from 2022-01-01
school updated cnt 1697 total props updated 222583
Trigger reimport when school bndMt change: 7.810s




3. run processSchoolBndChange 
nohup ./start.sh lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -d 2022-01-01 > ./logs/processSchoolBndChange.log 2>&1 &

school updated cnt 1697 total props updated 40173
(most property has more than one school, so the number here is less then dry run)


4. add processSchoolBndChange to crontab
# findschool
30 2 * * * /cfg/start.sh lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model >> /cfg/logs/processSchoolBndChange.log 2>&1
