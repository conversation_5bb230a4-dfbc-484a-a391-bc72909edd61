BranchName: [v6_watch]
Author: [<PERSON><PERSON><PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch:
Detail: 
  add check in watch
  add host in process status _id
## step
-1. kill Mongo/Sql Watch

0. drop unique index filePath in processStatus
  db.getCollection('processStatus').drop({})

1. restart watch
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchMongo.log 2>&1 &
  
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init > ./logs/watchSql.log 2>&1 &



   
Online Date:[(2021-10-12 15:02)]
- [X] run batch