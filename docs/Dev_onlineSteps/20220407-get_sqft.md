BranchName: [get_sqft]
Author: [<PERSON>|<PERSON><PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. buildPropSqft
   nohup ./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee init > ./logs/buildPropSqft.log 2>&1 &
   40min

   expected:
   listing:
   db.getCollection('sqfts_aggregate').count({})
   2229494
   rni:
   db.getCollection('sqft_middle').count({})
   4569022

   wait step 1 finished and do following. 3,4,5 can run together.

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

3. addRmSqftToProperties
   nohup ./start.sh lib/batchBase.coffee batch/prop/addRmSqft.coffee > ./logs/addRmSqft.log 2>&1 &
   1 hour
   expected:
   db.getCollection('properties').find({rmSqft:{$ne:null}})

4. addOfferD
   nohup ./start.sh lib/batchBase.coffee batch/prop/addOfferD.coffee > ./logs/addOfferD.log 2>&1 &
   30min

5. add isEstate or isPOS
   nohup ./start.sh lib/batchBase.coffee batch/prop/parseOwner.coffee> ./logs/parseOwner.log 2>&1 &
   30min
