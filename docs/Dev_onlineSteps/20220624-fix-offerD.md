BranchName: [release_fix_offerD]
Author: [<PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]


1. addOfferD
  nohup ./start.sh lib/batchBase.coffee batch/prop/addOfferD.coffee > ./logs/addOfferD.log 2>&1 &
  30min

2. restart watch
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta -t deleteEvow force > ./logs/watchImportToProp.log 2>&1 &
