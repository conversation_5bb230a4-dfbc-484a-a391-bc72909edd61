BranchName: [release_fixmt]
Author: [<PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]

fix mt set to new date bug, fix evow status

# steps: 
1. ./start.sh lib/batchBase.coffee batch/correctData/202201231fixPropertiesMt.coffee.
example output {
  todo: 42778,
  useOffDId: [ 'TRBW5340974' ],
  useSpcts: 2815,
  useOffD: 1,
  fixRecordsMtUsingEvow: 9126,
  fixReordsMtUsingProp: 2816,
  notFoundInProperty: [ should be empty in Prod.these props has bad prov or city
    'TRBZ4292862', 'TRBZ4292798',
    'TRBZ4292919', 'TRBZ5401123',
    'TRBZ4292906', 'TRBZ5382737',
    'TRBZ5427427', 'TRBZ4292737',
    'TRBZ4292915', 'TRBZ4292794',
    'TRBZ4292876', 'TRBZ5382466'
  ],
  noOffDForStatusU: [],
  noOffDForStatusA: [
    'TRBN5312564', 'TRBW5445932', 'TRBN5361413', 'TRBX5278640', 'TRBC5381286',
    ... 840 more items
  ],
  foundInEvow: 9126,
  notFoundInEvow: 3768,
  idxIdNotInIdxRecord: 12894,
  idxFoundInIDXRecords: 29884
}
2. toManualImportIds.txt is saved in logs. ask Tao to do manual import of these 860 records.