BranchName: [pn_fix]
Author: [<PERSON>/<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]


1. run script in mongo shell to convert Apartment to Condo

db.getCollection('user_watch_location').find({propType:'Apartment'}).forEach(function(doc){
  if (doc._id){
      var propType = doc.propType;
      var apartMIdx = propType.indexOf('Apartment');
      if (apartMIdx >= 0) {
          propType[apartMIdx] = 'Condo'
      }
      db.user_watch_location.updateOne({_id:doc._id},{$set:{propType}});
  }else{
      print(doc);
  }
})

2. restart application server

3. restart watchAndPushNotify
   nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model routine > ./logs/watchPropPushNotify.log 2>&1 &