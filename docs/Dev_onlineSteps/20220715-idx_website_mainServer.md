BranchName: [idx_website]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

# change settings

## remove settings.www

cfg/tpls/vhost.yml, remove settings.www from vhost.yml

## generate site setting

add the following to cfg/tpls/config.coffee root path
srcPath: "<%this.srcPath%>"

./start.sh lib/batchBase.coffee batch/webSetting/addNewSite.coffee 

setting file is under src/webSettingDatas/*.json, add [filename] to update specified setting.

check db(data) to confirm  
db.getCollection('site').find({_dm:['www.4salebc.ca','www.realmaster.com']})


# migration for mongo/ES
added new field rltr to display in web list page
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force> ./logs/watchEs.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchPropMongo.log 2>&1 &


# kill and restart watch (no bcre for now)
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta > ./logs/watchImportToProp.log 2>&1 &


# restart server

