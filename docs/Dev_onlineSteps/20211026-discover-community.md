Need to wait for the following data
  stat_uname collection
  boundary collection with CANADA->CA
  property tags


BranchName: [discover]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: discover feeds page, search props by tags, search cmtys, edm add cmty

## steps:

### update edm batch

#### remove property cmd for following jobs
##### change from
5 17 1-6,8-31 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property forumw 
projectrcmd assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1

5 17 7 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property forumw projectrcmd statM rmlog assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1

1 1 * * 6 /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw project forum property stat rmlog noviewed assignment exlisting weekly production >> /cfg/logs/edmWeekly.log 2>&1

##### to
5 17 1-6,8-31 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw 
projectrcmd assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1

5 17 7 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd statM rmlog assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1

1 1 * * 6 /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw project forum stat rmlog noviewed assignment exlisting weekly production >> /cfg/logs/edmWeekly.log 2>&1

#### add property cmd before subscription for following jobs
##### change from
1 18 * * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model subscription production >> /cfg/logs/edmDailySub.log 2>&1

##### to
1 18 * * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property subscription production >> /cfg/logs/edmDailySub.log 2>&1