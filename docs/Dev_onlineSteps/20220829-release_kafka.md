BranchName: [release_kafka]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]

# Kafka MirrorMaker 2.0 Setup

## Config and Start MirrorMaker

1. Create a config file `connect-mirror-maker.properties`.

this is just a example, please update connection information youself.
```apache
# Specify any number of cluster aliases
clusters = A, B

# Connection information for each cluster
# This is a comma separated host:port pairs for each cluster
# for e.g. "A_host1:9092, A_host2:9092, A_host3:9092"
A.bootstrap.servers = localhost:9093
B.bootstrap.servers = localhost:9094

A->B.enabled=true
B->A.enabled=true

topics=.*
groups=.*

tasks.max = 2
replication.factor=1

checkpoints.topic.replication.factor=1
heartbeats.topic.replication.factor=1
offset-syncs.topic.replication.factor=1

offset.storage.replication.factor=1
status.storage.replication.factor=1
config.storage.replication.factor=1

refresh.topics.enabled = true
refresh.topics.interval.seconds = 30
refresh.groups.enabled = true
refresh.groups.interval.seconds = 30
```

2. Start connect-mirror-maker.
```
$ connect-mirror-maker ./connect-mirror-maker.properties
```

## Usage Example
1. Create topic `test-topic` on cluster A.
```
$ kafka-topics --bootstrap-server localhost:9093 --create --topic test-topic
Created topic test-topic.
```

2. List topic on cluster B.
```
$ kafka-topics --bootstrap-server localhost:9094 --list
A.checkpoints.internal
A.heartbeats
A.test-topic
__consumer_offsets
heartbeats
mm2-configs.A.internal
mm2-offset-syncs.A.internal
mm2-offsets.A.internal
mm2-status.A.internal
```
You can see `A.test-topic` topic also created by MirrorMaker on cluster B, the topic name is prefixed with `A.`.

3. Start consumer on cluster B, you can use `--whitelist` with wildcard to specify topic name.
```
$ kafka-console-consumer --bootstrap-server localhost:9094 --whitelist '.*test-topic'
```
Keep this consumer running, start another terminal to run a producer at next step.

4. Producer message on cluster A.
```
$ kafka-console-producer --bootstrap-server localhost:9093 --topic test-topic
>Hello World
```

5. On the consumer that start at step 3, you will receive same message `Hello World`.

## Notice
使用 regexp 的方式 subscribe topic 時 (如前面的第3步 `--whitelist '.*test-topic'`)，Kafka 實際上是先用對應的 regexp 把所有相符的 topic 列出後逐個 subscribe，如果先以 regexp 的方式啟動 consumer 之後才建立 topic，原本就已經啟動的 consumer 並不會 subscribe 這個新的 topic，因此請先 create topic 再啟動 consumer 程序，或是在 topic 建立後重啟相關程序。

## Reference
https://docs.microsoft.com/en-us/azure/hdinsight/kafka/kafka-mirrormaker-2-0-guide
https://medium.com/larus-team/how-to-setup-mirrormaker-2-0-on-apache-kafka-multi-cluster-environment-87712d7997a4


cfg changes:
import on: ca8 kafka is mirror and no security
topic throught mirror maker will be renamed, res from ca9, req from ca8

kafka:
  topic:
    sqftFromCondosCa: 'ca9.res.sqft.condosCa'
    condosToGetSqft: 'req.sqft.condos'
  verbose: 1
  brokers: 'localhost:9092'
  clientId:'ca8-server'
  consumerGroupId:'ca8-consumer'
