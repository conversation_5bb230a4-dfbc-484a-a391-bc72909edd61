BranchName: [release_rm_pic]
Author: [<PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/removeDDFImage.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]

db.getCollection('properties').createIndex {ddfID:1}


change chrontab

from
```
10 3 */4 * * /cfg/start.sh batch/removeDDFImage.coffee 730 /mnt/raid0/mlsimgs/crea/ddf/img >> /cfg/logs/removeDDFImage.log 2>&1
10 4 */4 * * /cfg/start.sh batch/removeTREBImage.coffee 730 /mnt/raid0/mlsimgs/treb/mls >> /cfg/logs/removeDDFImage.log 2>&1
```

to 

```

10 3 */4 * * /cfg/start.sh lib/batchBase.coffee batch/removeDDFImage.coffee  730 /mnt/raid0/mlsimgs/crea/ddf/img >> /cfg/logs/removeDDFImage.log 2>&1
10 4 */4 * * /cfg/start.sh lib/batchBase.coffee batch/removeDDFImage.coffee  730 /mnt/raid0/mlsimgs/treb/mls >> /cfg/logs/removeDDFImage.log 2>&1


```