BranchName: [addProcessStatus]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

# steps:

- update cron jobs
``` shell
# original
1 7 * * * /cfg/start.sh lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' >> /cfg/logs/formdump.log 2>&1
30 1 * * * /cfg/start.sh lib/batchBase.coffee batch/findschoolv3.coffee >> /cfg/logs/findschool.log 2>&1
4 17 * * * /cfg/start.sh batch/topListingNotify.coffee production >> /cfg/logs/edmTopListingDaily.log 2>&1

#to 
1 7 * * * /cfg/start.sh lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' --frequency daily -s 1 >> /cfg/logs/formdump.log 2>&1
30 1 * * * /cfg/start.sh lib/batchBase.coffee batch/findschool/findschoolv3.coffee -f daily -s 1 >> /cfg/logs/findschool.log 2>&1
4 17 * * * /cfg/start.sh lib/batchBase.coffee batch/topListingNotify.coffee production -f daily -s 1 >> /cfg/logs/edmTopListingDaily.log 2>&1

```
change dtaDownload add frenquency in chrontab (no refer for current command)
```
10,40 0-23 * * * /cfg/start.sh lib/batchBase.coffee mlsImport/dtaDownload.coffee -f hourly -s 1 >> /cfg/logs/dta.log 2>&1
```

kill watchPropAndUpdateMongo
kill watchPropAndUpdatePostSql in each server x3
kill watchAndImportToProperties
kill photoDownload.coffee crea
kill photoDownload.coffee treb
kill trebDownload

2. run batch:

./start.sh lib/batchBase.coffee batch/correctData/20211224_fix_watch_in_sysData.coffee
npm i node-rets

3. restart watch
  
  
   ```
   screen -r
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init force > ./logs/watchPropMongo.log 2>&1 &

  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta init force -h 0.2 > ./logs/watchImportToProp.log 2>&1 &

   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1 & 

   nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee rKfpLGwCCyfcWM1KzTdSQ6Y8 VenzibWOzDXnAm70CCJEpeVW > logs/creaDownload.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow EV20ywa 'C8p6\$39' > logs/trebEVOW.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx D20ywa 'H9p3\$63' > logs/trebIDX.log 2>&1 &

   [Not runned]nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee vow V14fxd 'Rd\$8439' skipDiff > logs/trebVOW.log 2>&1 &

   Start sql init in each server.
   nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init force > ./logs/watchPropSql.log 2>&1 & 

   ```


- start checkServerStatus

example

```shell
# cd src
./batch/checkServerStatus.sh -a http://app.test:8082 -w http://d7ww.realmaster.com:8082 -e <EMAIL>


# on e1
59 * * * * /home/<USER>/realmaster-appweb/src/batch/checkServerStatus.sh -a https://realmaster.com -w https://www.realmaster.com -e <EMAIL>,<EMAIL>,<EMAIL>
# on shf1
01 * * * * /home/<USER>/realmaster-appweb/src/batch/checkServerStatus.sh -a https://ch.realmaster.cn -w https://www.realmaster.cn -e <EMAIL>,<EMAIL>
```

Online Date:[(2022-01-14 12:33)]
- [X] update cronjob
- [X] run batch
- [X] restart server
- [X] restart watch