BranchName: [fix_pushnotify]
Author: [liurui]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]


1. fix watched location/building name
  db.getCollection('user_watch_location').find({tp:{$ne:'cmty'}}).forEach(function(doc){
      if (doc.nm){
          id = doc.nm.split(',');
          id[1] = ' '+id[1];
          id = id.join(',');
          print(id);
          db.user_watch_location.updateOne({_id:doc._id},{$set:{nm:id}});
      }else{
          print(doc);
      }
  })

2. watched building add loc when range is 0
   ./start.sh lib/batchBase.coffee batch/correctData/20220810fixWatchBuildingLoc.coffee
