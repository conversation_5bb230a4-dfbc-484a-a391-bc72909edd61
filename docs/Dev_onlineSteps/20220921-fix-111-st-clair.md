BranchName: [N/A]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

# Problems about 111 St Clair
1. Some properties have different lat/lng
2. Some properties have wrong uaddr

# Fix step
1. Run mongodb command to correct wrong address, those properties by given uaddr query condition were confirmed should be update.

```javascript
db.properties.updateMany({ uaddr: { $in: [
  "CA:ON:TORONTO:111 ST CLAIR AVE W AVE",
  "CA:ON:TORONTO:111 ST CLAIR AVE W RD",
  "CA:ON:TORONTO:111 ST CLAIR ST",
  "CA:ON:TORONTO:111 ST CLAIR ST W",
  "CA:ON:TORONTO:111 ST CLAIR W AVE",
  "CA:ON:TORONTO:111 ST CLAIR W AVE W",
  "CA:ON:TORONTO:111 ST CLAIR WEST AVE W",
  "CA:ON:TORONTO:111 ST CLAIRE AVE W"
]}}, {
  $set: {
    uaddr: "CA:ON:TORONTO:111 ST CLAIR AVE W",
    st : "St. Clair Ave W",
    st_dir : "W",
    st_num : "111",
    st_sfx : "Ave",
    addr : "111 St. Clair Ave W",
    showAddr : "111 St. Clair Ave W",
    faddr : "St Clair Ave W, Toronto, ON M4V 1N5, Canada"
  }
});
```

2. Run refineGeocoding to make all properties with same uaddr have same lat/lng, please notice "updateprop" parameter is necessery to update existing properties.
```
./start.sh lib/batchBase.coffee batch/prop/refineGeocoding.coffee updateprop
```
