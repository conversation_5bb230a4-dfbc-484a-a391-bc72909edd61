BranchName: [pushnotify]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. Migrate old fav cmty to user watching collection
   ./start.sh lib/batchBase.coffee  batch/correctData/20220413_migrate_favCmty2userWatch.coffee dry
   
2. update edm cmd
   From
   1 18 * * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property subscription production >> /cfg/logs/edmDailySub.log 2>&1
   
   
   To
   1 18 * * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property production >> /cfg/logs/edmDailySub.log 2>&1

3. restart watchAndPushNotify
   nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model routine > ./logs/watchPropPushNotify.log 2>&1 &