BranchName: [release_watch]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Description: watch token update in interval. refactor watch

steps:
1. kill all watches
kill watchPropAndUpdateMongo
kill watchPropAndUpdatePostSql in each server
kill watchAndImportToProperties
kill photoDownload.coffee crea
kill photoDownload.coffee treb

2. run batch:

./start.sh lib/batchBase.coffee batch/correctData/20211224_fix_watch_in_sysData.coffee
npm i node-rets

3. restart watch:
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init force> ./logs/watchPropMongo.log 2>&1 &

Start sql init in different server.
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init force> ./logs/watchPropSql.log 2>&1 & 

nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force> ./logs/watchImportToProp.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1 & 

Online Date:[]
- [] restart server
- [] restart watch