BranchName: [get_sqft]
Author: [<PERSON>]
BatchName: [./start.sh lib/batchBase.coffee batch/prop/buildPropSqft.coffee]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. restart watch with updating outside mongodb
   kill watchAndImportToProperties (try not use -9, it will do graceful exit)

   nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta -d '**************************************************************' force > ./logs/watchImportToProp.log 2>&1 &
