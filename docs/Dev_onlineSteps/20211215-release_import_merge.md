BranchName: [release_import_merge]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch: ./start.sh lib/batchBase.coffee batch/correctData/unsetMerged.coffee
Detail: 
  do not merge props from same source
  query mongo properties when query by id

## step
  1. ./start.sh lib/batchBase.coffee batch/correctData/unsetMerged.coffee 
    1 min
    example result: 130 treb, 13 bre
    
  2. kill watchAndImportToProperties (try not use -9, it will do graceful exit)
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcre -t trebMan -t dta  > ./logs/watchImportToProp.log 2>&1 &

Online Date:[]
- [X] run batch
- [X] restart watch
- [X] restart server