BranchName: [release_fix_sqft]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]

1. restart watch
  kill watchAndImportToProperties (try not use -9, it will do graceful exit)
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &

2. restart import download
  nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee [user] [pass] > logs/creaDownload.log 2>&1 &
  nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow  [user] [pass] > logs/trebEVOW.log 2>&1 &
  nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx  [user] [pass] > logs/trebIDX.log 2>&1 &