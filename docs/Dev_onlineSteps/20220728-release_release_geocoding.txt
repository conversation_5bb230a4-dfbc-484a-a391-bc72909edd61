BranchName: [release_geocoding]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]

fix bug: two address with same uaddr but different lat,lng
root cause:
if update from auto import, it will use the rlat, rlng. q = 105 (should use q=120 here)
if update from manual import. it will use lat,lng from source. should use rlat and rlng. q = 120.



1. restart watch
  kill watchAndImportToProperties (try not use -9, it will do graceful exit)
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &
