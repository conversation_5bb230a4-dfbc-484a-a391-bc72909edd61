BranchName: [release_import]
Author: [<PERSON><PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]

 
1. rename collection in rni db:
use rni
mls_bcre_records -> mls_bcre_manual_records
db.getCollection('mls_bcre_records').renameCollection('mls_bcre_manual_records')

mls_bcre_raws -> mls_bcre_manual_raws
db.getCollection('mls_bcre_raws').renameCollection('mls_bcre_manual_raws')

mls_bcre_records_log -> mls_bcre_manual_records_log
db.getCollection('mls_bcre_records_log').renameCollection('mls_bcre_manual_records_log')

2. drop mls_bcre_records and mls_bcre_raws in listing db
use listing
db.getCollection('mls_bcre_records').drop()
db.getCollection('mls_bcre_raws').drop()
db.getCollection('mls_bcre_records_log').drop()

3. restart app server
4. restart watch
  kill watchAndImportToProperties (try not use -9, it will do graceful exit)
  
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force > ./logs/watchImportToProp.log 2>&1 &
