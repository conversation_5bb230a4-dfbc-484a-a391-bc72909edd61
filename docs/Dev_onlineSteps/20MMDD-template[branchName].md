### this file is for online steps template

# case 1, fix/feat that need to update/restore db
copy the follow to file:

=========Start of copy template1=======

BranchName: [branch]
Author: [name]
BatchName: [batch/i18n/i18n.coffee]/[N/A]
BatchDetail: See inside batch file
DB to Restore: [fileStorage/XXX.bson]/[N/A]
Sales Doc: [N/A]\[Sales_featureDescription/user/XXX.md]

# steps:

- Ensure batch pre requisite is correct
- `./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m import -t kr -f {{abs path}}-kr-zh-translations.txt > logs/{{batchName}}.log 2>&1 &`
  *Error Handling: should error happen what to do?*
- `./start.sh [more batchs to run]`
  *More Error Handling: should error happen what to do?*
- Ensure batch runned correctly, expected out put should be


# Check this after onlined(@admin)
Online Date:[YYYYMMDD hh:mm]
- [X] (batch/i18n/i18n.coffee) runned
- [ ] (batch/i18n/i18nClean.coffee) runned

=========End of copy=========

# case 2,fix/feature need to update config

=========Start of copy template2=======

BranchName: [branch]
Author: [name]
Related Feature: [translate]
Sales Doc: [N/A]\[Sales_featureDescription/user/XXX.md]

# steps:

- Updates config(remove if not need)

##base.yml
+deeplKey: 'c8311202-a251-97d9-b15d-3795ac88e820'

##config.coffee
app_config:
  +deeplKey: '<%this.deeplKey%>'

- install npm dependency(remove if not need)
  brew install lfs
  git lfs install

# Check this after onlined(@admin)
Online Date:[YYYYMMDD hh:mm]
- [ ] Update cfg, coffee setup.coffee
- [ ] restart server

=========End of copy=========


# case 3, fix/feature related to import

=========Start of copy template3=======

BranchName: [branch]
Author: [name]
BatchName: [batch/i18n/i18n.coffee]/[N/A]
BatchDetail: See inside batch file
DB to Restore: [fileStorage/XXX.bson]/[N/A]
Sales Doc: [N/A]\[Sales_featureDescription/user/XXX.md]

# steps:

- Ensure batch pre requisite is correct
- Stop corntab job
- screen -r
- `nohup ./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m import -t kr -f {{abs path}}-kr-zh-translations.txt > logs/{{batchName}}.log 2>&1 &`
	*Error Handling: should error happen what to do?*
- `nohup ./start.sh [more batchs to run]`
  *More Error Handling: should error happen what to do?*
- Ensure batch runned correctly, expected out put should be
- add job_daily.sh in crontab.
- ctrl+a+d

# Check this after onlined(@admin)
Online Date:[YYYYMMDD hh:mm]
- [ ] Update db
- [ ] Updated corntab

=========End of copy=========
