BranchName: [release_import]
Author: [<PERSON>]
DB to Restore: [N/A]
Sales Doc: [N/A]
Batch: 
Detail: 
  fix status
  hide sp from his

## step
  1. remove fields in rni db, only in treb. modified 1769246, take 350sec in test server.
    db.getCollection('mls_treb_master_records').updateMany({},{$unset:{phodl:1,phomt:1}})
    
  2. kill watchAndImportToProperties (try not use -9, it will do graceful exit)
   
  3. restart watch
  nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcre -t trebMan -t dta  > ./logs/watchImportToProp.log 2>&1 &

  4. fix error data
    NOTE: evow does not has availables. it will not auto set status U
    fix status in rni, will auto trigger watch again. (5758)

    db.getCollection('mls_treb_master_records').count({Status:'U',status:'A',_mt:{$gt:new Date('2021-12-15')}}) (1522)
    
    ```
    db.getCollection('mls_treb_master_records').createIndex({_mt:-1})

    db.getCollection('mls_treb_master_records').updateMany({Status:'U',status:'A',_mt:{$gt:new Date('2021-12-15')}},{$set:{status:'U'}})

    ```
  
Online Date:[]
- [] run db command.
- [] restart watch
- [] restart server