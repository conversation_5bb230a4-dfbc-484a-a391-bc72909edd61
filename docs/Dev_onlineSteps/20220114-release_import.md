BranchName: [release_import]
Author: [<PERSON>]
BatchName: [N/A]
DB to Restore: [N/A]
Sales Doc: [N/A]

# steps:

add indexes to rni tables

- kill and restart download 
  
   ```
   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea >> logs/creaPhoto.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb >> logs/trebPhoto.log 2>&1 & 
   nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee 
   rKfpLGwCCyfcWM1KzTdSQ6Y8 VenzibWOzDXnAm70CCJEpeVW > logs/creaDownload.log 2>&1 &

   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee evow EV20ywa 'C8p6\$39' > logs/trebEVOW.log 2>&1 &
   nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownload.coffee idx D20ywa 'H9p3\$63' > logs/trebIDX.log 2>&1 &

   
   ```
