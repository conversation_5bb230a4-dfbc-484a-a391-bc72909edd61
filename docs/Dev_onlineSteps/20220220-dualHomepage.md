BranchName: [dual_homepage]
Author: [Sam]
Sales Doc: [N/A]

# steps:

## change share.secret in cfg
NOTE: this is test key, use prod key
RMT547dc488a8d7d5828d34437872064a9bt1 -> RMT547dc488a8d7d5828d34437872064
37 byte -> 32 byte

## npm
npm i validator/stripe/uuid
# add cfg to server
app_config:
  dualHomepage: true
  followUpBoss:
    token:'fka_02gWVHLlA1VvNq1AR2X82ZuhXeFjtWLmjB'
    xSystem: 'realmasterWeb'
    xSystemKey: 'bade25031e36fc00f93db96e9fbc404c'
## restart server


# register webhook event api by post to /webhooks manully

go to 
https://docs.followupboss.com/reference/webhooks-1
params:
event: peopleUpdated
url: https://realmaster.com/1.5/fub/peopleUpdated
xSystemKey: 'bade25031e36fc00f93db96e9fbc404c'


# sync agent account
IN APP:
admin goto more -> manage people fub info
sync from fub
add _crmNoAssign for these account:

<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
