# Online Step - Census 2021

Branch Name: [census-2021]  
Author: [<PERSON>]  
DB to Restore: [census2021]  

## Files location
All files already on the appd8@ca5, `/home/<USER>/census2021`, `census2021.bson.gz` and `census2021.metadata.json.gz` are MongoDB dump, and `census2021.tar.gz` is image files, we will use it later.

Those files also store in repo `rmlfs`, `rmlfs/fileStorage/census2016_bbox.bson`, `rmlfs/fileStorage/census2016_bbox.metadata.json` and `rmlfs/imgstocks/census2021.tar.gz`

## Restore MongoDB Census 2021 collection
```
mongorestore --username d1 --password d1 --authenticationDatabase admin --host 127.0.0.1 --port 27018 --gzip --db listing /home/<USER>/census2021/census2021.bson.gz

mongorestore --username d1 --password d1 --authenticationDatabase admin --host 127.0.0.1 --port 27018 --gzip --db listing /home/<USER>/census2021/census2021Keys.bson.gz

mongorestore --username d1 --password d1 --authenticationDatabase admin --host 127.0.0.1 --port 27018 --gzip --db listing /home/<USER>/census2021/census2021Notes.bson.gz
```

## Extract images
Census map images should be store at `realmaster-appweb/src/webroot/public/img/imgstocks/census`, extract `census2021.tar.gz` and store all files in that path. If directory is not exist, create it first.
```
tar -xf /rmlfs/imgstocks/census2021.tar.gz -C /realmaster-appweb/src/webroot/public/img/imgstocks/census
tar -xf /rmlfs/imgstocks/cmtys.tar.gz -C /realmaster-appweb/src
```

## Add cs21 tag for properties
```
nohup ./start.sh lib/batchBase.coffee batch/tag/addTagToProperty.coffee init force census2021 > ./logs/addTagToProperty_census2021.log 2>&1 &
```


restart batch `watchAndImportToProperties.coffee` and server, then it's all finish.
