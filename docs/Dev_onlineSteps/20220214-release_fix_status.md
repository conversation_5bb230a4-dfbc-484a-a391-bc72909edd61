BranchName: [release_fix_status]
Author: [<PERSON>]
BatchName: []
DB to Restore: [N/A]
Sales Doc: [N/A]

fix mt set to new date bug, fix evow status

# steps: 
1. ./start.sh lib/batchBase.coffee batch/correctData/20220212findIdsOfWrongStatus.coffee > ./logs/fixWrongStatus.log 2>&1 &
example output {
  todo: 2543
}
2. idWithWrongStatus.txt is saved in logs
3. restart watch
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta routine force> ./logs/watchImportToProp.log 2>&1 &
4. manual import ids from: @fred 
  1. idWithWrongStatus.txt(these are Status = A, status = U)
  2. toManualImportIds.txt (if not done. this records are not found in evow, but status in properties are still A)

  expect:
  1. no change to properties mt
  2. fix status using trebMan
  2. if status in trebMan is U, set spcts = offD


online date: (2022-02-22 13:42) @rain

 