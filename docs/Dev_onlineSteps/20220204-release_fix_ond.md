BranchName: [release_fix_ond]
Author: [<PERSON>]
Batch<PERSON><PERSON>: [20220204fixPropertiesOnD]
DB to Restore: [N/A]
Sales Doc: [N/A]

fix onD and offD for those records with onD < offD

# steps: 

1. fix onD offD, around 5 min

nohup ./start.sh lib/batchBase.coffee batch/correctData/20220204fixPropertiesOnD.coffee > ./logs/20220204fixPropertiesOnD.log 2>&1 & 


2. restart watch
kill watchAndImportToProperties
 
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t treb -t ddf -t bcreMan -t trebMan -t dta force> ./logs/watchImportToProp.log 2>&1 &

 {
  todo: 109064,
  wrongTs: 3754,
  fixTsUsingOnD: 3754,
  fixTsUsingOffD: 0,
  wrongMt: 107,
  fixMtUsingOffD: 2,
  fixMtUsingOnD: 105,
  noFix: 0,
  fixOnDUsingTs: 101845,
  fixOffDUsingSldd: 0,
  fixOffDUsingUnavailDt: 101,
  fixOffDUsingOnd: 2395,
  fixOffDUsingMt: 51462,
  fixoffD: 53958,
  fixts: 3754,
  fixmt: 107,
  fixonD: 101845
}