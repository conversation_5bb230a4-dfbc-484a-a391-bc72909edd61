BranchName: [update_adv_filter]
Author: [Sam]
DB to Restore: [N/A]
Sales Doc: [N/A]

Add new fields to advance filter for prop searching

# add new field rmPsn for search
./start.sh lib/batchBase.coffee batch/correctData/addRmPsnField.coffee dryRun

# rerun elasticsearch watch to build new index
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force> ./logs/watchEs.log 2>&1 &

# rerun mongo 
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee init > ./logs/watchPropMongo.log 2>&1 &