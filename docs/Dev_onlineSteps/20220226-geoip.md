BranchName: [N/A]
Author: [<PERSON>]
Related Feature: [geoip]
Sales Doc: [N/A]

# steps:

- edit start.sh

  - dont remove ". $HOME/.nvm/nvm.sh" line.

- install npm dependency

```
  npm install geolite2-redist maxmind
```
check if node version > 16.14.2
./start.sh lib/batchBase.coffee batch/user/checkIpCity.coffee

# Usage:

- req.getIpCity() or req.getIpCity(ip)
  - may return null

# Check this after onlined(@admin)

Online Date:[YYYYMMDD hh:mm]

- [ ] edit start.sh
- [ ] installed npm module
