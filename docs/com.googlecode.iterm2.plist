<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AdjustWindowForFontSizeChange</key>
	<true/>
	<key>AllowClipboardAccess</key>
	<false/>
	<key>AlternateMouseScroll</key>
	<true/>
	<key>AnimateDimming</key>
	<false/>
	<key>AppleAntiAliasingThreshold</key>
	<integer>1</integer>
	<key>ApplePressAndHoldEnabled</key>
	<false/>
	<key>AppleScrollAnimationEnabled</key>
	<integer>0</integer>
	<key>AppleSmoothFixedFontsSizeThreshold</key>
	<integer>1</integer>
	<key>AppleWindowTabbingMode</key>
	<string>manual</string>
	<key>AutoHideTmuxClientSession</key>
	<false/>
	<key>CheckTestRelease</key>
	<false/>
	<key>ClosingHotkeySwitchesSpaces</key>
	<true/>
	<key>CommandSelection</key>
	<true/>
	<key>Control</key>
	<integer>1</integer>
	<key>CopyLastNewline</key>
	<false/>
	<key>CopySelection</key>
	<true/>
	<key>Custom Color Presets</key>
	<dict>
		<key>Solarized Dark</key>
		<dict>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.14145714044570923</real>
				<key>Green Component</key>
				<real>0.10840655118227005</real>
				<key>Red Component</key>
				<real>0.81926977634429932</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.38298487663269043</real>
				<key>Green Component</key>
				<real>0.35665956139564514</real>
				<key>Red Component</key>
				<real>0.27671992778778076</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43850564956665039</real>
				<key>Green Component</key>
				<real>0.40717673301696777</real>
				<key>Red Component</key>
				<real>0.32436618208885193</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.72908437252044678</real>
				<key>Green Component</key>
				<real>0.33896297216415405</real>
				<key>Red Component</key>
				<real>0.34798634052276611</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.86405980587005615</real>
				<key>Green Component</key>
				<real>0.95794391632080078</real>
				<key>Red Component</key>
				<real>0.98943418264389038</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.020208755508065224</real>
				<key>Green Component</key>
				<real>0.54115492105484009</real>
				<key>Red Component</key>
				<real>0.44977453351020813</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.023484811186790466</real>
				<key>Green Component</key>
				<real>0.46751424670219421</real>
				<key>Red Component</key>
				<real>0.64746475219726562</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.78231418132781982</real>
				<key>Green Component</key>
				<real>0.46265947818756104</real>
				<key>Red Component</key>
				<real>0.12754884362220764</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43516635894775391</real>
				<key>Green Component</key>
				<real>0.10802463442087173</real>
				<key>Red Component</key>
				<real>0.77738940715789795</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.52502274513244629</real>
				<key>Green Component</key>
				<real>0.57082360982894897</real>
				<key>Red Component</key>
				<real>0.14679534733295441</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.79781103134155273</real>
				<key>Green Component</key>
				<real>0.89001238346099854</real>
				<key>Red Component</key>
				<real>0.91611063480377197</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.15170273184776306</real>
				<key>Green Component</key>
				<real>0.11783610284328461</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.073530435562133789</real>
				<key>Green Component</key>
				<real>0.21325300633907318</real>
				<key>Red Component</key>
				<real>0.74176257848739624</real>
			</dict>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.15170273184776306</real>
				<key>Green Component</key>
				<real>0.11783610284328461</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
		</dict>
		<key>Solarized Light</key>
		<dict>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.14145712554454803</real>
				<key>Green Component</key>
				<real>0.10840645432472229</real>
				<key>Red Component</key>
				<real>0.81926983594894409</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.38298487663269043</real>
				<key>Green Component</key>
				<real>0.35665956139564514</real>
				<key>Red Component</key>
				<real>0.27671992778778076</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43850564956665039</real>
				<key>Green Component</key>
				<real>0.40717673301696777</real>
				<key>Red Component</key>
				<real>0.32436618208885193</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.72908437252044678</real>
				<key>Green Component</key>
				<real>0.33896297216415405</real>
				<key>Red Component</key>
				<real>0.34798634052276611</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.86405980587005615</real>
				<key>Green Component</key>
				<real>0.95794391632080078</real>
				<key>Red Component</key>
				<real>0.98943418264389038</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.020208755508065224</real>
				<key>Green Component</key>
				<real>0.54115492105484009</real>
				<key>Red Component</key>
				<real>0.44977453351020813</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.023484811186790466</real>
				<key>Green Component</key>
				<real>0.46751424670219421</real>
				<key>Red Component</key>
				<real>0.64746475219726562</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.78231418132781982</real>
				<key>Green Component</key>
				<real>0.46265947818756104</real>
				<key>Red Component</key>
				<real>0.12754884362220764</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43516635894775391</real>
				<key>Green Component</key>
				<real>0.10802463442087173</real>
				<key>Red Component</key>
				<real>0.77738940715789795</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.52502274513244629</real>
				<key>Green Component</key>
				<real>0.57082360982894897</real>
				<key>Red Component</key>
				<real>0.14679534733295441</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.79781103134155273</real>
				<key>Green Component</key>
				<real>0.89001238346099854</real>
				<key>Red Component</key>
				<real>0.91611063480377197</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.15170273184776306</real>
				<key>Green Component</key>
				<real>0.11783610284328461</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.073530435562133789</real>
				<key>Green Component</key>
				<real>0.21325300633907318</real>
				<key>Red Component</key>
				<real>0.74176257848739624</real>
			</dict>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.86405980587005615</real>
				<key>Green Component</key>
				<real>0.95794391632080078</real>
				<key>Red Component</key>
				<real>0.98943418264389038</real>
			</dict>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.38298487663269043</real>
				<key>Green Component</key>
				<real>0.35665956139564514</real>
				<key>Red Component</key>
				<real>0.27671992778778076</real>
			</dict>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43850564956665039</real>
				<key>Green Component</key>
				<real>0.40717673301696777</real>
				<key>Red Component</key>
				<real>0.32436618208885193</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.79781103134155273</real>
				<key>Green Component</key>
				<real>0.89001238346099854</real>
				<key>Red Component</key>
				<real>0.91611063480377197</real>
			</dict>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43850564956665039</real>
				<key>Green Component</key>
				<real>0.40717673301696777</real>
				<key>Red Component</key>
				<real>0.32436618208885193</real>
			</dict>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.38298487663269043</real>
				<key>Green Component</key>
				<real>0.35665956139564514</real>
				<key>Red Component</key>
				<real>0.27671992778778076</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.79781103134155273</real>
				<key>Green Component</key>
				<real>0.89001238346099854</real>
				<key>Red Component</key>
				<real>0.91611063480377197</real>
			</dict>
		</dict>
	</dict>
	<key>Default Bookmark Guid</key>
	<string>C4156A23-8730-43F2-AD25-9DFDD8556C6E</string>
	<key>DimBackgroundWindows</key>
	<false/>
	<key>DimInactiveSplitPanes</key>
	<true/>
	<key>DimOnlyText</key>
	<false/>
	<key>DisableFullscreenTransparency</key>
	<false/>
	<key>EnableRendezvous</key>
	<false/>
	<key>FocusFollowsMouse</key>
	<false/>
	<key>FsTabDelay</key>
	<real>1</real>
	<key>HapticFeedbackForEsc</key>
	<false/>
	<key>HiddenAFRStrokeThickness</key>
	<real>0.0</real>
	<key>HiddenAdvancedFontRendering</key>
	<false/>
	<key>HideActivityIndicator</key>
	<false/>
	<key>HideMenuBarInFullscreen</key>
	<true/>
	<key>HideScrollbar</key>
	<false/>
	<key>HideTab</key>
	<true/>
	<key>HighlightTabLabels</key>
	<true/>
	<key>HotKeyBookmark</key>
	<string>D3D9908E-C06A-4B91-BC20-AED646EC7625</string>
	<key>HotKeyTogglesWindow</key>
	<false/>
	<key>Hotkey</key>
	<true/>
	<key>HotkeyChar</key>
	<integer>105</integer>
	<key>HotkeyCode</key>
	<integer>34</integer>
	<key>HotkeyMigratedFromSingleToMulti</key>
	<true/>
	<key>HotkeyModifiers</key>
	<integer>1638696</integer>
	<key>IRMemory</key>
	<integer>4</integer>
	<key>JobName</key>
	<true/>
	<key>LeftCommand</key>
	<integer>7</integer>
	<key>LeftOption</key>
	<integer>2</integer>
	<key>LoadPrefsFromCustomFolder</key>
	<true/>
	<key>MaxVertically</key>
	<false/>
	<key>NSNavLastRootDirectory</key>
	<string>~/Documents</string>
	<key>NSNavPanelExpandedSizeForOpenMode</key>
	<string>{799, 448}</string>
	<key>NSNavPanelExpandedSizeForSaveMode</key>
	<string>{712, 565}</string>
	<key>NSQuotedKeystrokeBinding</key>
	<string></string>
	<key>NSRepeatCountBinding</key>
	<string></string>
	<key>NSScrollAnimationEnabled</key>
	<false/>
	<key>NSScrollViewShouldScrollUnderTitlebar</key>
	<false/>
	<key>NSSplitView Subview Frames NSColorPanelSplitView</key>
	<array>
		<string>0.000000, 0.000000, 224.000000, 258.000000, NO, NO</string>
		<string>0.000000, 259.000000, 224.000000, 48.000000, NO, NO</string>
	</array>
	<key>NSTableView Columns v2 KeyBingingTable</key>
	<data>
	YnBsaXN0MDDUAQIDBAUGBwpYJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9iamVjdHMS
	AAGGoF8QD05TS2V5ZWRBcmNoaXZlctEICVVBcnJheYABrgsMEx4fICEiIyQqNDU2VSRu
	dWxs0g0ODxJaTlMub2JqZWN0c1YkY2xhc3OiEBGAAoAKgA3TFA0OFRkdV05TLmtleXOj
	FhcYgAOABIAFoxobHIAGgAeACIAJWklkZW50aWZpZXJVV2lkdGhWSGlkZGVuUTAjQGjA
	AAAAAAAI0iUmJyhaJGNsYXNzbmFtZVgkY2xhc3Nlc1xOU0RpY3Rpb25hcnmiJylYTlNP
	YmplY3TTFA0OKy8doxYXGIADgASABaMwMRyAC4AMgAiACVExI0BzsAAAAAAA0iUmNzhe
	TlNNdXRhYmxlQXJyYXmjNzkpV05TQXJyYXkACAARABoAJAApADIANwBJAEwAUgBUAGMA
	aQBuAHkAgACDAIUAhwCJAJAAmACcAJ4AoACiAKYAqACqAKwArgC5AL8AxgDIANEA0gDX
	AOIA6wD4APsBBAELAQ8BEQETARUBGQEbAR0BHwEhASMBLAExAUABRAAAAAAAAAIBAAAA
	AAAAADoAAAAAAAAAAAAAAAAAAAFM
	</data>
	<key>NSTableView Sort Ordering v2 KeyBingingTable</key>
	<data>
	YnBsaXN0MDDUAQIDBAUGBwpYJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9iamVjdHMS
	AAGGoF8QD05TS2V5ZWRBcmNoaXZlctEICVVBcnJheYABowsMEVUkbnVsbNINDg8QWk5T
	Lm9iamVjdHNWJGNsYXNzoIAC0hITFBVaJGNsYXNzbmFtZVgkY2xhc3Nlc15OU011dGFi
	bGVBcnJheaMUFhdXTlNBcnJheVhOU09iamVjdAgRGiQpMjdJTFJUWF5jbnV2eH2IkaCk
	rAAAAAAAAAEBAAAAAAAAABgAAAAAAAAAAAAAAAAAAAC1
	</data>
	<key>NSTableView Supports v2 KeyBingingTable</key>
	<true/>
	<key>NSToolbar Configuration com.apple.NSColorPanel</key>
	<dict>
		<key>TB Is Shown</key>
		<integer>1</integer>
	</dict>
	<key>NSWindow Frame NSColorPanel</key>
	<string>30 83 224 275 0 0 1440 877 </string>
	<key>NSWindow Frame NSNavPanelAutosaveName</key>
	<string>2000 276 799 448 0 0 1440 877 </string>
	<key>NSWindow Frame Preferences</key>
	<string>85 419 606 456 0 0 1440 877 </string>
	<key>NSWindow Frame SUAutomaticUpdateAlert</key>
	<string>428 527 616 174 0 0 1440 877 </string>
	<key>NSWindow Frame SessionsPreferences</key>
	<string>269 126 606 469 0 0 1440 877 </string>
	<key>NSWindow Frame iTerm Window 0</key>
	<string>1764 -132 1544 790 1440 -180 1920 1057 </string>
	<key>NSWindow Frame iTerm Window 1</key>
	<string>183 34 585 473 0 0 1440 877 </string>
	<key>NSWindow Frame iTerm Window 2</key>
	<string>225 22 570 451 0 0 1440 877 </string>
	<key>New Bookmarks</key>
	<array>
		<dict>
			<key>ASCII Anti Aliased</key>
			<true/>
			<key>Allow Title Reporting</key>
			<false/>
			<key>Ambiguous Double Width</key>
			<false/>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.14145714044570923</real>
				<key>Green Component</key>
				<real>0.10840655118227005</real>
				<key>Red Component</key>
				<real>0.81926977634429932</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.38298487663269043</real>
				<key>Green Component</key>
				<real>0.35665956139564514</real>
				<key>Red Component</key>
				<real>0.27671992778778076</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43850564956665039</real>
				<key>Green Component</key>
				<real>0.40717673301696777</real>
				<key>Red Component</key>
				<real>0.32436618208885193</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.72908437252044678</real>
				<key>Green Component</key>
				<real>0.33896297216415405</real>
				<key>Red Component</key>
				<real>0.34798634052276611</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.86405980587005615</real>
				<key>Green Component</key>
				<real>0.95794391632080078</real>
				<key>Red Component</key>
				<real>0.98943418264389038</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.020208755508065224</real>
				<key>Green Component</key>
				<real>0.54115492105484009</real>
				<key>Red Component</key>
				<real>0.44977453351020813</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.023484811186790466</real>
				<key>Green Component</key>
				<real>0.46751424670219421</real>
				<key>Red Component</key>
				<real>0.64746475219726562</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.78231418132781982</real>
				<key>Green Component</key>
				<real>0.46265947818756104</real>
				<key>Red Component</key>
				<real>0.12754884362220764</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.43516635894775391</real>
				<key>Green Component</key>
				<real>0.10802463442087173</real>
				<key>Red Component</key>
				<real>0.77738940715789795</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.52502274513244629</real>
				<key>Green Component</key>
				<real>0.57082360982894897</real>
				<key>Red Component</key>
				<real>0.14679534733295441</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.79781103134155273</real>
				<key>Green Component</key>
				<real>0.89001238346099854</real>
				<key>Red Component</key>
				<real>0.91611063480377197</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.15170273184776306</real>
				<key>Green Component</key>
				<real>0.11783610284328461</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.073530435562133789</real>
				<key>Green Component</key>
				<real>0.21325300633907318</real>
				<key>Red Component</key>
				<real>0.74176257848739624</real>
			</dict>
			<key>Automatically Log</key>
			<false/>
			<key>BM Growl</key>
			<true/>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.15170273184776306</real>
				<key>Green Component</key>
				<real>0.11783610284328461</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Background Image Is Tiled</key>
			<false/>
			<key>Background Image Location</key>
			<string></string>
			<key>Blend</key>
			<real>0.30000001192092896</real>
			<key>Blink Allowed</key>
			<false/>
			<key>Blinking Cursor</key>
			<false/>
			<key>Blur</key>
			<false/>
			<key>Blur Radius</key>
			<real>2</real>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.56363654136657715</real>
				<key>Green Component</key>
				<real>0.56485837697982788</real>
				<key>Red Component</key>
				<real>0.50599193572998047</real>
			</dict>
			<key>Character Encoding</key>
			<integer>4</integer>
			<key>Close Sessions On End</key>
			<true/>
			<key>Columns</key>
			<integer>80</integer>
			<key>Command</key>
			<string></string>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.19370138645172119</real>
				<key>Green Component</key>
				<real>0.15575926005840302</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Cursor Type</key>
			<integer>2</integer>
			<key>Custom Command</key>
			<string>No</string>
			<key>Custom Directory</key>
			<string>No</string>
			<key>Default Bookmark</key>
			<string>No</string>
			<key>Disable Printing</key>
			<false/>
			<key>Disable Smcup Rmcup</key>
			<false/>
			<key>Disable Window Resizing</key>
			<true/>
			<key>Flashing Bell</key>
			<false/>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.51685798168182373</real>
				<key>Green Component</key>
				<real>0.50962930917739868</real>
				<key>Red Component</key>
				<real>0.44058024883270264</real>
			</dict>
			<key>Guid</key>
			<string>C4156A23-8730-43F2-AD25-9DFDD8556C6E</string>
			<key>Hide After Opening</key>
			<false/>
			<key>Horizontal Spacing</key>
			<real>1</real>
			<key>Idle Code</key>
			<integer>0</integer>
			<key>Initial Text</key>
			<string></string>
			<key>Jobs to Ignore</key>
			<array>
				<string>rlogin</string>
				<string>ssh</string>
				<string>slogin</string>
				<string>telnet</string>
			</array>
			<key>Keyboard Map</key>
			<dict>
				<key>0x2d-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x32-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x00</string>
				</dict>
				<key>0x33-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b</string>
				</dict>
				<key>0x34-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1c</string>
				</dict>
				<key>0x35-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1d</string>
				</dict>
				<key>0x36-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1e</string>
				</dict>
				<key>0x37-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x38-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x7f</string>
				</dict>
				<key>0xf700-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2A</string>
				</dict>
				<key>0xf700-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5A</string>
				</dict>
				<key>0xf700-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6A</string>
				</dict>
				<key>0xf700-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x41</string>
				</dict>
				<key>0xf701-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2B</string>
				</dict>
				<key>0xf701-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5B</string>
				</dict>
				<key>0xf701-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6B</string>
				</dict>
				<key>0xf701-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x42</string>
				</dict>
				<key>0xf702-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2D</string>
				</dict>
				<key>0xf702-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5D</string>
				</dict>
				<key>0xf702-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6D</string>
				</dict>
				<key>0xf702-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x44</string>
				</dict>
				<key>0xf703-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2C</string>
				</dict>
				<key>0xf703-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5C</string>
				</dict>
				<key>0xf703-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6C</string>
				</dict>
				<key>0xf703-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x43</string>
				</dict>
				<key>0xf704-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2P</string>
				</dict>
				<key>0xf705-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2Q</string>
				</dict>
				<key>0xf706-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2R</string>
				</dict>
				<key>0xf707-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2S</string>
				</dict>
				<key>0xf708-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[15;2~</string>
				</dict>
				<key>0xf709-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[17;2~</string>
				</dict>
				<key>0xf70a-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[18;2~</string>
				</dict>
				<key>0xf70b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[19;2~</string>
				</dict>
				<key>0xf70c-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[20;2~</string>
				</dict>
				<key>0xf70d-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[21;2~</string>
				</dict>
				<key>0xf70e-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[23;2~</string>
				</dict>
				<key>0xf70f-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[24;2~</string>
				</dict>
				<key>0xf729-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2H</string>
				</dict>
				<key>0xf729-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5H</string>
				</dict>
				<key>0xf72b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2F</string>
				</dict>
				<key>0xf72b-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5F</string>
				</dict>
			</dict>
			<key>Log Directory</key>
			<string></string>
			<key>Minimum Contrast</key>
			<real>0.0</real>
			<key>Mouse Reporting</key>
			<true/>
			<key>Name</key>
			<string>Default</string>
			<key>Non Ascii Font</key>
			<string>Monaco 12</string>
			<key>Non-ASCII Anti Aliased</key>
			<true/>
			<key>Normal Font</key>
			<string>Monaco 12</string>
			<key>Option Key Sends</key>
			<integer>0</integer>
			<key>Prompt Before Closing 2</key>
			<integer>0</integer>
			<key>Right Option Key Sends</key>
			<integer>0</integer>
			<key>Rows</key>
			<integer>25</integer>
			<key>Screen</key>
			<integer>-1</integer>
			<key>Scrollback Lines</key>
			<integer>1000</integer>
			<key>Scrollback With Status Bar</key>
			<false/>
			<key>Scrollback in Alternate Screen</key>
			<true/>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.61306273937225342</real>
				<key>Green Component</key>
				<real>0.35463613271713257</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.13104961812496185</real>
				<key>Green Component</key>
				<real>0.78167229890823364</real>
				<key>Red Component</key>
				<real>0.80354219675064087</real>
			</dict>
			<key>Semantic History</key>
			<dict>
				<key>action</key>
				<string>best editor</string>
				<key>editor</key>
				<string>com.sublimetext.3</string>
				<key>text</key>
				<string></string>
			</dict>
			<key>Send Code When Idle</key>
			<false/>
			<key>Set Local Environment Vars</key>
			<true/>
			<key>Shortcut</key>
			<string></string>
			<key>Show Status Bar</key>
			<true/>
			<key>Silence Bell</key>
			<false/>
			<key>Smart Cursor Color</key>
			<true/>
			<key>Smart Selection Rules</key>
			<array>
				<dict>
					<key>notes</key>
					<string>Word bounded by whitespace</string>
					<key>precision</key>
					<string>low</string>
					<key>regex</key>
					<string>\S+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>C++ namespace::identifier</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>([a-zA-Z0-9_]+::)+[a-zA-Z0-9_]+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Paths</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>\~?/?([[:letter:][:number:]._-]+/+)+[[:letter:][:number:]._-]+/?</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Quoted string</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>@?"(?:[^"\\]|\\.)*"</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Java/Python include paths</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>([[:letter:][:number:]._]+\.)+[[:letter:][:number:]._]+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>mailto URL</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>\bmailto:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Obj-C selector</string>
					<key>precision</key>
					<string>high</string>
					<key>regex</key>
					<string>@selector\([^)]+\)</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>email address</string>
					<key>precision</key>
					<string>high</string>
					<key>regex</key>
					<string>\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>HTTP URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>https?://([a-z0-9A-Z]+(:[a-zA-Z0-9]+)?@)?[a-z0-9A-Z]+(\.[a-z0-9A-Z]+)*((:[0-9]+)?)(/[a-zA-Z0-9;/\.\-_+%~?&amp;@=#\(\)]*)?</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>SSH URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>\bssh:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Telnet URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>\btelnet:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
			</array>
			<key>Status Bar Layout</key>
			<dict>
				<key>advanced configuration</key>
				<dict>
					<key>algorithm</key>
					<integer>0</integer>
					<key>font</key>
					<string>.AppleSystemUIFont 12</string>
				</dict>
				<key>components</key>
				<array>
					<dict>
						<key>class</key>
						<string>iTermStatusBarGitComponent</string>
						<key>configuration</key>
						<dict>
							<key>knobs</key>
							<dict>
								<key>base: compression resistance</key>
								<integer>1</integer>
								<key>base: priority</key>
								<real>5</real>
								<key>iTermStatusBarGitComponentPollingIntervalKey</key>
								<real>10</real>
								<key>maxwidth</key>
								<real>+infinity</real>
								<key>minwidth</key>
								<integer>0</integer>
								<key>shared text color</key>
								<dict>
									<key>Alpha Component</key>
									<real>1</real>
									<key>Blue Component</key>
									<real>0.0</real>
									<key>Color Space</key>
									<string>sRGB</string>
									<key>Green Component</key>
									<real>0.0</real>
									<key>Red Component</key>
									<real>0.0</real>
								</dict>
							</dict>
							<key>layout advanced configuration dictionary value</key>
							<dict>
								<key>algorithm</key>
								<integer>0</integer>
								<key>font</key>
								<string>.AppleSystemUIFont 12</string>
							</dict>
						</dict>
					</dict>
					<dict>
						<key>class</key>
						<string>iTermStatusBarWorkingDirectoryComponent</string>
						<key>configuration</key>
						<dict>
							<key>knobs</key>
							<dict>
								<key>base: compression resistance</key>
								<integer>1</integer>
								<key>base: priority</key>
								<real>5</real>
								<key>maxwidth</key>
								<real>+infinity</real>
								<key>minwidth</key>
								<real>0.0</real>
								<key>shared text color</key>
								<dict>
									<key>Alpha Component</key>
									<real>1</real>
									<key>Blue Component</key>
									<real>0.0</real>
									<key>Color Space</key>
									<string>sRGB</string>
									<key>Green Component</key>
									<real>0.0</real>
									<key>Red Component</key>
									<real>0.0</real>
								</dict>
							</dict>
							<key>layout advanced configuration dictionary value</key>
							<dict>
								<key>algorithm</key>
								<integer>0</integer>
								<key>font</key>
								<string>.AppleSystemUIFont 12</string>
							</dict>
						</dict>
					</dict>
					<dict>
						<key>class</key>
						<string>iTermStatusBarCPUUtilizationComponent</string>
						<key>configuration</key>
						<dict>
							<key>knobs</key>
							<dict>
								<key>base: priority</key>
								<real>5</real>
								<key>shared text color</key>
								<dict>
									<key>Alpha Component</key>
									<real>1</real>
									<key>Blue Component</key>
									<real>0.0</real>
									<key>Color Space</key>
									<string>sRGB</string>
									<key>Green Component</key>
									<real>0.0</real>
									<key>Red Component</key>
									<real>0.0</real>
								</dict>
							</dict>
							<key>layout advanced configuration dictionary value</key>
							<dict>
								<key>algorithm</key>
								<integer>0</integer>
								<key>font</key>
								<string>.AppleSystemUIFont 12</string>
							</dict>
						</dict>
					</dict>
					<dict>
						<key>class</key>
						<string>iTermStatusBarMemoryUtilizationComponent</string>
						<key>configuration</key>
						<dict>
							<key>knobs</key>
							<dict>
								<key>base: priority</key>
								<real>5</real>
								<key>shared text color</key>
								<dict>
									<key>Alpha Component</key>
									<real>1</real>
									<key>Blue Component</key>
									<real>0.0</real>
									<key>Color Space</key>
									<string>sRGB</string>
									<key>Green Component</key>
									<real>0.0</real>
									<key>Red Component</key>
									<real>0.0</real>
								</dict>
							</dict>
							<key>layout advanced configuration dictionary value</key>
							<dict>
								<key>algorithm</key>
								<integer>0</integer>
								<key>font</key>
								<string>.AppleSystemUIFont 12</string>
							</dict>
						</dict>
					</dict>
				</array>
			</dict>
			<key>Sync Title</key>
			<false/>
			<key>Tags</key>
			<array/>
			<key>Terminal Type</key>
			<string>xterm-256color</string>
			<key>Transparency</key>
			<real>0.0</real>
			<key>Triggers</key>
			<array/>
			<key>Unlimited Scrollback</key>
			<false/>
			<key>Use Bold Font</key>
			<false/>
			<key>Use Bright Bold</key>
			<false/>
			<key>Use Canonical Parser</key>
			<false/>
			<key>Use Italic Font</key>
			<true/>
			<key>Vertical Spacing</key>
			<real>1</real>
			<key>Visual Bell</key>
			<true/>
			<key>Window Type</key>
			<integer>0</integer>
			<key>Working Directory</key>
			<string>/Users/<USER>/string>
		</dict>
		<dict>
			<key>ASCII Anti Aliased</key>
			<true/>
			<key>Allow Title Reporting</key>
			<false/>
			<key>Ambiguous Double Width</key>
			<false/>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Automatically Log</key>
			<false/>
			<key>BM Growl</key>
			<true/>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Background Image Is Tiled</key>
			<false/>
			<key>Background Image Location</key>
			<string></string>
			<key>Blend</key>
			<real>0.5</real>
			<key>Blink Allowed</key>
			<false/>
			<key>Blinking Cursor</key>
			<false/>
			<key>Blur</key>
			<true/>
			<key>Blur Radius</key>
			<real>2</real>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Character Encoding</key>
			<integer>4</integer>
			<key>Close Sessions On End</key>
			<true/>
			<key>Columns</key>
			<integer>80</integer>
			<key>Command</key>
			<string></string>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Cursor Type</key>
			<integer>2</integer>
			<key>Custom Command</key>
			<string>No</string>
			<key>Custom Directory</key>
			<string>No</string>
			<key>Default Bookmark</key>
			<string>No</string>
			<key>Disable Printing</key>
			<false/>
			<key>Disable Smcup Rmcup</key>
			<false/>
			<key>Disable Window Resizing</key>
			<true/>
			<key>Flashing Bell</key>
			<false/>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Guid</key>
			<string>D3D9908E-C06A-4B91-BC20-AED646EC7625</string>
			<key>Hide After Opening</key>
			<false/>
			<key>Horizontal Spacing</key>
			<real>1</real>
			<key>Idle Code</key>
			<integer>0</integer>
			<key>Initial Text</key>
			<string></string>
			<key>Jobs to Ignore</key>
			<array>
				<string>rlogin</string>
				<string>ssh</string>
				<string>slogin</string>
				<string>telnet</string>
			</array>
			<key>Keyboard Map</key>
			<dict>
				<key>0x2d-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x32-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x00</string>
				</dict>
				<key>0x33-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b</string>
				</dict>
				<key>0x34-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1c</string>
				</dict>
				<key>0x35-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1d</string>
				</dict>
				<key>0x36-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1e</string>
				</dict>
				<key>0x37-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x38-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x7f</string>
				</dict>
				<key>0xf700-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2A</string>
				</dict>
				<key>0xf700-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5A</string>
				</dict>
				<key>0xf700-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6A</string>
				</dict>
				<key>0xf700-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x41</string>
				</dict>
				<key>0xf701-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2B</string>
				</dict>
				<key>0xf701-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5B</string>
				</dict>
				<key>0xf701-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6B</string>
				</dict>
				<key>0xf701-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x42</string>
				</dict>
				<key>0xf702-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2D</string>
				</dict>
				<key>0xf702-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5D</string>
				</dict>
				<key>0xf702-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6D</string>
				</dict>
				<key>0xf702-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x44</string>
				</dict>
				<key>0xf703-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2C</string>
				</dict>
				<key>0xf703-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5C</string>
				</dict>
				<key>0xf703-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6C</string>
				</dict>
				<key>0xf703-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x43</string>
				</dict>
				<key>0xf704-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2P</string>
				</dict>
				<key>0xf705-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2Q</string>
				</dict>
				<key>0xf706-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2R</string>
				</dict>
				<key>0xf707-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2S</string>
				</dict>
				<key>0xf708-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[15;2~</string>
				</dict>
				<key>0xf709-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[17;2~</string>
				</dict>
				<key>0xf70a-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[18;2~</string>
				</dict>
				<key>0xf70b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[19;2~</string>
				</dict>
				<key>0xf70c-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[20;2~</string>
				</dict>
				<key>0xf70d-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[21;2~</string>
				</dict>
				<key>0xf70e-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[23;2~</string>
				</dict>
				<key>0xf70f-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[24;2~</string>
				</dict>
				<key>0xf729-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2H</string>
				</dict>
				<key>0xf729-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5H</string>
				</dict>
				<key>0xf72b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2F</string>
				</dict>
				<key>0xf72b-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5F</string>
				</dict>
			</dict>
			<key>Log Directory</key>
			<string></string>
			<key>Minimum Contrast</key>
			<real>0.0</real>
			<key>Mouse Reporting</key>
			<true/>
			<key>Name</key>
			<string>Hotkey Window</string>
			<key>Non Ascii Font</key>
			<string>Monaco 12</string>
			<key>Non-ASCII Anti Aliased</key>
			<true/>
			<key>Normal Font</key>
			<string>Monaco 12</string>
			<key>Option Key Sends</key>
			<integer>0</integer>
			<key>Prompt Before Closing 2</key>
			<integer>0</integer>
			<key>Right Option Key Sends</key>
			<integer>0</integer>
			<key>Rows</key>
			<integer>25</integer>
			<key>Screen</key>
			<integer>-1</integer>
			<key>Scrollback Lines</key>
			<integer>1000</integer>
			<key>Scrollback With Status Bar</key>
			<false/>
			<key>Scrollback in Alternate Screen</key>
			<true/>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.8353000283241272</real>
				<key>Red Component</key>
				<real>0.70980000495910645</real>
			</dict>
			<key>Semantic History</key>
			<dict>
				<key>action</key>
				<string>best editor</string>
				<key>editor</key>
				<string>com.sublimetext.3</string>
				<key>text</key>
				<string></string>
			</dict>
			<key>Send Code When Idle</key>
			<false/>
			<key>Set Local Environment Vars</key>
			<true/>
			<key>Shortcut</key>
			<string></string>
			<key>Silence Bell</key>
			<false/>
			<key>Smart Cursor Color</key>
			<true/>
			<key>Smart Selection Rules</key>
			<array>
				<dict>
					<key>notes</key>
					<string>Word bounded by whitespace</string>
					<key>precision</key>
					<string>low</string>
					<key>regex</key>
					<string>\S+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>C++ namespace::identifier</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>([a-zA-Z0-9_]+::)+[a-zA-Z0-9_]+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Paths</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>\~?/?([[:letter:][:number:]._-]+/+)+[[:letter:][:number:]._-]+/?</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Quoted string</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>@?"(?:[^"\\]|\\.)*"</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Java/Python include paths</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>([[:letter:][:number:]._]+\.)+[[:letter:][:number:]._]+</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>mailto URL</string>
					<key>precision</key>
					<string>normal</string>
					<key>regex</key>
					<string>\bmailto:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Obj-C selector</string>
					<key>precision</key>
					<string>high</string>
					<key>regex</key>
					<string>@selector\([^)]+\)</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>email address</string>
					<key>precision</key>
					<string>high</string>
					<key>regex</key>
					<string>\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>HTTP URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>https?://([a-z0-9A-Z]+(:[a-zA-Z0-9]+)?@)?[a-z0-9A-Z]+(\.[a-z0-9A-Z]+)*((:[0-9]+)?)(/[a-zA-Z0-9;/\.\-_+%~?&amp;@=#\(\)]*)?</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>SSH URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>\bssh:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
				<dict>
					<key>notes</key>
					<string>Telnet URL</string>
					<key>precision</key>
					<string>very_high</string>
					<key>regex</key>
					<string>\btelnet:([a-z0-9A-Z_]+@)?([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\b</string>
				</dict>
			</array>
			<key>Space</key>
			<integer>-1</integer>
			<key>Sync Title</key>
			<false/>
			<key>Tags</key>
			<array/>
			<key>Terminal Type</key>
			<string>xterm-256color</string>
			<key>Transparency</key>
			<real>0.30000001192092896</real>
			<key>Triggers</key>
			<array/>
			<key>Unlimited Scrollback</key>
			<false/>
			<key>Use Bold Font</key>
			<true/>
			<key>Use Bright Bold</key>
			<true/>
			<key>Use Canonical Parser</key>
			<false/>
			<key>Use Italic Font</key>
			<true/>
			<key>Vertical Spacing</key>
			<real>1</real>
			<key>Visual Bell</key>
			<true/>
			<key>Window Type</key>
			<integer>2</integer>
			<key>Working Directory</key>
			<string>/Users/<USER>/string>
		</dict>
	</array>
	<key>NoSyncAllAppVersions</key>
	<array>
		<string>3.2.6</string>
		<string>3.3.9</string>
		<string>3.3.12</string>
	</array>
	<key>NoSyncConfirmBeta</key>
	<true/>
	<key>NoSyncDoNotWarnBeforeMultilinePaste</key>
	<true/>
	<key>NoSyncDoNotWarnBeforeMultilinePaste_selection</key>
	<integer>0</integer>
	<key>NoSyncDoNotWarnBeforePastingOneLineEndingInNewlineAtShellPrompt</key>
	<true/>
	<key>NoSyncDoNotWarnBeforePastingOneLineEndingInNewlineAtShellPrompt_selection</key>
	<integer>1</integer>
	<key>NoSyncFrame_SessionsPreferences</key>
	<dict>
		<key>screenFrame</key>
		<string>{{1440, -180}, {1920, 1080}}</string>
		<key>topLeft</key>
		<string>{2097, 749}</string>
	</dict>
	<key>NoSyncFrame_SharedPreferences</key>
	<dict>
		<key>screenFrame</key>
		<string>{{1440, -180}, {1920, 1080}}</string>
		<key>topLeft</key>
		<string>{1508, 664}</string>
	</dict>
	<key>NoSyncHaveRequestedFullDiskAccess</key>
	<true/>
	<key>NoSyncHaveShownPromoWithId_4</key>
	<true/>
	<key>NoSyncInstallationId</key>
	<string>E168B6AB-2D65-442E-81C0-CC7E7B49D7B9</string>
	<key>NoSyncLastTipTime</key>
	<real>610046804.83505499</real>
	<key>NoSyncLaunchExperienceControllerRunCount</key>
	<integer>19</integer>
	<key>NoSyncNeverRemindPrefsChangesLostForFile</key>
	<true/>
	<key>NoSyncNeverRemindPrefsChangesLostForFile_selection</key>
	<integer>0</integer>
	<key>NoSyncNextAnnoyanceTime</key>
	<real>609779531.80716097</real>
	<key>NoSyncOnboardingWindowHasBeenShown</key>
	<true/>
	<key>NoSyncPermissionToShowTip</key>
	<true/>
	<key>NoSyncRecordedVariables</key>
	<dict>
		<key>0</key>
		<array>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string></string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
		</array>
		<key>1</key>
		<array>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>presentationName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxRole</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>lastCommand</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>profileName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>termid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>id</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>jobName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>columns</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tab.tmuxWindowTitle</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>hostname</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxClientName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>path</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>triggerName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>terminalIconName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxWindowPane</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxStatusRight</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>mouseReportingMode</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>iterm2</string>
				<key>nonterminalContext</key>
				<integer>4</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>name</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxPaneTitle</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>rows</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>username</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tty</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>autoLogId</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>badge</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tab.tmuxWindowName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>tab</string>
				<key>nonterminalContext</key>
				<integer>2</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxStatusLeft</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>autoNameFormat</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>autoName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>terminalWindowName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>creationTimeString</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>commandLine</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>jobPid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>pid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
		</array>
		<key>16</key>
		<array>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.presentationName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.iterm2.localhostName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>style</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>frame</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.pid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.termid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.terminalIconName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.terminalWindowName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>currentTab</string>
				<key>nonterminalContext</key>
				<integer>2</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.window</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>id</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.name</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>titleOverride</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>number</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.path</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.commandLine</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.hostname</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.tty</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.username</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>iterm2</string>
				<key>nonterminalContext</key>
				<integer>4</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>titleOverrideFormat</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentTab.currentSession.jobName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
		</array>
		<key>2</key>
		<array>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.pid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.terminalIconName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>title</string>
				<key>nonterminalContext</key>
				<integer>1</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>title</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxWindowTitle</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.commandLine</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.presentationName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>iterm2.localhostName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.jobPid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxWindowName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>window</string>
				<key>nonterminalContext</key>
				<integer>16</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.tty</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.jobName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.name</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>window</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>id</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>titleOverride</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.username</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.termid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>iterm2</string>
				<key>nonterminalContext</key>
				<integer>4</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>titleOverrideFormat</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.hostname</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.terminalWindowName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession.path</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>tmuxWindow</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>currentSession</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<false/>
				<key>name</key>
				<string>currentSession</string>
				<key>nonterminalContext</key>
				<integer>1</integer>
			</dict>
		</array>
		<key>4</key>
		<array>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>pid</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>localhostName</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
			<dict>
				<key>isTerminal</key>
				<true/>
				<key>name</key>
				<string>effectiveTheme</string>
				<key>nonterminalContext</key>
				<integer>0</integer>
			</dict>
		</array>
	</dict>
	<key>NoSyncTimeOfFirstLaunchOfVersionWithTip</key>
	<real>486912528.31174302</real>
	<key>NoSyncTimeOfLastPromo</key>
	<real>477959342.55845201</real>
	<key>NoSyncTimeOfLastPromoDownload</key>
	<real>486912497.38426399</real>
	<key>NoSyncTipsToNotShow</key>
	<array>
		<string>0009</string>
		<string>0016</string>
		<string>0023</string>
		<string>0030</string>
		<string>0059</string>
		<string>0066</string>
		<string>0073</string>
		<string>0002</string>
		<string>0038</string>
		<string>0045</string>
		<string>0052</string>
		<string>0017</string>
		<string>0024</string>
		<string>0031</string>
		<string>0067</string>
		<string>0074</string>
		<string>0003</string>
		<string>0010</string>
		<string>0039</string>
		<string>0046</string>
		<string>0053</string>
		<string>0060</string>
		<string>0018</string>
		<string>0025</string>
		<string>0032</string>
		<string>0068</string>
		<string>0004</string>
		<string>0011</string>
		<string>0047</string>
		<string>0054</string>
		<string>0061</string>
		<string>0019</string>
		<string>0026</string>
		<string>0033</string>
		<string>0040</string>
		<string>0069</string>
		<string>0076</string>
		<string>0005</string>
		<string>0012</string>
		<string>0048</string>
		<string>0055</string>
		<string>0062</string>
		<string>0027</string>
		<string>0034</string>
		<string>0041</string>
		<string>0077</string>
		<string>0006</string>
		<string>0013</string>
		<string>0020</string>
		<string>0049</string>
		<string>0056</string>
		<string>0063</string>
		<string>0070</string>
		<string>0028</string>
		<string>0035</string>
		<string>0042</string>
		<string>0007</string>
		<string>0014</string>
		<string>0021</string>
		<string>0057</string>
		<string>0064</string>
		<string>0071</string>
		<string>0000</string>
		<string>0029</string>
		<string>0036</string>
		<string>0043</string>
		<string>0050</string>
		<string>0008</string>
		<string>0015</string>
		<string>0022</string>
		<string>0058</string>
		<string>0065</string>
		<string>0072</string>
		<string>0001</string>
		<string>0037</string>
		<string>000</string>
		<string>0044</string>
		<string>0051</string>
		<string>0078</string>
	</array>
	<key>NumberOfLinesForAccessibility</key>
	<integer>8000</integer>
	<key>OnlyWhenMoreTabs</key>
	<true/>
	<key>OpenArrangementAtStartup</key>
	<false/>
	<key>OpenBookmark</key>
	<false/>
	<key>OpenTmuxWindowsIn</key>
	<integer>0</integer>
	<key>PMPrintingExpandedStateForPrint2</key>
	<false/>
	<key>PassOnControlClick</key>
	<false/>
	<key>PasteFromClipboard</key>
	<false/>
	<key>PasteTabToStringTabStopSize</key>
	<integer>4</integer>
	<key>PointerActions</key>
	<dict>
		<key>Button,1,1,,</key>
		<dict>
			<key>Action</key>
			<string>kContextMenuPointerAction</string>
		</dict>
		<key>Button,2,1,,</key>
		<dict>
			<key>Action</key>
			<string>kPasteFromClipboardPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeDown,,</key>
		<dict>
			<key>Action</key>
			<string>kPrevWindowPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeLeft,,</key>
		<dict>
			<key>Action</key>
			<string>kPrevTabPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeRight,,</key>
		<dict>
			<key>Action</key>
			<string>kNextTabPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeUp,,</key>
		<dict>
			<key>Action</key>
			<string>kNextWindowPointerAction</string>
		</dict>
	</dict>
	<key>PrefsCustomFolder</key>
	<string>/Users/<USER>/Documents</string>
	<key>Print In Black And White</key>
	<true/>
	<key>PromptOnQuit</key>
	<true/>
	<key>QuitWhenAllWindowsClosed</key>
	<false/>
	<key>RightCommand</key>
	<integer>8</integer>
	<key>RightOption</key>
	<integer>3</integer>
	<key>SUAutomaticallyUpdate</key>
	<true/>
	<key>SUEnableAutomaticChecks</key>
	<true/>
	<key>SUFeedAlternateAppNameKey</key>
	<string>iTerm</string>
	<key>SUFeedURL</key>
	<string>https://iterm2.com/appcasts/final_new.xml?shard=34</string>
	<key>SUHasLaunchedBefore</key>
	<true/>
	<key>SULastCheckTime</key>
	<date>2020-11-08T21:55:11Z</date>
	<key>SUUpdateRelaunchingMarker</key>
	<false/>
	<key>SavePasteHistory</key>
	<false/>
	<key>ShowBookmarkName</key>
	<false/>
	<key>ShowFullScreenTabBar</key>
	<false/>
	<key>ShowPaneTitles</key>
	<true/>
	<key>SmartPlacement</key>
	<false/>
	<key>SoundForEsc</key>
	<false/>
	<key>SplitPaneDimmingAmount</key>
	<real>0.40000000596046448</real>
	<key>StatusBarPosition</key>
	<integer>1</integer>
	<key>SwitchTabModifier</key>
	<integer>4</integer>
	<key>SwitchWindowModifier</key>
	<integer>6</integer>
	<key>TabViewType</key>
	<integer>0</integer>
	<key>ThreeFingerEmulates</key>
	<false/>
	<key>TmuxDashboardLimit</key>
	<integer>10</integer>
	<key>TripleClickSelectsFullWrappedLines</key>
	<false/>
	<key>URLHandlersByGuid</key>
	<dict/>
	<key>UseBorder</key>
	<false/>
	<key>UseCompactLabel</key>
	<true/>
	<key>UseLionStyleFullscreen</key>
	<true/>
	<key>VisualIndicatorForEsc</key>
	<false/>
	<key>WebKitDefaultFontSize</key>
	<integer>11</integer>
	<key>WebKitStandardFont</key>
	<string>.AppleSystemUIFont</string>
	<key>WindowNumber</key>
	<true/>
	<key>WindowStyle</key>
	<integer>0</integer>
	<key>WordCharacters</key>
	<string>/-+\~_.</string>
	<key>findIgnoreCase_iTerm</key>
	<true/>
	<key>findMode_iTerm</key>
	<integer>0</integer>
	<key>findRegex_iTerm</key>
	<false/>
	<key>iTerm Version</key>
	<string>3.3.12</string>
	<key>kCPFavoritesUserDefaultsKey</key>
	<array>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFVCbGFja9USExQV
		FhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJOU0N1
		c3RvbUNvbG9yU3BhY2VWJGNsYXNzRzAgMCAwIDFGMCAwIDAAEAGAA4AG0x0e
		Fh8gIVROU0lEVU5TSUNDEAeABIAFTxEMSAAADEhMaW5vAhAAAG1udHJSR0Ig
		WFlaIAfOAAIACQAGADEAAGFjc3BNU0ZUAAAAAElFQyBzUkdCAAAAAAAAAAAA
		AAAAAAD21gABAAAAANMtSFAgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAEWNwcnQAAAFQAAAAM2Rlc2MAAAGEAAAA
		bHd0cHQAAAHwAAAAFGJrcHQAAAIEAAAAFHJYWVoAAAIYAAAAFGdYWVoAAAIs
		AAAAFGJYWVoAAAJAAAAAFGRtbmQAAAJUAAAAcGRtZGQAAALEAAAAiHZ1ZWQA
		AANMAAAAhnZpZXcAAAPUAAAAJGx1bWkAAAP4AAAAFG1lYXMAAAQMAAAAJHRl
		Y2gAAAQwAAAADHJUUkMAAAQ8AAAIDGdUUkMAAAQ8AAAIDGJUUkMAAAQ8AAAI
		DHRleHQAAAAAQ29weXJpZ2h0IChjKSAxOTk4IEhld2xldHQtUGFja2FyZCBD
		b21wYW55AABkZXNjAAAAAAAAABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAA
		AAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAA81EAAQAAAAEW
		zFhZWiAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAG+iAAA49QAAA5BYWVog
		AAAAAAAAYpkAALeFAAAY2lhZWiAAAAAAAAAkoAAAD4QAALbPZGVzYwAAAAAA
		AAAWSUVDIGh0dHA6Ly93d3cuaWVjLmNoAAAAAAAAAAAAAAAWSUVDIGh0dHA6
		Ly93d3cuaWVjLmNoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALklFQyA2MTk2Ni0yLjEgRGVmYXVs
		dCBSR0IgY29sb3VyIHNwYWNlIC0gc1JHQgAAAAAAAAAAAAAALklFQyA2MTk2
		Ni0yLjEgRGVmYXVsdCBSR0IgY29sb3VyIHNwYWNlIC0gc1JHQgAAAAAAAAAA
		AAAAAAAAAAAAAAAAAABkZXNjAAAAAAAAACxSZWZlcmVuY2UgVmlld2luZyBD
		b25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAsUmVmZXJlbmNl
		IFZpZXdpbmcgQ29uZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAdmlldwAAAAAAE6T+ABRfLgAQzxQAA+3MAAQTCwAD
		XJ4AAAABWFlaIAAAAAAATAlWAFAAAABXH+dtZWFzAAAAAAAAAAEAAAAAAAAA
		AAAAAAAAAAAAAAACjwAAAAJzaWcgAAAAAENSVCBjdXJ2AAAAAAAABAAAAAAF
		AAoADwAUABkAHgAjACgALQAyADcAOwBAAEUASgBPAFQAWQBeAGMAaABtAHIA
		dwB8AIEAhgCLAJAAlQCaAJ8ApACpAK4AsgC3ALwAwQDGAMsA0ADVANsA4ADl
		AOsA8AD2APsBAQEHAQ0BEwEZAR8BJQErATIBOAE+AUUBTAFSAVkBYAFnAW4B
		dQF8AYMBiwGSAZoBoQGpAbEBuQHBAckB0QHZAeEB6QHyAfoCAwIMAhQCHQIm
		Ai8COAJBAksCVAJdAmcCcQJ6AoQCjgKYAqICrAK2AsECywLVAuAC6wL1AwAD
		CwMWAyEDLQM4A0MDTwNaA2YDcgN+A4oDlgOiA64DugPHA9MD4APsA/kEBgQT
		BCAELQQ7BEgEVQRjBHEEfgSMBJoEqAS2BMQE0wThBPAE/gUNBRwFKwU6BUkF
		WAVnBXcFhgWWBaYFtQXFBdUF5QX2BgYGFgYnBjcGSAZZBmoGewaMBp0GrwbA
		BtEG4wb1BwcHGQcrBz0HTwdhB3QHhgeZB6wHvwfSB+UH+AgLCB8IMghGCFoI
		bgiCCJYIqgi+CNII5wj7CRAJJQk6CU8JZAl5CY8JpAm6Cc8J5Qn7ChEKJwo9
		ClQKagqBCpgKrgrFCtwK8wsLCyILOQtRC2kLgAuYC7ALyAvhC/kMEgwqDEMM
		XAx1DI4MpwzADNkM8w0NDSYNQA1aDXQNjg2pDcMN3g34DhMOLg5JDmQOfw6b
		DrYO0g7uDwkPJQ9BD14Peg+WD7MPzw/sEAkQJhBDEGEQfhCbELkQ1xD1ERMR
		MRFPEW0RjBGqEckR6BIHEiYSRRJkEoQSoxLDEuMTAxMjE0MTYxODE6QTxRPl
		FAYUJxRJFGoUixStFM4U8BUSFTQVVhV4FZsVvRXgFgMWJhZJFmwWjxayFtYW
		+hcdF0EXZReJF64X0hf3GBsYQBhlGIoYrxjVGPoZIBlFGWsZkRm3Gd0aBBoq
		GlEadxqeGsUa7BsUGzsbYxuKG7Ib2hwCHCocUhx7HKMczBz1HR4dRx1wHZkd
		wx3sHhYeQB5qHpQevh7pHxMfPh9pH5Qfvx/qIBUgQSBsIJggxCDwIRwhSCF1
		IaEhziH7IiciVSKCIq8i3SMKIzgjZiOUI8Ij8CQfJE0kfCSrJNolCSU4JWgl
		lyXHJfcmJyZXJocmtyboJxgnSSd6J6sn3CgNKD8ocSiiKNQpBik4KWspnSnQ
		KgIqNSpoKpsqzysCKzYraSudK9EsBSw5LG4soizXLQwtQS12Last4S4WLkwu
		gi63Lu4vJC9aL5Evxy/+MDUwbDCkMNsxEjFKMYIxujHyMioyYzKbMtQzDTNG
		M38zuDPxNCs0ZTSeNNg1EzVNNYc1wjX9Njc2cjauNuk3JDdgN5w31zgUOFA4
		jDjIOQU5Qjl/Obw5+To2OnQ6sjrvOy07azuqO+g8JzxlPKQ84z0iPWE9oT3g
		PiA+YD6gPuA/IT9hP6I/4kAjQGRApkDnQSlBakGsQe5CMEJyQrVC90M6Q31D
		wEQDREdEikTORRJFVUWaRd5GIkZnRqtG8Ec1R3tHwEgFSEtIkUjXSR1JY0mp
		SfBKN0p9SsRLDEtTS5pL4kwqTHJMuk0CTUpNk03cTiVObk63TwBPSU+TT91Q
		J1BxULtRBlFQUZtR5lIxUnxSx1MTU19TqlP2VEJUj1TbVShVdVXCVg9WXFap
		VvdXRFeSV+BYL1h9WMtZGllpWbhaB1pWWqZa9VtFW5Vb5Vw1XIZc1l0nXXhd
		yV4aXmxevV8PX2Ffs2AFYFdgqmD8YU9homH1YklinGLwY0Njl2PrZEBklGTp
		ZT1lkmXnZj1mkmboZz1nk2fpaD9olmjsaUNpmmnxakhqn2r3a09rp2v/bFds
		r20IbWBtuW4SbmtuxG8eb3hv0XArcIZw4HE6cZVx8HJLcqZzAXNdc7h0FHRw
		dMx1KHWFdeF2Pnabdvh3VnezeBF4bnjMeSp5iXnnekZ6pXsEe2N7wnwhfIF8
		4X1BfaF+AX5ifsJ/I3+Ef+WAR4CogQqBa4HNgjCCkoL0g1eDuoQdhICE44VH
		hauGDoZyhteHO4efiASIaYjOiTOJmYn+imSKyoswi5aL/IxjjMqNMY2Yjf+O
		Zo7OjzaPnpAGkG6Q1pE/kaiSEZJ6kuOTTZO2lCCUipT0lV+VyZY0lp+XCpd1
		l+CYTJi4mSSZkJn8mmia1ZtCm6+cHJyJnPedZJ3SnkCerp8dn4uf+qBpoNih
		R6G2oiailqMGo3aj5qRWpMelOKWpphqmi6b9p26n4KhSqMSpN6mpqhyqj6sC
		q3Wr6axcrNCtRK24ri2uoa8Wr4uwALB1sOqxYLHWskuywrM4s660JbSctRO1
		irYBtnm28Ldot+C4WbjRuUq5wro7urW7LrunvCG8m70VvY++Cr6Evv+/er/1
		wHDA7MFnwePCX8Lbw1jD1MRRxM7FS8XIxkbGw8dBx7/IPci8yTrJuco4yrfL
		Nsu2zDXMtc01zbXONs62zzfPuNA50LrRPNG+0j/SwdNE08bUSdTL1U7V0dZV
		1tjXXNfg2GTY6Nls2fHadtr724DcBdyK3RDdlt4c3qLfKd+v4DbgveFE4czi
		U+Lb42Pj6+Rz5PzlhOYN5pbnH+ep6DLovOlG6dDqW+rl63Dr++yG7RHtnO4o
		7rTvQO/M8Fjw5fFy8f/yjPMZ86f0NPTC9VD13vZt9vv3ivgZ+Kj5OPnH+lf6
		5/t3/Af8mP0p/br+S/7c/23//9IkJSYnWiRjbGFzc25hbWVYJGNsYXNzZXNc
		TlNDb2xvclNwYWNloigpXE5TQ29sb3JTcGFjZVhOU09iamVjdNIkJSssV05T
		Q29sb3KiKynSFi4vMFlOUy5zdHJpbmeACF8QJDA1RDExNzQ2LTJGMjEtNDQx
		Ny04NTBCLTM5RDM4QTAzRDI2MdIkJTIzXxAPTlNNdXRhYmxlU3RyaW5nozI0
		KVhOU1N0cmluZwAIABEAGgAkACkAMgA3AEkAUABVAFsAZgBoAGoAbAB2AHwA
		ggCNAJoAoACtAMIAyQDRANgA2gDcAN4A5QDqAPAA8gD0APYNQg1HDVINWw1o
		DWsNeA2BDYYNjg2RDZYNoA2iDckNzg3gDeQAAAAAAAACAQAAAAAAAAA1AAAA
		AAAAAAAAAAAAAAAN7Q==
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFgxMCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuMSAwLjEgMC4xIDFPECkwLjA3
		NzUyNDgwMzU4IDAuMDc3NTIyNDkzOSAwLjA3NzUyMzgxMjY1ABABgAOABtMd
		HhYfICFUTlNJRFVOU0lDQxAHgASABU8RDEgAAAxITGlubwIQAABtbnRyUkdC
		IFhZWiAHzgACAAkABgAxAABhY3NwTVNGVAAAAABJRUMgc1JHQgAAAAAAAAAA
		AAAAAAAA9tYAAQAAAADTLUhQICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAABFjcHJ0AAABUAAAADNkZXNjAAABhAAA
		AGx3dHB0AAAB8AAAABRia3B0AAACBAAAABRyWFlaAAACGAAAABRnWFlaAAAC
		LAAAABRiWFlaAAACQAAAABRkbW5kAAACVAAAAHBkbWRkAAACxAAAAIh2dWVk
		AAADTAAAAIZ2aWV3AAAD1AAAACRsdW1pAAAD+AAAABRtZWFzAAAEDAAAACR0
		ZWNoAAAEMAAAAAxyVFJDAAAEPAAACAxnVFJDAAAEPAAACAxiVFJDAAAEPAAA
		CAx0ZXh0AAAAAENvcHlyaWdodCAoYykgMTk5OCBIZXdsZXR0LVBhY2thcmQg
		Q29tcGFueQAAZGVzYwAAAAAAAAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAA
		AAAAABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAPNRAAEAAAAB
		FsxYWVogAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAABvogAAOPUAAAOQWFla
		IAAAAAAAAGKZAAC3hQAAGNpYWVogAAAAAAAAJKAAAA+EAAC2z2Rlc2MAAAAA
		AAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAFklFQyBodHRw
		Oi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAABkZXNjAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1
		bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAC5JRUMgNjE5
		NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAsUmVmZXJlbmNlIFZpZXdpbmcg
		Q29uZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAALFJlZmVyZW5j
		ZSBWaWV3aW5nIENvbmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAHZpZXcAAAAAABOk/gAUXy4AEM8UAAPtzAAEEwsA
		A1yeAAAAAVhZWiAAAAAAAEwJVgBQAAAAVx/nbWVhcwAAAAAAAAABAAAAAAAA
		AAAAAAAAAAAAAAAAAo8AAAACc2lnIAAAAABDUlQgY3VydgAAAAAAAAQAAAAA
		BQAKAA8AFAAZAB4AIwAoAC0AMgA3ADsAQABFAEoATwBUAFkAXgBjAGgAbQBy
		AHcAfACBAIYAiwCQAJUAmgCfAKQAqQCuALIAtwC8AMEAxgDLANAA1QDbAOAA
		5QDrAPAA9gD7AQEBBwENARMBGQEfASUBKwEyATgBPgFFAUwBUgFZAWABZwFu
		AXUBfAGDAYsBkgGaAaEBqQGxAbkBwQHJAdEB2QHhAekB8gH6AgMCDAIUAh0C
		JgIvAjgCQQJLAlQCXQJnAnECegKEAo4CmAKiAqwCtgLBAssC1QLgAusC9QMA
		AwsDFgMhAy0DOANDA08DWgNmA3IDfgOKA5YDogOuA7oDxwPTA+AD7AP5BAYE
		EwQgBC0EOwRIBFUEYwRxBH4EjASaBKgEtgTEBNME4QTwBP4FDQUcBSsFOgVJ
		BVgFZwV3BYYFlgWmBbUFxQXVBeUF9gYGBhYGJwY3BkgGWQZqBnsGjAadBq8G
		wAbRBuMG9QcHBxkHKwc9B08HYQd0B4YHmQesB78H0gflB/gICwgfCDIIRgha
		CG4IggiWCKoIvgjSCOcI+wkQCSUJOglPCWQJeQmPCaQJugnPCeUJ+woRCicK
		PQpUCmoKgQqYCq4KxQrcCvMLCwsiCzkLUQtpC4ALmAuwC8gL4Qv5DBIMKgxD
		DFwMdQyODKcMwAzZDPMNDQ0mDUANWg10DY4NqQ3DDd4N+A4TDi4OSQ5kDn8O
		mw62DtIO7g8JDyUPQQ9eD3oPlg+zD88P7BAJECYQQxBhEH4QmxC5ENcQ9RET
		ETERTxFtEYwRqhHJEegSBxImEkUSZBKEEqMSwxLjEwMTIxNDE2MTgxOkE8UT
		5RQGFCcUSRRqFIsUrRTOFPAVEhU0FVYVeBWbFb0V4BYDFiYWSRZsFo8WshbW
		FvoXHRdBF2UXiReuF9IX9xgbGEAYZRiKGK8Y1Rj6GSAZRRlrGZEZtxndGgQa
		KhpRGncanhrFGuwbFBs7G2MbihuyG9ocAhwqHFIcexyjHMwc9R0eHUcdcB2Z
		HcMd7B4WHkAeah6UHr4e6R8THz4faR+UH78f6iAVIEEgbCCYIMQg8CEcIUgh
		dSGhIc4h+yInIlUigiKvIt0jCiM4I2YjlCPCI/AkHyRNJHwkqyTaJQklOCVo
		JZclxyX3JicmVyaHJrcm6CcYJ0kneierJ9woDSg/KHEooijUKQYpOClrKZ0p
		0CoCKjUqaCqbKs8rAis2K2krnSvRLAUsOSxuLKIs1y0MLUEtdi2rLeEuFi5M
		LoIuty7uLyQvWi+RL8cv/jA1MGwwpDDbMRIxSjGCMbox8jIqMmMymzLUMw0z
		RjN/M7gz8TQrNGU0njTYNRM1TTWHNcI1/TY3NnI2rjbpNyQ3YDecN9c4FDhQ
		OIw4yDkFOUI5fzm8Ofk6Njp0OrI67zstO2s7qjvoPCc8ZTykPOM9Ij1hPaE9
		4D4gPmA+oD7gPyE/YT+iP+JAI0BkQKZA50EpQWpBrEHuQjBCckK1QvdDOkN9
		Q8BEA0RHRIpEzkUSRVVFmkXeRiJGZ0arRvBHNUd7R8BIBUhLSJFI10kdSWNJ
		qUnwSjdKfUrESwxLU0uaS+JMKkxyTLpNAk1KTZNN3E4lTm5Ot08AT0lPk0/d
		UCdQcVC7UQZRUFGbUeZSMVJ8UsdTE1NfU6pT9lRCVI9U21UoVXVVwlYPVlxW
		qVb3V0RXklfgWC9YfVjLWRpZaVm4WgdaVlqmWvVbRVuVW+VcNVyGXNZdJ114
		XcleGl5sXr1fD19hX7NgBWBXYKpg/GFPYaJh9WJJYpxi8GNDY5dj62RAZJRk
		6WU9ZZJl52Y9ZpJm6Gc9Z5Nn6Wg/aJZo7GlDaZpp8WpIap9q92tPa6dr/2xX
		bK9tCG1gbbluEm5rbsRvHm94b9FwK3CGcOBxOnGVcfByS3KmcwFzXXO4dBR0
		cHTMdSh1hXXhdj52m3b4d1Z3s3gReG54zHkqeYl553pGeqV7BHtje8J8IXyB
		fOF9QX2hfgF+Yn7CfyN/hH/lgEeAqIEKgWuBzYIwgpKC9INXg7qEHYSAhOOF
		R4Wrhg6GcobXhzuHn4gEiGmIzokziZmJ/opkisqLMIuWi/yMY4zKjTGNmI3/
		jmaOzo82j56QBpBukNaRP5GokhGSepLjk02TtpQglIqU9JVflcmWNJaflwqX
		dZfgmEyYuJkkmZCZ/JpomtWbQpuvnByciZz3nWSd0p5Anq6fHZ+Ln/qgaaDY
		oUehtqImopajBqN2o+akVqTHpTilqaYapoum/adup+CoUqjEqTepqaocqo+r
		Aqt1q+msXKzQrUStuK4trqGvFq+LsACwdbDqsWCx1rJLssKzOLOutCW0nLUT
		tYq2AbZ5tvC3aLfguFm40blKucK6O7q1uy67p7whvJu9Fb2Pvgq+hL7/v3q/
		9cBwwOzBZ8Hjwl/C28NYw9TEUcTOxUvFyMZGxsPHQce/yD3IvMk6ybnKOMq3
		yzbLtsw1zLXNNc21zjbOts83z7jQOdC60TzRvtI/0sHTRNPG1EnUy9VO1dHW
		VdbY11zX4Nhk2OjZbNnx2nba+9uA3AXcit0Q3ZbeHN6i3ynfr+A24L3hROHM
		4lPi2+Nj4+vkc+T85YTmDeaW5x/nqegy6LzpRunQ6lvq5etw6/vshu0R7Zzu
		KO6070DvzPBY8OXxcvH/8ozzGfOn9DT0wvVQ9d72bfb794r4Gfio+Tj5x/pX
		+uf7d/wH/Jj9Kf26/kv+3P9t///SJCUmJ1okY2xhc3NuYW1lWCRjbGFzc2Vz
		XE5TQ29sb3JTcGFjZaIoKVxOU0NvbG9yU3BhY2VYTlNPYmplY3TSJCUrLFdO
		U0NvbG9yoisp0hYuLzBZTlMuc3RyaW5ngAhfECQ1MzBBRkVGOS00QTMxLTRB
		QjQtQUI3MC1GNzUxQTMxRUZDMDfSJCUyM18QD05TTXV0YWJsZVN0cmluZ6My
		NClYTlNTdHJpbmcACAARABoAJAApADIANwBJAFAAVQBbAGYAaABqAGwAdgB8
		AIUAkACdAKMAsADFAMwA2gEGAQgBCgEMARMBGAEeASABIgEkDXANdQ2ADYkN
		lg2ZDaYNrw20DbwNvw3EDc4N0A33DfwODg4SAAAAAAAAAgEAAAAAAAAANQAA
		AAAAAAAAAAAAAAAADhs=
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFgyMCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuMiAwLjIgMC4yIDFPECcwLjE1
		MDU2MzEzNTcgMC4xNTA1NTg2MzU2IDAuMTUwNTYxMjEzNQAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkN0RGMjAxMkEtNDg2OC00MjRE
		LTg1MEQtRTEyQTlGNDdGMEU20iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFgzMCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuMyAwLjMgMC4zIDFPECYwLjIz
		NDA0Njk4MDcgMC4yMzQwMzk5OTIxIDAuMjM0MDQzOTI2ABABgAOABtMdHhYf
		ICFUTlNJRFVOU0lDQxAHgASABU8RDEgAAAxITGlubwIQAABtbnRyUkdCIFhZ
		WiAHzgACAAkABgAxAABhY3NwTVNGVAAAAABJRUMgc1JHQgAAAAAAAAAAAAAA
		AAAA9tYAAQAAAADTLUhQICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAABFjcHJ0AAABUAAAADNkZXNjAAABhAAAAGx3
		dHB0AAAB8AAAABRia3B0AAACBAAAABRyWFlaAAACGAAAABRnWFlaAAACLAAA
		ABRiWFlaAAACQAAAABRkbW5kAAACVAAAAHBkbWRkAAACxAAAAIh2dWVkAAAD
		TAAAAIZ2aWV3AAAD1AAAACRsdW1pAAAD+AAAABRtZWFzAAAEDAAAACR0ZWNo
		AAAEMAAAAAxyVFJDAAAEPAAACAxnVFJDAAAEPAAACAxiVFJDAAAEPAAACAx0
		ZXh0AAAAAENvcHlyaWdodCAoYykgMTk5OCBIZXdsZXR0LVBhY2thcmQgQ29t
		cGFueQAAZGVzYwAAAAAAAAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAA
		ABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAPNRAAEAAAABFsxY
		WVogAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAABvogAAOPUAAAOQWFlaIAAA
		AAAAAGKZAAC3hQAAGNpYWVogAAAAAAAAJKAAAA+EAAC2z2Rlc2MAAAAAAAAA
		FklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAFklFQyBodHRwOi8v
		d3d3LmllYy5jaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAABkZXNjAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQg
		UkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAC5JRUMgNjE5NjYt
		Mi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAsUmVmZXJlbmNlIFZpZXdpbmcgQ29u
		ZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAALFJlZmVyZW5jZSBW
		aWV3aW5nIENvbmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAHZpZXcAAAAAABOk/gAUXy4AEM8UAAPtzAAEEwsAA1ye
		AAAAAVhZWiAAAAAAAEwJVgBQAAAAVx/nbWVhcwAAAAAAAAABAAAAAAAAAAAA
		AAAAAAAAAAAAAo8AAAACc2lnIAAAAABDUlQgY3VydgAAAAAAAAQAAAAABQAK
		AA8AFAAZAB4AIwAoAC0AMgA3ADsAQABFAEoATwBUAFkAXgBjAGgAbQByAHcA
		fACBAIYAiwCQAJUAmgCfAKQAqQCuALIAtwC8AMEAxgDLANAA1QDbAOAA5QDr
		APAA9gD7AQEBBwENARMBGQEfASUBKwEyATgBPgFFAUwBUgFZAWABZwFuAXUB
		fAGDAYsBkgGaAaEBqQGxAbkBwQHJAdEB2QHhAekB8gH6AgMCDAIUAh0CJgIv
		AjgCQQJLAlQCXQJnAnECegKEAo4CmAKiAqwCtgLBAssC1QLgAusC9QMAAwsD
		FgMhAy0DOANDA08DWgNmA3IDfgOKA5YDogOuA7oDxwPTA+AD7AP5BAYEEwQg
		BC0EOwRIBFUEYwRxBH4EjASaBKgEtgTEBNME4QTwBP4FDQUcBSsFOgVJBVgF
		ZwV3BYYFlgWmBbUFxQXVBeUF9gYGBhYGJwY3BkgGWQZqBnsGjAadBq8GwAbR
		BuMG9QcHBxkHKwc9B08HYQd0B4YHmQesB78H0gflB/gICwgfCDIIRghaCG4I
		ggiWCKoIvgjSCOcI+wkQCSUJOglPCWQJeQmPCaQJugnPCeUJ+woRCicKPQpU
		CmoKgQqYCq4KxQrcCvMLCwsiCzkLUQtpC4ALmAuwC8gL4Qv5DBIMKgxDDFwM
		dQyODKcMwAzZDPMNDQ0mDUANWg10DY4NqQ3DDd4N+A4TDi4OSQ5kDn8Omw62
		DtIO7g8JDyUPQQ9eD3oPlg+zD88P7BAJECYQQxBhEH4QmxC5ENcQ9RETETER
		TxFtEYwRqhHJEegSBxImEkUSZBKEEqMSwxLjEwMTIxNDE2MTgxOkE8UT5RQG
		FCcUSRRqFIsUrRTOFPAVEhU0FVYVeBWbFb0V4BYDFiYWSRZsFo8WshbWFvoX
		HRdBF2UXiReuF9IX9xgbGEAYZRiKGK8Y1Rj6GSAZRRlrGZEZtxndGgQaKhpR
		GncanhrFGuwbFBs7G2MbihuyG9ocAhwqHFIcexyjHMwc9R0eHUcdcB2ZHcMd
		7B4WHkAeah6UHr4e6R8THz4faR+UH78f6iAVIEEgbCCYIMQg8CEcIUghdSGh
		Ic4h+yInIlUigiKvIt0jCiM4I2YjlCPCI/AkHyRNJHwkqyTaJQklOCVoJZcl
		xyX3JicmVyaHJrcm6CcYJ0kneierJ9woDSg/KHEooijUKQYpOClrKZ0p0CoC
		KjUqaCqbKs8rAis2K2krnSvRLAUsOSxuLKIs1y0MLUEtdi2rLeEuFi5MLoIu
		ty7uLyQvWi+RL8cv/jA1MGwwpDDbMRIxSjGCMbox8jIqMmMymzLUMw0zRjN/
		M7gz8TQrNGU0njTYNRM1TTWHNcI1/TY3NnI2rjbpNyQ3YDecN9c4FDhQOIw4
		yDkFOUI5fzm8Ofk6Njp0OrI67zstO2s7qjvoPCc8ZTykPOM9Ij1hPaE94D4g
		PmA+oD7gPyE/YT+iP+JAI0BkQKZA50EpQWpBrEHuQjBCckK1QvdDOkN9Q8BE
		A0RHRIpEzkUSRVVFmkXeRiJGZ0arRvBHNUd7R8BIBUhLSJFI10kdSWNJqUnw
		SjdKfUrESwxLU0uaS+JMKkxyTLpNAk1KTZNN3E4lTm5Ot08AT0lPk0/dUCdQ
		cVC7UQZRUFGbUeZSMVJ8UsdTE1NfU6pT9lRCVI9U21UoVXVVwlYPVlxWqVb3
		V0RXklfgWC9YfVjLWRpZaVm4WgdaVlqmWvVbRVuVW+VcNVyGXNZdJ114Xcle
		Gl5sXr1fD19hX7NgBWBXYKpg/GFPYaJh9WJJYpxi8GNDY5dj62RAZJRk6WU9
		ZZJl52Y9ZpJm6Gc9Z5Nn6Wg/aJZo7GlDaZpp8WpIap9q92tPa6dr/2xXbK9t
		CG1gbbluEm5rbsRvHm94b9FwK3CGcOBxOnGVcfByS3KmcwFzXXO4dBR0cHTM
		dSh1hXXhdj52m3b4d1Z3s3gReG54zHkqeYl553pGeqV7BHtje8J8IXyBfOF9
		QX2hfgF+Yn7CfyN/hH/lgEeAqIEKgWuBzYIwgpKC9INXg7qEHYSAhOOFR4Wr
		hg6GcobXhzuHn4gEiGmIzokziZmJ/opkisqLMIuWi/yMY4zKjTGNmI3/jmaO
		zo82j56QBpBukNaRP5GokhGSepLjk02TtpQglIqU9JVflcmWNJaflwqXdZfg
		mEyYuJkkmZCZ/JpomtWbQpuvnByciZz3nWSd0p5Anq6fHZ+Ln/qgaaDYoUeh
		tqImopajBqN2o+akVqTHpTilqaYapoum/adup+CoUqjEqTepqaocqo+rAqt1
		q+msXKzQrUStuK4trqGvFq+LsACwdbDqsWCx1rJLssKzOLOutCW0nLUTtYq2
		AbZ5tvC3aLfguFm40blKucK6O7q1uy67p7whvJu9Fb2Pvgq+hL7/v3q/9cBw
		wOzBZ8Hjwl/C28NYw9TEUcTOxUvFyMZGxsPHQce/yD3IvMk6ybnKOMq3yzbL
		tsw1zLXNNc21zjbOts83z7jQOdC60TzRvtI/0sHTRNPG1EnUy9VO1dHWVdbY
		11zX4Nhk2OjZbNnx2nba+9uA3AXcit0Q3ZbeHN6i3ynfr+A24L3hROHM4lPi
		2+Nj4+vkc+T85YTmDeaW5x/nqegy6LzpRunQ6lvq5etw6/vshu0R7ZzuKO60
		70DvzPBY8OXxcvH/8ozzGfOn9DT0wvVQ9d72bfb794r4Gfio+Tj5x/pX+uf7
		d/wH/Jj9Kf26/kv+3P9t///SJCUmJ1okY2xhc3NuYW1lWCRjbGFzc2VzXE5T
		Q29sb3JTcGFjZaIoKVxOU0NvbG9yU3BhY2VYTlNPYmplY3TSJCUrLFdOU0Nv
		bG9yoisp0hYuLzBZTlMuc3RyaW5ngAhfECQ1MkExRjhBNi03NDhFLTRDNTkt
		QkUyMC1DQzRGMjAzMTcwQ0bSJCUyM18QD05TTXV0YWJsZVN0cmluZ6MyNClY
		TlNTdHJpbmcACAARABoAJAApADIANwBJAFAAVQBbAGYAaABqAGwAdgB8AIUA
		kACdAKMAsADFAMwA2gEDAQUBBwEJARABFQEbAR0BHwEhDW0Ncg19DYYNkw2W
		DaMNrA2xDbkNvA3BDcsNzQ30DfkOCw4PAAAAAAAAAgEAAAAAAAAANQAAAAAA
		AAAAAAAAAAAADhg=
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg0MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuNCAwLjQgMC40IDFPECcwLjMy
		NTg0Njk3MDEgMC4zMjU4MzcxOTQ5IDAuMzI1ODQyNzM4MgAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkNTYyRkM5QzktQ0UzOS00MjQy
		LTlBRDAtRTcyRTdCOTVCNkRE0iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg1MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuNSAwLjUgMC41IDFPECcwLjQy
		NDY3NDIxMjkgMC40MjQ2NjE1MTcxIDAuNDI0NjY4NjY5NwAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkMTAyNDg5RDEtRTNCRC00RTRD
		LUEwMTItODEwNTNCMzZDMjFB0iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg2MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuNiAwLjYgMC42IDFPECcwLjUy
		OTY0NzU4ODcgMC41Mjk2MzE3MzM5IDAuNTI5NjQwNzM0MgAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkRUJFMzUzNUUtNzYzOC00RkI4
		LThEQUEtRTA5QjM3QUI5QkQx0iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg3MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuNyAwLjcgMC43IDFPECcwLjY0
		MDExOTY3MTggMC42NDAxMDA0NzkxIDAuNjQwMTExMzI3MgAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkQ0RDMkRFQkMtQzdFOC00OUU3
		LUFDMTMtQzdEODdGMDA1QzA10iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg4MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuOCAwLjggMC44IDFPECcwLjc1
		NTU5MDkxNTcgMC43NTU1NjgzODUxIDAuNzU1NTgxMTQwNQAQAYADgAbTHR4W
		HyAhVE5TSURVTlNJQ0MQB4AEgAVPEQxIAAAMSExpbm8CEAAAbW50clJHQiBY
		WVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAA
		AAAAAPbWAAEAAAAA0y1IUCAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAARY3BydAAAAVAAAAAzZGVzYwAAAYQAAABs
		d3RwdAAAAfAAAAAUYmtwdAAAAgQAAAAUclhZWgAAAhgAAAAUZ1hZWgAAAiwA
		AAAUYlhZWgAAAkAAAAAUZG1uZAAAAlQAAABwZG1kZAAAAsQAAACIdnVlZAAA
		A0wAAACGdmlldwAAA9QAAAAkbHVtaQAAA/gAAAAUbWVhcwAABAwAAAAkdGVj
		aAAABDAAAAAMclRSQwAABDwAAAgMZ1RSQwAABDwAAAgMYlRSQwAABDwAAAgM
		dGV4dAAAAABDb3B5cmlnaHQgKGMpIDE5OTggSGV3bGV0dC1QYWNrYXJkIENv
		bXBhbnkAAGRlc2MAAAAAAAAAEnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAA
		AAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAAARbM
		WFlaIAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAAb6IAADj1AAADkFhZWiAA
		AAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9kZXNjAAAAAAAA
		ABZJRUMgaHR0cDovL3d3dy5pZWMuY2gAAAAAAAAAAAAAABZJRUMgaHR0cDov
		L3d3dy5pZWMuY2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAuSUVDIDYxOTY2LTIuMSBEZWZhdWx0
		IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAAAAAuSUVDIDYxOTY2
		LTIuMSBEZWZhdWx0IFJHQiBjb2xvdXIgc3BhY2UgLSBzUkdCAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAGRlc2MAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENv
		bmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAACxSZWZlcmVuY2Ug
		Vmlld2luZyBDb25kaXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAB2aWV3AAAAAAATpP4AFF8uABDPFAAD7cwABBMLAANc
		ngAAAAFYWVogAAAAAABMCVYAUAAAAFcf521lYXMAAAAAAAAAAQAAAAAAAAAA
		AAAAAAAAAAAAAAKPAAAAAnNpZyAAAAAAQ1JUIGN1cnYAAAAAAAAEAAAAAAUA
		CgAPABQAGQAeACMAKAAtADIANwA7AEAARQBKAE8AVABZAF4AYwBoAG0AcgB3
		AHwAgQCGAIsAkACVAJoAnwCkAKkArgCyALcAvADBAMYAywDQANUA2wDgAOUA
		6wDwAPYA+wEBAQcBDQETARkBHwElASsBMgE4AT4BRQFMAVIBWQFgAWcBbgF1
		AXwBgwGLAZIBmgGhAakBsQG5AcEByQHRAdkB4QHpAfIB+gIDAgwCFAIdAiYC
		LwI4AkECSwJUAl0CZwJxAnoChAKOApgCogKsArYCwQLLAtUC4ALrAvUDAAML
		AxYDIQMtAzgDQwNPA1oDZgNyA34DigOWA6IDrgO6A8cD0wPgA+wD+QQGBBME
		IAQtBDsESARVBGMEcQR+BIwEmgSoBLYExATTBOEE8AT+BQ0FHAUrBToFSQVY
		BWcFdwWGBZYFpgW1BcUF1QXlBfYGBgYWBicGNwZIBlkGagZ7BowGnQavBsAG
		0QbjBvUHBwcZBysHPQdPB2EHdAeGB5kHrAe/B9IH5Qf4CAsIHwgyCEYIWghu
		CIIIlgiqCL4I0gjnCPsJEAklCToJTwlkCXkJjwmkCboJzwnlCfsKEQonCj0K
		VApqCoEKmAquCsUK3ArzCwsLIgs5C1ELaQuAC5gLsAvIC+EL+QwSDCoMQwxc
		DHUMjgynDMAM2QzzDQ0NJg1ADVoNdA2ODakNww3eDfgOEw4uDkkOZA5/DpsO
		tg7SDu4PCQ8lD0EPXg96D5YPsw/PD+wQCRAmEEMQYRB+EJsQuRDXEPURExEx
		EU8RbRGMEaoRyRHoEgcSJhJFEmQShBKjEsMS4xMDEyMTQxNjE4MTpBPFE+UU
		BhQnFEkUahSLFK0UzhTwFRIVNBVWFXgVmxW9FeAWAxYmFkkWbBaPFrIW1hb6
		Fx0XQRdlF4kXrhfSF/cYGxhAGGUYihivGNUY+hkgGUUZaxmRGbcZ3RoEGioa
		URp3Gp4axRrsGxQbOxtjG4obshvaHAIcKhxSHHscoxzMHPUdHh1HHXAdmR3D
		HeweFh5AHmoelB6+HukfEx8+H2kflB+/H+ogFSBBIGwgmCDEIPAhHCFIIXUh
		oSHOIfsiJyJVIoIiryLdIwojOCNmI5QjwiPwJB8kTSR8JKsk2iUJJTglaCWX
		Jccl9yYnJlcmhya3JugnGCdJJ3onqyfcKA0oPyhxKKIo1CkGKTgpaymdKdAq
		Aio1KmgqmyrPKwIrNitpK50r0SwFLDksbiyiLNctDC1BLXYtqy3hLhYuTC6C
		Lrcu7i8kL1ovkS/HL/4wNTBsMKQw2zESMUoxgjG6MfIyKjJjMpsy1DMNM0Yz
		fzO4M/E0KzRlNJ402DUTNU01hzXCNf02NzZyNq426TckN2A3nDfXOBQ4UDiM
		OMg5BTlCOX85vDn5OjY6dDqyOu87LTtrO6o76DwnPGU8pDzjPSI9YT2hPeA+
		ID5gPqA+4D8hP2E/oj/iQCNAZECmQOdBKUFqQaxB7kIwQnJCtUL3QzpDfUPA
		RANER0SKRM5FEkVVRZpF3kYiRmdGq0bwRzVHe0fASAVIS0iRSNdJHUljSalJ
		8Eo3Sn1KxEsMS1NLmkviTCpMcky6TQJNSk2TTdxOJU5uTrdPAE9JT5NP3VAn
		UHFQu1EGUVBRm1HmUjFSfFLHUxNTX1OqU/ZUQlSPVNtVKFV1VcJWD1ZcVqlW
		91dEV5JX4FgvWH1Yy1kaWWlZuFoHWlZaplr1W0VblVvlXDVchlzWXSddeF3J
		XhpebF69Xw9fYV+zYAVgV2CqYPxhT2GiYfViSWKcYvBjQ2OXY+tkQGSUZOll
		PWWSZedmPWaSZuhnPWeTZ+loP2iWaOxpQ2maafFqSGqfavdrT2una/9sV2yv
		bQhtYG25bhJua27Ebx5veG/RcCtwhnDgcTpxlXHwcktypnMBc11zuHQUdHB0
		zHUodYV14XY+dpt2+HdWd7N4EXhueMx5KnmJeed6RnqlewR7Y3vCfCF8gXzh
		fUF9oX4BfmJ+wn8jf4R/5YBHgKiBCoFrgc2CMIKSgvSDV4O6hB2EgITjhUeF
		q4YOhnKG14c7h5+IBIhpiM6JM4mZif6KZIrKizCLlov8jGOMyo0xjZiN/45m
		js6PNo+ekAaQbpDWkT+RqJIRknqS45NNk7aUIJSKlPSVX5XJljSWn5cKl3WX
		4JhMmLiZJJmQmfyaaJrVm0Kbr5wcnImc951kndKeQJ6unx2fi5/6oGmg2KFH
		obaiJqKWowajdqPmpFakx6U4pammGqaLpv2nbqfgqFKoxKk3qamqHKqPqwKr
		davprFys0K1ErbiuLa6hrxavi7AAsHWw6rFgsdayS7LCszizrrQltJy1E7WK
		tgG2ebbwt2i34LhZuNG5SrnCuju6tbsuu6e8IbybvRW9j74KvoS+/796v/XA
		cMDswWfB48JfwtvDWMPUxFHEzsVLxcjGRsbDx0HHv8g9yLzJOsm5yjjKt8s2
		y7bMNcy1zTXNtc42zrbPN8+40DnQutE80b7SP9LB00TTxtRJ1MvVTtXR1lXW
		2Ndc1+DYZNjo2WzZ8dp22vvbgNwF3IrdEN2W3hzeot8p36/gNuC94UThzOJT
		4tvjY+Pr5HPk/OWE5g3mlucf56noMui86Ubp0Opb6uXrcOv77IbtEe2c7iju
		tO9A78zwWPDl8XLx//KM8xnzp/Q09ML1UPXe9m32+/eK+Bn4qPk4+cf6V/rn
		+3f8B/yY/Sn9uv5L/tz/bf//0iQlJidaJGNsYXNzbmFtZVgkY2xhc3Nlc1xO
		U0NvbG9yU3BhY2WiKClcTlNDb2xvclNwYWNlWE5TT2JqZWN00iQlKyxXTlND
		b2xvcqIrKdIWLi8wWU5TLnN0cmluZ4AIXxAkNDM0NzkxQUEtQUU0RC00RjQw
		LTlEMDYtNkUxRjJCREQ2REU50iQlMjNfEA9OU011dGFibGVTdHJpbmejMjQp
		WE5TU3RyaW5nAAgAEQAaACQAKQAyADcASQBQAFUAWwBmAGgAagBsAHYAfACF
		AJAAnQCjALAAxQDMANoBBAEGAQgBCgERARYBHAEeASABIg1uDXMNfg2HDZQN
		lw2kDa0Nsg26Db0Nwg3MDc4N9Q36DgwOEAAAAAAAAAIBAAAAAAAAADUAAAAA
		AAAAAAAAAAAAAA4Z
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFg5MCUgR3JhedUS
		ExQVFhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJO
		U0N1c3RvbUNvbG9yU3BhY2VWJGNsYXNzTTAuOSAwLjkgMC45IDFPECUwLjg3
		NTY2MTg1IDAuODc1NjM1NTA0NyAwLjg3NTY1MDQ2NTUAEAGAA4AG0x0eFh8g
		IVROU0lEVU5TSUNDEAeABIAFTxEMSAAADEhMaW5vAhAAAG1udHJSR0IgWFla
		IAfOAAIACQAGADEAAGFjc3BNU0ZUAAAAAElFQyBzUkdCAAAAAAAAAAAAAAAA
		AAD21gABAAAAANMtSFAgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAEWNwcnQAAAFQAAAAM2Rlc2MAAAGEAAAAbHd0
		cHQAAAHwAAAAFGJrcHQAAAIEAAAAFHJYWVoAAAIYAAAAFGdYWVoAAAIsAAAA
		FGJYWVoAAAJAAAAAFGRtbmQAAAJUAAAAcGRtZGQAAALEAAAAiHZ1ZWQAAANM
		AAAAhnZpZXcAAAPUAAAAJGx1bWkAAAP4AAAAFG1lYXMAAAQMAAAAJHRlY2gA
		AAQwAAAADHJUUkMAAAQ8AAAIDGdUUkMAAAQ8AAAIDGJUUkMAAAQ8AAAIDHRl
		eHQAAAAAQ29weXJpZ2h0IChjKSAxOTk4IEhld2xldHQtUGFja2FyZCBDb21w
		YW55AABkZXNjAAAAAAAAABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAA
		EnNSR0IgSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYWVogAAAAAAAA81EAAQAAAAEWzFhZ
		WiAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAG+iAAA49QAAA5BYWVogAAAA
		AAAAYpkAALeFAAAY2lhZWiAAAAAAAAAkoAAAD4QAALbPZGVzYwAAAAAAAAAW
		SUVDIGh0dHA6Ly93d3cuaWVjLmNoAAAAAAAAAAAAAAAWSUVDIGh0dHA6Ly93
		d3cuaWVjLmNoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAGRlc2MAAAAAAAAALklFQyA2MTk2Ni0yLjEgRGVmYXVsdCBS
		R0IgY29sb3VyIHNwYWNlIC0gc1JHQgAAAAAAAAAAAAAALklFQyA2MTk2Ni0y
		LjEgRGVmYXVsdCBSR0IgY29sb3VyIHNwYWNlIC0gc1JHQgAAAAAAAAAAAAAA
		AAAAAAAAAAAAAABkZXNjAAAAAAAAACxSZWZlcmVuY2UgVmlld2luZyBDb25k
		aXRpb24gaW4gSUVDNjE5NjYtMi4xAAAAAAAAAAAAAAAsUmVmZXJlbmNlIFZp
		ZXdpbmcgQ29uZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAdmlldwAAAAAAE6T+ABRfLgAQzxQAA+3MAAQTCwADXJ4A
		AAABWFlaIAAAAAAATAlWAFAAAABXH+dtZWFzAAAAAAAAAAEAAAAAAAAAAAAA
		AAAAAAAAAAACjwAAAAJzaWcgAAAAAENSVCBjdXJ2AAAAAAAABAAAAAAFAAoA
		DwAUABkAHgAjACgALQAyADcAOwBAAEUASgBPAFQAWQBeAGMAaABtAHIAdwB8
		AIEAhgCLAJAAlQCaAJ8ApACpAK4AsgC3ALwAwQDGAMsA0ADVANsA4ADlAOsA
		8AD2APsBAQEHAQ0BEwEZAR8BJQErATIBOAE+AUUBTAFSAVkBYAFnAW4BdQF8
		AYMBiwGSAZoBoQGpAbEBuQHBAckB0QHZAeEB6QHyAfoCAwIMAhQCHQImAi8C
		OAJBAksCVAJdAmcCcQJ6AoQCjgKYAqICrAK2AsECywLVAuAC6wL1AwADCwMW
		AyEDLQM4A0MDTwNaA2YDcgN+A4oDlgOiA64DugPHA9MD4APsA/kEBgQTBCAE
		LQQ7BEgEVQRjBHEEfgSMBJoEqAS2BMQE0wThBPAE/gUNBRwFKwU6BUkFWAVn
		BXcFhgWWBaYFtQXFBdUF5QX2BgYGFgYnBjcGSAZZBmoGewaMBp0GrwbABtEG
		4wb1BwcHGQcrBz0HTwdhB3QHhgeZB6wHvwfSB+UH+AgLCB8IMghGCFoIbgiC
		CJYIqgi+CNII5wj7CRAJJQk6CU8JZAl5CY8JpAm6Cc8J5Qn7ChEKJwo9ClQK
		agqBCpgKrgrFCtwK8wsLCyILOQtRC2kLgAuYC7ALyAvhC/kMEgwqDEMMXAx1
		DI4MpwzADNkM8w0NDSYNQA1aDXQNjg2pDcMN3g34DhMOLg5JDmQOfw6bDrYO
		0g7uDwkPJQ9BD14Peg+WD7MPzw/sEAkQJhBDEGEQfhCbELkQ1xD1ERMRMRFP
		EW0RjBGqEckR6BIHEiYSRRJkEoQSoxLDEuMTAxMjE0MTYxODE6QTxRPlFAYU
		JxRJFGoUixStFM4U8BUSFTQVVhV4FZsVvRXgFgMWJhZJFmwWjxayFtYW+hcd
		F0EXZReJF64X0hf3GBsYQBhlGIoYrxjVGPoZIBlFGWsZkRm3Gd0aBBoqGlEa
		dxqeGsUa7BsUGzsbYxuKG7Ib2hwCHCocUhx7HKMczBz1HR4dRx1wHZkdwx3s
		HhYeQB5qHpQevh7pHxMfPh9pH5Qfvx/qIBUgQSBsIJggxCDwIRwhSCF1IaEh
		ziH7IiciVSKCIq8i3SMKIzgjZiOUI8Ij8CQfJE0kfCSrJNolCSU4JWgllyXH
		JfcmJyZXJocmtyboJxgnSSd6J6sn3CgNKD8ocSiiKNQpBik4KWspnSnQKgIq
		NSpoKpsqzysCKzYraSudK9EsBSw5LG4soizXLQwtQS12Last4S4WLkwugi63
		Lu4vJC9aL5Evxy/+MDUwbDCkMNsxEjFKMYIxujHyMioyYzKbMtQzDTNGM38z
		uDPxNCs0ZTSeNNg1EzVNNYc1wjX9Njc2cjauNuk3JDdgN5w31zgUOFA4jDjI
		OQU5Qjl/Obw5+To2OnQ6sjrvOy07azuqO+g8JzxlPKQ84z0iPWE9oT3gPiA+
		YD6gPuA/IT9hP6I/4kAjQGRApkDnQSlBakGsQe5CMEJyQrVC90M6Q31DwEQD
		REdEikTORRJFVUWaRd5GIkZnRqtG8Ec1R3tHwEgFSEtIkUjXSR1JY0mpSfBK
		N0p9SsRLDEtTS5pL4kwqTHJMuk0CTUpNk03cTiVObk63TwBPSU+TT91QJ1Bx
		ULtRBlFQUZtR5lIxUnxSx1MTU19TqlP2VEJUj1TbVShVdVXCVg9WXFapVvdX
		RFeSV+BYL1h9WMtZGllpWbhaB1pWWqZa9VtFW5Vb5Vw1XIZc1l0nXXhdyV4a
		XmxevV8PX2Ffs2AFYFdgqmD8YU9homH1YklinGLwY0Njl2PrZEBklGTpZT1l
		kmXnZj1mkmboZz1nk2fpaD9olmjsaUNpmmnxakhqn2r3a09rp2v/bFdsr20I
		bWBtuW4SbmtuxG8eb3hv0XArcIZw4HE6cZVx8HJLcqZzAXNdc7h0FHRwdMx1
		KHWFdeF2Pnabdvh3VnezeBF4bnjMeSp5iXnnekZ6pXsEe2N7wnwhfIF84X1B
		faF+AX5ifsJ/I3+Ef+WAR4CogQqBa4HNgjCCkoL0g1eDuoQdhICE44VHhauG
		DoZyhteHO4efiASIaYjOiTOJmYn+imSKyoswi5aL/IxjjMqNMY2Yjf+OZo7O
		jzaPnpAGkG6Q1pE/kaiSEZJ6kuOTTZO2lCCUipT0lV+VyZY0lp+XCpd1l+CY
		TJi4mSSZkJn8mmia1ZtCm6+cHJyJnPedZJ3SnkCerp8dn4uf+qBpoNihR6G2
		oiailqMGo3aj5qRWpMelOKWpphqmi6b9p26n4KhSqMSpN6mpqhyqj6sCq3Wr
		6axcrNCtRK24ri2uoa8Wr4uwALB1sOqxYLHWskuywrM4s660JbSctRO1irYB
		tnm28Ldot+C4WbjRuUq5wro7urW7LrunvCG8m70VvY++Cr6Evv+/er/1wHDA
		7MFnwePCX8Lbw1jD1MRRxM7FS8XIxkbGw8dBx7/IPci8yTrJuco4yrfLNsu2
		zDXMtc01zbXONs62zzfPuNA50LrRPNG+0j/SwdNE08bUSdTL1U7V0dZV1tjX
		XNfg2GTY6Nls2fHadtr724DcBdyK3RDdlt4c3qLfKd+v4DbgveFE4cziU+Lb
		42Pj6+Rz5PzlhOYN5pbnH+ep6DLovOlG6dDqW+rl63Dr++yG7RHtnO4o7rTv
		QO/M8Fjw5fFy8f/yjPMZ86f0NPTC9VD13vZt9vv3ivgZ+Kj5OPnH+lf65/t3
		/Af8mP0p/br+S/7c/23//9IkJSYnWiRjbGFzc25hbWVYJGNsYXNzZXNcTlND
		b2xvclNwYWNloigpXE5TQ29sb3JTcGFjZVhOU09iamVjdNIkJSssV05TQ29s
		b3KiKynSFi4vMFlOUy5zdHJpbmeACF8QJDM5NTMyNDdELTg2REUtNDU3RC1B
		QzNBLUVERUZCMUJDRjExMNIkJTIzXxAPTlNNdXRhYmxlU3RyaW5nozI0KVhO
		U1N0cmluZwAIABEAGgAkACkAMgA3AEkAUABVAFsAZgBoAGoAbAB2AHwAhQCQ
		AJ0AowCwAMUAzADaAQIBBAEGAQgBDwEUARoBHAEeASANbA1xDXwNhQ2SDZUN
		og2rDbANuA27DcANyg3MDfMN+A4KDg4AAAAAAAACAQAAAAAAAAA1AAAAAAAA
		AAAAAAAAAAAOFw==
		</data>
		<data>
		YnBsaXN0MDDUAQIDBAUGBw5YJHZlcnNpb25ZJGFyY2hpdmVyVCR0b3BYJG9i
		amVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctMICQoLDA1UbmFtZVVjb2xv
		clppZGVudGlmaWVygAGAAoAHqQ8QERwiIyotMVUkbnVsbFVXaGl0ZdUSExQV
		FhcYGRobXE5TQ29tcG9uZW50c1VOU1JHQlxOU0NvbG9yU3BhY2VfEBJOU0N1
		c3RvbUNvbG9yU3BhY2VWJGNsYXNzRzEgMSAxIDFPEBoxIDAuOTk5OTc0Mzcg
		MC45OTk5OTEyOTc3ABABgAOABtMdHhYfICFUTlNJRFVOU0lDQxAHgASABU8R
		DEgAAAxITGlubwIQAABtbnRyUkdCIFhZWiAHzgACAAkABgAxAABhY3NwTVNG
		VAAAAABJRUMgc1JHQgAAAAAAAAAAAAAAAAAA9tYAAQAAAADTLUhQICAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABFj
		cHJ0AAABUAAAADNkZXNjAAABhAAAAGx3dHB0AAAB8AAAABRia3B0AAACBAAA
		ABRyWFlaAAACGAAAABRnWFlaAAACLAAAABRiWFlaAAACQAAAABRkbW5kAAAC
		VAAAAHBkbWRkAAACxAAAAIh2dWVkAAADTAAAAIZ2aWV3AAAD1AAAACRsdW1p
		AAAD+AAAABRtZWFzAAAEDAAAACR0ZWNoAAAEMAAAAAxyVFJDAAAEPAAACAxn
		VFJDAAAEPAAACAxiVFJDAAAEPAAACAx0ZXh0AAAAAENvcHlyaWdodCAoYykg
		MTk5OCBIZXdsZXR0LVBhY2thcmQgQ29tcGFueQAAZGVzYwAAAAAAAAASc1JH
		QiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAABJzUkdCIElFQzYxOTY2LTIuMQAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
		AAAAWFlaIAAAAAAAAPNRAAEAAAABFsxYWVogAAAAAAAAAAAAAAAAAAAAAFhZ
		WiAAAAAAAABvogAAOPUAAAOQWFlaIAAAAAAAAGKZAAC3hQAAGNpYWVogAAAA
		AAAAJKAAAA+EAAC2z2Rlc2MAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5j
		aAAAAAAAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAA
		AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABkZXNjAAAAAAAA
		AC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNS
		R0IAAAAAAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91
		ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZGVzYwAAAAAA
		AAAsUmVmZXJlbmNlIFZpZXdpbmcgQ29uZGl0aW9uIGluIElFQzYxOTY2LTIu
		MQAAAAAAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENvbmRpdGlvbiBpbiBJ
		RUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHZpZXcAAAAA
		ABOk/gAUXy4AEM8UAAPtzAAEEwsAA1yeAAAAAVhZWiAAAAAAAEwJVgBQAAAA
		Vx/nbWVhcwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAo8AAAACc2lnIAAA
		AABDUlQgY3VydgAAAAAAAAQAAAAABQAKAA8AFAAZAB4AIwAoAC0AMgA3ADsA
		QABFAEoATwBUAFkAXgBjAGgAbQByAHcAfACBAIYAiwCQAJUAmgCfAKQAqQCu
		ALIAtwC8AMEAxgDLANAA1QDbAOAA5QDrAPAA9gD7AQEBBwENARMBGQEfASUB
		KwEyATgBPgFFAUwBUgFZAWABZwFuAXUBfAGDAYsBkgGaAaEBqQGxAbkBwQHJ
		AdEB2QHhAekB8gH6AgMCDAIUAh0CJgIvAjgCQQJLAlQCXQJnAnECegKEAo4C
		mAKiAqwCtgLBAssC1QLgAusC9QMAAwsDFgMhAy0DOANDA08DWgNmA3IDfgOK
		A5YDogOuA7oDxwPTA+AD7AP5BAYEEwQgBC0EOwRIBFUEYwRxBH4EjASaBKgE
		tgTEBNME4QTwBP4FDQUcBSsFOgVJBVgFZwV3BYYFlgWmBbUFxQXVBeUF9gYG
		BhYGJwY3BkgGWQZqBnsGjAadBq8GwAbRBuMG9QcHBxkHKwc9B08HYQd0B4YH
		mQesB78H0gflB/gICwgfCDIIRghaCG4IggiWCKoIvgjSCOcI+wkQCSUJOglP
		CWQJeQmPCaQJugnPCeUJ+woRCicKPQpUCmoKgQqYCq4KxQrcCvMLCwsiCzkL
		UQtpC4ALmAuwC8gL4Qv5DBIMKgxDDFwMdQyODKcMwAzZDPMNDQ0mDUANWg10
		DY4NqQ3DDd4N+A4TDi4OSQ5kDn8Omw62DtIO7g8JDyUPQQ9eD3oPlg+zD88P
		7BAJECYQQxBhEH4QmxC5ENcQ9RETETERTxFtEYwRqhHJEegSBxImEkUSZBKE
		EqMSwxLjEwMTIxNDE2MTgxOkE8UT5RQGFCcUSRRqFIsUrRTOFPAVEhU0FVYV
		eBWbFb0V4BYDFiYWSRZsFo8WshbWFvoXHRdBF2UXiReuF9IX9xgbGEAYZRiK
		GK8Y1Rj6GSAZRRlrGZEZtxndGgQaKhpRGncanhrFGuwbFBs7G2MbihuyG9oc
		AhwqHFIcexyjHMwc9R0eHUcdcB2ZHcMd7B4WHkAeah6UHr4e6R8THz4faR+U
		H78f6iAVIEEgbCCYIMQg8CEcIUghdSGhIc4h+yInIlUigiKvIt0jCiM4I2Yj
		lCPCI/AkHyRNJHwkqyTaJQklOCVoJZclxyX3JicmVyaHJrcm6CcYJ0kneier
		J9woDSg/KHEooijUKQYpOClrKZ0p0CoCKjUqaCqbKs8rAis2K2krnSvRLAUs
		OSxuLKIs1y0MLUEtdi2rLeEuFi5MLoIuty7uLyQvWi+RL8cv/jA1MGwwpDDb
		MRIxSjGCMbox8jIqMmMymzLUMw0zRjN/M7gz8TQrNGU0njTYNRM1TTWHNcI1
		/TY3NnI2rjbpNyQ3YDecN9c4FDhQOIw4yDkFOUI5fzm8Ofk6Njp0OrI67zst
		O2s7qjvoPCc8ZTykPOM9Ij1hPaE94D4gPmA+oD7gPyE/YT+iP+JAI0BkQKZA
		50EpQWpBrEHuQjBCckK1QvdDOkN9Q8BEA0RHRIpEzkUSRVVFmkXeRiJGZ0ar
		RvBHNUd7R8BIBUhLSJFI10kdSWNJqUnwSjdKfUrESwxLU0uaS+JMKkxyTLpN
		Ak1KTZNN3E4lTm5Ot08AT0lPk0/dUCdQcVC7UQZRUFGbUeZSMVJ8UsdTE1Nf
		U6pT9lRCVI9U21UoVXVVwlYPVlxWqVb3V0RXklfgWC9YfVjLWRpZaVm4Wgda
		VlqmWvVbRVuVW+VcNVyGXNZdJ114XcleGl5sXr1fD19hX7NgBWBXYKpg/GFP
		YaJh9WJJYpxi8GNDY5dj62RAZJRk6WU9ZZJl52Y9ZpJm6Gc9Z5Nn6Wg/aJZo
		7GlDaZpp8WpIap9q92tPa6dr/2xXbK9tCG1gbbluEm5rbsRvHm94b9FwK3CG
		cOBxOnGVcfByS3KmcwFzXXO4dBR0cHTMdSh1hXXhdj52m3b4d1Z3s3gReG54
		zHkqeYl553pGeqV7BHtje8J8IXyBfOF9QX2hfgF+Yn7CfyN/hH/lgEeAqIEK
		gWuBzYIwgpKC9INXg7qEHYSAhOOFR4Wrhg6GcobXhzuHn4gEiGmIzokziZmJ
		/opkisqLMIuWi/yMY4zKjTGNmI3/jmaOzo82j56QBpBukNaRP5GokhGSepLj
		k02TtpQglIqU9JVflcmWNJaflwqXdZfgmEyYuJkkmZCZ/JpomtWbQpuvnByc
		iZz3nWSd0p5Anq6fHZ+Ln/qgaaDYoUehtqImopajBqN2o+akVqTHpTilqaYa
		poum/adup+CoUqjEqTepqaocqo+rAqt1q+msXKzQrUStuK4trqGvFq+LsACw
		dbDqsWCx1rJLssKzOLOutCW0nLUTtYq2AbZ5tvC3aLfguFm40blKucK6O7q1
		uy67p7whvJu9Fb2Pvgq+hL7/v3q/9cBwwOzBZ8Hjwl/C28NYw9TEUcTOxUvF
		yMZGxsPHQce/yD3IvMk6ybnKOMq3yzbLtsw1zLXNNc21zjbOts83z7jQOdC6
		0TzRvtI/0sHTRNPG1EnUy9VO1dHWVdbY11zX4Nhk2OjZbNnx2nba+9uA3AXc
		it0Q3ZbeHN6i3ynfr+A24L3hROHM4lPi2+Nj4+vkc+T85YTmDeaW5x/nqegy
		6LzpRunQ6lvq5etw6/vshu0R7ZzuKO6070DvzPBY8OXxcvH/8ozzGfOn9DT0
		wvVQ9d72bfb794r4Gfio+Tj5x/pX+uf7d/wH/Jj9Kf26/kv+3P9t///SJCUm
		J1okY2xhc3NuYW1lWCRjbGFzc2VzXE5TQ29sb3JTcGFjZaIoKVxOU0NvbG9y
		U3BhY2VYTlNPYmplY3TSJCUrLFdOU0NvbG9yoisp0hYuLzBZTlMuc3RyaW5n
		gAhfECRFRUEyMUQ2Mi0zMEMxLTQwQTUtQjgyQy05MEUzOUYwQ0M0QUTSJCUy
		M18QD05TTXV0YWJsZVN0cmluZ6MyNClYTlNTdHJpbmcACAARABoAJAApADIA
		NwBJAFAAVQBbAGYAaABqAGwAdgB8AIIAjQCaAKAArQDCAMkA0QDuAPAA8gD0
		APsBAAEGAQgBCgEMDVgNXQ1oDXENfg2BDY4Nlw2cDaQNpw2sDbYNuA3fDeQN
		9g36AAAAAAAAAgEAAAAAAAAANQAAAAAAAAAAAAAAAAAADgM=
		</data>
	</array>
	<key>kCPKSelectionViewPreferredModeKey</key>
	<integer>0</integer>
	<key>kCPKSelectionViewShowHSBTextFieldsKey</key>
	<false/>
</dict>
</plist>
