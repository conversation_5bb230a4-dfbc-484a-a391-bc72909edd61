## description
support website google/facebook/apple login

## files
lib: src/libapp/oauth.coffee

## setup
facebookOauth:
  appId: 357776481094717
  apiSecret: 339ec1a8538ee2889dd0e8606fc7cb0a
  redirect: '/oauth/facebook' #https://www.realmaster.com
googleOauth:
  clientId: '344011320921-vh4gmos4t6rej56k2oa3brharpio1nfn.apps.googleusercontent.com'
  clientSecret: '9IE0XrIOOpA5ZmN0YWzyZXcM'
  redirect: '/oauth/google' #https://www.realmaster.com
  appleOauth:
    redirect_uri: '/oauth/apple'
appleOauth:
  client_id: "com.realmaster.web"
  team_id: "8A2DLASB37"
  key_id: "5XJFAC59B8"
  redirect_uri: "/oauth/apple" #https://www.realmaster.com
  scope: "name email"
  client_id_app: "com.realmaster"
  authKeyPath: "<%this.cfgPath%>/../keys/appleAuthKey.p8"

Add apple privateKey to cfgLocal/keys/appleAuthKey.p8
cp ./keys/appleAuthKey.p8 ../../rmcfg/keys/appleAuthKey.p8