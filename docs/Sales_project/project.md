### 如何使用project create banner ad 以及 banner 管理

# 1. 如何从project创建banner

1. 进入project 编辑页面，点击create banner ad
2. 进入banner管理页面（从mls首页，更多进入）
3. 编辑刚刚创建的banner


# 2. banner 广告字段的详细介绍
* 标为展示的字段仅影响显示


1. [行为]tgt(redirect url): 点击后跳转的url/页面，如果是app内跳转用“/”开头，不要http://realmaster开头，而且要确保页面能关闭。 如/1.5/mapSearch?mode=list&mapmode=projects&id=5e53fd03a4bb714aba2e12b2
2. [展示]img src: 展示的图片(如果是banner用640x180(高)的图片)
3. [行为]order: 数字，排序使用，越高越靠前
4. [行为]type: 影响展示位置，banner位于首页顶部，project位于首页中间(project 生成的广告类型不用填写tgt(1)，会被用pid overwrite，但是banner类型会直接使用tgt，因为不仅有project的广告)
5. [行为][展示]status: A=Active, U=Unactive, U即为不显示
6. [行为]prov:  广告所面向的用户的位置（基于用户首页选择的城市）
7. [行为]lang: 广告所面向的用户的语言（基于用户选择的语言）
8. [行为]cipOnly: 广告所面向的位置（基于用户使用app时的物理位置）勾选表明只对China ip显示
9. [行为]inApp: 点击后的跳转发生的位置，勾选表明在app内打开页面，反之使用外部浏览器(如果是project类型， inapp=false会用popup打开projec详细页面)
10. [行为]cOnly: 广告所面向的用户的role（基于用户是否认证），勾选表明client （和11,12冲突，只能选其一）
11. [行为]rOnly: 广告所面向的用户的role（基于用户是否认证），勾选表明realtor（和10,12冲突，只能选其一）
12. [行为]cAndR: 广告所面向的用户的role（基于用户是否认证），勾选表明client+realtor （和10,11冲突，只能选其一）
13. [行为]noend: 忽略，非针对banner/project广告
14. [行为]show homepage market/mls: 必填！广告展示的页面，mls首页(红色）或者RM market（绿色）首页
15. [行为]url(popup redirect): 忽略，非针对banner/project广告
16. [展示]tl: 忽略，非针对banner/project广告
17. [展示]desc: project 形容，如： $110万位于新兴潜力城镇Keswick的湖滨成熟大型社区，周边购物生活设施齐全，1分钟上高速404
18. [展示]desc_en: 非中文客户展示的desc（同17）
19. [展示]nm(popup name): 忽略，非针对banner/project广告
20. [展示]name_en(banner/project name): 非中文客户展示的name（同21）
21. [展示]name: project项目名 如：Simcoe Landing 豪华双车库独立屋
22. [展示]builder: project builder name
23. [展示]city: project所属城市