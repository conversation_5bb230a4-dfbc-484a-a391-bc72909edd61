<mxfile host="65bd71144e">
    <diagram id="lKHO16nb5Zx4hKFfgpot" name="Page-1">
        <mxGraphModel dx="864" dy="436" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-12" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="140" y="130" width="390" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-10" value="API" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="s5s492Y2SKMGhU0GuEYR-1" target="s5s492Y2SKMGhU0GuEYR-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-1" value="DB" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="140" y="570" width="520" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-9" value="API" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="s5s492Y2SKMGhU0GuEYR-3" target="s5s492Y2SKMGhU0GuEYR-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-3" value="DB Driver (mongo_db/sqlDb/ES) npm mongodb" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="140" y="470" width="520" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="s5s492Y2SKMGhU0GuEYR-4" target="s5s492Y2SKMGhU0GuEYR-5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-11" value="API" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="s5s492Y2SKMGhU0GuEYR-8" vertex="1" connectable="0">
                    <mxGeometry x="-0.1741" y="1" relative="1" as="geometry">
                        <mxPoint y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-4" value="Model (Object Oriented Abbstraction)&lt;br&gt;Property.findInput /User/Forum(forum/groupForum)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="140" y="370" width="520" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-5" value="Business Logic" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="260" width="350" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-6" value="Controller (GET/POST)&amp;nbsp;&lt;br&gt;appAuth/isAllowed" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="180" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-7" value="Presentation&lt;br&gt;VIEW/ckup/theme" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="344" y="180" width="166" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.771;entryY=-0.011;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="s5s492Y2SKMGhU0GuEYR-13" target="s5s492Y2SKMGhU0GuEYR-4" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="630" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-13" value="Batch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="570" y="150" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="s5s492Y2SKMGhU0GuEYR-16" value="Module Folder" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="140" y="140" width="130" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>