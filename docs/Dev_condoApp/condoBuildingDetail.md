### TO CONFIRM
difference with vip and normal user.

## 公寓building详情页面
### Building Info,
Col:condos
Summary
[TODO]Amenities: 
  amen, amen_extra in condos Collection. Need to add the accuracy (eg:5/10)
  weight 
  need to change to fixed fields 

### Market  Trends:
stat_rooms_annually
[TODO] condo group by rooms， bed，1+den， 2beds (need to add group by den) d
选了房间数，apply到所有的section？
stat_rooms_annually，stat_rooms_monthly
[TODO]histgram can not add, compare, consider change to fix step. camparable.


[x] lpPerSqft
[TODO] YoY Growth: lp年同比
[x] mfeePrSqft 
[TODO] mfeePrSqft Growth
[x]taxPrSqft
[TODO]taxPrSqft Growth

### condo和社区对比图
stat_rooms_annually，stat_rooms_monthly
[x] lp，lpr，mfee，tax 当年或者当月数据对比，uaddr compare cmty
[x] cntNew, cntSld,cntOff
[x] sldDom, offDom

### Community
same as discover

## 房源Detail
### Investment Analysis
stat_rooms_annually，stat_rooms_monthly
[x] lp, lpr by month
[TODO] YoY (statUname,当前年和上一年), 
[TODO] YoY 排名，在城市范围,cmty范围给condo 的YoY,进行排名
[TODO] YoY 计算: delta year/ last Year+delta year


### 估价
[] Est home value  - to compute
[] Est closing cost - to compute
[] Est Rent - to compute
[] Est Rent Dom (add dom est in evaluation, or use sldDom of statUname)
[] Est Rent yield = (Est Rent *12 - mortgage - tax -maint fee - dom*lpr/30 )/Est home value
[] Est size: 有提供时使用中间值，没有提供时参考历史数据
[TODO]?? @Fred 需求： 新房源，买过的房子，看现在的价值。 下市的，出租的？most recent viewed.
 20w properties * 2 sec?  5 day！,只针对新房源估价。
 变化触发计算，新上市，sold。new every day，all once a week. refer:if no change of comparable listings,skip evaluation

调整手动估价，选用权重最高的房源做自动估价，给出使用的房源，将周边近似但没有使用的房源（active）作为推荐房源。一周跑一次
Home Value ，记录mt，acurate，range，hist，isUseLp

### Building Info
[TODO] SAME as building info

## Community Detail 社区详情

### community
[x] avg lp, MoM, YoY, vc, (statUname)
[x] selling fast: sldDom? 热卖排名? sldDom <10 就是速卖。
[x] mos (statUname) mos, mos < 4, seller market, mos>6 buyer, other balance
[] Amenity locallogic, propdeail里加最近的地铁站的信息

### More Demogrphics
[x] Population
[x]Average Age
[x] Household average size (Average size of census families,Average household size 用哪个？)
[x] Renters (156）(Total - Private households by tenure - 25% sample data (156)) renter)
[x] Immigrants (97 Total - Admission category and applicant type for the immigrant population in private households who landed between 1980 and 2016 - 25% sample data (97))
[x] Condos （Apartment in a building that has five or more storeys，Apartment in a building that has fewer than five storeys）
[x] College/university Education
[x] Avg Price (statUname)
[x] Low Income （56）(Total - Low-income status in 2015 for the population in private households）
[x] Not in Labour Force ( may use Unemployment rate)
[x] Single （Private households by household size， 1person）
[x] Household with children （With children in a census family ）


construction
1643. Total - Occupied private dwellings by period of construction - 25% sample data (162)

occupation
1884. Total labour force population aged 15 years and over by occupation - National Occupational Classification (NOC) 2016 - 25% sample data (223)

housing
  41. Total - Occupied private dwellings by structural type of dwelling - 100% data (7)


Commute:
1930. Total - Main mode of commuting for the employed labour force aged 15 years and over in private households with a usual place of work or no fixed workplace address - 25% sample data (230)


## 行情

use Stat_monthly
for sale or for rent
[x] LP YOY of 1 year,同比，mos
[x] CAGR，复合增长率
CAGR = (Ending Value/Beginning Value)^(1/# of years)－1
[] 

### ??? housing price
[x] sp or lp avg, mom, yoy, 
[TODO] growth ranking (待计算) 
   YOY ranking / MOM ranking / absolute value ranking
  (lp,lpr,sp,mfee,tax,sldDom)

  do ranking for 
  [TODO] cmty in city, by type, by rooms.
  [TODO] uaddr in cmty, uaddr in city


### comparison Analystics
stat_type_rooms_annual 
stat_type_rooms_monthly
? stat_type_rooms_weekly (only for cmty and above level)

左列表
[x]lp avg, median
[TODO] ?@Fred. how to deal with den. group by beds with den (need to add), add extra br_plus or one fld. discuss with fred
[x] cntNew, sp,dom,
[TODO] 左边选房型和bdrms， 选是avg还是median price，右边是同样类型的 cntNew,cndSld, sldDom/offDom, only apply to comparison section, if move bdrms select to top,remove here.
[] when in cmty, toronto means price compare to price of toronto.


### Price Distribution
sp {range,value}
[TODO] @Fred., change algrithom of histgram, how to adjust step.
[] month is only previous month, natural calendar.
[TODO] 3 Months is for total
[TODO] 1 year, calendar year or add 12 month? 



### housing supply
[x] cntNew, cntSld, dom, mos,avl

### housing demand
[TODO] search ranking (vc vc/dom), fav，vc，
[TODO] sell fast, sldDom(?) discuss with Fred. <10 may not apply to all region. zolo for cmty,sp>lp, hottest cmty. overasking%*1 + sldDom% *2, ranking 
[X] Turn over rate (MOS)
[x] sp/lp
[x] lpr/lp or sp
[TODO] growth. 要在每个字段增加同比，环比数值，用来排序。 TODO, 


python 计算考虑分布。
