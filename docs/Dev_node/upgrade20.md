切换版本后需要 npm install coffeescript -g

前端打包出现的问题
Npm run build
Error:digital envelope routines::unsupported. node17不再支持
	export NODE_OPTIONS=--openssl-legacy-provider
	改package参数 export NODE_OPTIONS=--openssl-legacy-provider && (加上原有参数)

React 打包
Error: --openssl-legacy-provider is not allowed in NODE_OPTIONS. Node16
	echo $NODE_OPTIONS
	export NODE_OPTIONS=. 除 --openssl-legacy-provider以外的其他数据

./bin/brew_saves.sh
Error terser: command not found
	 npm install terser -g

./bin/brew_css.sh
Error:uglifycss: command not found
	npm install uglifycss -g
uglifycss 2018年后不再更新

Sass:build:app
error:node-sass: command not found
	npm install node-sass -g


./bin/brew_coffee.sh
	npm install --global @babel/core
