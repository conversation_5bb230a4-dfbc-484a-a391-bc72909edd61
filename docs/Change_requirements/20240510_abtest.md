# 需求 [abtest]


## 反馈

1. allen提出首次下载app后，想要根据是否跳转登录页面做abtest

## 需求提出人:   allen

## 修改人：      liurui

## 提出日期:     2024-03-04

## 原因
之前未提供

## 解决办法
权限设置之后根据(随机跳转逻辑)跳转页面，且发送post记录用户跳转页面
新注册用户匹配uuid，对应修改状态

config.coffee
app_config:
  abTest:
    homepage:true/false #true是使用abtest，根据随机数分配跳转页面，false是原来的流程(setup 结束跳转登陆页)
    ratioOfA:0.1 #A对照组比例,默认为0.5

A对照组(使用原来的处理方式)，B测试组
data abtest collection
  新abtest记录状态的字段需要加key
  _id:uuid
  home:A/B  index key
  seed:随机数
  signUp: new Date()
  ts

随机跳转逻辑
生成随机3位小数 Math.random().toFixed(3)   <=0.5 A  > 0.5 B

## 确认日期: 2024-05-11

## 测试方式
3号服，进入首页(https://d3.realmaster.com/home/<USER>
local storage 修改initApp 为0
设置的跳转测试页面比例为A:B=1:9
