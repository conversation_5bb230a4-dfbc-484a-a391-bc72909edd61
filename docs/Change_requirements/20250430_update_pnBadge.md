# 需求 [update_pnBadge]

## 反馈

1. 需要在后台增加一个接收并保持pushNotify counter的接口,推送时候自动+1

## 需求提出人:    Fred

## 修改人：       liurui

## 提出日期:      2025-04-28

## 原因

1. 之前未提供

## 解决办法

1. model增加function，在pn发送之前使用 inc增加计数
   1. 修改文件调用的地方
      1. appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee 修改为使用model，发送pn
         1. 文件头部引入model pushNotifyModel = MODEL 'PushNotify'
         2. 330行  改为使用pushNotifyModel.send
   2. appweb/src/model/009_pushNotify.coffee
      1. 在send 中增加处理，发送pushNotify前使用pn搜索user表和pn_token表，如果存在pn，则更新pnBadge字段，$inc:1
2. 增加post，接收传入参数，更新对应pn的pnBadge值
   1. appweb/src/apps/80_sites/AppRM/user/settings.coffee:476,接收pnToken和cnt参数
      1. 用户已经登录，更新user表中的pnBadge
      2. 用户未登录，更新pn_token表中的pnBadge
3. 用户登录之后删除pn_token时，需要同步更新pnBadge
   1. appweb/src/model/010_user.coffee:3888 在：3894删除PnToken数据之前，先检查PnToken表中的对应pn有没有pnBadge
      1. 如果存在，set添加pnBadge，同步保存到user表中
4. 在pushNotify的时候，更新数据库，并获取最新的pnBadge数据，添加到notice中，一同推送
5. 处理pushNotify多个pn，每个pn对应不同的badge

## 是否需要补充UT

1. 需要添加UT

## 确认日期:    2025-04-30