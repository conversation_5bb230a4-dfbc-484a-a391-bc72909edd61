# 需求 [queue_no_return]

## 反馈

1. treb reso oh queue 没有返回结果时候，一直在重复执行相同的query queue

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-01-15

## 原因

1. trebRsoDownload.coffee getAndSaveResourcesAsync中，queue 没有返回结果时候，直接break，没有删除queue中的数据

## 解决办法

1. 修改trebRsoDownload.coffee getAndSaveResourcesAsync中，queue 没有返回结果时候，如果mode是MODE_QUEUE，则删除queue中的数据

## 确认日期:    2025-01

## online-step

1. 重启trebResoDownload.coffee
