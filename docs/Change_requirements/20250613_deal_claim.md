# 需求 [deal_claim]

## 反馈

1. 只能录入一个月内成交的。
<!-- 2. 一周内成交的，奖励2倍分数；两周内的，奖励1.5倍分数；超过的正常无奖励。 暂时不修改(tao)-->
3. 成交是指最终完成交易的，可以在入口处判断（treb是sold，bc是closed，依次类推）
4. 房源认领的房源地图
5. 增加房源deal failed提示，增加筛选





## 需求提出人:     Allen
## 修改人：       liurui

## 提出日期:      2025-04-29

## 原因

1. 之前未提供


## 解决办法

1. claim.coffee 修改gClaimRange限制为30天
<!-- 2. 增加 token 参数 -->
3. claim.coffee 增加post /propClaim/map 接口，返回已经claim的房源数据
   1. 支持选择agent
4. /1.5/map/notesMap?from=claim 支持 from = claim参数
   1. get ‘/1.5/saves/deals’ 右上角可以点击 MAP 跳转到 mapsearch页面 参考 appweb/src/appDev3/saves/properties.js 修改 appweb/src/appDev3/saves/deals.js
   2. from = claim，通过 post ‘/propClaim/map 获取已经claim的房源显示在地图上
5. 增加房源deal failed提示，增加筛选
   1. 在deals列表页增加deal failed提示
   2. 增加deal failed type筛选

## 影响范围

1. 房源claim及toke

## 是否需要补充UT

1. 需要补充UT


## 确认日期:    2025-06-13

## online-step

1. 重启server

上线后提示tao:
claim限制时间已经改为30天，由于只能基于当前第三方给回来的数据状态判断成交状态，并不知道以后交易状态是否会改变，所以增加了deal failed提示（type增加deal failed选项），对于已经claim的房源，如果deal failed需要claim admin手动删除claim信息，退回token，这部分可能需要定期检查