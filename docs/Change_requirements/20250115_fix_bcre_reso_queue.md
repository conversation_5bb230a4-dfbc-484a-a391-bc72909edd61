# 需求 [fix_bcre_reso_queue]

## 反馈

1. bcre reso office queue query 报错 {"error":{"code":400,"message":"Cannot perform operation with incompatible types"}}。 debug.fatal is not a function

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-01-16

## 原因

1. 使用$filter=OfficeKeyNumeric in ('2056','2679'), 没有改成int，导致报错
2. 应该使用debug.critical

## 解决办法

1. 修改bcreResoDownload.coffee downloadQueuedResources的buildFilter，增加判断，如果resourceName是office或者member，则使用int，不加引号
2. 修改bcreResoDownload.coffee 使用debug.critical
3. 同样修改trebResoDownload.coffee 使用debug.critical


## 确认日期:    2025-01

## online-step

1. 重启bcreResoDownload.coffee
2. 重启trebResoDownload.coffee
