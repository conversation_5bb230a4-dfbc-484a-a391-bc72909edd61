# 需求 [fix_laMerge]

## 反馈

1. 房源merge的时候，如果出现DDF->CAR->TRB 多次merge的情况,会出现la2信息丢失的情况
2. treb reso在mapping时会出现la只有Office name的情况

## 需求提出人:   Fred

## 修改人：      Lu<PERSON>iaowei

## 提出日期:     2025-01-06

## 原因

1. 房源merge逻辑存在问题,只有DDF merge 到其他board时会添加la2字段,其他board merge时不会添加la2字段
2. 源数据只有office name数据,没有其他信息

## 解决办法

1. 修改房源merge逻辑,添加la,la2字段的merge
   1. 如果房源存在有效la/la2字段,不进行merge
   2. 如果房源不存在有效la/la2字段,进行merge
2. 修改treb reso mapping逻辑,la如果只有name 不转换la字段
3. 因为前端对没有la/la2添加了处理,不添加batch修复历史数据,只处理后续merge房源数据

## 确认日期:    2025-01-07

## online-step

1. 重启watch&import
