# 需求 [fix_stAbbr]

## 反馈

1. tao反馈W8311740,W8252802,W8272258在地图检索时重复
2. fred反馈W8311740的ddf没有merge到treb房源,unit test应该cover这些情况的,DDF:3438 ENNISKILLEN CIRCLE,TREB:3438 Enniskillen Circ,两个后缀差一个c，但地址统一化，应该在merge前面的

## 需求提出人:   tao/fred

## 修改人：      luoxiaowei

## 提出日期:     2024-06-04

## 原因

1. 反馈1是因为DDF房源没有merge到TREB房源,没有merge是因为两个房源的uaddr不一致,DDF早于TREB房源进入properties
2. 反馈2是因为在做地址统一时,street abbreviation的常量定义覆盖面不全,现在做street abbr是根据CA_EN.coffee和CA.coffee定义的进行缩写,其中缺少了CIRC的缩写对应

## 解决办法

1. 补全street abbreviation的定义
2. 数据修复问题方案
   1. 增加batch对现有properties的uaddr进行修复
   2. 执行batch src/batch/prop/fix_propMerged.coffee  对房源进行重新merge

## 确认日期:   2024-06-05

## online-step

1. 重启watch&import
2. 执行batch->src/batch/geo/fix_street_abbr.coffee 修复properties的uaddr
3. 执行batch->src/batch/prop/fix_propMerged.coffee 对房源重新merge

## 2024-06-27 review反馈需要整理房源uaddr修改的影响

1. 以下collections需要进行处理
   1. condos
   2. showing
   3. user_watch_location
   4. evaluation
   5. prop_notes
   6. user_stream
