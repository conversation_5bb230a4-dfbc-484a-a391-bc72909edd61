# 需求 [bc_new_status]

## 反馈

1. BC将提供新的status与日期，需要支持显示以及对应检索、统计，支持前后一致
<!-- New Status
   Active Under Contract – This optional status will be used when a listing has an accepted offer with subjects on it.
   Pending – Replacing the current Sold status, this status will be applied to listings that have an accepted subject-free offer, or once all subjects are removed.
   Closed – Listings will automatically move to this status at the end of their completion date.
-->
<!-- New Date Fields
   Seller’s Acceptance Date – This will represent the contractual date that a offer was accepted and the status moves to “Pending”.
   Subject Removal Date – This will represent the date the subjects were removed.
   Closing Date – This will represent the completion date, which is also when the status moves to “Closed”.  This information will be populated on the day this change is made.
-->

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2024-04-18

## 方案一

1. 记录origStatus作为显示使用
2. Active Under Contract - 认为是Active，status为A,lst转为Auc(New查询时添加该类型)
3. Pending - 认为是sold，status为U， 转换lst为Pnd(Sld查询时添加该类型)
4. Closed - status为U,lst转为Cld(Sld查询时添加该类型)
5. Seller’s Acceptance Date - 记录acceD，前端添加展示
6. Subject Removal Date - 记录sremD,不替换sldd，优先级放后计算offD
7. Closing Date - 记录clD,不替换sldd，优先级放后计算offD
<!-- TODO:前端需要对新状态,日期fields展示进行修改 -->

## 确认日期:    2024-04-23
