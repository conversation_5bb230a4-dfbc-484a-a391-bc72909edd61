# 需求 [fix_rae_log_dup]

## 反馈

1.<PERSON> download E11000 duplicate key error collection: rni.mls_rae_logs index: id_1_hash_-1 dup key: { id: 225328871, hash: "81+N4HeZMuQtOz/Nqk2L1LCHe+o=" }

## 需求提出人:   Fred

## 修改人：     Maggie

## 提出日期:     2024-12-16

## 原因

1. Rae数据中出现了修改后，又修改回去，导致hash重复，unique:true报错
2. new：
    {
    id: 225328871,
    hash: '81+N4HeZMuQtOz/Nqk2L1LCHe+o=',
    sid: 'E4416051',
    mt: 2024-12-16T00:27:34.382Z,
    }
  已经有的log：
  {
    _id: ObjectId('675f56e15fa37f1c37bb9a5f'),
    id: 225328871,
    hash: 'l5qMBNBmXarcyrkT0Pr+0Wf8XMQ=',
    sid: 'E4416051',
    mt: ISODate('2024-12-15T22:15:54.189Z'),
  },
  {
    _id: ObjectId('675f535c5fa37f1c37bb9a5a'),
    id: 225328871,
    hash: '81+N4HeZMuQtOz/Nqk2L1LCHe+o=',
    sid: 'E4416051',
    mt: ISODate('2024-12-15T22:04:23.930Z'),
  },


## 解决办法

1. 删除RaeLogCol.createIndex { id:1, hash:-1 }的unique:true, 
   

## 确认日期:    2024-12-16

## online-step

1. 暂停raeDownload
   ```
   systemctl --user stop batch@raeDownload
   ```
2. 删除mls_rae_logs中的 id:1, hash:-1  index
   ```
   db.mls_rae_logs.dropIndex({id:1, hash:-1})
   ```
3. 重新启动raeDownload.coffee(可以在local.ini中添加[rae]token = ""，运行时不需要再加-t)
    ```
    ./start.sh -t batch -n raeDownload -cmd "lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee [-t token force]"
    ```
