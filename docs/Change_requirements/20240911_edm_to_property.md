# 需求 [edm_to_property]

## 反馈

1. 需要根据edm结构，增加数据进入到properties。

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-09-11

## 解决办法

1. 添加src/libapp/impMappingEdm.coffee文件用于处理edm数据从rni到properties的mapping
2. watch&import添加oreb数据支持,merge逻辑添加src:'EDM'的处理逻辑
4. elasticsearch mapping添加edm需要的字段
5. 房源检索/详情展示等添加edm的处理逻辑
6. 添加unit test cases

页面修改：
1. 添加Listing updated ts：Listing information last updated on October 6th, 2024 at 8:47pm EDT. 时间使用当地时间。
2. 在listing筛选的时候，要显示listing数目
3. 不能显示DOM，要改成Day on Website。这个应该从ts开始计算。
4. Listing Contract Date. 这个不显示。
5. 显示一下Source：REALTORS(R) Association of Edmonton




## 确认日期:    2024-09-12

## online-step[新config]

1. 重启服务器
2. 暂停raeDownload
   ```
   systemctl --user stop batch@raeDownload
   ```
3. 执行以下脚本修改rni数据的mt字段
   1. db.mls_rae_records.updateMany({}, [{$set:{mt:"$BridgeModificationTimestamp"}}])
4. 重启raeDownload
   ```
   sh start.sh -t batch -n raeDownload -cmd 'lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee -t token force'
   ```
5. 重启watchPropAndUpdateElasticSearch.coffee
   ```
    sh start.sh  -t batch -n watchES -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
   ```
6. 重启watch&import,对edm的房源全部导入到properties
   ```
   sh start.sh  -t batch -n watchAndImportToProperties lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model  -t edm -t deleteEvow -h 12000 init nogeo force debug
   ```
