# 需求 [addLog_watchImport]

## 反馈

1. 因为存在properties数据与rni数据不一致，无法定位问题，fred反馈需要在watch，import时分别添加log记录

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-05-15

## 解决办法

1. 分别在watch与import的地方记录log到数据库,再出现类似case时根据log进行定位分析原因
<!-- 记录在tmp.properties_import_log中，添加logs字段
  logs    array     watch/import log  限制长度200

  logs:[
    {lSrc:'evow', step:'watch', lsc:'', lp:'', ts:'', m:''},
    {lSrc:'idx', step:'import', lsc:'', lp:'', ts:'', m:''}
  ]
-->

## 确认日期 2025-05-20
