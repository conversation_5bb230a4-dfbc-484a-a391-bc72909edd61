# 需求 [fix_origUnt]

## 反馈

1. sales(张林Sophie)反馈E8225374房源详情中地址错误
2. fred反馈autocompleteGetNext返回的房源列表addr没有显示unt

## 需求提出人:    fred

## 修改人:       luoxiaowei

## 提出日期:     2024-04-13

## 原因

1. rni数据提供了错误的unt，在rni数据修正后import逻辑没有修改origUnt 导致房源addr显示了错误的unt信息
2. autocompleteGetNext返回的房源列表addr中的unt只判断unt或apt_num字段，没有添加origUnt的判断
<!-- rni数据
  {
    _id: ObjectId("661840eabf21a9f52841f294"),
    data: { Apt_num: '', Unit_num: '59' },
    id: 'E8225374',
    _mt: ISODate("2024-04-11T19:58:34.353Z")
  },
  {
    _id: ObjectId("6618256ebf21a9f52841a328"),
    data: { Apt_num: '59', Unit_num: '59' },
    id: 'E8225374',
    _mt: ISODate("2024-04-11T18:01:18.866Z")
  }
-->

## 解决办法

1. import在unset unt时同时更新origUnt(同时添加origCity,origAddr,origCmty判断),添加对应的unit test cases
2. 添加batch修复现有数据
3. autocompleteGetNext返回房源列表显示的addr添加origUnt判断

## 需求确认日期:  2024-04-15
