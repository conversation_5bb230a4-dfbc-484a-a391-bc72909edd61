# 需求 [app_individualCenter]

## 反馈

Allen提出个人中心更换图标，菜单显示顺序进行调整；把原先的 MY AD 放在wepage下方；把 Share Stats 放在 more 里等；
当用户退出（自己主动点注销logout/长时间不打开app被踢出），可以考虑区分，主动退出不做登录邮箱填充提示，系统退出再次登陆时，登录邮箱填充提示。
管理/经纪/用户 个人中心页面权限保持现有逻辑不变，（包括ad）。只是改图标和图标位置。

## 需求提出人:   Allen

## 修改人：      zhangman

## 提出日期:     2024-12-1

## 解决办法

针对登录邮箱填充提示的解决办法：
  1. 登录成功/打开app都会进入主页，将邮箱加密并且使用 RMSrv.setItemObj 存入storage, 
   recordEml: {
    eml: '邮箱',
    ts: '更新时间'
   }
  2. 自己主动点击 logout，将其从 storage中删除；
  3. 在一个设备上很长时间没有点开app，点击登录界面时才会给邮箱提示；


## 确认日期:    2024-12-20