# 需求 [fix_city_init]

## 反馈

1. user/fred 反馈Bowmanville, in Clarington Ontario. 没有出现在城市选择里
   

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-05-03

## 原因

_aggregateProvCityCmty很久没跑过
popular city是固定的，从src/lib/staticCityFields.coffee#DEFAULT_POP_CITY_LIST 加载的。
选择省份之后显示的城市是半固定的，是某次从properties生成的（>20 properties for community）

## 解决办法
1. +batch run this aggregate manually, 增加batch手动init，
2. bndCity去掉， 
3. use ES to search/aggragate
  


## 确认日期:     2024-05


