# 需求 [app_detailAddAcu]

## 反馈

1. 房源详情页面: bltYr1_m1,bltYr1_m1_acu 和 bltYr2_m1，ltYr2_m1_acu显示逻辑：如果bltYr1_m1和bltYr2_m1相等，但是acu不相等，则显示acu最小的；如果bltYr1_m1和bltYr2_m1不相等，则bltYr1_m1和bltYr2_m1显示范围，acu显示最小的。sp包含m1-9,有哪些显示哪些；
eg: bltYr1_m1: 2007,
  bltYr1_m1_acu: 8607,
  bltYr2_m1: 2007,
  bltYr2_m1_acu: 8583,
  sqft_m1: 640,
  sqft_m1_acu: 8841,
  rent_m1: 2300,
  rent_m1_acu: 8785,
  value_m1: 574000,
  value_m1_acu: 9584,
  sp_m1: 524000,
  sp_m1_acu: 9671,
  sp_m2: 521738,
  sp_m2_acu: 9674,
  sp_m9: 524083,
  sp_m9_acu: 9935

2024-09-11 提出增加了新的字符，包含：
  1. sp_m1 ~ sp_m19, sp_m1_acu ~ sp_m19_acu
  2. ml_mt 显示成YY-MM-DD HH:MI格式, title：Update
  3. bltYr1_m1 ~ bltYr1_m19，bltYr2_m1 ~ bltYr2_m19
  4. sqft_m1 ~ sqft_m19
  5. rent_m1 ~ rent_m19

eg: rent_m11: 2400,rent_m11_acu: 8786,rent_m12: 2500,rent_m11_acu: 8787
ml_mt: '2024-07-18T01:53:00.546Z', 
bltYr1_m5: 2008, bltYr1_m5_acu: 8608, bltYr2_m5: 2007, bltYr2_m5_acu: 8583,bltYr1_m19: 2008, bltYr1_m19_acu: 8582, bltYr2_m19: 2011, bltYr2_m19_acu: 8583,
sqft_m5: 645, sqft_m5_acu: 8845,sqft_m19: 645, sqft_m19_acu: 8845,

## 需求提出人:   fred

## 修改人：      zhangman

## 提出日期:     2024-07-15

## 解决办法

查询字段增加之前没有的字段，房源详情页修改原有的显示逻辑，增加新的显示需求

## 确认日期:    2024-07-17