# 需求 [web_menuRegistry]

重构 web 后台管理界面的侧边菜单栏

## 需求提出人:   Fred

## 修改人：      ZhangMan

## 提出日期:     2024-12-06

## 解决办法

1. 使用 Registry Pattern 进行重构：写一个 MenuRegistry class， 包含 registerMenu 和 getMenus等函数，在需要被添加的 Control 文件中，将菜单注册到菜单注册表中。根据传入的 user 获取哪些菜单是可以被该用户看到的。

2. 使用一个接口获取到可以显示的菜单栏，如果右侧内容是用 iframe 打开，则直接切换 iframe 的 url(给最终iframe需要显示的url)，否则打开一个新的窗口。

3. 如果直接在浏览器地址栏输入地址，不需要iframe打开的直接跳转页面，不带右边菜单栏，例如：'http://www.test:8080/en/buildings'直接打开全屏的builds编辑页面;如果需要iframe打开，则 redirect到/adminindex，传递需要打开的url；

## 确认日期:    2024-12-11