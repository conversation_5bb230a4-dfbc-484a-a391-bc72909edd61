# 需求 [app_notesAddFilter]

## 反馈

1. 在saved模块中笔记列表给nodeAdmin增加过滤选项。
2. 笔记统计表格修复名字存在空白问题

## 需求提出人:   Rain

## 修改人：      zhangman

## 提出日期:     2024-06-13

## 解决办法

1. 在saved模块中笔记列表给nodeAdmin增加过滤选项: 可以选择过滤掉只有图片的笔记；也可以选择名字（根据uid，aggregate propNotes中的数据，$addToSet:nm，例如：获取到的 nm:['wang','xiao wang']，则前端显示 wang,xiao wang），将选中人的笔记过滤掉。
2. 笔记统计表格之前只取了nm字段，现在增加nm_en,nm_zh; 优先顺序：nm，nm_en, nm_zh,有哪一个先显示哪一个

## 确认日期:    2024-06-19