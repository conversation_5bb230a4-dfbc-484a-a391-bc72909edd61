## 反馈
1. fred反馈dot html中不能使用JSON.parse('{{= JSON.stringify(it)}}')传参至js

## 需求提出人:   fred
## 修改人:       liurui

## 提出日期
1. 2024-05-07

## 原因
1. html代码在后端加载，如果参数出错会导致后台不断重启，风险很大
2. 之前写代码考虑不周

## 解决办法 [fix_html_data]
1. 需要传递的参数显示在div中，再在js里读取数据

## 需求确认日期
1. 2024-05-08

测试路径
app
/sys/proxyemail
/s/share
/1.5/prop/topup/createSession
  prop detail -> 'Top This Listing Now' -> 'pay'(如果vars.topup为true，需要改为false才能进入createSession页面)
/1.5/community/filter
/1.5/community/map
/1.5/landlord/owners
/1.5/notes?propId
/1.5/notes/editNotes?propId

web
/census/generateMapImage 需要有census2016数据
/cmty/cmtyStaticMap 需要登录有admin权限的账号
web prop list
web prop detail

