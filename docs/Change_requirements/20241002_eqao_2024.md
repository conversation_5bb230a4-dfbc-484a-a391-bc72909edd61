# 需求 [rollback_notes]

## 反馈

1. eqao 需要最新2024数据，网站改版，需要重新写puppeteer获取数据

## 需求提出人:   Tao，Rain

## 修改人：      Maggie

## 提出日期:     2024-10-2

## 原因

## 解决办法

1. 根据2024版网站，写/src/batch/puppeteer/src/school_eqao_2024.js，获取网页上所有的数据，province[isProvince:ture]，board[isBoard]，school 保存到school_log collection
   1. 第一次运行后，发现有83个地址没有正则出来，重新修改正则，
   2. 仍有13个address特殊, 手动修改addr,city,prov,country,zip:
      1. <PERSON> Bolstraat 1 , Canadian Section   , Brunssum  6445EE。    #879231
      2. #4 Tigris Road    , Cupecoy #879266
      3. El Tagamoa El Khames , 4th District , Zone # 6, Katameya , New Cairo #879304
      4. 36 Nam Long Shan Road    , Aberdeen #879134
      5. Via Cavour 13    , Lanciano Chieti 66034 #99500
      6. 153 Matsugo    , Tokorozawa Saitama 359-0027 #879061
      7. "Tai Fung Rd, Taikoo Shing #879037
      8. Longdong, No.23 Huamei Road , Guangzhou Guangdong Province 510520 #879258
      9. Alyce Heights Drive    , Alyce Glen Petit Valley X1X1X1 #879177
      10. 14# Xijia Datang, Nanjing No. 13 High School   , Nanjing Jiangsu Province 210008 #879482
      11. Cret-Taconnet 4    , Neuchatel  2002 #889172
      12. Hakim Juman Street , Chaguanas Republic Of Trinidad And Tobago 350000 #879380
      13. Yong Yang West Road    , Wuqing District Tianjin 301700 #879398
   
2. 修改src/batch/school_eqao/school_eqao.coffee:
   1. school eqao数据合并到school collection，添加[boardIdEqao：99500] 记录boardID
      "board" : "Private Boards - English (99500)"
3. 修改src/batch/school_eqao/school_board.coffee: 将log中province和board的数据，保存到school_board collection，根据 查找到之前的board数据，更新eqao信息，也添加[boardIdEqao：99500]
4. 修改src/batch/export/schoolEqaoSortAndVcStats.coffee
   1. 添加setProvAndBoardData 根据[boardIdEqao：99500]获取对应的board数据


Orbstack 中测试puppeteer需要使用HEADLESS=true，如果遇到error: Launching browser Error: Failed to launch the browser process!。需要在rocky9中安装：
sudo dnf install epel-release -y
sudo dnf install chromium -y


## 确认日期:    2024-10

## online-step

1. puppeteer获取的eqao2024数据，已经上传到rmlfs，1个prov，65个board，4426个school
2. 将数据导入mongo listing school_log中
3. 执行batch-> src/batch/school_eqao/school_eqao.coffee
   ```
   sh start.sh -t batch -n school_eqao -cmd "lib/batchBase.coffee batch/school_eqao/school_eqao.coffee"
   ```
4. 执行batch-> src/batch/school_eqao/school_board.coffee
   ```
   sh start.sh -t batch -n school_board -cmd "lib/batchBase.coffee batch/school_eqao/school_board.coffee"
   ```
5. 执行batch-> src/batch/export/schoolEqaoSortAndVcStats_2024.coffee
   ```
   sh start.sh -t batch -n schoolEqaoSortAndVcStats -cmd "lib/batchBase.coffee batch/export/schoolEqaoSortAndVcStats_2024.coffee -y 2024 -n 10000"
   ```
