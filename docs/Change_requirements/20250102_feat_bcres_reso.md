# 需求 [feat_bcres_reso]

## 反馈

1. bcre 使用新的bridge reso

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-01-02


## 解决办法
1. init 和 routine 并行, 根据输入参数init和routine来决定执行哪个。processHandler,importKey 根据mode来决定, 开始时先下载members, offices, lookups，使用initial mode。
2. openhouse更新，不会更新property的时间戳, 获得的数据只有OpenHouseStatus 是Active的。通过时间戳获取最新的openhouse后，进行updateResourceInProperties，如果property不存在，则加入property queue。
3. init filter BridgeModificationTimestamp，下载property。
4. queue 有property, openhouse, office, member，lookup。 api都支持$filter=ListingId in ('********', '********')格式。 office和member的要保存所有字段到merged。
5. 下载的prop要merge member和office， 
   member: ListAgentKeyNumeric,CoListAgentKeyNumeric,
   office: BuyerOfficeKeyNumeric,CoBuyerOfficeKeyNumeric,ListOfficeKeyNumeric,CoListOfficeKeyNumeric
6. downloadInitialResource 都使用downloadResourceReplication，lookup也有replication API
   


## 确认日期:    2025-01


## online-step
1. 停止旧的bcre download
   ```
   systemctl --user stop bcreDownload
   ```
2. 重启watchAndImport， 把-t bcre 改为 -t bcreReso
3. 启动新的bcre reso download[可能分开运行init和routine]
   ```
   ./start.sh -t batch -n bcreResoDownload -cmd 'lib/batchBase.coffee mlsImport/bcreReso/bcreResoDownload.coffee routine'

   ./start.sh -t batch -n bcreResoDownload -cmd 'lib/batchBase.coffee mlsImport/bcreReso/bcreResoDownload.coffee init'
   ```