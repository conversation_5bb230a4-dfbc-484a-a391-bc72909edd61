# 需求 [fix_setStatusU]

## 反馈

1. fred反馈房源N10426010 在evow是A，但idx的ids里面由于12小时以上没更新，给改成U了，已经不在用idx更新evow的status了，需要确认

## 需求提出人:   Fred

## 修改人：      Luoxiaowei

## 提出日期:     2024-11-18

## 原因

1. 房源N10426010在mls_treb_master_ids中的src为[ 'evow', 'idx' ],idx的ids里面由于12小时以上没更新通过setStatusU将房源status改为了U

## 解决办法

1. setStatusU当src是idx时,只更新src是idx，不更新src是evow的记录
2. 添加batch,统计修复mls_treb_master_record数据(可能需要和expire一起),dryrun 统计
<!--
与上次提交存在需求冲突
docs: docs/Change_requirements/20240607_fix_idx.md
commitId: 036318db1f76337045573ffa1b34d68c79662404
evow的过期可以通过batchTrebExp处理,改回idx的setStatusU不修改evow数据
-->

## 确认日期:    2024-11-18

## online-step

1. 需要batchTrebExp上线之后再上线
2. restart trebDownloadV2
3. 执行batch修复mls_treb_master_record数据(src/batch/prop/fix_setStatusU.coffee)
