# 需求 [ottawa_import]
详见email
The URL for the OREB Matrix VOW server is:   https://rets.oreb.mlxmatrix.com/RETS/login.ashx
If the information contained in these documents are not helpful, let me know and I will try and find you someone here that can help you.

 

RETS Connector is CoreLogic’s Windows RETS Client. Some basic documentation is available and is based on their windows client.  

https://retsconnector.com/RETSConnectorWebHelp/index.html

 

For RETS Connections documentation:

Please visit www.RESO.org

 

The link below has pre-compiled clients and documentation depending on the library chosen.

https://www.reso.org/rets-tools/

 

Developer Resources:

https://www.reso.org/developer-resources/

 



## 反馈
1. fred反馈ottawa 房源可以导入
2. fred: ottawa没有文档，要下载数据自己看和总结

   

## 需求提出人:    fred
## 修改人:       luoxiaowei/rain

## 提出日期
1. 2024-04-09fred提出反馈1


## 原因
1. 没有API说明，只有用户和密码和url
2. rets-client 有bug，使用own implementation of formidable+needle


## 解决办法
1. 增加nnb文件测试接口版本和返回内容，包括图片和数据格式
2. 反向生成说明文档，对比其他import raw数据
3. 见* mlsImport/oreb/orebDownload.coffee





## 需求确认日期
1. 2024-04-09 meeting确认 [反馈1](#反馈)

