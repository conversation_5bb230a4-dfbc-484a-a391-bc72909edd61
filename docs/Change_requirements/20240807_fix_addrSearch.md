# 需求 [fix_rahbSaletp]

## 反馈

1. 用户反馈32 Nottingham Dr未找到N9119039房源

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON><PERSON>

## 提出日期:     2024-08-07

## 原因

1. 在进行searchTxt查询时，没有按照匹配度顺序返回
2. 通过addr查询时会查询merged房源,但是返回结果非PropAdmin会对sid已经存在的merged房源进行filter
3. 房源在query时的limit与对查询结果的过滤limit设置的值不一致,详见src/model/properties.coffee#387,#753,#885
4. 房源在查询结束后会进行二次排序(ts年份倒序,sale优先于lease)

## 解决办法

1. 在id/addr查询时添加默认排序[排序规则](#修改后idaddr查询排序规则)
2. 房源查询时根据用户roles判断是否查询merged/del房源
   1. 可以查询merged房源的角色权限:propAdmin, inRealGroup, vip, share
   2. 可以查询del房源的角色权限:admin, inRealGroup
3. 修改房源查询结果limit的值,与query值统一
4. 添加与修复unit test

## 修改后id/addr查询排序规则

1. 匹配度排序
   1. addr查询按以下规则排序
      1. 从前往后match 最优先
      2. 前面多出\s
      3. 只match中间部分
   2. id查询按照id字段长度升序
2. status 升序 A优先,U在后面
3. saletp 降序 sale优先，lease最后
4. spcts倒序

## 确认日期:    2024-08-08
