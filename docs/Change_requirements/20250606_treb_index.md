# 需求 [treb_index]

## 反馈

1. 新listing 图片显示慢

## 需求提出人:    Fred

## 修改人：       Maggie

## 提出日期:      2025-06-05

## 原因

1. media queue 处理时间长，大约300s以内，新房源的图会有2-5min延迟。
media 保存时间长（30ms一个），删除掉之前旧的index ResourceRecordKey后，保存时间到1ms。
同时


## 解决办法
1. 清理旧的不用index：reso_treb_evow_media表 ResourceRecordKey，reso_treb_evow_merged表OriginatingSystemName
2. 将media保存 改成bulkWrite  速度快很多，现在并行4个，每个一整批queue处理只需要2-7s

## 影响范围

1. trebReso download media 处理

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-06

## online-step

1. 修改index：
```
// 删除 reso_treb_evow_media 中两个索引
db.reso_treb_evow_media.dropIndex("ResourceRecordKey_-1")
db.reso_treb_evow_media.dropIndex("ResourceRecordKey_1")

// 重新创建 RRK_1 索引并加上 background
db.reso_treb_evow_media.dropIndex("RRK_1")
db.reso_treb_evow_media.createIndex({ RRK: 1 }, { name: "RRK_1", background: true })

// 删除 reso_treb_evow_merged 中包含 OriginatingSystemName 的复合索引
db.reso_treb_evow_merged.dropIndex("mt_1_OriginatingSystemName_1")
db.reso_treb_evow_merged.dropIndex("mt_-1")

```
2. 重启trebResoDownload
