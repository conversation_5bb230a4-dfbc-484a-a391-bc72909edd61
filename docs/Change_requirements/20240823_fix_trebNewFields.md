# 需求 [fix_trebNewFields]

## 反馈

1. Fred反馈TRB房源新增字段，要看一下是否这些字段会进入properties，如果没有进入，要加一下，并在详细里面展示

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2024-08-13

## 原因

1. treb房源rni数据新增以下字段

-------------------------------------------------------------
| Display Name             | SQLName                        |
| ------------------------ | ------------------------------ |
| Channel Name             | channel_name                   |
| Development Charges Paid | development_charges_paid_multi |
| Docking Type             | docking_type_multi             |
| Fireplace Features       | fireplace_features_multi       |
| Number of Fireplaces     | fireplace_total                |
| Foundation Detail        | foundation_details_multi       |
| Interior Features        | interior_features_multi        |
| Lease To Own Equipment   | lease_to_own_equipment_multi   |
| Roof                     | roof_multi                     |
| Showing Requirements     | showing_requirements_multi     |
| Topography               | topography_multi               |
| Water View               | water_view_multi               |
| Waterfront Yes/No        | water_front_yn                 |

## 解决办法

1. 在treb watch&import时添加以上字段mapping
2. 前端添加以上字段显示,详细 Other下面
3. 添加batch对properties已有数据进行重新mapping
4. mapping规则如下

```javascript
{
    channel_name: 'ChannelName'
    development_charges_paid_multi: 'DevelopmentChargesPaid'
    docking_type_multi: 'DockingType'
    fireplace_features_multi: 'FireplaceFeatures'
    fireplaces_total: 'FireplacesTotal'
    foundation_details_multi: 'FoundationDetails'
    interior_features_multi: 'InteriorFeatures'
    lease_to_own_equipment_multi: 'LeaseToOwnEquipment'
    roof_multi: 'Roof'
    showing_requirements_multi: 'ShowingRequirements'
    topography_multi: 'Topography'
    water_view_multi: 'WaterView'
    water_front_yn: 'WaterFront'
}
```

## 确认日期:    2024-08

## online-step

1. 重启房源watch&import
2. 执行batch -> src/batch/prop/fix_trebNewFields.coffee
