# 需求 [fix_edmStat]

## 反馈

1. fred反馈edm周报的城市统计数字对不上，总和比子类还少
2. 如果需要修改程序增加一下： 房源变化量 个数+百分比，absorption rate

## 需求提出人:   Fred

## 修改人：      Luoxiaowei

## 提出日期:     2024-11-16

## 原因

1. 新的config没有添加propertiesBase,导致使用的mongo查询,没有使用es查询，mongo的房源统计只对总和添加了merged房源过滤,房源类型统计没有添加merged房源过滤

## 解决办法

1. 添加propertiesBase的config,使用es统计
2. mongo房源统计都添加房源merged过滤
3. 城市按月统计时添加房源变化量(上市房源-下市房源),absorption rate(售出数量/在市数量)
4. prop_mw 添加支持[-n 3]参数，可以修改number of weeks/months，原来的scriptMode使用仍然不变。 

## 确认日期:    2024-11-20

## online-step

1. 重新统计数据,执行src/stats/prop_mw.coffee
2. restart edmNotify
