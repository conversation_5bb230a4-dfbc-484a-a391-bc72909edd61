# 需求 [add_azure]

## 反馈

1. Fred/Rain反馈bing geoCoder会在6月份到期,需要改用 azure maps

## 需求提出人:    Fred

## 修改人：       liurui

## 提出日期:      2025-06-10

## Azure Maps 文档说明 `https://learn.microsoft.com/en-us/rest/api/maps/search/get-search-address?view=rest-maps-1.0&tabs=HTTP`

## 解决办法

1. 添加azure_maps的解析
2. 修改geoCoder的使用,bing改用azure
3. 在mapServer.coffee中添加getAzureAPIKey函数
4. rmconfig添加azure maps key

## Azure Maps 字段映射

| Azure Maps 字段               | 说明                                                            | 示例值                        |
|------------------------------|-----------------------------------------------------------------|------------------------------|
| `address.streetNumber`       | st_num, 门牌号                                                   | 18                           |
| `address.streetName`         | st, 街道名                                                       | Owen Drive                   |
| `address.municipality`       | city, 城市/地区(优先)                                             | Etobicoke                    |
| `address.localName`          | city, 地方名（与城市可能相同），municipality不存在时使用               | Etobicoke                    |
| `address.municipalitySubdivision` | subCity                                                    |                              |
| `address.countrySubdivision` | prov,  州/省缩写(优先)                                            | ON                           |
| `address.countrySubdivisionName` | prov, 州/省完整名                                             | Ontario                      |
| `address.countrySubdivisionCode` | prov, 州/省缩写，同上(以上没有时使用)                            | ON                           |
| `address.postalCode`         | zip_pre, 邮编前缀(完整邮编不存在时使用)                              | M8W                          |
| `address.extendedPostalCode` | zip, 完整邮编                                                     | M8W 1W7                      |
| `address.countryCode`        | cnty, 国家代码（优先）                                             | CA                           |
| `address.country`            | cnty, 国家名称                                                    | Canada                       |
| `address.freeformAddress`    | faddr, 格式化完整地址                                              | 18 Owen Drive, Etobicoke...  |
| `address.neighbourhood`      | cmty, 社区名称                                                    |                        |
| `position.lat`               | lat, 地址中心点坐标纬度                                             | 43.5973051                   |
| `position.lon`               | lng, 地址中心点坐标经度                                             | -79.5446145                  |
| `viewport.topLeftPoint` / `btmRightPoint` | nelat/nelng/swlat/swlng, 视图范围(需要计算转换得到)     |{"lat": 43.5973,"lon": -79.5446}|
| `type`                       | 结果类型, 优先使用：Point Address,通常为最佳匹配结果                   | Point Address                |
| `score`                      | geoq, 匹配评分（越接近1越准确,geo值需要*100）                         | 0.89                         |

## geoCoder各engine的数据统计, 由于here maps使用次数较多,考虑保留here maps

1. bing:  191579
2. here:  111254
3. google:  106341
4. mapbox:  73400

## 2025-06-17新需求，修改geocoder的默认使用顺序

### 各engine额度价格对比

| Engine  | 免费额度/月                | 价格/千次                         | 备注                           |
|---------|---------------------------|----------------------------------|--------------------------------|
| Bing    | 1w次                      | $4.50                            |                                |
| Azure   | 5000次                    | $4.50(Gen1定价)                  | Gen2定价为分层定价,需要联系销售    |
| Here    | 25w次(旧版),30000次(新版)   | $0.76(新版)                      |                                |
| Google  | 4w次($200计算得到)         | $5.00(10w次以内),$4.00(11-50w)   |                                |
| Mapbox  | 10w次                     | $0.75(10-50w),$0.60(50-100w)    |                                |

### 准确度对比

1. 在geoCache中获取了8条从17年到25年手动修改的记录,使用各个engine做geocoding，计算与记录中坐标的距离
   1. 有6次准确度(距离越近越准确)为: google > bing/azure > here > mapbox
   2. 有1次准确度(距离越近越准确)为: google > here > bing/azure > mapbox
   3. 有1次准确度(距离越近越准确)为: bing/azure > google > here > mapbox

### 结合费用与准确度,修改优先级为: Google, Here, Azure, Mapbox

## 影响范围

1. geoCoding取消bing maps,改用 azure maps

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-06

## online-step

1. 确认服务器可以访问到`https://atlas.microsoft.com`
2. 更新rmconfig(`https://github.com/real-rm/rmconfig/pull/22`),设置azure maps的key
3. 重启server
4. 重启watch&import

## Azure Geocoding 解析流程图

### 主流程 - azureGeocode 函数

```mermaid
graph TD
    A["azureGeocode({qaddr, lat, lng, zip, src, id})"] --> B{检查输入参数}
    B -->|qaddr和lat/lng都为空| C["返回错误: NO_QADDR"]
    B -->|参数正常| D["获取Azure API Key"]
    D --> E{API Key是否存在}
    E -->|不存在| F["返回错误: CHECK_MAPSERVER_CONFIG"]
    E -->|存在| G["初始化统计参数statParam"]
    G --> H{判断地理编码类型}
    H -->|有qaddr| I["设置isQaddr=true<br/>构建正向地理编码URL"]
    H -->|有lat/lng| J["构建反向地理编码URL"]
    I --> K["发送HTTP请求"]
    J --> K
    K --> L{网络请求是否成功}
    L -->|失败| M["记录错误并返回"]
    L -->|成功| N["解析JSON响应"]
    N --> O{JSON解析是否成功}
    O -->|失败| P["记录统计并返回错误"]
    O -->|成功| Q{HTTP状态是否OK}
    Q -->|不OK| R["返回HTTP错误"]
    Q -->|OK| S["初始化返回对象ret"]
    S --> T{是否有API错误}
    T -->|有错误| U["设置ret.err<br/>记录统计并返回"]
    T -->|无错误| V{根据请求类型提取结果}
    V -->|正向地理编码| W["从json.results[0]提取结果"]
    V -->|反向地理编码| X["从json.addresses[0]提取结果"]
    W --> Y{是否有结果}
    X --> Y
    Y -->|无结果| Z["设置NO_RESULTS错误<br/>记录统计并返回"]
    Y -->|有结果| AA["调用parseAzureResult解析"]
    AA --> BB["调用propAddress.formatProvAndCity"]
    BB --> CC{格式化是否成功}
    CC -->|失败| DD["记录错误统计并返回"]
    CC -->|成功| EE["调用adjustGeocodingQuality调整评分"]
    EE --> FF["保存到原始缓存saveToOriginalGeoCache"]
    FF --> GG["记录成功统计statParam.geoq"]
    GG --> HH["返回解析结果"]
```

### 解析流程 - parseAzureResult 函数

```mermaid
graph TD
    A["parseAzureResult(geoResult, ret)"] --> B{检查position字段}
    B -->|存在position| C{position类型判断}
    C -->|字符串类型| D["按逗号分割position<br/>解析lat,lng并转换为浮点数"]
    C -->|对象类型| E["直接提取position.lat和position.lon"]
    D --> F["设置ret.lat和ret.lng"]
    E --> F
    B -->|不存在position| G["解析质量评分"]
    F --> G
    G --> H{是否有score字段}
    H -->|有score| I["ret.q = score * 100"]
    H -->|无score| J["跳过评分设置"]
    I --> K["检查address字段"]
    J --> K
    K --> L{是否有address字段}
    L -->|无address| M["跳到视图边界解析"]
    L -->|有address| N["解析完整格式化地址"]
    N --> O{是否有freeformAddress}
    O -->|有| P["ret.faddr = freeformAddress<br/>ret.geoAddr = 第一部分"]
    O -->|无| Q["解析门牌号和街道"]
    P --> Q
    Q --> R["提取streetNumber -> ret.st_num<br/>提取streetName -> ret.st"]
    R --> S["解析邮编信息"]
    S --> T["优先extendedPostalCode<br/>否则使用postalCode"]
    T --> U["解析社区信息"]
    U --> V["neighbourhood -> ret.cmty"]
    V --> W["解析城市信息"]
    W --> X["优先municipality<br/>否则使用localName"]
    X --> Y["解析subCity"]
    Y --> Z["municipalitySubdivision -> ret.subCity"]
    Z --> AA["解析省份信息"]
    AA --> BB["按优先级选择:<br/>countrySubdivision<br/>countrySubdivisionName<br/>countrySubdivisionCode"]
    BB --> CC["解析国家信息"]
    CC --> DD["优先countryCode<br/>否则使用country"]
    DD --> EE{检查boundingBox字段}
    EE -->|有boundingBox| FF["分割northEast和southWest<br/>设置视图边界坐标"]
    EE -->|无boundingBox| GG["检查viewport字段"]
    GG --> M
    FF --> M
    M --> HH{是否有viewport字段}
    HH -->|有viewport| II["提取topLeftPoint和btmRightPoint"]
    II --> JJ["转换坐标:<br/>nelat=topLeft.lat<br/>nelng=btmRight.lon<br/>swlat=btmRight.lat<br/>swlng=topLeft.lon"]
    JJ --> KK["构建locality数组"]
    HH -->|无viewport| KK
    KK --> LL["添加city到locality"]
    LL --> MM["添加cmty到locality"]
    MM --> NN["添加address.localName到locality"]
    NN --> OO["解析完成"]
```

## 详细解析逻辑描述

### 1. azureGeocode 函数 (src/model/020_geoCoderLocal.coffee)

**函数定义：**
```coffeescript
azureGeocode = ({qaddr, lat, lng, zip, src, id}, cb) ->
```

**输入参数：**
- `qaddr` (String): 查询地址字符串
- `lat` (Number, 可选): 纬度坐标（用于反向地理编码）
- `lng` (Number, 可选): 经度坐标（用于反向地理编码）
- `zip` (String, 可选): 邮编信息
- `src` (String): 数据来源标识
- `id` (String): 属性ID，用于统计
- `cb` (Function): 回调函数，接收(err, result)参数

**输出结果：**
通过回调函数返回解析后的地理编码结果对象，包含以下字段：
- `src`: 'azure'
- `q`: 质量评分 (0-100)
- `qaddr`: 查询地址
- `lat/lng`: 坐标信息
- `faddr`: 格式化完整地址
- `st_num/st`: 门牌号/街道名
- `city/prov/cnty`: 城市/省份/国家
- `zip`: 邮编
- `err`: 错误信息(如有)

**内部逻辑：**

1. **参数验证**
   - 检查 qaddr 是否存在
   - 如果没有 qaddr 但有 lat/lng，返回不支持反向地理编码错误
   - 如果都没有，返回 NO_QADDR 错误

2. **构建API请求URL**
   ```coffeescript
   azureAPIKey = mapServer.getAzureAPIKey()
   url = "https://atlas.microsoft.com/search/address/json?api-version=1.0&subscription-key=#{azureAPIKey}&query=#{encodeURIComponent(qaddr)}&countrySet=CA"
   ```

3. **发送HTTP请求**
   - 使用 fetch 发送 GET 请求
   - 设置超时时间为 3000ms
   - 捕获网络错误并记录统计

4. **解析响应**
   - 检查 HTTP 状态码
   - 解析 JSON 响应
   - 验证响应格式和状态

5. **提取结果**
   - 从 `json.results[0]` 提取第一个结果
   - 如果没有结果，设置 NO_RESULTS 错误

6. **调用解析函数**
   - 调用 `parseAzureResult(r, ret)` 解析原始结果
   - 调用 `propAddress.formatProvAndCity(ret)` 格式化省市信息
   - 调用 `adjustGeocodingQuality(ret, qaddr, zip)` 调整质量评分

7. **保存和统计**
   - 调用 `saveToOriginalGeoCache('azure', ret, r)` 保存原始缓存
   - 调用 `recordGeocodingStats` 记录统计信息
   - 返回解析结果

### 2. parseAzureResult 函数 (src/libapp/geoCodeHelper.coffee)

**函数定义：**
```coffeescript
module.exports.parseAzureResult = parseAzureResult = (geoResult, ret) ->
```

**输入参数：**
- `geoResult` (Object): Azure Maps API 返回的原始结果对象
- `ret` (Object): 要填充的结果对象

**输出结果：**
修改 ret 对象，填充以下字段：
- `lat/lng`: 坐标信息
- `q`: 质量评分
- `faddr/geoAddr`: 格式化地址
- `st_num/st`: 门牌号和街道
- `city/prov/cnty`: 行政区划
- `zip`: 邮编
- `nelat/nelng/swlat/swlng`: 视图边界
- `locality`: 地区信息数组

**内部逻辑：**

1. **解析坐标信息**
   ```coffeescript
   if (position = geoResult.position)
     ret.lat = position.lat
     ret.lng = position.lon
   ```

2. **解析质量评分**
   ```coffeescript
   ret.q = geoResult.score * 100 if geoResult.score
   
   # 根据结果类型调整评分
   if geoResult.type is 'Point Address'
     ret.q = 100
   else if geoResult.type?.includes('Street')
     ret.q = 85 if ret.q < 85
   else if geoResult.type?.includes('Geography')
     ret.q = 75 if ret.q < 75
   ```

3. **解析地址组件**
   ```coffeescript
   if (address = geoResult.address)
     # 完整格式化地址
     if address.freeformAddress
       ret.faddr = address.freeformAddress
       ret.geoAddr = address.freeformAddress.split(',')[0]
     
     # 门牌号和街道
     ret.st_num = address.streetNumber if address.streetNumber
     ret.st = address.streetName if address.streetName
     
     # 邮编（优先使用完整邮编）
     ret.zip = address.extendedPostalCode or address.postalCode
     
     # 城市（优先使用localName）
     ret.city = address.municipality or address.localName
     
     # 省份（按优先级选择）
     ret.prov = address.countrySubdivision or 
                address.countrySubdivisionName or 
                address.countrySubdivisionCode
     
     # 国家（优先使用国家代码）
     ret.cnty = address.countryCode or address.country
   ```

 4. **解析视图边界**
   ```coffeescript
   if (viewport = geoResult.viewport)
     if (topLeft = viewport.topLeftPoint) and (btmRight = viewport.btmRightPoint)
       # topLeftPoint是西北角(纬度最大,经度最小), btmRightPoint是东南角(纬度最小,经度最大)
       # 需要转换为东北角(nelat/nelng)和西南角(swlat/swlng)
       ret.nelat = topLeft.lat      # 东北角纬度 = 西北角纬度(纬度最大值)
       ret.nelng = btmRight.lon     # 东北角经度 = 东南角经度(经度最大值)
       ret.swlat = btmRight.lat     # 西南角纬度 = 东南角纬度(纬度最小值)
       ret.swlng = topLeft.lon      # 西南角经度 = 西北角经度(经度最小值)
   ```

 5. **构建locality数组**
   ```coffeescript
   ret.locality = []
   ret.locality.push(ret.city) if ret.city
   # 可以根据需要添加其他地区信息
   ```

**坐标转换说明：**

Azure Maps API viewport字段结构：
```json
{
  "viewport": {
    "topLeftPoint": {
      "lat": 43.6028,     // 西北角纬度（最大纬度）
      "lon": -79.5496     // 西北角经度（最小经度）
    },
    "btmRightPoint": {
      "lat": 43.5918,     // 东南角纬度（最小纬度）
      "lon": -79.5396     // 东南角经度（最大经度）
    }
  }
}
```

系统需要的边界坐标格式：
- `nelat` (东北角纬度): 取最大纬度值 = `topLeftPoint.lat`
- `nelng` (东北角经度): 取最大经度值 = `btmRightPoint.lon`
- `swlat` (西南角纬度): 取最小纬度值 = `btmRightPoint.lat`
- `swlng` (西南角经度): 取最小经度值 = `topLeftPoint.lon`

### 3. 配置和映射更新

**在 020_geoCoderLocal.coffee 中添加：**

1. **更新 GEO_CODER_ENGINE_MAPPING**
   ```coffeescript
   GEO_CODER_ENGINE_MAPPING =
     'azure': azureGeocode  # 新增
     'mapbox': mapboxGeocode
     'bing': bingGeocode    # 保留但可能废弃
     'here': hereGeocode
     'google': googleGeocode
     'mapquest': mapquestGeocode
   ```

2. **更新 getGeocoders 函数**
   ```coffeescript
   getGeocoders = (engineType) ->
     if engineType is 'publicApi'
       return [hereGeocode, mapboxGeocode, azureGeocode]  # 添加 azure
     else if ret = GEO_CODER_ENGINE_MAPPING[engineType]
       return [ret]
     else
       # 替换 bing 为 azure
       return [azureGeocode, hereGeocode, googleGeocode, mapboxGeocode]
   ```

3. **更新 parseGeocodeResult 函数**
   ```coffeescript
   @parseGeocodeResult: (geoCacheOrig, qaddr) ->
     geoRetParseMap = {
       'azure': parseAzureResult    # 新增
       'bing': parseBingResult
       'here': parseHereResult
       'google': parseGoogleResult
       'mapbox': parseMapboxResult
       'mapquest': parseMapquestResult
     }
     # ... 其余逻辑保持不变
   ```

### 4. 配置系统更新 (src/lib/mapServer.coffee)

**需要进行的配置修改：**

1. **更新配置section列表**
   ```coffeescript
   module.exports._config_sections = ['google','mapbox','here','mapquest','azureGeocode','serverBase']
   ```

2. **更新默认配置对象**
   ```coffeescript
   DEFATULT_APP_CONFIG={google:null,mapbox:null,azureGeocode:null}
   ```

3. **添加getAzureAPIKey函数**
   ```coffeescript
   exports.getAzureAPIKey = getAzureAPIKey = ()->
     azureApiKey = DEFATULT_APP_CONFIG.azureGeocoder?.primaryKey or DEFATULT_APP_CONFIG.azureGeocoder?.secondaryKey
     debug.debug 'azureApiKey',azureApiKey
     unless azureApiKey
       debug.error 'Missing azure key in app_config'
       return
     return azureApiKey
   ```

4. **更新020_geoCoderLocal.coffee配置**
   ```coffeescript
   config = CONFIG(['google','mapbox','serverBase','here','mapquest','azureGeocode'])
   ```

**配置文件格式 (rmconfig)：**
```json
{
  "azure": {
    "key": "your_azure_maps_subscription_key_here"
  }
}
```

### 5. 错误处理和统计

**统计记录包含：**
- `src`: 'reverseGeocoding' 或具体来源
- `engine`: 'azure'
- `geoq`: 地理编码质量评分
- `err`: 错误信息（如有)
- `noRs`: 无结果标记
- `id`: 属性ID

**常见错误类型：**
- `NO_QADDR`: 没有查询地址
- `NO_RESULTS`: API 返回无结果
- `CHECK_MAPSERVER_CONFIG`: 配置错误（通常是缺少Azure API密钥）
- 网络超时错误
- API 认证错误（无效的subscription-key）

### 6. 测试考虑

**单元测试需要覆盖：**
1. 正常地址解析流程
2. 无结果情况处理
3. 网络错误处理
4. 字段映射的正确性
5. 质量评分计算
6. 边界情况（空字段、异常数据）
7. 配置密钥获取功能
8. 反向地理编码功能（支持lat/lng输入）

**集成测试需要验证：**
1. 与现有geocoding流程的兼容性
2. 缓存机制的正确性
3. 统计记录的准确性
4. 配置管理的有效性
5. Azure API密钥配置的正确性
