# 需求 [trans_log]

trans log没必要记录在properties下，记engine和id和ts到trasn.log就行，没必要记录内容, 另外要+batch remove properties.rmlog.m_zh, 还有就是应该用deepseek但是现在很多用了gemini要看下

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-01-10

## 解决办法
1. model/properties.coffee中，updateMZh 中，去掉rmlog
2. watchPropAndTranslate.coffee中，添加debug.info engine，id，ts
3. 添加batch rmTransLog.coffee 删除properties.rmlog.m_zh
4. 修改ut


## 确认日期:    2025-01

## online-step
1. 重启 watchPropAndTranslate.coffee
   ```
   ./start.sh -t batch -n watchPropAndTranslate -cmd "lib/batchBase.coffee batch/prop/watchPropAndTranslate.coffee preload-model dryrun"
   ```
2. 重启appweb
3. 运行batch rmTransLog.coffee
   ```
   ./start.sh -t batch -n rmTransLog -cmd "lib/batchBase.coffee batch/prop/rmTransLog.coffee preload-model dryrun"
   ```