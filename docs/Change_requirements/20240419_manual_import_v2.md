## 反馈
1. rain 反馈 新chrome插件可以手动导入treb房源
2. 后端需要增加对新格式的支持
3. 插件可以导入crime数据，后端需要提供支持(只存数据)



## 需求提出人:   rain
## 修改人:       luoxiaowei/rain

## 提出日期
1. 2024-04-15提出反馈1

## 原因
1. chrome插件更新到v3版本，现在是v2


## 解决办法
1. 更新metadata，使用新v3接口截断ajax请求，然后发给任意url，比如localhost/_imp_
2. 此需求可以满足未来对于crime data的导入
3. 后端增加对于新数据格式的处理，数据在微信群聊new_manual_import.zip

## 最新状态
1. crime 数据已经可以导入，见[crime]
2. DDL/Due July 30th(2024), <PERSON><PERSON><PERSON> will be removed

## 需求确认日期
1. 2024-04-18 确认需求1
