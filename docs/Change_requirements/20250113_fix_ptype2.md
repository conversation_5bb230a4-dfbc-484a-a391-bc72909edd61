# 需求 [fix_ptype2]

## 反馈

1. Rain反馈EDM房源存在ptype2存在[null]的情况
2. cleanInvalidFields会删除空数组字段

## 需求提出人:   Rain

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-01-11

## 原因

1. EDM房源在mapping过程中默认PropertySubType存在，所以ptype2会存在[null]的情况
2. cleanInvalidFields会删除empty array/object字段
3. 目前数据库ptype2存在null的统计数据如下

```
ptype2存在null的统计数据
[
  { _id: 'EDM', count: 3271 },
  { _id: 'RHB', count: 476 },
  { _id: 'OTW', count: 12653 },
  { _id: 'CAR', count: 784 },
  { _id: 'TRB', count: 1 },     // propType
  { _id: 'BRE', count: 6081 }
]
ptype2为[null]的统计数据
[
  { _id: 'EDM', count: 2791 },  // ArchitecturalStyle, BUILDINGTYPE, OTHERPROPERTYTYPES distinct,和fred确认哪些可以mapping到ptype2上
  { _id: 'OTW', count: 4218 },  // PropertySubType
  { _id: 'BRE', count: 1187 },  // 历史原因，都为21年7月份之前的数据，重新import可以获取,(propType/PropType)
  { _id: 'CAR', count: 74 },    // ArchitecturalStyle, StructureType
  { _id: 'RHB', count: 476 }    // PropertySubType,ArchitecturalStyle,StructureType 都为空
]
```
<!--
ptype2 如果mapping不上 给空数组
历史数据不处理
batch针对来源,对ptype2进行处理,(BRE/RHB去掉null,其他来源重新import)
-->

## 解决办法

1. 修改房源mapping，添加对ptype2的缺失字段处理
2. 添加batch修复数据(重新import)
3. cleanInvalidFields修改逻辑，保留empty array/object字段, 添加参数 fieldsToKeep = {fld:default} eg: {ptype2:[]}

## 确认日期:    2025-01

## online-step

1. 重启watch&import
2. 执行batch修复数据 -> src/batch/prop/fix_ptype2.coffee
