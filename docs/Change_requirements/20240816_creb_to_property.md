# 需求 [creb_to_property]

## 反馈

1. fred提出需要根据rni结构，增加creb数据进入到properties,需要修改图片URL生成函数,对应这个board。图片路径格式：
   /clg/[[size.]][phoMt]/[keyNumeric_last34]/[keyNumeric_last12]/[sid]_[objectId].jpg
   该Board已经给了lat/lng，geocoding部分类似DDF

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2024-08-04

## 解决办法

1. 添加src/libapp/impMappingCreb.coffee文件用于处理creb数据从rni到properties的mapping
2. watch&import添加creb数据支持,merge逻辑添加src:'CLG'的处理逻辑
3. 需要修改图片的url生成函数,添加对应creb图片路径格式的处理逻辑
4. elasticsearch mapping添加creb需要的字段
5. 房源检索/详情展示等添加creb的处理逻辑
6. 添加unit test cases

## 确认日期:    2024-08

## online-step

1. 需要等oreb上线之后,merge再上线
2. 新启动watch只导入creb (2024-08-05之后的房源)  init force -h 1440
3. 重启app server
