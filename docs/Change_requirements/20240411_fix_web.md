# 需求 [fix_web]

## 反馈

Fred反馈error log里有大量googlebot发出的error：
2024-02-29T00:38:48.968 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:38:48.968:{
  ip: '*************',
  ts: 2024-02-29T05:38:48.968Z,
  appver: undefined,
  ua: 'Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/121.0.6167.139 Safari/537.36',
  cookies: {
    locale: 'en',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/markham-on/113-reansbury-cres/cachet-TRBN8096008\n' +
    '13'
}]
 2024-02-29T00:38:49.147 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:38:49.147:{
  ip: '*************',
  ts: 2024-02-29T05:38:49.147Z,
  appver: undefined,
  ua: 'Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/121.0.6167.139 Safari/537.36',
  cookies: {
    locale: 'en',
    'cmate-sid': 'vW3pPssY7DxHKxMrshrs6U4zsq9S4VWOAY9bAj5w32fbXCdchxDc6HSZlt0c84dx'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/markham-on/113-reansbury-cres/cachet-TRBN8096008\n' +
    '2'
}]
2024-02-29T00:38:56.692 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:38:56.692:{
  ip: '*************',
  ts: 2024-02-29T05:38:56.692Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'kr',
    'cmate-sid': 'vW3pPssY7DxHKxMrshrs6U4zsq9S4VWOAY9bAj5w32fbXCdchxDc6HSZlt0c84dx'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/kr/toronto-on/8-widmer-street/waterfront-communities-c01-RM1-61197\n' +
    '2'
}]
2024-02-29T00:38:57.480 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:38:57.479:{
  ip: '*************',
  ts: 2024-02-29T05:38:57.479Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'zh',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/zh/rural-strathcona-county-ab/325-52555-rge-rd-223/lark-hill-farms-DDF26225173\n' +
    '2'
}]
2024-02-29T00:39:14.427 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:39:14.427:{
  ip: '*************',
  ts: 2024-02-29T05:39:14.427Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    'cmate-sid': 'RwhGdvBHl0OaxRMWQYrppEVkPkFirDzKQO0bVnwR0aYhyg0VVJoshyQZlt6spkwh',
    locale: 'kr'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/kr/smiths-falls-on/67-abel-street/smiths-falls-DDF26364629\n' +
    '2'
}]
2024-02-29T00:40:18.641 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:40:18.641:{
  ip: '*************',
  ts: 2024-02-29T05:40:18.641Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'en',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/en/kitchener-on/95-vista-cres/TRBX7370878\n' +
    '2'
}]
 2024-02-29T00:40:18.810 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:40:18.810:{
  ip: '*************',
  ts: 2024-02-29T05:40:18.810Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'en',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/en/kitchener-on/95-vista-cres/TRBX7370878\n' +
    '13'
}]
2024-02-29T00:40:21.924 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:40:21.923:{
  ip: '*************',
  ts: 2024-02-29T05:40:21.923Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'zh',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/zh/grande-prairie-ab/9418-101-ave/hillside-DDF26428439\n' +
    '2'
}]
2024-02-29T00:41:14.611 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:41:14.611:{
  ip: '*************',
  ts: 2024-02-29T05:41:14.611Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'zh-cn',
    cityhist: '[{"city":"Vancouver","prov":"BC","pr":"BC","url":"/for-sale/Vancouver-BC/Hastings","cmty":"Hastings"}]',
    'cmate-sid': 'MluXSLrnMbA6az5139sa37mXCfNoCh3WPIvYvaENqEIVRX9G2IFvTE6Zlt4ne35p'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/zh-cn/for-sale/Vancouver-BC/Hastings\n' +
    '1'
}]
2024-02-29T00:41:15.562 Site(WebRealMaster) Domain(www.realmaster.com) URL(/cError) Type(WRN) Key(null) Msg:[WRN@2024-02-29T00:41:15.562:{
  ip: '*************',
  ts: 2024-02-29T05:41:15.562Z,
  appver: undefined,
  ua: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  cookies: {
    locale: 'en',
    'cmate-sid': 'vW3pPssY7DxHKxMrshrs6U4zsq9S4VWOAY9bAj5w32fbXCdchxDc6HSZlt0c84dx'
  },
  user: {},
  msg: 'Uncaught ReferenceError: $ is not defined\n' +
    'https://www.realmaster.com/en/toronto-on/12-batavia-ave/4-rockcliffe-smythe-TRBW8067998\n' +
    '2'
}]

## 需求提出人:   Fred

## 修改人：      zhang man

## 提出日期:     2024-02-29 

## 确认日期:     2024-03-25

## 原因

报错说 $ is not defined，应该是web房源详情页用了jquery，但是没有引用jquery包

## 解决办法

1. 最开始不知道是什么原因，为了error log不被改error刷屏先修改了rmsrv5web.coffee的 window.onerror函数里将googlebot的报错屏蔽；
2. 现在发现详情页用了jquery但是没有引jquery包，因此在详情页面引入了jquery包，将rmsrv5web.coffee写的屏蔽googlebot的代码注释了，待上线后观察。
