# 需求 [add_queryForWatch]

## 反馈

1. rain提出watchAndImportToProperties需要增加一个参数 rerun -q “{city:’Ottawa’,mt:2024-12-27T23:57:43.073Z}”
和routine/init/rerun 同级别，支持从query重跑watchImport, 忽略db的时间戳
2. rerun参数需求：主要用来import旧数据，跑完退出，提示用户重新跑routine/init

## 需求提出人:   Rain

## 修改人：      Luo xiaowei

## 提出日期:     2024-12-29

## 解决办法

1. watchAndImportToProperties增加rerun,-q参数
2. 添加函数解析-q参数(query语句需要严格符合json/mongo格式)<!-- NOTE: 在命令行中query中的引号,$等需要加转义符\ -->
   1. 时间戳字段需要使用$date然后在代码中处理,eg:'{mt:{\$gte:{\$date:\'2024-01-01T00:00:00.000Z\'}}}'
   2. 如果多个src共用一个query，则需要使用_连接，eg:'{treb_car:{city:\'Ottawa\'}}'
3. rerun并且有query时，根据query只import旧数据,跑完退出watch并提示用户重新跑routine/init

## eg.
```javascript
q={treb:{city:'Ottawa',mt:{$gte:{$date:'2024-01-01T00:00:00.000Z'}}}}
JSON.stringify(q)='{"treb":{"city":"Ottawa","mt":{"$gte":{"$date":"2024-01-01T00:00:00.000Z"}}}'
```
```bash
./start.sh  -t batch -n watchAndImportToProperties_edm -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb force rerun -q '{\"treb\":{\"city\":\"Ottawa\",\"mt\":{\"\$gte\":{\"\$date\":\"2024-01-01T00:00:00.000Z\"}}}}' "
```

## 确认日期:    2024-12-31

## online-step

1. 添加rerun,-q参数 重启watch&import
2. rerun执行完成后,重启watch&import
