### 需求 [app_showFloorPlan]
1. 在房源详情页地址下方增加BuildName模块（地址匹配到公寓中的building时显示，支持MLS和独家房源）：该模块显示两行文字，第一行是具体的building name；第二行是Building Developer。该模块可点击，点击后弹出浮层。浮层显示公寓字典中给的描述，点击翻译按钮直接翻译。
2. 在房源详情页增加总单元数(Units)的显示：显示在左侧最后面，后缀显示Estimated。详情页的build storey和year没有MLS或者经纪填的等数据时，显示condos表里的数据。
3. 在房源详情页的笔记按钮上方增加户型图按钮（仅房大师经济可以看见，并且只在building中显示）。
4. 点击房源详情页的户型图按钮，打开房源图片预览页面，自动定位Tab到Floor Plans，自动定位到相同bd的Tag，例如：户型图是2个bd，则tag 2bd为被选中状态。
5. 房源图片预览页面: 
  模式1:房源详情页面打开：显示Pictures，Building等tab
  模式2:编辑笔记页面打开：显示Pictures，Building等tab
  模式3:与图片的单图预览（如独家房）区分开：带删除按钮的图片预览不显示Pictures，Building等tab
  增加Tabs（支持左右滑动）：
     1. Pictures：MLS房源图片。权限：所有人可见
     2. Building：公寓字典中的Building图片（只在building中显示）。权限：所有人可见
     3. Floor Plans：公寓字典中的户型图。权限：admin，editor，房大师经济
     4. Shared：经济笔记中上传的图片 权限：admin，editor，房大师经济
  底部显示Building Name（只在匹配到building时显示），地址，id，社区名和城市
6. Floor Plans的显示
  来源：根据来源如果是MLS房源的认为是可信的，显示信任图标，经纪不需要点match；如果是从building网页端上传的公寓信息，来源就是人工，经纪可以点击match；
  显示：在图片右下角显示点match人数，点match的图标等；左下角第一行显示unit#（只有当building的unit#与mls的一致时显示）；第二行显示卧室，浴室，面积，朝向；第三行显示户型图名称，开盘价格
  点match行为：每个经纪只能对同一个unit点赞一个fp。当已有一个点赞的fp，再点match其它的fp给出提示，确认后把之前的取消match。如果之前的图片没有显示unit#，点了match后增加单独unit#字段，直接保存mls的unit#。
  排序：前台查询和显示优先级
    a）src是mls
    a）unit#
    b）点赞数从多到少
    c）bd数+朝向

7. Shared 经济笔记上传的图片显示
  固定显示在图片的左下角，不随列表和单图模式切换变化。

## 需求提出人:    Allen
## 修改人:       zhang man

## 提出日期
1. 2024-03-13 提出需求

## 解决办法

图片预览：重新写一个图片预览接口 1.5/SV/photoScrollSwipe，房源详情页更换为调用这个接口预览图片

API设计：

| request URL | Params    | result           | description                                                 |
| ----------- | --------- | ---------------- | ----------------------------------------------------------- |
| floorPlan/fpAndBuildingImage | uaddr,unt | {ok:1,result:{notesImgs:[{nm:'',nm_en:'',nm_zh:'',img:'',avt:''}],buildingPic:[{img:'',w:'',h''}],floorPlanPic:[{_id:uuid,img:'',bdrms:'',bthrms:'',price:'',sqft:'',nm:'',unt:'',fce:'',matchCnt:0,isMatch:true}]}} | 获取收集的building，floor Plan图片和指定uaddr的经济上传的笔记图片，其中floor plan和笔记图片需要admin，editor和房大师经济权限。buildingPic数组需要将imgs数组拆分成单个记录。针对floorplanPic数组：需要将imgs数组拆分成单个记录，需要根据前端传过来的unt在文档 unts（lvl+unt）和 matchUids 里进行匹配，找到相同的数据后，将unt写入对应img记录，其余img记录不存在unt信息。统计针对unt经纪match的个数，以及当前经纪是否有match过。floorplan数据需要根据规则排序。|
| floorPlan/updateMatch | unt,id,propId,isMatch | {ok:1} | 房大师经纪针对floorplan点击match和取消自己点的match |

Model设计：

| name | Params    | result           | description                                                 |
| ----------- | --------- | ---------------- | ----------------------------------------------------------- |
| getFloorplanAndBuildingImg | uaddr,isGroup | {buildingPic:[{img:'',w:'',h''}],floorPlanPic:[{_id:'',img:'',bdrms:'',bthrms:'',price:'',sqft:'',nm:'',unt:'',fce:'',}]} | 如果isInRGroup是false，根据uaddr，只查找tp是building的数据；如果isInRGroup是true，根据uaddr在floorplans表里查找文档，找出的文档根据tp区分是building还是floorplan放在对应的数组里。|
| getNotesPicByUaddr | uaddr | [{nm:'',nm_en:'',nm_zh:'',img:'',avt:''}] |  根据给定的uaddr查询所有该地址下的笔记，获取上传的图片，头像和名字 |
| updateMatch| uid,id,unt,propId,isMatch | 更新结果 | 如果 isMatch:true，通过 id 查找当前文档的 matchUids 字段的数组，添加数据 {uid,unt(mls的unt),propId,ts}。如果 isMatch:false 通过 id 查找当前文档的 matchUids 字段的数组，删除uid 和 unt 都对应的数据。|

## 需求确认日期
1. 2024-9-4