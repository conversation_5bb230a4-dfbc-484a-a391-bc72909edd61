# 需求 [oreb_to_property]

## 反馈

1. fred提出需要根据rni结构，增加oreb数据进入到properties,需要修改图片URL生成函数,对应这个board。图片路径格式：
   /otw/[[size.]][phoMt]/[mui_last34]/[mui_last12]/[sid]_[objectId].jpg (前端存在处理图片的逻辑, 和rain确认)

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2024-06-25

## 解决办法

1. 添加src/libapp/impMappingOreb.coffee文件用于处理oreb数据从rni到properties的mapping
2. watch&import添加oreb数据支持,merge逻辑添加src:'OTW'的处理逻辑
3. 需要修改图片的url生成函数,添加对应oreb图片路径格式的处理逻辑
4. elasticsearch mapping添加oreb需要的字段
5. 房源检索/详情展示等添加oreb的处理逻辑
6. 添加unit test cases

## 确认日期:    2024-08-14

## online-step

1. 重启服务器
2. 暂停orebDownload,crebDownload
3. 执行以下脚本修改rni数据的mt字段
   1. db.mls_oreb_master_records.updateMany({}, [{$set:{mt:"$MatrixModifiedDT"}}])
   2. db.mls_creb_master_records.updateMany({}, [{$set:{mt:"$ModificationTimestamp"}}])
4. 重启orebDownload,crebDownload
5. 重启watchPropAndUpdateElasticSearch.coffee
6. 重启watch&import,对oreb的房源全部导入到properties(2015-11-13之后的房源)
