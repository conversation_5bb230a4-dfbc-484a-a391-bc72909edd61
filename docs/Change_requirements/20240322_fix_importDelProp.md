# 需求 [fix_importDelProp]

## 反馈

1. tao/sales反馈有些房源在市状态没有更新
2. watchAndImportToProperties.coffee有bug（deleteEvow），重启会把应该删除的prop重新导入，比如N8071628
3. import unit test修复
4. fred反馈DDF26650670房源open house时间问题

## 需求提出人:   Rain

## 修改人：      Luo xiaowei

## 提出日期:     2024-03-17

## 确认日期:     2024-03-18 会议上确认需求

## 原因

1. watchAndImportToProperties筛选时query语句存在问题，没有筛选到mls_treb_evow_delete_ids数据，将删除的props重新导入
2. open house时间问题由于DDF对时间处理bug(ddfFormatDateTime)，将'12:00 PM'解析为'24:00'

## 解决办法

1. 修改import逻辑，import时不导入del房源
   1. 修改mls_treb_evow_delete_ids筛选的query语句
   2. 房源删除时对records房源添加del字段，import时过滤del房源
2. 添加batch修复import错误的房源，对rni现有数据添加del字段
3. 修复import unittest，并添加相关cases测试
4. 修复DDF对时间处理的bug，添加unit test
