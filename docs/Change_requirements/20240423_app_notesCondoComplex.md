## 需求 [app_notesCondoComplex]
1. noteAdmin可以将双子楼的两个公寓进行uaddr关联，在笔记列表页进行显示

## 需求提出人:    @fred
## 修改人:       @zhangman

## 提出日期
2023.6.30

## 原因
1.相同的building，比如A房间addr为260 Queens Quay Blvd W，B房间addr为260 Queens Quay Blvd，则这两个房源的uaddr不相同，在A房源写的笔记，在B房源看不到该笔记。
2.如果是双子楼的building，uaddr确实不一样，但是物业等信息可能是一样的，希望在A栋楼写了笔记，在B栋也可以看见。

## 解决办法
原因1已解决
原因2：
1. 将房源的笔记列表页内容分为3部分：My Note：我对于该uaddr写的笔记，Building Notes：其他经济对于该uaddr写的笔记，Complex Notes：显示Condo Complex按钮（权限noteAdmin），作为地址关联的入口，noteAdmin 将该房源uaddr与A房源uaddr进行关联后，如果该经济在A房源写了笔记，则会将A房源的笔记显示在该部分,如果没有笔记，则显示关联的地址。如果都没有则Complex Notes模块不显示。
2. 从房源类型为Condo的任意房源进入笔记列表页，点击Condo Complex按钮会将该房源的地址自动填入搜索框进行200m范围的uaddr查找（condos表目前没有经纬度），显示10个距离最近的uaddr，noteAdmin可以选择关联的tags和uaddr。在搜索框中手动输入地址，会根据输入的地址显示完整addr选择列表，选择addr后，根据选中的addr进行200m范围的uaddr查找，显示可关联的10个选项。noteAdmin也可以取消两个地址之间的关联。

## 需求确认日期
2024.5.21