# 需求 [revise_car_records]

## 反馈

1. revise_car_mapping 上线失败
2. MongoError: properties.updateOne MongoServerError: Updating the path '@odata.id' would create a conflict at '@odata'
3. carDownload 使用$top 和 $skip的时候，如果有数据修改，会有遗漏.
4. carLog  "UnparsedAddress" : "16-1254 Plains Road E, Burlington ON L7S 1W6", 会直接转成addrOrig, addr 在formatProvAndCity() 会将原本的st:'Plains' 修改为st:'Plains Road E Burlington On L7s 1w6'


## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-12-02

## 原因

1. watchAndImportToProperties.coffee 中CarRecords使用的就的car数据库
2. carDownload中使用的replaceOne，导致 '@odata.id'没有被转换为这种结构：
   ```
      "@odata" : {
         "id" : "https://api.bridgedataoutput.com/api/v2/OData/rahb/Property('e7d574406128e345c59b06c3a6b69240')"
      },
   ```

## 解决办法

1. 修改watchAndImportToProperties.coffee 使用CarRecords = COLLECTION('rni','mls_car_master_records')。
2. 修改carDownload 中字段 "@odata.id" 改为odataID。
3. 数据库中已有数据进行修改，字段 "@odata.id" 改为odataID。
4. 修改carDownload：将原来 params.$skip += params.$top 修改为 不改变skip，更新filter条件： params.$filter="BridgeModificationTimestamp ge #{lastResoRequestTsNew.toISOString()}"，
5. impMappingCar中，将UnparsedAddress mapping到origAddr。


   
## 确认日期:    2024-12-02

## online-step

0. 需要先执行branch rm_treb_merge，将merge到rahb的treb数据的merged去掉。
1. 重启服务器
2. 停止carDownload
   ```
   systemctl --user stop batch@carDownload
   ```

3. 修改mls_car_raw_records 和 mls_car_master_records
   （1）先备份2个collection，待处理完没问题再删除：
   ```
   # 备份 mls_car_raw_records
   mongodump --db rni --collection mls_car_raw_records --out /path/to/backup/

   # 备份 mls_car_master_records
   mongodump --db rni --collection mls_car_master_records --out /path/to/backup/

   ```
   (2) 处理@odata.id字段
   ```
   db.mls_car_raw_records.find().forEach((doc) => {
      const odataIdValue = doc["@odata.id"]; // 获取点号字段的值
      delete doc["@odata.id"]; // 删除原有字段
      doc.odataID = odataIdValue; // 新增替代字段

      db.mls_car_raw_records.replaceOne({ _id: doc._id }, doc); // 用更新后的文档替换原文档
   });

   db.mls_car_master_records.find().forEach((doc) => {
      const odataIdValue = doc["@odata.id"]; // 获取点号字段的值
      delete doc["@odata.id"]; // 删除原有字段
      doc.odataID = odataIdValue; // 新增替代字段

      db.mls_car_master_records.replaceOne({ _id: doc._id }, doc); // 用更新后的文档替换原文档
   });

   ```
4. 重启carDownload
   ```
   ./start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee -t token'
   ```

5. 停止正在运行的watchPropAndUpdateElasticSearch
   ```
   systemctl --user stop batch@watchPropAndUpdateElasticSearch
   ```

6. init watchPropAndUpdateElasticSearch.coffee
   ```
   ./start.sh  -t batch -n watchPropAndUpdateElasticSearch -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
   ```

7. 重启watch&import,对car的房源全部导入到properties
   ```
   ./start.sh -t batch -n watchAndImportToProperties_car  -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model  -t car -h 30000 init nogeo force debug" 
   ```

8. 跑完步骤7后重启
   ```
   ./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow -t oreb -t creb -t edm  -t car -h 1 routine force"
   ```