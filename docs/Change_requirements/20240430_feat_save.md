# 需求 [feat_save]

## 反馈

1. tao 反馈收藏房源应该等同于收藏地址
2. @Tao @Allen 刚才和@Rain 讨论一下，还是需要让用户有选择是收藏listing还是收藏property。不然如果只有address，用户就没有办法取消某一个单独的listing收藏，或者全取消，或者全收藏。所以在收藏弹窗页面，要有选项，是address+unit还是只是listing。可以有个checkbox，缺省是address+unit，但可以是listing。现有的收藏不变。取消的时候也是，如果是address+unit，要可以取消单独listing，也可以取消这个address+unit。这个可以认为是一种单独的收藏类型。它可以自动触发该地址的新listing的自动收藏和推送。

## 需求提出人:   tao

## 修改人：      luoxiaowei

## 提出日期:     2024-04-30

## 原因
tao想知道某个房源后续上市情况


## 技术术语
listing: 某次上市记录，某property可以有多个listing
property: 某地址上的房子本身,某地址可以有多个property(翻建)
address+unt: 某地址

## 解决办法

1. 方案一这个你上次说过。@罗晓伟 你回来后，可以看一下，我们收藏能否改成用uaddrplus作为id。这个可能涉及多个地方（通知/检索/地图显示（历史房源怎么办，可能还要加工收藏日作为过滤条件）），还需要一个batch把现有的fav都改一遍。
2. 方案二 反馈2

最终修改方案
watchPropAndPushNotify新房源进入后
   1. 查看当前listing是否被收藏
   2. 使用uaddrplus(address+unit)查看三个月内(对比mt)是否有匹配的历史listing
前端配合修改
   1. 首页feeds添加对应tag

## 确认日期:     2024-05-
