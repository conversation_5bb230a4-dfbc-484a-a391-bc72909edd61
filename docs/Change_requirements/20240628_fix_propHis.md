# 需求 [fix_propHis]

## 反馈

1. Sophie反馈房源N8455766历史数据缺少
2. fred反馈N8315344房源历史中N5588357多了need verified标识,并且排序放在了最后

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-06-28

## 原因

1. 反馈1历史房源uaddr不一致,uaddr分别为CA:ON:NEWMARKET:471 SILKEN LAUMANN DR和CA:ON:NEWMARKET:471 SILKEN LAUMANN DR N，并且两个地址的定位相差18m导致使用经纬度查询历史房源也不满足条件
2. 反馈2因为uaddr不一致,uaddr分别为CA:ON:MARKHAM:197 KIRK DR和CA:ON:MARKHAM:197 KIRK DR W,代码中判断认为addr不一致所以为need verified

### 解决方案

1. geocode逻辑修改与流程图更新
   1. geocoding 改为async
   2. geocode时对非apartment的房源也进行设定距离内的相似地址检查, 合并  距离范围根据type设定 距离设定代码中有, 需要找一下<!-- 考虑查出来的结果很多的情况 -->
   3. 解析geocode result时，将city/cmty信息都存入locality字段中
   4. address 使用geocode返回回来的formatedAddress进行address统一, 可以规避direction和拼写错误的问题, 原有的address可以添加到aUaddr中
2. 房源历史查找api(findAddrHistory)重构，
   1. 房源历史查找距离限制范围加大, 对结果的uaddr进行相似度比较过滤
   2. need verified的判断逻辑改为addr与loc同时比较
   3. 返回房源按时间排序
3. 添加batch对geo_cache数据进行相似地址检查,合并处理，修改相关collections记录
4. unit test添加

## 确认日期:    2024-07-03

### geocode返回的formatedAddress调查结果

1. 返回的formatedAddress可以避免direction和拼写错误问题 对返回回来的formatedAddress使用address 统一,只使用第一部分 eg:105 William St, Bobcaygeon, ON K0M 1A0, Canada 使用105 William St作为address
2. 在formatedAddress一致的情况下，不同source的 location相差范围在5m之内
3. qaddr使用city和cmty的问题，case：105 William St, Bobcaygeon, ON K0M 1A0, Canada和case：105 William St, Kawartha Lakes, ON K0M 1A0, Canada 查出来的位置差很多
properties添加locality字段，geocode返回的数据将city cmty相关值addToSet(cols:geo_cache, properties)进去

### online-step

1. 重启watch&import
2. 执行batch->src/batch/geo/merge_geoCache.coffee
