# 需求 [task_push]

## 反馈

1. forum的推送不再直接推送，而是记录到mongodb（作为priority queue，优先推送lts新的用户）
2. 仿照watchPushNotiy写一个batch，watch这个queue，highwatermark 50, 调用推送发送push notify
3. 记录到Mongo需要用bulkWrite,从收到推送请求到写入完成保持在5s内

## 需求提出人:    rain

## 修改人：       liurui

## 提出日期:      2025-05-27

## 原因

1. 当前forum推送采用同步推送模式，在用户量大时会导致响应超时
2. 推送过程中如果网络中断会导致推送失败
3. 缺乏优先级控制，无法保证重要推送的及时性
4. 推送性能需要优化，目标是从推送请求到写入完成控制在5秒内

## 解决办法
1. forum推送时添加相关信息到 tasks 表中
2. 添加 batch watch tasks表，如果表中有任务，获取信息回来，按照选定的用户范围，获取用户，根据语言和badge grouping后发送pn
3. ios推送恢复批量推送


## 确认日期:    2025-06-05

## Online step

1. 重启server

2. 启动tasks watch batch：
```
sh ./start.sh -t batch -n watchTasks1 -cmd "lib/batchBase.coffee batch/pushNotify/watchTasks.coffee preload-model"
```
