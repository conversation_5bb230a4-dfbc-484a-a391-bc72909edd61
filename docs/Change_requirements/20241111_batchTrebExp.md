# 需求 [batchTrebExp]

## 反馈

1. Allen/Fred反馈收到大量treb房源的Import has issues邮件，rni数据与最新download数据status/price不一致

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON>

## 提出日期:     2024-11-11

## 原因

1. 房源确实长时间没有更新导致id进入需要手动下载的表(mls_treb_manual_ids),因为手动下载表优先级更高导致过期判断早于正常download更新判断

## 解决办法

1. 添加定时batch,每天计算判断treb房源是否过期,如果过期更新status(U)以及lsc(Exp),添加manualExp字段{LastStatus,LastLsc,mt}
2. 在判断rni和download数据不一致时，添加exp判断，如果时exp判断，跳过邮件发送并输出log
3. 邮件折叠,当存在问题房源超过3个时,合并发送邮件,notify admin
4. 需要将一批的issueIds 打包发送。最后结束时，所有id一个邮件发送。在saveRecordsBatch 中添加局部变量issueIds，传给saveRecord()，saveRecord()只更新issueIds，然后在saveRecordsBatch 一批结束前发送邮件。
<!-- X9353931没有过期,但是status改为U,Lsc为Ter,目前未找到原因先记下来暂时不做处理  -->

## 确认日期:    2024-11-12

## online-step

1. batch需要添加定时任务，每天执行
2. 重启trebDownloadV2
