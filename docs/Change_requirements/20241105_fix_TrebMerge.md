# 需求 [fix_TrebMerge]

## 反馈

1. Tao反馈很多房子没图片
2. Rain调查后发现在treb是两个可以merge的房子,但是没有merge

## 需求提出人:   Rain

## 修改人：      <PERSON><PERSON> x<PERSON>owei

## 提出日期:     2024-11-05

## 原因

1. treb将提供了可以merge的不同房源,其中一个房源有图片,另一个房源没有图片
2. 没有merge原因主要有两个
   1. 因为两个房源的sid不一致,onD的时间差距大于1天导致房源没有merge
   2. 因为都是Treb房源,查找可以merge的房源不会查找TRB,BRE的相同source房源

## 解决办法

1. 查找可以merge房源时增加没有图片的房源onD限制时间(7天)
   1. 当房源添加了pho之后,取消merge并重新查找可以merge的房源
   2. 当TRB房源没有pho时,取消src !== 'TRB'的限制
   3. 修改merge优先级的判断,对都是TRB房源添加pho判断,没有pho的需要merge到有pho的房源上,否则按mt早的merge到mt晚的房源上
2. merge后的房源需要添加aSIDs字段, aSIDs: [{sid:'40672861',id:'DDF27614671'}]
3. ut添加与修复
4. 添加batch处理历史merged房源,添加aSIDs字段
5. aSIDs 只记录在 propToKeep

## 需求2

1. RHB房源不再更新,修改merge逻辑,RHB房源为最低优先级,DDF与RHB房源merge时 RHB房源merge到DDF房源上
2. 房源merge采用打分逻辑

```
RHB   0
DDF   10
TRB   50    ON省+10,没有图片-10,dta来源-10, Toronto/Richmond Hill/Markham/Aurora市+10
BRE   50    BC省+10
OTW   50    Ottawa市+15
CLG   50    AB省+10,Calgary市+15
EDM   50    AB省+10,Edmonton市+15
CAR   50    Mississauga/Hamilton/Burlington/Waterloo市+15
NOTE: TRB/OTW/CAR的房源,没有图片优先级低于有图片,当都为ON省都有或都没有图片时,TRB优先
没有图片-35
如果分数一致,warning log,不进行merge(相同source房源,暂时不清楚哪个更优先,不进行merge)
```
<!--
TODO: 如果ddf有图片，其他没图片，仍然其他优先，把ddf的图片merge到其它board上
NOTE: 该需求需要考虑不同board的图片url生成/显示逻辑,当前branch不添加该功能
-->

## 确认日期:    2024-11-06

## online-step

1. 重启watch&Import
```
systemctl --user stop batch@watchAndImportToProperties

./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t bcre -t deleteEvow -t oreb -t creb -t edm -t car -h 1 routine force"
```
2. 执行batch对没有图片的treb房源查找可以merge的房源 -> src/batch/prop/fix_propMerged.coffee -d 2024-01-01 -s TRB
```
sh start.sh -t batch -n fix_propMerged -cmd "lib/batchBase.coffee batch/prop/fix_propMerged.coffee -d 2024-01-01 -s TRB"
```
3. 执行batch对24年开始的merged房源添加aSIDs字段 -> src/batch/prop/add_aSIDs.coffee
```
sh start.sh -t batch -n add_aSIDs -cmd "lib/batchBase.coffee batch/prop/add_aSIDs.coffee dryrun"
```
4. es watch需要重新init
```
systemctl --user stop batch@watchPropAndUpdateElasticSearch

./start.sh -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force"
```
5. 重启server
```
systemctl --user stop appweb

./start.sh
```