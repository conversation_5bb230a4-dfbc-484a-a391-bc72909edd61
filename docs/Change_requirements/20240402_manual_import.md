# 需求 [manual_import]
本branch涉及解决办法1和3，解决办法2参见fix_import
Functions:
1 dump manual ids, update state
2 check records current state
3 reset dump status

## 反馈
1. tao/sales反馈有些房源在市状态没有更新
2. 我们需要有个自动侦查房源是否有问题的机制。比如10天以上没有更新的，自动用id查一下。之后有几种情况。有可能的确没有更新。如果返回错误，需要加到手动更新一个列表，发我们自己。如果已经更新了状态，这个可能是程序出问题了，要列出ID和详情并报警给我们。

## 需求提出人:    tao/rain
## 修改人:       liurui/rain

## 提出日期
1. 2024-03-27提出反馈1
2. 2024-04-01fred提出反馈2

## 需求确认日期
1. 2024-03-27 meeting确认 [反馈1](#反馈)
2. 2024-04-01 meeting确认 [反馈2](#反馈)

## 原因
1. debug结果为房源在treb的node-rets 返回未找到，可能因为房源从evow系统撤出


## 解决办法
1. 增加手动页面输入id，管理员可以从该页面copy id list，然后去treb系统手动导入
2. +batch从数据库自动查找10天没跟新的active房源，加入到该db，自动程序可以重新按照id查找这些房源
3. 管理页面可以更新房源最新状态






