# 需求 [fix_skipGeoCoding]

## 反馈

1. fred提出对rni中存在loc信息的房源需要跳过geocoding
2. rain提出对rni中存在loc信息计算geoq需要dry

## 需求提出人:   Fred/Rain

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2024-09-18

## 解决办法

1. dry,提出rni房源loc检查func,watch&import时调用该函数
   1. shouldSkipGeoCoding 查找Boundary判断是否需要跳过geoCoding
   2. addBoundaryInfo对跳过geoCoding的房源添加boundary tag信息
   3. 修改getBoundaryTag函数,已经查找过的房源不再查找Boundary
2. 对源数据提供loc信息的geoq计算逻辑调整,添加说明(city一致/Boundary不存在(偏远地区)认为geoq=100,否则认为geoq=90)
3. 添加unit test cases

## 确认日期:    2024-09-25

## online-step

1. 重启watch&import
