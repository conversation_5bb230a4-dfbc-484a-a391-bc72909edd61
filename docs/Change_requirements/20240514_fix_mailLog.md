# 需求 [fix_mailLog]

## 反馈

1. fred反馈mail log存储数据过大,导致数据库宕掉
2. fred反馈mongo4里面的set/getPreDefine和timeseriesColl的定义方式需要简化和直观化,function/变量名字要清楚反应含义和要作的事情
3. rain反馈需要将edm与主程序的mail log区分开保存,主程序的mail log保存到chome,edm的mail log保存到rni

## 需求提出人:   fred/rain

## 修改人：      luoxiaowei

## 提出日期:     2024-05-06

## 原因

1. 反馈1是因为记录mail log时没有区分edm和主程序,将所有sendMail的信息都记录在了chome.mail_log下面(限制30天过期),导致数据保存过多

## 解决办法

1. mongo4.coffee下面的set/getPreDefine函数合并为一个 -> getPreDefineColl
2. 将单独创建时序表的函数(createTimeSeriesCol)与创建常规collection(get_coll)的函数合并,修改传参让函数同时支持时序表与常规表的创建/获取
3. 将edm发送的邮件与主程序发送的邮件分开记录mail log(edm记录到rni,主程序记录到chome),现有chome.mail_log的数据可以不管,会自动过期(建议直接删除该表)
4. 添加unit test

## 确认日期:  2024-05-15

## 2024-05-20 新需求

1. 区分edm和主程序在程序启动时就获取到predefine的coll,key统一定为mailLog
2. fred提出sendMail log可能有3种情况。存rni/存data，也可能不存。要判断，得不到collection的时候，就不存了。batchBase里面可能应该加optional的可以指定选项
3. get_coll api修改定义为get_coll: (dbName,collName, options, callback)->

### 解决方案

1. config设置preDefColls,在server启动时进行set predefine colls
2. batch在不需要保存mail log的时候,支持参数no-mail-log

## OnlineStep

1. 需要在config.coffee文件下配置如下内容(现有的predefine collections):

preDefColls:
  mailLog:
    dbName:'chome'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600
  emailWhitelist:
    dbName:'chome'
    collName:'email_whitelist'
  emailMXFailed:
    dbName:'chome'
    collName:'email_mx_failed'

preDefBatchColls:
  mailLog:
    dbName:'rni'
    collName:'mail_log'
    options:
      timeseries:
        timeField:'timestamp'
        metaField:'metadata'
      expireAfterSeconds:30*24*3600

## 2024-06-04

1. fred review问题反馈 coffeemate3.coffee#599,605 应该使用cb err. await/async用throw，callback一般第一个参数是error。599行可以一起改一下。
