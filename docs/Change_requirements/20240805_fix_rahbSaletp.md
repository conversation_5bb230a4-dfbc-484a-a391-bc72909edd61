# 需求 [fix_rahbSaletp]

## 反馈

1. fred反馈房源RHBH4198430在RNI里面是lease，不知道为什么properties里面变成了sale

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON>

## 提出日期:     2024-08-05

## 原因

1. rahb房源在import时判断RAHB_LeaseAgreementYN != null时为lease，否则为sale，sale/lease判断存在问题

## rni数据调查

1. RAHB_TransactionType字段不存在有1186条记录
2. RAHB_TransactionType和RAHB_LeasesYN字段同时不存在有1166条记录
3. RAHB_TransactionType和RAHB_LeaseAgreementYN字段同时不存在有937条记录
4. RAHB_TransactionType,RAHB_LeaseAgreementYN,RAHB_LeasesYN同时不存在有917条记录

## 解决办法

1. 修改rahb房源saletp的判断逻辑
   1. RAHB_TransactionType存在时根据当前字段判断
   2. RAHB_TransactionType字段不存在时，根据lp判断(2w以内认为是出租)
   3. RAHB_TransactionType不存在并且价格超过2w时, 根据RAHB_LeasesYN/RAHB_LeaseAgreementYN 判断
2. 添加batch统计/修复rahb现有数据,saletp,lp/lpr,lst(Sld/Lsd)

## 确认日期:    2024-08-05

## online-step
