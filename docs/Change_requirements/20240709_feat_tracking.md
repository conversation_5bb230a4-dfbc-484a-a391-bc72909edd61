# 需求 [feat_tracking]

## 反馈

1. https://s.realmaster.com/send_logger.html
参照这个页面里面的js，把详细页面的点击 周边房源下面的“周边/Nearby”事件发送给事件收集服。把详细页面打开的事件也记录/发送一下。

## 需求提出人:   fred

## 修改人：      liurui

## 提出日期:     2024-06-19

## 解决办法

1. 添加[logger.lib.min.js](https://s.realmaster.com/logger.lib.min.js)
2. 点击事件添加logger.send
3. LOGGER.send({
    obj: 'logger',页面(e.g. propdetail/showing)
    sub_obj:'' fn名字或者事件发生的地方
    act: 'click/close/scroll',
    uuid: '427ba208',RMSrv.getCookie('uuid')
    obj_id: 'send_logger.html',房源id
    user_roles: 'guest',登录用户具体权限
    platform: 'h5',安卓/ios/h5/pc/微信
    client_ts: '',事件发生时间
    tgp: '',A/B
    query_string: 附加信息,
    src: 'url'
  });


日志表结构（源 LOG）
log_time datetime UTC 自动生成
client_ts datetime 选填。UTC 传值，支持格式： 2024-05-11 15:28:53 或 2024-05-11T15:26:44.722Z
obj string 必填。对应上文定义的 {objects} [0]事件
sub_obj string 选填。对应上文定义的 {objects} [1]事件
act string 必填。对应上文定义的 {action} 事件
uuid string 必填。用户id = getUniqueId()
obj_id string 必填。objects[0]的id
user_roles string 必填。realtor/user/guest
tgp string 选填。根据query的tgp=group1,group2，按着规则解析为string test group
query_string string 选填。对应上文中的Query串，请存入json串类型
platform string 必填。Android/iOS/h5/pc
src string 选填。来源
client_ip string 选填。原始IP串，不转换
server_name string 选填。请求的域名，对应存在多域名的情况
host_name string 不填，服务器的名称，服务自动添加。

## 确认日期:    2024-07-11

