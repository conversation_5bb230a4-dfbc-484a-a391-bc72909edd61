# 需求 [rm_rahb]

## 反馈

1. 删除目前在市的rahb房源，修改showing, prop_notes, prop_notes_personal, prop_fav 的id

## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-12-03

## 原因

1. Rahb 不再更新


## 解决办法

1. Batch 获取 src是RHB，（1）查找到对应的merged ID，替换showing(修改_id 和 src[src=idNew的前3位，如TRB，CAR])，prop_notes, prop_notes_personal, prop_fav 的旧 RHB id
（2）如果status是'A' 的prop, 删除这些prop（存到tmp，2年后删除）
2. 在model propNotes,PropFav,showing中添加updatePropID
   
## 确认日期:    2024-12-04

## online-step
1. 运行batch rm_rahb
```
sh start.sh -t batch -n rm_rahb -cmd "lib/batchBase.coffee batch/prop/rm_rahb.coffee preload-model dryrun"
```