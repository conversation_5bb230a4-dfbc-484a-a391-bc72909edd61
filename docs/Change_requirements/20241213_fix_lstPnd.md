# 需求 [fix_lstPnd]

## 反馈

1. fred反馈Edmonton检索成交房源时，返回了inactive的房源

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON>

## 提出日期:     2024-12-13

## 原因

1. EDM/CLG房源存在status == ‘Pending’，但是没有成交的房源, BRE存在status == 'Pending'的成交房源, 双方存在lst冲突导致查询成交房源时存在问题
<!-- BRE房源的Pnd/Cld算作成交房源,需求文档: docs/Change_requirements/20240425_BC_statusChange.md -->

## 解决办法

1. 修改EDM/CLG房源对Pending转换的lst格式
2. 房源import时保存原有的mlsStatus
3. 添加batch将房源原本的mlsStatus添加进来
4. 前端显示添加房源的mlsStatus信息(app详情/检索列表成交添加MlsStatus显示)

## 不同board的MlsStatus mapping

1. TRB, Lsc直接转换，没有做任何处理
2. BRE
    'Active' :               'New'
    'Cancel Protected' :     'Cp'
    'Collapsed':             'Clp'
    'Expired':               'Exp'
    'Hold all Action':       'Haa'
    'Terminated' :           'Ter'
    'Sold' :                 'Sld'
    'Pending':               'Pnd'
    'Rented' :               'Lsd'
    'Active Under Contract': 'Auc'
    'Closed' :               'Cld'
3. CLG
    'Active' :               'New'
    'Pending' :              'Sus'
    'Withdrawn':             'Wd'
    'Expired':               'Exp'
    'Leased':                'Lsd'
    'Rented' :               'Lsd'
    'Sold' :                 'Sld'
    'Terminated':            'Ter'
4. OTW
    'Active' :               'New'
    'Cancelled' :            'Ter'
    'Conditional':           'Sc'
    'Expired':               'Exp'
    'Leased':                'Lsd'
    'Rented' :               'Lsd'
    'Sold' :                 'Sld'
    'Suspended':             'Sus'
5. EDM
    'Active' :               'New'
    'Cancelled' :            'Ter'
    'Conditional':           'Sc'
    'Expired':               'Exp'
    'Leased':                'Lsd'
    'Rented' :               'Lsd'
    'Sold' :                 'Sld'
    'Suspended':             'Sus'
    'Pending':               'Sus'
6. CAR
    'Active' :               'New'
    'Cancelled' :            'Ter'
    'Conditional':           'Sc'
    'Expired':               'Exp'
    'Leased':                'Lsd'
    'Rented' :               'Lsd'
    'Sold' :                 'Sld'
    'Suspended':             'Sus'
    'Hold':                  'Sus'
    'Pending':               'Pnd'
    'Closed':                'Cld'
7. RHB, 已经不在更新,原本的mapping关系
8. DDF, 没有lst字段

## 确认日期:    2024-12-16

## online-step

1. 重启watch&import
```
systemctl --user stop batch@watchAndImportToProperties
./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcre -t deleteEvow -t oreb -t creb -t car -t edm -h 1 routine force"
```
2. 重新init elastic
```
systemctl --user stop batch@watchPropAndUpdateElasticSearch
./start.sh  -t batch -n watchPropAndUpdateElasticSearch -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
```
3. 重启server
```
./start.sh
```
4. 执行batch->src/batch/prop/add_MlsStatus.coffee
```
./start.sh -t batch -n addMlsStatus -cmd "lib/batchBase.coffee batch/prop/add_MlsStatus.coffee dryrun -d [YYYY-MM-DD]"
```
./start.sh -t batch -n addMlsStatus -cmd "lib/batchBase.coffee batch/prop/add_MlsStatus.coffee -d 2022-01-01"
