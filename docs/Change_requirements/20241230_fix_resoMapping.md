# 需求 [fix_resoMapping]

## 反馈

1. Fred反馈房源X6361120在市时间为-9803
2. <PERSON>/<PERSON><PERSON><PERSON>反馈mapping到properties的数据可能有些格式是错的，已经检查出来的sldd有问题（存储为字符串了）

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2024-12-13

## 原因

1. 房源X6361120源数据提供的DaysOnMarket为-9803
2. reso mapping时对部分日期字段的处理存在问题，没有将下面的日期字段转为numberDate
<!-- 'poss_date', 'sldd', 'onD', 'offD', 'ld', 'exp', 'picTs','ListingContractDate', 'input_date', 'dt_ter', 'lud', 'unavail_dt', 'cd', 'cldd' -->

## 解决办法

1. reso mapping添加对dom字段的判断,如果dom为负数error log,尝试修复或跳过记录
2. reso mapping添加对特殊日期字段的处理,将特殊日期转为numberDate
3. 添加batch,将特殊日期字段为字符串的记录修改为numberDate

## 确认日期:    2024-12-16

## online-step

1. 执行batch
2. 重启watch&import
