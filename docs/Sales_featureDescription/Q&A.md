

#### 分享
Q: 为什么我点了不要推广，下面还是有房大师的链接。然后那个经纪转发的新市豪区下面就没有房大师的链接呢？有没有可能下面都没有房大师的链接呢？
A: 第一次转发，微信会自带source，想要去掉的话用微信打开，然后二次转发。

### 数据
#### 数据源
Q:只更新图片不更新prop？
A:需要更改房源某些字段才会更新，或者propDetail里use treb pic


Q:这个经纪说已经改成一房了。但是我们这里还是显示1+1
A:数据同步不会去掉已经写了的字段，需要新import上线

Q: 温莎那边的房源数据我们齐吗？数据更新一般都是多久呢？
A: DDF和TRB的我们可能有，winsor自己board的就没有,需要自己上暗盘

Q: Apt_num 和unit_num 区别
A: Apt_num is Municipal unit, should show in address. unit_num is legal unit, show in detail

Q: realtor 上的房源是不是我们都有？
A: realtor上的房源比我们多。只有选了DDF national pool或者post on interenet之类的选项的才会给我们

Q: MSL 上改了sld状态，我们app没有
A : 对于商业房源，treb给过来的所有的residencial 房源都是有状态的。商业的只有10%的有状态


### 视频
Q: 如果客户想在他的listing上面加视频 那是不是视频的来源必须是他自己的YouTube channel?
A: 只能是youtube的视频，不限制channel，需要tao来加，客户不能自己加


### 分享
分享签名总是加不上。
推广那个check mark多点几下，可能是系统版本过低，webview对check支持有问题。


### 置顶
can only see avt after login
if none vip user top listing by himself，no avt.
置顶关联经纪账号

Q: 房源detail页左下角联系经纪不现实
A: 滚动显示,只有用户安装了wechat才会显示。 通过useragent判断
top_agent 和地图上的是一样的。是管理员添加。 admin tools 下面，top agent manage，设置一些信息。

toplisting 是另一个业务，prop topupuids 是置顶人信息，读最后一个。

存在房源下，记录存在transaction下面。

more billing working。

### 置顶排序策略
1. 如果toplisting 小于limit（5）个，完全随机排序
2. 如果大于limit， 随机排序，考虑权重，越新上的topListing(topMt)权重越大。
   
### 论坛
Q: 被投诉骚扰女经纪，封号
A: 更多，用户管理，block user by device id。 管理员可以删除。

### 暗盘
Q: 标题使用自动的还是手动的。
标题这里有个锁，左边那个小锁，是用来区分自动生成标题还是用手动的，变成黑色就锁住了。如果显示仍然有问题，需要更新手机系统。
如果编辑修改了需要重新promote，修改才会生效

Q：为什么出现两个房源
通过分享到58创建的暗盘房源，生成了一个新的暗盘，推广到58里。
在点击推广到市场，会反向推回到properties里。这样就出现了重复房源。bug已经修复，不允许mlslisting推广到市场。
在wesite里，是不显示从mlslisting过来的房源的

Q: 为什么网站编辑暗盘地图出不来？
A: 网站需要用https 登陆，如果有问题，检查是否使用cache，清空或者使用新的浏览器。

Q: 这个楼花转让在地图上可以看到。为什么输入地址以后就不显示呢？
A: 设置了daddr，autocomplete查不出来。

Q: 我们楼花转让里面那些置顶的，在后面列表里为什么又重复出来一次呢？
A: 业务逻辑就是这样设计的。置顶重复出现。mls和楼花都会。

Q: 编辑房源地图找不到
A: 一定要点save&promote，选择commission，然后推广到市场。列表页的最后一个button也是推广到市场的意思。
点亮thumb，增加rcmd=1，会作为推荐房源在微图文的下方推荐房源里显示。

Q:我的房源列表页的share 1/8是什么意思
1是当天的分享数，8是总共的分享数

### 微网站
用户微网站mls listing没有关联需要增加TREB_ID（对应数据库aid字段），ddfid(对应数据库ddf_rid字段。)管理员在用户管理页面可以添加。
"ddf_rid" : "DDF2041886",
"rid" : "9583169", 经纪填的id
"aid" : "TRB2041886", admin给的trbid。
三个match任意一个都应该查到。
### 房源详情
如果图片和位置有问题，管理员可以手动刷新图片，修改位置。
隐藏交易历史？如何隐藏？

### 微图文
非vip最多80 vip最多1000，超过了要手动删除
微图文编辑<-  -> 会删除光标前/后所有内容

### 用户
如果用户需要修改密码，但是始终收不到邮件，可以管理员在用户管理页面修改密码。


### 估价 Evaluation
管理员可以删除评估历史。

### 删掉mls房源
prop manage 里display photo, delete prop。google 那边删除。
图片服把图片删掉。google,remove from google.

### cpm 出不来
检查是否被terminate了。
检查投放的类型是否正确，residential和commercial

### 聊天
Q: 城市专家客户有个疑问 有什么便捷的方法可以一次性把之前联系过她的房大师用户的邮箱从app里导出来呢？
A: 这个需求涉及到客户隐私，我们app也只是chat并未直接提供联系方式，所以不会提供
联系方式分为app内chat或者直接email/call经纪。chat时只对话可见，聊天内容中也许客户会提供联系方式，chat中应该看不到客户profile的，所以我们不会提供联系下载。


### 收藏
vip 收藏夹上限50个，非vip6个。收藏房源没有上限
MAX_GROUPS = 6 #includes a cntr
MAX_GROUPS = 50 if req.isAllowed 'vipRealtor'
### 房源分享
只有treb和residencail的可以分享。
prop.ptype !== 'Commercial' && prop.src == 'TRB'

### 更新照片 
更新照片之后缩略图需要24小时才能更新。


### 骚扰
有经纪举报骚扰，怎么处理账号？
在用户管理界面，管理员可以封掉这个email对话和论坛留言。