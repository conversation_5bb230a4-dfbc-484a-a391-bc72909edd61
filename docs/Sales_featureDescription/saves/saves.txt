整合收藏的properties、projects、searches、agents(businesses）入口
添加rmlisting收藏、分享、加入showing功能
替换prop列表rm房源查询数据库（user_listing => props-part）
修复fav prop grp地图显示zoom

注：上线时需要删除model/properties.coffee  1388-1391
  正式服properties和props_part表同步，冗余代码

后端文件
apps/80_sites/AppRM/user/saves.coffee

前端文件（使用v3.js）
themes/rmApp/saves/
webroot/public/js/saves/
style/sass/apps/pages/saves.scss


重新的vue component
propItem.js
propFavAction.js
propAddToShowing.js
listingShareDesc.js
shareDialog.js
projItem.js => appProjectList中projItem显示片段
contactCrm.js
crm_mixin.js
searchOption.js => savedSearches中searchOption显示片段
user_mixins.js
agent.js => (yellowpage/userInfoCard.vue user/User.vue)样式功能综合提取
contactRealtor.js
