showing+contact优化,测试流程

首页mls页面
|功能|期待||
|---|:---|---|
|  添加二维码扫描  |  扫描跳转对应页面，非app内二维码报错  |  完成✅  |
|  测试 |  微信扫描二维码（进入下载房大师页面）；经纪和经纪互相扫描（可以绑定）；经纪和客户扫描（可以绑定）；未登录状态（扫描显示错误提示页面）；同一个showing不能同时关联1个以上客户。

|功能|期待|被绑定客户部分可看|
|---|:---|---|
|  showing together  |  如果有绑定的经纪显示，没有则版块整体不显示  | 完成✅   |
|  测试 | 经纪绑定经纪显示；经纪绑定客户显示；未登录时不显示；   |
|  upcomming部分  |  显示未来所有绑定自己的经纪给自己添加的showing  | 完成✅   |
|  测试  |  绑定1个经纪；绑定N+1经纪；经纪和经纪互相绑定showing； |    |
|  complete部分  |  显示最近去过的5个绑定自己的经纪加的showing（点击see all可以加载近半年的）  |  完成✅  |
| 测试 |  过去showing点击进入showing+日期；|    |
|  buylist部分  |  显示所有自己的意向房源（6个以上显示see all，进入buylist页面）  | 完成✅   |
|  测试|  经纪在showing详情页增加buylist，减少buylist，在首页会发生改变。  |    |
|  点击showing  |  打开对应showing详情（只读，对应房源分享页面）  |  完成✅|
|  测试 |  用户点击无法修改，正常显示排序 |    |
|  点击房源  |  打开对应房源详情  | 完成✅   |
|  测试  |  正常显示房源详情，可以收藏及分享 |    |
|  点击经纪头像  |  打开经纪wepage页面  | 完成✅   |
|  测试 |  正常显示经纪主页，可以收藏以及分享 |    |


showing分享页面
|功能|期待||
|---|:---|---|
|  添加app支持  |  app内可以打开  |正常显示    |
|  删除title客户名字显示  |  页面顶部显示 showing+日期  |  正常显示  |


showing列表
|功能|期待||
|---|:---|---|
|  头部添加buylist入口  |  打开查看已经添加至showing的房源  |  完成✅  |


showing 详情
|功能|期待||
|---|:---|---|
|  添加add to buylist  |  showing顶部分区buylist数量增加，buylist页面增加该房源  |完成✅    |
|  测试 |  添加/取消，重复操作，显示正常  |    |
|  添加booking failed  |  当前房源底部操作部分收起，清空当前房源开始时间，左上角排序删除  | 完成✅   |
|  测试 |  收起/打开，重复操作，显示正常  |    |
|  增加分享方式  |  增加复制链接，发送短信，发送邮件的分享方式  |  完成✅  |
|  测试 |  发送邮件，发送短信用户可接受，showing列表链接App外能打开，并可以联系经纪|    |


contact
|功能|期待||
|---|:---|---|
|  添加invite（vip经纪）  |  生成二维码供客户扫描，建立绑定  | 完成✅   |
|  添加unlink（vip经纪）  |  解除与客户的绑定关系 | 完成✅   |  
| 测试 | 解除与客户的绑定关系，客户刷新后对应showing自动清除，如果没有showing则该板块不显示  |    |
|  添加搜索  |  搜索客户  | 完成✅   |
|  测试  |  搜索客户功能正常，可首字母搜索推荐  |    |


buylist
|功能|期待|经纪部分|
|---|:---|---|
|  添加buylist页面  |  展示所有客户的意向房源  | 完成✅   |
|  测试 |  展示所有客户的意向房源，时间由近到远排序。  |    |
|  右上角客户过滤  |  展示指定客户的意向房源  | 完成✅   |
|  测试|  展示指定客户的意向房源，可删除，可修改，叉掉指定客户自动返回所有客户buylist页面  |    |
|  点击下方showing  |  打开对应showing详情（可修改）  |  完成✅  |
| 测试  |  打开对应showing详情（可修改），修改后返回buylist显示最新状态  |    |
|  点击房源  |  打开对应房源详情  |完成✅    |
|  点击删除  |  删除对应房源，同时对应showing里的房源add to buylist状态应该改变  |完成✅    |

|功能|期待|被绑定客户部分可看|
|---|:---|---|
|  点击下方showing  |  打开对应showing详情（只读，对应房源分享页面）  | 完成✅|
|  点击房源  |  打开对应房源详情  |完成✅    |
|  测试  |  打开对应房源详情，返回则到showing列表|   |
|  点击经纪头像  |  打开经纪wepage页面  | 完成✅|
|测试 |  打开经纪wepage页面，返回回到showing列表；经纪主页Emall无法操作|  |

扫描二维码绑定经纪
|功能|期待||
|---|:---|---|
|  自己绑定自己  |  报错（cannot invitr yourself）  |  完成✅  |
|  重复绑定  |  提示已经绑定过该经纪（app内两个账号只能绑定一次）  | 完成✅   |
|  首次绑定  |  确认是否与该经纪建立绑定关系，确认则立即创建（saves/linked agent可以看到所有与自己绑定过关系的经纪）  | 完成✅   |

save/linked agent
|功能|期待|被绑定客户部分可看|
|---|:---|---|
|  添加linked agent页面  |  展示所有与自己绑定的经纪  |  完成✅  |
|  测试 |显示所有绑定的经纪，绑定测试1，绑定测试2，均正常显示|
|  删除  |  解除与经纪的绑定  | 完成✅   |
| 测试 |  解除与经纪的绑定，对应的showing删除|    |
