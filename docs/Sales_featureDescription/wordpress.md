# Wordpress
**Sync wordpress post to app forum**

## Config
In Wordpress dashboard -> 设置 -> 常规, scroll to bottom at API Settings

**Replace with your local config**
Webhook URL: http://www.realmaster.com/1.5/forum/wordPressEcho
Webhook URL for Comment: http://www.realmaster.com/1.5/forum/wordPressCmntEcho

## Update wordpress credential in Realmaster APP
In App homepage as dev/admin, go to more -> edit wordpress profile
in wordpress config, fill in WordPress Host/username/password

## set up wordpress local
Download MAMP to set up PHP server
Download wordpress, follow wordpress setup online, config mysql database

## Sync production wordpress to local
1. Go to production wordpress plugin All-In-One WP Migration
2. 导出到文件，保存导出文件到本地
3. In local wordpress, 导入来自文件

## Test
create new post in wordpress and check if app forum has new post

## Past Problems
* If the password of user RealMasterAdm changed, sync wordpress post will fail
* Curl ssl certification verify failed, fix by ignore ssl in wordpress
* Make sure production wordpress webhook urls are correct

### If have any questions, info Sam or Julie