#features
1. add push notify user self-check page
settings->notification
如果手机的notification关闭，提示diagnose需要打开
diagnose
如果是6.1.3以下版本，提示升级
如果是6.1.3及以上，检查google service （ios默认有google service）。notification， language
发送一个测试pn，检查是否能够收到（非native andriod不稳定，可能收不到）

2.notification manage


3. email verify
新注册用户都没有email verify,
app注册用户没有emlV字段，可以登陆app，但是setting里显示未verify。
网站注册用户在login里emlV=false，
emlV=false的用户登陆app时会提示需要验证才能登陆。登陆网站可以正常登陆,3小时候要求重新验证。

注册邮件里有一个link，link里是一个vcode，点击link可以激活，设置emlV = true
在notification，verify email点击后发送一个code，输入code后验证，同样设置emlV = true

4. user manage email subscription
app端退订，要求登陆后退订
web端退订，如果是email打开链接可以不用login。email打开的链接修改savedseach 24小时过期，跳转登陆页面。
exuser，不显示savedsearch，不检查login，直接根据id更新user_extra表。
