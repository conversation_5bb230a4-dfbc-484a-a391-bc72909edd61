# How To Add a New Language

## Database

1. Add id to new translate, this will check is any new translate since last run, and add 'id' to new document. The id will be use as query when import (Collection.updateOne())

```bash
./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m id
# above commend will add {id:'L99999'} to documents that don't have id. 
```


2. Download txt file for translation, the following commend will download all document that without 'kr' field

``` bash
# structure
./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m dump -f [/absolute_path_to_download] -t [new_language_field]

# example
./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m dump -f /User/louis/downloads/toBeTranslate.txt -t kr
# above example will download documents that without kr to downloads folder
```

The txt will looks like this

```txt
L10002|# - form.|# - form.
L10003|#trl spc|#trl spc
```

3. Translate the txt file from step 1 into new language
*Double check some special characters such as "% s "  make sure there is no space in between like "%s"*

```txt
L10002|# - form.|#-표
L10003|#trl spc|대형 트럭 주차 공간
```
4. Import translated txt

```bash
# structure
./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m import -f [/absolute_path_to_translated_file] -t [new_language_field]

# example
./start.sh lib/batchBase.coffee batch/i18n/i18n.coffee -m import -f /User/louis/downloads/translated.txt -t kr
# above example will read translated.txt and update documents with kr field
```

## Source code
新加js片段选择语言
  引入 changeLanguage.js
  调用 createLangTemplate（传入当前设置的lang）
  添加接受设置结果函数 setLang（接收参数lang）
  eg:(vue)
    mounted(){
      this.createLangTemplate = createLangTemplate;
      window.setLang = this.setLang;
    }

新添加语种需修改文件
lib/i18n.coffee
  locale
  language_list
  language_list_obj
00_constant.coffee
  LANGUAGE_LIST
  LANGUAGE_OBJ:语种全称
  LANGUAGE_ABBR_OBJ：语种简称
changeLanguage.js
  LANGUAGE_OBJ：选择语言部分
languageHelper.coffee
  language
realtorVerify.coffee
  TWILIO_LANG_MATCH
010_user.coffee
  ensureUser
coffee4client/rmsrv*.coffee
  shareLang、_shareLang:分享设置部分

shareDialog.* & shareDialog2.*:添加可分享的语言

appAdsManage.vue
  template
    lang
      checkbox
  methods
    parseLang
      langMapping
appListSearch.vue
  methods
    setLang
      url.replace
edm.vue
  mounted
    if this.curLang
appCpmEdit.vue
  template
    language
      checkbox
propDetail.vue
  methods
    setLanguage
      url.replace

website/layout.html
  language:web默认header部分


修改 config web语言支持
vhost.yml
settings:
  www:
    setting:
      langPrefix:
        kr: 'kr'