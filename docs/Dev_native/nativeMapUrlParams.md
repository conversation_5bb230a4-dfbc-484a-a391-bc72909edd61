## native url
native会检测webview的url，如果触发以下条件会跳转native，或者不处理
```javascript
if (Platform.OS === 'android' && /assets\/src\/html\/index\.html/.test(url)) {
  return true;
}
isAutocomplete(url) {
  // return false;
  return /\/1\.5\/autocomplete/.test(url)
}
function isMapSearchMode(url){
  // China ip cant load google native map
  // console.log('######appConfig',getAppConfig('useWebMap'))
  if (getAppConfig('useWebMap') === true) {
    return false;
  }
  // console.log(url, /stigma\/house/.test(url))
  if(/\/1\.5\/mapSearch\/advFilter/.test(url)){
    return false;
  }
  if (/mapSearch.*useWebMap/.test(url)) {
    return false;
  }
  if (/mapSearch.*mode=list/.test(url)) {
    return false;
  }
  if (/grp=/.test(url)) {
    return false;
  }
  if (/stigma\/house/.test(url)) {
    return true;
  }
  // vars.src == 'nativeMap' || vars.src == 'nativeAutocomplete'; already in map or autocomplete
  if (/src=native/.test(url)) {
    return false;
  }
  // if (/schoolSearch\?gps=1/.test(url)) {
  //   return true
  // }
  // console.log('++++++url',url)
  // NOTE: support native measure
  if (/1\.5\/tools\/measure/.test(url)) {
    return true;
  }
  return /\/1\.5\/mapSearch/.test(url)
}
extractMapOptFromUrl(url){
  // var opt = {
  //   tp:'mapSearch',
  // }
  var opt = urlParamToObject(url);
  opt.tp = 'mapSearch';
  // TODO: school only support incomplete
  if (/schoolSearch/.test(url)) {
    // opt.pubSchOnly = true
  }
  if (/stigma\/house/.test(url)) {
    opt.stig = true
    // console.log('++++++',this.data.jsonTp)
    // if (this.data.jsonTp && (this.data.jsonTp=='popup' || this.data.jsonTp=='pageContent')) {
    //   opt.closePopup = true
    // }
  }
  if (/tools\/measure/.test(url)) {
    opt.measure = true
    // console.log('++++++',this.data.jsonTp)
    // if (this.data.jsonTp && (this.data.jsonTp=='popup' || this.data.jsonTp=='pageContent')) {
    //   opt.closePopup = true
    // }
  }
  return opt;
}
if (this.isAutocomplete(navState.url)) {
  let url = navState.url;
  if (url.indexOf('?') > -1) {
    url = url.split('?')[1]
  }
  var opt = {};
  url = url.split('&');
  for (let i of url) {
    var tmp = i.split('=');
    var p = tmp[0];
    var v = decodeURIComponent(tmp[1]);
    opt[p] = v;
  }
  opt.tp = 'autocomplete';
  clearTimeout(this.autocompleteTimeout);
  this.autocompleteTimeout = setTimeout(() => {
    // console.log('send ts:',Date.now())
    EventEmitter.emit("app.message",{msg:JSON.stringify(opt)});
  }, 200);
  return false;
}
if (GoogleMapProviderAvailable && isMapSearchMode(navState.url)) {
  var opt = this.extractMapOptFromUrl(navState.url)
  clearTimeout(this.mapSearchTimeout);
  this.mapSearchTimeout = setTimeout(() => {
    // console.log('send ts:',Date.now(),opt)
    EventEmitter.emit("app.message",{msg:JSON.stringify(opt)});
  }, 200);
  return false;
}
if (Platform.OS === 'android' && /^http\:\/\/10\.0\.2\.2/.test(url)) {
  return this.shouldJump(url);
}
if (/192\.168\.|^http\:\/\/app\.test/.test(url)){
  return this.shouldJump(url); // for local test
}
// TODO: has bug match,
if (/^https?\:\/\/[^\/\?]*[d\d+]*realmaster/.test(url)) {
  return this.shouldJump(url);
}
if (Platform.OS === 'ios' && navState.navigationType !== 'click') {
  // console.log('here');
  return true;
}
if(Platform.OS !== 'ios' && /youtube|youtu/.test(url)) {
  return true;
}
if (this.data && this.data.selector && this.data.selector !== '#callBackString') {
  //console.log("data not callBackString");
  return true;
}
if(/^rmCall|^file\:|about\:blank/.test(url)){
  return true;
}
if (/^(tel|geo|mailto|sms|market)\:/.test(url)) {
  // console.log('linking--->>>>'+url)
  // Linking.openURL(url);
  // NOTE: react-native-webview changed
  return false;
}
if (/^https?\:\/\/[^\/\?]*walkscore.com/.test(url)){
  return true; // services we use
}
if (/R\d\.M\d/.test(navState.url)){ // for full url
  return true; // for purposely outgoing link
}
console.log('Invalid Url in app -> ' + navState.url);
return false;
```
# 地图接受的参数
@param[stig] = bool #是否显示stig <- extractMapOptFromUrl
@param[measure] = bool #是否是measure <- extractMapOptFromUrl
@param[hMarker] = bool, house marker 1
@param[cMarker] = bool, red dot marker 2
@param[uMarker] = bool, blue dot marker 3 = 优先级， 越高越显示
@param[coords] = 
@param[gps] = bool #call locate me after map init
@param[saletp] = lease/sale
@param[oh] = bool
@param[mapmode] = assignment/commercial/exlisting/exrent/rent/projects/sold
@param['bdrms','gr','bthrms','min_lp','max_lp','oh','neartype','sold','sch','lpChg','recent','soldLoss'] = 
@param[ptype2] = str
@param[readFilter] = bool
@param[] = 
