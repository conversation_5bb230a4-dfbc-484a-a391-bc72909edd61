native branches:
  release
  production
  dev1=[6.3.0]/dev1.1=[6.3.0_change_sth]/dev1.2/..
  dev2=[6.4.0]/dev2.1=[6.4.0_change_sth]

native 开发步骤
0 [dev1]不是realname，改成开发用的branch [name], 比如[6.3.0]/[6.3.0_cmty_layer]/[6.3.0_refactor_startup]
1 新需求checkout [release] branch, 创建新branch [dev1], 如果新需求和[dev1]是同时开发，checkout用[dev1]
2 功能改完后PR，merge进总branch [dev1]
3 发布测试版本给测试人员
4 没问题后merge进[release]
5 发布正式版本,测试下列上线测试的内容
6 如果测试没问题执行发布步骤


测试步骤：
安卓和苹果（测试服+正式服）使用测试机制（testflight/apk)
- [] 下载后登录，注册过程(apple/google/facebook/wechat)
- [] 旧版本测试新web是否崩溃
- [] 首页升级提示
- [] 分享测试，微信（包括海报图片分享），email，sms
- [] 升级后登录，注册过程, 包括facebook/google切换账户登录
- [] push notify 测试
- [] camera QRCode test
- [] add to Calendar
- [] location and permissions
- [] init变量 pn，deviceId
- [] universal links, open app from link, eg: https://www.realmaster.cn/1.5/prop/detail?id=TRBC7033030&lang=zh-cn&fub_src=fub
- [] webview test tel: mailto: (DO NOT TEST IN EMULATOR!!!)

# react changes

* scrollEnabled: in IOS -> WeCard -> Edit, see if it scrolls
* wechat share: share-img = data:img
* splash display

# 3rd party libs

* vip save share img
* push notifications
* facebook/google/apple share/login
* mapsearch locate me
* prop +calendar
* wechat login
* camera scan qr code
* get page content
* off line test, show red screen

发布步骤：
1 更新ios/play console 新release和更新内容
2 ios/android 提交审核, +群里通知当前状态
3 ios 审核完后发布全部，+群里通知当前状态
3 安卓发布20% 观察crash report，（2days），如果没问题就发布100%（如果上版本crash很多可以40%），+群里通知当前状态
4 更新版本号
5 更新下载链接 dl.realmaster.cn/RealMaster.apk，+群里通知当前状态
6 merge [release]->[production]


线上app需要紧急修改步骤：
1 修改checkout [production] branch -> [6.3.1_fix_crash]
2 发布测试版，测试修复的内容，+群里通知测试
3 如没有问题遵循测试步骤+发布步骤