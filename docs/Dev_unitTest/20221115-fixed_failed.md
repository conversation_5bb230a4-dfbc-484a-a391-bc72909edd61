branch:[fix_unittest]
# 已测试, [x] = pass
- [x] addrSuggest
- [x] admin
- [x] city
- [] cmty @liurui
- [] contactRealtor @charlie/liurui/luoxiaowei?
- [x] coop
- [] edm @charlie/liurui/luoxiaowei?
- [] elasticsearch @rain
- [x] findschool
- [] geocoder #due to require in saveToMaster @charlie
- [] importWithHook @charlie
- [x] lib # except kafka @luoxiaowei
- [] libapp @luoxiaowei
- [] locallogic @luoxiaowei
- [] payment @liurui
- [] propertyImport @charlie
- [x] pushnotify
- [x] saves
- [x] school
- [x] stigmatized
- [x] topagent
- [x] transit @rain
- [x] user #14 fail @rain
- [] userweblogin @luoxiaowei
- [x] watch


# 已处理
1.addrSuggest
	config4中mapbox 缺少address
2.coopStdFun
	登陆用户名输入错误，与插入user中数据不符
3.edmBatch
	接口返回值变动，需要调整tests测试用例
4.ESPropSearch
	本地数据清除
5.dataImport
	需要在cfg配置rni数据库权限
6.helpers_date
	本地时区问题
7.common
	imgServerDlAddr 配置出错
8.condos
	返回做了调整
9.properties
	本地时区问题
	5/22/2016. 应为undefined
10.saveToMaster
	addHistory未exports
11.topUpAndTax	before
	CA QC值填写有误
12.02_propertyImport
	传参缺少SqftsAggregate
13.properties
	getStatusLongName返回变更
14.saves
	lib/appBase.coffee  需要补充函数isIOSDevice
15.stigmatized
	返回的res参数写成了list
	res.ckup返回statuscode为200
16.test
	没有对应的batch文件(修改路径即可)
17.transitStdFun
	需要dump sysdata,数据导入写法错误
18.0101_registerAndLogin
	增加了邮箱验证，注册返回内容变更，注册后由于未验证不可登陆

# 待处理
1.cmtySearch
	useSearchEngine使用mongo(已解决)
	get user edm props  数据结构变更，需要修改导入数据(待处理)
2.contactRealtor
	TITLE_STR引用位置错误(已解决)
	返回数据结构存在变更(待处理)
3.newGeocoder(待处理)
	coded undefined
	DDF		null
	gGeoCache save upsert
4.01_propertyImport
	需要配置tmp数据库信息(已解决)
	commonFixture/fixtures/vow导入失败,git pull时文件损坏(已解决)
	传参缺少SqftsAggregate(已解决)
	impMappingProperty引用formatPtype函数错误，应改为impMappingBcreHelper(已解决)
	取消PropertiesNew的判断(已解决)
	返回数据结构变更，测试用例需要调整(待处理)
5.03_propertyImportMerge
	返回数据结构变更，测试用例需要调整(待处理)
6.transitHelper
	error：Cannot redefine property: __stack，在lib/debug.coffee没有发现重新定义的地方