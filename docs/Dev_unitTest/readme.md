## 前置条件
1. 改hosts文件，`127.0.0.1       localhost app.test www.test #www.avionhome.com`
2. clone cfglocal.test branch, testD1 is for d1 test server

# usage
checkout cfglocal [test] branch.
edit base.yml, set port and test db
edit config4.coffee, set local params
```
coffee ./setup.coffee
./mate4.sh compile
```
如果改了apps文件，需要重新compile
`./mate4.sh compile`


`./mate4.sh compile [folder] ` to only compile certain folder

##如果需要插入数据，在测试文件里写如下
```
helpers.checkAndsetUpFixture __dirname, ()->
  setTimeout(done, 2500)
```

## set up env（should already installed in package.json）
### API(http) framework, mocha, superttest,should
- [ ] npm install mocha
- [ ] npm install should
- [ ] npm install supertest
- [ ] npm install nyc

https://github.com/mochajs/mocha
https://github.com/visionmedia/supertest

should
https://github.com/shouldjs/should.js
http://shouldjs.github.io/


### test batch
use params b to not start web server.
eg:
`./test.sh -f user/0101_registerAndLogin.js -b`

### test one file only
eg:
`./test.sh -f user/0101_registerAndLogin.js`
 
### tips for test lib file with functions only.
run ./test.sh will load a lot resources and is slow. could improve ./test.sh to support lib function test only.

compile file to js, then run mocha filename.js to do function test
eg:
```
cd unitTest/libapp
coffee -b -c propSqft.coffee
mocha propSqft.js
```

### run case in prov folder:
`./test.sh -m prov`

### run all case:
`./test.sh`

### generate report
  `./test.sh -r`
  测试全部并生成report.目前不支持部分文件的report

### coverage
find coverage report in src/coverage


###
使用 helpers.MSG_STRINGS 里的error去比较error的string

### rules
- design it to be dead-simple, short, abstraction-free, flat, delightful to work with
- Include 3 parts in each test name（what is being test，scenario, expect result）
- Aaa pattern, 测试case3部分，set up 环境（数据准备，登陆等），执行测试（1句话），测试结果
- Stick to black-box testing: Test only public methods
- fast-check。 自动测试多种随机输入
- Avoid global test fixtures and seeds, add data per-test
- cross check all possible input


put coffee file in unitTest, built file in unitTestJs.

### 测试特定文件或者module, see: passedFiles.csv/passedModules.csv
```
./test.sh -c passedFiles.csv
./test.sh -v passedModules.csv
```
or
```
./testPassed.sh -e <EMAIL>
```
passedFiles 和 passedModules 最后一行是空行


### prepare data
put json file under fixtures of each module. json file has to be format of extended json for mongo, pay attention to objectId and date.
can user mongoexport to get the json
eg:
```
    {"_id":{"$oid":"5fd14f5ce0389706e50ed084"},"eml":["<EMAIL>"],"nm":"yufengyw20011","src":"iphone:appDebug","id":"U2176745510","edmGrp":3,"locale":"zh-cn","splang":["English"],"roles":["_dev","_admin","vip_plus"],{"$date":"2021-01-20T18:20:19.09Z"},"city":{"city":"Toronto","prov":"ON","lat":43.7052296823361,"lng":-79.3998191054558}},
unitTest
  city
    fixtures
      chome
        user.json
      listing
        property.json
```




### API(http/lib/model) test only for now
# frame work?
# After model can run externally

#datas: db dump -> restore
#tests:

https://karma-runner.github.io/latest/index.html
https://mochajs.org/#getting-started
https://jasmine.github.io/setup/nodejs.html

1. setup env input data/db data
2. call API, compare rets
3. db look up
4. report 99% usable

env setup:

http:
  prop sql/mongo (geoq,addr,lp,sp)
  school
  register
  wecard
  imgInsert

lib:
  lib.user
  lib.prop
  libapp

model:
  property
  user
  wecard
# NOTE: 
model only works insode describe.it cb fns, otherwise will be undefined

### frontend test
https://testing-library.com/docs/
appium

