## description
support client site using appweb oauth login

## files
src/lib/oauthProvider.coffee
src/model/oauthProviderGrant.coffee
src/apps/80_sites/AppRM/auth/provider.coffee
src/apps/80_sites/WebRM/login.coffee
src/webDev/components/login.vue

## setup
in rmconfig local.ini

```
[oauthProvider]
secret = "provider_secret_key"

  [[oauthProvider.clients]]
  clientId = "report_rentals"
  clientSecret = "client_secret_key"
  redirectUri = "http://CLIENT_APP.com/v1/auth/oauth/callback"
```

其中: oauthProvider.clients 的配置需要与 client 网站的 local.ini 配置一致, 例如: 

```
# 这是在 client 网站的配置, 不属于 rmconfig
[oauthClient]
clientId = "report_rentals"
clientSecret = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"
redirectUrl = "http://CLIENT_APP.com/v1/auth/oauth/callback"
authUrl = "https://realmaster.com/provider/authorize"
tokenUrl = "https://realmaster.com/provider/token"
userInfoUrl = "https://realmaster.com/provider/userinfo"
```

note: 未来会使用auth.realmaster.com和go backend, authUrl/tokenUrl/userInfoUrl 根据实际情况修改

## flow

1. Authorization Request

The client application initiates the OAuth flow by redirecting the user to the RealMaster authorization endpoint.

Endpoint:
GET /provider/authorize

Example URL:
http://realmaster.com/provider/authorize?
  client_id=demo-client&
  redirect_uri=http://client-app.com/callback&
  client_state=xyz123

2. User Authentication

If user is not authenticated, RealMaster redirects to login page.

Endpoint:
GET /www/login

Example URL:
http://realmaster.com/www/login?
  client_id=demo-client&
  redirect_uri=http://client-app.com/callback&
  client_state=xyz123

3. User Login

The user enters their credentials on the RealMaster login page.

Endpoint:
POST /login
Content-Type: application/json

Request Body:
{
  "eml": "<EMAIL>",
  "pwd": "userpassword",
  "oauthClient": {
    "clientId": "demo-client",
    "redirectUri": "http://client-app.com/callback",
    "clientState": "xyz123",
  }
}

4. Authorization Code Generation

After successful authentication, RealMaster generates an authorization code and returns it to the client.
Success Response:
{
  "ok": 1,
  "clientUrl": "http://client-app.com/callback?code=AUTH_CODE&client_state=xyz123"
}

5. Token Request

The client exchanges the authorization code for access and refresh tokens.
Endpoint:
POST /provider/token
Content-Type: application/json

Request Body:
{
  "code": "received-auth-code",
  "client_id": "demo-client",
  "redirect_uri": "http://client-app.com/callback",
  "timestamp": "**********",
  "signature": "example_signature"
}

Note: client_secret will be replaced by HMAC for more safety

6. Access Token Response

RealMaster validates the request and issues both access and refresh tokens.

Success Response:
HTTP/1.1 200 OK
Content-Type: application/json

{
  "access_token": "JWT_ACCESS_TOKEN",
  "refresh_token": "JWT_REFRESH_TOKEN",
  "token_type": "Bearer",
  "expires_in": 300
}


7. API Request with Access Token

The client uses the access token to request user data.

Endpoint:
GET /provider/userinfo
Authorization: Bearer JWT_ACCESS_TOKEN

8. Protected Resource Response

RealMaster validates the access token and returns the requested user data.
Success Response:
{
  "id": "user123",
  "email": "<EMAIL>",
  "name": "User Name",
  <!-- "phone": "**********" -->
}

9. Token Refresh Flow

When the access token expires, the client can use the refresh token to obtain new tokens.

Endpoint:
POST /provider/token
Content-Type: application/json

Request Body:
{
  "grant_type": "refresh_token",
  "refresh_token": "oauth_refresh_token",
}

Success Response:
{
  "access_token": "NEW_JWT_ACCESS_TOKEN",
  "refresh_token": "NEW_JWT_REFRESH_TOKEN",
  "token_type": "Bearer",
  "expires_in": 300
}
