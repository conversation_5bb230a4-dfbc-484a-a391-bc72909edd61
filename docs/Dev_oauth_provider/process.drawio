<mxfile host="65bd71144e">
    <diagram id="oauth_flow" name="OAuth 2.0 Flow">
        <mxGraphModel dx="565" dy="237" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2200" pageHeight="1900" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="browser" value="Browser" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="200" y="80" width="120" height="1800" as="geometry"/>
                </mxCell>
                <mxCell id="client" value="Client Server&#xa;(Go)" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="600" y="80" width="120" height="1800" as="geometry"/>
                </mxCell>
                <mxCell id="check_tokens" value="2. Check cookies for oauth_refresh_token" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="client" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="60" y="120" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="90" y="120"/>
                            <mxPoint x="90" y="150"/>
                        </Array>
                        <mxPoint x="60" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="provider" value="RealMaster&#xa;OAuth Provider" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;fillColor=#ffe6cc;strokeColor=#d79b00;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="1000" y="80" width="120" height="1800" as="geometry"/>
                </mxCell>
                <mxCell id="resource_server" value="RealMaster&#xa;Resource Server" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="1400" y="80" width="120" height="1800" as="geometry"/>
                </mxCell>
                <mxCell id="click" value="1. Click &#39;Continue with RealMaster&#39;&lt;br&gt;URL: http://site1.local1:3000/&lt;br&gt;(request with cookie if has)" style="html=1;verticalAlign=bottom;endArrow=block;entryX=0;entryY=0;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="260" y="160" as="sourcePoint"/>
                        <mxPoint x="660" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="token_check_frame" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="250" width="1100" height="1410" as="geometry"/>
                </mxCell>
                <mxCell id="no_tokens" value="[No valid oauth_refresh_token]" style="text;html=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="150" y="250" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="has_refresh" value="[Has valid oauth_refresh_token]" style="text;html=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="150" y="1400" width="130" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="state" value="3. Generate state (UUID)&#xa;Store in states map" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
                    <mxGeometry x="-0.0102" relative="1" as="geometry">
                        <mxPoint x="660" y="300" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="690" y="300"/>
                            <mxPoint x="690" y="330"/>
                        </Array>
                        <mxPoint x="660" y="330" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="redirect" value="4. HTTP 302 Redirect&lt;br&gt;/provider/authorize?&lt;br&gt;client_id=demo-client&amp;amp;&lt;br&gt;redirect_uri=http://site1.local1:3000/callback&amp;amp;&lt;br&gt;client_state={state}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="400" as="sourcePoint"/>
                        <mxPoint x="260" y="400" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="auth_request" value="5. GET Request with auth params" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="260" y="480" as="sourcePoint"/>
                        <mxPoint x="1060" y="480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="check_session" value="6. Check session&#xa;req.session.get(&#39;user&#39;)" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="520" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="1090" y="520"/>
                            <mxPoint x="1090" y="550"/>
                        </Array>
                        <mxPoint x="1060" y="550" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="login_alt_frame" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="150" y="600" width="1000" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="not_logged_in" value="[Not logged in]" style="text;html=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="200" y="600" width="90" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="show_login" value="7a. HTTP 302 Redirect to login&#xa;/www/login with OAuth params" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="650" as="sourcePoint"/>
                        <mxPoint x="260" y="650" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="login_form" value="8a. POST /login&#xa;{eml, pwd, recaptcha, oauthClient}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="260" y="730" as="sourcePoint"/>
                        <mxPoint x="1060" y="730" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="logged_in" value="[Already logged in]" style="text;html=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="200" y="830" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="gen_auth_code" value="9. Generate auth code&lt;br&gt;Store auth code use mongo auto expire" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="880" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="1090" y="880"/>
                            <mxPoint x="1090" y="910"/>
                        </Array>
                        <mxPoint x="1060" y="910" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="redirect_code" value="10. HTTP 302 Redirect&lt;br&gt;/callback?code={auth_code}&amp;amp;state={UUID}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="980" as="sourcePoint"/>
                        <mxPoint x="260" y="980" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="callback" value="11. GET /callback" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="260" y="1080" as="sourcePoint"/>
                        <mxPoint x="660" y="1080" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="token_exchange" value="12. POST /provider/token&lt;br&gt;{code, client_id, redirect_uri, signature, time_stamp}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="1180" as="sourcePoint"/>
                        <mxPoint x="1060" y="1180" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="return_tokens" value="13. Return tokens&#xa;{&#xa;  access_token: JWT (exp: 5m),&#xa;  refresh_token: JWT (exp: 30d)&#xa;}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="1280" as="sourcePoint"/>
                        <mxPoint x="660" y="1280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="store_tokens" value="14. Set cookie&lt;br&gt;ouath_refresh_token=refresh_token" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="1320" as="sourcePoint"/>
                        <mxPoint x="260" y="1320" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="refresh_request" value="15. POST /provider/token&lt;br&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{client_id,&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;grant_type, refresh_token&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;, signature, time_stamp}&lt;/span&gt;" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="1480" as="sourcePoint"/>
                        <mxPoint x="1060" y="1480" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="verify_refresh" value="16. Verify refresh token&lt;br&gt;Check refresh token not used&lt;br&gt;then mark used(Invalidate)" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="1520" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="1090" y="1520"/>
                            <mxPoint x="1090" y="1550"/>
                        </Array>
                        <mxPoint x="1060" y="1550" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="return_new_tokens" value="17. Return new tokens&#xa;{&#xa;  access_token: JWT (exp: 5m),&#xa;  refresh_token: JWT (exp: 30d)&#xa;}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1060" y="1600" as="sourcePoint"/>
                        <mxPoint x="660" y="1600" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="store_new_tokens" value="18. Set new cookie&lt;br&gt;oauth_refresh_token=refresh_token" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="1640" as="sourcePoint"/>
                        <mxPoint x="260" y="1640" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="api_request" value="19. GET /resource/userinfo&#xa;Authorization: Bearer {access_token}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="1730" as="sourcePoint"/>
                        <mxPoint x="1460" y="1730" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="return_user" value="20. Return user info&#xa;{id, email, name, phone}" style="html=1;verticalAlign=bottom;endArrow=block;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1460" y="1780" as="sourcePoint"/>
                        <mxPoint x="660" y="1780" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="" style="endArrow=none;dashed=1;html=1;exitX=0.997;exitY=0.509;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.006;entryY=0.509;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;dashPattern=1 4;" parent="1" source="login_alt_frame" target="login_alt_frame" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="699" y="533" as="sourcePoint"/>
                        <mxPoint x="749" y="483" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="3" value="" style="endArrow=none;dashed=1;html=1;exitX=0.996;exitY=0.794;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.004;entryY=0.795;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;dashPattern=1 4;" parent="1" source="token_check_frame" target="token_check_frame" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1155.5" y="1370" as="sourcePoint"/>
                        <mxPoint x="164.5" y="1370" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>