
https://developers.google.com/transit/gtfs/reference/?csw=1
stop.txt fileds: stop_id,stop_name,stop_lat,stop_lon,zone_id,stop_url,location_type,parent_station,wheelchair_boarding

location_type
* 0 or blank - Stop. A location where passengers board or disembark from a transit vehicle.
* 1 - Station. A physical structure or area that contains one or more stop.
* 2 - Station Entrance/Exit. A location where passengers can enter or exit a station from the street. The stop entry must also specify a parent_station value referencing the stop ID of the parent station for the entrance.

#todo. current data only has 0 or blank. may have 2 in future

{
    "_id" : ObjectId("5c78437638ffee6c3c88aac5"),
    "city" : "Toronto",
    "prov" : "Ontario",
    "stop_id" : "WE",
    "type" : "UP",
    "lat" : "43.70058",
    "lng" : "-79.51355",
    "loc" : {
        "type" : "Point",
        "coordinates" : [
            "-79.51355",
            "43.70058"
        ]
    },
    "loc_type" : "0",
    "nm" : "Weston GO/UP",
    "url" : "http://www.upexpress.com/SchedulesStations/WestonStation",
    "wc" : "1",
    "zone" : "04"
}


# add weight to Transit stop