## 数据导入部分

### 数据说明：
目前我们导入的地铁文件有两种格式
1. GTFS （google transit 标准。数据结构参考#https://developers.google.com/transit/gtfs/reference/）
'TTC','GO','UP','Calgary','Edmonton','Vancouver','YRT','Barrie','KingstonBus','MississaugaBus','WaterlooGRT'
2. source https://transitfeeds.com/l/32-canada
   1. AB
      1. Calgary https://transitfeeds.com/p/calgary-transit/238
      2. Edmonton https://transitfeeds.com/p/edmonton-transit-system/27
   2. BC
      1. Vancouver
         1. BCFerries https://transitfeeds.com/p/bc-ferries/916
         2. Translink https://transitfeeds.com/p/translink-vancouver/29
      2. Victoria https://transitfeeds.com/p/bc-transit/695
   3. NS Halifax https://transitfeeds.com/p/metrotransit/36
   4. ON
      1. Barrie https://transitfeeds.com/p/barrie-transit/522
      2. TODO: other city all from transit feed.copy here
   5. Montreal 
      1. exo: https://exo.quebec/fr/a-propos/donnees-ouvertes
      2. Montreal stm ：https://transitfeeds.com/p/societe-de-transport-de-montreal/39
3. 原始数据位置不一定准确，eg：ttc stop: SPADINA STATION - NORTHBOUND PLATFORM，loc错误。
4. Montreal 数据属于EXO的放在一起。
   1. Montreal STM routes里删掉 前四个地铁，gtfs里montreal的地铁线画的不详细，使用的是osm的数据。如果以后gtfs数据更好可以去掉osm的
   2. MontrealSub的数据是osm得到的，需要单独导入。https://wiki.openstreetmap.org/wiki/Montreal_Metro

## Note: transitfeeds is not updated and will be deprecated.  use https://database.mobilitydata.org/ instead
### 数据文件位置：
appd3@*************:~/import_do_not_remove/transitData

### 导入数据文件脚本 transi_route,transit_stop：
./start.sh lib/batchBase.coffee batch/transit/transitRouteImportV2.coffee [filepath] -p [prov] -r [region] -a [agency]
同一条route有多个shape，对应地图上不同的点，有些是区间车，这些根据起始点来判断，只留下长的一个shape。

eg: import ttc
./start.sh lib/batchBase.coffee batch/transit/transitRouteImportV2.coffee ../batch/transit/transitData -p ON -r Toronto -a TTC
eg: import ALL
nohup ./start.sh lib/batchBase.coffee batch/transit/transitRouteImportV2.coffee ~/transit/ > ./logs/importTransit.log 2>&1 &

### 导入MontrealSub 地铁
./start.sh lib/batchBase.coffee  batch/transit/osmTransitImport.coffee MontrealSub ../batch/transit/transitData/
### 给地铁增加城市。
./start.sh batch/boundary/addCity2Col.coffee force transitStop

### mergeStop and add weight
 ./start.sh lib/batchBase.coffee batch/transit/mergeStop.coffee
 合并90米之内的点
 add merged tag to stop which is merged to other stop
 cluster stop has ids includs all merged stop
 crluste stop might be changed each time re run batch
 DO NOT REFER crluste stop IN OTHER COLLECTION
### zoom: 
  if stop is merged by several stops, it has zoom 14
  if stop is merged to another stop. it has zoom 17, otherwise, no zoom 



### addweight  only (run only when needed, already in mergeStop)
 ./start.sh lib/batchBase.coffee batch/transit/addStopWeight.coffee
 增加weight。merged props weight is 0.

数据结构：
transit_stop:
mapping from GTFS stops.txt.
eg:
{
    "_id" : "ON_TTC_7174", # prov_agency_stopid
    "agency" : "TTC",
    "code" : "247", # from csv
    "fnm" : "BAY ST AT FRONT ST WEST (UNION STATION)", # from csv
    "geometry" : {
        "type" : "Point",
        "coordinates" : [ 
            -79.378935, 
            43.645849
        ]
    },
    "lat" : 43.645849, # from csv
    "lng" : -79.378935, # from csv
    "nm" : "Bay St At Front St West", # abbr of fnm
    "prov" : "ON",
    "region" : "Toronto",
    "status" : "A",
    "stop_id" : "7174", # from csv
    "type" : "Feature",
    "wc" : "1",
    "zoom" : 17,
    "route_tp" : [ 
        "Bus"
    ], # calculated from routes.
    "routes" : [ 
        {
            "_id" : "ON_TTC_61403_833125",
            "route_id" : "61403",
            "route_tp" : "Bus",
            "fnm" : "Bay",
            "nm" : "Bay"
        }
    ],# calculated from routes.
    "city" : "Toronto",
    "weight" : 7
}
transit_route_shape:
suppose one shape only exist in one route
one route may have multiple shapes。
one shape has one bnds, compress to bnds14,bnds17
bnds.features.properties.stops has sequeced stops of this routes


NOTE:一个shape可能有多个route, GO.来去的两条线，不同routeid，同一个shape，目前按一个route处理的。




##### DEPRECATED:
### merge same region，生成一个区域内的地铁，到transit_route_region
./start.sh batch/transit/transitRouteMerge.coffee
## 根据数据生成图片
使用qgis的pyshon plugin，生成各collection对应的图片，压缩并根据需求切割。目前只使用了transit_route_region的图片

### 压缩transit route region图片，skip if already has image

1. copy batch/transit/qgisExportTransitRouteFinal.py 到qgis 的python plugin中，然后运行，得到transit route的图片和pgw文件,备份存放到appd3@*************:~/import_do_not_remove/transitData/transitRouteRegionWithStop
   
2. 将pwg文件copy到src/mediafile/transitRouteRegionWithStop 下
3. 将png文件copy到public/img/map-layer/transitRouteRegionWithStop

4. 添加image path 和 word file到collection,该步骤需要原始的图片文件，谨慎执行。
计算图片的w,h和bbox。
./start.sh batch/transit/parseTransitWorldFile.coffee tansit_merge

5. 压缩并切割png图片, 该程序目前不支持重复run，需要原始的图片文件，需要使用时注意备份。
./start.sh batch/transit/imageCompress.coffee

重命名：
find . -name "*-fs8.png" -exec sh -c 'mv "$1" "${1%-fs8.png}.png"' _ {} \;

### 其他文件目前没有图片，需要的情况可以再次生成,qgisExportTransitRouteFinal.py







### compress transit
 ./start.sh lib/batchBase.coffee batch/transit/compressRoutes.coffee
 增加bnds14，bnds17

 ###  add bboxP

 run this in mongo to add bboxP
db.getCollection('transit_route').find({}).forEach(function(doc) {
    if (doc.bbox) {
            bboxP = {
     "type":"Polygon",
     "coordinates":[[
      [doc.bbox[0],doc.bbox[1]],
         [doc.bbox[2],doc.bbox[1]],
         [doc.bbox[2],doc.bbox[3]],
         [doc.bbox[0],doc.bbox[3]],
         [doc.bbox[0],doc.bbox[1]]
     ]]
     }
     db.getCollection('transit_route').updateOne({_id:doc._id},{$set:{bboxP:bboxP}})
    }
});