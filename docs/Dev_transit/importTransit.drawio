<mxfile host="65bd71144e">
    <diagram id="gD02Q_T7gqjt6KUmYWO0" name="importTransit">
        <mxGraphModel dx="957" dy="569" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="EJBZIEOzCeof_mpdvUf_-6" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="jgei_27ixi6Q01Zq4EVH-2" target="jgei_27ixi6Q01Zq4EVH-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="jgei_27ixi6Q01Zq4EVH-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;addCity2Col.coffee&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;add city to transit stop&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="170" y="260" width="210" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="jgei_27ixi6Q01Zq4EVH-3" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;mergeStop.coffee&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;merge stops within 90 meters,&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;font-size: 10px&quot;&gt;add stop weight( higer as more routes passed)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="380" width="230" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="EJBZIEOzCeof_mpdvUf_-3" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.431;entryY=0.007;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="vVEqP45EC-b1YXThL3-Y-2" target="jgei_27ixi6Q01Zq4EVH-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="vVEqP45EC-b1YXThL3-Y-2" value="&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;transitRouteImportV2.coffee&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;import GTFS format data&lt;/font&gt;&lt;br&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="40" y="70" width="210" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="EJBZIEOzCeof_mpdvUf_-4" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.647;entryY=-0.01;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="EJBZIEOzCeof_mpdvUf_-2" target="jgei_27ixi6Q01Zq4EVH-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="EJBZIEOzCeof_mpdvUf_-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;osmTransitImport&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;import montreal sub&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="290" y="70" width="170" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="cWXP-koLmyBx5j6wYi2Z" name="transitRouteImportV2.coffee">
        <mxGraphModel dx="957" dy="569" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0K3QigbEpQCfruQ517XP-0"/>
                <mxCell id="0K3QigbEpQCfruQ517XP-1" parent="0K3QigbEpQCfruQ517XP-0"/>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-1" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;batch/transit/transitRouteImportV2&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="50" y="50" width="260" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;transit_stop&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=10;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1010" y="255" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-3" value="&lt;div style=&quot;text-align: left&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=10;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="125" y="255" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-4" value="&lt;h1&gt;&lt;span&gt;&lt;font style=&quot;font-size: 14px&quot;&gt;Feeds Example:&lt;/font&gt;&lt;/span&gt;&lt;/h1&gt;&lt;div&gt;&lt;span&gt;&lt;font style=&quot;font-size: 14px&quot;&gt;&lt;br&gt;&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;p&gt;TTC:&lt;/p&gt;&lt;p&gt;agency.txt: 运营公司信息，eg:TTC, 可能会有子公司&lt;/p&gt;&lt;p&gt;calendar_dates.txt&lt;/p&gt;&lt;p&gt;routes.txt&lt;/p&gt;&lt;p&gt;shapes.txt&lt;/p&gt;&lt;p&gt;stop_times.txt&lt;/p&gt;&lt;p&gt;stops.txt&lt;/p&gt;&lt;p&gt;trips.txt&lt;/p&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="60" y="90" width="300" height="380" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-6" value="transit_agency&lt;br&gt;eg:ON_Toronto_UP_UPExpress" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1020" y="130" width="220" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-17" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-7" target="dfwcQaSzh-XcfWP3f4-j-16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-7" value="agency.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="510" y="130" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-20" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-9" target="dfwcQaSzh-XcfWP3f4-j-19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-9" value="stops.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="510" y="255" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-23" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-11" target="dfwcQaSzh-XcfWP3f4-j-22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-11" value="shapes.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="510" y="370" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-27" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-12" target="dfwcQaSzh-XcfWP3f4-j-26">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-12" value="trips.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="500" y="495" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-29" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-13" target="dfwcQaSzh-XcfWP3f4-j-28">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-13" value="stop_times.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="500" y="620" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-31" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-14" target="dfwcQaSzh-XcfWP3f4-j-30">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-14" value="routes.txt" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="520" y="890" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-18" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-16" target="dfwcQaSzh-XcfWP3f4-j-6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-16" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importAgency&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="740" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-21" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-19" target="dfwcQaSzh-XcfWP3f4-j-2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-19" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importStop&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="750" y="265" width="110" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-22" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importShapes&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;read shape.txt,&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;generate&amp;nbsp;&lt;span style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;gOrdered_shapes&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="715" y="365" width="265" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-26" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importTrips&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;read trips.txt&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;appendTripInfoToShape&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;一个shape 对应多个trip，shape有位置信息，可以理解为线路，trip可以理解为不同的班次&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="715" y="490" width="270" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-28" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importTimes&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;read stop_times.txt&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;connect stops of trip to shape，将线路上的站点和shape关联起来。&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="720" y="620" width="260" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-37" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-30" target="dfwcQaSzh-XcfWP3f4-j-34">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-39" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-30" target="dfwcQaSzh-XcfWP3f4-j-38">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-30" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;importRoute&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;read routes.txt&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;transformRoute&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;mergeDupShapeOfRoute: 一个shape如果有多个trip，取最长的一个。&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;saveRoute&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;saveRouteShape， route_shape has bnds14,bnds17, display at different zoom&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="750" y="850" width="350" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-32" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontSize=12;fontFamily=Helvetica;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1250" y="470" width="130" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-36" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-33" target="dfwcQaSzh-XcfWP3f4-j-2">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1380" y="295"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-33" value="&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left ; background-color: rgb(30 , 30 , 30)&quot;&gt;setRouteForStop&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1345" y="400" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-35" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-34" target="dfwcQaSzh-XcfWP3f4-j-33">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1330" y="540" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-34" value="transit_route" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1330" y="820" width="150" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-40" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.076;entryY=0.095;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;fontSize=12;" edge="1" parent="0K3QigbEpQCfruQ517XP-1" source="dfwcQaSzh-XcfWP3f4-j-38" target="dfwcQaSzh-XcfWP3f4-j-34">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-38" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;transit_route_shape&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1060" y="540" width="170" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-41" value="&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;one route may have multiple shapes。&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;fld: shapes&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontFamily=Helvetica;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="1195" y="630" width="270" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="dfwcQaSzh-XcfWP3f4-j-42" value="" style="whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="0K3QigbEpQCfruQ517XP-1">
                    <mxGeometry x="730" y="1160" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="oGZ3dDrI_vPyL5f9qBMt" name="mergeStop">
        <mxGraphModel dx="957" dy="569" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-0"/>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-1" parent="NNOjq5ZXHQFOdYmfT3fN-0"/>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;mergeStop.coffee&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;合并同名且位置靠近的站点，合并位置相同的站点。减少显示的站点的数量。给站点添加权重。用于显示排序&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontFamily=Helvetica;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="35" y="60" width="590" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-8" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-3" target="NNOjq5ZXHQFOdYmfT3fN-7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-3" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;aggregateStopAtSameLoc&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="250" y="200" width="190" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-17" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-4" target="NNOjq5ZXHQFOdYmfT3fN-16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-4" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;addMergeStopOfSameLocToMap&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="585" y="350" width="195" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-10" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="590" y="480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-5" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;mergeStopWithSameName&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="245" y="450" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-26" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-6" target="NNOjq5ZXHQFOdYmfT3fN-21">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-6" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;addStopWeight&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="515" y="810" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-9" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-7" target="NNOjq5ZXHQFOdYmfT3fN-4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-7" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;transit_stop_group_loc&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;temp table&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="580" y="187.5" width="210" height="95" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-13" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-11" target="NNOjq5ZXHQFOdYmfT3fN-12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-11" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;transit_stop_group&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="540" y="440" width="150" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-19" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-12" target="NNOjq5ZXHQFOdYmfT3fN-6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-12" value="calculate cluster of&amp;nbsp; stops with same name&lt;br&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;saveMergeStopOfSameName&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;mergeStopWithSameNm and StopWithSameLoc&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;saveMergedStopWithLocToDB&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="403.75" y="600" width="422.5" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-18" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-16" target="NNOjq5ZXHQFOdYmfT3fN-12">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="910" y="660"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-16" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;global object：&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;stopsOfSameLocMap&lt;/div&gt;" style="shape=internalStorage;whiteSpace=wrap;html=1;backgroundOutline=1;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="870" y="340" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-24" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-21" target="NNOjq5ZXHQFOdYmfT3fN-3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-25" style="edgeStyle=orthogonalEdgeStyle;html=1;fontFamily=Helvetica;fontSize=12;" edge="1" parent="NNOjq5ZXHQFOdYmfT3fN-1" source="NNOjq5ZXHQFOdYmfT3fN-21" target="NNOjq5ZXHQFOdYmfT3fN-5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="NNOjq5ZXHQFOdYmfT3fN-21" value="transit_stop" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="NNOjq5ZXHQFOdYmfT3fN-1">
                    <mxGeometry x="10" y="390" width="100" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>