从第三方获取数据 https://api.openweathermap.org/data/3.0/onecall?lat={lat}&lon={lon}&exclude={part}&appid={API key}

#### 需求及过程
1. showing保存时，showing选择日期和第一个开始时间，根据房源信息可获取city，prov等信息。(前提是未完成的showing，其次选择的开始时间必须在48小时内才会调用天气接口）
2. weather数据库根据看房时间是否小于等于_exp2d以及city查询天气信息，如果有则在hourly数组中取出前端需要的天气信息。
3. 如果weather数据库没有获取到数据，则根据city/subcity在数据库的 boundary表中获取center point,调用第三方天气接口获取数据,解析从第三方获取的数据，拿到weather表需要的数据进行保存，返回前端需要的天气信息。
4. 打开保存过的showing，且符合选择的开始时间在48小时内，则重复2-3步骤

#### weather Collection

放置在data库里,该表记录48h后过期
目前从第三方获取的数据单位设置为 metric（温度以摄氏度表示，风速以米/秒表示）

| field   | data type | description      | notes                                                       |
| ------- | --------- | ---------------- | ----------------------------------------------------------- |
| _id     | Auto      |                  | 自动生成                                                      |
| city    | String    | city             |  唯一索引                                                    |
| prov    | String    | province         |                         |
| lng     | Double    | longitude       |                         |
| lat     | Double    | latitude        |                         |
| _mt     | Date      | Time to obtain the record|                         |
| _exp2d  | Date      | Expiration time |                        |
| current | Object    | Current weather data|                                        |
| .dt     | Date      | Current time     |  |
| .sunrise| Date      | Sunrise time   |                                                   |
| .sunset | Date      | Sunset time   |                                                   |
| .temp   | Double    | Temperature(Celsius)|                                                   |
| .fellike| Double    | feels like(Celsius)|                                                             |
| .pressure| Int      | pressure(hPa)        |                                                             |
| .hmdty  | Int       | humidity(%)     |                                                             |
| .dewpoint| Double   | Atmospheric temperature(Celsius)|                                             |
| .uv     | Double    | Current UV index |                                                             |
| .clouds | Int       | Cloudiness(%)|                                                             |
| .visb   | Int       | Average visibility, metres|                             |
| .windsp | Double    | wind speed(metre/sec)|                             |
| .winddeg| Int       | Wind direction, degrees(meteorological)|                             |
| .windgu | Double    | (where available)Wind gust(metre/sec)       |                      |
| .weather| String    | main weather    |                         |
| .desc   | String    | weather description|                         |
| .rain   | Double    | (where available) Precipitation, mm/h|eg.{"1h": 2.44}                       |
| .snow   | Double    | (where available) Precipitation, mm/h|eg.{"1h": 2.44}                       |
| hourly  | Array     | Hourly forecast weather data     |                                        |
| .dt     | Date      | Time of the forecasted data     |  |
| .temp   | Double    | Temperature(Celsius)|                                                   |
| .fellike| Double    | feels like(Celsius)|                                                             |
| .pressure| Int      | pressure(hPa)        |                                                             |
| .hmdty  | Int       | humidity(%)     |                                                             |
| .dewpoint| Double   | Atmospheric temperature(Celsius)|                                             |
| .uv     | Double    | Current UV index |                                                             |
| .clouds | Int       | Cloudiness(%)|                                                             |
| .visb   | Int       | Average visibility, metres       |                             |
| .windsp | Double    | wind speed(metre/sec)|                             |
| .winddeg| Int       | Wind direction, degrees (meteorological)|                             |
| .windgu | Double    | (where available)Wind gust(metre/sec)      |                      |
| .weather| String    | main weather    |                         |
| .desc   | String    | weather description|                         |
| .pop    | Double    | Probability of precipitation(0-1,where 0 is equal to 0%, 1 is equal to 100%)|                         |
| .rain   | Double    | (where available) Precipitation, mm/h| eg.{"1h": 2.44}                    |
| .snow   | Double    | (where available) Precipitation, mm/h| eg.{"1h": 2.44}                    |
| daily   | Array     | Daily forecast weather data     |                                        |
| .dt     | Date      | Time of the forecasted data     |  |
| .sunrise| Date      | Sunrise time   |                                                   |
| .sunset | Date      | Sunset time   |                                                   |
| .moonrise| Date     | The time of when the moon rises for this day   |                                                   |
| .moonset   | Date    | The time of when the moon sets for this day   |                                                   |
| .moonphase| Double    | Moon phase   |                                                   |
| .summary   | String    | Human-readable description of the weather conditions for the day   |              |
| .tempday   | Double    | Day temperature   |                                                   |
| .tempmin   | Double    | Min daily temperature(Celsius)   |                                                   |
| .tempmax   | Double    | Max daily temperature(Celsius)   |                                                   |
| .tempnight   | Double    | Night temperature(Celsius)   |                                                   |
| .tempeve   | Double    | Evening temperature(Celsius)   |                                                   |
| .tempmorn   | Double    | Morning temperature(Celsius)   |                                                   |
| .fellikeday| Double    | Day temperature(Celsius)       |                                                             |
| .fellikenight| Double    | Night temperature(Celsius)     |                                                             |
| .fellikeeve| Double    | Evening temperature(Celsius)      |                                                             |
| .fellikemorn| Double    | Morning temperature(Celsius)      |                                                             |
| .pressure| Int      | pressure(hPa)             |                                                             |
| .hmdty  | Int       | humidity(%)     |                                                             |
| .dewpoint| Double   | Atmospheric temperature(Celsius)|                                             |
| .uv     | Double    | Current UV index |                                                             |
| .clouds | Int       | Cloudiness(%)|                                                             |
| .windsp | Double    | wind speed(metre/sec)|                             |
| .winddeg| Int       | Wind direction, degrees (meteorological)|                             |
| .windgu | Double    | (where available)Wind gust(metre/sec)       |                      |
| .weather| String    | main weather    |                         |
| .desc   | String    | weather description|                         |
| .pop    | Double    | Probability of precipitation(0-1)|                         |
| .rain   | Double    | (where available) Precipitation volume, mm|                         |
| .snow   | Double    | (where available) Snow volume, mm|                         |
| alerts  | Array     |  |                        |
| .sendernm  | String | Name of the alert source |                        |
| .altevent  | String | Alert event name |                        |
| .altstart  | Date | Date and time of the start of the alert |                        |
| .altend  | Date     | Date and time of the end of the alert |                        |
| .altdesc  | String     | Description of the alert |                        |
| .alttags  | Array     | Type of severe weather |                        |
