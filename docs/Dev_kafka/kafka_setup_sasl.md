kafka 

config的坑：
1 配置末尾空格去掉
2 配置不支持#同行comment
3 -- vs -
4 有些--add-config会触发acl，更改cdrwa权限
5 init的话仅仅删除data dir 不够, 还要删log dir
6 https://stackoverflow.com/questions/41964676/kafka-connect-running-out-of-heap-space heapspace error due to failed handshake
7 must sign with SAN


Ideas:
1 port forwarding as localhost
2 wildcard fqdn

# Start zookeeper+kafkaServer
0 edit config change data dir
```

zookeeper-server-start.sh -daemon zookeeperCa9.properties
export KAFKA_OPTS=-Djava.security.auth.login.config=/home/<USER>/kafka/config/kafka-server-jaas.conf
kafka-server-start.sh -daemon server0Ca9.properties
```
check ports:
`netstat -tulpn | grep LISTEN`

# check status:
```
jps
zookeeper-shell.sh localhost:2181
  ls /brokers/ids
  ls /config/users
zookeeper-shell.sh localhost:2182 -zk-tls-config-file  zookeeper-clientCa9.properties
```
# check topics:
```
kafka-topics.sh --list --bootstrap-server localhost:9092
kafka-topics.sh --bootstrap-server localhost:9092 --topic ca9.res.sqft.condosCa --describe 
kafka-topics.sh --list --bootstrap-server *************:19091 --command-config topic.properties
kafka-topics.sh --bootstrap-server *************:19091 --topic res.sqft.condosCa --describe --command-config topic.properties
```
# DEPRECATED:
--broker-list -> --bootstrap-server
--zookeeper -> --bootstrap-server


# create topics:
kafka-topics.sh --create --bootstrap-server localhost:9092 --topic req.sqft.condos

--command-config topic.properties
kafka-topics.sh --create --bootstrap-server *************:19091 --topic condosToGetSqft --command-config topic.properties



# create producer:
 kafka-console-producer.sh --bootstrap-server localhost:9092 --topic req.sqft.condos
 
 kafka-console-producer.sh  --topic test-topic --producer.config producer0.properties --broker-list *************:19091
 kafka-console-producer.sh  --topic condosToGetSqft --producer.config config/producer0.properties --broker-list *************:19091

# create consumer:
```
kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic ca9.res.sqft.condosCa --from-beginning --group test-consumer-ca8a

kafka-console-consumer.sh --bootstrap-server *************:19091 --topic ca8.req.test-topic --from-beginning --group test-consumer --consumer.config config/consumer0.properties 

./bin/kafka-console-consumer.sh --topic sqftFromCondosCa --from-beginning --group test-consumer --consumer.config config/consumer0.properties --bootstrap-server *************:19091

```
# check consumer class:
```
kafka-run-class.sh kafka.admin.ConsumerGroupCommand --bootstrap-server *************:19091 --group test-consumer --describe

kafka-run-class.sh kafka.tools.GetOffsetShell --bootstrap-server localhost:9092 --topic req.sqft.condos
kafka-run-class.sh kafka.tools.GetOffsetShell --bootstrap-server *************:19091 --topic ca8.req.sqft.condos --command-config topic.properties
kafka-consumer-groups.sh --describe --group test-consumer --members --bootstrap-server *************:19091 --command-config config/topic.properties
```

# stop server:
kafka-server-stop.sh 
zookeeper-server-stop.sh



## SSL/TLS Security:
1/2 way handshake  TLSv1.2
handshake Mech: KS -> CA (TS)

1 zookeeper client security ^3.5 (except topics.sh not support ssl)
2 kafka client security

Steps:https://youtu.be/hR_OuiqLgOo?t=498
1 fix CA
2 create  trust store
3 create keystone
4 create certificate signing req
5 sign cert using CA
6 import signed certificate and CA to KeyStore
Auth: sasl_ssl(user+pwd+ssl) for https://docs.vmware.com/en/VMware-Smart-Assurance/10.1.0/sa-ui-installation-config-guide-10.1.0/GUID-DF659094-60D3-4E1B-8D63-3DE3ED8B0EDF.html


Example steps to implement CA, truststore and keystore for zookeeper SSL security :

𝟏. 𝐆𝐞𝐧𝐞𝐫𝐚𝐭𝐞 𝐂𝐀 == 
```
openssl req -new -x509 -keyout ca-key -out ca-cert -days 3650 \
 -subj "/C=GB/CN=ca-cert" \
 -addext "subjectAltName=DNS:localhost, DNS:mq.realmaster.com, IP:*************, IP:127.0.0.1"

openssl req -new -x509 -keyout ca-key -out ca-cert -days 365 -subj '/CN=<fqdn>'   -extensions san   -config <(echo '[req]'; echo 'distinguished_name=req'; echo '[san]'; echo 'subjectAltName = DNS:localhost, IP:127.0.0.1, DNS:<hostname>, IP:<ip-address>')

```
(pwd:ca9kfk, common-name:ca-cert, eml:<EMAIL>)

Repeat: 𝟐. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐓𝐫𝐮𝐬𝐭𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper.truststore.jks -alias ca-cert -import -file ca-cert`
𝟑. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper.keystore.jks -alias zookeeper -validity 3650 -genkey -keyalg RSA -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:mq.realmaster.com`

𝟒. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐬𝐢𝐠𝐧𝐢𝐧𝐠 𝐫𝐞𝐪𝐮𝐞𝐬𝐭 (𝐂𝐒𝐑) == 
`keytool -keystore kafka.zookeeper.keystore.jks -alias zookeeper -certreq -file ca-request-zookeeper`
𝟓. 𝐒𝐢𝐠𝐧 𝐭𝐡𝐞 𝐂𝐒𝐑 == 
`openssl x509 -req -extfile <(printf "subjectAltName=DNS:localhost, DNS:mq.realmaster.com, IP:*************, IP:127.0.0.1") -CA ca-cert -CAkey ca-key -in ca-request-zookeeper -out ca-signed-zookeeper -days 3650 -CAcreateserial`
𝟔. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐂𝐀 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper.keystore.jks -alias ca-cert -import -file ca-cert`
𝟕. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐬𝐢𝐠𝐧𝐞𝐝 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper.keystore.jks -alias zookeeper -import -file ca-signed-zookeeper`

Zookeeper-client:
𝟐. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐓𝐫𝐮𝐬𝐭𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper-client.truststore.jks -alias ca-cert -import -file ca-cert`
𝟑. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper-client.keystore.jks -alias zookeeper-client -validity 3650 -genkey -keyalg RSA -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:mq.realmaster.com`
 (cn: localhost)
𝟒. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐬𝐢𝐠𝐧𝐢𝐧𝐠 𝐫𝐞𝐪𝐮𝐞𝐬𝐭 (𝐂𝐒𝐑) == 
`keytool -keystore kafka.zookeeper-client.keystore.jks -alias zookeeper-client -certreq -file ca-request-zookeeper-client`
𝟓. 𝐒𝐢𝐠𝐧 𝐭𝐡𝐞 𝐂𝐒𝐑 == 
refer: kafka_setup.txt
<!-- openssl ca -config openssl-ca.cnf -policy signing_policy -extensions signing_req -out server.crt -infiles server.csr -->
`openssl x509 -req -extfile <(printf "subjectAltName=DNS:localhost, DNS:mq.realmaster.com, IP:*************, IP:127.0.0.1") -CA ca-cert -CAkey ca-key -in ca-request-zookeeper-client -out ca-signed-zookeeper-client -days 3650 -CAcreateserial`
𝟔. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐂𝐀 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper-client.keystore.jks -alias ca-cert -import -file ca-cert`
𝟕. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐬𝐢𝐠𝐧𝐞𝐝 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.zookeeper-client.keystore.jks -alias zookeeper-client -import -file ca-signed-zookeeper-client`

rm ca-cert.srl
rm ca-request-zookeeper-client
rm ca-signed-zookeeper-client

server:
𝟐. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐓𝐫𝐮𝐬𝐭𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.broker0.truststore.jks -alias ca-cert -import -file ca-cert`
𝟑. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.broker0.keystore.jks -alias broker0 -validity 3650 -genkey -keyalg RSA -ext SAN=dns:localhost,ip:*************,ip:127.0.0.1,dns:mq.realmaster.com`
(cn: localhost)
𝟒. 𝐂𝐫𝐞𝐚𝐭𝐞 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐬𝐢𝐠𝐧𝐢𝐧𝐠 𝐫𝐞𝐪𝐮𝐞𝐬𝐭 (𝐂𝐒𝐑) == 
`keytool -keystore kafka.broker0.keystore.jks -alias broker0 -certreq -file ca-request-broker0`
𝟓. 𝐒𝐢𝐠𝐧 𝐭𝐡𝐞 𝐂𝐒𝐑 == 
`openssl x509 -req -extfile <(printf "subjectAltName=DNS:localhost, DNS:mq.realmaster.com, IP:*************, IP:127.0.0.1") -CA ca-cert -CAkey ca-key -in ca-request-broker0 -out ca-signed-broker0 -days 3650 -CAcreateserial`
𝟔. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐂𝐀 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.broker0.keystore.jks -alias ca-cert -import -file ca-cert`
𝟕. 𝐈𝐦𝐩𝐨𝐫𝐭 𝐭𝐡𝐞 𝐬𝐢𝐠𝐧𝐞𝐝 𝐜𝐞𝐫𝐭𝐢𝐟𝐢𝐜𝐚𝐭𝐞 𝐢𝐧𝐭𝐨 𝐊𝐞𝐲𝐬𝐭𝐨𝐫𝐞 == 
`keytool -keystore kafka.broker0.keystore.jks -alias broker0 -import -file ca-signed-broker0`

producer:

consumer:



SSL SPECIFIC PROPERTIES TO CONFIGURE ZOOKEEPER AND BROKERS FOR ZOOKEEPER SECURITY:
𝟏. 𝐙𝐎𝐎𝐊𝐄𝐄𝐏𝐄𝐑.𝐏𝐑𝐎𝐏𝐄𝐑𝐓𝐈𝐄𝐒
secureClientPort=2182
authProvider.x509=org.apache.zookeeper.server.auth.X509AuthenticationProvider
serverCnxnFactory=org.apache.zookeeper.server.NettyServerCnxnFactory
ssl.trustStore.location=kafka.zookeeper.truststore.jks
ssl.trustStore.password=12345
ssl.keyStore.location=kafka.zookeeper.keystore.jks
ssl.keyStore.password=12345
ssl.clientAuth=need

𝟐. 𝐒𝐄𝐑𝐕𝐄𝐑.𝐏𝐑𝐎𝐏𝐄𝐑𝐓𝐈𝐄𝐒
zookeeper.connect=localhost:2182
zookeeper.clientCnxnSocket=org.apache.zookeeper.ClientCnxnSocketNetty
zookeeper.ssl.client.enable=true
zookeeper.ssl.protocol=TLSv1.2
zookeeper.ssl.truststore.location=kafka.broker0.truststore.jks
zookeeper.ssl.truststore.password=12345
zookeeper.ssl.keystore.location=/kafka.broker0.keystore.jks
zookeeper.ssl.keystore.password=12345
zookeeper.set.acl=true

SSL SPECIFIC PROPERTIES TO CONFIGURE BROKERS, PRODUCERS AND CONSUMERS FOR KAFKA/BROKER SECURITY:
𝟏. 𝐒𝐄𝐑𝐕𝐄𝐑.𝐏𝐑𝐎𝐏𝐄𝐑𝐓𝐈𝐄𝐒
listeners=SSL://localhost:9092
advertised.listeners=SSL://localhost:9092
ssl.truststore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.truststore.jks
ssl.truststore.password=ca9kfk
ssl.keystore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.keystore.jks
ssl.keystore.password=ca9kfk
ssl.key.password=ca9kfk
security.inter.broker.protocol=SSL
ssl.client.auth=required
ssl.protocol=TLSv1.2

𝟐. 𝐏𝐑𝐎𝐃𝐔𝐂𝐄𝐑.𝐏𝐑𝐎𝐏𝐄𝐑𝐓𝐈𝐄𝐒/kafka-topic.properties
bootstrap.servers=localhost:9092
security.protocol=SSL
ssl.truststore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.truststore.jks
ssl.truststore.password=ca9kfk
ssl.keystore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.keystore.jks
ssl.keystore.password=ca9kfk
ssl.key.password=ca9kfk
ssl.protocol=TLSv1.2

𝟑. 𝐂𝐎𝐍𝐒𝐔𝐌𝐄𝐑.𝐏𝐑𝐎𝐏𝐄𝐑𝐓𝐈𝐄𝐒
bootstrap.servers=localhost:9092
group.id=test-consumer
security.protocol=SSL
ssl.truststore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.truststore.jks
ssl.truststore.password=ca9kfk
ssl.keystore.location=/home/<USER>/kafka/config/ssl/kafka.broker0.keystore.jks
ssl.keystore.password=ca9kfk
ssl.key.password=ca9kfk
ssl.protocol=TLSv1.2


## SASL+SCRAM:
user/pwd || oauth
SSL->SASL_SSL/SASL_PLAINTEXT

# add user
```
kafka-configs.sh --zookeeper localhost:2182 --zk-tls-config-file  zookeeper-clientCa9.properties --entity-type users --entity-name broker-admin --alter --add-config 'SCRAM-SHA-512=[password=ca9kfk]' 

kafka-configs.sh --zookeeper localhost:2182 --zk-tls-config-file  zookeeper-clientCa9.properties --alter --add-config 'SCRAM-SHA-512=[password=ca9kfk]' --entity-type users --entity-name admin

kafka-configs.sh --zookeeper localhost:2181 --alter --add-config 'SCRAM-SHA-512=[password=ca9kfk]' --entity-type users --entity-name admin

kafka-configs.sh --bootstrap-server localhost:9092  --entity-type users --entity-name broker-admin --alter --add-config 'SCRAM-SHA-512=[password=ca9kfk]' 
```
# view user
```
kafka-configs.sh --zookeeper localhost:2182 --zk-tls-config-file  zookeeper-clientCa9.properties --describe --entity-type users --entity-name admin
```
# Add Acls:
kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 --add --allow-principal User:admin --operation All --topic test-topic --group=* 

# Delete Config:
--zk-tls-config-file zookeeper-clientCa9.properties
kafka-configs.sh --zookeeper localhost:2181  --zk-tls-config-file zookeeper-clientCa9.properties --alter --delete-config 'SCRAM-SHA-512' --entity-type users --entity-name broker-admin 

security.inter.broker.protocol=SASL_SSL
ssl.client.auth=none
listeners=SASL_SSL://localhost:9092

sasl.enabled.mechanisms=SCRAM-SHA-512
sasl.mechanism.inter.broker.protocol=SCRAM-SHA-512
listener.name.sasl_ssl.scram-sha-512.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="broker-admin" password="ca9kfk";
super.users=User:broker-admin

authorizer.class.name=kafka.security.authorizer.AclAuthorizer




kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 --add --allow-principal User:admin --operation Create --operation Describe  --topic test-topic
kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 --add --allow-principal User:admin --producer --topic test-topic

kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 --add --allow-principal User:admin --consumer --topic test-topic --group test-consumer-group

setAcl /config/changes "world:anyone:cdrwa"
setAcl /config/users/admin "world:anyone:cdrwa"

internal ip:
remote ip:*************


## mirror maker
user: ca8a@ca8
folder: ca8a/kafkamm

# start mirror maker:
1. start ca8 kafka server use command above(no auth)
2. nohup connect-mirror-maker.sh connect-mirror-maker-ca8.properties > mm2.log 2>&1 &

# NOTE:
topic with specific name only `req.* ca8->ca9|res.* ca9->ca8` will be synced


change default names:
condosToGetSqft  -> req.sqft.condos
sqftFromCondosCa -> res.sqft.condosCa  / res.sqft.dataVal

# topic through mirror maker will be changed
on ca9:
req.sqft.condos -> ca8.req.sqft.condos

on ca8:
res.sqft.condosCa -> ca9.res.sqft.condosCa