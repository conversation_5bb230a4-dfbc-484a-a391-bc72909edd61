Kafka setup

yum install java-11-openjdk

https://adityasridhar.com/posts/how-to-easily-install-kafka-without-zookeeper
https://hevodata.com/learn/kafka-without-zookeeper/

systemd:
https://www.digitalocean.com/community/tutorials/how-to-install-apache-kafka-on-centos-7
https://geekflare.com/systemd-start-services-linux-7/


security:
https://blog.csdn.net/davidhhs/article/details/124980386
https://cloud.tencent.com/developer/article/2020540
https://cloud.tencent.com/developer/article/1916506
https://kafka.apache.org/documentation/#security

https://medium.com/@stephane.maarek/introduction-to-apache-kafka-security-c8951d410adf

https://docs.confluent.io/platform/current/kafka/authentication_ssl.html



https://downloads.apache.org/kafka/3.2.1/kafka_2.13-3.2.1.tgz


kRaft mode:

https://github.com/apache/kafka/blob/trunk/config/kraft/README.md


Setup server:
1. cd kafka/config/kraft/
2. cp server.properties server1.properties
3. edit server1.properties for id, all ports, log file folder location
    1. for replcaset: edit controller.quorum.voters=[id@host:port],[id2@host2:port2],...
4. kafka-storage.sh random-uuid, and write down uuid
5. kafka-storage.sh format -t <uuid> -c <location/server1.properties> to initialize folder
6. Add ssl support and change ssl setting in .properties file, if needed
7. export KAFKA_HEAP_OPTS="-Xmx200M -Xms100M"     to change default memory allocation
8. export KAFKA_HEAP_OPTS="-Xmx1G -Xms1G"
9. kafka-server-start.sh -daemon <location/server1.properties>
10. ps -ef | grep kafka or check kafka/logs/server.log for error


Setup SSL:


create CA
    - create file openssl-ca.cnf
>>>>>>
HOME            = .
RANDFILE        = $ENV::HOME/.rnd

####################################################################
[ ca ]
default_ca    = CA_default      # The default ca section

[ CA_default ]

base_dir      = .
certificate   = $base_dir/cacert.pem   # The CA certifcate
private_key   = $base_dir/cakey.pem    # The CA private key
new_certs_dir = $base_dir              # Location for new certs after signing
database      = $base_dir/index.txt    # Database index file
serial        = $base_dir/serial.txt   # The current serial number

default_days     = 1000         # How long to certify for
default_crl_days = 30           # How long before next CRL
default_md       = sha256       # Use public key default MD
preserve         = no           # Keep passed DN ordering

x509_extensions = ca_extensions # The extensions to add to the cert

email_in_dn     = no            # Don't concat the email in the DN
copy_extensions = copy          # Required to copy SANs from CSR to cert

####################################################################
[ req ]
default_bits       = 4096
default_keyfile    = cakey.pem
distinguished_name = ca_distinguished_name
x509_extensions    = ca_extensions
string_mask        = utf8only

####################################################################
[ ca_distinguished_name ]
countryName         =Country Name (2 letter code)
countryName_default = CA

stateOrProvinceName         = State or Province Name (full name)
stateOrProvinceName_default = Ontario

localityName                = Locality Name (eg, city)
localityName_default        = Toronto

organizationName            = Organization Name (eg, company)
organizationName_default    = RealMaster Technology Inc.

organizationalUnitName         = Organizational Unit (eg, division)
organizationalUnitName_default = Development

commonName         = Common Name (e.g. server FQDN or YOUR name)
commonName_default = localhost

emailAddress         = Email Address
emailAddress_default = <EMAIL>

####################################################################
[ ca_extensions ]

subjectKeyIdentifier   = hash
authorityKeyIdentifier = keyid:always, issuer
basicConstraints       = critical, CA:true
keyUsage               = keyCertSign, cRLSign

####################################################################
[ signing_policy ]
countryName            = optional
stateOrProvinceName    = optional
localityName           = optional
organizationName       = optional
organizationalUnitName = optional
commonName             = supplied
emailAddress           = optional

####################################################################
[ signing_req ]
subjectKeyIdentifier   = hash
authorityKeyIdentifier = keyid,issuer
basicConstraints       = CA:FALSE
keyUsage               = digitalSignature, keyEncipherment
<<<<<<
-  commands:
echo 01 > serial.txt
touch index.txt
openssl req -x509 -config openssl-ca.cnf -newkey rsa:4096 -sha256 -nodes -out cacert.pem -outform PEM
- will generate: cakey.pem, cacert.pem


Trust CA from truststore

- command
keytool -keystore client.truststore.jks -alias CARoot -import -file cacert.pem
keytool -keystore server.truststore.jks -alias CARoot -import -file cacert.pem

For each server:
generate certificate signing requests: CSR
- create keystore, NOTICE: FQDN must match hostname, and set as CN(First name & last name) part, and used in settings(server.properties)
keytool -keystore server.keystore.jks -alias localhost -validity {validity} -genkey -keyalg RSA (-destkeystoretype pkcs12) -ext SAN=DNS:{FQDN},IP:{IPADDRESS1}
keytool -keystore server.keystore.jks -alias localhost -validity 3650 -genkey -keyalg RSA -ext SAN=DNS:win.rm.home,IP:127.0.0.1,IP:***************
- create CSR
keytool -certreq -alias localhost -file server.csr -keystore server.keystore.jks


Signing the certificate

- command:
openssl ca -config openssl-ca.cnf -policy signing_policy -extensions signing_req -out server.crt -infiles server.csr
- will generate server.crt and 01.pem (the same)

Import certificate and CA root certificate to keystore
- commands
 keytool -keystore server.keystore.jks -alias CARoot -import -file cacert.pem
 keytool -keystore server.keystore.jks -alias localhost -import -file server.crt



Change /config/kraft/server1.properties

- add broker setting
############################# Broker Settings #############################
ssl.keystore.location=/home/<USER>/kafka/keys/server1.keystore.jks
ssl.keystore.password=centos
#ssl.key.password=test1234
ssl.truststore.location=/home/<USER>/kafka/keys/server.truststore.jks
ssl.truststore.password=centos
ssl.client.auth=required
ssl.enabled.protocols=TLSv1.2,TLSv1.1,TLSv1
security.inter.broker.protocol=SSL
ssl.secure.random.implementation=SHA1PRNG
security.protocol=SSL

- change listener
listeners=PLAINTEXT://:9112,SSL://:9111,CONTROLLER://:9113
advertised.listeners=SSL://localhost:9111
### comment out this line, conflict with security.inter.broker.protocol
#inter.broker.listener.name=PLAINTEXT
#listener.security.protocol.map=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
listener.security.protocol.map=CONTROLLER:SSL,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL



For Client:
generate certificate signing requests: CSR
- create keystore,  (-dname "CN=rajind,OU=dev,O=bft,L=mt,C=Srilanka")
keytool -keystore client.keystore.jks -alias client -validity 3650 -genkey -keyalg RSA
- create CSR
keytool -certreq -alias client -file client.csr -keystore client.keystore.jks

Signing the certificate

- command:
openssl ca -config openssl-ca.cnf -policy signing_policy -extensions signing_req -out client.crt -infiles client.csr
- will generate client.crt and 01.pem (the same)

Import certificate and CA root certificate to keystore
- commands
 keytool -keystore server.keystore.jks -alias CARoot -import -file cacert.pem
 keytool -keystore server.keystore.jks -alias localhost -import -file server.crt


extracting private keys
keytool -importkeystore -srckeystore client.keystore.jks -srcstorepass SrcPassword -srckeypass SrcKeyPassword -srcalias client -destalias notebook -destkeystore client.p12 -deststoretype PKCS12 -deststorepass password -destkeypass password

openssl pkcs12 -in client.p12 -nodes -nocerts -out private_key.pem