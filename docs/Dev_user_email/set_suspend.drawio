<mxfile host="65bd71144e">
    <diagram id="334UgDzBY8aknOs-Td5j" name="第 1 页">
        <mxGraphModel dx="1076" dy="471" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="120" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="2" target="108" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); line-height: 18px;&quot;&gt;emails:[str],eventType:str,msgId:str,reason:str,tag:str&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="2.75" y="22" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="132" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="6" target="117" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="update user.edmNo=true" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="668" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="155" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" parent="1" source="7" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="810" y="472"/>
                            <mxPoint x="810" y="882"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="从suspend_email表删除记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="570" y="452" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="134" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="10" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="unset user.edmNo" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="452" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="return" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="500" y="962" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="122" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="62" target="109" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="127" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="62" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="810" y="242" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;eventType&lt;font style=&quot;font-size: 12px;&quot; color=&quot;#d4d4d4&quot;&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30);&quot;&gt;&amp;nbsp;in&lt;br&gt;['&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;Delivery','Send']&lt;/span&gt;&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="212" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="121" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="108" target="62" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="126" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="108" target="114" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="108" value="eventType&lt;font color=&quot;#d4d4d4&quot;&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30);&quot;&gt;&amp;nbsp;in ['&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;Reject',&lt;br&gt;'Rendering Failure','DeliveryDelay&lt;/span&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;']&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="102" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="123" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="109" target="112" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="128" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="109" target="115" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="109" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;eventType&lt;font style=&quot;font-size: 12px;&quot; color=&quot;#d4d4d4&quot;&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30);&quot;&gt;&amp;nbsp;in&lt;br&gt;['&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;Open','Click&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;']&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="332" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="125" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="110" target="111" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="130" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="110" target="118" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="110" value="eventType=='softBounce'" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="552" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="131" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="111" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="152" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" parent="1" source="111" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="111" value="eventType in &lt;br&gt;['hardBounce','&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;Complaint&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;']&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="658" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="124" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="112" target="110" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="129" value="" style="edgeStyle=none;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="112" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="112" value="eventType=='&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212);&quot;&gt;Subscription&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;'&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31.5" y="442" width="242.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="153" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" parent="1" source="114" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="810" y="132"/>
                            <mxPoint x="810" y="882"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="114" value="error log" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="112" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="169" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="115" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="810" y="362" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="115" value="unset user.edmUnread&lt;br&gt;unset user.edmNoBounce" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="342" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="168" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" parent="1" source="117" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="117" value="循环emails更新/添加suspend_email表记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="762" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="154" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;" parent="1" source="118" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="118" value="$inc user.edmNoBounce" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="324" y="562" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="150" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontFamily=Helvetica;fontSize=12;" parent="1" source="119" target="38" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="119" value="循环emails更新/添加email_history表记录" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="372.5" y="862" width="375" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="156" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="264" y="102" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="157" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="274" y="200" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="158" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="264" y="332" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="159" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="264" y="442" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="160" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="264" y="552" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="161" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="264" y="658" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="162" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="170" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="163" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="290" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="164" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="400" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="165" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="510" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="166" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="620" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="167" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="100" y="780" width="60" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>