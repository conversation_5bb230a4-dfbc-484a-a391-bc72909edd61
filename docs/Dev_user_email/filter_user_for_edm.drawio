<mxfile host="65bd71144e">
    <diagram id="fEzP1wiB059b0zi8KWj6" name="第 1 页">
        <mxGraphModel dx="1076" dy="389" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;" parent="1" source="2" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="input:{query:{},source:'',needProfile}" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="20" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="1" source="7" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="" style="edgeStyle=orthogonalEdgeStyle;shape=flexArrow;rounded=0;html=1;fontColor=#ffffff;entryX=0.996;entryY=0.128;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="7" target="64" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="将suspend_email中存在的email从users过滤掉" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="370" y="602" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="return users" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="370" y="692" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="9" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="9" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="source&amp;nbsp;= 'user'?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="265" y="95" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="10" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.563;entryY=0.075;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#ffffff;" parent="1" source="10" target="67" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="source&amp;nbsp;= 'extra'?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="435" y="145" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="11" target="45" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.438;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#ffffff;" parent="1" source="11" target="68" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="source&amp;nbsp;= 'crm'?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="605" y="195" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="1" source="12" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="user表查询" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="240" y="317" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;" parent="1" source="13" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="71" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;" parent="1" source="13" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="needProfile=true?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="195" width="170" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="450" y="90" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="620" y="140" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="470" y="240" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="630" y="260" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="200" y="140" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="250" y="250" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontColor=#ffffff;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="33" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="user表关联&lt;font color=&quot;#ffffff&quot; face=&quot;Menlo, Monaco, Courier New, monospace&quot;&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30);&quot;&gt;user_profile查询&lt;/span&gt;&lt;/font&gt;" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="10" y="317" width="190" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="80" y="190" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="1" source="42" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="获取用户信息users" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="370" y="520" width="300" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="return []" style="whiteSpace=wrap;html=1;rounded=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="726" y="690" width="203" height="42" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="790" y="190" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="40" y="572" width="250" height="398" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="" style="whiteSpace=wrap;html=1;rounded=0;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry width="250" height="398" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="input:users" style="whiteSpace=wrap;html=1;rounded=1;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry x="30" y="20" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="根据emails在suspend_email中查询，得到edmnos" style="whiteSpace=wrap;html=1;rounded=0;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry x="30" y="178" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="65" source="56" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="获取所有的邮箱,emails:[]" style="whiteSpace=wrap;html=1;rounded=0;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry x="30" y="98" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;" parent="65" source="52" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="循环users，删除在edmnos中存在的用户" style="whiteSpace=wrap;html=1;rounded=0;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry x="30" y="258" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="65" source="53" target="58" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="return users" style="whiteSpace=wrap;html=1;rounded=1;fontColor=#ffffff;" parent="65" vertex="1">
                    <mxGeometry x="30" y="338" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;fontColor=#ffffff;" parent="65" source="58" target="62" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;" parent="1" source="67" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="520" y="400"/>
                            <mxPoint x="520" y="400"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="67" value="user_extra表查询" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="430" y="317" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#ffffff;" parent="1" source="68" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="crm表查询" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="620" y="317" width="160" height="40" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>