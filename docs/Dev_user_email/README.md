### description
1.在edm发送邮件时过滤不需要发送的用户(register，changepassword，forgetpassword等邮件发送不需要通过suspend_email过滤)
2.对admin设置edmNo，sns通知邮件发送状态对suspend_email，edmno_set_log操作，记录原因与操作记录。

### collection
\* required

## chome.suspend_email
| field      | data type      | description                                           | 
| ---------- | -------------- | ------------------------------------------------------|
| _id\*      | string         | email                                                 |
| reasons\*  | reason[]       | refer [reason structure](#reason) ,length limit 20    |
| _mt        | date           | update time                                           |
| ts         | date           | create time                                           |

### reason
| field      | data type      | description                                           | 
| ---------- | -------------- | ------------------------------------------------------|
| tp\*       | string         | eventType                                             |
| reason\*   | string         | reason of setting suspend                             |
| msgId      | string         | messageId                                             |
| ts\*       | date           | create time                                           |


## rni.email_history
| field      | data type      | description                                           |
| ---------- | -------------- | ------------------------------------------------------|
| _id\*      | string         | objectId                                              |
| eml\*      | string         | email                                                 |
| msgId      | string         | messageId (get by aws)                                |
| tp\*       | string         | eventType                                             |
| reason     | string         |                                                       |
| tag        | string         | send mail tag                                         |
| ts         | date           | create time                                           |

idx:email,msgId
expidx:30d(exp30d)

### TODO
# edmUnread状态的设置(batch,效率：一定时间内只有delivery),open/click时unset掉

### SNS_NOTICE
# https://docs.aws.amazon.com/zh_cn/ses/latest/dg/event-publishing-retrieving-sns-contents.html
# softBounce归零的时机(open,click),edm用户筛选时softBounce(edmNoBounce)限制<7(global)
# eventType:
 - Bounce:              hard(Permanent) set user.edmNo=true;soft(Transient) user.edmNoBounce $inc:1
 - Complaint:           abuse,auth-failure,fraud,virus，admin-set set user.edmNo=true; not-spam,other error log
 - Delivery:            log
 - Send:                log
 - Reject:              Bad content,检查邮件内容，error log
 - Open:                unset edmUnread
 - Click:               unset edmUnread
 - Rendering Failure:   error log
 - DeliveryDelay:       error log
 - Subscription:        unset edmNo