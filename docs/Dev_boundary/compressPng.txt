
现用imageoptim压一遍，然后用ImageAlpha压缩。数字是color数。
/Applications/ImageAlpha.app/Contents/MacOS/pngquant -h
/Applications/ImageAlpha.app/Contents/MacOS/pngquant 2 * --force 

find . -name "*_2048.png" -exec sh -c 'mv "$1" "${1%_2048.png}.bed"' _ {} \;
rm *bed
find . -name "*-fs8.png" -exec sh -c 'mv "$1" "${1%-fs8.png}.png"' _ {} \;

usage:  pngquant [options] [ncolors] -- pngfile [pngfile ...]
        pngquant [options] [ncolors] - >stdout <stdin

options:
  --force           overwrite existing output files (synonym: -f)
  --skip-if-larger  only save converted files if they're smaller than original
  --output file     destination file path to use instead of --ext (synonym: -o)
  --ext new.png     set custom suffix/extension for output filenames
  --quality min-max don't save below min, use fewer colors below max (0-100)
  --speed N         speed/quality trade-off. 1=slow, 3=default, 11=fast & rough
  --nofs            disable <PERSON>-<PERSON><PERSON> dithering
  --posterize N     output lower-precision color (e.g. for ARGB4444 output)
  --strip           remove optional metadata (default on Mac)
  --verbose         print status messages (synonym: -v)

Quantizes one or more 32-bit RGBA PNGs to 8-bit (or smaller) RGBA-palette.
The output filename is the same as the input name except that
it ends in "-fs8.png", "-or8.png" or your custom extension (unless the
input is stdin, in which case the quantized image will go to stdout).
If you pass the special output path "-" and a single input file, that file
will be processed and the quantized image will go to stdout.
The default behavior if the output file exists is to skip the conversion;
use --force to overwrite. See man page for full list of options.