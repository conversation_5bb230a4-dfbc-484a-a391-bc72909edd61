boundaryManage.coffee

# support collections:
  Census,Boundary,boundaryError,TransitRoute,TransitStop,School,SchoolError,Census2016,Census2016Error

# Api point:
www.test:8080/boundary
boundaryAdmin can access
Add _boundary for boundaryAdmin

# features
## 列表
  bounday 列表可以查询，并通过列表和地图展示，可以删除。
## 编辑
  编辑页面可以编辑census以外的所有信息。
  画boundary时，如果只有一个boundary，直接保存就可以。
  如果出现多个boundary嵌套的情况，
  1. 选中父boundary，指定id，点击set property 保存。
  2.选中子boundary，设置exclude=1， exbnid为父boundary id
  3.保存， 或者保存并预览，会刷新页面，重新load保存的数据，这时候可以看到hole在图上。

boundary相关数据都有 spacial index，在导入数据时，不合法的数据不能通过index检查，（通常是有线交叉）会放到 对应的error 数据库表中。eg：boundaryError, censusError， 需要手工修改这些数据，然后存回到原始表中。

geojson格式：
"bnds" : {
        "type" : "FeatureCollection",
        "features" : [
            {
                "type" : "Feature",
                "geometry" : {
                    "type" : "Polygon",
                    "properties":
                    "coordinates" : [
                        [
                            [
                                -79.4112345119285,
                                43.6651497352186
                            ],[
                                -79.3941005607861,
                                43.6686887146812
                            ],  [
                                -79.4112345119285,
                                43.6651497352186
                            ]
                          ]
                        ]
                      },
                "properties" : {
                   "bnid" : 1022496,
                   "exclude" : 0,
                   "exbnid" : -1
                 },
                 "bbox" : [
                     -79.4112345119285,
                     43.656479906159,
                     -79.3906029600138,
                     43.6686887146812
                 ]
                }
              ]
            }
