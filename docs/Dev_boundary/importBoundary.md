# data source
## prov, city, region Boundary could get from canada census:
https://www12.statcan.gc.ca/census-recensement/2021/geo/sip-pis/boundary-limites/index2021-eng.cfm?year=21
Administrative boundaries
    Provinces/territories
    Census divisions -> mapping to region
    Census subdivisions
## treb boundary. from old treb data
## torontoCmtyCouncil,  多伦多subcity的boundary
    https://www.toronto.ca/city-government/data-research-maps/neighbourhoods-communities/community-council-area-profiles/
    https://www.toronto.ca/city-government/data-research-maps/neighbourhoods-communities/neighbourhood-profiles/find-your-neighbourhood/#location=&lat=&lng=&zoom=
    https://open.toronto.ca/dataset/neighbourhood-profiles/
    https://www.arcgis.com/home/<USER>
    https://app.trreb.ca/trrebdata/common/maps/Toronto.pdf
# bcarea.txt
    from fred



# import batch

### 将boundary相关的collection转换，用geojson替换原来的bns （已经转换过了。）
./start.sh batch/boundary/bnToGeojson.coffee treb_comm2

./start.sh batch/boundary/bnToGeojson.coffee census
./start.sh batch/boundary/bnToGeojson.coffee school. 有4条数据不合法，存在cross edge。

## import boundary 
./start.sh batch/boundary/importBoundary.coffee
./start.sh batch/boundary/importBoundary.coffee prov   /mnt/files/appd3/boundary/
./start.sh batch/boundary/importBoundary.coffee area /mnt/files/appd3/boundary/
./start.sh batch/boundary/importBoundary.coffee city /mnt/files/appd3/boundary/
./start.sh batch/boundary/importBoundary.coffee census2016   /mnt/files/appd3/boundary/
./start.sh batch/boundary/importBoundary.coffee torontoCmtyCouncil (subcity)

## import bc boundary
./start.sh batch/boundary/importBcBoundary.coffee force /mnt/files/appd3/boundary/


geojson格式：
"bnds" : {
        "type" : "FeatureCollection",
        "features" : [
            {
                "type" : "Feature",
                "geometry" : {
                    "type" : "Polygon",
                    "properties":
                    "coordinates" : [
                        [
                            [
                                -79.4112345119285,
                                43.6651497352186
                            ],[
                                -79.3941005607861,
                                43.6686887146812
                            ],  [
                                -79.4112345119285,
                                43.6651497352186
                            ]
                          ]
                        ]
                      },
                "properties" : {
                   "bnid" : 1022496,
                   "exclude" : 0,
                   "exbnid" : -1
                 },
                 "bbox" : [
                     -79.4112345119285,
                     43.656479906159,
                     -79.3906029600138,
                     43.6686887146812
                 ]
                }
              ]
            }

# Import Toronto Property Boundaries
https://open.toronto.ca/dataset/property-boundaries/
Download geojson format file

use `jq` to convert format
```
jq --compact-output ".features" PROPERTY_BOUNDARIES_WGS84.geojson > PROPERTY_BOUNDARIES_WGS84_PROCESSED.geojson
```
Some boundaries have duplicate vertices, thehy will cause mongodb can't create 2dsphere index, we just ignore those datas, so make sure collection is exist and create index before insert data, then use `--writeConcern "{w:0}"` while import to ignore those data.
```
mongosh --eval 'db.property_boundary.createIndex({ geometry : "2dsphere" });' listing
```
use `mongoimport` to import
```
mongoimport PROPERTY_BOUNDARIES_WGS84_PROCESSED.geojson -d=listing -c=property_boundary --jsonArray --writeConcern "{w:0}"
mongoimport -u d1 -h localhost --port 27017 -d vow -c property_boundary --jsonArray --writeConcern "{w:0}" --authenticationDatabase admin PROPERTY_BOUNDARIES_WGS84_PROCESSED.geojson
```