#### Description

1. 仅在MLS房源详细页放置悬浮在顶层位置固定的按钮，房大师经济、app editor、admin可以看见。显示针对该uaddr下我的笔记数量/房大师经济写的总笔记数量；分享后不显示该按钮。点击该按钮跳转至笔记列表页。
2. 笔记列表页分为我的笔记和所有笔记。我的笔记显示头像、姓名、时间、编辑按钮（点击跳转至编辑页）和删除按钮（点击采用标准删除）、笔记内容、笔记上传的图片；如果我的笔记部分未编写笔记显示添加笔记的按钮；所有笔记部分显示所有房大师经济针对该uaddr编写的笔记，超过三行的文字内容折叠起来，可以根据Tags进行筛选，点击图片后打开图片浏览模块；admin和app editor可以置顶该笔记；
3. 笔记编辑部分：编辑框为空时显示Enter a note文件；可以不选择tag进行输入，也可以选择Tags进行输入。不选择tag进行输入部分只支持在文本开头，如果开始选择了tag则在tag下边进行内容编写。图片上传、预览和删除参考独家房编辑功能。如果笔记正文为空且没有图片则save不可用。点击右上角关闭按钮弹出弹窗二次关闭确认。
4. Tag选择弹窗用在列表页：点击Tag在高亮和非高亮进行切换，点击后关闭窗口。Tag选择弹窗用在编辑页：点击非高亮tag后，关闭窗口。选择已选中的tag后提示 Tag already exists。如果正文删除了tag光健字，tag不在高亮。
5. saves模块针对收藏的房源列表显示笔记行和笔记计数，没有笔记时显示Add a note，有笔记时显示笔记内容和删除icon，执行标准删除。点击笔记文本进入笔记编辑窗口，点击笔记计数进入笔记浏览窗口。
6. saves模块添加Notes列表，房大师经济显示本人的所有笔记，编辑过笔记的房源链接，一个笔记如果对应多个房源链接采用滚动。admin 和 app Editor显示房大师经济全部笔记。点击右上角Map可以在地图中显示房源信息。

注意：同一个uaddr，相同的uid只能创建一个笔记。

#### prop_notes Collection

放置在data库里

| field   | data type | description      | notes                                                       |
| ------- | --------- | ---------------- | ----------------------------------------------------------- |
| _id     | Auto      |                  | 自动生成                                                    |
| uid     | ObjectId  | 用户的_id        | 和uaddr一起加唯一索引                                       |
| uaddr   | string    | 房源地址         | 和uid一起加唯一索引                                         |
| memo    | array     |                  |                                                             |
| -tag    | string    | 用户选中的标签   | 针对于用户不选择tag直接填写内容的部分默认创建时tag为'noTag' |
| -m      | string    | 该标签对应的内容 |                                                             |
| -ts     | string    | 创建或更新的时间 |                                                             |
| pic     | array     | 笔记中上传的图片 |                                                             |
| ts      | string    | 创建时间戳       |                                                             |
| _mt     | Date      | 更新时间戳       | 数据库自动更新                                              |
| topMt   | string    | 被置顶的时间     |                                                             |
| propIds | array     | 房源的propId     |                                                             |
| mt      | string    | 更新时间戳       | 保存时手动更新，用于列表显示时间                            |
| nm_en   | string    | 用户英文名       | 从user中获取                            |
| nm_zh   | string    | 用户中文名       | 从user中获取                            |
| avt     | string    | 用户头像链接      |从user中获取                        |
| nm      | string    | 用户nm       |  从user中获取                        |
| editTkId | ObjectId    | 新建笔记产生的token记录的id      |                       |
| prisTkId      | ObjectId    | admin点赞产生的token记录的id       |                       |
