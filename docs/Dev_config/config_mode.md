# 关于cfglocal/rmcfg里的masterMode & chinaMode
* masterMode只是管createIndex。
* 和rni有关的，应该用chinaMode判断。
* masterMode只有一个服务器，其它都是非masterMode。
* chinaMode和非chinaMode都是有多个服务器。
* createRNIIndex 服务器是否调用createIndex

# masterMode
 - only use to createIndex for collection of database.rni
 - migrate,程序启动时对data/table操作
 ## files that need to be checked:
  - src/apps/80_sites/AppRM/ioNhooks/dataImport.coffee
    - collection: mls_bcre_manual_records_log,mls_treb_manual_records_log
  - src/model/user_email.coffee
    - collection: email_history

# chinaMode
 - true:  can connect database.rni
 - false: can not connect database.rni
 ## files that need to be checked:
  - src/apps/80_sites/AppRM/ioNhooks/dataImport.coffee
    - collection: mls_treb_master_records,mls_bcre_manual_records,mls_bcre_manual_raws,mls_treb_manual_records,mls_treb_manual_records_log,mls_bcre_manual_records_log
  - src/model/properties.coffee
    - collection: mls_treb_master_records,mls_crea_ddf_records,mls_treb_master_ids,mls_crea_ddf_ids
  - src/model/snsReply.coffee
    - collection: ses_sns_reply
    - remark: when chinaMode is true,do not load this file
  - src/model/user_email.coffee
    - collection: email_history
  - src/apps/80_sites/AppRM/ioNhooks/sns_receiver.coffee
    - collection: ses_sns_reply
    - remark: when chinaMode is true,do not load this file