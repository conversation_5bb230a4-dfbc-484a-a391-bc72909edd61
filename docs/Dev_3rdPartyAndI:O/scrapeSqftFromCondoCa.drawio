<mxfile host="65bd71144e">
    <diagram id="3IrWiU20rwhub1vG3jht" name="overview">
        <mxGraphModel dx="997" dy="636" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="5" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="2" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="2" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="watchAndImportToProperties.coffee" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="80" y="50" width="310" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="3" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="Topic: sqftFromCondos" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="14">
                    <mxGeometry x="-0.5075" y="-1" relative="1" as="geometry">
                        <mxPoint x="42" y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="3" target="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="scrape process&lt;br&gt;&lt;span&gt;&amp;nbsp;&lt;/span&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="57.5" y="520" width="175" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="sqftToProcess" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="80" y="170" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="6" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Kafka In Bach Sever" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="290" y="180" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="8" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="Kafka Middle server" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="480" y="330" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="10" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="Topic :SqftToProcess" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="12">
                    <mxGeometry x="0.0484" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="Kafka local server&lt;br&gt;optional" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="290" y="410" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="&lt;span style=&quot;font-size: 11px&quot;&gt;sqftFromCondos&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="70" y="620" width="150" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="700" y="50" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.684;entryY=0.001;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="22" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="610" y="160" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="25" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="Another scrape" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="620" y="590" width="130" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>