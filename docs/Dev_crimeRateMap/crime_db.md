## DB structure of crime data
after post to _imp_/crime1
data is then converted to own format

# Criminal code
https://laws-lois.justice.gc.ca/eng/acts/c-46/

# vow.crime
```
{
  _id: sysid
  sid: origId
  src: src
  lat: 43.11
  lng: -79.11
  loc: {geopoint}
  odt:  Occurrence Date:	
  ots:  Occurrence time:	
  ts: ots or default ts when crime happened, 原始数据可能没有ots
  impts: imported ts
  otp:  Occurrence type/Crime Category: Assault see *otp list
  odtl: Occurrence Detail/desc: 

  status: ClearanceStatus: Solved/Open/Other
  
  ptp:   Premise Type: House
  ltp:   Location Type: Residential Nearest/Parking Lot 
  st:    StreetName
  sttp:  StreetType
  ldtl:  Location Detail:	Nearest Intersection
  
  dst:   District/Division:	3
  cmty:  Municipality/Neighbourhood:	Georgina
  prov:  ON
  ptz:   PatrolZone: 11350
  ward:  Ward: M7
  vcm:   Offences and/or victims involved: 1

  bndCmty: calculated cmty
  bndCity: 
  bndProv:
}
type type:{
  ASL: Assault
  BNE: Break/Enter
  DRP: Drug Poss
  DRT: Drug Traff
  FRA: Fraud
  HOM: Homicide
  MIS: Mischief
  ROB: Robbery
  VEH: Veh Theft
  BKT: Bike theft
  SXV: Sex Violation
  SHT: Shooting/Firearm
  THV: Theft over
  MSP: Missing person
  OTH: Other
}
```
# vow.crime_stat
deprecated, use db.aggragate to find stat

## TODO: add crime data to census21 db
for bnds calc number of crime