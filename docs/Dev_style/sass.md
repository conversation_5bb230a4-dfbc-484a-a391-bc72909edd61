Starter
source folder: src/style/sass

```
cd src
npm install
```

## For VUE: sass includes in general vue compilation
## For CSS files:
  ``cd src``
### watch sass file changes:
```
  npm run sass:watch:web
  npm run sass:watch:app
```
### build css file:
```
  npm run sass:build:web
  npm run sass:build:app
```

sass syntax example in appSettings.vue and style/sass/ folder

https://css-tricks.com/sass-style-guide/

offical documentation: https://sass-lang.com

bash sass usage: https://sass-lang.com/documentation/cli/dart-sass/