cpmBranchUpdate-2019-05-16
更新数据：
./start.sh batch/fixCpmbalance.coffee

由于上次更新，inc balance的时候 忘记用负数，导致数据出错，同时只inc，没有set的过程无法判断是否到了新的一天。算法有问题，修改如下。
新的广告策略：
存当天的vc，当前的balance
根据当天的vc，计算当天剩余的balance，当前小时剩余的balance。
如果当前小时权重为0，不出广告。
如果之前有剩余，全都加到当前小时上，用完为止。


cpmBranchUpdate-2019-03-29

广告使用说明
管理员：
))*
Impression Ads Manage
新建编辑广告。
))*
general service status setting(translate/propSoldPrice)
设置广告价钱

地图preview修改。增加广告
论坛修改：
文件从webdev移动到coffee4client,
fix share with comments

涉及到topagent的修改：
分离了transaction modal，
Topagent, transaction.里加了uid，代替eml查询。
