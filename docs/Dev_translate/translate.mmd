sequenceDiagram
    participant Task as 任务 (Task)
    participant Manager as 翻译管理器 (TranslationManager)
    participant Translator as 翻译器 (Translator)

    Task->>Manager: translate(message, translatorList)
    Manager->>Manager: getAvailableTranslator()
    alt 有可用翻译器
        Manager->>Task: 返回翻译器
    else 无可用翻译器
        Manager->>Manager: 任务进入等待队列
        Task-->>Manager: await Promise
        Note over Task,Manager: 任务被挂起
    end

    Task->>Translator: 执行翻译任务 (translate)
    Translator->>Task: 返回翻译结果
    Translator->>Manager: release()
    Manager->>Manager: processWaitingQueue()
    alt 等待队列不为空
        Manager->>Manager: 唤醒第一个等待任务
        Note over Task,Manager: 被唤醒的任务继续执行
        Manager->>Manager: getAvailableTranslator()
    end
