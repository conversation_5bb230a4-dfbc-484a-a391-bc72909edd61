## description
添加edm_ads coll 在使用edm notify发送的邮件中添加广告模块

## 数据格式
  基本格式
  {
    type:banner/project/realtor/mortgage/edm/splash 广告类型 
        首页banner/邮件广告/开屏广告
    ts:date 模版创建时间
    src:'' 开屏广告限制尺寸（1068*1920）,banner限制640x180
    tgt:'' 跳转链接,配合inapp， 如果选中inapp，则link应以/开头
    inapp:是否为app内跳转
    lang:zh/en/zhCn/kr
    grp:all/realtor/notRealtor/noFloow multiple 发送目标人群
    area:china/nochina multiple 生效区域 国内/国外
    uid:user._id
    st:{number} start time
    et:{number} end time
    prov:指定省份
    <!-- 统计 -->
    vc:0 #view count
    cc:0 #click count
    <!-- splash -->
    vc:
    cc:
    skip:
    <!-- splash -->

    stat:{}按天统计数据
  }
#### banner:{
    src:'' 限制尺寸  640x180
    inapp: 在app中跳转
    name:banner name
    name_en:banner name
    noend: event no end day
    order: for sort, high-low
    rm:在rm首页展示
    mls:在mls首页展示
  }
#### project{
    builder:project builder
    city:project city
    desc:project desc chinese version
    desc_en:project desc english version
    inapp: 在app中跳转
    name:proj name
    name_en:proj name
    noend: event no end day
    order: for sort, high-low
    tl: project title
    pid: project id
  }
#### realtor{
    inapp: 在app中跳转
    noend: event no end day
  }
#### mortgage{
    inapp: 在app中跳转
    noend: event no end day
  }
#### edm{
    desc:'' description chinese version
    desc_en:'' description english version
    dsclsr:'' disclosure chinese version
    dsclsr_en:'' disclosure english version
    posn:top/middle/bottom position multiple {edm only}
    batch:week/daily/saved multiple {edm only}
    <!-- 统计 -->
    tc:0 total count 发送或者展示
    [weekily/daily/saved]send:0 #携带广告发送的数量
    sendc:0
    showc:0
    stat中添加position相关统计
  }
#### splash{
    src:'' 限制尺寸  1080x2340
    noend: event no end day
  }
