<mxfile host="65bd71144e">
    <diagram id="yttY2iC1YdI07kNKlu8a" name="Page-1">
        <mxGraphModel dx="1216" dy="682" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" style="edgeStyle=none;html=1;" edge="1" parent="1" source="4" target="7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="event.userlog" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="330" y="135" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;" edge="1" parent="1" source="5" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="(host.)event.userlog" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" vertex="1" parent="1">
                    <mxGeometry x="330" y="425" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" style="edgeStyle=none;html=1;" edge="1" parent="1" source="7" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="mm2 in US" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="430" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="China APP/Web" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;" edge="1" parent="1" source="10" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="Non-China APP/Web" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="12" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="log process" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="580" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="Apache Doris or ClickHouse" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="630" y="570" width="60" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>