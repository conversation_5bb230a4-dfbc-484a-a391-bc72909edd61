<mxfile host="65bd71144e">
    <diagram id="OZZXLQ2i3qRPyKpvqVRo" name="Page-1">
        <mxGraphModel dx="2066" dy="370" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="5" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="TRB,RAHB,BRE,DDF,TrbM,BreM" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;whiteSpace=wrap;size=15;html=1;bpmnTransferType=input;" parent="1" vertex="1">
                    <mxGeometry x="-80" y="40" width="40" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="8" style="edgeStyle=none;html=1;" parent="1" source="3" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;endArrow=classicThin;endFill=1;" parent="1" source="3" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="270" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="3" value="import. [trebMan,treb,deleteVow,bcre,bcreMan,rahb,ddf]&lt;br&gt;update.[addr, photo]" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="270" y="180" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="6" style="edgeStyle=none;html=1;" parent="1" source="4" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="4" target="15" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="Source Download Procedures" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="260" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=diamond;endFill=1;" parent="1" source="7" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=37.5;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="7" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="560" y="310" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="7" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="Data Transform &amp;amp; Process" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="260" y="332.5" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="31" style="edgeStyle=none;html=1;endArrow=classicThin;endFill=1;" parent="1" source="9" target="26" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;" parent="1" source="9" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="save.property" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="283.25" y="570" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="GeoCoder" style="shape=ellipse;html=1;dashed=0;whitespace=wrap;perimeter=ellipsePerimeter;" parent="1" vertex="1">
                    <mxGeometry x="560" y="372.5" width="90" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="Boundaries" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="562.5" y="280" width="85" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="RNI" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="550" y="30" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="25" style="edgeStyle=none;html=1;entryX=0.625;entryY=0.2;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="17" target="24" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="148" y="452" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" style="edgeStyle=none;html=1;endArrow=classicThin;endFill=1;" parent="1" source="17" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="43" style="edgeStyle=none;html=1;entryX=0.983;entryY=0.075;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="17" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="Photo&lt;br&gt;Download" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="336" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="19" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="TRB,DDF,BRE" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;whiteSpace=wrap;size=15;html=1;bpmnTransferType=input;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="336" width="40" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="File System" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="444" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="26" target="28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="51" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classic;endFill=1;" parent="1" source="26" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="Save to DB" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="260" y="752.5" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="listing.properties" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="550" y="732.5" width="90" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="36" style="edgeStyle=none;html=1;" parent="1" source="33" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="Filter &amp;amp; Copy" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="752.5" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="img_tmp" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry y="444" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="Other System (Avion/4salebc)" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="10" y="1740" width="650" height="290" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="xxx.save.property" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="44" vertex="1">
                    <mxGeometry x="93.25" y="30" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="Save to DB" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="44" vertex="1">
                    <mxGeometry x="70" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="41" style="edgeStyle=none;html=1;" parent="44" source="35" target="39" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="listing.properties" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="44" vertex="1">
                    <mxGeometry x="373.25" y="150" width="90" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="38" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="44" source="39" target="40" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="Update Addr, Update Photo," style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="45" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="Main App/Web" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-100" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="50" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="updated.property" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="283.25" y="870" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="56" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="53" target="55" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="53" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="86" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="53" target="78" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="User Notify" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="260" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="55" value="users, profile, showing, fav, user_log" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="537.5" y="980" width="110" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="60" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="57" target="59" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="send.pushNotify.[10,20]" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="340" y="1090" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="62" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classicThin;endFill=1;" parent="1" source="59" target="61" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="Remove/Update &lt;br&gt;failed Token" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="62" vertex="1" connectable="0">
                    <mxGeometry x="-0.1867" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="65" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="59" target="96" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="510" y="1135" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" value="Send PN" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="65" vertex="1" connectable="0">
                    <mxGeometry x="-0.0088" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="Push Notify" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1195" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="users, pn_log" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="537.5" y="1180" width="110" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="68" style="edgeStyle=none;html=1;endArrow=classicThin;endFill=1;" parent="1" source="67" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="Send to systtem admin" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="68" vertex="1" connectable="0">
                    <mxGeometry x="-0.0916" y="-4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="87" style="edgeStyle=none;html=1;" parent="1" source="67" target="78" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="67" value="APP.Web&lt;br&gt;System Alert/Notify" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="1060" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="71" style="edgeStyle=none;html=1;endArrow=classicThin;endFill=1;" parent="1" source="70" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="88" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="70" target="78" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="APP/Web&lt;br&gt;form/event input" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-90" y="1210" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="74" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="73" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="75" value="Market Assignment" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="74" vertex="1" connectable="0">
                    <mxGeometry x="0.0101" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="Main App/Web" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-100" y="564" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="77" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="78" target="83" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="78" value="send.email.[10,20]" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="196.5" y="1300" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="79" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="83" target="84" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="Mark&lt;br&gt;failed Email" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="79" vertex="1" connectable="0">
                    <mxGeometry x="-0.1867" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="81" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=classicThin;endFill=1;" parent="1" source="83" target="97" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="510" y="1325" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="82" value="Send Email" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="81" vertex="1" connectable="0">
                    <mxGeometry x="-0.0088" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="83" value="Send Email" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="users, email_error_log" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="560" y="1390" width="110" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="90" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=classic;endFill=1;" parent="1" source="89" target="93" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="89" value="event.SES" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="196.5" y="1460" width="73.5" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="92" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=classic;endFill=1;" parent="1" source="91" target="89" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="91" value="SNS Webhook" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-80" y="1454" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="94" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="93" target="84" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="95" value="Mark failed, &lt;br&gt;log open/clicked" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="94" vertex="1" connectable="0">
                    <mxGeometry x="-0.2097" y="2" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="93" value="email log/analytics" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1454" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="96" value="APN/Firebase" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=default;fontFamily=Helvetica;" parent="1" vertex="1">
                    <mxGeometry x="527.5" y="1120" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="97" value="SMTP/AWS" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=default;fontFamily=Helvetica;" parent="1" vertex="1">
                    <mxGeometry x="545" y="1309" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="99" value="&lt;h1&gt;queue.[10,20]&lt;/h1&gt;&lt;p&gt;Use two or more queues to prioritize requests. Give higher priority queue separate line to process. Always process queue 10 before 20 or higher number.&lt;/p&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;fontFamily=Helvetica;" parent="1" vertex="1">
                    <mxGeometry x="190" y="1550" width="300" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="100" value="&lt;h1&gt;Naming&lt;/h1&gt;&lt;div&gt;(prefix.)action/adjective.object(.priority)&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;camelCase starts with lowercase. Except acronym.&amp;nbsp;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;fontFamily=Helvetica;" parent="1" vertex="1">
                    <mxGeometry x="500" y="1560" width="300" height="120" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>