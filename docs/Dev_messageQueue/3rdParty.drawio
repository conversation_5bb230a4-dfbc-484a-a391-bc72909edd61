<mxfile host="65bd71144e">
    <diagram id="X1HL0qzF9vUigWUe286a" name="Page-1">
        <mxGraphModel dx="1024" dy="590" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="27" value="Gateway Server" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;" vertex="1" parent="1">
                    <mxGeometry x="360" y="130" width="150" height="430" as="geometry"/>
                </mxCell>
                <mxCell id="2" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="70" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;" parent="1" source="3" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="req.xxx" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="70" y="180" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=none;html=1;" parent="1" source="6" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="6" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Request PG" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="60" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="db" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="350" y="30" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;" parent="1" source="8" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="host.req.xxx" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="390" y="180" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="17" style="edgeStyle=none;html=1;" parent="1" source="9" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" style="edgeStyle=none;html=1;" parent="1" source="9" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="mm2" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;" parent="1" source="10" target="14" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="10" target="20" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="Program" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="520" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="13" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="70" y="520" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" style="edgeStyle=none;html=1;endArrow=diamond;endFill=1;" parent="1" source="13" target="23" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="host.resp.xxx" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="70" y="470" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0.825;entryY=1.008;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="14" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="resp.xxx" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.queue;fillColor=#D9A741;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="390" y="470" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="local DB" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="720" y="290" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="22" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=diamond;endFill=1;" parent="1" source="23" target="24" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="Request PG" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="60" y="590" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="db" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="350" y="580" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="&lt;h1&gt;req/resp&lt;/h1&gt;&lt;div&gt;This naming is reserved for 3rd party or outside service processes.&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="580" y="450" width="220" height="120" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>