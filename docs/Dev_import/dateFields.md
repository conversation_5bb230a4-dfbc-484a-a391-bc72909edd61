### Treb:

### idx,vow,evow
|fld | val | type|mapping to |
|----|-----|-----|------------
|phoMt    | 2020-10-20 03:44:20.000Z|utc| mt
|Pix_updt | 2021-04-09 13:47:04.0  | local string| phomt,may used for onD
|mt       | 2021-05-13 04:47:39.670Z| utc| mt, offD
|ld       | 2020-10-19 00:00:00.0| local string | onD
|Dt_ter   | 2021-05-04 00:00:00.0|
|input_date |2020-10-19 00:00:00.0
|Poss_date  |2021-06-30 00:00:00.0
|Unavail_dt |2021-05-04 00:00:00.0
|Timestamp_sql  |2021-05-04 17:11:19.0
|xd             |2021-10-21 00:00:00.0


### trebManual
|fld | val | type|mapping to |
|----|-----|-----|------------
|ld         |/Date(1620273600000-0400)/ | treb string, ios with timezone| onD
|unavail_dt |/Date(1621569600000-0400)/
|cd         |/Date(1627704000000-0400)/||sldd
|xd         |/Date(1627704000000-0400)/||xd
|mt         | 2021-05-25 04:05:09.965Z| utc| mt, offD

dta

|fld | val | type|mapping to |
|----|-----|-----|------------
|Input_date |9/28/2021| | onD
|Lud |9/28/2021 | mt
|pctd|6/3/2021 10:58:53 PM
|poss_date|9/30/2021
|Pix_ts|20210405.135324
|xd|9/30/2021


### DDF

|fld | val | type|mapping to |
|----|-----|-----|------------
|ListingContractDate |2021-07-08,11/01/2013| string dd/mm/yyyy| onD
|PhotoDlDate|2016-03-18 00:00:00|string|onD

### BCRE
|fld | val | type|mapping to |
|----|-----|-----|------------
| ExpiryDate|9/13/2021| string mm/dd/yyyy| onD
|ListDate|4/19/2021|local string
|mt|2021-05-14 15:28:05.638Z|utc|onD
|ConfirmSoldDate|8/6/2021|


