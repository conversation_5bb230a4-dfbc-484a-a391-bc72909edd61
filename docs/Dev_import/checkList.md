# 问题列表

## import

1. 不同board房源download存在dry问题, eg: logHash数据检查添加,retry,数据保存等
2. download函数缺少unit test, 存在房源更新但是rni数据没有更新的cases
3. 从第三方下载房源长时间不更新的问题, 需要确认第三方没有更新还是我们没有获取推送
4. 图片下载保存,使用第三方url还是下载到自己服务器

## geocoding

1. city问题
   1. 使用的subCity/cmty
   2. 包含有特殊字符,eg: ()-+,罗马字符,数字
   3. 包含有town,village等后缀
   4. city没有统一,  eg: COQUITLAM 和 PORT COQUITLAM
   5. zip错误,不同城市存在相同的address,给错了zip导致geoCoding出错
   6. 源数据的city/cmty/addr等会添加一些额外信息,需要进行format处理,eg: Toronto C07, Newmarket (Newmarket Industrial Park)
2. addr问题
   1. 拼写错误, st/st_sfx拼写错误,影响format,eg:Elizabeth-><PERSON>, Blvd->Bv
   2. 顺序错误,eg: 8 Rue Lanthier -> 8 Lanthier Rue
   3. 解析address中的unit
   4. origUnit错误
   5. addr包含多个unit eg: 205,210, 50 Nolanridge Crt NW -> unit:205,210
   6. st_num与address中不一致,eg: 203 Maple St -> orig_st_num:273
   7. st_sfx缩写mapping
   8. 存在特殊字符, basement,Ph,upper,townhouse
   9. 特殊的st_num, eg: Lot 11 Gord Vinson Ave
   10. 街道名为数字, eg: #8 -15 28TH ST N, unit:8, st_num:15
   11. st_sfx重复, eg: 3985 Grand Park Drive Dr N
   12. 不是所有房源都需要解析unt, 出租,condo,mfee,源数据提供unt
   13. 相同城市存在多个相同名称的街道, eg: TORONTO,120 HOMEWOOD AVE 有两个zip, M2M1K3和M4Y2J3
   14. st_sfx重复,但是无法判断, eg: 75 Street Nicholas ST
   15. 部分房源不将origAddr转为addr时, buildAddrFromSt 函数缺少对st_dir的拼接,仅拼接st_num,st,st_sfx, eg:房源TRBN11912131,origAddr:'#803A - 10 Rouge Valley Drive, Markham, ON L6G 0G9',    st_num: "10", st:"Rouge Valley",  st_sfx: "Dr", st_dir: "W"
   16. 项目代码对street direction的处理不统一, mapping过程中处理为st_dir, addr解析时处理为dir
3. unit错误
   1. 拼写错误,eg: 1A -> 1A1
   2. 街道名是数字时的解析, eg: 2108 10 St NW
   3. 解析出来的unt与origUnt不一致, eg: 15 Sherbourne Street Unit 10, origUnt:0
   4. 多个unit同时在address中存在, eg: 202/201 366 Clubhouse Dr, unit:201 202
   5. 多个unit在address中存在,并且存在和st_num一致的unit, eg: 1,2 1 Brookpark Blvd SW, unit:1,2 st_num:1
   6. unit在address中出现多次. eg: 309-220 KENYON STREET W UNIT#309  unit:309, st_num:220; #904-904 155 YORKVILLE AVE, unit:904
   7. 源数据提供的unit实际为st_num, eg: 4 Heritage Way Unit 204, origUnit:4, actual unit:204
   8. 源数据提供的unit格式为number, 需要转换为string使用
4. 跳过geoCoding时没有从geoCache获取信息,会导致房源uaddr不一致而产生没有merge的情况
5. geoCoding返回的city与源数据的city可能不一致,(city,subCity问题)
6. geoCoding时的address与返回的address可能不一致, 需要用geoCoding返回的address作为format uaddr(是否需要再次format address处理)
7. 可以使用相同uaddr的不同房源geoCoding后返回给房源使用的uaddr不一致, eg: CA:ON:TORONTO:75 ST NICHOLAS和CA:ON:TORONTO:75 NICHOLAS ST
8. address相似度判断函数存在问题,需要修复
   1. 27 HARBOURVIEW DR    ->  27 HARBOUR VIEW DR     现在函数认为false,应该为true
   2. 6 WESTBROOK AVE      ->  6 EASTBROOK AVE        现在函数认为true,应该为false
   3. 547 27TH AVE W       ->  547 28TH AVE W         现在函数认为true,应该为false
   4. 35 PLOUGHMAN'S CRES  ->  35 PLOUGHMANS CRES     现在函数认为false,应该为true
9. 对street为方位的街道处理存在问题, eg: 130 Rue North 目前会被处理为130 Rue N, 实际应该为不处理,或者处理为130 North Rue
10. 源数据out of area并且没有zip,loc等信息,仍然做了geocoding,需要验证房源数据是否有效
11. 错误地址处理geocoding，地址偏差和错误地址

## mapping

1. status/lst的判断问题,不同省份对Pending的定义不同,EDM/CLG的pending含义是suspend,处理错误会影响房源检索
2. 源数据存在错误,mapping/import过程中没有处理,会对app使用造成影响(eg:字段值为字符串NA,N/A,null,undefined,空字符串等,dom为负数)
3. 对sldd/ld等日期字段,需要处理成为number格式,需要对properties的结构以及字段定义添加格式说明
4. rooms的长度单位确认以及转换(前端默认返回的为meter)
5. open house的源数据时间为UTC iso date string，需要转换为local date time
6. la,存在只有OfficeName没有其它信息的cases,此时mapping需要添加判断并不添加la字段
7. 使用Map做对应时需要考虑undefined的情况，否则会覆盖掉原值, eg: ret.fce = DIRECTION_ABBR[ret.fce?.toUpperCase()], 当DIRECTION_ABBR不存在对应值时,会将ret.fce设置为undefined
8. mapping时不能默认字段内容存在,会导致转换时出现错误/无效数据, eg: PropertySubType可能不存在, 此时 record.ptype2.push record.PropertySubType 会导致ptype2存在null
9. mapping过程中会将sid/id/origSid等字段转为number,部分字段需要字符串格式,需要转换回来
10. status/lst的mapping需要统一,目前存在不同board对相同status转为lst不同的cases, eg: CAR对Hold转为Sus,bcreReso对Hold转为Haa

## transform

1. 房源循环merge问题, 因为有多个board,可能出现房源循环merge的情况,需要考虑显示优先级判断(房源打分逻辑), eg: A->B->C->D
2. 房源his.ts与spcts的差异,会影响pushNotify
3. 目前addHistoryForOldProp逻辑不清晰,需要整理重构
4. 房源生成picUrls，现在代码逻辑复杂，没有相关文档，存在前后端dry的情况,以及后端存在多个func,需要整理重构
5. 房源列表/详情的图片展示问题,部分地方(eg:evaluation)后端不获取图片,由前端获取(rmapp.listingPicUrls),但是由于前后端函数分开使用,并且图片获取存在多个func,需要dry并且重构
6. 房源详细展示内容,展示内容不统一,对Building/Land/Other等区分不明确,存在dry问题
7. 字段有效性判断,存在dom为负数的cases, eg: X6361120源数据提供的DaysOnMarket为-9803

## 统计

1. active房源数量的统计问题,目前的周/月统计是限定到最后一天之前没有下市的房源,不是限定时间内上市的房源
2. edmStat对展示内容的统计缺少null/undefined的判断,以及字段类型不统一
3. merged房源需要过滤掉,如果重构需要考虑不同board相同房源的统计问题

## 检索

1. 查询房源列表接口不统一,存在dry问题
2. merged/deleted房源检索权限控制,展示问题
3. 房源检索返回列表的排序问题
4. 后端返回给前端的数据字段控制,目前会返回很多不需要的信息
5. 房源列表/详情展示图片字段需要统一, eg: thumbUrl,picUrls,phoUrls,前端额外使用img等
6. 前端对status/lst的展示, 需要统一胶囊显示哪些内容,额外的MlsStatus显示在哪里,price off的状态显示, eg: 当lst不是'Pc'但是存在降价行为时不会展示降了多少价格(价格变动后lst改为Pc,当房源信息再次更新时lst会改回去)
7. 前端对房源成交日期的显示字段需要统一,后端根据offD/sldd来判断,前端根据spcts/mt/ts来显示

## 推送

1. spcts与his.ts的判断逻辑需要整理说明,之前出现过his.ts > spcts时才推送,目前为同一天变动时推送
2. getStatusLongName需要和所有的status进行统一/dry, 出现过新增MlsStatus但是getStatusLongName没有更新导致出现error的情况
