<mxfile host="65bd71144e">
    <diagram id="AhqRZB4kuoiP2DEG-h7x" name="overview">
        <mxGraphModel dx="1924" dy="1596" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-65" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-155" y="660" width="1005" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-63" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-155" y="280" width="1025" height="310" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-52" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-170" y="-770" width="1075" height="960" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-14" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-20" y="-1100" width="420" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-50" value="" style="edgeStyle=none;html=1;startArrow=none;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="FuloIg9Zkkq3iPyGNmpK-49" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="290" y="732" as="targetPoint"/>
                        <mxPoint x="230" y="751.0344827586205" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-18" value="bcreManual" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-110" y="725" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-55" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="110" y="865" as="targetPoint"/>
                        <mxPoint x="-15" y="865" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-19" value="bcreAuto" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-110" y="842.5" width="100" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-6" value="" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-9" target="p-lCuBrAOnFdzZ8LLya8-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="98.38000000000011" y="-354.9200000000001" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-9" value="idx" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-50" y="-375" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-7" value="" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-10" target="b5yyK5RsCXVblgwcsPuY-15" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="110" y="-505" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-10" value="trebManual" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-35" y="-515" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-11" value="crea" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-110" y="355" width="140" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-18" value="" style="edgeStyle=none;html=1;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-12" target="p-lCuBrAOnFdzZ8LLya8-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-12" value="evow" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-50" y="-55" width="90" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-9" value="" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-17" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="50" y="-600.0884353741496" as="sourcePoint"/>
                        <mxPoint x="110" y="-580" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-17" value="dta" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="-40" y="-600" width="90" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-42" value="" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=37.5;entryPerimeter=0;" parent="1" source="b5yyK5RsCXVblgwcsPuY-11" target="FuloIg9Zkkq3iPyGNmpK-15" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="235" y="-606.3953488372093" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-18" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-15" target="b5yyK5RsCXVblgwcsPuY-74" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1115" y="-80" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-15" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_dta_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="387.5" y="-620" width="190" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-19" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-19" target="b5yyK5RsCXVblgwcsPuY-74" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1070" y="-64.22000000000003" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-19" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_manual_records&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_manual_records_log&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="370" y="-530" width="225" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-43" value="" style="edgeStyle=none;html=1;exitX=1.015;exitY=0.389;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="b5yyK5RsCXVblgwcsPuY-15" target="FuloIg9Zkkq3iPyGNmpK-19" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="-509.1935483870968" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-22" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.632;entryY=0.992;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-49" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1093.7600000000002" y="5.220000000000027" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-49" value="&lt;meta charset=&quot;utf-8&quot;&gt;&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: menlo, monaco, &amp;quot;courier new&amp;quot;, monospace; font-weight: normal; font-size: 12px; line-height: 18px;&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_bcre_manual_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="460" y="720" width="225" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-70" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-54" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1070" y="5.779999999999973" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-71" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-54" target="b5yyK5RsCXVblgwcsPuY-68" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-54" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_bcre_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="347.5" y="825" width="195" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-76" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="9WjZ4EFDmvEfMEHPlldF-2" target="b5yyK5RsCXVblgwcsPuY-72" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-79" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="9WjZ4EFDmvEfMEHPlldF-2" target="b5yyK5RsCXVblgwcsPuY-78" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-81" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="9WjZ4EFDmvEfMEHPlldF-2" target="b5yyK5RsCXVblgwcsPuY-80" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-2" value="vow:Properties" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1240" y="-75" width="110" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-3" value="prop_parts Mongo" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1680" y="-269.61" width="110" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-4" value="&lt;span&gt;prop_parts postSql&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1720" y="-70" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-5" value="ES" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1670" y="120" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-20" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1040" y="-243"/>
                            <mxPoint x="1040" y="-74"/>
                        </Array>
                        <mxPoint x="1050" y="-74" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9WjZ4EFDmvEfMEHPlldF-21" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-45" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="980" y="-29.220000000000027" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-17" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-1" target="DuSC2G0q_YJ5Wn37FPwd-20" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="280" y="-360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-41" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-1" target="DuSC2G0q_YJ5Wn37FPwd-28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-44" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0;entryDx=0;entryDy=38.66379310344828;entryPerimeter=0;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-1" target="FuloIg9Zkkq3iPyGNmpK-25" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="p-lCuBrAOnFdzZ8LLya8-1" value="&lt;span style=&quot;font-size: 12px&quot;&gt;TrebDownload.coffee idx&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="100" y="-380" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-40" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-2" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="400" y="-27" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="400" y="-27"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-43" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-2" target="b5yyK5RsCXVblgwcsPuY-36" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-45" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-2" target="FuloIg9Zkkq3iPyGNmpK-25" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="560" y="-80" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="175" y="-80"/>
                            <mxPoint x="480" y="-80"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-46" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-2" target="DuSC2G0q_YJ5Wn37FPwd-28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="175" y="-80"/>
                            <mxPoint x="340" y="-80"/>
                            <mxPoint x="340" y="-243"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="p-lCuBrAOnFdzZ8LLya8-2" value="&lt;span style=&quot;font-size: 12px&quot;&gt;TrebDownload.coffee evow&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="92.5" y="-55" width="150" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-58" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-3" target="FuloIg9Zkkq3iPyGNmpK-16" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-59" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-3" target="FuloIg9Zkkq3iPyGNmpK-27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-62" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0;entryDx=0;entryDy=45;entryPerimeter=0;" parent="1" source="p-lCuBrAOnFdzZ8LLya8-3" target="FuloIg9Zkkq3iPyGNmpK-28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="p-lCuBrAOnFdzZ8LLya8-3" value="&lt;span style=&quot;color: rgb(106 , 153 , 85) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; background-color: rgb(30 , 30 , 30)&quot;&gt;creaDownload&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="100" y="360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="sHev6QmxGmu8JpkamDSC-1" value="" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-11" target="p-lCuBrAOnFdzZ8LLya8-3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="330" y="457.5" as="targetPoint"/>
                        <mxPoint x="80" y="457.5" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="sHev6QmxGmu8JpkamDSC-2" value="" style="edgeStyle=none;html=1;endArrow=none;" parent="1" source="DuSC2G0q_YJ5Wn37FPwd-18" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="110" y="750" as="targetPoint"/>
                        <mxPoint x="90" y="664.5" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-66" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="sHev6QmxGmu8JpkamDSC-6" target="FuloIg9Zkkq3iPyGNmpK-54" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="sHev6QmxGmu8JpkamDSC-6" value="&lt;span style=&quot;font-family: &amp;#34;helvetica&amp;#34;&quot;&gt;bcreDownload&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="110" y="835" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="nRjW3tKa-QSpvxCFfLyp-4" value="" style="edgeStyle=none;html=1;fontSize=14;entryX=0.391;entryY=-0.018;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="b5yyK5RsCXVblgwcsPuY-11" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="162" y="-660" as="sourcePoint"/>
                        <mxPoint x="173.695652173913" y="-620" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="nRjW3tKa-QSpvxCFfLyp-3" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;vow: data_download&lt;br&gt;for credential&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="77.5" y="-740" width="170" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="nRjW3tKa-QSpvxCFfLyp-5" value="all downloaded data is saved in rni db" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
                    <mxGeometry x="-15" y="-1100" width="310" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-1" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="-1060" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-3" value="batch in crontab" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="112.5" y="-1020" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-4" value="" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="-1015" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-5" value="nonstop batch" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="105" y="-1065" width="115" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-7" value="" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="-970" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-8" value="source data from third party" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="92.5" y="-970" width="217.5" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-9" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="32.5" y="-920" width="60" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-10" value="db" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="119" y="-910" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-11" value="dtaDownload.coffee" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="115" y="-610" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-12" value="" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="-840" width="80" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-13" value="API" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="130" y="-840" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-15" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85)&quot;&gt;API '&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_imp_/trb/&lt;/span&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85)&quot;&gt;'&amp;nbsp;&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85)&quot;&gt;dataImport.coffee&lt;/span&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="106.25" y="-522.5" width="157.5" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-36" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_evow_logs&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;evow_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_evow_ids&lt;/span&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_import_history&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="62.16" y="50" width="210.68" height="124.14" as="geometry"/>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-20" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_idx_logs&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_idx_ids&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;mls_treb_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;idx_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;records&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_import_history&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="69.66" y="-279.23" width="210.68" height="129.23" as="geometry"/>
                </mxCell>
                <mxCell id="DuSC2G0q_YJ5Wn37FPwd-28" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_master_records&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="380" y="-279.23" width="200" height="71.81" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-25" value="&lt;span style=&quot;color: rgb(206 , 145 , 120) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;mls_treb_master_ids&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="390.17" y="-166.55" width="179.83" height="61.55" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-45" value="&lt;span style=&quot;color: rgb(206 , 145 , 120) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;mls_treb_evow_delete_ids&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="400" y="-60" width="190" height="61.55" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-48" value="" style="endArrow=classic;startArrow=classic;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-25" target="b5yyK5RsCXVblgwcsPuY-49" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="680" y="10" as="sourcePoint"/>
                        <mxPoint x="770" y="60" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-50" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0;entryDx=0;entryDy=46.3575;entryPerimeter=0;" parent="1" source="b5yyK5RsCXVblgwcsPuY-49" target="DuSC2G0q_YJ5Wn37FPwd-28" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="810" y="-233"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-49" value="retsPhotoDownload.coffee treb" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="700" y="-165" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-60" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="b5yyK5RsCXVblgwcsPuY-51" target="FuloIg9Zkkq3iPyGNmpK-27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-51" value="photoDownload.coffee ddf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="670" y="380" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-16" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_crea_ddf_logs&lt;br&gt;&lt;/span&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_crea_import_history&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="64.99999999999997" y="478" width="190" height="72.5" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-27" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_crea_ddf_records&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="342.5" y="355" width="200" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="FuloIg9Zkkq3iPyGNmpK-28" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_crea_ddf_ids&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="342.5" y="470" width="185" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-53" value="Treb Download" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="710" y="-740" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-61" value="" style="endArrow=classic;startArrow=classic;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="FuloIg9Zkkq3iPyGNmpK-28" target="b5yyK5RsCXVblgwcsPuY-51" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="542.5" y="575" as="sourcePoint"/>
                        <mxPoint x="613.2106781186548" y="525" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-64" value="DDF Download" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="640" y="300" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-67" value="Bcre. Download" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="680" y="680" width="105" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-68" value="photoDownload.coffee bcre" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="630" y="830" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-84" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;" parent="1" source="b5yyK5RsCXVblgwcsPuY-72" target="9WjZ4EFDmvEfMEHPlldF-3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1610" y="-217"/>
                            <mxPoint x="1610" y="-217"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-72" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;watchPropAndUpdateMongo&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1400" y="-239.60999999999999" width="190" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-73" value="&lt;span style=&quot;color: rgb(106 , 153 , 85) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;API '&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;_imp_/trb&lt;/span&gt;&lt;span style=&quot;background-color: rgb(30 , 30 , 30) ; color: rgb(106 , 153 , 85) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;'&lt;br&gt;in dataImport&lt;/span&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="106" y="715" width="144" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-75" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="b5yyK5RsCXVblgwcsPuY-74" target="9WjZ4EFDmvEfMEHPlldF-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-74" value="&lt;span&gt;WatchAndImportToProperties&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="980" y="-70" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-83" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="b5yyK5RsCXVblgwcsPuY-78" target="9WjZ4EFDmvEfMEHPlldF-4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-78" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;watchPropAndUpdatePostSql&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1425" y="-60" width="185" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-82" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="1" source="b5yyK5RsCXVblgwcsPuY-80" target="9WjZ4EFDmvEfMEHPlldF-5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="b5yyK5RsCXVblgwcsPuY-80" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;watchPropAndUpdateElasticSearch&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1250" y="130" width="300" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="WC9fmAv327224TK77fM--1" value="&lt;span style=&quot;color: rgb(206 , 145 , 120) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;mls_treb_idx_ids&lt;/span&gt;&amp;nbsp;save actived ids.&lt;br&gt;&lt;br&gt;only mark propeties as inactive when ids not updated for 12 hours&amp;nbsp;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-160" y="-258.33" width="210" height="78.33" as="geometry"/>
                </mxCell>
                <mxCell id="WC9fmAv327224TK77fM--2" value="mls_bcre_deleted&lt;br&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="347.5" y="910" width="190" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="O1GELdH6KiyHu5YmwT9o" name="download(treb/crea)">
        <mxGraphModel dx="1107" dy="636" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="iI-cOJIMsT5jjl5ogIiY-0"/>
                <mxCell id="iI-cOJIMsT5jjl5ogIiY-1" parent="iI-cOJIMsT5jjl5ogIiY-0"/>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-0" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="60" y="190" width="430" height="490" as="geometry"/>
                </mxCell>
                <mxCell id="E-rsx3XsyxjsCeoGxvOS-1" value="To imporve&lt;br&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="540" y="30" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="E-rsx3XsyxjsCeoGxvOS-2" value="&lt;ul&gt;&lt;li&gt;read credential from db instead of from command line&lt;/li&gt;&lt;li&gt;&lt;br&gt;&lt;/li&gt;&lt;/ul&gt;" style="text;strokeColor=none;fillColor=none;html=1;whiteSpace=wrap;verticalAlign=middle;overflow=hidden;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="460" y="60" width="380" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="mziI3Mxf42N_X9gdPkJW-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;initialize&lt;br&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #6796e6&quot;&gt;1.&lt;/span&gt; parameter check,&lt;/div&gt;&lt;div&gt;   &lt;span style=&quot;color: #6796e6&quot;&gt;2.&lt;/span&gt; collections,&lt;/div&gt;&lt;div&gt;   &lt;span style=&quot;color: #6796e6&quot;&gt;3.&lt;/span&gt; get credentials&lt;/div&gt;&lt;div&gt;   &lt;span style=&quot;color: #6796e6&quot;&gt;4.&lt;/span&gt; connections,&lt;/div&gt;&lt;div&gt;   &lt;span style=&quot;color: #6796e6&quot;&gt;5.&lt;/span&gt; when error, exit process with error code&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="60" width="420" height="170" as="geometry"/>
                </mxCell>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-6" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="iI-cOJIMsT5jjl5ogIiY-1" source="mziI3Mxf42N_X9gdPkJW-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="680" y="310" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mziI3Mxf42N_X9gdPkJW-1" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;font color=&quot;#d4d4d4&quot;&gt;queryAndDownloadRecord&lt;/font&gt;&lt;br&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;get records since last download&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;font color=&quot;#6a9955&quot;&gt;for evow, also download deleted listings&lt;/font&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=default;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="105" y="270" width="335" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-3" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="iI-cOJIMsT5jjl5ogIiY-1" source="mziI3Mxf42N_X9gdPkJW-2" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="530" y="411.17647058823525" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mziI3Mxf42N_X9gdPkJW-2" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;getAvailables&lt;/div&gt;&lt;p style=&quot;text-align: left ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;for idx, evow commercial, ddf records&lt;/p&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=default;fillColor=#2A2927;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="105" y="370" width="330" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-7" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.403;entryY=0.983;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=14;" parent="iI-cOJIMsT5jjl5ogIiY-1" source="mziI3Mxf42N_X9gdPkJW-3" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="594.48" y="448.6400000000001" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-8" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="iI-cOJIMsT5jjl5ogIiY-1" source="mziI3Mxf42N_X9gdPkJW-3" target="L_3DC3e8BzM2hAeg9GDL-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="785" y="540"/>
                        </Array>
                        <mxPoint x="765" y="370" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mziI3Mxf42N_X9gdPkJW-3" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;setStatusU&lt;br&gt;expire idx, evow commercial records which is not in avaulables&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="115" y="490" width="320" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="mziI3Mxf42N_X9gdPkJW-6" value="sleep when error or update count is too small&lt;br&gt;update processStatus" style="rounded=0;whiteSpace=wrap;html=1;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="115" y="600" width="330" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="nlgY-dSzqv8Me9BIOs5r-1" value="&lt;font style=&quot;font-size: 14px&quot;&gt;Main loop, run forever&lt;/font&gt;" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="140" y="210" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="L_3DC3e8BzM2hAeg9GDL-0" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_idx&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_ids&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_ddf&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_ids&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_evow&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_ids&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="530" y="360" width="170" height="95" as="geometry"/>
                </mxCell>
                <mxCell id="L_3DC3e8BzM2hAeg9GDL-1" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_idx&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_records&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_ddf&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_records&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_evow&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;_records&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_idx_logs&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_evow_logs&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_treb_evow_delete_ids&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="670" y="190" width="220" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="L_3DC3e8BzM2hAeg9GDL-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;trebDownload.coffe idx&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85)&quot;&gt;trebDownload.coffe evow&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;creaDownload.coffee&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: #6a9955&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;" parent="iI-cOJIMsT5jjl5ogIiY-1" vertex="1">
                    <mxGeometry x="190" y="730" width="240" height="90" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="us9WZ_HgD671WO57tPYd" name="watchAndImportToProperties">
        <mxGraphModel dx="1924" dy="1468" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="sCkIcAjsY36WDOxj_e-U-0"/>
                <mxCell id="sCkIcAjsY36WDOxj_e-U-1" parent="sCkIcAjsY36WDOxj_e-U-0"/>
                <mxCell id="C60bgJNwPqtOcPjQ03F0-1" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="370" y="-810" width="1490" height="1010" as="geometry"/>
                </mxCell>
                <mxCell id="C60bgJNwPqtOcPjQ03F0-3" value="" style="rounded=0;whiteSpace=wrap;html=1;fontSize=24;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="-630" y="-680" width="890" height="810" as="geometry"/>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-4" style="html=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;fontSize=14;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;edgeStyle=orthogonalEdgeStyle;" parent="sCkIcAjsY36WDOxj_e-U-1" source="2Czpfvj03H4Ts-qEw4II-5" target="F1dI7lXzM6tUKDPWWrqj-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="210" y="-545.909090909091" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="JV2XwysYsOPdOq727dYZ-0" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="sCkIcAjsY36WDOxj_e-U-1" source="IIoNJWk8UWwdsSpk73q2-0" target="F1dI7lXzM6tUKDPWWrqj-1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="IIoNJWk8UWwdsSpk73q2-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_dta_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="40" y="-630" width="190" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-5" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="s6e35jcMBYsDqxEAtWrS-0" target="F1dI7lXzM6tUKDPWWrqj-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="s6e35jcMBYsDqxEAtWrS-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_manual_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="40" y="-730" width="170" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="2Czpfvj03H4Ts-qEw4II-5" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_treb_master_records&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="31.25" y="-550" width="200" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-14" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="2Czpfvj03H4Ts-qEw4II-6" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="270" y="-398.191823899371" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2Czpfvj03H4Ts-qEw4II-6" value="&lt;span style=&quot;color: rgb(206 , 145 , 120) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;mls_treb_evow_delete_ids&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="22.5" y="-430" width="207.5" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-4" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="q9Rp1dWzbbexEO4Rgrn3-5" target="F1dI7lXzM6tUKDPWWrqj-6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="q9Rp1dWzbbexEO4Rgrn3-5" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;mls_crea_ddf_records&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="30" y="-290" width="160" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-7" value="" style="edgeStyle=none;html=1;fontSize=14;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="82k5M2eLYPQnXMP7_clH-0" target="PGKiuUjwco12wOoNXkO9-9" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="310" y="-155" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="82k5M2eLYPQnXMP7_clH-0" value="&lt;meta charset=&quot;utf-8&quot;&gt;&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: menlo, monaco, &amp;quot;courier new&amp;quot;, monospace; font-weight: normal; font-size: 12px; line-height: 18px;&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_bcre_manual_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="30" y="-190" width="180" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-8" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="F1dI7lXzM6tUKDPWWrqj-0" target="F1dI7lXzM6tUKDPWWrqj-8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_bcre_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="30" y="-80" width="190" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-19" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="F1dI7lXzM6tUKDPWWrqj-1" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-1" value="impMappingTreb.coffee" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="425" y="-600" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-18" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="F1dI7lXzM6tUKDPWWrqj-2" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-2" value="impMappingTrebManual.coffee" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="405" y="-730" width="210" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-21" style="edgeStyle=none;html=1;fontSize=14;entryX=-0.024;entryY=0.597;entryDx=0;entryDy=0;entryPerimeter=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="F1dI7lXzM6tUKDPWWrqj-6" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="-490" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-6" value="impMappingDDF" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="425" y="-290" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-23" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="F1dI7lXzM6tUKDPWWrqj-8" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="670" y="-480" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="F1dI7lXzM6tUKDPWWrqj-8" value="impMappingBcreAuto" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="425" y="-80" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-1" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85)&quot;&gt;&lt;font style=&quot;font-size: 24px&quot;&gt;watchAndImportToProperties&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="-600" y="-745" width="510" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-22" style="edgeStyle=none;html=1;fontSize=14;entryX=-0.019;entryY=0.884;entryDx=0;entryDy=0;entryPerimeter=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-9" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="630" y="-470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-9" value="impMappingBcreManual" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="420" y="-200" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-20" style="edgeStyle=none;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="380" y="-400.7142857142858" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-30" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-16" target="PGKiuUjwco12wOoNXkO9-26" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-16" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace&quot;&gt;formatProp&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;/div&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; background-color: rgb(30 , 30 , 30)&quot;&gt;from&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; background-color: rgb(30 , 30 , 30)&quot;&gt;impFormat.coffee:&lt;br&gt;&lt;br&gt;&lt;/span&gt;&lt;div style=&quot;color: rgb(204, 204, 204); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; font-size: 12px; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;setOrigId&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(156 , 220 , 254) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace&quot;&gt;formatAddress&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;formatSqft&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;color: rgb(204, 204, 204); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;formatTrbtp&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;formateBasment&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;formatDate(onD offD,sldd)&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;formatNumberFlds&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;checkPropPrice(log when error. keep one when lp = lpr)&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;color: rgb(204, 204, 204); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;checkDom&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;color: rgb(204, 204, 204); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;setSstpAndSptp&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;align=left;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="710" y="-690" width="240" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-28" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-24" target="PGKiuUjwco12wOoNXkO9-27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-24" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;geocode&lt;/span&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; color: rgb(220 , 220 , 170)&quot;&gt;from new_geocoder.coffee&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;通过房源的Uaddr检查geo_cache，判断使用cache或者prop或者do real geocoding&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;在cache里找到q&amp;gt;=100或者半年以内的地址，和房源比较，选择更优先的使用。如果是手动导入的source，保存到cache里&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;align=left;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="1470" y="-630" width="300" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-29" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-26" target="PGKiuUjwco12wOoNXkO9-24" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-26" value="&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;checkProp&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;function in saveToMaster&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;do not import if has:&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span style=&quot;background-color: rgb(30 , 30 , 30) ; color: rgb(212 , 212 , 212) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px&quot;&gt;missing required fields&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;[&lt;span style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace&quot;&gt;'saletp', 'ptype', 'src','cnty', 'prov', 'city'&lt;/span&gt;]&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;no price&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;ptype is not string&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;saved to tmp.&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;properties_import_log&lt;/span&gt;&amp;nbsp;when error&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="1050" y="-630" width="250" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-32" value="" style="edgeStyle=none;html=1;fontSize=14;exitX=0.444;exitY=1.013;exitDx=0;exitDy=0;exitPerimeter=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-27" target="PGKiuUjwco12wOoNXkO9-31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Z4If6IE_83yki3jJoqyJ-2" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="Z4If6IE_83yki3jJoqyJ-1" target="PGKiuUjwco12wOoNXkO9-16" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-27" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;mergeProp function in saveToMaster&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;merge with old properties&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;merge same records when id is different.&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;lower priority prop has fields merged: _id.&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;1. Query existing or duplicate listing (by onD,lp, uaddr sid)&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;2. Merge fields&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;3. check priority according by source&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; color: rgb(212 , 212 , 212) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;when new is Lower Priority&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; color: rgb(212 , 212 , 212) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;only update specified fields,&lt;/span&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;set new.merged = old._id&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; text-align: left ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170)&quot;&gt;save merged properties. but do not show when query(unless query by id)&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; color: rgb(212 , 212 , 212) ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; color: rgb(212 , 212 , 212) ; line-height: 18px&quot;&gt;&lt;div style=&quot;text-align: left ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;If new is Not lower Priority&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #569cd6 ; font-weight: bold&quot;&gt;update all fields.&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;setImportDate&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;mergeLa&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;addHistory&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;mergeFieldsShouldNotOverwrite&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;addRmSqft&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="660" y="-310" width="390" height="460" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-34" value="" style="edgeStyle=none;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" source="PGKiuUjwco12wOoNXkO9-31" target="PGKiuUjwco12wOoNXkO9-33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-31" value="&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;add Tags&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;from propertyTagHelper&lt;/span&gt;.&lt;span style=&quot;color: #dcdcaa&quot;&gt;addtags&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;add sldDom,sldDiff, school, census,boundary, transit tags&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;if loc is not changed, do not recalculate tag&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;unset tags which is not in new prop&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=14;rounded=0;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="1090" y="-160" width="360" height="205" as="geometry"/>
                </mxCell>
                <mxCell id="PGKiuUjwco12wOoNXkO9-33" value="save to Properties&lt;br&gt;&lt;br&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;unset require fields (br_plus...)&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;set merged flag&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;save to log&lt;br&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;'tmp'&lt;/span&gt;,&lt;span style=&quot;color: #ce9178&quot;&gt;'properties_import_log'&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=14;rounded=0;align=left;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="1535" y="-135" width="255" height="155" as="geometry"/>
                </mxCell>
                <mxCell id="C60bgJNwPqtOcPjQ03F0-2" value="&lt;font style=&quot;font-size: 24px&quot;&gt;saveToMaster.coffee&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="880" y="-850" width="190" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="C60bgJNwPqtOcPjQ03F0-4" value="&lt;font style=&quot;font-size: 14px&quot;&gt;1. init&amp;nbsp;&lt;br&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;-h(query back hours) required， get new token，import from back hours.&lt;/div&gt;2. routine&lt;br&gt;find resume token in sysdata, start from resume token. if token is invalide, start from the token&amp;nbsp;&lt;span style=&quot;background-color: rgb(30 , 30 , 30) ; color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px&quot;&gt;savedTokenClusterTs&lt;/span&gt;&lt;span&gt;.&amp;nbsp; query&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(156 , 220 , 254) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px&quot;&gt;_mt&amp;gt; sysdata.&lt;/span&gt;&lt;span style=&quot;background-color: rgb(30 , 30 , 30) ; color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px&quot;&gt;savedTokenClusterTs&lt;/span&gt;&lt;br&gt;3. onchange&lt;br&gt;To keep order,when change , save to queue and then process one by one&lt;br&gt;mapping-&amp;gt;format-&amp;gt;geocoding-&amp;gt;merge-&amp;gt;addTag-&amp;gt;saveToDB&amp;nbsp;&lt;br&gt;&lt;br&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="-570" y="-610" width="480" height="480" as="geometry"/>
                </mxCell>
                <mxCell id="JV2XwysYsOPdOq727dYZ-3" value="Convert by Source" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="380" y="-800" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="JV2XwysYsOPdOq727dYZ-4" value="&lt;span style=&quot;color: rgb(156 , 220 , 254) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;deleteAndBackupEvowProp&lt;br&gt;function&lt;br&gt;&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="260" y="-440" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Z4If6IE_83yki3jJoqyJ-3" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" parent="sCkIcAjsY36WDOxj_e-U-1" source="Z4If6IE_83yki3jJoqyJ-0" target="Z4If6IE_83yki3jJoqyJ-1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Z4If6IE_83yki3jJoqyJ-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #ce9178&quot;&gt;mls_rahb_records&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="31.25" y="20" width="190" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="Z4If6IE_83yki3jJoqyJ-1" value="impMappingRahb" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="425" y="25" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Z4If6IE_83yki3jJoqyJ-6" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;quot;menlo&amp;quot; , &amp;quot;monaco&amp;quot; , &amp;quot;courier new&amp;quot; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(206, 145, 120); text-align: left;&quot;&gt;props_watch_import_log&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;html=1;boundedLbl=1;backgroundOutline=1;size=15;whiteSpace=wrap;" parent="sCkIcAjsY36WDOxj_e-U-1" vertex="1">
                    <mxGeometry x="360" y="-1050" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="vkdDc6QvCSLJQfKWweWb-4" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;&amp;nbsp;NOTE:&lt;/span&gt;&lt;/div&gt;在做impMapping之前会先记录logs到tmp.properties_import_log" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#CC0000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=south;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=20;pointerEvents=1;align=left;" vertex="1" parent="sCkIcAjsY36WDOxj_e-U-1">
                    <mxGeometry x="200" y="-857.5" width="190" height="75" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="8GTqg5eq9qbiNzfVbDc0" name="geocoding">
        <mxGraphModel dx="1953" dy="1669" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-0"/>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-1" parent="fzO2dva-NlQsZcq2xKeE-0"/>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-33" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="110" y="-460" width="790" height="1010" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-69" value="" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="80" y="590" width="910" height="830" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-36" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-3" target="Yxa9xKBjVdZ2Dxa1vjHN-34" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-3" value="input addressObj&amp;nbsp;&lt;br&gt;{addr, city, prov, cnty(default CA), lat,lng(optional)}" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="280" y="-340" width="236" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-3" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-6" target="Yxa9xKBjVdZ2Dxa1vjHN-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-4" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Yxa9xKBjVdZ2Dxa1vjHN-3" vertex="1" connectable="0">
                    <mxGeometry x="0.0903" y="-4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-8" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-6" target="Yxa9xKBjVdZ2Dxa1vjHN-7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-9" value="&amp;nbsp;No&amp;nbsp; (ddf, treb)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="Yxa9xKBjVdZ2Dxa1vjHN-8" vertex="1" connectable="0">
                    <mxGeometry x="0.244" y="-4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-6" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;cacheResultIsBetter&lt;br&gt;than input q&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="279.5" y="50" width="250" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-39" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;  &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; geocoding score&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;src = man, treb, ddf, google/bing/here/mapbox/osm/mapquest&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; q = 130, manual fixed&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt;&amp;nbsp;rq = 120, computed by stat with condos of same uaddr&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;font face=&quot;menlo, monaco, courier new, monospace&quot;&gt;(&lt;/font&gt;&lt;font face=&quot;Menlo, Monaco, Courier New, monospace&quot;&gt;refineGeocoding.coffee, should run regularly.&amp;nbsp;&lt;/font&gt;&lt;font face=&quot;menlo, monaco, courier new, monospace&quot;&gt;)&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; q = 105, from treb manual import&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; q = 101, from ddf Address&lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; q = 100, from geocoding engine. &lt;/div&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; text-align: left&quot;&gt;      &lt;span style=&quot;color: #6796e6&quot;&gt;*&lt;/span&gt; q &amp;lt; 100, from geocoding engine. redo geocoding when over half years.&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;font color=&quot;#d4d4d4&quot; face=&quot;menlo, monaco, courier new, monospace&quot;&gt;&lt;span style=&quot;font-size: 12px ; background-color: rgb(30 , 30 , 30)&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;geoCacheOrig&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;save original result from engine. query by qaddr&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;geo_cache&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;&lt;div&gt;{&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;_id&quot; : &quot;CA:ON:CLEARVIEW:7300 HIGHWAY 26 AVE&quot;,&lt;/div&gt;&lt;div&gt;&lt;span&gt;&amp;nbsp; &amp;nbsp; &quot;aUaddr&quot; : [&amp;nbsp;&lt;/span&gt;&lt;span&gt;&amp;nbsp; ], # all format related to this address&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;addr&quot; : &quot;7300 Hwy 26&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;city&quot; : &quot;Clearview&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;cmty&quot; : &quot;Stayner&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;cnty&quot; : &quot;CA&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;faddr&quot; : &quot;7300 Highway 26, Clearview, ON L0M, Canada&quot;,&amp;nbsp;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;lat&quot; : 44.4198471,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;lng&quot; : -80.0906478,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;mt&quot; : ISODate(&quot;2021-09-20T21:24:52.469Z&quot;),&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;nelat&quot; : 44.4159843824293,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;nelng&quot; : -80.0978582894731,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;prov&quot; : &quot;ON&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;ptype&quot; : &quot;b&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;ptype2&quot; : [&amp;nbsp;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &quot;Business&quot;,&amp;nbsp;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &quot;Without Property&quot;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; ],&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;q&quot; : 100,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;qaddr&quot; : &quot;7300 Highway 26 Ave,Clearview, ON, CA&quot;,&lt;span&gt;#use this for query by engine&lt;/span&gt;&lt;/div&gt;&lt;/span&gt;&lt;span&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;src&quot; : &quot;bing&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;st&quot; : &quot;Hwy 26&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;st_num&quot; : &quot;7300&quot;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;swlat&quot; : 44.4237098175707,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;swlng&quot; : -80.0834373105269,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;ts&quot; : ISODate(&quot;2021-09-20T21:24:52.469Z&quot;),&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;zip&quot; : &quot;L0M1S0&quot;,&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;rlat&quot; : 43.45048454, # refine lat, from stat&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;rlng&quot; : -80.493321825,&amp;nbsp;&lt;span&gt;&amp;nbsp;&lt;/span&gt;&lt;span&gt;# refine lng, from stat&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;dist&quot; : 14,&amp;nbsp;&lt;span&gt;&amp;nbsp;&lt;/span&gt;&lt;span&gt;# distance of refined location and&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;avgLat&quot; : 43.45048454, # avg lat of the same address&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;avgLng&quot; : -80.493321825,&amp;nbsp;&lt;span&gt;# avg lng of the same address&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;rq&quot; : 120,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;rsrc&quot; : &quot;stat&quot;,&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;distAvgToG&quot; : 66,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;distOrigToG&quot; : 210,&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;goLat&quot; : 43.4046388, # google lat, only quey when dist &amp;gt; 100. to help get refine result.&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;goLng&quot; : -80.5314361, # if&amp;nbsp;&lt;span style=&quot;color: rgb(106 , 153 , 85) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px&quot;&gt;google is 100, use google or which one near google&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; &quot;goq&quot; : 100,&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;}&lt;/div&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;span&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&amp;nbsp;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=14;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="-695" y="-380" width="570" height="1140" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" target="fzO2dva-NlQsZcq2xKeE-60" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="305" y="960" as="targetPoint"/>
                        <mxPoint x="295" y="910" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-44" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="402.5" y="1080" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-44" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="290" y="1170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-44" value="result.q = 100" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="240" y="1040" width="100" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="KSY81GyFyXBszdat42JA-1" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;exitX=1.014;exitY=0.376;exitDx=0;exitDy=0;exitPerimeter=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-25" target="KSY81GyFyXBszdat42JA-2" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="502.5" y="1080" as="sourcePoint"/>
                        <mxPoint x="605" y="1080" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-21" target="fzO2dva-NlQsZcq2xKeE-66" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="295" y="1280" as="targetPoint"/>
                        <mxPoint x="290" y="1250" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-48" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="362.5" y="1080" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-49" value="No&lt;br&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="290" y="1130" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-66" target="fzO2dva-NlQsZcq2xKeE-65" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="855" as="targetPoint"/>
                        <mxPoint x="230" y="1305" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-52" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="175" y="1130" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-53" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-67" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="452.5" y="1120" as="targetPoint"/>
                        <mxPoint x="475" y="1280" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-56" value="No" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="360" y="1300" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-57" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="450" y="1160" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-60" target="fzO2dva-NlQsZcq2xKeE-44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-59" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-60" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="470" y="970" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-60" value="has result" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="250" y="930" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-61" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="295" y="1020" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-20" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" target="fzO2dva-NlQsZcq2xKeE-66" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="550" y="970" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="900" y="970"/>
                            <mxPoint x="900" y="1360"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-64" value="No" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="360" y="960" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-65" value="geoCodeOne&lt;br&gt;by One Engine" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="230" y="820" width="120" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-22" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-66" target="fzO2dva-NlQsZcq2xKeE-67" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-66" value="has engine to Try" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="235" y="1290" width="115" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-24" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-67" target="fzO2dva-NlQsZcq2xKeE-68" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="EI16iINKFTHXt4higwJh-0" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="g_OoEFqVbsG9zlH1AYZm-24">
                    <mxGeometry x="-0.1431" y="-4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-67" value="find best in queue" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="388.5" y="1210" width="127.5" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="KSY81GyFyXBszdat42JA-3" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;" parent="fzO2dva-NlQsZcq2xKeE-1" source="fzO2dva-NlQsZcq2xKeE-68" target="KSY81GyFyXBszdat42JA-2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-68" value="save error&lt;br&gt;codingresult = null" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="700" y="1215" width="100" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-72" value="&lt;span style=&quot;text-align: center&quot;&gt;&lt;span style=&quot;font-size: 12px&quot;&gt;&lt;br&gt;&lt;br&gt;&lt;/span&gt;&lt;/span&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;geoCodeOneWithEngine&lt;/span&gt;&lt;/div&gt;&lt;span style=&quot;text-align: center&quot;&gt;&lt;div style=&quot;font-size: 12px ; text-align: center&quot;&gt;&lt;span style=&quot;font-size: 14px ; text-align: left&quot;&gt;different engine and save best&lt;/span&gt;&lt;/div&gt;&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;fontSize=14;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="585" y="800" width="230" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="fzO2dva-NlQsZcq2xKeE-75" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;geoCodeOneOrUpsert&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(156 , 220 , 254)&quot;&gt;save both original uaddr and formated uaddr in db in aUaddr fields&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: rgb(156 , 220 , 254)&quot;&gt;query by formated uaddr, aUaddr is just for reference&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="595" y="-310" width="290" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="KSY81GyFyXBszdat42JA-2" value="return coding result" style="shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="665" y="1040" width="180" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-5" value="&lt;font style=&quot;font-size: 12px&quot;&gt;cachedResult&lt;/font&gt;" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" target="fzO2dva-NlQsZcq2xKeE-6" edge="1">
                    <mxGeometry x="0.4286" y="-24" relative="1" as="geometry">
                        <mxPoint x="404.5" y="-20" as="sourcePoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-2" value="return Cached Result" style="whiteSpace=wrap;html=1;shadow=0;strokeWidth=1;spacing=6;spacingTop=-4;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="640" y="70" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-2" style="edgeStyle=none;html=1;" edge="1" parent="fzO2dva-NlQsZcq2xKeE-1" source="Yxa9xKBjVdZ2Dxa1vjHN-7" target="Yxa9xKBjVdZ2Dxa1vjHN-28">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-4" value="" style="edgeStyle=none;html=1;" edge="1" parent="fzO2dva-NlQsZcq2xKeE-1" source="Yxa9xKBjVdZ2Dxa1vjHN-7" target="LEZOpFXxq8tSM9E3D9zJ-3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-7" value="input has q" style="rhombus;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="350" y="180" width="105.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-10" value="return input loc&lt;br&gt;save to geo_cache" style="whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="710" y="200" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-19" value="use input is prop is not active or sold. but not save &lt;br&gt;&lt;br&gt;(was for data clean, should not happen for now)" style="whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="665" y="310" width="220" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-26" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="Yxa9xKBjVdZ2Dxa1vjHN-28" target="Yxa9xKBjVdZ2Dxa1vjHN-25" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="415" y="520" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-25" value="return geo coding result" style="whiteSpace=wrap;html=1;rounded=0;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="640" y="460" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-27" value="&lt;span style=&quot;color: rgb(220 , 220 , 170) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;checkGeoCache&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(156 , 220 , 254) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;by _id=formatedUaddr&lt;br&gt;&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(220 , 220 , 170) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;&lt;font style=&quot;font-size: 9px&quot;&gt;return null if q&amp;lt;100 and mt&amp;lt;halfyears ago&lt;/font&gt;&lt;br&gt;&lt;/span&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="252.5" y="-80" width="290" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-28" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;geoCodeOneWithEngine&lt;/span&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="314" y="460" width="185" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-37" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" source="Yxa9xKBjVdZ2Dxa1vjHN-34" target="Yxa9xKBjVdZ2Dxa1vjHN-27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Yxa9xKBjVdZ2Dxa1vjHN-34" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;checkAndformatGeoInput&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="307.5" y="-195" width="180" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-3" value="&lt;span style=&quot;color: rgb(156 , 220 , 254) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;formatedUaddr&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="370" y="-130" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-6" value="" style="edgeStyle=none;html=1;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-18" target="fzO2dva-NlQsZcq2xKeE-65" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="295" y="790" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-7" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" parent="g_OoEFqVbsG9zlH1AYZm-6" vertex="1" connectable="0">
                    <mxGeometry x="-0.5554" y="5" relative="1" as="geometry">
                        <mxPoint x="-10" y="-107" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-19" value="Text" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" parent="g_OoEFqVbsG9zlH1AYZm-6" vertex="1" connectable="0">
                    <mxGeometry x="-0.1932" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-17" value="" style="edgeStyle=none;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-11" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="285" y="710" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-11" value="&lt;span style=&quot;color: rgb(156 , 220 , 254) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;input:&lt;br&gt;&amp;nbsp;qaddr, enginetype,&lt;br&gt;lat,lng(for revese geocding)&lt;br&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="175" y="610" width="220" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-18" value="get engine lists" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="230" y="710" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-21" value="&lt;span&gt;add result in queue&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="232.5" y="1170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-25" value="&lt;span&gt;save result to geo_coder&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="409.5" y="1050" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-26" value="&lt;span&gt;errs.push e&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="470" y="950" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-29" value="&lt;span style=&quot;color: rgb(240 , 240 , 240) ; font-family: &amp;#34;helvetica&amp;#34; ; font-size: 12px ; font-style: normal ; font-weight: 400 ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; background-color: rgb(42 , 42 , 42) ; display: inline ; float: none&quot;&gt;saveResult&lt;br&gt;&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;geocodingResult could be result from geocoding or from treb manual or ddf&lt;br&gt;&lt;/span&gt;&lt;br&gt;To improve:&lt;br&gt;aUaddr seems useless and might make the code confused. We might be able to remove this fields. using formated address as key. may cause duplicate record(different record is same address), should be okay.&lt;br&gt;&lt;br&gt;&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="560" y="1450" width="325" height="170" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-43" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-34" target="g_OoEFqVbsG9zlH1AYZm-42" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-34" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;input：&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;uaddrAfterFormat&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;uaddrBeforeFormat&lt;/div&gt;&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;geocodingResult&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="162.5" y="1485" width="195" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-37" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-42" target="g_OoEFqVbsG9zlH1AYZm-36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="370" y="1630" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-38" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" parent="g_OoEFqVbsG9zlH1AYZm-37" vertex="1" connectable="0">
                    <mxGeometry x="-0.0467" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-40" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-42" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="290" y="1660" as="sourcePoint"/>
                        <mxPoint x="260" y="1770" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-55" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-36" target="g_OoEFqVbsG9zlH1AYZm-56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="695" y="1740" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-36" value="save&amp;nbsp;&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;geocodingResult&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;&lt;br&gt;&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="460" y="1635" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-41" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="252.5" y="1730" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-42" value="&lt;span&gt;find existing record by&amp;nbsp;&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;uaddrAfterFormat&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="190" y="1620" width="140" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-46" value="Yes" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-44" target="g_OoEFqVbsG9zlH1AYZm-45" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-48" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-44" target="g_OoEFqVbsG9zlH1AYZm-47" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-44" value="result.q = existing.q" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="220" y="1770" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-53" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-45" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="700" y="1940" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-45" value="add&amp;nbsp;&lt;span style=&quot;background-color: rgb(30 , 30 , 30) ; color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;uaddrBeforeFormat to geo_cache&lt;/span&gt;" style="whiteSpace=wrap;html=1;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="475" y="1775" width="165" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-51" value="No" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-47" target="g_OoEFqVbsG9zlH1AYZm-58" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="640" y="1970" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-61" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-47" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="260" y="2090" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-62" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" parent="g_OoEFqVbsG9zlH1AYZm-61" vertex="1" connectable="0">
                    <mxGeometry x="-0.2583" y="4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-47" value="result.q &amp;gt; existing.q" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="220" y="1930" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-49" value="No" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="235" y="1870" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-56" value="&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;return&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(212 , 212 , 212) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; background-color: rgb(30 , 30 , 30)&quot;&gt;geocodingResult&lt;/span&gt;" style="shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="710" y="1715" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-58" value="&lt;span&gt;return cached result&lt;/span&gt;" style="shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="529.5" y="1930" width="210" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-64" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" source="g_OoEFqVbsG9zlH1AYZm-63" target="g_OoEFqVbsG9zlH1AYZm-56" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="g_OoEFqVbsG9zlH1AYZm-63" value="update Geo_cache&lt;br&gt;&lt;br&gt;save existing record in his&lt;br&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;set geoConflict if dist of two point &amp;gt; 1000&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="fzO2dva-NlQsZcq2xKeE-1" vertex="1">
                    <mxGeometry x="194" y="2090" width="156" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-5" style="edgeStyle=none;html=1;" edge="1" parent="fzO2dva-NlQsZcq2xKeE-1" source="LEZOpFXxq8tSM9E3D9zJ-3" target="Yxa9xKBjVdZ2Dxa1vjHN-10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-8" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="LEZOpFXxq8tSM9E3D9zJ-5">
                    <mxGeometry x="0.1652" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-7" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="fzO2dva-NlQsZcq2xKeE-1" source="LEZOpFXxq8tSM9E3D9zJ-3">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="350" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="567" y="350"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-9" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="LEZOpFXxq8tSM9E3D9zJ-7">
                    <mxGeometry x="-0.5203" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-3" value="q&amp;gt;100" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="fzO2dva-NlQsZcq2xKeE-1">
                    <mxGeometry x="514.5" y="180" width="105.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="LEZOpFXxq8tSM9E3D9zJ-11" value="&lt;div style=&quot;background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212)&quot;&gt; &lt;span style=&quot;color: #c586c0&quot;&gt;if&lt;/span&gt; engineType is &lt;span style=&quot;color: #ce9178&quot;&gt;'publicApi' (for API&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;color: rgb(106 , 153 , 85) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;/1.5/geocoding&lt;/span&gt;&lt;span style=&quot;color: rgb(206 , 145 , 120)&quot;&gt;)&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left ; color: rgb(212 , 212 , 212)&quot;&gt;    &lt;span style=&quot;color: #c586c0&quot;&gt;return&lt;/span&gt; [hereGeocode,mapboxGeocode]&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212)&quot;&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#c586c0&quot;&gt;for import&lt;/font&gt;&lt;font color=&quot;#d4d4d4&quot;&gt; [bingGeocode,hereGeocode,googleGeocode,mapquestGeocode]&lt;/font&gt;&lt;/div&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="fzO2dva-NlQsZcq2xKeE-1">
                    <mxGeometry x="430" y="610" width="512.5" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="W1L1ibQPXHxDVYdM7vFM" name="photoDownload">
        <mxGraphModel dx="919" dy="525" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="eW1nchQFJraJ8CrKeJgQ-0"/>
                <mxCell id="eW1nchQFJraJ8CrKeJgQ-1" parent="eW1nchQFJraJ8CrKeJgQ-0"/>
                <mxCell id="1B0yDT3X4ExNvDgcbnXz-0" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;downloadPhotosForTrebListing&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=14;" parent="eW1nchQFJraJ8CrKeJgQ-1" vertex="1">
                    <mxGeometry x="90" y="50" width="220" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="1B0yDT3X4ExNvDgcbnXz-1" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;span style=&quot;color: #9cdcfe&quot;&gt;downloadPhotosForCreaListing&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=14;" parent="eW1nchQFJraJ8CrKeJgQ-1" vertex="1">
                    <mxGeometry x="90" y="190" width="220" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="kqeE71Xa7AMHB2IBEYbM-1" value="downloadPhotosForBclisting&lt;br&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="eW1nchQFJraJ8CrKeJgQ-1" vertex="1">
                    <mxGeometry x="170" y="300" width="60" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram id="O9rWNDrwCcpdMS6fiYZl" name="manualFixLoc">
        <mxGraphModel dx="1103" dy="569" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="irigpfF7VLM5QmXLqBek-0"/>
                <mxCell id="irigpfF7VLM5QmXLqBek-1" parent="irigpfF7VLM5QmXLqBek-0"/>
                <mxCell id="irigpfF7VLM5QmXLqBek-2" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; font-weight: normal ; font-size: 12px ; line-height: 18px&quot;&gt;&lt;div&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;model/properties.coffee&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: #dcdcaa&quot;&gt;updateLocation&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry y="10" width="230" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-4" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="irigpfF7VLM5QmXLqBek-1" source="wFZBXdTuNYvRySRFY2Wh-0" target="wFZBXdTuNYvRySRFY2Wh-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-0" value="input&lt;br&gt;prop id,&amp;nbsp;&lt;br&gt;lat, lng, cnty,prov, city, addr, st, st_num, faddr" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="230" y="30" width="380" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" source="wFZBXdTuNYvRySRFY2Wh-3" target="wFZBXdTuNYvRySRFY2Wh-8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-3" value="TODO: format input&lt;br&gt;update properties. set old flds in origFlds. add rmlog" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="335" y="160" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-11" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" source="wFZBXdTuNYvRySRFY2Wh-8" target="wFZBXdTuNYvRySRFY2Wh-10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-8" value="update geo_cache&lt;br&gt;&amp;nbsp;using new uaddr" style="whiteSpace=wrap;html=1;rounded=0;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="270" y="290" width="300" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-13" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" source="wFZBXdTuNYvRySRFY2Wh-10" target="wFZBXdTuNYvRySRFY2Wh-12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-10" value="find all propertiess with the same new uaddr" style="whiteSpace=wrap;html=1;rounded=0;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="260" y="410" width="320" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-15" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" source="wFZBXdTuNYvRySRFY2Wh-12" target="wFZBXdTuNYvRySRFY2Wh-14" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-12" value="update rni original records, add&amp;nbsp;&lt;span style=&quot;color: rgb(220 , 220 , 170) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace&quot;&gt;addManMtToSource,&lt;br&gt;&lt;/span&gt;&lt;span&gt;trigger reimport&amp;nbsp;&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="345" y="520" width="150" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="wFZBXdTuNYvRySRFY2Wh-14" value="return updated count number" style="shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontSize=12;" parent="irigpfF7VLM5QmXLqBek-1" vertex="1">
                    <mxGeometry x="315" y="650" width="210" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>