### Design for mlsimport modules, down load listings from treb, crea and gvreb source

#### Database schema

- Use xxx_ids collection
  - monitor status, only keep status U records for 12H
  - limit the size of this collection for better performance. set status and add exp to expired records.
  - records with exp will expire in 30 days
  - each source has it's own \_ids collection for clearer results
  - common fields:
    | Field | Description |
    |-----------------|-----------------|
    | \_id | raw record id (C9999999,E999999)
    | lastUpdated | last updated timestamp. from source. default laTs
    | synced | downloaded record after last update timestamp, equal to lastUpdated.
    | laTs | last active timestamp. Use this one to expiry listings. batch machine time. Evow has availables for Commercial only. For Residentail/Condo evow has status in source. no expire.
    | mt | last modified timestamp. batch machine time.
    | ts | insert timestamp. batch machine time.
    | cls | ResidentailPoperty/CondoProperty/CommercialProperty.
    | phoMt [^*] | photo max last updated timestamp. from source. it could be null
    | phoDl [^*] | photo last downloaded timestamp, equal to phoMt. from source.
    | phoErr [^*] | photo download err happen ts(if old record will mark this err too)
    | maxPicSz [^*] | max picture size in bytes.
    | avgPicSz [^*] | average picture size in bytes.
    | picNum [^*] | picture number.
    [^*]: For TREB and other Board, which has multiple sources, \* fields are for the xxx_master_ids collection only
    difference with synced and lastUpdated
    synced only update when download record details.lastUpdated is updated when get avaible ids.
    if lastUpdated>synced, record is available but not download. should query this record again.
- Use xxx_records collection
  - detail record. one record for each id. keep forever.
  - one collection for each source. IDX and VOW are different collections.
  - \_id match properties collection for easy reference.
  - common fields:
    | Field | Description |
    |-----------------|-----------------|
    | \_id | id match properties collection. (DDF999999, TRBC99999, BRER2266729)
    | laTs | last active timestamp. Use this one to expiry listings. batch machine time.
    | mt | last modified timestamp. batch machine time.
    | ts | insert timestamp. batch machine time.
    | phoMt [^*] | photo max last updated timestamp. from source.
    | phoDl [^*] | photo last downloaded timestamp, equal to phoMt. from source.
    | maxPicSz [^*] | max picture size in bytes.
    | avgPicSz [^*] | average picture size in bytes.
    | picNum [^*] | picture number.
- mls_bcre_raw_records collection
  - raw records from source server, before translated to human readable format.
  - ListingID as \_id.
  - In mls_bcre_records collection, BRE+DisplayID as \_id, field name and value will be translated to the corresponding dbname.
- Use xxx_logs collection
  - record all download records. accumulate and do not overwrite existing.
  - one collection for each source.
  - common fields:
    | Field | Description |
    |-----------------|-----------------|
    | \_id | automatic id
    | id | raw record id
    | data | raw record.
- mls_treb_evow_delete_ids
  - record the ids from Delete class
- mls_treb_master_ids collection for TREB system
  - to aggregate same record from different source, and prevent repeating download of the same picture.
  - common fields:
    | Field | Description |
    |-----------------|-----------------|
    | \_id | 'TRBC99999'
    | src | ['idx','evow']
    | lSrc | last updated source. one of 'idx','evow'
    | \* | other fields as xxx_ids collection
- mls_treb_master_records collection for TREB system
  - to have a center collection for TREB listings
  - has src and lSrc fields as \_master_ids collection
  - To be checked later: update conflict fields as following priority tables(higher number for higher priority) ??
  - when save to master record. idx can only overwite lSrc = idx,(check fields from idx and evow, if same, idx can not overwrite evow. change Status to status.)
    | Field | IDX | EVOW | Manual[^1] |
    |-----------------|-----|------|-------|
    | status | 1 | 3 | 2 |
    | price | 1 | 3 | 2 |
    [^1]: Not used now.

- mls_import_keys collection to save meta information

  - \_id: BOARD_SOURCE_xxx
  - common keys:
    - Treb(SRC)Credential: login credentials
    - Treb(SRC)Download: query timestamps
    - Treb(SRC)Import: last import informatoin
    - TrebCurrentProcess: record current running process
    - BcreCredential: login credentials

- mls_bcre_meta collection to save meta information for gvreb
  - \_id: xxx [SYSTEM,RESOURCE,CLASS,TABLE,OBJECT,LOOKUP,LOOKUP_TYPE]

- mls_treb_manual_ids(collManualIDs): collection to save admin manual ids
  - _id: TRB/BRE(W111111)
  - src: TRB/BRE
  - id: W111111
  - status: New/Downloaded
  - logId: inserted log id
  - mt: ts


#### Import procedures: forever loop

1. initialize:
   1. parameter check,
   2. collections,
   3. get credentials
   4. connections,
   5. when error, exit process with error code
   6. retrieve meta data (for gvreb)
2. main loop
   1. get updated listings(by timestamp_sql>lastTs)
      1. loop pages if more than limit
      2. getObject for photo urls
   2. get all available IDs (there might be ids in available but not in updated listings. so download again by ids )
   3. download listings that in available list but not syned yet.
   4. if Step 2.1 or 2.3 results less than threshold(100?), sleep for 3 minutes.
3. when error in main loop, close all connections, dbs, return to Step 1.

#### Available list

1. get all Availables
2. save each record one by one, set same laTs for all records
   1. to \_ids
   2. save to \_master_ids with source if TREB source

#### Save records

1. save \_log collection
2. read \_master_ids or \_ids
3. get photos if needed (this step can be turned off)
4. save to \_raw_records
5. save to \_ids
6. if TREB, check fields priority based on Step 2 lSrc ？？
   1. save to \_master_records
   2. save to \_master_ids

#### Download photos

Photo download in a **separate** module, shall be used when saving records, and can be used in a separate batch.
For stream error handling, check here: https://stackoverflow.com/questions/21771220/error-handling-with-node-js-streams
To detect how many pictures, use batch download algorithm.

1. send **batch** of 3-5 pictures to routine
2. routine return {cntDownloaded:number,toContinue:boolean}
3. repeat Step 1 until toContinue is false

toContinue will be false, only when the last 2 pictures can not be downloaded. i.e. 1.OK, 2.Fail, 3.Fail. Otherwise it's always true.

## Usage
```
go src
npm install

> creaDownload.coffee user password [downloadPhotos]
> trebDownload.coffee idx/evow user password [downloadPhotos]
> photoDownload.coffee treb/crea [watchOnly]
```
