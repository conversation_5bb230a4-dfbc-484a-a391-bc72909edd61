## Forum:
  * record last 100 viewed posts (more then 5 seconds)
  * daily email when viewed, commented, posted, fav-ed forum has comments
  * weekly notify hot post/news

## Properties
  * record last 200 viewed listings (more then 5 seconds)
  * daily email when viewed, fav-ed listings, price changed/sold
  * daily/weekly email for searched results
  * weekly notify viewed counter for above listings
  * weekly/daily topped listing

## Pre-Construction
  * daily/weekly, when has promotion flag
  * daily fav-ed projects has wants/needs


  forum (daily forum update)
  Faved forum have new comments
  Comment forum have new comments
  Self forum have new comments
  not Viewed forum which has vc more then 400 has new comments

  forum weekly (Weekly forum update)
  Hot forum posted within 7 days
  Edm tag added by admin

  forumW  (Weekly forum update)
  Whole forum added by admin

  update property  (daily property update)
  faved property sold
  viewed property price change

  Newprop (showSavedSearch optional)
  New property in saved search

  soldprop (showSavedSearch optional)
  sold property in saved search

  Project (noviewed optional)
  Faved,viewd project has update or new wants

  projectrcmd (noviewed optional)
  Used in daily, send  project recommend by admin

  Project weekly
  send  weekly project recommend by admin

  stat
  Weekly stat

  statM
  Monthly stat

  subscription
  for each of agents' saved search, setup subscription for one user(by email)
  when do edm subscription, send listings match the subscription to user and agent at same time
  use can only contac agent to unsub
  agent can click unsub and then edit the unsbu without loginin within 24 hours.
  each unsub link has a timestamp.

## How to test Edm

project edm > 7days ago
projectrcmd rcmd > 7 days ago
publicforum
publicforumw edmw
forum daily weekly edm edmd edmw set in forum detail page
property: either set user_log tp:propChg ts > Date.now() or set new propChg user_log with watchPropAndPushNotify
assignment ts: > last edmts
exlisting ts: > last edmts
statM: month stat->week _id: 202105M-ON-MARKHAM