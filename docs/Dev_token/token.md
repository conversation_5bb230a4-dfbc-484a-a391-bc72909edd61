sysdata Collection

| field   | data type | description      | notes                                                       |
| ------- | --------- | ---------------- | ----------------------------------------------------------- |
| _id     | string    | token项目，值为systemTokenKeys  |                                                       |
| _mt | Date    | 数据库自己生成        |                         |
| item | array     | token系统指定的项目 |
|   .nm | string     | 项目名称 |                          |
|   .key | string     | 程序内部使用key |                        |
|   .t | int     | 系统规定增加或扣除的积分，当用户点击某些操作时，对其积分进行加减 |                        |
|   .tp | string     | token类型 |
|   .canBeNegative | boolean     | token可以为负数 |

token Collection，放置在data库里

| field   | data type | description      | notes                                                       |
| ------- | --------- | ---------------- | ----------------------------------------------------------- |
| _id     | Auto      |                  | 数据库自动生成                                                 |
| uid     | ObjectId  | 用户的_id         | uid+tp 创建索引用于提高查询速度                                    |
| tp | string    | 当前token类型(如果是Token类型没有该字段)        |   |
| _mt | Date    | 数据库自己生成        |                         |
| ts | Date    | 记录创建日期        |                         |
| token | int    | 用户目前token总余额        |                         |
| t | int | 管理员/系统给的token分数，eg：-10, 10 |                          |
| reason | string     | 管理员给的理由(如果是系统扣除，该条记录里没有该字段) |                        |
| admUid | string     | 修改该条数据的管理员uid(如果是系统扣除，该条记录里没有该字段) |                        |
| key | string     | 系统来源：填系统指定的key |                        |
| m | string  | 用于前端显示的额外信息 |                        |
| id | string     | 比如房源id，报告等 |                        |                       |
| v | string     | 报告的版本 |                        |
| pcOfShr | int     | 记录占key对应的token值的百分比(1-100)，缺省默认百分百 | 房源认领可能是多个人分key里面的token值，需要按这个百分比获取|

Control Design:

| request URL | Params    | result           | description                                                 |
| ----------- | --------- | ---------------- | ----------------------------------------------------------- |
| token/getHistoryDetail | { uid,page,type } | { ok:1,result:{limit: 5,detail:{token: 0, history:[ {token: 10,date:'2024/05',his:[{ts:'', t:'+1', reason: '',nm:'editorName',uid: "editorUid"}]}]}} } | 用户和管理员可以看到具体Token加减历史记录。 从model获取的数据库数据处理成前端需要的数据格式。|
| token/adminModify | { uid, token, reason,type } | {ok:1，result:{token:0}} | 管理员修改用户Token记录|
| token/getKeyList | {} | { ok:1,result:{_id: '', item: [{nm: '', key: '', t: 0 }]}} | 查询系统录入的token项目 |
| token/updateSystemKey | {key:'',token:0} | { ok:1 } | 更新系统token规定的分数 |
| token/getUserList | {eml:'', page,type} | { ok:1,result:{limit:20,userList:[{nm: '', token: 0/'-', eml: '', avt: '', uid: '', _id: ''}]} } | 管理员看到的用户Token列表页，按更新时间倒序排列显示，支持eml模糊查询 |

Model Design:

| name | Params    | result           | description                                                 |
| ----------- | --------- | ---------------- | ----------------------------------------------------------- |
| getHistory| { uids,page,type,date } | {tokenList: [token.md token表结构], limit} | 根据uid查询该用户的token信息|
| adminModify | { uid, token, reason, uidAdmin,type } | {err:null,token:0} | 将管理员给用户手动加减积分记录保存到数据库|
| systemModify | { uid, keyid, memo, version, action, pcOfShr } | {err:null,token:0} | 根据系统指定key来加减用户积分|
| getUserList | {eml,page,type} | tokenList: [{nm: '', token: 0/'-', eml: '', avt: '', uid: '', _id: ''}],limit,userInfo | 如果没有eml参数：按uid分组查询最新记录的token，关联user表获取eml，nm，avt等信息。有eml参数：根据eml模糊查找user表获取uid等基本信息，之后根据uid查找token表获取最新token信息 |
| canPerform |{uid,key}| true/false | 查询用户当前分数是否达到操作key需要的分数|