<mxfile host="65bd71144e">
    <diagram id="Mz9KtUo-wx6iId71P3Za" name="Page-1">
        <mxGraphModel dx="1795" dy="2026" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-1" value="Model(Mongo/ES)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="380" width="420" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-2" value="Controller(GET/POST)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="200" width="415" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-3" value="Business Logic" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="380" y="290" width="260" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-4" value="View(vue3/dot)&lt;br&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="-20" y="200" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-5" value="&lt;font color=&quot;#0000ff&quot;&gt;Web&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="410" y="30" width="230" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-6" value="" style="shape=flexArrow;endArrow=classic;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="220" y="290" as="sourcePoint"/>
                        <mxPoint x="295" y="379" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-7" value="" style="shape=flexArrow;endArrow=classic;html=1;exitX=0.692;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="300" as="sourcePoint"/>
                        <mxPoint x="480" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-8" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.683;entryY=0.029;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-3" target="bU40nCMmlP7GFg8wUXHY-1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="420" y="350" as="sourcePoint"/>
                        <mxPoint x="470" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-9" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.561;entryY=-0.017;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.181;exitY=1.017;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-5" target="bU40nCMmlP7GFg8wUXHY-2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="220" y="110" as="sourcePoint"/>
                        <mxPoint x="270" y="60" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-10" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-4" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="210" as="sourcePoint"/>
                        <mxPoint x="520" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-11" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.733;exitY=0;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-5" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="200" as="sourcePoint"/>
                        <mxPoint x="330" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-13" value="lib.js" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="690" y="250" width="80" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-14" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.013;entryY=0.72;entryDx=0;entryDy=0;entryPerimeter=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-1" target="bU40nCMmlP7GFg8wUXHY-13" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="640" y="430" as="sourcePoint"/>
                        <mxPoint x="690" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-15" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.013;entryY=0.385;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="bU40nCMmlP7GFg8wUXHY-13" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="640" y="330" as="sourcePoint"/>
                        <mxPoint x="690" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-16" value="" style="shape=flexArrow;endArrow=classic;html=1;endWidth=6;endSize=4.62;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=-0.025;entryY=0.185;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-13" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="630" y="250" as="sourcePoint"/>
                        <mxPoint x="690" y="238" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-17" value="common.js" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="700" y="80" width="90" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-18" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" target="bU40nCMmlP7GFg8wUXHY-17" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="640" y="70" as="sourcePoint"/>
                        <mxPoint x="690" y="20" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-19" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-17" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="630" y="220" as="sourcePoint"/>
                        <mxPoint x="680" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-20" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.1;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="bU40nCMmlP7GFg8wUXHY-17" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="640" y="305" as="sourcePoint"/>
                        <mxPoint x="700" y="175" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-21" value="rmsrv5wk.js" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="-100" width="60" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-22" value="rmapp.js" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="445" y="-100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-23" value="&lt;font color=&quot;#0000ff&quot;&gt;App(ReactNative)&lt;br&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;" parent="1" vertex="1">
                    <mxGeometry x="120" y="30" width="240" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-24" value="" style="shape=flexArrow;endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.102;entryY=-0.05;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-23" target="bU40nCMmlP7GFg8wUXHY-2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="180" y="120" as="sourcePoint"/>
                        <mxPoint x="180" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-25" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=0.75;entryY=1;entryDx=0;entryDy=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-2" target="bU40nCMmlP7GFg8wUXHY-23" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="190" y="200" as="sourcePoint"/>
                        <mxPoint x="240" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-26" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" source="bU40nCMmlP7GFg8wUXHY-23" target="bU40nCMmlP7GFg8wUXHY-21" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="210" y="30" as="sourcePoint"/>
                        <mxPoint x="260" y="-20" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-27" value="" style="shape=flexArrow;endArrow=classic;html=1;exitX=0.713;exitY=-0.033;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-23" target="bU40nCMmlP7GFg8wUXHY-22" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="30" as="sourcePoint"/>
                        <mxPoint x="440" y="-50" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-28" value="" style="shape=flexArrow;endArrow=classic;html=1;exitX=0.283;exitY=-0.017;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="bU40nCMmlP7GFg8wUXHY-5" target="bU40nCMmlP7GFg8wUXHY-22" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="460" y="30" as="sourcePoint"/>
                        <mxPoint x="510" y="-20" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="bU40nCMmlP7GFg8wUXHY-29" value="" style="shape=flexArrow;endArrow=classic;html=1;entryX=-0.011;entryY=0.367;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" target="bU40nCMmlP7GFg8wUXHY-17" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="360" y="80" as="sourcePoint"/>
                        <mxPoint x="410" y="30" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="410" y="135"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>