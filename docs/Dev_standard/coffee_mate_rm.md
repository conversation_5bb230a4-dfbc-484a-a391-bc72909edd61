### Coffee Controller 开发practise/coffee dev guidelines

## 1. 使用 MSG_STRINGS 作为 常用 返回值给客户,
通过
```
MSG_STRINGS.def {
  ERROR_PRICE: 'Incorrect charege amount',
}
return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
```
使用 *namingRile.md* 定义新值.
string hard code避免使用，使用变量. eg. MSG_STRINGS.ERROR

gGlobalVariable = global value

SOURCE_TYPE_APP = ‘APP’

SOURCE_TYPES = {APP:SOURCETYPE_APP}
## 2. 用deug.debug
```
debug = DEBUG()
debug.debug 'when debug' # debug level, releated to verbose level
debug.info 'when log' # will log every time
debug.warn 'when warn' # sth not right but not error either
debug.error 'when error' # will logout in error file
```
## 3. resp json error 使用 respError
```
respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp} if err
```
resp json ret=result
参考 *responseJSON.txt* 的格式

## 4. 使用？检查是否存在
无论是conf，还是db的值，都可能不存在.
expose出去的基本都要check.

内部使用可以不check existance
```
appId = conf?.wechatApp?.AppID
favMap = ret?.favMap
```

## 5. 尽量用functional programming
list modification is releated to coffee forloop slice, array changed but length not modified
```
DO NOT: 
changeList(list)
return list
DO:
list = filterResults(list,roles)
return list

Object.assign {},ret,change
```
## 6. path defination, DEF 在同一APP下
```
#'1.5/translate'
APP '1.5'
APP 'translate', true
```

## 7. dataMethods是前端 pageData的接口
frontend:
see *pagedata_mixins.js*
```
import pagedata_mixins from './pagedata_mixins'

export default {
  mixins: [pagedata_mixins],
  datas: ['sessionUser'],
  beforeMount () {
    this.getPageData(this.datas, {opts}, true);
  },
}
```

backend:
see *src/apps/80_sites/01_lib/11_pageDataMethods.coffee*
```
dataMethods.rltrTopAd = (req, user)->
  return getConfig('rltrTopAd')
```

## 8. 避免使用DEF
使用export+MODAL
```
DO NOT:
getExchangeNames = DEF 'getExchangeNames'
DO:
libApp = INCLUDE 'libApp'
prop = MODEL 'properties'
```

<!-- TODO前端调用参考 -->
## 9. standard functions
参考 *stdFunctions.coffee*
```
ADDSTDFN 'searchCmtys',{needReq:true,userObject:true},(opt,cb)->
  searchCmtys opt,(err,cmtys)->
    return cb(err) if err
    cb null,cmtys
```

## 10. view 和 dot template的使用
VIEW 在APP下存在，是server render.

dot html是单独的文件 see *dot.coffee*,  eg. *homePageMls.coffee*
```
VIEW 'propManagePage',->  
  div id:'vueBody',->
    text """<app-prop-manage></app-prop-manage>"""
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  js '/js/entry/commons.js'
  js '/js/entry/appPropManage.js'
```

## 11. req.getDevType() 及其他的使用
req下有很多自带的function, see *coffeemate3.coffee*
可以通过ADDPLUGIN添加
```
ADDPLUGIN 'verGTE',verGTE
req.verGTE()
req.isIOS()
req.l10n()
req.locale()
req.remoteIP()/req.isChinaIP()
req.session.set()
req.fullUrl()
```

权限判断也是使用req.isAllowed & req.hasRole下实现
参考 *userAccessControl.coffee + access.coffee*
```
req.isAllowed('vipUser', user)
req.hasRole('realotor',user)
```

## 12. 使用 INCLUDE 替换require
```
helpers = INCLUDE 'lib.helpers'
```

## 13. COLLECTION + MODEL 的使用
COLLECTION 调用mongo_db/mongo4.coffee
MODEL 调用model文件夹下的文件，并且调用COLLECTION
所有db CRUD 都应该在MODEL里,不要传req到model里

## 14. theme的使用(待补充)


## 15. appAuth的使用
有url， 无user -> redirect login. 

无url， 无user -> 继续执行但 user=null
```
UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
```

## 16. CallBacks
没有return会继续执行，导致多个cb被执行

多个EXIT in cb 会随机EXIT，应该最外层执行EXIT


## 17. async await functions
使用try catch await 替换promise.then().catch()+cb(err,ret)， 可以混用但要清楚执行逻辑。

coffee已经支持async function

[coffee async](https://coffeescript.org/#async-functions)