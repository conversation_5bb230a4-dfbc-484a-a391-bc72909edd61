SEO:
测试页面是否可以被抓取
https://search.google.com/test/mobile-friendly
添加结构化数据
https://developers.google.com/search/docs/guides/sd-policies    ***重点
https://search.google.com/structured-data/testing-tool

Implement dynamic rendering
https://developers.google.com/search/docs/guides/dynamic-rendering
- [ ] https://support.google.com/webmasters/answer/189077?hl=en 、、
- [ ]  Do we really need Google's Accelerated Mobile Pages (AMP)?
https://www.tunetheweb.com/blog/do-we-really-need-google-amp/


目标： app和分享的流量能导入到主网站，提高主网站index和排名。

- [x] 多语言网站识别。   https://support.google.com/webmasters/answer/189077?hl=en
- [ ] 重复内容处理
- [x]  给重复的内容加上 rel="alternate" 或  canonical，，指向主站，可以提高主站的ranking
Consolidate duplicate URLs （Why should I choose a canonical URL?）

https://support.google.com/webmasters/answer/139066?hl=en
https://support.google.com/webmasters/answer/66359?hl=en

可能存在的问题。
   1 .如果加了canonical的两个页面内容不像，google可能会忽视这个tag（
if they’re only topically similar but not extremely close in exact words, the canonical designation might be disregarded by search engines.）

5 common mistakes with rel=canonical 
https://webmasters.googleblog.com/2013/04/5-common-mistakes-with-relcanonical.html

2. 即使加了rel="alternate" ， 也应该让google能够crawl重复的内容，才能识别。分享服务器和app可能都需要处理。（Duplicate content，https://support.google.com/webmasters/answer/66359?hl=en）

解决方案：
第一步，
指定www.realmaster.com 为主站，realmaster.com, realmaster.cn 的房源，楼盘，论坛 都添加canonical指向主站。（如果google 索引到这些内容，那么在search console里www.realmaster.com 的链接下会出现alternate的内容。）
如果第一步不起作用。
  1. www.realmaster.com 为主站，realmaster.cn 完善成给中国国内用的网站，健全内部链接，让google容易crawl到，但是内容大部分仍然为主站的duplicate。解决问题2
2. 考虑www.realmaster.com 做英文网站，www.realmaster.cn做中文网站，分享内容全都从这个网站出来，现在的分享页面停用。

如何添加 Separate URLs

https://developers.google.com/search/mobile-sites/mobile-seo/separate-urls



remove index from google
https://search.google.com/search-console/removals?resource_id=sc-domain%3Arealmaster.com

remove content cache from google
https://search.google.com/search-console/remove-outdated-content