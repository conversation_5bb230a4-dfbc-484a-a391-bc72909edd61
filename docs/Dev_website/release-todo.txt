2020-03-26
1. nginx
  ## nodejs config
  location / {
    root /var/nginx/www.realmaster.com;
    #root /mnt/data/www_realmaster_cn/wordpress;
    #index index.php index.html index.htm;
    add_header Access-Control-Allow-Origin *;

    if (-f $request_filename) {
      expires 30d;
      break;
    }
  }
  ## wordpress config
  location ~* ^/(wp-|blog|20|post-sitemap.xml) {
    root             "/Users/<USER>/Documents/Employers/realmaster/wordpress";
    index index.php index.html index.htm;
    add_header Access-Control-Allow-Origin *;
    #try_files        $uri =404;
    fastcgi_pass     unix:/Applications/MAMP/Library/logs/fastcgi/nginxFastCGI.sock;
    fastcgi_param    SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include          fastcgi_params;

    if (-f $request_filename) {
      expires 30d;
      break;
    }

    if (!-e $request_filename) {
      rewrite ^(.+)$ /index.php?q=$1 last;
    }
  }
2. vhost.yml add lang prefix
3. wordpress header menu, remove assignment,exclusive, modify with zh-cn