location ~* ^/(wp-|blog|20) {
  root             "/Users/<USER>/Documents/Employers/realmaster/wordpress";
  index index.php index.html index.htm;
  add_header Access-Control-Allow-Origin *;
  #try_files        $uri =404;
  fastcgi_pass     unix:/Applications/MAMP/Library/logs/fastcgi/nginxFastCGI.sock;
  fastcgi_param    SCRIPT_FILENAME $document_root$fastcgi_script_name;
  include          fastcgi_params;

  if (-f $request_filename) {
    expires 30d;
    break;
  }

  if (!-e $request_filename) {
    rewrite ^(.+)$ /index.php?q=$1 last;
  }
}