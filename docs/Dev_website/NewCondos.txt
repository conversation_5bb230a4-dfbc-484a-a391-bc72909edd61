
新写的文件
后台文件    src/apps/80_sites/WebRM/newCondos/index.coffee


页面外框     src/themes/website/layout.html
城市列表页    src/themes/website/newcondos/index.html
condo列表页  src/themes/website/newcondos/list.html
condo详情    src/themes/website/newcondos/detail.html

css文件  src/webroot/public/css/web/newCondos.css
js文件   src/webroot/public/js/web/newcondos.js


agent list组件  src/coffee4client/components/project/agentSetting.vue

修改的其他文件
src/apps/80_sites/AppRM/rmListing/project.coffee(添加判断是什么端口进入，返回webAgent还是appAgent，判断返回的floorplan的数据)
src/coffee4client/components/frac/SearchUserList.vue（添加判断返回的用户信息是web的还是app的）
src/coffee4client/components/project/appProjManage.vue(添加agent list组件，并且在上传图片部分进行传参处理)

comments:
jquery是否应该升级版本？ 还是用vanila html操作dom？
