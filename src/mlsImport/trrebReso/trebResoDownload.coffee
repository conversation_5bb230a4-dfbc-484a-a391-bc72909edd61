###
*  Description:    fetch treb[evow,idx] listings from RESO API
*  New config:     ./start.sh -t batch -n trebResoDownload -cmd 'lib/batchBase.coffee mlsImport/trrebReso/trebResoDownload.coffee [idx] [evow]'
*  Author:         Maggie
*  Run duration:   forever
*  Run frequency:  forever
*  Param:          photo-download: download photo or not
                   idx: download idx
                   evow: download evow
*  Note:           photo download is not supported now [photo_download]
# [init] and [routine] will run in parallel
# [init] - Fetches historical data backwards in time, fetch properties using ModificationTimestamp, then put other resoReturn into queue
# [routine] - Fetches new data going forward, fetch properties using ModificationTimestamp, then put other resoReturn into queue,
              also fetch openhouse and media using ModificationTimestamp
###

debug = DEBUG()
util = require 'util'
config = CONFIG(['trebReso'])
EventLogger = INCLUDE 'lib.eventLog'
{ getHash } = require '../../lib/helpers_string'
{ sleep } = require '../../lib/helpers_function'
SpeedMeter = require '../../lib/speed'
{ updateResourceRequestLog, getLastResourceRequestInfo, ignorableErrorReso, convertRoom, convertOpenHouse, convertMedia, convertMediaAbbr, filterCompressTrebResoMedia,convertMediaToAbbreviated } = require '../../libapp/impCommon'
filterEmptyFieldsNSaveStats = require('../../libapp/impCommon').getEmptyFilterNStatsFunction()
{ createTrebResoClient } = require('../../libapp/reso')
ResourceDownloadQueue = require '../../libapp/mlsResourceDownloadQueue'
{
  processKillSignal,
  RUNNING_NORMAL,
  RUNNING_ERROR,
  RUNNING_WARNING,
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = INCLUDE('libapp.processHelper')

ONE_YEAR_SECONDS = 365*24*3600
THIRTY_DAYS_SECONDS = 30*24*3600
THREE_MONTHS_SECONDS = 90*24*3600
ProcessStatusCol = COLLECTION 'vow','processStatus'
MlsImportKeysCol = COLLECTION 'rni','mls_import_keys'
FieldStatsCol = COLLECTION 'rni','mls_field_stats'

gDownloadIdx = AVGS.includes('idx')
gDownloadEvow = AVGS.includes('evow')
gDownloadString = 
  if gDownloadIdx and gDownloadEvow
      'idx_evow'
  else if gDownloadIdx
      'idx'
  else if gDownloadEvow
      'evow'

PROCESS_STATUS_ID = 'RESO_TREB_PropertyDownload_'+ gDownloadString
IMPORT_KEY_ID = 'RESO_TREB_PropertyDownload'
TRB_FIELD_STATS_ID = 'TRB'
OTW_FIELD_STATS_ID = 'OTW'
CAR_FIELD_STATS_ID = 'CAR'
AVG_RUNNING_MS = 5 * 60 * 1000 # 5 minutes
ROUTINE_SLEEP_MS = 15 * 60 * 1000 # 15 minutes
ROUTINE_MEDIA_SLEEP_MS = 5 * 60 * 1000 # 5 minutes
OPEN_HOUSE_THRESHOLD = 30 * 24 * 60 * 60 * 1000 # 30 days
INIT_FETCH_INTERVAL = 12 * 60 * 60 * 1000 # 0.5 days
INIT_FINISHED_BLANK_PERIOD = 30 * 24 * 60 * 60 * 1000 # 30 days
FETCH_TIMEOUT = 10 *60 * 1000 # 10 minutes
MLS_ID_FIELD = 'ListingKey'
LAST_MT_FIELD = 'ModificationTimestamp'
RESOURCE_NAME_PROPERTY = 'property'
RESOURCE_NAME_MEDIA = 'media'
RESOURCE_TO_BE_MERGED_EVOW = ['openHouse','propertyRoom','media']
RESOURCE_MERGED_RESERVED = ['openHouse','propertyRoom','phoUrls','thumbUrl','pho','docHL','tnHL','phoHL']
RESOURCE_TO_BE_MERGED_IDX = ['propertyRoom','media']
RESOURCE_NEED_GET_BY_MT_IN_MAIN_LOOP_IDX = ['media']
RESOURCE_NEED_GET_BY_MT_IN_MAIN_LOOP_EVOW = ['openHouse']
RESOURCE_NO_MERGE=['field','lookup','office'] # member doesn't work
QUEUED_RESOURCES_IDX = ['property','propertyRoom','media']
QUEUED_RESOURCES_EVOW = ['property','openHouse','propertyRoom','media']
MODE_QUEUE = 'queue'
MODE_ROUTINE = 'routine'
MODE_INIT = 'init'
RETRY_INTERVALS = [5, 10, 20, 60, 120, 240] # Minutes
GET_RESOURCE_MAX_RETRIES = 3
GET_RESOURCE_WAIT_TIME = 5 * 60 * 1000 # 5 minutes
OLDEST_QUERY_TS = new Date('2002-10-22T04:00:00Z')
QUERY_WAIT_TIME = 4 * 1000 # 8 seconds
DEFAULT_PHOTO_PARALLEL = 10
PHOTO_DOWNLOAD_ALLOWED_MS = 30 * 1000 # 30 seconds
QUEUE_SLEEP_MS = 30 * 1000 # 30 seconds
IMAGE_FOLDER_MODE = 0o775
IMAGE_FILE_MODE = 0o664
DEFAULT_PHOTO_DOWNLOAD_START_TS = new Date('1970-01-01T00:00:00.0Z')
MAX_RETRY_ATTEMPTS = 3
NEW_PROP_PRIORITY = 20000
QUEUE_BATCH_SIZE_OPEN_HOUSE = 200
QUEUE_BATCH_SIZE_MEDIA = 50
QUEUE_BATCH_SIZE_PROPERTY_ROOM = 50
QUEUE_BATCH_SIZE_PROPERTY = 50
# Parallel processing configuration for different resource types
PARALLEL_CONFIG = {
  media: 4,  # Media resources use 4 parallel processes
  default: 1  # All other resources use 1 process
}
# 50*4 times of the queue batch size. LargestNoWatermark, Largest, Medium, Thumbnail
# Each property may have 50 pictures, and each picture may have 4 types of data. 
# Plus extra documents.
MAX_RECORDS_PER_PAGE = 10000
# class ResourceManager
#   constructor: (config) ->
#     @resourceQueues = {}
#     @getPhotos = false
#     @photoParam = {
#       evow: @createPhotoParams(config?.trebReso?.evow?.photo_parallel)
#       idx: @createPhotoParams(config?.trebReso?.idx?.photo_parallel)
#     }
#   createPhotoParams: (parallel = DEFAULT_PHOTO_PARALLEL) ->
#     parallelPhotoDownloads: parallel
#     concurrent_photo_download_count: 0
#     photoDownloadError: null


RESOURCE_MAPPING={
  "evow":{
    property:{typeName:'PROPERTY',queueBatchSize:QUEUE_BATCH_SIZE_PROPERTY},
    openHouse:{resoKey:'OpenHouseKey',typeName:'OPEN_HOUSE',statusField:'OpenHouseStatus',propertyLinkField:'ListingKey', queueBatchSize:QUEUE_BATCH_SIZE_OPEN_HOUSE,propertyLinkFieldAbbr:'ListingKey',statusFieldAbbr:'OpenHouseStatus' },
    propertyRoom: {resoKey:'RoomKey',typeName:'PROPERTY_ROOM',statusField:'RoomStatus',propertyLinkField:'ListingKey', queueBatchSize:QUEUE_BATCH_SIZE_PROPERTY_ROOM,propertyLinkFieldAbbr:'ListingKey',statusFieldAbbr:'RoomStatus'},
    media: {resoKey:'MediaKey',typeName:'MEDIA',statusField:'MediaStatus',propertyLinkField:'ResourceRecordKey', queueBatchSize:QUEUE_BATCH_SIZE_MEDIA,propertyLinkFieldAbbr:'RRK',statusFieldAbbr:'MS'},
    field: {resoKey:'FieldKey',typeName:'FIELD'},
    lookup: {resoKey:'LookupKey',typeName:'LOOKUP'},
    office: {resoKey:'OfficeKey',typeName:'OFFICE'},
  },
  "idx":{
    property:{typeName:'PROPERTY',queueBatchSize:QUEUE_BATCH_SIZE_PROPERTY},
    openHouse:{resoKey:'OpenHouseKey',typeName:'OPEN_HOUSE',statusField:'OpenHouseStatus',propertyLinkField:'ListingKey', queueBatchSize:QUEUE_BATCH_SIZE_OPEN_HOUSE,propertyLinkFieldAbbr:'ListingKey',statusFieldAbbr:'OpenHouseStatus'},
    propertyRoom: {resoKey:'RoomKey',typeName:'PROPERTY_ROOM',statusField:'RoomStatus',propertyLinkField:'ListingKey', queueBatchSize:QUEUE_BATCH_SIZE_PROPERTY_ROOM,propertyLinkFieldAbbr:'ListingKey',statusFieldAbbr:'RoomStatus'},
    media: {resoKey:'MediaKey',typeName:'MEDIA',statusField:'MediaStatus',propertyLinkField:'ResourceRecordKey',queueBatchSize:QUEUE_BATCH_SIZE_MEDIA,propertyLinkFieldAbbr:'RRK',statusFieldAbbr:'MS'},
    field: {resoKey:'FieldKey',typeName:'FIELD'},
    lookup: {resoKey:'LookupKey',typeName:'LOOKUP'},
    office: {resoKey:'OfficeKey',typeName:'OFFICE'},
  }
}

# used to notify the main loop to exit
gFatalError = null

{
  handleProcessStatus,
  handleExit,
  checkRunningProcess
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
}

gracefulExit = (error)->
  if error?
    debug.error error
  else
    debug.log 'gracefulExit'
  try
    await MlsImportKeysCol.updateOne {_id:IMPORT_KEY_ID},
      {$set:{
        error: error?.toString(),
        status:'exit'},},
        {upsert:true}
  catch err
    debug.error err
  handleExit error

processKillSignal PROCESS_STATUS_ID,->
  gracefulExit 'process killed by signal'

if config.trebReso?
  TRB_RESO_EVOW_API_TOKEN = config.trebReso.evow?.token
  TRB_RESO_IDX_API_TOKEN = config.trebReso.idx?.token
if gDownloadEvow
  if not TRB_RESO_EVOW_API_TOKEN
    throw new Error('Please provide the Treb Reso API evow token in the config file.')
  TrbResoEvowClient = createTrebResoClient({ dataset: 'odata', token: TRB_RESO_EVOW_API_TOKEN })
if gDownloadIdx
  if not TRB_RESO_IDX_API_TOKEN
    throw new Error('Please provide the Treb Reso API idx token in the config file.')
  TrbResoIdxClient =  createTrebResoClient({ dataset: 'odata', token: TRB_RESO_IDX_API_TOKEN })


if config.trebReso?.test
  OLDEST_QUERY_TS = new Date('2024-12-24T04:00:00Z')

fieldNullCounts = {}
savePropertyCounts = 0
gSpeedMeter = SpeedMeter.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

shouldSkipPropertySave = (hash, lastLog, mode) ->
  return false if mode is MODE_QUEUE
  return hash is lastLog?.hash


###*
# Saves or updates a property record in multiple collections while handling duplicates
# and maintaining data consistency across collections.
#
# @param {Object} property - The property record to save/update
# @param {Object} params - Configuration parameters containing:
#   @param {Object} coll - Collection references:
#     @param {Collection} log - Log collection for tracking changes
#     @param {Collection} dup - Duplicate log collection
#     @param {Collection} raw - Raw data collection
#     @param {Collection} filtered - Filtered data collection
#   @param {string} trebType - Type of TREB data (e.g. 'evow', 'idx')
# @param {string} mode - Mode of operation (queue/routine/init)
# @returns {Object|null} The saved property with _id or null if duplicate
###
saveProperty = (property, params, mode) ->
  # Create copy without modification timestamp for hash calculation
  propertyCopy = Object.assign {}, property
  delete propertyCopy[LAST_MT_FIELD]
  hash = getHash(JSON.stringify(propertyCopy))

  # Get collection references
  {log: logCol, dup: dupLogCol, raw: rawCol, merged: mergedCol} = params.coll
  
  # Check for duplicate by comparing hash with last record
  lastLog = await logCol.findOne(
    {id: property[MLS_ID_FIELD]},
    {projection: {_id:1, hash: 1, ModificationTimestamp: 1, id:1}, sort: {_mt: -1}}
  )

  # Handle duplicate case - same hash as previous record
  if shouldSkipPropertySave(hash, lastLog, mode)
    gSpeedMeter.check {"#{params.trebType}SameHash": 1}
    
    # Update modification timestamp if changed
    if lastLog[LAST_MT_FIELD]? and 
       property[LAST_MT_FIELD]? and 
       lastLog[LAST_MT_FIELD] isnt property[LAST_MT_FIELD]
      
      debug.info "#{lastLog.id} same hash as last log, updating #{LAST_MT_FIELD}", property[LAST_MT_FIELD]
      
      await logCol.updateOne(
        {_id: lastLog._id},
        {$set: {ModificationTimestamp: property.ModificationTimestamp}}
      )
      
    await dupLogCol.insertOne({
      data: property,
      id: property[MLS_ID_FIELD], 
      hash: hash,
      ModificationTimestamp: property[LAST_MT_FIELD]
    })
    
    return null

  # Save new/updated record
  mt = new Date(property[LAST_MT_FIELD])
  
  # Insert into log collection
  insertRet = await logCol.insertOne({
    id: property[MLS_ID_FIELD],
    hash: hash,
    sid: property[MLS_ID_FIELD],
    data: property,
    mt: mt,
    ModificationTimestamp: property[LAST_MT_FIELD]
  })
  gSpeedMeter.check {"#{params.trebType}Log": 1}

  # Prepare for raw collection
  property.mt = mt
  id = TRB_FIELD_STATS_ID + property[MLS_ID_FIELD]
  existProp = await rawCol.findOne({_id: id})
  ts = if existProp then existProp.ts else new Date()

  # Update field statistics
  FIELD_STATS_ID = "RESO_TREB_#{params.trebType}"
  propertyCopyFilterd = await filterEmptyFieldsNSaveStats(
    property, 
    FieldStatsCol,
    FIELD_STATS_ID
  )

  # Update raw collection if property is new or modified
  if not existProp or existProp.ModificationTimestamp < property.ModificationTimestamp
    await rawCol.replaceOne(
      {_id: id},
      {...property, ts: ts},
      {upsert: true}
    )
    gSpeedMeter.check {"#{params.trebType}Raw": 1}
  else
    debug.error "Property raw not new/updated, skipping #{insertRet.insertedId} #{property[MLS_ID_FIELD]} #{property.ModificationTimestamp}"

  # Recovery check: Update filtered collection
  existMergedProp = await mergedCol.findOne({_id: id})
  if not existMergedProp or existMergedProp?.ModificationTimestamp < property.ModificationTimestamp
    await updateFilteredRecord({
      id,
      propertyCopy: propertyCopyFilterd,
      ts,
      mt,
      mergedCol,
      existMergedProp
    })
    gSpeedMeter.check {"#{params.trebType}Filtered": 1}
  else
    debug.error "Property merged not new/updated, skipping #{insertRet.insertedId} #{property[MLS_ID_FIELD]} #{property.ModificationTimestamp}"

  property._id = id

  # add to resource queue for further processing
  await addPropToResourceQueue(property, params, mode)
  return property

updateFilteredRecord=({id,propertyCopy,ts,mt,mergedCol,existMergedProp})->
  # Step 1: Filter and retain specified fields
  reservedFields = {}
  if existMergedProp?
    for field in RESOURCE_MERGED_RESERVED
      if existMergedProp[field]?
        reservedFields[field] = existMergedProp[field]
  # Step 2: Merge reserved fields with new property
  newProperty = Object.assign {}, propertyCopy, reservedFields, {ts: ts, mt:mt}
  debug.debug "updateFilteredRecord", {id,newProperty}
  # Step 3: Perform `replaceOne`
  await mergedCol.replaceOne {_id: id}, newProperty, {upsert: true}
  return newProperty


addPropToResourceQueue = (prop, params, mode)->
  mergedCol = params.coll.merged
  existMergedProp = await mergedCol.findOne {_id:TRB_FIELD_STATS_ID + prop[MLS_ID_FIELD]}
  priority = calcPropPriority(prop, mode,existMergedProp)
  for resourceName in params.needMerge
    queueCol = params.queues[resourceName]
    if resourceName is 'openHouse' and prop.MlsStatus not in ['New','Price Change','Extension']
      debug.debug "skip #{prop[MLS_ID_FIELD]} #{prop.MlsStatus} #{resourceName}"
      continue
    await queueCol.addToQueue({_id:prop[MLS_ID_FIELD]},priority)

###*
# Queries and updates properties from RESO API based on a batch of records from the queue.
# Processes records in batches, removes them from queue when done, and adds related resources to queues.
#
# @param {Object} params - Configuration parameters for RESO API and collections
# @param {Array} records - Array of records from queue to process, each containing _id
# @param {Object} queue - Queue object to remove processed records from
# @param {Object} queryLog - Logger object for tracking progress
# @param {string} resourceName - Resource name to process, default is RESOURCE_NAME_PROPERTY
# @returns {Array} [updatedTotal, isDone] - Total records updated and completion status
###
getAndSavePropertiesByQueue = ({params, records, queue, queryLog, resourceName = RESOURCE_NAME_PROPERTY}) ->
  mode = MODE_QUEUE
  if resourceName isnt RESOURCE_NAME_PROPERTY
    gFatalError = new Error("resourceName must be #{RESOURCE_NAME_PROPERTY} got #{resourceName}")
    throw new Error("resourceName must be #{RESOURCE_NAME_PROPERTY}")
  # Return early if no records to process
  if not records or records?.length is 0
    debug.info "records array is empty, skip"
    return [0, null]

  # Initialize state
  isDone = false
  updatedTotal = 0
  returnedCountTotal = 0
  recordsLeft = records.slice()
  resourceConfig = params.resourceMapping[resourceName]
  resoClient = params.resoClient
  logKey = "get #{params.trebType} properties by queue"

  buildFilter = ()->
    listingKeys = recordsLeft.map((record) -> "'#{record._id}'")
    "ListingKey in (#{listingKeys.join(',')})"

  # Set up initial RESO API parameters
  resoParams = {
    $top: 1000,
    $skip: 0,
    $count: true,
    $filter: buildFilter()
  }

  # Main processing loop
  loop
    count = 0
    debug.info "Fetching properties by queue", {logKey, resoParams}

    if not resoParams.$filter
      debug.error "resoParams.$filter is empty, skip #{resourceName} #{params.trebType} #{mode}"
      isDone = true
      break

    # Log query parameters
    queryLog.begin logKey, {
      resourceName: 'property',
      trebType: params.trebType,
      filter: resoParams.$filter,
      top: resoParams.$top,
      skip: resoParams.$skip,
      count: resoParams.$count
    }

    # Get properties from RESO API
    resoReturn = await callWithRetry {
      func: -> resoClient.getResourceAsync resoClient.resourceTypes.PROPERTY,resoParams
      resourceName,
      trebType: params.trebType,
      mode,
      downloadName: "RESO_TREB_#{params.trebType}_#{mode}_queue_Download",
      params
    }

    if not resoReturn?.value?
      debug.error "No properties returned from API"
      queryLog.end logKey, {error: "No properties returned"}
      throw new Error("No properties returned")

    # Process each property
    for property in resoReturn.value
      count += 1
      await saveProperty(property, params, mode)
      recordsLeft = recordsLeft.filter((record) -> record._id isnt property[MLS_ID_FIELD])
      await queue.removeFromQueue({_id: property._id})

    # Update counters and logs
    updatedTotal += count
    returnedCount = resoReturn["@odata.count"]
    returnedCountTotal += returnedCount
    queryLog.update logKey, {count, updatedTotal, returnedCount, returnedCountTotal}

    # Check if batch is complete
    if resoReturn.value.length < resoParams.$top
      # Update status to 'U' for remaining records that were not returned
      if recordsLeft.length > 0
        debug.info "there is no return for these properties", recordsLeft

        for record in recordsLeft
          debug.info "Updating status to 'U' for #{record._id}"
          await params.coll.merged.updateOne(
            {_id: "TRB" + record._id},
            {$set: {MlsStatus: 'NotFound'}}
          )
      queryLog.end logKey, {isDone: true, updatedTotal, returnedCountTotal}
      await queue.removeBatchFromQueue(records)
      isDone = true
      break

    # Update filter for next batch
    resoParams.$filter = buildFilter()
    await sleep(QUERY_WAIT_TIME)

  return [updatedTotal, null]


###
# 计算房源优先级，融合 getRecordPriority 及图片缺失加分逻辑
# 并参考 impMappingRESO.coffee 的 onD 计算方式
# @param {Object} record - 房源记录
# @param {string} mode - 当前处理模式
# @param {Object} existMergedProp - 已合并的房源记录
# @returns {number} 优先级分数，分数越高优先级越高
###
# TODO：add ut
calcPropPriority = (record, mode, existMergedProp) ->
  priority = 0

  ###
  # 1. 没有图片（phoUrls 缺失或为空），最高优先级
  #   - existMergedProp.phoUrls 不存在时，加最高分
  ###
  hasPhoto = existMergedProp?.phoUrls?
  if not hasPhoto
    priority += 30000

  ###
  # 2. 活跃状态加分
  #   - MlsStatus 字段为 'a', 'active', 'new'（不区分大小写）
  ###
  isActive = record.MlsStatus?.toLowerCase() in ['a', 'active', 'new']
  if isActive
    priority += 10000

  ###
  # 3. 计算 onD，参考 impMappingRESO.coffee 字段优先级
  #   - 优先取ListingContractDate、OriginalEntryTimestamp
  #   - 若 onD 为本年度，则加分
  ###
  onD = record.ListingContractDate or record.OriginalEntryTimestamp
  onDate = null
  if onD?
    onDate = new Date(onD)
    if not isNaN(onDate) and onDate.getFullYear() is (new Date()).getFullYear()
      priority += 5000

  ###
  # 4. GTA 区域加分
  #   - Area 字段包含 Toronto/Peel/York/Durham/Halton
  ###
  if record.CountyOrParish? and /Toronto|Peel|York|Durham|Halton/i.test(record.CountyOrParish)
    priority += 500

  ###
  # 5. 当天新房源加分（录入时间在3天内）
  ###
  if onDate? and not isNaN(onDate) and (Date.now() - onDate < 3*24*60*60*1000)
    priority += 300

  ###
  # 6. 住宅类型加分
  #   - PropertyType 字段包含 residential 或 condo
  ###
  if record.PropertyType? and /residential|condo/i.test(record.PropertyType)
    priority += 200

  ###
  # 7. 状态加分
  #   - Sold: +50, Leased: +30
  ###
  if record.MlsStatus is 'Sold'
    priority += 50
  else if record.MlsStatus is 'Leased'
    priority += 30

  ###
  # 8. 省份加分
  #   - StateOrProvince 字段为 'ON'
  ###
  if record.StateOrProvince is 'ON'
    priority += 20

  ###
  # 9. 市场天数加分
  #   - DaysOnMarket 字段为数字且在0~30天内，优先级越高
  ###
  dom = parseInt(record.DaysOnMarket) if /^\d+$/.test record.DaysOnMarket
  if dom >= 0 and dom <= 30
    priority += 30-dom

  return priority


###*
# Downloads and upserts property records from RESO API asynchronously
# Handles both initial historical data load and routine updates
# When init, it'll fetch only INIT_FETCH_INTERVAL records before queryTs
#
# @param {Object} params - Configuration parameters containing:
#   @param {Date} queryTs - Timestamp to start querying from
#   @param {Object} params - RESO API and collection configuration 
#   @param {string} mode - Either MODE_INIT or MODE_ROUTINE
#   @param {Object} queryLog - For logging download progress
#   @param {Date} queryBeginTs - For initial load, the timestamp to start querying from
#   @param {string} downloadName - Name of the download operation. Used for timestamp logging.
# @returns {Array} [updatedTotal, lastTs] - Total records processed and completion status
###
getAndSavePropertiesByMt = ({queryTs, params, mode, queryLog, queryBeginTs, downloadName,resourceName = RESOURCE_NAME_PROPERTY}) ->
  if resourceName isnt RESOURCE_NAME_PROPERTY
    gFatalError = new Error("resourceName must be #{RESOURCE_NAME_PROPERTY} got #{resourceName}")
    throw new Error("resourceName must be #{RESOURCE_NAME_PROPERTY}")
  queryTs = new Date(queryTs) if (typeof(queryTs) is 'string') or (typeof(queryTs) is 'number')

  # Calculate query time window based on mode
  if mode is MODE_INIT
    # For initial load, get records modified between queryBeginTs and queryTs
    filter = "ModificationTimestamp lt #{queryTs.toISOString()} and ModificationTimestamp ge #{queryBeginTs.toISOString()}"
    orderby = "ModificationTimestamp desc"
  else if mode is MODE_ROUTINE
    # For routine updates, get records modified since queryTs
    # NOTE: queryTs is the last successful request timestamp, qurey one minute before it.
    queryTsNew = new Date(queryTs.getTime() - 1000 * 60)
    filter = "ModificationTimestamp ge #{queryTsNew.toISOString()}"
    orderby = "ModificationTimestamp asc"
  else
    throw new Error("Invalid mode: #{mode}")

  # Set up RESO API query parameters
  resoParams = {
    $top: MAX_RECORDS_PER_PAGE,  # Number of records per page
    $skip: 0,     # Starting offset
    $filter: filter,
    $orderby: orderby,
    $count: true  # Include total count in response
  }

  lastResoRequestTsNew = queryTs
  resourceConfig = params.resourceMapping[resourceName]
  ResoClient = params.resoClient
  isDone = false
  updatedTotal = 0  
  logKey = "get #{params.trebType} #{mode} properties"

  # Main download loop - continues until all records are processed
  while not isDone
    updatedCount = 0
    debug.info logKey, {resoParams}

    # Log query parameters
    queryLog.begin logKey, {
      resourceName: 'property',
      trebType: params.trebType,
      mode,
      filter: resoParams.$filter,
      top: resoParams.$top,
      skip: resoParams.$skip,
      orderby: resoParams.$orderby,
      count: resoParams.$count
    }

    # Fetch resoReturn from RESO API
    resoReturn = await callWithRetry {
      func: -> ResoClient.getResourceAsync(
        ResoClient.resourceTypes[resourceConfig.typeName],
        resoParams,
        FETCH_TIMEOUT
      )
      resourceName,
      trebType: params.trebType,
      mode,
      downloadName,
      params
    }

    # when at the end of the time period either backward or forward, we need to handle it. count could be 0.
    returnedCount = resoReturn?["@odata.count"]
    # Validate API response
    if (returnedCount is 0) or (resoReturn.value?.length is 0)
      debug.info "no records returned, skip"
      queryLog.end logKey, {
        isDone: true, 
        valueLength: resoReturn.value?.length,
        returnedCount
        }
      isDone = true
      break

    # Process each property in the response
    for property in resoReturn.value
      await saveProperty(property, params, mode)
      lastResoRequestTsNew = property.ModificationTimestamp
      updatedCount++

    # Update counters and logs
    updatedTotal += updatedCount
    queryLog.update logKey, {
      updatedCount,
      updatedTotal,
      returnedCount,
      lastMT: lastResoRequestTsNew
    }

    # Check if batch is complete
    isDone = (resoReturn.value.length < resoParams.$top)

    if mode is MODE_ROUTINE 
      # update resource request log only for routine mode.
      # For init mode, we save the timestamp into the overall key record.
      # So we have to return the timestamp to the caller.
      await updateResourceRequestLog({
        MlsImportKeysCol,
        nextTs: lastResoRequestTsNew,
        resourceName,
        count: resoReturn.value.length,
        done: false, # for routine mode, we never done.
        downloadName
      })
    else if mode is MODE_INIT
      if resourceName in RESOURCE_TO_BE_MERGED_EVOW
        throw new Error("Unsupported mode: #{mode}")
    else
      throw new Error("Unsupported mode: #{mode}")

    # close this log to reserve the values
    queryLog.end logKey, {
      isDone,
      updatedTotal,
      lastMT: lastResoRequestTsNew
    }

    break if isDone

    # Update filter for next batch based on mode
    if mode is MODE_INIT
      # the server may have more records at the same timestamp of the last record, so we need to include it.
      resoParams.$filter = "ModificationTimestamp le #{lastResoRequestTsNew} and ModificationTimestamp ge #{queryBeginTs.toISOString()}"
    else
      resoParams.$filter = "ModificationTimestamp ge #{lastResoRequestTsNew}"

    # Wait before next request to avoid rate limiting
    await sleep(QUERY_WAIT_TIME)

  return [updatedTotal, lastResoRequestTsNew]

      

handleResourceError = ({error, resourceName, trebType, mode, retryCount, downloadName}) ->
  debug.error "handleResourceError", {error, resourceName, trebType, mode, retryCount, downloadName}
  if ignorableErrorReso(error)
    # Just return current retryCount for ignorable errors
    return retryCount
  else
    retryCount++

  if retryCount > RETRY_INTERVALS.length
    debug.critical "Failed after #{retryCount} times for #{resourceName} #{mode} #{trebType}. Exiting loop.", error
    gFatalError = error
    throw error

  sleepTime = RETRY_INTERVALS[retryCount - 1]
  debug.info "Retry attempt #{retryCount} for #{resourceName} #{mode} #{trebType}. Sleeping for #{sleepTime} minutes"
  await sleep(sleepTime * 60 * 1000)
  return retryCount


# Fetches and saves resources (like openHouse, propertyRoom, media etc) from RESO API
# May perform multiple requests to fetch all the resources under given queryTs or queue.
# @param {string} resourceName - Name of the resource to fetch (openHouse, propertyRoom etc)
# @param {Object} params - Configuration parameters
# @param {string} mode - Mode of operation (queue/routine)
# @param {Object} queue - Queue object for the resource
# @param {Array} records - Array of record to process
# @param {Date} queryTs - Timestamp to start querying from
# @param {Date} lastResoRequestTs - Last successful request timestamp
# @param {Object} queryLog - Logger object
# @returns {Array} [totalCount, lastRequestTimestamp]
# TODO: fn too long
getAndSaveResourcesAsync = ({
  resourceName,
  params,
  mode,
  queue,
  records, # for queue mode, this is the records to download
  queryTs, # for routine mode, this is the query timestamp
  queryLog,
  downloadName
}) ->
  debug.info "Starting getAndSaveResourcesAsync", {
    resourceName, 
    trebType: params.trebType,
    mode,
    queryTs,
    records: records?.length
  }

  # Initialize counters and state
  updatedTotal = 0
  returnedCountTotal = 0
  lastResoRequestTsNew = queryTs
  finishedIds = []
  oneResource = []
  oldListingKey = null
  isDone = false
  # Setup initial query parameters
  resoParams = buildQueryParams({
    resourceName, 
    params,
    mode,
    queryTs, 
    records,
    finishedIds
  })

  # Get resource configuration and client
  resourceConfig = params.resourceMapping[resourceName]
  ResoClient = params.resoClient

  # Main fetch loop until the resources are all fetched under given query
  while not isDone
    updatedCount = 0
    logKey = "get #{resourceName} #{params.trebType} #{mode}"
    debug.info logKey, {resoParams}

    tmpTime = new Date()
    if (resourceName in RESOURCE_TO_BE_MERGED_EVOW) and (not resoParams.$filter)
      debug.error "resoParams.$filter is empty, skip #{resourceName} #{params.trebType} #{mode}"
      isDone = true
      break

    # need to log the primitive values, not the object. Contents in object will change in each loop. queryLog is only saved at the end of the loop.
    queryLog.begin logKey, {
      resourceName,
      trebType: params.trebType,
      mode,
      filter: resoParams.$filter,
      top: resoParams.$top,
      skip: resoParams.$skip,
      orderby: resoParams.$orderby,
      count: resoParams.$count
    }

    # Fetch resoReturn from RESO API
    resoReturn = await callWithRetry {
      func: -> ResoClient.getResourceAsync(
        ResoClient.resourceTypes[resourceConfig.typeName],
        resoParams,
        FETCH_TIMEOUT
      )
      resourceName,
      trebType: params.trebType,
      mode,
      downloadName,
      params
    }

    returnedCount = resoReturn["@odata.count"]
    # Validate API response
    if (returnedCount is 0) or (resoReturn.value?.length is 0)
      debug.info "no records returned, skip #{resourceName} #{params.trebType} #{mode}"
      if mode is MODE_QUEUE
        await queue.removeBatchFromQueue(records)
        # NOTE：unset the resource for records
        await unsetResourceForRecordsMerged({resourceName, params, records, finishedIds})
      queryLog.end logKey, {
        isDone: true, 
        valueLength: resoReturn.value?.length,
        returnedCount
        }
      isDone = true
      break

    # save resource to collection
    await saveResource(resoReturn.value, resourceName, params, mode, resourceConfig)
    # Process each resource
    for resource in resoReturn.value
      [lastResoRequestTsNew, oldListingKey, oneResource] = await processResource({
        resource,
        resourceName,
        params,
        mode,
        oneResource,
        finishedIds,
        queue,
        resourceConfig,
        oldListingKey
      })
      updatedCount++

    updatedTotal += updatedCount
    # queryLog is used to log all the intermediate values for later use and debugging
    queryLog.update logKey, {
      updatedCount,
      updatedTotal,
      returnedCount,
      lastMT: lastResoRequestTsNew
    }

    # office resource has not lastMT field, so we use queryTs as the lastMT
    lastResoRequestTsNew ?= queryTs

    # Check if batch is complete
    isDone = (resoReturn.value.length < resoParams.$top)

    if mode is MODE_ROUTINE
      # save the lastMT for next query
      await updateResourceRequestLog({
        MlsImportKeysCol,
        nextTs: lastResoRequestTsNew,
        resourceName,
        count: resoReturn.value.length,
        done: false,
        downloadName
      })
    else if mode is MODE_QUEUE
      if isDone
        # merge the remaining resources into property
        await mergeResourceArrayIntoProperty({
          key: oldListingKey,
          params,
          resourceName,
          oneResource
        })
        finishedIds.push(oldListingKey)
         # unset the resource for records which are not in the finishedIds
        await unsetResourceForRecordsMerged({resourceName, params, records, finishedIds})
        await queue.removeBatchFromQueue(records)
        debug.debug "All done", resourceName, new Date() - tmpTime
      else
        debug.warn "queue mode reached limit. #{resourceName} #{params.trebType} #{mode} #{resoReturn.value.length}"
    else # MODE_INIT
      if resourceName in RESOURCE_TO_BE_MERGED_EVOW
        throw new Error("Unsupported mode: #{mode}")

    # merge resource into property from resourceUpdatedByMtQueue
    if resourceName in RESOURCE_TO_BE_MERGED_EVOW
      updatedCount = await mergeResourcesIntoProperty({params, resourceName})
      debug.info "merged #{resourceName} into property count", updatedCount
    # if media, get all _id from mediaCompress and compress the phoUrls
    if (resourceName is 'media') and (mode is MODE_ROUTINE)
      compressedMediaCount = await filterCompressMediaAndUpdateMerged(params)
      debug.info "compressedMediaCount", compressedMediaCount
    
    # close this log to reserve the values
    queryLog.end logKey, {
      isDone,
      updatedTotal,
      lastMT: lastResoRequestTsNew
    }

    break if isDone

    # Update query parameters for next batch
    if resourceName in RESOURCE_TO_BE_MERGED_EVOW
      resoParams = buildQueryParams({
        resourceName,
        params,
        mode,
        queryTs: lastResoRequestTsNew, # use the lastMT as the queryTs for next batch
        records,
        finishedIds
      })
    else if resourceName in RESOURCE_NO_MERGE
      resoParams.$skip += params.$top
    else
      throw new Error("Unsupported resource type: #{resourceName} in getAndSaveResourcesAsync")

    await sleep(QUERY_WAIT_TIME)

  # office resource has not lastMT field, so we use queryTs as the lastMT
  return [updatedTotal, (lastResoRequestTsNew or queryTs)]


# save resources to collection use bulkWrite
saveResource = (resources, resourceName, params, mode, resourceConfig) ->
  tmpTime = new Date()
  ops = resources.map (resource) ->
    docId = resource[resourceConfig.resoKey]
    data  = if resourceName is 'media' then convertMediaToAbbreviated(resource) else resource
    updateOne:
      filter: {_id: docId}
      update: {
        $set: data
        $setOnInsert: {ts: new Date()}
      }
      upsert: true

  try
    await params.coll[resourceName].bulkWrite(ops, {ordered: false})
  catch err
    debug.error "bulkWrite failed for #{resourceName}", err
    throw err
  debug.debug "saveResource", resourceName, resources.length, new Date() - tmpTime

# merge resource into property, get resources from local resource collection
mergeResourcesIntoProperty = ({params, resourceName}) ->
  needUpdateProps = await params.coll.resourceUpdatedByMt.findToArray({resourceType: resourceName})
  mergedCol = params.coll.merged
  updatedCount = 0
  allRecords = []
  finishedIds = []
  for needUpdateProp in needUpdateProps
    needUpdateProp._id = (needUpdateProp.id).slice(3) #needUpdateProp.id='TRBN12144314'
    allRecords.push(needUpdateProp)
    prop = await mergedCol.findOne {_id: needUpdateProp.id}
    if not prop
      continue # already added to queue in addResourceToResourceUpdatedByMtQueue
    # step1: get all the resources from the resource collection
    listingKey = (prop._id).slice(3)
    queryKeyField = params.resourceMapping[resourceName].propertyLinkFieldAbbr
    query = {}
    query[queryKeyField] = listingKey
    resources = await params.coll[resourceName].findToArray query

    # step2: get all active
    resourcesActive = []
    statusField = params.resourceMapping[resourceName].statusFieldAbbr
    resourcesActive = resources.filter((resource) -> resource[statusField] is 'Active')
    resourcesActive.sort (a, b) ->a._mt - b._mt
    
    # step3: merge the resources into property
    await mergeResourceArrayIntoProperty({
      key: listingKey,
      params,
      resourceName: resourceName,
      oneResource: resourcesActive,
      resourceAbbr: true
    })
    if resourcesActive.length > 0
      finishedIds.push(listingKey)
    else
      debug.debug "no active resources for #{resourceName} #{listingKey}"

    # step4: add ts to mediaCompress and delete the resource from resourceUpdatedByMtQueue
    if (resourceName is 'media') and (resourcesActive.length > 0)
      await params.coll.mediaCompress.updateOne {_id: prop._id},
        { $set: {ts: new Date()}},
        { upsert: true}
    await params.coll.resourceUpdatedByMt.deleteOne {id: prop._id,resourceType: resourceName}
    updatedCount++

  # step5: unset the resource for records not in the finishedIds
  await unsetResourceForRecordsMerged({resourceName, params, records: allRecords, finishedIds})
  return updatedCount


# unset the resource for records[record._id='N12144314'] not in the finishedIds['N12144314','N12144315']
unsetResourceForRecordsMerged = ({resourceName, params, records, finishedIds=[]}) ->
  debug.debug "unsetResourceForRecordsMerged #{resourceName} #{params.trebType}", records, finishedIds
  propQueue = params.queues[RESOURCE_NAME_PROPERTY]
  mergedCol = params.coll.merged
  unset = {}
  for record in records
    key = record._id
    if key in finishedIds
      continue
    prop = await mergedCol.findOne {ListingKey: key}
    if not prop
      await propQueue.addToQueue({_id: key}, NEW_PROP_PRIORITY)
      debug.debug "add new prop to queue from #{resourceName}",key#,resource,
    else
      unset[resourceName] = 1
      if resourceName in ['media']
        unset.phoUrls = 1
        unset.thumbUrl = 1
        unset.pho = 1
      await mergedCol.updateOne {ListingKey: key}, {$unset: unset}
      debug.debug "unset #{resourceName} from #{key}"



# Get all _id from mediaCompress and filter and compress to get phoUrls,thumbUrl and pho, and update the merged
# @param {Object} params - Configuration parameters
filterCompressMediaAndUpdateMerged = (params) ->
  mediaCompress = await params.coll.mediaCompress.findToArray({})
  mergedCol = params.coll.merged
  debug.debug "filterCompressMediaAndUpdateMerged #{mediaCompress.length}"
  updatedCount = 0
  for prop in mediaCompress
    set = {}
    unset = {}
    prop = await mergedCol.findOne {_id: prop._id},{media:1}
    {thumbUrl, phoNum, compressedMedia, vturl} = filterCompressTrebResoMedia({
      media: prop.media,
      id: prop._id
    })
    debug.debug "compressed filterCompressMediaAndUpdateMerged #{prop._id}", thumbUrl, phoNum, compressedMedia, vturl, prop.media
    if vturl
      set.vturl = vturl
    else
      unset.vturl = 1
    if compressedMedia and thumbUrl
      set.phoUrls = compressedMedia
      set.thumbUrl = thumbUrl
      set.pho = phoNum
      await mergedCol.updateOne { _id: prop._id }, { $set: set, $unset: unset }
    else
      unset.phoUrls = 1
      unset.thumbUrl = 1
      unset.pho = 1
      await mergedCol.updateOne { _id: prop._id }, { $unset: unset }
    await params.coll.mediaCompress.deleteOne {_id: prop._id}
    updatedCount++
  return updatedCount

# Process a single resource by saving it to the collection and merging it with property if needed
# @param {Object} resource - The resource to process
# @param {string} resourceName - Name of the resource (openHouse, propertyRoom etc)
# @param {Object} params - Configuration parameters
# @param {string} mode - Mode of operation (queue/routine)
# @param {Array} oneResource - Array to collect resources for batch processing
# @param {Array} finishedIds - Array of processed property IDs
# @param {Object} queue - Queue object for the resource
# @param {Object} resourceConfig - Resource configuration
# @param {string} oldListingKey - Previous listing key for batch processing
# @returns {Array} [lastResoRequestTs, listingKey, resourceArray]
processResource = ({
  resource,
  resourceName,
  params,
  mode,
  oneResource,
  finishedIds,
  queue,
  resourceConfig,
  oldListingKey
}) ->
  lastResoRequestTsNew = resource[LAST_MT_FIELD]

  # Return early if resource doesn't need merging with property
  if resourceName in RESOURCE_NO_MERGE
    return [lastResoRequestTsNew, null, oneResource]

  # Process resource based on mode
  if mode is MODE_QUEUE
    # Batch process resources for same property
    [lastResoRequestTsNew, oldListingKey, oneResource] = await processFilteredResource({
      resource,
      params,
      resourceName,
      oneResource,
      finishedIds,
      queue,
      oldListingKey,
      resourceConfig
    })
  else
    # Merge single resource into property
    # await mergeResourceIntoProperty({resource, params, resourceName, mode})
    # add resource to resourceUpdatedByMtQueue
    await addResourceToResourceUpdatedByMtQueue({resource, params, resourceName})
    gSpeedMeter.check {"#{params.trebType}#{resourceName}":1}

  return [lastResoRequestTsNew, oldListingKey, oneResource]


# add resource to resourceUpdatedByMtQueue
addResourceToResourceUpdatedByMtQueue = ({resource, params, resourceName}) ->
  resourceUpdatedByMtCol = params.coll.resourceUpdatedByMt
  propertyLinkField = params.resourceMapping[resourceName].propertyLinkField 
  id = TRB_FIELD_STATS_ID + resource[propertyLinkField]
  mergedCol = params.coll.merged
  prop = await mergedCol.findOne {ListingKey: resource[propertyLinkField]}
  propQueue = params.queues[RESOURCE_NAME_PROPERTY]
  if not prop
    await propQueue.addToQueue({_id: resource[propertyLinkField]}, NEW_PROP_PRIORITY)
    debug.debug "add new prop to queue from #{resourceName}",resource[propertyLinkField]#,resource,

  # Create a unique document for this property and resource type
  try
    await resourceUpdatedByMtCol.updateOne(
      { id, resourceType: resourceName },
      { $set: { ts: new Date() }},
      { upsert: true }
    )
  catch err
    if err.code is 11000  # DuplicateKey
      debug.warn "Duplicate queue entry #{id}-#{resourceName}, skip"
    else
      throw err


# Process a resource when filtering by property array, collecting resources for batch processing
# @param {Object} resource - The resource to process
# @param {Object} params - Configuration parameters  
# @param {string} resourceName - Name of the resource
# @param {Array} oneResource - Array to collect resources
# @param {Array} finishedIds - Array of processed property IDs
# @param {Object} queue - Queue object
# @param {string} oldListingKey - Previous listing key
# @param {Object} resourceConfig - Resource configuration
# @returns {Array} [lastResoRequestTs, listingKey, resourceArray]
processFilteredResource = ({
  resource,
  params,
  resourceName,
  oneResource,
  finishedIds,
  queue,
  oldListingKey,
  resourceConfig
}) ->
  propertyKey = resource[resourceConfig.propertyLinkField]
  
  # First resource in batch
  if not oldListingKey
    oldListingKey = propertyKey
    oneResource.push(resource)
    return [resource[LAST_MT_FIELD], oldListingKey, oneResource]

  # Resource belongs to different property
  if oldListingKey isnt propertyKey
    # Process previous batch
    await mergeResourceArrayIntoProperty({
      key: oldListingKey,
      params,
      resourceName,
      oneResource
    })
    await queue.removeFromQueue({_id: oldListingKey})
    finishedIds.push(oldListingKey)
    
    # Start new batch
    oneResource = [resource]
    oldListingKey = propertyKey
  else
    # Add to current batch
    oneResource.push(resource)

  return [resource[LAST_MT_FIELD], oldListingKey, oneResource]


buildQueryParams = ({resourceName, params, mode, queryTs, records, finishedIds}) ->
  resoParams = {
    $top: MAX_RECORDS_PER_PAGE,
    $skip: 0,
    $count: true
  }
  if resourceName in RESOURCE_TO_BE_MERGED_EVOW
    [filter, orderby] = buildFilterAndOrderBy({
      mode,
      resourceName,
      params,
      records,
      finishedIds,
      queryTs
    })
    if filter
      resoParams.$filter = filter
    if orderby
      resoParams.$orderby = orderby
  if resourceName in RESOURCE_NO_MERGE
    # max amount Lookup is around 9k
    resoParams.$top = 100000
  return resoParams


buildFilterAndOrderBy = ({mode, resourceName, params, records, finishedIds, queryTs}) ->
  if mode is MODE_QUEUE
    if not records?.length > 0
      throw new Error("records array is empty! in #{resourceName} #{mode}")
    return buildIdArrayQuery({
      resourceName,
      params, 
      records,
      finishedIds
    })
  else
    return buildTimestampQuery({
      resourceName,
      queryTs
    })

# Builds filter and orderby clauses for queries based on an array of record IDs
# @param {string} resourceName - Name of the resource (propertyRoom, openHouse, media etc)
# @param {Object} params - Configuration parameters containing resource mappings
# @param {Array} records - Array of records containing IDs to filter by
# @param {Array} finishedIds - Optional array of already processed IDs to exclude
# @returns {Array} [filter, orderby] - Query filter and sort clauses, or [null, null] if no valid records
buildIdArrayQuery = ({
  resourceName,
  params,
  records,
  finishedIds=null
}) ->
  propertyLinkField = params.resourceMapping[resourceName].propertyLinkField
  
  # Filter out any already processed records
  if finishedIds?.length > 0
    records = records.filter((record) -> record._id not in finishedIds)
    
  # Return null if no records to process
  if records?.length is 0
    return [null, null]
    
  # Build IN clause with record IDs
  recordIds = records.map((record) -> "'#{record._id}'").join(',')
  filter = "#{propertyLinkField} in (#{recordIds})"
  
  # Add status filter except for idx propertyRoom
  if not ((resourceName is 'propertyRoom') and (params.trebType is 'idx'))
    statusField = params.resourceMapping[resourceName].statusField
    filter = filter + " and #{statusField} eq 'Active'"
    
  # Set appropriate ordering based on resource type
  if resourceName in ['propertyRoom', 'openHouse']
    orderby = "ListingKey asc"
  else if resourceName in ['media']
    orderby = "ResourceRecordKey asc"
  else
    orderby = "ModificationTimestamp asc"
    
  return [filter, orderby]

# Builds filter and orderby clauses for timestamp-based queries
# @param {string} resourceName - Name of the resource (propertyRoom, openHouse, media etc) 
# @param {Date} queryTs - Base timestamp to query from
# @returns {Array} [filter, orderby] - Query filter and sort clauses
buildTimestampQuery = ({
  resourceName,
  queryTs
}) ->
  # For openHouse, don't query data older than threshold
  thresholdDate = new Date(Date.now() - OPEN_HOUSE_THRESHOLD)
  if (resourceName is 'openHouse') and (queryTs < thresholdDate)
    queryTs = thresholdDate

  queryTs = new Date(queryTs) if 'string' is typeof queryTs
  # Only apply timestamp filter for openHouse and media  
  filter = null
  if resourceName in ['openHouse', 'media']
    filter = "#{LAST_MT_FIELD} ge #{queryTs.toISOString()}"
    
  # Always sort by modification time
  orderby = "ModificationTimestamp asc"
  return [filter, orderby]


mergeResourceArrayIntoProperty = ({key, params, resourceName, oneResource, resourceAbbr=false}) ->
  if oneResource.length is 0
    return null
  mergedCol = params.coll.merged
  prop = await mergedCol.findOne {ListingKey: key},{projection: {_id: 1}}
  readableResources = []
  if resourceAbbr and (resourceName is RESOURCE_NAME_MEDIA)
    resourceNameConvert = 'mediaAbbr'
  else
    resourceNameConvert = resourceName
  for resource in oneResource
    readableResource = convertResourceToReadable({resource, resourceName: resourceNameConvert})
    # if (resourceName is 'media') and (resource.MediaCategory is 'Photo') and (resource.ImageSizeDescription isnt 'LargestNoWatermark')
    #     continue
    readableResources.push(readableResource)
  if not prop
    debug.error "mergeResourceArrayIntoProperty #{params.trebType} #{resourceName} #{key} not found" #oneResource
    return null
  set = {}
  unset = {}
  set[resourceName] = readableResources
  if (resourceName is 'media') and (set.media?)
    set.media = set.media
      .map((item) -> { ...item, id: parseInt(item.id) || 0 })
      .sort((a, b) -> a.id - b.id)
    # compress the media urls
    {thumbUrl, phoNum, compressedMedia, vturl} = filterCompressTrebResoMedia({
      media: set.media,
      id: prop._id
    })
    if vturl
      set.vturl = vturl
    else
      unset.vturl = 1
    if compressedMedia and thumbUrl
      set.phoUrls = compressedMedia
      set.thumbUrl = thumbUrl
      set.pho = phoNum
    else
      unset.phoUrls = 1
      unset.thumbUrl = 1
      unset.pho = 1
    debug.debug "compressed mergeResourceArrayIntoProperty #{params.trebType} #{resourceName} _id:#{prop._id}, set:#{set}, unset:#{unset}, media:#{set.media}"

  debug.debug "mergeResourceArrayIntoProperty #{params.trebType} #{resourceName} _id:#{prop._id}, readableResources.length:#{readableResources.length}, oneResource.length:#{oneResource.length}"

  await mergedCol.updateOne { _id: prop._id },
    { $set: set, $unset: unset },
    { upsert: true }
  return prop


RESOURCE_CONVERTERS = {
  'propertyRoom': convertRoom
  'openHouse': convertOpenHouse
  'media': convertMedia
  'mediaAbbr': convertMediaAbbr
}

convertResourceToReadable = ({resource, resourceName}) ->
  converter = RESOURCE_CONVERTERS[resourceName]
  if not converter
    throw new Error("No converter found for resource type: #{resourceName}")
  convertedResource = converter(resource)
  return convertedResource


# return [resourceStatus, media]
# convertResourceToReadableAndAddPhotoQueue = ({params, resource, resourceName}) ->
#   readableResource = convertResourceToReadable({resource, resourceName})
#   statusField = params.resourceMapping[resourceName].statusField

#   # Create basic object with key
#   result = {}
#   resoKey = params.resourceMapping[resourceName].resoKey
#   result[resoKey] = resource[resoKey]
#   # if (resourceName is 'media') and (resource.MediaCategory is 'Photo') and (resource.ImageSizeDescription isnt 'LargestNoWatermark') # only download largest photo  #sizeDesc
#   #   return ['NotLargestPhoto', result]
#   if resource[statusField] is 'Deleted'
#     if resourceName is 'media'
#       #delete img
#       debug.debug "should delete img", resource.ResourceRecordKey, resource.MediaKey
#     return ['Deleted', readableResource]

#   # #TODO: img: addToQueueAndDownloadPhoto
#   return ['Active', readableResource]


# mergeResourceIntoProperty = ({resource, params, resourceName, mode}) ->
#   [resourceStatus, readableResource] = convertResourceToReadableAndAddPhotoQueue({params, resource, resourceName})
#   mergedCol = params.coll.merged
#   resoKey = params.resourceMapping[resourceName].resoKey
#   propertyLinkField = params.resourceMapping[resourceName].propertyLinkField  
#   query = { ListingKey:resource[propertyLinkField] }
#   debug.debug "do mergeResourceIntoProperty #{resourceName} #{params.trebType} #{mode}", query, resource[resoKey] #,resource
#   propQueue = params.queues[RESOURCE_NAME_PROPERTY]
#   prop = await mergedCol.findOne query,
#     readConcern:
#       level: "majority"
#     maxTimeMS: 1000
#   if not prop
#     await propQueue.addToQueue({_id: resource[propertyLinkField]}, NEW_PROP_PRIORITY)
#     debug.debug "add new prop to queue from #{resourceName}",resource[propertyLinkField]#,resource,
#     return null
#   set = generateSetForPropertyResource(prop, resourceName, readableResource, resoKey,resource)
#   if not set
#     return null
#   debug.debug "mergeResourceIntoProperty #{mode} #{resourceName} #{params.trebType}", prop._id, set
#   if resourceName is 'media'
#     await params.coll.mediaCompress.updateOne {
#       _id: prop._id
#     }, {
#       $set: {
#         ts: new Date(),
#       }
#     }, {
#       upsert: true
#     }
#   await mergedCol.updateOne { _id: prop._id },
#     { $set: set },
#     { upsert: true, writeConcern: { w: "majority" } }
#   return prop


isResourceNewer = (resource, prop) ->
  try
    if not resource[LAST_MT_FIELD]? or not prop[LAST_MT_FIELD]?
      debug.warn {
        action: 'missing_timestamp',
        resource: resource,
        property_id: prop._id
      }
      return false
    resourceTs = new Date(resource[LAST_MT_FIELD])
    propTs = new Date(prop[LAST_MT_FIELD])
    debug.debug "isResourceNewer, resourceTs #{resourceTs}, propTs #{propTs}"
    return resourceTs >= propTs
  catch err
    debug.error {
      action: 'timestamp_comparison_error',
      error: err.message,
      resource: resource,
      property_id: prop._id
    }
    return false



shouldUpdateResource = ({resourceIndex, resource, prop, readableResource, resourceName}) ->
  # 如果资源不存在，需要更新
  if resourceIndex < 0
    return true
    
  existingResource = prop[resourceName]?[resourceIndex]
  if not existingResource
    return true

  hasChanged = not util.isDeepStrictEqual(readableResource, existingResource)
  isNewer = isResourceNewer(resource, prop)
  
  debug.debug "shouldUpdateResource", hasChanged, isNewer, readableResource, existingResource
  #NOTE：Only resources obtained via MT will be checked here; no need to check isNewer
  return hasChanged

generateSetForPropertyResource = (prop, resourceName, readableResource, resoKey, resource) ->
  set = {}
  if not prop[resourceName]
    set[resourceName] = [readableResource]
  else
    resourceIndex = prop[resourceName].findIndex (r) -> r.key is readableResource.key
    needUpdate = shouldUpdateResource({
      resourceIndex,
      resource,
      prop,
      readableResource,
      resourceName
    })
    if needUpdate
      newIndex = if resourceIndex >= 0 then resourceIndex else prop[resourceName].length
      prop[resourceName][newIndex] = readableResource
    else
      return null
    set[resourceName] = prop[resourceName]
  # only save active resource
  set[resourceName] = set[resourceName].filter((item) -> item.status is 'Active')
  if (resourceName is 'media') and (set.media?)
    set.media = set.media
      .map((item) -> { ...item, id: parseInt(item.id) || 0 })
      .sort((a, b) -> a.id - b.id)
  return set



   
###*
# Queries historical property data backwards in time from a given timestamp
# Will fetch records in intervals until either:
# 1. No more records are found for a sufficient time period
# 2. Reached the oldest allowed query timestamp
#
# @param {Date} queryTs - Starting timestamp to query backwards from
# @param {Object} params - Configuration parameters containing:
#   @param {string} trebType - Type of TREB data being queried
#   @param {Object} coll - Database collections
#   @param {Object} resoClient - RESO API client
# @returns {void}
###
initialQueryBack = (queryTs, params) ->
  # Initialize logging and counters
  queryLog = EventLogger.createLogger(debug)
  updatedCount = 0
  totalUpdatedCount = 0
  queryTs = new Date(queryTs) # make sure queryTs is a Date object
  lastFetchedTs = queryTs
  allDone = false
  mode = MODE_INIT
  resourceName = RESOURCE_NAME_PROPERTY
  downloadName = "RESO_TREB_#{params.trebType}_#{mode}_#{RESOURCE_NAME_PROPERTY}_Download"
  startTs = null

  # Main query loop - continues until all historical data is fetched
  while not allDone
    return if gFatalError
    try
      curError = null
      updatedCount = 0
      startTs ?= new Date()
      
      # Calculate next timestamp interval to query
      queryBeginTs = new Date(queryTs.getTime() - INIT_FETCH_INTERVAL)

      # Fetch and save properties for this interval
      [updatedCount, lastTs] = await getAndSavePropertiesByMt({
        queryTs,
        params,
        mode: MODE_INIT,
        queryLog,
        queryBeginTs,
        downloadName
      })
      totalUpdatedCount += updatedCount
      gSpeedMeter.check {"init_#{resourceName}":updatedCount}

      # Update last fetched timestamp if we got new records
      if lastTs isnt queryTs
        lastFetchedTs = lastTs
      # Check if we're done based on:
      # 1. No records found AND sufficient blank period has passed, OR [deleted]
      # 2. Reached oldest allowed query timestamp
      # NOTE: 在2021-12-17-2022-01-17，30天内没有数据
      if (updatedCount is 0) and (queryTs < OLDEST_QUERY_TS)
        allDone = true

      # Update import status in database
      set = {}
      if allDone
        set[params.trebType + 'InitDone'] = true
      set[params.trebType + 'InitDoneTs'] = queryBeginTs
      set['status'] = RUNNING_NORMAL
      await MlsImportKeysCol.updateOne { _id: IMPORT_KEY_ID },
        { $set: set, $unset:{error:1}},
        { upsert: true }

      # Update process status
      await handleProcessStatus {
        status: RUNNING_NORMAL,
        nextTs: (Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }

      break if allDone
      queryTs = queryBeginTs

    catch error
      # Handle errors with retries
      curError = error
      debug.error "initialQueryBack #{resourceName} #{params.trebType} error", error
      
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error: error.toString(),
        nextTs: (Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }

    # Log results for this iteration
    await params.coll.eventLog.updateOne \
      {_id: "init-#{params.trebType}-#{startTs.toISOString()}"},
      {$set: {
        startTs,
        endTs: new Date(),
        logs: queryLog.logs,
        error: curError?.toString() or 'none', 
        lastUpdatedCount: updatedCount,
        updatedTotal: totalUpdatedCount
      }},{upsert: true}

    # Reset for next iteration
    if queryLog.logs.length > 100
      queryLog = EventLogger.createLogger(debug)
      totalUpdatedCount = 0
      startTs = null
      curError = null


downloadNoMergeResources = (queryTs, params) ->
  queryLog = EventLogger.createLogger(debug)
  startTs = new Date()
  totalUpdatedCount = 0
  updatedCount = 0
  curError = null  # Initialize error variable
  try
    for resourceName in RESOURCE_NO_MERGE
      return if gFatalError
      downloadName = "RESO_TREB_#{params.trebType}_init_#{resourceName}_Download"
      debug.info "downloading no-merge resource", resourceName, params.trebType
      [count, _] = await getAndSaveResourcesAsync({
        resourceName,
        params,
        mode: MODE_INIT,
        queryTs,
        queryLog,
        downloadName
      })
      updatedCount = count
      totalUpdatedCount += count
  catch error
    curError = error  # Set error if caught
    debug.critical error
    gFatalError = error
    await handleProcessStatus {
      status: RUNNING_ERROR,
      error,
      nextTs: new Date(Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
    }
  await params.coll.eventLog.updateOne \
    {_id: "init-#{params.trebType}-NoMerge"},
    {$set: {
      startTs,
      endTs: new Date(),
      status: 'Finished',
      logs: queryLog.logs,
      error: curError?.toString(),
      updatedCount,
      updatedTotal: totalUpdatedCount,
    }},{upsert: true}


# A single thread to download ids from a queue of a resource type.
# It will run until the queue is empty, then sleep for QUEUE_SLEEP_MS.
# It will run again when the queue is not empty.
# It will not end.
downloadQueue = (resourceName, params) ->
  startTs = null
  totalUpdatedCount = 0
  curError = null
  queryLog = EventLogger.createLogger(debug)
  queue = params.queues[resourceName]
  if resourceName is RESOURCE_NAME_PROPERTY
    getAndSaveFunc = getAndSavePropertiesByQueue
  else
    getAndSaveFunc = getAndSaveResourcesAsync
  downloadName = "RESO_TREB_#{params.trebType}_queue_#{resourceName}_Download"
  while true
    # if fatal error, exit the loop
    return if gFatalError
    curError = null
    updatedCount = 0
    startTs ?= new Date()
    queueBatchSize = params.resourceMapping[resourceName].queueBatchSize
    # Get parallel count for this resource type
    parallelCount = PARALLEL_CONFIG[resourceName] or PARALLEL_CONFIG.default
    # Get more records for parallel processing
    records = await queue.getNextBatch(queueBatchSize * parallelCount)
    if records?.length > 0
      # Split records into parallelCount chunks
      chunkSize = Math.ceil(records.length / parallelCount)
      recordChunks = []
      for i in [0...parallelCount]
        start = i * chunkSize
        end = Math.min(start + chunkSize, records.length)
        if start < records.length
          recordChunks.push(records.slice(start, end))

      # Process chunks in parallel
      results = await Promise.all(recordChunks.map((chunk) ->
        getAndSaveFunc({
          resourceName
          params
          mode: MODE_QUEUE
          queue
          records: chunk
          queryLog
          downloadName
        })
      ))

      # Combine results
      for [count, _] in results
        updatedCount += count
      totalUpdatedCount += updatedCount
      gSpeedMeter.check {"queue_#{resourceName}":updatedCount}
    #save queryLog or sleep
    if records?.length > 0
      await params.coll.eventLog.updateOne \
        {_id: "queue-#{resourceName}-#{params.trebType}-#{startTs.toISOString()}"},
        {$set: {
          startTs,
          endTs: new Date(),
          logs: queryLog.logs,
          error: curError?.toString() or 'none',
          lastUpdatedCount: updatedCount,
          updatedTotal: totalUpdatedCount,
        }},{upsert: true}
        if queryLog.logs.length > 100
          queryLog = EventLogger.createLogger(debug)
          totalUpdatedCount = 0
          startTs = null
    else # queue is empty
      await sleep(QUEUE_SLEEP_MS)
      startTs = null
      queryLog = EventLogger.createLogger(debug)
      totalUpdatedCount = 0
  return

# A thread to download property and other resoReturn by ModificationTimestamp.
# This is run on routine mode.
# Download resources by ModificationTimestamp for a given resource name
downloadResourcesByMt = (resourceName, queryTs, params, mode, queryLog, queryKey) ->
  if resourceName is RESOURCE_NAME_PROPERTY
    getAndSaveFunc = getAndSavePropertiesByMt
  else
    getAndSaveFunc = getAndSaveResourcesAsync
  startTs = new Date()
  downloadName = "RESO_TREB_#{params.trebType}_#{mode}_#{resourceName}_Download"
  # Check if we have any saved state from previous runs
  logInDB = await getLastResourceRequestInfo(MlsImportKeysCol, resourceName, downloadName)
  # get and save properties or other resources
  [updatedCount, nextQueryTs] = await getAndSaveFunc({
    queryTs:(logInDB?.next or queryTs), 
    resourceName, 
    params, 
    mode, 
    downloadName,
    queryLog})
  gSpeedMeter.check {"routine_#{resourceName}":updatedCount}

  endTs = new Date()
  logObj = {}
  logObj[resourceName] = {
    updatedCount, 
    startTs,
    endTs,
    durationSec: (endTs.getTime() - startTs.getTime()) / 1000
  }
  queryLog.update(queryKey, logObj)
  await updateResourceRequestLog({
    MlsImportKeysCol,
    nextTs: nextQueryTs,
    resourceName,
    count: updatedCount,
    done: false,
    downloadName
  })
  return [updatedCount, nextQueryTs]

# A thread to download property and other resoReturn by ModificationTimestamp.
# This is run on routine mode.
mainLoop = (queryTs, params) ->
  resourceNames = [RESOURCE_NAME_PROPERTY].concat(params.needGetByMtInMainLoop)
  queryLog = EventLogger.createLogger(debug)
  queryKey = "mainLoop-#{params.trebType}"
  mode = MODE_ROUTINE
  while true
    # if fatal error, exit the loop
    return if gFatalError
    startTs = new Date()
    curError = null
    totalUpdatedCount = 0
    try
      queryLog.begin(queryKey, {queryTs})
      minNextQueryTs = new Date() # nextQueryTs shall be after queryTs and the min of all nextQueryTs
      # get property and other resources by ModificationTimestamp
      for resourceName in resourceNames
        [updatedCount, nextQueryTs] = await downloadResourcesByMt(resourceName, queryTs, params, mode, queryLog, queryKey)
        totalUpdatedCount += updatedCount
        minNextQueryTs = Math.min(minNextQueryTs, new Date(nextQueryTs))

      set = {}
      set[params.trebType + 'RoutineNext'] = new Date(nextQueryTs)
      set['status'] = RUNNING_NORMAL
      # IMPORT_KEY_ID record the local time of last run
      await MlsImportKeysCol.updateOne { _id: IMPORT_KEY_ID },
        { $set: set, $unset:{error:1}},
      { upsert: true }
      await handleProcessStatus {
        status: RUNNING_NORMAL,
        nextTs: (Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }
      queryLog.end(queryKey, {endQueryTs:queryTs, endNextQueryTs: nextQueryTs})
      queryTs = minNextQueryTs
    catch error
      curError = error
      debug.error error
      # TODO: different thread shall have different error handling
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: (Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }
    #save queryLog
    await params.coll.eventLog.updateOne \
      {_id: "routine-mainLoop-#{params.trebType}-#{startTs.toISOString()}"},
      {$set: {
        startTs,
        endTs: new Date(),
        status: 'Finished',
        logs: queryLog.logs,
        error: curError?.toString() or 'none',
        lastUpdatedCount: updatedCount,
        updatedTotal: totalUpdatedCount,
      }},{upsert: true}
    queryLog = EventLogger.createLogger(debug)
    sleepMs = startTs.getTime() + ROUTINE_SLEEP_MS - Date.now()
    if sleepMs > 0
      debug.info "routine loop sleep #{(sleepMs / 6000)/10.0} min"
      await sleep(sleepMs)


# A thread to download media by ModificationTimestamp.
# This is run on routine mode.
getMediaByMt = (queryTs, params) ->
  queryLog = EventLogger.createLogger(debug)
  queryKey = "MediaByMt-#{params.trebType}"
  debug.debug "getMediaByMt", queryTs
  mode = MODE_ROUTINE
  while true
    # if fatal error, exit the loop
    return if gFatalError
    startTs = new Date()
    curError = null
    totalUpdatedCount = 0
    queryLog.begin(queryKey, {queryTs})
    try
      # get media by ModificationTimestamp
      [updatedCount, nextQueryTs] = await downloadResourcesByMt(RESOURCE_NAME_MEDIA, queryTs, params, mode, queryLog, queryKey)
      totalUpdatedCount += updatedCount
      queryTs = nextQueryTs
    catch error
      curError = error
      debug.error error
      queryLog.update(queryKey, {error:error.toString()})
    queryLog.end(queryKey, {nextQueryTs, updatedCount})
    #save queryLog
    await params.coll.eventLog.updateOne \
      {_id: "routine-mediaByMt-#{params.trebType}-#{startTs.toISOString()}"},
      {$set: {
        startTs,
        endTs: new Date(),
        status: 'Finished',
        logs: queryLog.logs,
        error: curError?.toString() or 'none',
        lastUpdatedCount: updatedCount,
        updatedTotal: totalUpdatedCount,
      }},{upsert: true}
    queryLog = EventLogger.createLogger(debug)
    sleepMs = startTs.getTime() + ROUTINE_MEDIA_SLEEP_MS - Date.now()
    if sleepMs > 0
      debug.info "getMediaByMt loop sleep #{(sleepMs / 6000)/10.0} min"
      await sleep(sleepMs)



callWithRetry = ({func, resourceName, trebType, mode, downloadName, params}) ->
  retryCount = 0
  while true
    try
      return await func()
    catch error
      debug.error "#{resourceName} #{params.trebType} #{mode} on try #{retryCount}", error
      retryCount = await handleResourceError({
        error,
        resourceName,
        trebType: params.trebType,
        mode,
        retryCount,
        downloadName
      })


initializeImportKey = (now) ->
  importKey = await MlsImportKeysCol.findOne { _id: IMPORT_KEY_ID }
  if not importKey
    importKey = {
      ts: now,
      evowRoutineNext: null,
      evowInitDoneTs: null,
      evowInitDone:false,
      idxRoutineNext: null,
      idxInitDoneTs: null,
      idxInitDone:false
    }
    await MlsImportKeysCol.updateOne { _id: IMPORT_KEY_ID },
      { $set: importKey},
      { upsert: true }
  return importKey


getTrebTypes = () ->
  if (not gDownloadIdx) and (not gDownloadEvow)
    throw new Error('no treb type to download')
  trebTypes = []
  if gDownloadIdx
    trebTypes.push('idx')
  if gDownloadEvow
    trebTypes.push('evow')
  debug.info "trebTypes", trebTypes
  return trebTypes


initializeCollectionsAndGenerateParams = (trebType) ->
  collections = initializeCollections(trebType)
  params = await generateParams(trebType, collections)
  return params
  
# initialize collections for a treb type.
# We keep index creation right under the collection creation, so we don't miss any index.
# We keep related things together, so we can find them easily.
initializeCollections = (trebType) ->
  collections = {}

  # Log collection
  collections.log = COLLECTION 'rni', "reso_treb_#{trebType}_logs"
  collections.log.createIndex { id:1, mt: 1 }, { background: true }
  collections.log.createIndex { id:1, _mt: -1 }, { background: true }
  collections.log.createIndex { id:1, hash:-1 }, { background: true }
  collections.log.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }

  # Dup log collection
  collections.dup = COLLECTION 'rni', "reso_treb_#{trebType}_dup_logs"
  collections.dup.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }

  # Raw collection
  collections.raw = COLLECTION 'rni', "reso_treb_#{trebType}_raw"
  collections.raw.createIndex { _mt: 1 }, { background: true }
  collections.raw.createIndex { ListingKey: 1 }, { background: true }

  # Merged collection
  collections.merged = COLLECTION 'rni', "reso_treb_#{trebType}_merged"
  collections.merged.createIndex { _mt: 1 }, { background: true }
  collections.merged.createIndex { ListingKey: 1 }, { background: true }

  # Resource collections
  collections.openHouse = COLLECTION 'rni', "reso_treb_#{trebType}_openhouse"
  collections.openHouse.createIndex { ListingKey: 1 }, { background: true }
  collections.propertyRoom = COLLECTION 'rni', "reso_treb_#{trebType}_propertyRoom"
  collections.propertyRoom.createIndex { ListingKey: 1 }, { background: true }
  collections.media = COLLECTION 'rni', "reso_treb_#{trebType}_media"
  collections.media.createIndex { RRK: 1 }, { background: true }

  # Other collections
  collections.field = COLLECTION 'rni', "reso_treb_#{trebType}_field"
  collections.lookup = COLLECTION 'rni', "reso_treb_#{trebType}_lookup"
  collections.office = COLLECTION 'rni', "reso_treb_#{trebType}_office"

  # Event log collection
  collections.eventLog = COLLECTION 'rni', 'reso_treb_event_log'
  collections.eventLog.createIndex { _mt:-1 }, { expireAfterSeconds:THREE_MONTHS_SECONDS, background:true }
  collections.eventLog.createIndex { id:1, _mt: -1 }, { background: true }
  
  # Media need compress collection
  collections.mediaCompress = COLLECTION 'rni', "reso_treb_#{trebType}_media_compress"

  # Resource updated by ModificationTimestamp collection
  collections.resourceUpdatedByMt = COLLECTION 'rni', "reso_treb_#{trebType}_resource_updated_by_mt"
  collections.resourceUpdatedByMt.createIndex { id: 1, resourceType: 1 }, { background: true, unique: true }
  return collections

#TODO: unify the queue/queueCol name
initializeQueues = (trebType, queuedResources) ->
  queueCols = {}
  for resourceType in queuedResources
    try
      queueCol = COLLECTION 'rni', "reso_treb_#{trebType}_#{resourceType}_queue"
      queueCols[resourceType] = new ResourceDownloadQueue(resourceType, queueCol)
      await queueCols[resourceType].ensureIndexes()
    catch err
      debug.error "Error creating indexes for #{resourceType} queue:", err
      throw err
  return queueCols


generateParams = (trebType, collections) ->
  # TODO: use mapping QUEUED_RESOURCES_#{trebType}
  queuedResources = if trebType is 'evow' then QUEUED_RESOURCES_EVOW else QUEUED_RESOURCES_IDX
  queueCols = await initializeQueues(trebType, queuedResources)
  {
    trebType: trebType
    resoClient: if trebType is 'evow' then TrbResoEvowClient else TrbResoIdxClient
    coll: collections
    resourceMapping: RESOURCE_MAPPING[trebType]
    needMerge: if trebType is 'evow' then RESOURCE_TO_BE_MERGED_EVOW else RESOURCE_TO_BE_MERGED_IDX
    needGetByMtInMainLoop: if trebType is 'evow' then RESOURCE_NEED_GET_BY_MT_IN_MAIN_LOOP_EVOW else RESOURCE_NEED_GET_BY_MT_IN_MAIN_LOOP_IDX
    queuedResources: queuedResources
    # TODO: 统一命名，统一缩写
    queues: queueCols
  }


main = () ->
  isForce = AVGS.includes('force')
  gGetPhotos = AVGS.includes('photo-download')
  if gGetPhotos
    return EXIT 'photo-download not supported now'
  now = new Date()
  try
    hasRunningProcess = await checkRunningProcess()
    if (not isForce) and hasRunningProcess
      return EXIT HAS_RUNNING_PROCESS
    importKey = await initializeImportKey(now)
    processNextTs = Date.now() + AVG_RUNNING_MS
    await handleProcessStatus {
      status: RUNNING_NORMAL,
      startTs: Date.now(),
      nextTs: processNextTs
    }
  catch error
    debug.error error
    return gracefulExit error
  # init and main loop
  try
    trebTypes = getTrebTypes()
    for trebType in trebTypes
      if not importKey[trebType + 'RoutineNext']
        queryTs = now
        set = {}
        set[trebType + 'RoutineNext'] = now
        set[trebType + 'InitDoneTs'] = now
        set['status'] = RUNNING_NORMAL
        await MlsImportKeysCol.updateOne { _id: IMPORT_KEY_ID },
          { $set: set, $unset:{error:1}},
          { upsert: true }
      else
        queryTs = importKey[trebType + 'RoutineNext']
      params = await initializeCollectionsAndGenerateParams(trebType)
      debug.debug "params", params
      # if not init done, then init 
      if (not importKey?[trebType + 'InitDone'])
        queryTsInit = importKey[trebType + 'InitDoneTs'] or now
        initialQueryBack(queryTsInit, params)
      # run queue
      for resourceType in params.queuedResources
        downloadQueue(resourceType, params)
      # download no merge resources
      await downloadNoMergeResources(queryTs, params)
      # do routine
      queryTsRoutine = importKey[trebType + 'RoutineNext'] or now
      getMediaByMt(queryTsRoutine, params)
      await mainLoop(queryTsRoutine,params) # not support run idx and evow together
  catch error
    debug.error 'catched error in main', error
    return gracefulExit error
  if gFatalError
    debug.critical 'catched fatal error in main', gFatalError
    return gracefulExit gFatalError
  # program shall not reach here, but we still give it a final exit code
  return EXIT 0
main()

