###
Import data from source database (main server) to target database (backup server)

Options:
-d --database: [required] update data from last update, default is 2017-01-01
-h --hostname: custom hostname, default hostname in connection string
-c --collection: source collection, it can be crea or treb, default crea
-v --verbose: verbose mode.

Usage:          ./start.sh lib/batchBase.coffee mlsImport/mlsImport.coffee -d "******************************************************" -c crea
Author:         Louis
Run duration:   408.098ms for 62 records
Run frequency:  depends
Created:        2021/03/16


Data schema:
same running status records to sysdata:
  _id: [hostname]@mlsImport_[crea|treb]
  lastMt: the last mt of processed record.
#region
mls_crea_ddf_ids:{
  _id:           _id without prefix DDF
  lastUpdated:   mt
  synced:        mt
  laTs:          batch machine time.
  mt:            batch machine time.
  ts:            batch machine time.
  phoMt:         most current time from Photo.PropertyPhoto[].LastUpdated and Photo.PropertyPhoto[].PhotoLastUpdated
  phoDl:         PhotoDlDate
  maxPicSz:      untouch
  avgPicSz:      untouch
  picNum:        untouch
}

mls_crea_ddf_records:{
  _id:           origianl _id with prefix DDF
  laTs:          batch machine time.
  mt:            batch machine time.
  ts:            batch machine time.
  phoMt:         most current time from Photo.PropertyPhoto[].LastUpdated and Photo.PropertyPhoto[].PhotoLastUpdated
  phoDl:         PhotoDlDate
  maxPicSz:      untouch
  avgPicSz:      untouch
  picNum:        Photo.PropertyPhoto.length
  ...raw data
}

mls_treb_master_ids:{
  _id:           with prefix TREB
  src:           trbtp
  lastUpdated:   mt or Timestamp_sql
  synced:        equal to lastUpdated.
  laTs:          batch machine time.
  mt:            batch machine time.
  ts:            batch machine time.
  phoMt:         Pix_updt
  phoDl:         phodl
  maxPicSz:      untouch
  avgPicSz:      untouch
  picNum:        pic_num
}

mls_treb_master_records collection:{
  _id:           with prefix TREB
  src:           trbtp
  lSrc:          Lsc
  ...raw data
}
#endregion
###
mongodb = require('mongodb')

sysDataHelper = require '../lib/sysDataHelper.coffee'

MongoClient = mongodb.MongoClient

yargs = require('yargs') # eslint-disable-line
yargs
  .option 'database', { alias: 'd', description: 'target database connection string',\
    required: true}
  .option 'collection', { alias: 'c', description: 'source collection',\
    required: true}
  .option 'hostname', { alias: 'h', description: 'source hostname'}
  .option 'verbose', { alias: 'v', description: 'verbose mode'}
  .help('help')

argv = yargs.argv


{processKillSignal} = require '../libapp/processHelper'

processKillSignal 'mlsImport',EXIT



helpers = INCLUDE 'lib.helpers'

# source database
SysData = null
CreaSource = COLLECTION 'vow', 'mls_crea_ddf_records'
TrebSource = COLLECTION 'vow', 'mls_treb_records'
CreaKeySource = COLLECTION 'vow', 'mls_crea_keys'
TrebKeySource = COLLECTION 'vow', 'mls_treb_keys'

# Global

# batch mechine time
gCurrentTime = new Date()
# option for mongodb find query
DATE_19700101 = new Date('1970-01-01T00:00:00.000Z')
gLastUpdateTime = DATE_19700101

# Target collection names
CREA_IDS = 'mls_crea_ddf_ids'
CREA_RECORDS = 'mls_crea_ddf_records'
CREA_KEYS = 'mls_import_keys'
TREB_IDS = 'mls_treb_master_ids'
TREB_RECORDS = 'mls_treb_master_records'
TREB_KEYS = 'mls_import_keys'


getHostname = (connectionString)->
  hostRegex = new RegExp(/(?<=\/\/)(.*?)(?=\/)/g)
  return connectionString.match hostRegex


# varuable init
verbose = if argv.verbose then 3 else 1
hostname = if argv.hostname then argv.hostname else getHostname argv.database
collection = if argv.collection in ['crea','treb'] then argv.collection else 'crea'
debug = DEBUG verbose
# Source sysData id
SYS_DATA_ID = "#{hostname}@mlsImport_#{collection}"
# Check point length
CHECK_POINT = 10
# Insert counts
gInsertCounts = 0


#region: ulti function
dbErrorHandler = ({error=null,successInfo=null,successVerbose=null})->
  if error
    debug.error error
    return EXIT 1,error
  debug.info successInfo if successInfo?
  debug.verbose successVerbose if successVerbose?

# To format Photo.PropertyPhoto.LastUpdated into ISO compatible
# input 28/10/2016 08:32:44 PM
# output 2016-10-28 08:32:44 PM GMT
timeStringFormatter = (string)->
  [date,time,period] = string.split(' ')
  [day,month,year] = date.split('/')
  return "#{year}-#{month}-#{day} #{time} #{period} GMT"


###
Pick most current date from array of IOSDate,
if input is invalid return 1970-01-01T00:00:00.000Z,
Params:
  ISODates: IOSDate[]
###
sortTime = (ISODates)->
  if ISODates.length is 0
    return null
  lastest = ISODates.map (date)->
    if (parsedDate = Date.parse(date)) is 'NaN' then 0 else parsedDate
  .sort (a,b)->b-a
  return lastest

#endregion

#region: data formatter, only copy active listing to master_id.
handleTrebData = (data,type)->
  temp = {
    # lSrc: data.Lsc or null
    phoMt: new Date(data.Pix_updt),
    phoDl: new Date(data.phodl)
  }
  if data.trbtp
    temp.src = data.trbtp
  if data.pic_num
    temp.picNum = data.pic_num
  if type is 'treb-ids'
    temp._id = data._id.replace /^TRB/,''
    temp.lastUpdated = temp.synced = new Date(data.Timestamp_sql)
    temp.laTs = gCurrentTime
    temp.mt = data.mt if data.mt
    temp.ts = data.ts if data.ts
  else if type is 'treb-raw'
    temp._id = data._id
    Object.assign temp,data
  else
    throw new Error("unknown type: #{type}")
  # delete temp.data._id
  return temp


ONE_HOUR_MS_1_MINUS = 3600000 - 1000
ONE_HOUR_MS_1_PLUS = 3600000 + 1000

handleCreaData = (data,type)->
  temp = {}
  if phoDl = data.PhotoDlDate then temp.phoDl = new Date(phoDl)
  if propertyPhoto = data.Photo?.PropertyPhoto
    photoUpdateTimes = []
    if (Array.isArray propertyPhoto) and propertyPhoto.length > 0
      photoUpdateTimes = data.Photo.PropertyPhoto.reduce((acc,curr)->
        if curr.LastUpdated
          acc.push timeStringFormatter(curr.LastUpdated)
        if curr.PhotoLastUpdated
          acc.push curr.PhotoLastUpdated
        acc
      ,[])
      temp.picNum = propertyPhoto.length
    else if (typeof propertyPhoto is 'object')
      if propertyPhoto.SequenceId?
        temp.picNum = 1
      if propertyPhoto.LastUpdated
        photoUpdateTimes.push photoUpdateTimes
      if propertyPhoto.PhotoLastUpdated
        photoUpdateTimes.push propertyPhoto.PhotoLastUpdated
    photoUpdateTimes = sortTime(photoUpdateTimes)
    if photoUpdateTimes.length
      temp.phoMt = new Date(photoUpdateTimes[0])
      temp.picTs = new Date(photoUpdateTimes[photoUpdateTimes.length-1])
  # in many cases phoDl is one hour less than phoMt, it was caused by string with timezone miscalculation.
  if temp.phoDl and temp.phoMt and \
  ONE_HOUR_MS_1_MINUS <= (temp.phoMt - temp.phoDl) <= ONE_HOUR_MS_1_PLUS
    temp.phoDl = temp.phoMt
  # make mt a Date object for crea
  if type is 'crea-ids'
    temp._id = data._id.replace(/^DDF/,'')
    temp.lastUpdated = new Date(data['@attributes']?.LastUpdated)
    temp.synced = temp.lastUpdated
    temp.laTs = gCurrentTime
    temp.ts = data.ts if data.ts
  else if type is 'crea-raw'
    temp = Object.assign temp,data
  else
    throw new Error("unknown type: #{type}")
  return temp

#endregion


MainMap = {
  crea:{
    source: CreaSource,
    keySource: CreaKeySource,
    ids: {
      name: CREA_IDS
      collection:null,
      type:'crea-ids',
    },
    records:{
      name: CREA_RECORDS
      collection:null,
      type:'crea-raw'
    },
    keys: {
      name: CREA_KEYS
      collection:null,
      maps:
        CreaDDFDownload:'CreaDDFDownload'
    },
    formatter:handleCreaData,
  }
  treb:{
    source: TrebSource,
    keySource: TrebKeySource,
    ids: {
      name: TREB_IDS
      collection:null,
      type:'treb-ids',
    },
    records:{
      name: TREB_RECORDS
      collection:null,
      type:'treb-raw'
    },
    keys: {
      name: TREB_KEYS
      collection:null,
      maps:
        Download_TREB_IDX: 'TrebIDXDownload'
        Download_TREB_AU_VOW: 'TrebEVOWDownload'
        Download_TREB_VOW: 'TrebVOWDownload'
    },
    formatter:handleTrebData
  }
}


# Check endTs and store it into gLastUpdateTime
checkLastUpdateTime = (cb)->
  sysDataHelper.checkSysdataAndSetStartTs SysData,\
    {isBatch: true, batchNm:'mlsImport',id:SYS_DATA_ID,force:true},(err,result)->
      dbErrorHandler({error:err})
      if not result
        throw new Error('no sysdata result')
      # no sysData found (only _id in result): Use default gLastUpdateTime
      # last update successed: Update gLastUpdateTime to result.lastMt
      if result.status is 'complete'
        gLastUpdateTime = result.lastMt
      # continue from last update: set gLastUpdateTime to result.startTs, and add skip
      else if result.status is 'running' # running 表示上次没跑完，不能用startTs
        gLastUpdateTime = result.lastMt or gLastUpdateTime
      else if result._id and Object.keys(result).length is 1
        debug.info 'New sysdata created'
      else
        throw new Error("unknown SysData status: #{result.status}")
      debug.info 'lastUpdated timestamp',gLastUpdateTime
      cb()

# Connect to targret database and set collections into Maps
connectToTarget = (map,connectionString,cb)->
  MongoClient.connect connectionString, {},(err,client)->
    dbErrorHandler({error:err,successInfo:'Connecting import db:',connectionString})
    targetDb = client.db()
    dbName = targetDb.databaseName
    debug.info "Target database #{dbName} connected successfully"
    # update Maps
    map.ids.collection = targetDb.collection map.ids.name
    map.records.collection = targetDb.collection map.records.name
    map.keys.collection = targetDb.collection map.keys.name
    SysData = targetDb.collection 'sysdata'
    return cb()

inserToTarget = (map,type,data,cb)->
  # check collection and cb
  if (map[type].collection is null) or (typeof cb isnt 'function')
    debug.error 'Target collection is null or no callback function'
    return EXIT 2
  # check data
  if not data?._id
    debug.warn "#{type} skipped, _id not found #{data}"
    return cb()
  if /ids/.test type
    return cb() if data.status is 'U'

  temp = map.formatter(data,map[type].type)
  #if is id, only insert active ids

  map[type].collection.updateOne {_id:temp._id}, {$set:temp}, {upsert:true},(err,result)->
    dbErrorHandler({error:err,successVerbose:"#{map[type].type} \
    #{temp._id} inserted"})
    updateCheckPoints type,data.mt,->
      return cb()

updateCheckPoints = (type,origianlMt,cb)->
  if type isnt 'ids'
    return cb()
  else
    gInsertCounts++
  
  if gInsertCounts % CHECK_POINT isnt 0
    return cb()
  SysData.updateOne {_id:SYS_DATA_ID},
  {$set:{lastMt:origianlMt}} ,(err,result)->
    dbErrorHandler({error:err})
    return cb()

getLocaleStringOfTime = (ts)->
  opt = {
    timeZoneName:'short'
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second:'2-digit'
  }
  return ts.toLocaleString('sv',opt).substr(0,19)

  
importFromSource = (collectionMap, cb)->
  # check source
  if collectionMap.source is null
    debug.error 'Source collection is null'
    return EXIT 2
  collectionMap.source.createIndex {mt:1},(err)->
    if err
      debug.error 'create index',err if err
      return EXIT 3
    if collection is 'treb'
      if gLastUpdateTime is DATE_19700101
        query = {}
      else
        query = $or:[
          {$and:[{mt:{$type:'date'}},{mt:{$gt:new Date(gLastUpdateTime)}}]},
          {$and:[ # treb has 30k string mt
            {mt:{$type:'string'}},
            {mt:{$gt:getLocaleStringOfTime gLastUpdateTime}}
            ]},
          ]
    else if collection is 'crea'
      if gLastUpdateTime is DATE_19700101
        query = {}
      else
        query = {mt:{$gt:getLocaleStringOfTime gLastUpdateTime}}
    else
      debug.error 'unknow source type',collection
      return EXIT 4,('unknow source type '+collection)
    debug.info 'query',JSON.stringify query
    collectionMap.source.find query,{sort:{mt:1}},(err,records)->
      debug.info "importing #{collection} from #{gLastUpdateTime}"
      dbErrorHandler({error:err})
      stream = records.stream()
      streamConfig =
        verbose: 1
        high:1
        stream: stream
        end: (err)->
          dbErrorHandler({error:err})
          return cb()
        process: (obj,callback)->
          # debug.debug obj
          obj.mt = new Date(obj.mt) if 'string' is typeof obj.mt
          inserToTarget collectionMap,'records',obj,->
            return callback() if obj.status is 'U'
            inserToTarget collectionMap,'ids',obj,->
              callback()
      helpers.streaming streamConfig

updateKeys = (collectionMap,cb)->
  todoMap = collectionMap.keys.maps
  todoKeys = Object.keys todoMap
  doOne = ->
    if key = todoKeys.pop()
      collectionMap.keySource.findOne {_id:key},(err,data)->
        return doOne() unless data
        delete data._id
        data.next = new Date(data.next)
        data.done = new Date(data.last)
        collectionMap.keys.collection.updateOne {_id:todoMap[key]},\
        {$set:data},{upsert:true},(err)->
          dbErrorHandler {error:err,successInfo:"Updated #{key}"}
          doOne()
    else
      cb()
  doOne()

finishUpdate = ()->
  #Update endTs
  SysData.updateOne {_id:SYS_DATA_ID},\
  {$set:{endTs:gCurrentTime,status:'complete'}} ,(err,result)->
    dbErrorHandler({error:err,successInfo:'update finished'})
    console.timeEnd('import from database')
    return EXIT 0

main = (map)->
  connectToTarget map,argv.database, ->
    checkLastUpdateTime ->
      importFromSource map,->
        updateKeys map,->
          finishUpdate()
          # EXIT 0


if argv.database
  console.time('import from database')
  debug.info "import from database #{collection}"
  main(MainMap[collection])