###
* Description: Fetch CREA listings from RESO API
* Command: ./start.sh -t batch -n creaResoDownload -cmd 'lib/batchBase.coffee mlsImport/creaReso/creaResoDownload.coffee [modes]'
* Author: Maggie
* Run duration: forever
* Run frequency: forever
* Params: 
*   init: Download all historical data
*   routine: Download new/updated data continuously
* Examples:
*   Run init only: ... init
*   Run routine only: ... routine  
*   Run both: ... init routine
###

debug = DEBUG()
config = CONFIG(['creaReso'])
EventLogger = INCLUDE 'lib.eventLog'
{ getHash } = require '../../lib/helpers_string'
{ sleep } = require '../../lib/helpers_function'
SpeedMeter = require '../../lib/speed'
impCommon = require '../../libapp/impCommon'
{ ignorableErrorReso, processPropertyMedia, compressMediaRoom } = impCommon
filterEmptyFieldsNSaveStats = impCommon.getEmptyFilterNStatsFunction()
{ createCreaResoClient } = require('../../libapp/reso')
ResourceDownloadQueue = require '../../libapp/mlsResourceDownloadQueue'
{
  processKillSignal,
  RUNNING_NORMAL,
  RUNNING_ERROR,
  RUNNING_WARNING,
  FINISH_WITHOUT_ERROR,
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = INCLUDE('libapp.processHelper')

# Add global variables at the top after imports
gCollections = null
gQueues = null
gClient = null
gFatalError = null
ProcessStatusCol = COLLECTION 'vow','processStatus'
MlsImportKeysCol = COLLECTION 'rni','mls_import_keys'
FieldStatsCol = COLLECTION 'rni','mls_field_stats'

# Constants
ONE_YEAR_SECONDS = 365*24*3600
THIRTY_DAYS_SECONDS = 30*24*3600
THREE_MONTHS_SECONDS = 90*24*3600
FETCH_TIMEOUT = 1000 * 60 * 10 # 10 minutes
QUEUE_SLEEP_MS = 1000 * 60 * 5 # 5 minutes
ROUTINE_SLEEP_MS = 1000 * 60 * 5 # 5 minutes
AVG_RUNNING_MS = 1000 * 60 * 30 # 30 minutes
QUEUE_BATCH_SIZE = 50
MAX_RECORDS_PER_PAGE = 100
MAX_RECORDS_PER_PAGE_REPLICATION = 100
QUERY_WAIT_TIME = 2000 # 2 seconds between queries
NEW_PROP_PRIORITY = 20000
LOG_BATCH_SIZE = 100
RETRY_INTERVALS = [5, 10, 20, 60, 120, 240] # Minutes
CREA_PREFIX = 'DDF'
MLS_ID_FIELD = 'ListingKey'
LAST_MT_FIELD = 'ModificationTimestamp'
OPENHOUSE_COLLECTION_INTERVAL = 24 * 60 * 60 * 1000 # 24 hours
gOpenHouseDict = {}
ACTIVE_PROP_CHECK_INTERVAL = 24 * 60 * 60 * 1000 # 24 hours
ACTIVE_PROP_CHECK_HOUR = 2 # 2 AM
OPENHOUSE_CHECK_INTERVAL = 24 * 60 * 60 * 1000 # 24 hours
OPENHOUSE_CHECK_HOUR = 0 # 0 AM
U_CNT_THRESHOLD = 3
# Mode constants
MODE_INIT = 'init'
MODE_ROUTINE = 'routine'
MODE_INITIAL = 'initial'
MODE_QUEUE = 'queue'
MODE_DAILY = 'daily'
# Resource names
RESOURCE_NAME_PROPERTY = 'property'
RESOURCE_NAME_MEMBER = 'member'
RESOURCE_NAME_OFFICE = 'office'
RESOURCE_NAME_OPENHOUSE = 'openhouse'

# Process IDs
PROCESS_STATUS_ID = (mode) -> "creaResoDownload_#{mode}"
IMPORT_KEY_ID = (resourceName, mode) -> "creaResoDownload_#{resourceName}_#{mode}"

# Add key field mapping
RESOURCE_KEY_FIELDS = {
  [RESOURCE_NAME_MEMBER]: 'MemberKey'
  [RESOURCE_NAME_OFFICE]: 'OfficeKey'
  [RESOURCE_NAME_OPENHOUSE]: 'OpenHouseKey'
  [RESOURCE_NAME_PROPERTY]: 'ListingKey'
}

RESOURCE_LINK_FIELDS = {
  [RESOURCE_NAME_MEMBER]: 'MemberKey'
  [RESOURCE_NAME_OFFICE]: 'OfficeKey'
  [RESOURCE_NAME_OPENHOUSE]: 'ListingKey'
  [RESOURCE_NAME_PROPERTY]: 'ListingKey'
}

savePropertyCounts = 0
gSpeedMeter = SpeedMeter.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}



handleResourceError = ({error, resourceName, mode, retryCount}) ->
  debug.error "handleResourceError", {error, resourceName, mode, retryCount}
  if ignorableErrorReso(error)
    # Just return current retryCount for ignorable errors
    return retryCount
  else
    retryCount++

  if retryCount > RETRY_INTERVALS.length
    debug.critical "Failed after #{retryCount} times for #{resourceName} #{mode}. Exiting loop.", error
    gFatalError = error
    throw error

  sleepTime = RETRY_INTERVALS[retryCount - 1]
  debug.info "Retry attempt #{retryCount} for #{resourceName} #{mode}. Sleeping for #{sleepTime} minutes"
  await sleep(sleepTime * 60 * 1000)
  return retryCount


callWithRetry = ({func, resourceName, mode, params}) ->
  retryCount = 0
  while true
    try
      return await func()
    catch error
      debug.error "#{resourceName} #{mode} on try #{retryCount}", error
      retryCount = await handleResourceError({
        error,
        resourceName,
        mode,
        retryCount,
      })

###*
# Check if initial downloads are completed
###
checkInitialDownloadsCompleted = ->
  completed = true
  for resourceName in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE]
    importKey = await MlsImportKeysCol.findOne {_id: IMPORT_KEY_ID(resourceName, MODE_INITIAL)}
    if not importKey?.initDone
      completed = false
      break
  return completed

###*
# Mark initial download completed for a resource
###
markInitialDownloadCompleted = (resourceName) ->
  await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(resourceName, MODE_INITIAL)},
    {$set: {
      initDone: true
      initDoneTs: new Date()
    },$unset:{error:1}},
    {upsert: true}

###*
# Get resource type from resource name
# @param {String} resourceName Resource type name
# @returns {String} CREA RESO resource type
###
getResourceType = (resourceName) ->
  switch resourceName
    when RESOURCE_NAME_PROPERTY then gClient.resourceTypes.PROPERTY
    when RESOURCE_NAME_MEMBER then gClient.resourceTypes.MEMBER
    when RESOURCE_NAME_OFFICE then gClient.resourceTypes.OFFICE
    when RESOURCE_NAME_OPENHOUSE then gClient.resourceTypes.OPEN_HOUSE
    else throw new Error("Invalid resource name: #{resourceName}")

###*
# Download full resource data
# @param {Object} params Download parameters
# @param {String} params.resourceName Resource type
# @param {Object} params.collections Collection references
# @param {Object} params.queryLog Query logger
# @param {Date} [params.queryTs] Optional timestamp for filtering
# @param {Date} [params.initDoneTs] Optional timestamp for init done
# @param {String} [params.mode] Operation mode
# @returns {Array} [updatedTotal, lastTs] Total records updated and last timestamp
###
downloadResourceReplication = (params) ->
  {resourceName, queryLog, queryTs, mode, initDoneTs} = params
  debug.info "Downloading full #{resourceName} data ..."
  
  resourceType = getResourceType(resourceName)
  keyField = RESOURCE_KEY_FIELDS[resourceName]
  if not keyField
    throw new Error("No key field defined for resource: #{resourceName}")

  resoParams = {
    $top: MAX_RECORDS_PER_PAGE_REPLICATION
    $count: true
    $orderby: "ModificationTimestamp asc"
  }

  # Build filter based on timestamps
  if queryTs?
    filterParts = []
    if initDoneTs?
      filterParts.push "ModificationTimestamp gt #{initDoneTs.toISOString()}"
    filterParts.push "ModificationTimestamp lt #{queryTs.toISOString()}"
    resoParams.$filter = filterParts.join(' and ')

  logKey = "get #{resourceName} replication"
  updatedTotal = 0
  lastTs = queryTs
  isDone = false
  nextLink = null
  startTs = new Date()
  debug.info logKey, {resoParams, queryTs, initDoneTs}
  queryLog.begin logKey, {startTs, resoParams, queryTs, initDoneTs}

  # replication first query shall be done outside of loop
  response = await callWithRetry({
    func: -> gClient.getResourceAsync(
      resourceType,
      resoParams,
      FETCH_TIMEOUT
    )
    resourceName,
    mode,
    params: {resoParams}
  })

  while not isDone
    return if gFatalError

    updatedCount = 0
    startTs = new Date()
    queryLog.begin logKey, {startTs,resoParams, queryTs, nextLink}

    try
      # Reserve nextLink for next page
      nextLink = response?['@odata.nextLink']
      
      # Check if we're done based on count
      returnedCount = response?["@odata.count"]
      if (returnedCount is 0) or (not response?.value?.length)
        debug.info "no records returned, skip #{resourceName} #{mode}", {
          returnedCount,
          nextLink,
          valueLength: response.value?.length,
        }
        queryLog.end logKey, {
          endTs: new Date(),
          durationMs: new Date().getTime() - startTs.getTime(),
          isDone: true, 
          valueLength: response.value?.length,
          returnedCount,
          nextLink,
        }
        isDone = true
        break

      for resource in response.value
        if resourceName is RESOURCE_NAME_PROPERTY
          await saveProperty(resource, MODE_INIT)
        else
          await gCollections[resourceName].updateOne(
            {_id: resource[keyField]},
            {$set: resource, $setOnInsert: {ts: new Date()}}, 
            {upsert: true})
          gSpeedMeter.check {"initial_#{resourceName}": 1}
        
        # Track the last timestamp
        resourceTs = new Date(resource[LAST_MT_FIELD])
        if (not lastTs?) or (resourceTs > lastTs)
          lastTs = resourceTs

        updatedCount++
      updatedTotal += updatedCount

      durationMs = Date.now() - startTs.getTime()
      queryLog.end logKey, {
        endTs: new Date(),
        durationMs,
        returnedCount,
        updatedTotal,
        updatedCount,
        lastTs,
      }

      if resourceName is RESOURCE_NAME_PROPERTY
        # Update import key with new timestamp
        await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(resourceName, mode)},
        {$set: {
          initDoneTs: lastTs
          status: RUNNING_NORMAL
          lastUpdated: new Date()
          updatedTotal,
          updatedCount,
          durationMs
        },$unset:{error:1}},
        {upsert: true}

      # Sleep for a short time to avoid rate limiting
      if durationMs < QUERY_WAIT_TIME
        await sleep(QUERY_WAIT_TIME - durationMs)

      # Get next page using nextLink
      if nextLink
        response = await callWithRetry({
          func: -> gClient.getResourceByNextLinkAsync(nextLink, FETCH_TIMEOUT)
          resourceName,
          mode,
          params: {resoParams}
        })
      else
        isDone = true
        debug.info "No nextLink, break #{resourceName} #{mode}"
        break
      

    catch error
      debug.error "Error downloading #{resourceName} replication", error
      throw error

  debug.info "Completed replicating #{updatedTotal} #{resourceName} records"
  return [updatedTotal, lastTs]

###*
# Download initial data for a resource
###
downloadInitialResource = (resourceName) ->
  importKey = await MlsImportKeysCol.findOne {_id: IMPORT_KEY_ID(resourceName, MODE_INITIAL)}
  if importKey?.initDone
    debug.info "Initial #{resourceName} data already downloaded"
    return

  debug.info "Downloading initial #{resourceName} data..."
  
  try
    queryLog = EventLogger.createLogger(debug)
    startTs = new Date()
    await downloadResourceReplication({
      resourceName,
      queryLog,
      mode: MODE_INITIAL
    })

    await gCollections.event_log.updateOne(
      {_id: "crea_reso_#{resourceName}_init_#{startTs.toISOString()}"},
      {$set: {
          event: 'downloadInitialResource',
          resourceName,
          mode: MODE_INITIAL,
          queryLog: queryLog.logs,
          startTs,
          endTs: new Date(),
          durationMs: new Date().getTime() - startTs.getTime(),
      }},
      {upsert: true}
    )

    await markInitialDownloadCompleted(resourceName)
    debug.info "Initial #{resourceName} download completed"

  catch error
    debug.error "Error downloading initial #{resourceName} data", error
    throw error
 
###*
# Download all initial data sets
###
downloadInitialData = () ->
  if await checkInitialDownloadsCompleted()
    debug.info "All initial data already downloaded"
    return

  debug.info "Downloading initial data sets..."

  # Download in sequence: members -> offices
  for resourceName in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE]
    await downloadInitialResource(resourceName)


hasPropertyChanged = (property, mt) ->
  # Create copy without modification timestamp for hash calculation
  propertyCopy = Object.assign {}, property
  delete propertyCopy[LAST_MT_FIELD]
  hash = getHash(JSON.stringify(propertyCopy))

  # Check for duplicate by comparing hash with last record
  lastLog = await gCollections.logs.findOne(
    {id: property.ListingKey},
    {projection: {hash: 1, ModificationTimestamp: 1}, sort: {_mt: -1}}
  )

  if hash is lastLog?.hash
    # Update modification timestamp in log if changed
    await gCollections.logs.updateOne(
      {_id: lastLog._id},
      {$set: {ModificationTimestamp: property[LAST_MT_FIELD]}})
    # save to dup log
    foundDup = await gCollections.dup_logs.findOne({id: property.ListingKey, hash: hash})
    if foundDup
      debug.debug "found dup #{property.ListingKey} in dup log, only update mt"
      await gCollections.dup_logs.updateOne({_id: foundDup._id}, {$set: {ModificationTimestamp: property[LAST_MT_FIELD]}})
    else
      compressedData = compressMediaRoom(property)
      await gCollections.dup_logs.insertOne({
        data: compressedData,
        id: property.ListingKey, 
        hash: hash,
        ModificationTimestamp: property[LAST_MT_FIELD]
      })
      debug.debug "insert dup log #{property.ListingKey} #{property[LAST_MT_FIELD]}"
    gSpeedMeter.check {DupLog: 1}
    return false

  # save to log
  await gCollections.logs.insertOne({
    id: property.ListingKey,
    hash: hash,
    sid: property.ListingId,
    data: property,
    mt: mt,
    ModificationTimestamp: property[LAST_MT_FIELD]
  })
  gSpeedMeter.check {Log: 1}
  return true


shouldSkipPropertySave = (property, mt, mode) ->
  return false if mode is MODE_QUEUE
  return not await hasPropertyChanged(property, mt)


###*
# Save property to collections with related data merging
# @param {Object} property Property data
# @param {String} mode Operation mode
###
saveProperty = (property, mode) ->
  # validate required fields
  if not property.ListingKey or not property[LAST_MT_FIELD]
    debug.error "Invalid property data", property
    return property

  # Check if property has changed
  mt = new Date(property[LAST_MT_FIELD])
  if await shouldSkipPropertySave(property, mt, mode)
    # debug.debug "Property #{property.ListingKey} unchanged, skipping save"
    gSpeedMeter.check {LogUnchanged: 1}
    return property

  # Save to raw collection
  property.mt = mt
  id = CREA_PREFIX + property[MLS_ID_FIELD]
  existProp = await gCollections.raw.findOne({_id: id})
  ts = if existProp then existProp.ts else new Date()
  status = if existProp then existProp.status else 'A'
  property.status = status
  
  # Update raw collection if property is new or modified
  if not existProp or existProp.ModificationTimestamp < property.ModificationTimestamp
    await gCollections.raw.replaceOne(
      {_id: id},
      {...property, ts: ts},
      {upsert: true}
    )
    gSpeedMeter.check {Raw: 1}
  else
    gSpeedMeter.check {RawNotNew: 1}
    debug.error "Property raw not new/updated, skipping #{id} #{property[MLS_ID_FIELD]} #{property.ModificationTimestamp}"

  # update merged collection
  await saveToMergedCollection({property, id, ts, mt, mode})

  return property


saveToMergedCollection = ({property, id, ts, mt, mode}) ->
  # Step 1: Filter and retain specified fields
  # Update field statistics
  FIELD_STATS_ID = "RESO_CREA"
  propertyCopyFilterd = await filterEmptyFieldsNSaveStats(
    property, 
    FieldStatsCol,
    FIELD_STATS_ID
  )

  # Step 2: check if merged property is new/updated
  existMergedProp = await gCollections.merged.findOne({_id: id})
  if existMergedProp and existMergedProp.ModificationTimestamp > property.ModificationTimestamp
    debug.debug "Property merged not new/updated, skipping #{id} #{property[MLS_ID_FIELD]} #{property.ModificationTimestamp}"
    gSpeedMeter.check {ExistMergedNew: 1}
    return property

  mergedDoc = {...propertyCopyFilterd, mt: mt, ts: ts}
  if existMergedProp?.UCnt?
    mergedDoc.UCnt = existMergedProp.UCnt
  await mergeResourceData({property, mergedDoc, id, mode})
  gSpeedMeter.check {Merged: 1}
  return property


mergeResourceData = ({property, mergedDoc, id, mode}) ->
  priority = calcPropPriority(property, mode)
  # Helper function to merge office data
  mergeOfficeData = (officeKey, prefix, mode) ->
    return if not officeKey
    office = await gCollections.office.findOne({_id: officeKey})
    if office
      mergedDoc["#{prefix}Office"] = office
      debug.debug "Office merged #{prefix}Office officeKey:#{officeKey} propertyKey:#{property.ListingKey}"#, office
    else
      if mode is MODE_ROUTINE
        debug.error "Office add to queue #{prefix}Office officeKey:#{officeKey} propertyKey:#{property.ListingKey}"
        await gQueues.office.addToQueue({_id: officeKey}, priority)

  # Helper function to merge member data  
  mergeMemberData = (memberKey, prefix, mode) ->
    return if not memberKey
    member = await gCollections.member.findOne({_id: memberKey})
    if member
      mergedDoc["#{prefix}Agent"] = member
      debug.debug "Member merged #{prefix}Agent memberKey:#{memberKey} propertyKey:#{property.ListingKey}"#, member
    else
      if mode is MODE_ROUTINE
        debug.error "Member add to queue #{prefix}Agent memberKey:#{memberKey} propertyKey:#{property.ListingKey} member:#{member}"
        await gQueues.member.addToQueue({_id: memberKey}, priority)

  # Merge office data
  for [key, prefix] in [
    [property.ListOfficeKey, 'List'],
    [property.CoListOfficeKey, 'CoList'],
  ]
    await mergeOfficeData(key, prefix, mode)

  # Merge member data
  for [key, prefix] in [
    [property.ListAgentKey, 'List'],
    [property.CoListAgentKey, 'CoList'],
  ]
    await mergeMemberData(key, prefix, mode)

  fieldsToRemove = {}
  # Merge openhouse data
  if property.ListingKey in Object.keys(gOpenHouseDict)
    mergedDoc.OpenHouses = gOpenHouseDict[property.ListingKey]
    mergedDoc.ohMt = new Date()
    debug.debug "OpenHouse merged #{property.ListingKey} #{mergedDoc.OpenHouses}, ohMt:#{mergedDoc.ohMt}"
  else
    delete mergedDoc.OpenHouses
    delete mergedDoc.ohMt
    fieldsToRemove.OpenHouses = true
    fieldsToRemove.ohMt = true
    debug.debug "OpenHouse not found #{property.ListingKey}"

  ###*
  # Process media (photos and video tours)
  ###
  processMedia = ->
    # Process media and get the processed document and fields to unset
    {processedMedia, fieldsToUnset} = processPropertyMedia(mergedDoc,id)
    # Update mergedDoc with processed version
    mergedDoc = Object.assign {}, processedMedia
    # Update fieldsToRemove with fields that should be unset
    Object.assign(fieldsToRemove, fieldsToUnset)
  # Process all media
  if mergedDoc.Media?
    processMedia()
  else
    debug.info "Media was already processed before for #{id}"

  # Save to merged collection: remove deleted fields and update all fields
  for key in Object.keys(mergedDoc)
    if not mergedDoc[key]?
      fieldsToRemove[key] = true
  update = {$set: mergedDoc}
  if Object.keys(fieldsToRemove).length
    update['$unset'] = fieldsToRemove
  await gCollections.merged.updateOne {_id: id},
    update,
    {upsert: true}

  return property


calcPropPriority = (record,mode) ->
  priority = 0
  status = record.StandardStatus   # [ 'Active','Cancel Protected','Closed','Expired','Pending','Rented','Terminated']
  if mode is MODE_ROUTINE
    priority += 20000
  if /residential/i.test(record.StructureType)
    priority += 10000
  if status is 'Active'
    priority += 1000
  if record.StateOrProvince is 'Ontario'
    priority += 50
  return priority


###*
# Initialize collections with proper indexes
###
initializeCollections = ->
  collections = {}
  # Core collections
  for name in ['logs', 'raw', 'merged', 'dup_logs']
    collections[name] = COLLECTION('rni', "reso_crea_#{name}")
  
  # Resource collections
  for name in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE, RESOURCE_NAME_OPENHOUSE]
    collections[name] = COLLECTION('rni', "reso_crea_#{name}")
    await collections[name].createIndex {"#{RESOURCE_KEY_FIELDS[name]}": 1}, {background: true}

  # Event log collection
  for name in ['event_log']
    collections[name] = COLLECTION('rni', "reso_crea_#{name}")

  # Create indexes
  await collections.logs.createIndex { id:1, mt: 1 }, { background: true }
  await collections.logs.createIndex { id:1, _mt: -1 }, { background: true }
  await collections.logs.createIndex { id:1, hash:-1 }, { background: true }
  await collections.logs.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }

  # Dup log index
  await collections.dup_logs.createIndex { id:1, hash:-1 },{ background: true }
  await collections.dup_logs.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }
  
  # Raw index
  await collections.raw.createIndex { _mt: 1 }, { background: true }
  await collections.raw.createIndex { ListingKey: 1 }, { background: true }
  await collections.raw.createIndex { mt:1, OriginatingSystemName: 1 }, { background:true }

  # Merged index
  await collections.merged.createIndex { _mt: 1 }, { background: true }
  await collections.merged.createIndex { ListingKey: 1 }, { background: true }
  await collections.merged.createIndex { mt:1, OriginatingSystemName: 1 }, { background:true }
  await collections.merged.createIndex { ListingId: 1 }, { background: true }
  await collections.merged.createIndex { ohMt: 1 }, { background: true }

  # Add indexes on merged collection for the lookup fields
  # Member keys
  await collections.merged.createIndex { ListAgentKey: 1 }, { background: true }
  await collections.merged.createIndex { CoListAgentKey: 1 }, { background: true }
  # Office keys
  await collections.merged.createIndex { ListOfficeKey: 1 }, { background: true }
  await collections.merged.createIndex { CoListOfficeKey: 1 }, { background: true }
    
  # Event log index
  await collections.event_log.createIndex { _mt:-1 }, { expireAfterSeconds:THREE_MONTHS_SECONDS, background:true }
  await collections.event_log.createIndex { id:1, _mt: -1 }, { background: true }

  # openhouse ListingKey index
  await collections.openhouse.createIndex { ListingKey: 1 }, { background: true }
  return collections
  

###*
# Initialize queues for each resource type
###
initializeQueues = ->
  queues = {}
  for name in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE, RESOURCE_NAME_PROPERTY]
    queueCol = COLLECTION('rni', "reso_crea_#{name}_queue")
    queues[name] = new ResourceDownloadQueue(name, queueCol)
    await queues[name].ensureIndexes()
  return queues

###*
# Initialize RESO client
###
initializeResoClient = ->
  if not config.creaReso?.user or not config.creaReso?.pass
    throw new Error('Please provide the CREA Reso username and password in the config file.')

  createCreaResoClient({
    user: config.creaReso.user
    pass: config.creaReso.pass
  })

###*
# Initialize import key for a resource and mode
# @param {String} resourceName Resource type
# @param {String} mode Operation mode (init/routine)
# @param {Date} now Current timestamp
###
initializeImportKey = (resourceName, mode, now) ->
  importKeyId = IMPORT_KEY_ID(resourceName, mode)
  importKey = await MlsImportKeysCol.findOne {_id: importKeyId}
  if not importKey
    importKey = {
      ts: now
      nextQueryTs: now
      resourceName
      mode
      status: RUNNING_NORMAL
    }
    await MlsImportKeysCol.updateOne {_id: importKeyId},
      {$set: importKey,$unset:{error:1}},
      {upsert: true}
  return importKey

###*
# Initialize import keys for all resources and modes
###
initializeResourceImportKeys = ({modes, now}) ->
  resourceImportKeys = {}
  
  # For init mode, only initialize property
  if MODE_INIT in modes
    resourceImportKeys[RESOURCE_NAME_PROPERTY] = {}
    resourceImportKeys[RESOURCE_NAME_PROPERTY][MODE_INIT] = await initializeImportKey(RESOURCE_NAME_PROPERTY, MODE_INIT, now)

  # For routine mode, initialize all resources
  if MODE_ROUTINE in modes
    for resourceName in [
      RESOURCE_NAME_PROPERTY,
      RESOURCE_NAME_OPENHOUSE,
      RESOURCE_NAME_MEMBER,
      RESOURCE_NAME_OFFICE,
    ]
      resourceImportKeys[resourceName] ?= {}
      resourceImportKeys[resourceName][MODE_ROUTINE] = await initializeImportKey(resourceName, MODE_ROUTINE, now)

  return resourceImportKeys

###*
# Get process handler for a specific mode
# @param {String} mode Operation mode (init/routine)
# @returns {Object} Process handler functions
###
getProcessHandlerForMode = (mode) ->
  {
    handleProcessStatus,
    handleExit,
    checkRunningProcess
  } = getProcessHandler {
    ProcessStatusCol,
    id: PROCESS_STATUS_ID(mode),
    filePath: BATCH_FILE_NAME,
    exitFn: EXIT
  }
  return {
    handleProcessStatus,
    handleExit,
    checkRunningProcess
  }

###*
# Handle graceful exit for a mode
###
gracefulExit = (error, mode) ->
  if error?
    debug.error error
  else
    debug.log 'gracefulExit'
  try
    await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(RESOURCE_NAME_PROPERTY, mode)},
      {$set:{
        error: error?.toString(),
        status: 'exit'
      }},
      {upsert: true}
  catch err
    debug.error err
  
  {handleExit} = getProcessHandlerForMode(mode)
  handleExit error

# Set up process kill signal handlers for each mode
processKillSignal PROCESS_STATUS_ID(MODE_INIT), ->
  gracefulExit 'process killed by signal', MODE_INIT

processKillSignal PROCESS_STATUS_ID(MODE_ROUTINE), ->
  gracefulExit 'process killed by signal', MODE_ROUTINE

processKillSignal PROCESS_STATUS_ID(MODE_DAILY), ->
  gracefulExit 'process killed by signal', MODE_DAILY
###*
# Validate command line modes
###
validateModes = (modes) ->
  if not modes?.length
    throw new Error("Must specify at least one mode: init and/or routine")
  
  # Filter out only the mode arguments
  validModes = modes.filter((mode) -> mode in [MODE_INIT, MODE_ROUTINE])
  
  # Ensure at least one valid mode is specified
  if not validModes.length
    throw new Error("Must specify at least one mode: init and/or routine")
      
  return validModes # Return only the valid modes for processing

###*
# Download and save resources by CREAModificationTimestamp for routine mode
# @param {String} resourceName Resource type
# @param {Date} queryTs Timestamp to query from
# @param {Object} queryLog Query logger
# @returns {Array} [updatedCount, lastTs] Total records updated and last timestamp
###
getAndSaveResourcesByMt = ({resourceName, queryTs, queryLog}) ->
  resourceType = getResourceType(resourceName)
  keyField = RESOURCE_KEY_FIELDS[resourceName]
  if not keyField
    throw new Error("No key field defined for resource: #{resourceName}")

  queryTs = new Date(queryTs) if typeof queryTs is 'string'

  # For routine mode, get records modified since queryTs
  resoParams = {
    $top: MAX_RECORDS_PER_PAGE
    $skip: 0
    $filter: "ModificationTimestamp ge #{queryTs.toISOString()}"
    $orderby: "ModificationTimestamp asc"
    $count: true
  }

  lastTs = queryTs
  isDone = false
  updatedTotal = 0
  logKey = "get #{resourceName} routine"

  while not isDone
    return if gFatalError

    updatedCount = 0
    debug.info logKey, {resoParams}
    startTs = new Date()
    queryLog.begin logKey, {resoParams:{
      top: resoParams.$top,
      skip: resoParams.$skip,
      filter: resoParams.$filter,
      count: resoParams.$count
    }, startTs}

    try
      response = await callWithRetry({
        func: -> gClient.getResourceAsync(
          resourceType,
          resoParams,
          FETCH_TIMEOUT
        )
        resourceName,
        mode: MODE_ROUTINE,
        params: {resoParams}
      })
      returnedCount = response?["@odata.count"]
      if (returnedCount is 0) or (not response?.value?.length)
        debug.info "no records returned #{resourceName} byMt"
        isDone = true
        queryLog.end logKey, {
          endTs: new Date(),
          durationMs: new Date().getTime() - startTs.getTime(),
          valueLength: response.value?.length,
          returnedCount,
          isDone
        }
        break
      updatedCount = 0
      for resource in response.value
        if resourceName is RESOURCE_NAME_PROPERTY
          await saveProperty(resource, MODE_ROUTINE)
        else
          await gCollections[resourceName].updateOne( 
            {_id: resource[keyField] },
            { $set: resource, $setOnInsert: { ts: new Date() }},
            { upsert: true }
          )
          gSpeedMeter.check {"mt_#{resourceName}": 1}

        lastTs = resource.ModificationTimestamp
        if new Date(lastTs) < new Date(queryTs)
          debug.warn "lastTs < queryTs", lastTs, queryTs
        updatedCount++
      updatedTotal += updatedCount
      await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(resourceName, MODE_ROUTINE)},
        {$set: {
          nextQueryTs: new Date(lastTs)
          status: RUNNING_NORMAL
          lastUpdated: new Date()
          updatedTotal,
          updatedCount,
          durationMs
        },$unset:{error:1}},
        {upsert: true}
      debug.debug "response.value.length:#{response.value.length}, resoParams.$top:#{resoParams.$top}, isDone:#{isDone}"
      isDone = response.value.length < resoParams.$top
      debug.debug "get #{resourceName} routine, returnedCount:#{returnedCount}, updatedTotal:#{updatedTotal}, lastTs:#{lastTs}, isDone:#{isDone}"
      durationMs = new Date().getTime() - startTs.getTime()
      queryLog.update logKey, {
        updatedTotal,
        updatedCount,
        returnedCount,
        lastTs,
        isDone,
        durationMs
      }
      resoParams.$filter = "ModificationTimestamp ge #{lastTs}"
      if durationMs < QUERY_WAIT_TIME
        await sleep(QUERY_WAIT_TIME - durationMs)

    catch error
      debug.error "Error downloading #{resourceName}", error
      throw error

  return [updatedTotal, lastTs]


###*
# Start queue workers for member and office resources
###
startQueueWorkers = ->
  for resourceName in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE, RESOURCE_NAME_PROPERTY]
    do (resourceName) ->
      process.nextTick ->
        try
          downloadQueuedResources(resourceName)
        catch error
          debug.error 'downloadQueuedResource', resourceName, error
          gFatalError = error
          gracefulExit(error, MODE_ROUTINE)

###*
# Download resources from queue
# @param {String} resourceName The name of the resource to download
###
downloadQueuedResources = (resourceName) ->
  resourceType = getResourceType(resourceName)
  keyField = RESOURCE_KEY_FIELDS[resourceName]
  linkField = RESOURCE_LINK_FIELDS[resourceName]
  if not linkField
    throw new Error("No link field defined for resource: #{resourceName}")
  
  logKey = "download #{resourceName} by queue"
  queryLog = EventLogger.createLogger(debug)
  updatedTotal = 0
  queueQueryCount = 0
  startTs = null
  while true
    return if gFatalError
    updatedCount = 0
    startTs ?= new Date()
    records = await gQueues[resourceName].getNextBatch(QUEUE_BATCH_SIZE)
    if not records?.length
      debug.info "no records returned #{resourceName} by queue, sleep #{QUEUE_SLEEP_MS}ms"
      await sleep(QUEUE_SLEEP_MS)
      continue

    queueQueryCount += records.length

    buildFilter = ()->
      recordKeys = records.map((record) -> "'#{record._id}'")
      return "#{linkField} in (#{recordKeys.join(',')})"

    resoParams = {
      $top: MAX_RECORDS_PER_PAGE,
      $skip: 0,
      $count: true,
      $filter: buildFilter()
    }
    debug.info logKey, {resoParams}
    queryLog.begin logKey, {resoParams:{
      top: resoParams.$top,
      skip: resoParams.$skip,
      filter: resoParams.$filter,
      count: resoParams.$count
    }, startTs: new Date(), queueQueryCount}
    response = await callWithRetry({
      func: -> gClient.getResourceAsync(
        resourceType,
        resoParams
      )
      resourceName,
      mode: 'queue',
      params: {resoParams}
    })
    returnedCount = response?["@odata.count"]
    if (returnedCount is 0) or (not response?.value?.length)
      debug.info "no #{resourceName} records returned by queue, sleep #{QUEUE_SLEEP_MS}ms"

    for resource in response.value
      updatedCount++
      if resourceName is RESOURCE_NAME_PROPERTY
        await saveProperty(resource, MODE_QUEUE)
      else
        await gCollections[resourceName].updateOne {_id: resource[keyField]},
        {$set: resource, $setOnInsert: {ts: new Date()}},
        {upsert: true}
        linkField = RESOURCE_LINK_FIELDS[resourceName]
        await updateResourceInProperties({resource, resourceName, linkField, mode: MODE_QUEUE})
    
    updatedTotal += updatedCount
    # remove them from queue
    await gQueues[resourceName].removeBatchFromQueue(records)

    gSpeedMeter.check {"q_#{resourceName}": response.value.length}

    queryLog.end logKey, {
      endTs: new Date(),
      durationMs: new Date().getTime() - startTs.getTime(),
      valueLength: response.value?.length,
      returnedCount,
      updatedCount,
      updatedTotal,
      queueQueryCount
    }
    if queryLog.logs.length >= LOG_BATCH_SIZE
      await gCollections.event_log.updateOne({
        _id: "crea_reso_#{resourceName}_queue_#{startTs.toISOString()}"
      },{$set:{
        startTs: startTs,
        endTs: new Date(),
        totalUpdatedCount: updatedTotal,
        durationSec: (new Date().getTime() - startTs.getTime()) / 1000
        logs: queryLog.logs,
      }},{upsert: true})
      queryLog = EventLogger.createLogger(debug)
      startTs = null

###*
# Check and process initial data download if needed
# @param {Boolean} isForce Whether to force download even if process is running
###
checkAndProcessInitialData = (isForce) ->
  return if await checkInitialDownloadsCompleted()

  {handleProcessStatus, checkRunningProcess} = getProcessHandlerForMode(MODE_INITIAL)
  hasRunningProcess = await checkRunningProcess()
  if (not isForce) and hasRunningProcess
    return EXIT HAS_RUNNING_PROCESS
    
  await handleProcessStatus {
    status: RUNNING_NORMAL,
    startTs: Date.now(),
    nextTs: Date.now() + AVG_RUNNING_MS
  }
  # Download initial data before starting workers
  try
    await downloadInitialData()
    debug.info "All initial data downloads completed"
    await handleProcessStatus {
      status: FINISH_WITHOUT_ERROR,
    }
    await ProcessStatusCol.updateOne(
      {
        _id: /creaResoDownload_initial_/,
        status: FINISH_WITHOUT_ERROR
      },
      { 
        $unset: {nextTs: 1}
      }
    )
  catch error
    await handleProcessStatus {
      status: RUNNING_ERROR,
      error,
    }
    throw error

###*
# Start routine worker for both property and resources
###
startRoutineWorker = ({resourceImportKeys, now}) ->

  resourceDownloadList = [
    RESOURCE_NAME_MEMBER,
    RESOURCE_NAME_OFFICE,
    RESOURCE_NAME_PROPERTY,
  ]

  queryLog = EventLogger.createLogger(debug)
  {handleProcessStatus} = getProcessHandlerForMode(MODE_ROUTINE)
  startTs = null
  totalUpdatedCount = 0
  while true
    return if gFatalError

    try

      startTs ?= new Date()

      # First process other resources
      for resourceName in resourceDownloadList
        # Get latest queryTs from database
        importKey = await MlsImportKeysCol.findOne {_id: IMPORT_KEY_ID(resourceName, MODE_ROUTINE)}
        queryTs = importKey?.nextQueryTs or now
        [updatedCount, nextQueryTs] = await getAndSaveResourcesByMt({
          resourceName,
          queryTs,
          queryLog
        })
        totalUpdatedCount += updatedCount
        nextQueryTs = new Date(nextQueryTs) if 'string' is typeof nextQueryTs
        # Update import key with new timestamp
        await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(resourceName, MODE_ROUTINE)},
          {$set: {
            nextQueryTs,
            status: RUNNING_NORMAL,
            updatedCount,
          },$unset:{error:1}},
          {upsert: true}

      await handleProcessStatus {
        status: RUNNING_NORMAL
        nextTs: new Date(Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }

      # Sleep if needed
      sleepMs = startTs.getTime() + ROUTINE_SLEEP_MS - Date.now()
      # Save logs before sleep if there are any
      if queryLog.logs.length > 0
        await gCollections.event_log.updateOne({
          _id: "crea_reso_routine_#{startTs.toISOString()}"
        },{$set:{
          startTs: startTs,
          endTs: new Date(),
          totalUpdatedCount,
          durationSec: (new Date().getTime() - startTs.getTime()) / 1000
          logs: queryLog.logs,
        }},{upsert: true})
        queryLog = EventLogger.createLogger(debug)
        startTs = null
      if sleepMs > 0    
        debug.info "Routine loop sleeping for #{sleepMs / 60000} min"
        await sleep(sleepMs)

    catch error
      debug.error error
      await handleProcessStatus {
        status: RUNNING_ERROR
        error
        nextTs: new Date(Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
      }
      
      await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(RESOURCE_NAME_PROPERTY, MODE_ROUTINE)},
        {$set: {
          status: RUNNING_ERROR
          error: error.toString()
          lastUpdated: new Date()
        },$unset:{error:1}},
        {upsert: true}
      # nobody monitors this error, so we need to exit the process
      gFatalError = error
      gracefulExit(error, MODE_ROUTINE)
      break

###*
# Start property init worker using replication API
###
startPropertyInitWorker = ({resourceImportKeys, now}) ->
  mode = MODE_INIT
  #get from db
  propertyImportKey = await MlsImportKeysCol.findOne {_id: IMPORT_KEY_ID(RESOURCE_NAME_PROPERTY, mode)}
  if propertyImportKey?.initDone
    debug.info "Property init already done"
    return

  {handleProcessStatus} = getProcessHandlerForMode(mode)
  queryLog = EventLogger.createLogger(debug)
  
  try
    startTs = new Date()
    await downloadResourceReplication({
      resourceName: RESOURCE_NAME_PROPERTY,
      collections: gCollections,
      queryLog,
      queryTs: propertyImportKey?.nextQueryTs or now,
      mode,
      initDoneTs: propertyImportKey?.initDoneTs or null
    })

    await gCollections.event_log.updateOne({
      _id: "crea_reso_property_init_#{startTs.toISOString()}"
    },{$set:{
      startTs,
      endTs: new Date(),
      durationMs: new Date().getTime() - startTs.getTime(),
      logs: queryLog.logs,
    }},{upsert: true})

    await MlsImportKeysCol.updateOne {_id: IMPORT_KEY_ID(RESOURCE_NAME_PROPERTY, mode)},
      {$set: {
        initDone: true
        lastUpdated: new Date()
      },$unset:{error:1}},
      {upsert: true}

    await handleProcessStatus {
      status:  FINISH_WITHOUT_ERROR,
    }
    await ProcessStatusCol.updateOne(
      {
        _id: /creaResoDownload_init_/,
        status: FINISH_WITHOUT_ERROR
      },
      { 
        $unset: {nextTs: 1}
      }
    )
  catch error
    debug.error error
    await handleProcessStatus {
      status: RUNNING_ERROR,
      error,
      nextTs: new Date(Date.now() + ROUTINE_SLEEP_MS + AVG_RUNNING_MS)
    }
    # nobody monitors this error, so we need to exit the process
    gFatalError = error
    gracefulExit(error, mode)

###*
# Update resources in properties that reference them
# @param {Object} resource The resource to update
# @param {String} resourceName The type of resource (member/office/openhouse)
# @param {String} linkField The link field of the resource
###
updateResourceInProperties = ({resource, resourceName, linkField, mode}) ->
  # Find properties that reference these resources
  resourceId = resource[linkField]
  query = switch resourceName
    when RESOURCE_NAME_MEMBER
      {$or: [
        {ListAgentKey: resourceId},
        {CoListAgentKey: resourceId},
      ]}
    when RESOURCE_NAME_OFFICE
      {$or: [
        {ListOfficeKey: resourceId},
        {CoListOfficeKey: resourceId},
      ]}
    else
      throw new Error("Invalid resource name for property update: #{resourceName}")

  debug.debug "updateResourceInProperties",resourceName, resourceId, resource,linkField,query,mode
  mergedProps = await gCollections.merged.findToArray(query)
  if mode is MODE_ROUTINE and mergedProps?.length < 1
    await gQueues[RESOURCE_NAME_PROPERTY].addToQueue({_id: resourceId},NEW_PROP_PRIORITY)
    debug.debug "new #{resourceName} add to property queue", resourceId
    return

  debug.debug "will update #{resourceName} in #{mergedProps?.length} properties"
  for mergedProp in mergedProps
    # Update the resources in property
    await mergeResourceData({property: mergedProp, mergedDoc: mergedProp, id: mergedProp._id, mode})
    gSpeedMeter.check {"update_prop_#{resourceName}": 1}


###*
# Download and store OpenHouse data
# @returns {Object} Dictionary of OpenHouse data by ListingKey
###
downloadOpenHouseData = (queryLog) ->
  debug.info "Downloading OpenHouse data..."
  startTs = new Date()

  resoParams = {
    $top: MAX_RECORDS_PER_PAGE
    $count: true
  }

  resourceType = getResourceType(RESOURCE_NAME_OPENHOUSE)
  logKey = "get openhouse daily"
  updatedTotal = 0
  isDone = false
  newOpenHouseDict = {}
  nextLink = null
  response = await callWithRetry({
    func: -> gClient.getResourceAsync(
      resourceType,
      resoParams,
      FETCH_TIMEOUT
    )
    resourceName: RESOURCE_NAME_OPENHOUSE,
    mode: MODE_DAILY,
    params: {resoParams}
  })

  while not isDone
    return if gFatalError

    updatedCount = 0
    debug.info logKey, {startTs,resoParams,nextLink}
    queryLog.begin logKey, {startTs,resoParams,nextLink}

    try
      nextLink = response?['@odata.nextLink']
      returnedCount = response?["@odata.count"]
      if (returnedCount is 0) or (not response?.value?.length)
        debug.info "no openhouse records returned daily", {
          returnedCount,
          nextLink,
          valueLength: response.value?.length,
        }
        queryLog.end logKey, {
          endTs: new Date(),
          durationMs: new Date().getTime() - startTs.getTime(),
          isDone: true, 
          valueLength: response.value?.length,
          returnedCount,
          nextLink,
        }
        isDone = true
        break
      # Process OpenHouse records
      for openhouse in response.value
        listingKey = openhouse.ListingKey
        newOpenHouseDict[listingKey] ?= []
        newOpenHouseDict[listingKey].push(openhouse)
        updatedCount++
        await gCollections[RESOURCE_NAME_OPENHOUSE].updateOne(
          {_id: openhouse.OpenHouseKey},
          {$set: openhouse, $setOnInsert: {ts: new Date()}}, 
          {upsert: true})
        gSpeedMeter.check {"daily_#{RESOURCE_NAME_OPENHOUSE}": 1}
      updatedTotal += updatedCount

      durationMs = Date.now() - startTs.getTime()
      queryLog.end logKey, {
        endTs: new Date(),
        durationMs,
        returnedCount,
        updatedTotal,
        updatedCount,
      }
      # Sleep for a short time to avoid rate limiting
      if durationMs < QUERY_WAIT_TIME
        await sleep(QUERY_WAIT_TIME - durationMs)
      # Get next page using nextLink
      if nextLink
        response = await callWithRetry({
          func: -> gClient.getResourceByNextLinkAsync(nextLink, FETCH_TIMEOUT)
          resourceName: RESOURCE_NAME_OPENHOUSE,
          mode: MODE_DAILY,
          params: {resoParams}
        })
      else
        isDone = true
        debug.info "No nextLink, break #{RESOURCE_NAME_OPENHOUSE} daily"
        break
    catch error
      debug.error "Error downloading OpenHouse data", error
      throw error

  debug.info "OpenHouse data download completed, total: #{updatedTotal}"
  return newOpenHouseDict


###*
# Update properties with OpenHouse data from global dictionary
###
updatePropertiesWithOpenHouses = ->
  debug.info "Updating properties with OpenHouse data..."
  now = new Date()
  
  for listingKey, openHouses of gOpenHouseDict
    try
      # Find property in merged collection
      property = await gCollections.merged.findOne({_id: CREA_PREFIX + listingKey})
      
      if property?
        # Update property with OpenHouse data and ohMt
        await gCollections.merged.updateOne(
          {_id: CREA_PREFIX + listingKey},
          {
            $set: {
              OpenHouses: openHouses
              ohMt: now
            }
          }
        )
        gSpeedMeter.check {UpdatedPropertyOpenHouses: 1}
      else
        # Add missing property to queue
        debug.info "Property not found for OpenHouse, adding to queue", listingKey
        await gQueues[RESOURCE_NAME_PROPERTY].addToQueue(
          {_id: listingKey},
          NEW_PROP_PRIORITY
        )
        gSpeedMeter.check {QueuedMissingProperty: 1}
    catch error
      debug.error "Error updating property with OpenHouse data", {listingKey, error}

###*
# Queue properties with stale OpenHouse data for update
###
queueStaleOpenHouseProperties = ->
  debug.info "Checking for stale OpenHouse data..."
  today = new Date()
  today.setHours(0, 0, 0, 0)
  
  # Find properties with outdated ohMt
  mergedProps = await gCollections.merged.findToArray({
    $and: [
      {ohMt: {$lt: today}},
      {ohMt: {$exists: true}}
    ]
  }, {
    projection: {_id: 1, ListingKey: 1}
  })

  for property in mergedProps
    try
      if property?.ListingKey
        # Add property to queue for update
        await gQueues[RESOURCE_NAME_PROPERTY].addToQueue(
          {_id: property.ListingKey},
          NEW_PROP_PRIORITY
        )
        gSpeedMeter.check {QueuedStaleOpenHouse: 1}
    catch error
      debug.error "Error queueing stale OpenHouse property", {property, error}

###*
# Download, process and log OpenHouse data
# @param {Object} queryLog Query log object
# @param {Date} startTs Start timestamp
###
downloadAndProcessOpenHouse = () ->
  try
    queryLog = EventLogger.createLogger(debug)
    startTs = new Date()
    # Update global dictionary
    gOpenHouseDict = await downloadOpenHouseData(queryLog)
    debug.debug "gOpenHouseDict length", Object.entries(gOpenHouseDict).length
    await updatePropertiesWithOpenHouses()
    await queueStaleOpenHouseProperties()
    
    # Log the operation
    await gCollections.event_log.updateOne(
      {_id: "crea_reso_openhouse_daily_#{startTs.toISOString()}"}
      {$set: {
        event: 'downloadDailyOpenHouse'
        resourceName: RESOURCE_NAME_OPENHOUSE
        mode: MODE_DAILY
        queryLog: queryLog.logs
        startTs
        endTs: new Date()
        durationMs: new Date().getTime() - startTs.getTime()
      }}
      {upsert: true}
    )
  catch error
    debug.error "Error downloadAndProcessOpenHouse", error
    throw error

###*
# Process OpenHouse data collection every 24 hours
###
startOpenHouseWorker = () ->
  mode = MODE_DAILY
  {handleProcessStatus} = getProcessHandlerForMode(mode)
  debug.info "Starting openhouse worker"
  await handleProcessStatus {
    status: RUNNING_NORMAL
  }
  # Then start the daily loop
  while true
    try
      now = new Date()
      nextRun = getNextRunTime(OPENHOUSE_CHECK_HOUR)
      timeUntilNextRun = nextRun.getTime() - now.getTime()
      debug.info "Sleeping until next openhouse check at #{nextRun}"
      await sleep(timeUntilNextRun)
      await downloadAndProcessOpenHouse()
      await handleProcessStatus {
        status: RUNNING_NORMAL
      }
    catch error
      debug.error "Error in processOpenHouseWorker", error
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: nextRun
      }

###*
# Get active properties from RESO API replication endpoint
# @returns {Object} Dictionary of active properties by ListingKey
###
getActivePropertiesFromReso = (queryLog) ->
  debug.info "Getting active properties from RESO API..."
  startTs = new Date()
  activeProps = {}

  resoParams = {
    $count: true
  }

  logKey = "get active properties replication"
  debug.info logKey, {resoParams}
  queryLog.begin logKey, {startTs, resoParams}

  try
    response = await callWithRetry({
      func: -> gClient.getResourceReplicationAsync(
        gClient.resourceTypes.PROPERTY,
        resoParams,
        FETCH_TIMEOUT
      )
      resourceName: RESOURCE_NAME_PROPERTY,
      mode: 'daily',
      params: {resoParams}
    })

    returnedCount = response?["@odata.count"]
    if (returnedCount is 0) or (not response?.value?.length)
      debug.error "no active properties returned", {
        returnedCount,
        valueLength: response.value?.length,
      }
      queryLog.end logKey, {
        endTs: new Date(),
        durationMs: new Date().getTime() - startTs.getTime(),
        isDone: true,
        valueLength: response.value?.length,
        returnedCount,
      }
      return activeProps

    # Process active properties
    for prop in response.value
      activeProps[prop.ListingKey] = prop.ModificationTimestamp
      await gCollections.merged.updateOne(
        {
          _id: CREA_PREFIX + prop.ListingKey,
          $or: [
            {status: {$ne: 'A'}},  # Update if not active
            {UCnt: {$exists: true}} # Or if UCnt exists (to reset it)
          ]
        },
        {
          $set: {status: 'A'},
          $unset: {UCnt: 1}
        }
      )

    durationMs = Date.now() - startTs.getTime()
    queryLog.end logKey, {
      endTs: new Date(),
      durationMs,
      returnedCount,
      updatedTotal: response.value.length,
    }

  catch error
    debug.error "Error getting active properties from RESO", error
    throw error

  debug.info "Active properties download completed, total: #{Object.keys(activeProps).length}"
  return activeProps

###*
# Get valid lease amount from property
# @param {Object} property Property object
# @returns {Number} Valid lease amount or 0
###
getValidLeaseAmount = (property) ->
  if property.LeaseAmount? and property.LeaseAmount > 0
    return property.LeaseAmount
  if property.TotalActualRent? and property.TotalActualRent > 0
    return property.TotalActualRent
  return 0

###*
# Get active properties from database
# @returns {Object} Dictionary of active properties by ListingKey
###
getActivePropertiesFromDb = ->
  debug.info "Getting active properties from database..."
  dbActiveProps = {}
  
  props = await gCollections.raw.findToArray(
    {status: "A"},
    {projection: {ListingKey: 1, ModificationTimestamp: 1, LeaseAmount: 1, TotalActualRent: 1}}
  )
  
  for prop in props
    if prop.ListingKey
      leaseAmount = getValidLeaseAmount(prop)
      dbActiveProps[prop.ListingKey] = leaseAmount
  
  debug.info "Found #{Object.keys(dbActiveProps).length} active properties in database"
  return dbActiveProps

###*
# Compare and update property statuses
# @param {Object} resoActiveProps Active properties from RESO API
# @param {Object} dbActiveProps Active properties by ListingKey with lease amounts
###
updatePropertyStatuses = (resoActiveProps, dbActiveProps) ->
  debug.info "Comparing and updating property statuses..."
  
  # Find properties that are no longer active
  for listingKey, leaseAmount of dbActiveProps
    if not resoActiveProps[listingKey]?
      debug.info "Property no longer active", listingKey
      # Prepare update based on leaseAmount
      updateSet = {}
      updateUnset = {}

      # Get current property to check UCnt
      mergedProp = await gCollections.merged.findOne({ListingKey: listingKey})
      
      # Initialize or increment UCnt
      updateSet.UCnt = (mergedProp?.UCnt or 0) + 1
      
      # Set status to U if UCnt exceeds threshold
      if updateSet.UCnt > U_CNT_THRESHOLD
        updateSet.status = "U"
        if leaseAmount > 0
          updateSet.StandardStatus = "Leased"
        else
          updateUnset.StandardStatus = 1
        debug.info "Deactivated property", listingKey, updateSet, updateUnset
        gSpeedMeter.check {DeactivatedProperty: 1}

      # Update raw collection
      await gCollections.merged.updateOne(
        {ListingKey: listingKey},
        {
          $set: updateSet
          $unset: updateUnset
        }
      )

  # Queue new active properties for update
  for listingKey, resoTs of resoActiveProps
    if not dbActiveProps[listingKey]?
      debug.info "New active property found", listingKey
      await gQueues[RESOURCE_NAME_PROPERTY].addToQueue(
        {_id: listingKey},
        NEW_PROP_PRIORITY
      )
      gSpeedMeter.check {NewActiveProperty: 1}

###*
# Process active property sync
###
processActivePropertySync = ->
  try
    queryLog = EventLogger.createLogger(debug)
    startTs = new Date()

    # Get active properties from both sources
    resoActiveProps = await getActivePropertiesFromReso(queryLog)
    dbActiveProps = await getActivePropertiesFromDb()

    # Compare and update
    await updatePropertyStatuses(resoActiveProps, dbActiveProps)

    # Log the operation
    await gCollections.event_log.updateOne(
      {_id: "crea_reso_active_prop_sync_#{startTs.toISOString()}"}
      {$set: {
        event: 'syncActiveProperties'
        mode: 'active'
        queryLog: queryLog.logs
        startTs
        endTs: new Date()
        durationMs: new Date().getTime() - startTs.getTime()
        resoActiveCount: Object.keys(resoActiveProps).length
        dbActiveCount: Object.keys(dbActiveProps).length
      }}
      {upsert: true}
    )
  catch error
    debug.error "Error in processActivePropertySync", error
    throw error

###*
# Get next run time for property status sync
# @returns {Date} Next scheduled run time at 2 AM
###
getNextRunTime = (checkHour = ACTIVE_PROP_CHECK_HOUR) ->
  now = new Date()
  nextRun = new Date(now)
  nextRun.setHours(checkHour, 0, 0, 0)
  if nextRun <= now
    nextRun.setDate(nextRun.getDate() + 1)
  return nextRun

###*
# Start property status sync worker that runs daily to sync active/inactive statuses
###
startPropertyStatusSyncWorker = ->
  mode = MODE_DAILY
  {handleProcessStatus} = getProcessHandlerForMode(mode)
  debug.info "Starting property status sync worker..."
  
  while true
    try
      nextRun = getNextRunTime()
      timeUntilNextRun = nextRun.getTime() - new Date().getTime()
      debug.info "Sleeping until next property status sync at #{nextRun}"
      await sleep(timeUntilNextRun)
      await processActivePropertySync()
      await handleProcessStatus {
        status: RUNNING_NORMAL,
      }
    catch error
      debug.error "Error in property status sync worker", error
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: nextRun
      }


###*
# Main program entry point
###
main = -> 
  try
    isForce = AVGS.includes('force')
    modes = validateModes(AVGS)
    # Initialize system
    now = new Date()
    gCollections = await initializeCollections()
    gQueues = await initializeQueues()
    gClient = initializeResoClient()

    # Check and process initial data
    await checkAndProcessInitialData(isForce)
    # Start OpenHouse collection schedule
    await downloadAndProcessOpenHouse()
    # Initialize import keys
    resourceImportKeys = await initializeResourceImportKeys({modes, now})
    # Start workers
    if MODE_INIT in modes
      startPropertyInitWorker({resourceImportKeys, now})
    if MODE_ROUTINE in modes
      startQueueWorkers()
      startRoutineWorker({resourceImportKeys, now})
      startOpenHouseWorker()
      startPropertyStatusSyncWorker()
  catch error
    debug.error error
    gFatalError = error
    for mode in modes
      await gracefulExit(error, mode)

# Start the program
main() 