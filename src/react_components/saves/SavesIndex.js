import SavedProperties from './SavedProperties.js'
import SavedCma from './SavedCma.js'

const { useEffect,useState } = React
const SavesIndex = () => {
  const [tab, setTab] = useState(0)
  const [tabNav, setTabs] = useState([{
    name: TRANSLATES.homes,
    index: 0
  }])
  const [ids, setIds] = useState([])

  useEffect(() => {
    getCMAIds()
    // 监听 CMA 列表更新事件
    window.bus.$on('update-cma-list', handleUpdateCmaList)
    return () => {
      window.bus.$off('update-cma-list', handleUpdateCmaList)
    }
  },[])

  const handleUpdateCmaList = (updatedList) => {
    // 更新 ids 状态，保持其他 tab 的数据不变
    setIds(prevIds => {
      return prevIds.map(item => {
        const updatedItem = updatedList.find(newItem => newItem._id === item._id)
        return updatedItem || item
      })
    })
  }

  const getCMAIds = ()=>{
    fetchData('/1.5/saveCMA/getCMAIds',{method:'post',body:{}},(error,res)=>{
      if(error || !res){
        window.bus.$emit('flash-message',error.toString())
      }else if(res.ok == 1 && res.result.length > 0){
        setTabs([{
          name: TRANSLATES.homes,
          index: 0
        },{
          name: 'CMA',
          index: 1
        }])
        setIds(res.result)
      }
    })
  }

  return (
    <React.Fragment>
      <div className="prop-list-top">
        <div className="container">
          <div className="title-line "> 
            <h1 className="content-title">{TRANSLATES.savedTitle}</h1> 
          </div>
          <nav className="prop-list-nav">
            <div className="prop-list-nav-container">
              <div className="prop-list-nav-active"></div>
              {tabNav.map(t=>
                <a
                  key={t.index} 
                  className={tab === t.index ?'prop-list-nav-link selected' :'prop-list-nav-link '}
                  onClick={()=>setTab(t.index)}
                  role='button'
                >
                  {t.name}
                </a>)}
            </div>
          </nav>
        </div>
      </div>
      {tab === 0 && <SavedProperties/>}
      {tab === 1 && <SavedCma cmalist={ids} firstid={ids[0]._id}/>}
    </React.Fragment>
  )
}

export default SavesIndex
