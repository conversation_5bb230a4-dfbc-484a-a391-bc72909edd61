import ImageWithFallback from '../shared_components/ImageWithFallback.js'
import SlideDrawer from '../shared_components/DrawerSearchPropId.js'
import '../../sortable1.15.0.min.js'
const { useEffect,useState,useRef } = React

const SavedCma = ({cmalist,firstid}) => {
  const [groupId, setGroupId] = useState(firstid)
  const [idsList, setIdsList] = useState([])
  const [showSign, setShowSign] = useState(true)
  const [language, setLanguage] = useState(LANG)
  const [selModel, setSelModel] = useState('diff')
  const [showElem, setShowElem] = useState('none')
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [showDelete, setShowDelete] = useState({})
  const idsListRef = useRef(idsList)
  const groupIdRef = useRef(groupId)

  useEffect(() => {
    groupIdRef.current = groupId
    let index = cmalist.findIndex((v)=>{
      return v._id.toString() == groupId.toString()
    })
    if (index !== -1){
      getIdsInfo(cmalist[index].ids)
    } else {
      setIdsList([])
    }
  },[groupId])

  useEffect(() => {
    window.bus.$on('select-search-propid', handleSelectPropId)
    return () => {
      window.bus.$off('select-search-propid', handleSelectPropId)
    }
  }, [])

  const updateCMAIds = (newIds, onSuccess) => {
    fetchData('/1.5/saveCMA/saveCMAIds', {
      method: 'post',
      body: { ids: newIds, _id: groupIdRef.current }
    }, (error, res) => {
      if (error || !res) {
        window.bus.$emit('flash-message', error.toString())
      } else if (res.ok !== 1) {
        window.bus.$emit('flash-message', res.e)
      } else {
        getIdsInfo(newIds)
        // 更新当前 CMA 组的数据
        const updatedCma = {
          ...cmalist.find(item => item._id.toString() === groupIdRef.current.toString()),
          ids: newIds
        }
        // 通知父组件更新数据
        window.bus.$emit('update-cma-list', [updatedCma])
        if (onSuccess) {
          onSuccess()
        }
      }
    })
  }

  const handleSelectPropId = (propID) => {
    setIsDrawerOpen(false)
    if (!propID) {
      window.bus.$emit('flash-message', TRANSLATES.pleaseSelect)
      return
    }
    let ids = idsListRef.current.map((item) => {
      return item._id
    })
    const newIds = [...ids, propID]
    updateCMAIds(newIds)
  }

  const getIdsInfo = (ids)=>{
    fetchData('/1.5/htmltoimg/idsData',{method:'post',body:{ids:ids}},(error,res)=>{
      if(error || !res){
        window.bus.$emit('flash-message',error.toString())
      }else if(res.ok !== 1){
        setShowElem('none')
        idsListRef.current = []
        setIdsList([])
        window.bus.$emit('flash-message',res.e)
      }else{
        setIdsList(res.result)
        idsListRef.current = res.result
        setShowElem('block')
        let opt = {
          animation: 150,
          ghostClass: 'sort-placeholder',
          delay:100,
          delayOnTouchOnly:true,
          touchStartThreshold: 10,
          onUpdate: (evt)=>{ dragEnd(evt) }
        }
        let previewImgBox = document.querySelector("#sortPreview")
        new Sortable(previewImgBox,opt)
      }
    })
  }

  const updateSortIds = ()=>{
    let ids = idsListRef.current.map((item)=>{
      return item._id
    })
    fetchData('/1.5/saveCMA/saveCMAIds',{method:'post',body:{ids:ids,_id:groupId}},(error,res)=>{
      if(error || !res){
        window.bus.$emit('flash-message',error.toString())
      }else if(res.ok !== 1){
        window.bus.$emit('flash-message',res.e)
      }else{
        window.open(`/1.5/htmltoimg/cmaWebDottemplate?lang=${language}&selModel=${selModel}&showSign=${showSign}&id=${groupId}`, 'RealMaster')
      }
    })
  }

  const dragEnd = (evt) => {
    let $ul = document.querySelector("#sortPreview")
    let newIndex = evt.newIndex
    let oldIndex = evt.oldIndex
    let $li
    let $oldLi = $ul.children[oldIndex]
    if (newIndex >= $ul.children.length){
      newIndex = $ul.children.length - 1
    }
    $li = $ul.children[newIndex]
    $ul.removeChild($li)
    if(newIndex > oldIndex) {
      $ul.insertBefore($li,$oldLi)
    } else {
      $ul.insertBefore($li,$oldLi.nextSibling)
    }
    let item = idsListRef.current.splice(oldIndex,1)
    idsListRef.current.splice(newIndex,0,item[0])
    setIdsList([...idsListRef.current])
  }

  const handleAddProp = () => {
    const options = {}
    setIsDrawerOpen(true)
    options.config = {
      modalId:'simPopUpModal',
      mountId:'propsearch-modal-root',
      width:'100%',
      height:'100vh',
      title:'',
      hide:true
    }
    window.bus.$emit('drawer-search-prop', options);
  }

  const handleDelete = (id) => {
    setShowDelete(prev => ({...prev, [id]: false}))
    let ids = idsListRef.current.map((item) => {
      return item._id
    }).filter(itemId => itemId !== id)
    updateCMAIds(ids)
  }

  let context = null
  context = (
    <React.Fragment>
      <div className="row" id="sortPreview">
        {idsList.map(id=>{
          return (
            <div className="col-xs-12 col-sm-6 col-md-4">
              <a className="listing-prop box-shadow border-radius-all" href={id.webUrl} target="_blank">
                <div className="listing-prop-img-container cursorDrag">
                  <span className='fa fa-arrows' style={{'position':'absolute',left:'45%',top:'50%',color:'#fff','font-size':'20px'}}></span>
                  {id.img?<ImageWithFallback src={id.img} className="listing-prop-img border-radius-top"/>:
                  <div className="listing-prop-no-img">
                    <div className="listing-prop-no-img-container">
                      <span className="listing-prop-no-img-icon fa fa-home"></span>
                      <span>{TRANSLATES.noImage}</span>
                    </div>
                  </div>}
                </div>
                <div className="listing-prop-detail">
                  <h3 className="listing-prop-price">
                    {id.spValStrRed?<span>{id.spValStrRed}</span>:id.lpValStrRed?<span>{id.lpValStrRed}</span>:''}
                  </h3>
                  <span className="listing-prop-id">{`ID ${id.sid || id.id}`}</span>
                  <span className="listing-prop-address">{id.addr},{id.city}</span>
                  <span style={{'position': 'relative'}}>
                    <span className="listing-prop-address displayDiv" style={{'margin':'5px 0 0'}}>
                      <span>
                        {id.onD}
                        {id.sldd?<span style={{'margin-left': '20px',color: '#e03131'}}>{id.sldd}</span>:''}
                      </span>
                      <span
                        style={{ display: showDelete[id._id] ? 'none' : 'inline-block' }}
                        className="sprite16-18 sprite16-4-8"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setShowDelete(prev => ({...prev, [id._id]: true}));
                        }}
                        />
                    </span>
                    <span
                      style={{ display: showDelete[id._id] ? 'inline-block' : 'none' }}
                      className="positionRight"
                      >
                      <span className="btnDelete btn-negative" onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleDelete(id._id);
                      }}>{TRANSLATES.delete}</span>
                      <span className="btnDelete btn-nooutline" style={{border:'1px solid #fff'}} onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowDelete(prev => ({...prev, [id._id]: false}));
                      }}>{TRANSLATES.cancel}</span>
                    </span>
                  </span>
                </div>
              </a>
            </div>
          )
        })}
      </div>
    </React.Fragment>
  )

  return (
    <div className="prop-list-lists">
      <div className="container">
        <div>
          <div className="listing-result-filters" style={{width: '100%'}}>
            {cmalist.map(cma=>
              <div key={cma._id}
                className={groupId.toString() === cma._id.toString() ? 'listing-result-filter selected':'listing-result-filter'}
                onClick={()=> setGroupId(cma._id)}
              >
                <span>{cma._mt}</span>
              </div>
            )}
          </div>
        </div>
        <SlideDrawer isOpen={isDrawerOpen} onClose={() => setIsDrawerOpen(false)} />
        <div style={{'display':showElem}}>
          <div className="displayDiv">
            <div className="footerDisplay">
              <div id="id_with_sign">
                <input type="checkbox" name="sign" id="sign" style={{width:'20px',height:'20px',marginTop: '0px'}} checked={showSign} onChange={()=> {showSign?setShowSign(false):setShowSign(true)}}></input>
                <span style={{margin:'0 0 0 7px','vertical-align':'text-bottom', fontSize:'14px'}}>{TRANSLATES.signature}</span>
              </div>
              <div className="langSelect" >
                <span id="id_model_diff" onClick={()=>setSelModel('diff')}>
                  <span className={selModel == 'diff'? 'langLabel langBackground': 'langLabel'}>{TRANSLATES.diff}</span>
                </span>
                <span id="id_model_list" onClick={()=>setSelModel('list')}>
                  <span className={selModel == 'list'? 'langLabel langBackground': 'langLabel'}>{TRANSLATES.list}</span>
                </span>
              </div>
              <div className="langSelect" style={{marginLeft: '0px'}}>
                <span id="id_share_lang_zh" onClick={()=>setLanguage('zh-cn')}>
                  <span className={language == 'zh-cn' || language == 'zh'? 'langLabel langBackground': 'langLabel'}>中</span>
                </span>
                <span id="id_share_lang_zh" onClick={()=>setLanguage('en')}>
                  <span className={language == 'en'? 'langLabel langBackground': 'langLabel'}>En</span>
                </span>
              </div>
              <a className="printBtn" onClick={()=>updateSortIds()} style={{cursor: 'pointer'}}>{TRANSLATES.print}</a>
            </div>
            <a className="printBtn" onClick={handleAddProp} style={{cursor: 'pointer'}}>{TRANSLATES.add}</a>
          </div>
        </div>
        {context}
      </div>
    </div>
  )
}

export default SavedCma