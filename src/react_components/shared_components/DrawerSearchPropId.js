const { useEffect,useState } = React

const DrawerSearchPropId = ({ isOpen, onClose }) => {
  const [content, setContent] = useState('')

  useEffect(() => {
    window.bus.$on('drawer-search-prop',(options={})=>getSelectIdModel(options))
    return () => {
      window.bus.$off('drawer-search-prop',(options={})=>getSelectIdModel(options))
    }
  }, [])

  useEffect(() => {
    const propsearchRoot = document.getElementById('propsearch-modal-root');
    let timer = null;
    if (propsearchRoot) {
      timer = setTimeout(() => {
        if (isOpen) {
          propsearchRoot.classList.add('active');
        } else {
          propsearchRoot.classList.remove('active');
        }
      }, 10);
    }
    return () => clearTimeout(timer);
  }, [isOpen]);

  const getSelectIdModel = ({config={}})=>{
    RMSrv.getPageContent('/1.5/autoCompleteSearch?isPopup=1&isMLS=1', '#callBackString', config, (val)=>{
      if (val == ':cancel') {
        return;
      }
      try {
        const json = JSON.parse(val);
        window.bus.$emit('select-search-propid',json.id)
      } catch (e) {
        console.error(e);
      }
    })
  }

  return (
    <div className={isOpen ? 'displayBlock' : 'displayNone'} >
      <div className='backdrop show' onClick={onClose}></div>
      <div id="propsearch-modal-root">
        <div className="bar">
          <header className="bar-nav"> {TRANSLATES.searchId}
            <span className="fa fa-close pull-right" style={{cursor: 'pointer'}} onClick={onClose}></span>
        </header>
        </div>
      </div>
    </div>
  )
}

export default DrawerSearchPropId; 