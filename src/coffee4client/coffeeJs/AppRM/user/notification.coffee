appProf = angular.module 'appProfile',[]
appProf.controller 'ctrlNotifications',['$scope','$http',($scope,$http)->
  $scope.loading = true
  $scope.formData = vars.noteOpt or {}
  $scope.hasPn = true
  $scope.showDiagnosing = false
  # $scope.showVerifyEmail = false
  $scope.curStatus = vars.curStatus
  $scope.showBackdrop = vars.showBackdrop
  $scope.emailStatusList = vars.emailStatus
  $scope.hasGoogleService = true
  $scope.canRefresh = false
  $scope.refresh = false
  $scope.is63NewerVer = isNewVersion(vars.coreVer,'6.3.1')
  $scope.is65NewerVer = isNewVersion(vars.coreVer,'6.4.0')
  $scope.getEmailStatus = ()->
    $scope.emailStatus = $scope.emailStatusList[$scope.curStatus]
  $scope.getEmailStatus()

  $scope.openVerifyPopup = ()->
    to = '/1.5/settings/verify?d=/1.5/settings/editProfile&verify=v'
    $scope.loading = true
    setTimeout(()->
      $scope.loading = false
      $scope.$apply()
    , 1000)
    RMSrv.getPageContentIframe(to,'#callBackString', {transparent:true}, (val)->
      if (val is ':cancel')
        return
      try
      # 刷新显示，mylisting publish验证手机/邮箱
        ret = JSON.parse(val)
        if ret.emlV is true
          $scope.curStatus = 'unsubscribe'
          $scope.showBackdrop = false
          $scope.getEmailStatus()
        $scope.$apply()
      catch e
        console.error(e)
    )
  if vars.showverify
    $scope.openVerifyPopup()

  $scope.linkTo = (url)->
    RMSrv.getPageContent url, '#callBackString', {toolbar:false,hide:false}, (val)->
      if val is ':cancel'
        return
      else
        # console.log val
        # val = '{'addr':'22 Dersingham Crescent','address':'22 Dersingham Crescent, Markham, Ontario L3T 4P5, Canada','lat':43.8149393,'lng':-79.3618453,'place_id':'address.1672716579465562','st_num':'22','st':'Dersingham Crescent','zip':'L3T 4P5','city':'Markham','prov':'Ontario','cnty':'Canada'}'
        try
          $scope.$apply()
        catch e
          console.log e
          # console.log $scope.tmpData
  RMSrv.action('pushToken') if RMSrv.action?

  isAuthedRet=(ret='')->
    return ['authorized','granted'].indexOf(ret) > -1

  closeLoading = ()->
    setTimeout (->
      $scope.loading = false
      $scope.$apply()
      return
      ), 10

  $scope.checkNotification = ()->
    $scope.refresh = false
    RMSrv.permissions('notification','check',-1,(data)->
      $scope.hasPn = isAuthedRet(data)
      closeLoading()
    )
  if(RMSrv.hasGoogleService)
    RMSrv.hasGoogleService ((ret)->
      $scope.hasGoogleService = ret
      if(ret is true)
        $scope.checkNotification()
        if($scope.is63NewerVer and RMSrv.onAppStateChange)
          RMSrv.onAppStateChange((opt)->
            $scope.loading = true
            $scope.checkNotification()
          )
        else
          $scope.canRefresh = true
          closeLoading()
      else
        $scope.hasPn = false
        closeLoading()
    )
  RMSrv.whenReg ()->
    closeLoading()
  setTimeout (->
    return closeLoading()
  ), 3000
  $scope.openSetting=()->
    $scope.refresh = true
    RMSrv.openSettings()
  $scope.goBack = ()->
    goBack2({d:vars.d,isPopup:vars.isPopup})
  $scope.turnOffAll = ()->
    if $scope.post is true
      return
    $scope.post = true
    post = {colseAll : 1}
    for i of $scope.formData
      if /^edm/.test(i)
        $scope.formData[i] = 1
    saver = $http.post('/1.5/settings/subscription', post)
    saver.success (data)->
      $scope.message = data.message
      if data.ok
        # console.log data
        $scope.post = false
      else
        # alert 'Error'
        RMSrv.dialogAlert 'Error'
    saver.error (data)->
      $scope.message = data.message
      alert 'Error when turn off all'
  setBool = (modal,fld, v)->
    if $scope.post is true
      return
    $scope.post = true
  # 添加loading,改为传单个
    if v is 1 then v = 0 else v = 1
    post = {}
    post[fld] = v
    $scope.formData[fld] = v
    url='/1.5/settings/subscription'
    if modal is 'mobile' then url = '/1.5/settings/notification'
    saver = $http.post(url, post)
    saver.success (data)->
      $scope.message = data.message
      if data.ok
        # console.log data
        $scope.post = false
      else
        # alert 'Error'
        RMSrv.dialogAlert 'Error'
    saver.error (data)->
      $scope.message = data.message
      alert 'Error when saving pref'
  $scope.showModel = (model)->
    # $scope.loading = true
    if model is 'email'
      if $scope.curStatus is 'unverified'
        $scope.openVerifyPopup()
    else
      # version > 6.1.3,后台已经判断过version。
      # version 不支持，显示升级界面
      if not isNewVersion(vars.coreVer, '6.1.3') #判断版本是否支持
        $scope.showAppUpgrade = true
      else
        $scope.showDiagnosing = true
        if window.checkHasGooglePlay
          checkHasGooglePlay()
        else
          console.error 'checkHasGooglePlay not exist'

  $scope.emailAction = (action,model)->
    # $scope.loading = true
    if (model is 'mobile') or (action is 'verify')
      return $scope.showModel(model)
    post = {}
    if action is 'set'
      curStatus = 'resubscribe'
    else if action is 'unset'
      curStatus = 'unsubscribe'
    else
      return
    post.action = action
    url='/1.5/settings/receving'
    saver = $http.post(url, post)
    saver.success (data)->
      # $scope.message = data.message
      if data.ok
        flashMessage(data.message)
        $scope.curStatus = curStatus
        # 根据新状态更新 showBackdrop
        if curStatus in ['resubscribe']
          $scope.showBackdrop = true
        else if curStatus is 'unsubscribe'
          $scope.showBackdrop = false
        $scope.getEmailStatus()
      else
        # alert 'Error'
        RMSrv.dialogAlert 'Error'
    saver.error (data)->
      $scope.message = data.err
      alert 'Error when saving pref'

  window.onload=()->
    option = vars.option
    Object.getOwnPropertyNames(option).forEach((k)->
      v = option[k]
      v.flds.forEach((n,i)->
        if n.tag
          str = "##{n.tag}"
          dom = document.querySelector(str)
          if dom
            dom.addEventListener('click', (ret)->
              setBool(k,n.tag, $scope.formData[n.tag])
              )
      )
    )
  ]