appProf = angular.module 'appProfile',[]
appProf.controller 'ctrlNotifications',['$scope','$http',($scope,$http)->
  $scope.formData = vars.fld or {}
  $scope.loading = false
  $scope.formData.exuser = vars.exuser
  $scope.i = vars.i or null
  if (Array.isArray($scope.i) and ($scope.i.length > 1) and ($scope.i[0] is $scope.i[1]))
    $scope.i = $scope.i[0]
  $scope.linkTo = (url)->
    window.open(url,'_blank')
  $scope.curStatus = vars.curStatus
  $scope.showBackdrop = vars.showBackdrop
  $scope.emailStatusList = vars.emailStatus
  $scope.getEmailStatus = ()->
    $scope.emailStatus = $scope.emailStatusList[$scope.curStatus]
    if $scope.curStatus is 'unverified'
      $scope.emailStatus.description = 'Complete email verification to receive listing alert'
      $scope.emailStatus.word = 'Unverified'
  $scope.getEmailStatus()
  # $scope.turnOffAll = ()->
  #   if $scope.post is true
  #     return
  #   $scope.post = true
  #   post = {colseAll : 1}
  #   post.i = $scope.i
  #   for i of $scope.formData
  #     if /^edm/.test(i)
  #       $scope.formData[i] = 1
  #   saver = $http.post('/setting/subscription', post)
  #   saver.success (data)->
  #     $scope.message = data.message
  #     if data.ok
  #       console.log data
  #       $scope.post = false
  #       # 请求成功修改btn显示样式
  #       toggleHandles=document.getElementsByClassName('toggle-handle email')
  #       for toggle in toggleHandles
  #         toggle.style.transform='translate3d(0px, 0px, 0px)'
  #     else
  #       # alert 'Error'
  #       RMSrv.dialogAlert 'Error'
  #   saver.error (data)->
  #     $scope.message = data.message
  #     alert 'Error when turn off all'
  $scope.setBool = (fld, v)->
    if v is 1 then v = 0 else v = 1
    $scope.formData[fld] = v
    post = {}
    post[fld] = v
    post.i = $scope.i
    saver = $http.post('/setting/subscription', post)
    saver.success (data)->
      $scope.message = data.message
      if data.ok
        console.log data
      else
        # alert 'Error'
        RMSrv.dialogAlert 'Error'
    saver.error (data)->
      $scope.message = data.message
      alert 'Error when saving pref'

  $scope.emailAction = (action)->
    # $scope.loading = true
    # if (model is 'mobile') or (action is 'verify')
    #   return $scope.showModel(model)
    action = $scope.emailStatus.action
    post = {i:$scope.i}
    if action is 'set'
      curStatus = 'resubscribe'
    else if action is 'unset'
      curStatus = 'unsubscribe'
    else
      return
    post.action = action
    url='/1.5/settings/receving'
    saver = $http.post(url, post)
    saver.success (data)->
      # $scope.message = data.message
      if data.ok
        $scope.curStatus = curStatus
        # 根据新状态更新 showBackdrop
        if curStatus in ['resubscribe']
          $scope.showBackdrop = true
        else if curStatus is 'unsubscribe'
          $scope.showBackdrop = false
        $scope.getEmailStatus()
        alert(data.message)
      else
        # alert 'Error'
        RMSrv.dialogAlert 'Error'
    saver.error (data)->
      $scope.message = data.err
      alert 'Error when saving pref'
  ]
