<template lang="pug">
div
  div.post-photo(v-show="post.photos && post.photos.length && post.src!='news' && post.src != 'video' && !(post.wpHosts&& post.wpHosts.length)")
    div.img-wrapper(v-for="photo in post.photos")
      lazy-image(v-if="photo", :alt="post.tl", :src="photo", :imgstyle="'width: 100%;background-size: 100% 100%;'")
      span.search(v-if="post.src=='property' && post.status != 'new'",@click="goToProperty()")
        span.details {{_('Details','forum')}}
        span.fa.fa-search-plus
  div.post-top-container(v-if="post.adBottomPhoto" style="margin-top:20px;")
    img(:src="post.adBottomPhoto", @error="setDefaultPhoto($event.currentTarget)",@click="openAd(post.adBottom,1)")
    div.post-top-text {{_('AD','forum')}}
      span(v-if="dispVar.forumAdmin") {{post.vcad1}}
  div#bottomCpm(v-if="!post.adBottomPhoto && !post.noCpm && !fromForumList", style="padding:10px 0px; margin-top:20px;")
    cpm-banner(current-loc="NB2")
  div.post-tags(v-if="!post.del && !fromForumList")
    span.btn.btn-default(v-if="post.city||post.prov||post.cnty") {{_(post.city||post.prov||post.cnty)}}
    span.btn.btn-default(v-for="tag in post.tags", @click="onTagClicked(tag)")
      span(v-if="tag=='HOT'") {{_('HOT','forum')}}
      span(v-else) {{tag}}
  div.signupform(v-if="post.showRequestInfo && !fromForumList")
    sign-up-form(:user-form.sync="userForm", :title="post.requestInfotl", :owner="owner", :is-web="true", :need-wxid="true",:feedurl="feedurl")

  div.signupform(v-if="post.formid && !fromForumList")
    app-form-preview-modal(:formid="post.formid", nobar=true, is-web=true, :forumid="post._id", :fortl="post.tl")

  div.post-topics(v-if="relatedPosts && relatedPosts.length && !fromForumList")
    div.tp-title {{_("Topic","forum")}}: {{ post.tp ||relatedPosts[0].tp}}
    div(v-for="p in relatedPosts")
      div.topic-content(v-bind:class="{mainTopic: p.tp}", @click="openRelatedPost(p._id)")
        img(v-if="p.thumb",:src="p.thumb", @error="setDefaultPhoto($event.currentTarget)")
        div.line-wrap.line-wrap-2.content-right(v-if="p.thumb") {{p.tl}}
        div.line-wrap.line-wrap-2.full-width(v-else) {{p.tl}}
  div.post-topics(v-show="wecards && wecards.length && !fromForumList")
    div.tp-title {{_("Agent Recommended","forum")}}
    div(v-for="p in wecards")
      div.topic-content(@click="openWecard(p)")
        img(v-if="p.meta.img",:src="p.meta.img || '/img/logo.png'", @error="setDefaultPhoto($event.currentTarget)")
        div.content-right
          div.line-wrap.line-wrap-1 {{p.meta.title}}
          div.line-wrap.line-wrap-1.font14(style="color: #777;")
            | {{p.meta.desc}}
  div.post-topics(v-show="recommeds && recommeds.length && !fromForumList")
    div.tp-title {{_("RealMaster Hot Forum Articles","forum")}}
    div(v-for="p in recommeds")
      div.topic-content(@click="openRelatedPost(p._id)")
        img(v-if="p.thumb",:src="p.thumb", @error="setDefaultPhoto($event.currentTarget)")
        div.line-wrap.line-wrap-2.content-right(v-if="p.thumb") {{p.tl}}
        div.line-wrap.line-wrap-2.full-width(v-else) {{p.tl}}
  a.download-button(v-if="wId && !fromForumList", href="/app-download") {{_('Read More Posts')}}
  div(style="margin: 0 -10px;" v-if="!fromForumList")
    common-comments
  div(style="padding-bottom: 20px" v-if="!fromForumList")
    div.pull-spinner(v-show="loading", style='display:block')
  report-forum-form(v-if="showReportForm && !fromForumList",:owner="{vip:true}",:feedurl="reportForm.feedurl",:title="reportForm.title",:userForm="reportForm.userForm",:page="'forumDetail'")
  get-app-bar(:dtl-in-app="true", :w-dl="false", :owner.sync="dispVar.ownerData", v-show="share && !fromForumList")

</template>

<script>
import FlashMessage from '../frac/FlashMessage'
import pagedata_mixins from '..//pagedata_mixins'
import ConfirmWithMessage from './ConfirmWithMessage'
// import ForumCmntCard from './ForumCmntCard'
import forum_mixins from './forum_mixins'
import forum_common_mixins from './forum_common_mixins'
import rmsrv_mixins from '../rmsrv_mixins'
import SignUpForm from '../frac/SignUpForm.vue'
import AppFormPreviewModal from '../form/appFormPreviewModal.vue'
import LZString from 'lz-string'
import forum_detail_mixins from './forum_detail_mixins'
import cpmBanner from '../cpm/cpmBanner'
import LazyImage from '../frac/LazyImage.vue'
import commonComments from './commonComments.vue'
import ReportForumForm from './ReportForumForm.vue'
import GetAppBar from '../frac/GetAppBar'

export default {
  mixins: [pagedata_mixins,forum_detail_mixins, forum_mixins, forum_common_mixins,rmsrv_mixins],
  data () {
    return {
      relatedPosts:[],
      authorIsVip:false,
      showAuthorDialog: false,
      feedurl: '/1.5/form/forminput',
      userForm : {forumid:null, forumtl: null},
      owner:{vip:1},
      parentName: 'forumViewContent',
      recent_cmnts: [],
      display_cmnts:[],
      sticky_cmnts: [],
      post_sticky: null,
      wSign: true,
      wComment: true,
      wId: vars.wId || vars.share || false,
      wct: 1,
      loading: false,
      deleteMethod: null,
      cmntSortOrder:'desc',
      cmntSortKey: 'ts',
      aId: false,
      post_pn: 0,
      sizes:[],
      picUrls:[],
      edm: null,
      edmd: null,
      edmw: null,
      recommeds: [],
      wecards:[],
      userid:'',
      qrcd:false,
      dispVar: {
        userCity:{o:'Toronto', p:'Ontario'},
      },
      share:vars.share,
      bottomCpmShowed:false,
      bottomCpmData:{},
      showReportForm:false,
      reportForm:{
        postId:null,
        title: 'Report',
        feedurl:'/1.5/form/forminput',
        userForm:{
          m:'',
          tp:'feedback',
          subjectTp:'Forum Complaint',
          formid:'system',
          id:'',
          violation:'',
          tl:''
        }
      },
      fromForumList: vars.fromForumList || false,
    };
  },
  computed:{
    showSearchIcon() {
      return this.post.src=='property' && vars.src!=='property'
    }
  },
  methods: {
    closeReportForm() {
      this.showReportForm = false;
      this.reportForm.userForm.m = '';
      this.reportForm.userForm.violation = '';
      if (this.videoWrapper) {
        this.videoWrapper.style.display = 'block';
      }
      if (this.bodyWrapper) {
        this.bodyWrapper.style.overflow = 'auto';
      }
    },
    showMembers() {
      window.bus.$emit('showReaders');
    },
    openWecard(card) {
      var url=""
      if(card.meta.prop) {
        url = '/1.5/prop/detail?id='+card.meta.id+'&wDl=1&sgn=1&lang=zh-cn'+'&aid='+vars.aid;
      } else {
        url = '/1.5/wecard/prop/'+ this.userid + '/' + card._id + '?wDl=1&sgn=1';
      }
      window.location.href=url;
    },
    openRelatedPost(id) {
      if (vars.id) {
        window.location.href = this.appendDomain('/1.5/topics/details?share=1&id='+id)
      } else {
        this.closeForumView(id);
      }
    },
    sortCmnt(cmnt1, cmnt2) {
      if (cmnt1.sticky && !cmnt2.sticky) return -1;
      if (!cmnt1.sticky && cmnt2.sticky) return 1;
      if (cmnt1.sticky && cmnt2.sticky)
        return (new Date(cmnt2.mt) - new Date(cmnt1.mt))
      if (this.cmntSortKey === 'ts') {
        if (this.cmntSortOrder === 'desc')
          return (new Date(cmnt2.ts) - new Date(cmnt1.ts))
        if (this.cmntSortOrder === 'asc')
          return (new Date(cmnt1.ts) - new Date(cmnt2.ts))
      }
    },
    reverseCmnt() {
      if (this.cmntSortOrder =='desc')
        this.cmntSortOrder ='asc'
      else
        this.cmntSortOrder ='desc'
      this.recent_cmnts.sort(this.sortCmnt);
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },
    showAllComments() {
      this.display_cmnts  = this.recent_cmnts;
    },
    CollapseComments() {
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },
    onTagClicked(tag) {
      if (vars.share){
        return;
      }
      this.closeForumView(null, tag);
    },
    getRecommend() {
      var self = this;
      var params = {aid:vars.aid};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      if (this.post.similars) {
        var similars = this.post.similars;
        if (similars.length > 20) {
          similars = similars.slice(0,20);
        }
        params.similars = similars;
      }
      self.$http.post('/1.5/forum/recommend',params).then(
        function(ret) {
          if (ret.data.ok) {
            if (ret.data.posts && ret.data.posts.length) {
              self.recommeds = ret.data.posts;
              if (self.recommeds )
              var index = self.recommeds.findIndex(function(post) {
                return post._id == vars.id;
              });
              if (index >=0) {
                self.recommeds.splice(index,1);
              }
            }
            if (ret.data.wecards) {
              self.wecards = ret.data.wecards;
              self.userid = ret.data.userid;
            }
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },

    // deleteComment(des, index, action, wpHosts) {
    //   var self = this, delFlag;
    //   var params = {action:'del', index: index, wpHosts: wpHosts, city: self.post.city, prov: self.post.prov};
    //   if (action == 'delete') {
    //     params.del = des;
    //     delFlag = true;
    //   } else if (action == 'recover') {
    //     params.del = false;
    //     delFlag = false;
    //   }
    //   var des = des;
    //   self.manageComment(params, function(ret) {
    // //     self.post.cmnts[index].del = des || false;
    //     self.post.cmntl = ret.cmntl;
    //     // self.initPostDetail();
    //     var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
    //     if (recent_cmnt_index>=0) self.recent_cmnts[recent_cmnt_index].del = des || false;
    //     // var sticky_cmnt = self.sticky_cmnts.find(function(el) { return (el.idx === index)});
    //     // if (sticky_cmnt) sticky_cmnt.del = des || false;
    //   });
    // },
    // deleteCmntConfirm(action, index, wpHosts) {
    //   event.stopPropagation();
    //   var msg = this.$parent._(action + ' cmnt #') + (index + 1);
    //   var placeholder = this.$parent._('Enter delete reason');
    //   //add parentName for a workaround to not active the dialog in forumList.
    //   return window.bus.$emit('show-confirm-dialog', {onConfirm: this.deleteComment, id: index, wpHosts: wpHosts, parentName:'forumViewContent', action: action, msg:msg, placeholder: placeholder});
    // },

    // stickyComment(index, sticky) {
    //   var self = this;
    //   var params = {action:'sticky', index: index, sticky: sticky, city: self.post.city, prov: self.post.prov};
    //   self.manageComment(params, function(ret) {
    //     self.post.cmnts[index].sticky = sticky;
    //     self.post.cmntl = ret.cmntl;
    //     var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
    //     if (recent_cmnt_index>=0) {
    //       self.recent_cmnts[recent_cmnt_index].sticky = sticky;
    //     }
    //   });
    // },
    isElementInViewport(el) {
      if(!el) {
        return false;
      }
      let rect = el.getBoundingClientRect();
      return (
         rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
       );
    },
    scrollListener() {
      var self = this;
      var bottomCpm = document.getElementById('bottomCpm');
      //set time out to avoid get cpm twice;

      setTimeout(function() {
        if (!self.bottomCpmShowed && self.isElementInViewport(bottomCpm)){
          window.bus.$emit('get-current-ad',self.bottomCpmData);
          self.bottomCpmShowed =true;
        }
      },1000);
    },
  },
  mounted () {
    // if link from property, open exist or create a new one.
    this.videoWrapper = document.querySelector('.video-wrapper');
    this.bodyWrapper = document.querySelector('body');
    var self = this;
    self.post = Object.assign(self.post, vars.post);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.relatedPosts = vars.relatedPosts;
    this.authorIsVip = vars.authorIsVip
    if (!vars.inFrame && self.post.src !== 'video') {
      setTimeout(function() {
        document.querySelector('#postViewContainer').style.top = '44px';
      }, 100);
    }
    this.datasObj = {};
    if (vars.aid) {
      this.aId = true;
    }
    // this.getPageData(this.datas, this.datasObj, true);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if(vars.wct!=undefined) {
      this.wct = vars.wct;
    }
    self.dispVar = Object.assign(self.dispVar, vars.dispVar);
    self.bottomCpmData = {loc:"NB2", city:self.dispVar.userCity.o,prov:self.dispVar.userCity.p}
    self.userForm.nm = self.dispVar.sessionUser.nm || null;
    self.userForm.eml = self.dispVar.sessionUser.eml || null;
    self.userForm.mbl = self.dispVar.sessionUser.mbl || null;
    // self.userForm.forumid = self.post._id;
    // self.userForm.forumtl = self.post.tl;
    if (self.post.gid) {
      self.userForm.tp = 'groupforum';
    } else {
      self.userForm.tp = 'forum';
    }
    self.userForm.formid = 'system';
    self.userForm.id = self.post._id;

    self.initPostDetail();
    self.userForm.url = self.shareUrl;
    self.$nextTick(function () {
      self.formatNews(self.dispVar.isApp);
    });
    self.initCmnts();
    if (!self.dispVar.isApp && vars.share) {
      self.getRecommend();
    }
    if (vars.nobar) {
      document.querySelector('#postViewContainer').style.top = '0px'
    }
    let forumTsDiv = document.querySelector('#forumTs');
    if (forumTsDiv) {
      forumTsDiv.innerHTML = self.formatTs2(self.post.ts)
    }
    window.bus.$on('addComment',function(d) {
      self.post.cmnts = d.cmnts.slice(0);
      self.initCmnts();
      document.getElementById('recent_cmnts_container').scrollIntoView();
      self.post.cmntl = d.cmntl;
    });
    let bottomCpm = document.getElementById('bottomCpm');
    if (this.isElementInViewport(bottomCpm)){
      window.bus.$emit('get-current-ad',this.bottomCpmData);
      this.bottomCpmShowed =true;
    } else {
      this.contentWrapper = document.getElementById('postViewContainer');
      window.addEventListener("scroll", this.scrollListener);
    }
    window.bus.$on('report_comment', function (cmnt) {
      if (self.videoWrapper) {
        self.videoWrapper.style.display = 'none';
      }
      if (self.bodyWrapper) {
        self.bodyWrapper.style.overflow = 'hidden';
      }
      self.showReportForm = true;
      var reportForm = self.reportForm;
      var {protocol,host} = location;
      if (self.dispVar.sessionUser._id) {
        var {eml,nm} = self.dispVar.sessionUser;
        reportForm.userForm.eml = eml;
        reportForm.userForm.nm = nm;
      }
      reportForm.userForm.id = self.post._id;
      reportForm.userForm.url = protocol+'//'+host+'/1.5/forum/details?id='+self.post._id;
      reportForm.userForm.img = self.post.thumb;
      reportForm.userForm.tl = self.post.tl;
      reportForm.userForm.cmnt = cmnt.m;
      reportForm.userForm.cmntnm = cmnt.fornm;
      reportForm.userForm.cmntuid = cmnt.uid;
      self.reportForm = reportForm;
    });
  },
  components: {
    FlashMessage,
    ConfirmWithMessage,
    SignUpForm,
    AppFormPreviewModal,
    cpmBanner,
    LazyImage,
    commonComments,
    ReportForumForm,
    GetAppBar
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import '../../../style/sass/components/forum/detailContent.scss';
</style>
