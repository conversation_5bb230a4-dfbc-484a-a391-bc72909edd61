<template lang="pug">
div
  flash-message
  div#edm
    #saveModal.modal(v-show="showConfirm",tabindex='-1', role='dialog', aria-labelledby='saveModalLabel')
      .modal-dialog(role='document')
        .modal-content
          .modal-header
            button.close(type='button', data-dismiss='modal', aria-label='Close')
              span(aria-hidden='true',@click="showConfirm=false") ×
            h4#saveModalLabel.modal-title(v-show="showSendBtn") Are You Sure?
          .modal-body
            div title: {{title}}
            div checked lang: {{getSelectLangs()}}
            div checked Group: {{checkedGroup}}
            div checked Roles: {{checkedRoles}}
            div(style="font-weight:bold;color:#d43f3a;") total user to send {{sendUserNum}}, must send test email before send to users
            div(v-show="dispVar.edmApprove") This will send to backend and start send job
            div(style="font-weight:bold;color:#d43f3a;",v-show="!dispVar.edmApprove") only edmApprove user can 'send'
          .modal-footer
            button.btn.btn-default(type='button', data-dismiss='modal' @click="showConfirm=false") Close
            button.btn.btn-success(type='button', v-show="showSendBtn", data-dismiss='modal', :disabled="!checkedGroup || !dispVar.edmApprove", @click="send()") Send
    .row(style="margin-bottom: 10px;")
      label.col-sm-2.control-label(for='sid') Template Name
      .col-sm-4
        input#subject.form-control(type='text', placeholder='Unique Template Name', v-model='nm')
      .col-sm-4
        select.form-control(v-model="type")
          option.selected(v-for="eg in emlTypes" v-bind:value="eg.v")!="{{eg.k}}"
      label.col-sm-2.show-label(for='state') {{state}}({{sentQty}})

    div.nav.nav-tabs(role='tablist')
      input(type='checkbox',v-model='isZhCn')
      label 中文
      input(type='checkbox',v-model='isZh')
      label 繁体
      input(type='checkbox',v-model='isEn')
      label Eng
      input(type='checkbox',v-model='isKr')
      label 한국어
      input(type='checkbox',v-model='isNone')
      label None
      input(type='checkbox',v-model='checkEdmNo')
      label Check System Announcements

    div.nav.nav-tabs(role='tablist')
      label
        input(type='checkbox',v-model='sortBylts')
        | Send to recently active users
      br
      label Number of recently active users:
        input(type='number',v-model='lstNum')
    form
      .row
        .col-sm-10
          input#subject.form-control(type='text', placeholder='Subject', v-model='title')
        label.col-sm-2.control-label(for='subject') Subject
      .row(style="margin-top:15px")
        .col-sm-10
          p.help-block
            | 1. select one from right
            | 2. insert image use external link
          //- p
          //-   | {{id}} {{checkedRoles | json}} : {{engine}} st:{{st}}
          //-   | {{checkedGroup}}
      //- .row(style="margin-top:15px")
      //-   .col-sm-12
      //-     label(v-for="ur in sendToLangs", style="padding:0 10px;")
      //-       input(type='checkbox', value='{{lang}}', v-model='sendToLang')
      //-       |  {{lang}}
      .row(v-show="checkedGroup=='roles' || checkedGroup=='dbExtraRole'")
        .col-sm-12
          label(v-for="ur in userRoles", style="padding:0 10px;")
            //- TODO: @liurui fix :id and :value
            input(type='checkbox', :id="ur", :value="ur._id", v-model ='checkedRoles')
            | {{ur._id}} ({{ur.count}})
      .row(v-show="checkedGroup=='extra'")
        .col-sm-10
          input#subject.form-control(type='text', placeholder='Extra emails', v-model='extraEml')
        label.col-sm-2.control-label(for='subject') Extras Emails

      .row
        .col-sm-10
          #summernote
        .col-sm-2
          | Select a to type
          .radio(v-for="ug in userGroups")
            label
              input(type='radio',:id="ug.k", :value="ug.k", v-model='checkedGroup', @click="selectGrp(ug.k)")
              |  {{ug.v}}
      .row
        .col-sm-6
          input.form-control(type='email', placeholder='from email', v-model="fromEml")
        .col-sm-3
          select.form-control(v-model="engine")
            option.selected(v-for="eg in engines" v-bind:value="eg.v")!="{{eg.k}}"
        .col-sm-3
          input.form-control(type='time', placeholder='start time', v-model="st")
      .row(style="margin-top: 15px;")
        .col-sm-6
          .input-group
            input.form-control(type='email', placeholder='test email address', v-model="testEmlAddr")
            span.input-group-btn
              button.btn.btn-success(type='button', :disabled="!testEmlAddr", @click='sendTestEml()') Send test email
        .col-sm-3.action
          button.save-btn.btn.btn-success(type='button', @click='save()',:disabled="disableSaveRole()") Save template
          button.del-btn.btn.btn-danger(type='button', :disabled="disableRemoveRole()", @click='remove()') Remove
        .col-sm-3
          button.btn.btn-warning(v-show="dispVar.edmApprove" style="margin-right: 5px;",type='button', :disabled="!checkedGroup || !hasSentTest", @click="save('showSend')") Send!
          button.btn.btn-warning(style="margin-right: 5px;",type='button', @click="save('showCount')") Count
          button.btn.btn-warning(type='button',v-if="state",@click="forceReset()") Force Reset
</template>

<script>
import FlashMessage from '../components/frac/FlashMessage.vue'
import pagedata_mixins from './pagedata_mixins'

export default {
  mixins:[pagedata_mixins],

  data () {
    return {
      id: vars.id || null,
      nm: vars.nm || '',
      hasSentTest:false,
      engine: vars.engine || 'SES',
      engines:[
        {k:'Amazon Simple Email Service', v:'SES'},
        // {k:'sendGrid.com', v:'sendGrid'},
        {k:'gmail account', v:'gmail'},
        {k:'sendmail server',v:'sendmail'}
      ],
      type: vars.type || 'Tips',
      state: vars.state || '',
      sentQty : vars.sentQty || 0,
      // sendToLangs:[
      //   'en',
      //   'zh',
      //   'zh-cn',
      //   'none',
      // ],
      // sendToLang:[],
      emlTypes:[
        {k:'Help & Tips', v:'Tips'}, // use capital letter for matching in NoTips
        {k:'News', v:'News'},
        {k:'Event', v:'Event'},
        {k:'Recommend Listing', v:'Recommend'},
        {k:'Promotions', v:'Promo'},
      ],
      st: vars.st || new Date().getHours() + ':' + new Date().getMinutes(),
      testEmlAddr: vars.testEml, //default send to login user, if changed, unsublink will be incorrect.
      fromEml: vars.fromEml,
      curLang: vars.curLang,
      extraEml: vars.exteml || '',
      templateVals: vars.templateVals || {},
      title:'',
      contents:'',
      userRoles:vars.userRoles || ['vip','vip_plus','realtor','_admin','_dev'],
      checkedRoles:[],
      checkedGroup:'',
      userGroups: [
        {k:'roles', v:'by roles'},
        {k:'follower', v:'user who have followed a realtor'},
        //{k:'followee', v:'user who have followers'}, // if not supported don't show
        {k:'noFollow', v:'all clients not followed a realtor'},
        {k:'all', v:'all users'},
        {k:'extra', v:'extra addr list'},
        {k:'dbExtraAll', v:'DB extra user list'},
        {k:'dbExtraRole', v:'DB extra user by role'}
      ],
      isZhCn: false,
      isZh: false,
      isEn: false,
      isNone: false,
      showConfirm: false,
      showSendBtn: false,
      sendUserNum:0,
      checkEdmNo:vars.checkEdmNo || true,
      sortBylts:vars.sortBylts || false,
      lstNum:vars.lstNum || 0,
      dispVar:{
        defaultSendEmail:'<EMAIL>',
        edmAdmin: false,
        edmApprove: false,
        isAdmin: false
      },
      datas:[
        'defaultSendEmail',
        'edmAdmin','edmApprove','isAdmin'
      ]
    };
  },
  components: {
    FlashMessage
  },
  mounted() {
    let self  = this;
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      // NOTE: 现有的defaultEmail为接收邮件，添加默认发送邮件为defaultSendEmail
      // TODO: 目前只使用ses发送,以后需要获取不同engine的defaultSendEmail,根据选择的engine修改fromEml
      self.fromEml = self.fromEml || self.dispVar.defaultSendEmail;
    });
    /**
     * 初始化 summernote 编辑器，移除原生图片/视频上传，添加自定义图片上传插件
     * @see 公用 insertImage 函数，选择图片后插入到编辑器
     */
    // 自定义 summernote toolbar，仅保留基础功能，移除图片/视频上传
    const buttonList = [
      ['style', ['style']],
      ['font', ['bold', 'underline', 'clear']],
      ['fontname', ['fontname']],
      ['fontsize', ['fontsize']],
      ['color', ['color']],
      ['para', ['ul', 'ol', 'paragraph']],
      ['table', ['table']],
      ['insert', ['link', 'hr']],
      ['upLoadImg', ['select']],
      ['view', ['fullscreen', 'codeview', 'help']]
    ];
    // 注册自定义图片上传插件
    if (window.$ && window.$.summernote) {
      window.$.summernote.addPlugin({
        name: 'upLoadImg',
        /**
         * 初始化插件，添加按钮
         * @param {object} layoutInfo summernote layout 信息
         */
        init: function(layoutInfo) {
          // 可选：按钮激活样式
        },
        /**
         * 定义按钮
         */
        buttons: {
          select: function() {
            // 使用 font-awesome 图片图标
            return window.$.summernote.renderer.getTemplate().iconButton('fa fa-image', {
              event: 'select',
              title: '上传图片',
              hide: false
            });
          }
        },
        /**
         * 事件绑定
         */
        events: {
          /**
           * 点击图片上传按钮事件
           * @param {Event} event 
           * @param {object} editor 
           * @param {object} layoutInfo 
           */
          select: function(event, editor, layoutInfo) {
            // 失焦，关闭软键盘
            window.$('.summernote').summernote('blur');
            if (window.keyboard && window.keyboard.isVisible) {
              window.keyboard.close();
            }
            // 调用全局 insertImage 选择图片
            if (typeof window.insertImage === 'function') {
              var opt = {
                url :'/1.5/img/insert',
                width: 'calc(100% - 2px)',
                height: 'calc(100% - 5px)',
                toolbar: false,
                hide: true
              }
              window.insertImage(opt, function(val) {
                if (val === ':cancel') {
                  return;
                }
                try {
                  var ret = JSON.parse(val);
                  if (ret.picUrls && ret.picUrls.length) {
                    ret.picUrls.forEach(function(imgUrl) {
                      window.$('#summernote').summernote('insertImage', imgUrl, imgUrl);
                    });
                  }
                } catch (e) {
                  console.error(e);
                }
              });
            } else {
              alert('insertImage 函数未定义');
            }
          }
        }
      });
    }
    // 初始化 summernote
    window.$('#summernote').summernote({
      height: 400,
      toolbar: buttonList,
      /**
       * 自定义 onCreateLink 回调，插入超链接时自动为 a 标签添加 data-src="realmaster"
       * @param {string} sLinkUrl 链接地址
       * @returns {string} 返回处理后的链接地址
       */
      onCreateLink: function(sLinkUrl) {
        // summernote 的 createLink 只处理 href，不能直接加 data-src
        // 需在 onCreateLink 后的 onLinkInserted 回调中处理
        setTimeout(function() {
          // 获取编辑器内所有 a 标签，查找 href 匹配的，添加 data-src
          window.$('#summernote').next('.note-editor').find('.note-editable a').each(function() {
            if (window.$(this).attr('href') === sLinkUrl) {
              window.$(this).attr('data-src', 'realmaster');
            }
          });
        }, 0);
        // 保持原有处理逻辑
        if (sLinkUrl.indexOf('@') !== -1 && sLinkUrl.indexOf(':') === -1) {
          sLinkUrl = 'mailto:' + sLinkUrl;
        }
        return sLinkUrl;
      }
    });
    // 载入模板内容
    if (this.templateVals.default) {
      this.setCurwork(this.templateVals.default);
      if(vars.grp) {
        this.checkedGroup = vars.grp;
      }
      if(vars.roles) {
        this.checkedRoles = vars.roles;
      }
    } else {
      this.saveCurwork();
    }
    if (this.curLang) {
      var arr = this.curLang.split(',');
      if(arr.indexOf('zh')>=0)
          this.isZh = true;
      if(arr.indexOf('zh-cn')>=0)
          this.isZhCn = true;
      if(arr.indexOf('en')>=0)
          this.isEn = true;
      if(arr.indexOf('kr')>=0)
          this.isKr = true;
      if(arr.indexOf('none')>=0)
          this.isNone = true;
    }
  },
  methods: {
    disableSaveRole() {
      // edmAprove,edmAdmin,admin都可以编辑保存
      if (this.dispVar.isAdmin || this.dispVar.edmApprove || this.dispVar.edmAdmin) {
        return false;
      } else {
        return true;
      }
    },
    disableRemoveRole() {
      if (this.dispVar.isAdmin) {
        return false;
      } else {
        return !this.id || this.dispVar.edmApprove;
      }
    },
    getSendUserNum(tp) {
      let self = this;
      self.showSendBtn = false;
      this.$http.post('/edm/getSendUserNum',{id:self.id}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if (tp == 'showSend') {
              self.showSendBtn = true;
            }
            this.showConfirm = true;
            self.sendUserNum = ret.cnt;
          } else {
            alert(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    forceReset() {
      let self = this;
      let ok = window.confirm('Are you sure to reset this template?');
      if(ok) {
        this.$http.post('/edm/resetSendStauts',{id:self.id}).then(
          function (ret) {
            ret = ret.data;
            if (ret.ok) {
              self.state = null;
            } else {
              alert(ret.err);
              RMSrv.dialogAlert(ret.err);
            }
          },
          function (ret) {
            ajaxError(ret);
          }
        );
      }
    },
    selectGrp(grp) {
      this.checkedRoles = [];
      if (grp !='roles' && grp !='dbExtraRole' ) {
        return;
      }
      var self = this;

      this.$http.post('/edm/roles',{grp:grp}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.userRoles = ret.roles;
          } else {
            alert(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    getSelectLangs() {
      var arr = []
      if (this.isZh)
        arr.push('zh');
      if (this.isZhCn)
        arr.push('zh-cn');
      if (this.isEn)
        arr.push('en');
      if (this.isKr)
        arr.push('kr');
      if (this.isNone)
        arr.push('none');
      return arr.join(',');
    },
    sendTestEml () {
      this.hasSentTest = true;
      this.saveCurwork();
      // post to backend
      this.$http.post('/edm', {
        engine:this.engine,
        testEml:this.testEmlAddr,
        curLang: this.getSelectLangs(),
        fromEml: this.fromEml,
        id:this.id,
        templateVals:this.templateVals,
        test:1}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            console.log(ret);
            alert('send');
          } else {
            alert(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    saveCurwork () {
      this.templateVals.default = {
        title:this.title,
        contents: $('#summernote').code(),
        text:$($('#summernote').code()).text()
      };
    },
    initNextwork (lang) {
      this.templateVals[lang] = {
        title:'',
        contents:'',
        text:''
      };
    },
    setCurwork (nextWork) {
      if (!nextWork) {
        return;
      }
      var self = this;
      self.title  = nextWork.title;
      $('#summernote').code(nextWork.contents);
    },
    getSaveData () {
      var data = {
        save:1,
        type:this.type,
        nm:this.nm,
        id:this.id,
        grp:this.checkedGroup,
        roles:this.checkedRoles,
        engine:this.engine,
        exteml:this.extraEml,
        templateVals:this.templateVals,
        curLang:this.getSelectLangs(),
        fromEml:this.fromEml,
        testEml:this.testEmlAddr,
        // sendToLang:this.sendToLang,
        checkEdmNo:this.checkEdmNo,
        sortBylts:this.sortBylts,
        lstNum:this.lstNum,
        st:this.st
      };
      return data;
    },
    /**
     * 校验并处理 HTML 内容中的超链接，未带 data-src=realmaster 的 a 标签移除 href
     * @returns {Object} { html: string, invalidCount: number }
     */
    checkLinksDataSrc() {
      var html = window.$('#summernote').code();
      // 创建临时 DOM 解析
      var tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      var links = tempDiv.querySelectorAll('a');
      var invalidCount = 0;
      for (var link of links) {
        if (link.getAttribute('data-src') !== 'realmaster') {
          link.removeAttribute('href');
          invalidCount++;
        }
      }
      return { html: tempDiv.innerHTML, invalidCount };
    },
    // post to save work
    save (tp) {
      if (!this.isZh&&!this.isZhCn&&!this.isEn&&!this.isKr&&!this.isNone) {
        alert('no language selected')
        return;
      }
      this.saveCurwork();
      if (this.state === 'Editing') {
        var checkResult = this.checkLinksDataSrc();
        if (checkResult.invalidCount > 0) {
          alert('未保存，有 ' + checkResult.invalidCount + ' 个超链接信息不全，点击后不能跳转对应页面，请重新添加');
          // 更新编辑器内容
          window.$('#summernote').code(checkResult.html);
          // 同步 templateVals
          return this.saveCurwork();
        }
      }
      var data = this.getSaveData();
      this.$http.post('/edm', data).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if (tp == 'showCount') {
              window.bus.$emit('flash-message', 'Saved');
            } else {
              alert('Saved');
            }
            this.id = ret.id;
          } else {
            // alert(ret.err);
            if (tp == 'showCount') {
              window.bus.$emit('flash-message', ret.err);
            } else {
              RMSrv.dialogAlert(ret.err);
            }
          }
          if (tp) {
            this.getSendUserNum(tp);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    remove () {
      this.saveCurwork();
      this.$http.post('/edm', {remove:1, id:this.id, templateVals:{}}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            alert('Removed');
            window.location = '/edm/list'
          } else {
            alert(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    send () {
      this.saveCurwork();
      this.showConfirm = false;
      // post to backend
      var data = this.getSaveData();
      data.ready = 1;
      this.$http.post('/edm', data).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            alert(JSON.stringify(ret));
            // window.location = '/edm/list';
          } else {
            alert(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    }
  }
}


</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
#saveModal {
  display: block;
}
#edm{
  padding: 10px;
  padding-top: 60px;
}
.control-label{
  text-align: left;
}
.show-label{
  text-align: center;
}
.action{
  text-align: right;
}
.save-btn{
  margin-right: 5px;
}
form{
  padding-top: 25px;
}
.nav-tabs label {
  margin-right: 10px;
}
</style>
