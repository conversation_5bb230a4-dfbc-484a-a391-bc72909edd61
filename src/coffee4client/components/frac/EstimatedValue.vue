<template lang="pug">
  div.estimated-value-card(v-if="estimateInfo")
    div.flexCenter
      div
        div.card-title
          div {{_('Estimated Home Value')}}
          span.fa.fa-question-circle-o(@click="alertExplain()")
        div.estimateTime
          span(v-if="estimateInfo.estVal == 'Waiting'") {{_('Caculating, check later')}}
          span(v-else) {{_('Updated')}} {{estimateInfo.estMt}}
      div.viewBtn(@click="handleEvaluationClick")
        span(v-if="isSharedProp") {{_('View')}}
        span(v-else) {{_('Customize')}}
    div.value-range-container(v-if="estimateInfo.estVal != 'Waiting'")
      div.slider-container
        div.slider-tracks-container
          div.slider-track.blue(v-if="estimateInfo.blueWidth", :style="{ width: estimateInfo.blueWidth || '0%' }")
          div.slider-track.green(:style="{ width: estimateInfo.greenWidth}")
          div.slider-track.red(v-if="estimateInfo.redWidth", :style="{ width: estimateInfo.redWidth || '0%' }")
        div.estimate-point(:style="estimateInfo.soldPost ||  estimateInfo.estPost" :class="estimateInfo.direction + '-aligned'")
          div.estimate-value.blur-effect(v-if="isSharedProp") {{_('View')}}
          div.estimate-value(v-else) {{getPointLabel()}}
        // 添加灰色圆点，仅在soldPrice存在且有estPost时显示
        div.estimate-point.gray-point(v-if="estimateInfo.soldPrice && estimateInfo.estPost", :style="estimateInfo.estPost")
      div.value-labels.full-labels
        span.min-label(:style="{left: estimateInfo.blueWidth || '0px' }")
          span.value-text(v-if="isSharedProp && !estimateInfo.soldPost" class="blur-effect") {{_('View')}}
          span.value-text(v-else) {{formatPrice(estimateInfo.estMin)}}
          br
          span.label-text {{_('Low')}}
        // 只在有soldPrice时显示Est. Value标签
        span.est-label(v-if="estimateInfo.soldPrice" :style="estimateInfo.posLabel")
          span.value-text {{formatPrice(estimateInfo.estVal)}}
          br
          span.label-text {{_('Est. Value')}}
        span.max-label(:style="estimateInfo.redWidth? 'right:'+estimateInfo.redWidth : 'right: 0'")
          span.value-text(v-if="isSharedProp && !estimateInfo.soldPost" class="blur-effect") {{_('View')}}
          span.value-text(v-else) {{formatPrice(estimateInfo.estMax)}}
          br
          span.label-text {{_('High')}}
</template>
<script>
import filters from '../filters'
export default {
  filters:{
    propPrice:filters.propPrice,
    currency:filters.currency
  },
  props: {
    estimateInfo: {
      type:Object,
      default:function () {
        return {}
      }
    },
    isShare: {
      type:Boolean,
      default:true
    },
    fromShare:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
    }
  },
  computed: {
    isSharedProp(){
      if(this.fromShare || this.isShare) return true;
      return false;
    }
  },
  methods: {
    formatPrice(value) {
      if (!value) return ''
      if (this.estimateInfo.soldPost) {
        return '$' + this.$options.filters.propPrice(value,2)
      } else {
        return this.$options.filters.currency(value, '$', 0)
      }
    },
    getPointLabel() {
      if (this.estimateInfo.soldPost) {
        return this.$parent._('SOLD') + ' ' + this.$options.filters.currency(this.estimateInfo.soldPrice, '$', 0)
      } else {
        return this.$options.filters.currency(this.estimateInfo.estVal, '$', 0)
      }
    },
    alertExplain() {
      // 触发父组件的事件处理
      window.bus.$emit('alert-info','ESTIMATE')
    },
    goToAppDownload() {
      window.location.href = '/adPage/needAPP';
    },
    handleEvaluationClick() {
      if (this.isSharedProp) {
        this.goToAppDownload()
      } else {
        // 触发父组件的方法
        this.$emit('go-evaluation')
      }
    }
  }
}
</script>

<style lang="scss">
@import '../../../style/sass/apps/components/estimatedCard.scss';
</style> 