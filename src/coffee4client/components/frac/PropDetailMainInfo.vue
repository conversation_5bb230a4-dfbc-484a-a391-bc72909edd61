<template lang="pug">
div.mainInfo
  div.flexCenter(style="justify-content:space-between;",v-show="iskeyfactsinfo && showKeyFacts")
    div.basic-info {{_("Key Facts")}}
    div(style="width:44px;text-align: right;",@click="toggleKeyFacts")
      span.fa.ss.ss11.ss-angle-up(style="margin-right:0px;")
  div(:class="{'flexCenter positionRela': (iskeyfactsinfo && !showKeyFacts)}")
    div(:class="{'fieldContent': (iskeyfactsinfo && !showKeyFacts)}")
      div.row(:class="{'flexShrink': (iskeyfactsinfo && !showKeyFacts)}",v-for='row in rows')
        div(:class="{'infoBox': (!iskeyfactsinfo || showKeyFacts)}",v-for='block in row')
          div(:style="calcFieldWidth",v-if="block && block.tp == 'badroom'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}",@click="alertExplain('GARAGE')",data-sub="explain",data-query="type:garage") {{_('Layout')}}
              span.fa.fa-question-circle-o
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span.fa.ss.ss14.ss-rmbed(v-show="computeBdrms(prop) != ''")
              span {{computeBdrms(prop)}}
              span.fa.ss.ss14.ss-rmbath(v-show="computeBthrms(prop) != null")
              span {{computeBthrms(prop)}}
              span.fa.ss.ss14.ss-rmcar(v-show="computeGr(prop) != null")
              span() {{computeGr(prop)}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'size'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}",@click="alertExplain('SIZE')",data-sub="explain",data-query="type:size") {{_('Size')}}
              span.fa.fa-question-circle-o
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              div(v-show="prop.sqft || prop.rmSqft")
                span.link(v-if='!dispVar.isLoggedIn && !prop.sqft && prop.rmSqft',@click.stop='emitEvent("redirectPage")',data-sub="exchange", data-query='place:1') {{_('Estimate')}}
                span(v-else)
                  span(v-show="prop.sqft",style='white-space: nowrap;') {{parseSqft(prop.sqft)}}
                  span(v-show="prop.rmSqft && dispVar.isLoggedIn")
                    span(v-show='prop.sqft && /\-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)') &nbsp;({{parseSqft(prop.rmSqft)}})
                    span(v-show='!prop.sqft') {{parseSqft(prop.rmSqft)}} ({{_('Estimated')}})
                  span &nbsp;{{_('ft&sup2;')}}
              div.oneline(v-show="prop.front_ft") {{_('Lot')}} : {{prop.front_ft}} * {{prop.depth}} {{prop.lotsz_code}} {{prop.irreg}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'fce'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}",@click="alertExplain('EXPOSURE')",data-sub="explain",data-query="type:exposure") {{_('Exposure','propertylisting')}}
              span.fa.fa-question-circle-o
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span {{prop.fce}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'bltYr'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Built Year')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span(v-html='computedBltYr()')
              //- span(v-show='!(prop.bltYr||prop.age||prop.Age||prop.ConstructedDate) && prop.rmBltYr') &nbsp;({{_('Estimated')}})

          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'pricePerSqft' && prop.ListPriceSquareFoot")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Price')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span ${{prop.ListPriceSquareFoot}}/{{_('ft&sup2;')}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'pricePerSqft' && prop.pricePerSqft")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Estimated Price')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span ${{prop.pricePerSqft}}/{{_('ft&sup2;')}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'mfee'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Maint Fee / Month')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span.link(@click="emitEvent('openTBrowser','/1.5/tools/currency?p=' + prop.mfee + '&nn=1')",data-sub="exchange", data-query='target:mfee,place:1') ${{prop.mfee}}
              span(v-show="calcPerSqft(prop.mfee)") &nbsp;($~{{calcPerSqft(prop.mfee)}}/{{_('ft&sup2;')}})
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'tax'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Property Tax')}}
              span(v-show='prop.taxyr') &nbsp;/ {{prop.taxyr}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span.link(@click="emitEvent('openTBrowser','/1.5/tools/currency?p=' + prop.tax + '&nn=1')",data-sub="exchange", data-query='target:tax,place:1') ${{prop.tax}}
              span(v-show="calcPerSqft(prop.tax)") &nbsp;($~{{calcPerSqft(prop.tax)}}/{{_('ft&sup2;')}})
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'poss'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Possession Date')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span(v-if="prop.poss_date && prop.psn") {{prop.poss_date | dotdate}}&nbsp;({{Array.isArray(prop.psn)?prop.psn.join(','):prop.psn}})
              span(v-else-if='prop.poss_date') {{prop.poss_date | dotdate}}
              span(v-else) {{prop.rmPsn || prop.rmPsn_en}} {{Array.isArray(prop.psn)?prop.psn.join(','):prop.psn}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'storeys'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Building Storeys')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span(v-if="prop.rmStoreys || prop.condoStories") {{prop.rmStoreys || prop.condoStories}} ({{_('Estimated')}})
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'transferTax'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Transfer Tax')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal link': (!iskeyfactsinfo || showKeyFacts)}",@click="emitEvent('openTBrowser','/1.5/tools/transtax?p='+prop.lp+(prop.area_code == '01' ? '&trnt=1' : '&trnt=0') + '&nn=1')",data-sub="transfer tax", data-query='place:1')
              span.val {{closeCostMin() | currency('$', 0)}}
              | &nbsp; - &nbsp;
              span.val {{closeCostMax() | currency('$', 0)}}
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'condoUnits'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Units')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span {{prop.condoUnits}} ({{_('Estimated')}})
          div(:style="calcFieldWidth",v-else-if="block && block.tp == 'propType'")
            div.infoTitle.grey(:class="{'oneline': (iskeyfactsinfo && !showKeyFacts)}") {{_('Type')}}
            div(:class="{'oneline weightBold': (iskeyfactsinfo && !showKeyFacts), 'infoVal': (!iskeyfactsinfo || showKeyFacts)}")
              span {{prop.propType}}
          //- NOTE: if block && block.tp == 'a' || block.tp == 'b' DOES NOT WORK! need ()
          div(v-else-if="block && (estimateFields.includes(block.tp))")
            div.infoTitle.grey {{_(block.title)}} ({{_('engine')}}:{{_('accuracy')}})
            div.infoVal
              span(v-if="block.title != 'Built Year'") {{prop[block.tp]}} ({{block.tp.split('_')[1]}}:{{prop[block.tp + '_acu'] / 100}}%)
              span(v-else) {{prop[block.tp]}} ({{block.tp.split('_')[1]}}:{{prop[block.tp + '_acu']}})
          div(v-else-if="block && (estimateNotAcc.includes(block.tp))")
            div.infoTitle.grey {{_(block.title)}}
            div.infoVal
              span {{prop[block.tp]}}
      div(v-show="iskeyfactsinfo && !showKeyFacts" style="position: absolute;right: 43px;z-index: 2;height: 100%;width: 20px;background: linear-gradient(to right, transparent, white);")
    div.expandUp(v-show="iskeyfactsinfo && !showKeyFacts",@click="toggleKeyFacts")
      span.fa.ss.ss11.ss-angle-down(style="margin-right:0px;")
  div(style="display:none")
    span(v-for="(v,k) in strings") {{_(v[0], v[1])}}
</template>

<script>
import filters from '../filters'
import prop_mixins from '../prop_mixins'

export default {
  mixins:[prop_mixins],
  filters:{
    dotdate:filters.dotdate,
    currency:filters.currency
  },
  props:{
    taxList:{
      type:Array,
      default:[]
    },
    iskeyfactsinfo: {
      type: Boolean,
      default: false
    },
    dispVar:{
      type:Object,
      default:function() {
        return {
          isLoggedIn:false
        }
      }
    },
    prop:{
      type:Object,
      default:function() {
        return {
          pnSrc:[]
        }
      }
    },
    rows:{
      type:Array,
      default:function() {
        return []
      }
    },
  },
  data () {
    return {
      estimateFields: [],
      estimateNotAcc: ['ml_mt','dom_m1_days','sp_mn_result','sp_mn_min','sp_mn_max','sp_md_result','sp_md_min','sp_md_max'],
      strings:{
        ESTIMATE:['Estimated'],
        KEYFACTS: ['Key Facts']
      },
      showKeyFacts: false
    };
  },
  computed: {
    calcFieldWidth() {
      if (this.iskeyfactsinfo && !this.showKeyFacts) {
        const screenWidth = window.innerWidth;
        const totalWidth = screenWidth - 20 - 44; // 减去边距和按钮宽度
        // 折叠状态下，每个项目占1/3
        let width = `calc(${totalWidth}px / 2 - 25px)`
        return `margin-right: 10px;width: ${width}`;
      }
      return '';
    }
  },
  mounted () {
    var bus = window.bus, self = this;
    // bus.$on('calc-main-info-height', function (idx) {
      // setTimeout(() => {
      //   self.calcHeight();
      // }, 100);
    // });
    this.initEstimateFields()
  },
  methods: {
    alertExplain(k){
      window.bus.$emit('alert-info',k)
    },
    calcPerSqft(val){
      if(!(this.prop.sqft || this.prop.rmSqft)){
        return ;
      }
      var sqft;
      if(this.prop.sqft && !/\-/.test(this.prop.sqft)){
        sqft = this.prop.sqft
      }else{
        if(this.prop.rmSqft && !/\-/.test(this.prop.rmSqft)){
          sqft = this.prop.rmSqft
        }else{
          return ;
        }
      }
      if(typeof(sqft) == 'string'){
        sqft = sqft.replace(/\D/g,'')
      }
      sqft = Number(sqft)
      return (val/sqft).toFixed(2)
    },
    // computedBdrms(){
    //   var prop = this.prop;
    //   if (prop.rmbdrm) {
    //     return prop.rmbdrm;
    //   }
    //   return (prop.bdrms || prop.tbdrms || '')+(prop.br_plus?'+'+prop.br_plus:'')
    // },
    // computedBthrms(){
    //   var prop = this.prop;
    //   if (prop.rmbthrm) {
    //     return prop.rmbthrm;
    //   }
    //   return prop.bthrms;
    // },
    // computedGr(){
    //   var prop = this.prop;
    //   if (prop.rmgr) {
    //     return prop.rmgr;
    //   }
    //   return prop.tgr;
    // },
    // parseSqft(sqft){
    //   if (/\-/.test(sqft)) {
    //     return sqft;
    //   }
    //   if ('number' == typeof sqft) {
    //     sqft = ''+sqft;
    //   }
    //   if (/\./.test(sqft)) {
    //     return sqft.split('.')[0];
    //   }
    //   return sqft;
    // },
    // calcHeight(){
    //   var infoLeft = document.getElementsByClassName('infoLeft')[0],self=this;
    //   var infoRight = document.getElementsByClassName('infoRight')[0];
    //   if(!(infoLeft && infoRight)){
    //     setTimeout(() => {
    //       self.calcHeight()
    //     }, 100);
    //     return ;
    //   }
    //   var leftInfo = infoLeft.getElementsByClassName('infoVal');
    //   var rightInfo = infoRight.getElementsByClassName('infoVal');
    //   [0,1,2,3].forEach((idx)=>{
    //     if(leftInfo[idx] && rightInfo[idx]){
    //       if(leftInfo[idx].offsetHeight>rightInfo[idx].offsetHeight){
    //         rightInfo[idx].style.height = leftInfo[idx].offsetHeight +'px'
    //       }else if(leftInfo[idx].offsetHeight<rightInfo[idx].offsetHeight){
    //         leftInfo[idx].style.height = rightInfo[idx].offsetHeight +'px'
    //       }
    //     }
    //   })
    // },
    emitEvent(fn,status){
      window.bus.$emit('admin-fn',{fn,status})
    },
    addAccuracyFields(tp, nubmer = 10){
      for(let i=1;i<=nubmer;i++){
        this.estimateFields.push(`${tp}${i}`)
      }
    },
    initEstimateFields(){
      this.addAccuracyFields('rent_mn');
      this.addAccuracyFields('value_mn');
      this.addAccuracyFields('bltYr_mn');
      this.addAccuracyFields('sqft_mn');
      this.addAccuracyFields('sp_mn', 30);
      this.addAccuracyFields('sp_md', 30);
    },
    toggleKeyFacts() {
      this.showKeyFacts = !this.showKeyFacts;
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang='scss'>
.link{
  color: #428bca;
}
.mainInfo{
  font-size: 14px;
  .row{
    display: flex;
    justify-content: space-between;
  }
  .oneline{
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .infoVal{
    font-weight: bold;
    word-wrap: break-word;
  }
  .infoBox{
    width: 49%;
  }
  .infoBox > div{
    padding-top: 18px;
  }
  .infoTitle {
    padding-bottom: 3px;
  }
  .fa,.grey{
    color: #777;
  }
  .fa{
    margin: 0px 5px 0px 5px;
    vertical-align: text-bottom;
    font-size: 14px;
  }
  .ss-rmbed{
    margin-left: 0;
  }
  .flexCenter{
    display: flex;
    align-items: center;
  }
  .fieldContent{
    display: flex;
    width: calc(100% - 44px);
    overflow: auto;
  }
  .flexShrink{
    flex-shrink: 0;
  }
  .basic-info{
    font-weight: bold;
    font-size: 14px;
  }
  .expandUp{
    width: 44px;
    text-align: right;
    background-color: #fff;
    padding: 11px 0;
  }
  .weightBold{
    font-weight: bold;
  }
  .positionRela{
    position: relative;
  }
}
</style>
