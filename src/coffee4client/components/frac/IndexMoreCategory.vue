<template lang="pug">
div#indexMoreCategory
  page-spinner(:loading.sync="jumping")
  header.bar.bar-nav
    a.icon.fa.fa-back.pull-left(@click="showIndex()", href="javascript:void 0")
    h1.title {{_('All','tools')}}
  div.content
    div.tools(v-if="canSeeAdminTools")
      div.tl {{'Admin Manage Sytem Pages'}}
      div.tools-wrapper
        div.drawer(@click="goTo(dr)" v-for="dr in adminTools")
          //- img.img(src="/img/category-png/rmcat-admin.png")
          div.icon(:class="dr.icon?dr.icon:'fa fa-gears'", :style="{color:dr.color?dr.color:'black'}")
          div(:style="{color:dr.color?dr.color:'black'}") {{_(dr.t, dr.ctx)}}
    div.tools(v-if="canSeeAdminTools")
      div.tl {{'Admin Manage User Pages'}}
      div.tools-wrapper
        div.drawer(@click="goTo(dr)" v-for="dr in adminUserToolsPage")
          //- img.img(src="/img/category-png/rmcat-admin.png")
          div.icon(:class="dr.icon?dr.icon:'fa fa-gears'", :style="{color:dr.color?dr.color:'black'}")
          div(:style="{color:dr.color?dr.color:'black'}") {{_(dr.t, dr.ctx)}}
    div.tools(v-if="adminCmds.length > 0")
      div.tl {{_('Admin Command')}}
      div.tools-wrapper
        div.drawer(@click="goTo(dr)" v-for="dr in adminCmds")
          div.icon(:class="dr.icon?dr.icon:'fa fa-gears'", :style="{color:dr.color?dr.color:'black'}")
          div(:style="{color:dr.color?dr.color:'black'}") {{_(dr.t, dr.ctx)}}
    div.tools(v-if="adminInfo.length > 0")
      div.tl {{_('Admin Info/Stats page')}}
      div.tools-wrapper
        div.drawer(@click="goTo(dr)" v-for="dr in adminInfo")
          div.icon(:class="dr.icon?dr.icon:'fa fa-gears'", :style="{color:dr.color?dr.color:'black'}")
          div(:style="{color:dr.color?dr.color:'black'}") {{_(dr.t, dr.ctx)}}
    div.tools(v-if="devTools.length > 0")
      div.tl {{_('Developer tools')}}
      div.tools-wrapper
        div.drawer(@click="goTo(dr)" v-for="dr in devTools")
          div.icon(:class="dr.icon?dr.icon:'fa fa-gears'", :style="{color:dr.color?dr.color:'black'}")
          div(:style="{color:dr.color?dr.color:'black'}") {{_(dr.t, dr.ctx)}}
    div.tools(v-if="dispVar.isDevGroup")
      div.tl {{_('Test Server')}}
      div.testWrapper
        input.testInput(v-model="testserver", type="text")
        a.btn.btn-primary(@click="goToTestServer()") {{_('Go')}}
    div.tools(v-if="showAgentTools")
      div.tl {{_('Agent','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in agentTools")
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
    div.tools
      div.tl {{_('Featured','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in computedFeaturedTools" )
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
    div.tools
      div.tl {{_('Buy','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in buyerTools")
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
    div.tools
      div.tl {{_('Rent','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in rentTools")
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
    div.tools
      div.tl {{_('Landlord','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in landloardTools")
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
    div.tools
      div.tl {{_('Tools','tools')}}
      div.tools-wrapper
        div.drawer(@click="openLink(dr)" v-for="dr in otherTools" v-show="!dr.show || dispVar[dr.show]")
          div.img(:class='dr.class')
          div {{_(dr.t, dr.ctx)}}
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
import pagedata_mixins from '../pagedata_mixins'
import PageSpinner from './PageSpinner.vue'

import DoubleGoLink from './DoubleGoLink.vue'
export default {
  mixins:[rmsrv_mixins,pagedata_mixins],
  props: {
  },
  data () {
    return {
      testserver:'',
      hasWechat:true,
      adminCmds:vars.adminCmds || [],
      adminTools:vars.adminTools || [],
      adminUserToolsPage:vars.adminUserToolsPage || [],
      devTools:vars.devTools || [],
      adminInfo:vars.adminInfo || [],
      jumping:false,
      dispVar: {
        userFollowedRltr: {}, //this var used in children component swipe
        userCity:    {o:'Toronto'},
        isLoggedIn:  false,
        isRealtor:   false,
        langCover:   false,
        langCoverOldVer: false,
        newsAdmin:   false,
        lang:        'en',
        popup:       {},
        isApp:       false,
        isCip:       false,
        isNotCip: true,
        showForum:   false,
        hasNewMsg:   false,
        hasNewVer:   false,
        // showIndexReno:false,
        isAdmin:     false,
        // showSoldPriceBtn: false,
        hasFollowedVipRealtor: false,
        allowedShareSignProp: false,
        newFormInput:false,
        isProdSales:false,
        forumAdmin:false,
        formOrganizer:false,
        isInrealAdmin:false,
        marketAdmin:false,
      },
      datas:[
        // 'showIndexReno',
        'allowedShareSignProp',
        'userFollowedRltr',
        'hasFollowedVipRealtor',
        'userCity',
        'isLoggedIn',
        'isRealtor',
        'isDevGroup',
        'isVipPlus',
        'hasAid',
        'langCover',
        'newsAdmin',
        'lang',
        'isApp',
        'isCip',
        'hasNewMsg',
        'hasNewVer',
        // 'isAllowedPredict',
        // 'userFavGroups',
        'hasFollowedRealtor',
        'isAdmin',
        'isCrmAdmin',
        'isProdSales',
        'isProjAdmin',
        'forumAdmin',
        'formOrganizer',
        'isInrealAdmin',
        'marketAdmin',
      ],
      agentTools:[
        {
          t:'Showing',
          ctx:'showing',
          class:'sprite realtorIcon showing',
          url:'/1.5/showing?d=/1.5/more'
        },
        {
          t:'Contacts',
          ctx:'crm',
          class:'sprite realtorIcon contact',
          url:'/1.5/crm?d=/1.5/more'
        },
        {
          t:'My Listing',
          ctx:'tools',
          class:'sprite realtorIcon listing',
          url:'/1.5/promote/mylisting?d=/1.5/more'
        },
        {
          t:'Follower',
          ctx:'tools',
          url:'/1.5/crm/leads?d=/1.5/more',
          class:'sprite realtorIcon follower',
        },
        {
          t:'WeForm',
          ctx:'tools',
          class:'sprite30-30 sprite30-1-7',
          url:'/1.5/form/forminput?d=/1.5/more'
        },
        {
          t:'WePage',
          ctx:'tools',
          class:'sprite30-30 sprite30-1-2',
          url:'/1.5/wecard?d=/1.5/more'
        },
        {
          t:'WeSite',
          ctx:'wesite',
          class:'sprite30-30 sprite30-1-5',
          url:'/1.5/wesite?d=/1.5/more'
        },
        {
          t:'Group',
          ctx:'tools',
          class:'sprite30-30 sprite30-1-3',
          url:'/group?d=/1.5/more'
        },
        {
          t:'Get Buyers',
          ctx:'tools',
          class:'sprite30-30 sprite30-1-6',
          url:'/app-download?action=showSMB?d=/1.5/more'
        },
      ],
      featuredTools:[
        {
          t:'Agent',
          ctx:'tools',
          class:'sprite30-30 sprite30-3-3',
          url:'/1.5/realtor?d=/1.5/more'
        },
        {
          t:'Public School',
          loc: true,
          ctx:'tools',
          class:'sprite30-30 sprite30-3-8',
          url:'/1.5/school/schoolList?d=/1.5/more&tab=public'
        },
        {
          t:'Private School',
          loc: true,
          ctx:'tools',
          class:'sprite30-30 sprite30-3-5',
          url:'/1.5/school/schoolList?d=/1.5/more&tab=private'
        },
        {
          t:'University',
          loc: true,
          ctx:'tools',
          class:'sprite30-30 sprite30-3-8',
          url:'/1.5/school/schoolList?d=/1.5/more&tab=university'
        },
        {
          t:'Stigmatized',
          ctx:'tools',
          loc:true,
          needLogin:true,
          class:'sprite30-30 sprite30-3-7',
          // url:'/stigma/house?gps=1&d=/1.5/more'
          url:'/1.5/map/webMap?d=/1.5/more&tab=stigma'
        },
        {
          t:'Market Trends',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-3-6',
          url:'/1.5/prop/stats?d=/1.5/more'
        },
        {
          t:'Saved',
          ctx:'tools',
          login:true,
          class:'sprite30-30 sprite30-3-4',
          url:'/1.5/saves/properties?d=/1.5/more&grp=0'
        }
      ],
      buyerTools:[
        {
          t:'Residential',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-3',
          url:'/1.5/mapSearch?mode=list&d=/1.5/more'
        },

        {
          t:'PreCon',
          ctx:'tools',
          class:'sprite30-30 sprite30-2-6',
          url:'/1.5/mapSearch?d=/1.5/more&mode=list&mapmode=projects'
        },
        {
          t:'Assignment',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-6',
          url:'/1.5/mapSearch?d=/1.5/more&mode=list&mapmode=assignment'
        },
        {
          t:'Exclusive',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-1',
          url:'/1.5/mapSearch?mode=list&mapmode=exlisting&d=/1.5/more',
        },
        {
          t:'Commercial',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-2',
          url:'/1.5/mapSearch?mode=list&mapmode=commercial&d=/1.5/more'
        },
        {
          t:'Open House',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-5',
          url:'/1.5/mapSearch?mode=list&d=/1.5/more&oh=true'
        },
        {
          t:'Sold',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-4',
          url:'/1.5/mapSearch?d=/1.5/more&mode=map&mapmode=sold'
        }
      ],
      rentTools:[
        {
          t:'Residential',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-8',
          url:'/1.5/mapSearch?d=/1.5/more&mode=list&saletp=lease'
        },
        {
          t:'Leased',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-8',
          url:'/1.5/mapSearch?d=/1.5/more&mode=map&mapmode=sold&saletp=lease'
        },
        {
          t:'Landlord Rental',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-9',
          url:'/1.5/mapSearch?d=/1.5/more&mode=list&saletp=lease&mapmode=rent'
        },
        {
          t:'Exclusive Rental',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-2-10',
          url:'/1.5/mapSearch?d=/1.5/more&mode=list&saletp=lease&mapmode=exlisting'
        },
        {
          t:'Commercial',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-3-2',
          url:'/1.5/mapSearch?mode=list&mapmode=commercial&saletp=lease&d=/1.5/more'
        }
      ],
      landloardTools:[
        {
          t:'Sell',
          ctx:'tools',
          class:'sprite30-30 sprite30-5-3',
          url:'/1.5/landlord/sellhome?d=/1.5/more'
        },
        {
          t:'Post a Rental',
          ctx:'tools',
          loc:true,
          class:'sprite30-30 sprite30-1-10',
          url:'/1.5/promote/mylisting?d=/1.5/more'
        },
        {
          t:'Renovation',
          ctx:'tools',
          class:'sprite30-30 sprite30-1-9',
          browser:true,
          url:'https://www.realrenovation.ca?d=/1.5/more'
        },
        {
          t:'Estimate',
          ctx:'tools',
          class:'sprite30-30 sprite30-3-1',
          url:'/1.5/evaluation?d=/1.5/more'
        },
        {
          t:'Landlord',
          ctx:'tools',
          class:'sprite30-30 sprite30-5-4',
          url:'/1.5/landlord/owners?d=/1.5/more'
        }
      ],
      otherTools:[
        {t:'Commute',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-1',
          url:'/1.5/direction?d=/1.5/more',
          show:'isNotCip'
        },
        { t:'Rent Or Buy',
          ctx:'tools',
          class:'sprite30-30 sprite30-3-9',
          url:'/1.5/tools/rentorbuy?d=/1.5/more'},
        { t:'Transfer Tax',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-7',
          url:'/1.5/tools/transtax?d=/1.5/more'},
        { t:'Mortgage',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-5',
          url:'/1.5/tools/mortgage?d=/1.5/more'},
        { t:'Exchange',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-2',
          url:'/1.5/tools/currency?d=/1.5/more'},
        { t:'Measure',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-4',
          url:'/1.5/tools/measure?d=/1.5/more'},
        { t:'Length',
          ctx:'tools',
          class:'sprite30-30 sprite30-4-4',
          url:'/1.5/tools/length?d=/1.5/more'},
        { t:'Area',
          ctx:'measure',
          class:'sprite30-30 sprite30-4-3',
          url:'/1.5/tools/area?d=/1.5/more'},
        { t:'Temperature',
          ctx:'tools',
          class:'sprite30-30 sprite30-3-10',
          url:'/1.5/tools/temperature?d=/1.5/more'},
        { t:'Share',
          class:'sprite30-30 sprite30-4-6',
          url:'/app-download?action=showSMB?d=/1.5/more'
        }
      ]
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (self.dispVar.isCip) {
        self.dispVar.isNotCip = false;
      }
      if (RMSrv.hasWechat){
        RMSrv.hasWechat((has)=>{
          if (has!=null) {
            self.hasWechat = has
          }
        })
      }
    });
  },
  computed:{
    computedFeaturedTools:function(){
      if(this.hasWechat){
        return this.featuredTools
      } else {
        let tmp = this.featuredTools.slice()
        tmp.splice(0,1)
        return tmp
      }
    },
    showAgentTools:function(){
      if(this.hasWechat){
        return this.dispVar.isRealtor;
      }
      return this.dispVar.isRealtor && this.dispVar.isVipPlus;
    },
    canSeeAdminTools:function () {
      return this.dispVar.isAdmin||this.dispVar.isProdSales || this.dispVar.forumAdmin || this.dispVar.formOrganizer || this.dispVar.isInrealAdmin;
    },
    canSeeCommandTools:function() {
      return this.dispVar.isAdmin;
    }
  },
  methods: {
    openLink(dr) {
      dr.cat = 'morePage'
      if(dr.needLogin && !this.dispVar.isLoggedIn){
        return window.location = '/1.5/user/login'
      }
      if(dr.browser){
        return RMSrv.showInBrowser(dr.url)
      }
      this.goTo(dr);
    },
    goToTestServer() {
      if (this.testserver.trim() == '') {
        RMSrv.dialogAlert('Please enter a test server number');
        return;
      }
      this.$http.post('/1.5/index/testserver',{num:this.testserver}).then(
        function(ret) {
          if (ret.body.ok || ret.body.testUrl) {
            // since we didnot go through bootup seq, we have to modify this by hand
            var domain = ret.body.domain || 'realmaster.com';
            RMSrv.setItemObj({key:'lastLoadDomain',value:domain,stringify:false,store:false},()=>{
              alert(`url=${ret.body.testUrl} \n---domain=${domain}`)
              window.location.href = ret.body.testUrl;
            })
          } else {
            RMSrv.dialogAlert('No test server found.')
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      )
    },
    // goToMarket(){
    //   var url = '/1.5/promote/market';
    //   window.location = url;
    // },
    isActive (key) {
      return this.$parent.curKey == key;
      self.$http.post('/1.5/prop/detail/'+sid+'.json', {}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.initProp();
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          // console.error( "server-error" );
          RMSrv.dialogAlert( "server-error" );
        }
      );
    },
    showIndex(){
      window.location="/1.5/index";
    }
  },
  components: {
    DoubleGoLink,
    PageSpinner
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
@import "/css/sprite.min.css";
.content {
  padding-bottom: 160px;
}
.tools{
  margin-top: 10px;
  background: white;
}
.tools .tl{
  padding: 15px 10px 15px 10px;
  color: black;
  font-weight: bold;
  font-size: 15px;
  /* border-bottom: 1px solid #f1f1f1; */
}
.tools-wrapper {
  padding: 0px 10px;
  display: flex;
  flex-wrap: wrap;
}
.tools-wrapper .drawer{
  display: inline-block;
  width: 20%;
  text-align: center;
  vertical-align: top;
  color: #444;
  font-size: 12px;
  padding-bottom: 10px;
}
.tools-wrapper .drawer div {
  padding: 0px 5px 5px 5px;
  line-height: 14px;
}
.tools-wrapper  img{
  border-radius: unset;
  height: 30px;
  width: 30px;
}
.tools-wrapper .icon {
  font-size: 20px;
  margin-bottom: 5px;
}
/* .tools-wrapper > div:nth-child(-n+15){
  border-bottom: 1px solid #f1f1f1;
}
.tools-wrapper > div:not(:nth-child(3n+1)){
  border-left: 1px solid #f1f1f1;
} */
.action{
  background: white;
  margin-top: 10px;
}
.testWrapper {
  padding:0 10px 10px 10px;
}
.testInput {
  padding:3px;
  border:1px solid #ccc;
}
.sprite {
  background-image: url('/img/sprite.png');
  background-repeat: no-repeat;
}
.realtorIcon {
  width: 30px;
  height: 30px;
  margin: auto;
  display: inline-block;
}
.sprite.realtorIcon {
  background-size:420px;
}
.sprite.realtorIcon.showing {
  background-position: -53px -337px;
}
.sprite.realtorIcon.contact {
  background-position: -215px -296px;
}
.sprite.realtorIcon.follower {
  background-position: -174px -337px;
}
.sprite.realtorIcon.listing {
  background-position: -135px -337px;
}
</style>
