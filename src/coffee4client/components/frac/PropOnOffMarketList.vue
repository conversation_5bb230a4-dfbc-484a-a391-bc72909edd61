<template lang="pug">
div
  div.all-records(v-if="prop.ltp!=='assignment' && prophis && prophis.length")
    div.listing-change(style="padding-top:10px")
      div
        label(style="margin-bottom:0;padding-bottom:0;") {{_('Listing History')}}
      div(v-if="isTrebProp",style="font-size:12px;font-weight: normal;color:#777")
        span(v-if="isVip") {{_('Since 2003','prop')}}
        span(v-else) {{_('Last 7 years')}}
    div.exact
      i.fa.fa-check-circle-o.right
      | {{_('Trusted')}}
      i.fa.fa-exclamation-circle.warn
      | {{_('To be verified')}}
    prop-his-list(:his.sync='prophis',:searchById.sync='searchById', :isApp.sync='isApp', :isLoggedIn.sync='isLoggedIn')
  div.showViewAll(v-show="prop.ltp!=='assignment' && !showedAllRecords && his && his.length>3")
    a.btn.btn-outlined(href="javascript:;", @click="handleListingHistory()",data-sub="all history")
      | {{_('View All')}}
      span.fa.ss.ss9.ss-angle-down

  div.current(style="padding:0 10px 5px 10px;")
    div.listing-change(style="margin-bottom: 5px;")
      div
        label {{_('Listing Change','prop')}}
        span.statusTag(v-if="prop.saleTpTag",:class="{'red-bg': prop.tagColor === 'red', 'green-bg': prop.tagColor === 'green'}") {{prop.saleTpTag}}
      div.pricehist
        span.ts(style="font-size:12px;")
          | {{computedLstd | dotdate}}
          span(v-show="computedOffDate(prop)") &nbsp;-&nbsp;{{computedOffDate(prop) | dotdate}}
    div.row(:class="{'entry':priceHist.length>1 && !prop.sp, 'entrysp': priceHist.length>1 && prop.sp}",:style="priceHist.length == 1 ?'padding-left: 15px;':''")
      span.fa.ss.ss7.ss-circle
      span.ts(v-if="priceHist && priceHist.length") {{priceHist[0].ts | dotdate}}
      span.ts(v-else-if="computedOffDate(prop)") {{computedOffDate(prop) | dotdate}}
      span.ts(v-else) {{computedLstd | dotdate}}
      span.ts(v-if="(!priceHist || !priceHist.length) && isRMProp()") {{computedLstd | dotdate}}
      span.marginLft15(style="color:#000",v-if="priceHist && priceHist.length") {{getStatusText(priceHist[0])}}
      div(style="margin-left: auto")
        div.price(v-if="prop.sp") {{ prop.sp | currency('$', 0) }}
        div.price(v-else) {{ prop.lp || prop.lpr | currency('$', 0)}}
        div(v-show="prop.sp")
          div.throughPrice
            span.pull-right(v-if='prop.sp',:class="{'through':prop.sp}") {{(prop.lp || prop.lpr) | currency('$', 0)}}
    div.pricehist.row(v-for="h,i in priceHist.slice(1)",:class="{'entry':priceHist.length>0}", v-if="priceHist && priceHist.length > 1")
      span.fa.ss.ss7.ss-circle
      div.ts {{h.ts | dotdate}}
      span.marginLft15 {{getStatusText(h)}}
      div.pull-right(style="margin-left: auto;")
        div.lp.red(v-if="(h.s == 'New') && h.lp") {{h.lp | currency('$', 0)}}
        div.lp.red(v-if="h.n && !isNaN(h.n)") {{h.n | currency('$', 0)}}
</template>

<script>
import filters from '../filters'
import PropHisList from './PropHisList.vue'

export default {
  filters:{
    dotdate:filters.dotdate,
    currency:filters.currency,
  },
  components:{
    PropHisList:PropHisList
  },
  props:{
    isVip:{
      default:function () {
        return false
      }
    },
    showAll:{
      type:Boolean,
      default:function () {
        return false
      }
    },
    prop:{
      type:Object,
      default:function () {
        return {}
      }
    },
    priceHist:{
      type:Array,
      default:function() {
        return []
      }
    },
    his:{
      type:Array,
      default:function() {
        return []
      }
    },
    searchById:{
      type:Function
    },
    isApp:{
      type:Boolean
    },
    isLoggedIn:{
      type:Boolean
    },
    showedAllRecords:{
      type:Boolean,
      default: false
    }
  },
  data () {
    return {
      prophis:[],
      checkHisN: ['Sc', 'U', 'Ter', 'Ext', 'Sus', 'Cld', 'Exp', 'Haa', 'Cp', 'Wd', 'Clp', 'Sld', 'Dft', 'Lsd'],
      checkHisS: ['New', 'Sld', 'Dft', 'Lsd'],
      statusMap:{}
    };
  },
  mounted () {
    this.prophis = this.his;
    // if(!this.showAll) {
    //   this.prophis = this.his.slice(0,2)
    // }
    var now = new Date();
    var month = this.zeroPadLeft(now.getMonth()+1);
    this.nowNum = ''+now.getFullYear()+month+this.zeroPadLeft(now.getDate());
    this.statusMap = {
      'New': () => this._('New', 'prop on market'),
      'Sld': () => this._('Sold'),
      'Dft': () => this._('Deal Fall Through'),
      'Sc': () => this._('Sold in condition'),
      'Lsd': () => this._('Leased'),
      'U': () => this._('Unavailable'),
      'Ter': () => this._('Terminated'),
      'Ext': () => this._('Extended'),
      'Sus': () => this._('Suspended'),
      'Cld': () => this._('Closed', 'proplst'),
      'Exp': () => this._('Expired', 'proplst'),
      'Haa': () => this._('Hold all Action', 'proplst'),
      'Cp': () => this._('Cancel Protected', 'proplst'),
      'Wd': () => this._('Withdrawn', 'proplst'),
      'Clp': () => this._('Collapsed', 'proplst'),
      'Pnd': () => this._('Pending')
    };
  },
  watch:{
    his(val){
      this.prophis = this.his;
      if(!this.showAll) {
        this.prophis = this.his.slice(0,3)
      }
    },
  },
  computed: {
    isTrebProp:function () {
      if (!this.prop._id) {
        return false;
      }
      return /^TRB/.test(this.prop._id);
    },
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    saletpIsSale: function () {
      if (Array.isArray(this.prop.saletp_en)) {
        return /Sale/.test(this.prop.saletp_en.join(','));
      }
      return /Sale/.test(this.prop.saletp_en.toString());
    },
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    computedLstd(){
      return this.prop.onD
    }
  },
  methods: {
    zeroPadLeft(n){
      var len = (''+n).length;
      if (len<2) {
        return '0'+n;
      } else {
        return n;
      }
    },
    computedOffDate(prop){
      if(!prop){
        prop = this.prop;
      }
      // var prop = this.prop;
      if (prop.status_en == 'A' || prop.status == 'A') {
        if (prop.exp < parseInt(this.nowNum)) {
          return '';
        }
        return prop.exp;
      } else {
        return prop.offD || prop.sldd || prop.exp;
      }
    },
    computedPriceChange(h) {
      if(h.o) {
        return
      }
    },
    redirectLogin(isRoot){
      return this.$parent.redirectLogin(isRoot)
    },
    getStatusText(h) {
      if (!h) return '';
      if(h.s == 'Pc'){
        if(h.c > 0){
          return `+${h.c}`
        }
        return h.c
      }
      // 优先检查h.s的状态
      if (h.s && this.checkHisS.includes(h.s) && this.statusMap[h.s]) {
        return this.statusMap[h.s]();
      }
      // 如果h.s没有匹配，则检查h.n的状态
      if (h.n && this.checkHisN.includes(h.n) && this.statusMap[h.n]) {
        return this.statusMap[h.n]();
      }
      return '';
    },
    handleListingHistory(){
      this.$emit('show-listing-history');
    },
    isRMProp () {
      return /^RM/.test(this.prop.id);
    },
  }
}
</script>


<!-- Add "scoped"!!!!! attribute to limit CSS to this component only -->
<style scoped>
.exact .fa.warn{
  margin-left: 10px;
  color:#E7AE00;
}
.exact .fa.right{
  color:rgba(30,166,27,0.9);
}
.exact .fa{
  font-size: 11px;
  padding: 0px 5px 0 0;
}
.exact{
  color:#777;
  margin-bottom: 8px;
  vertical-align: top;
  font-size: 12px;
}
.current label{
  padding-top: 10px;
  padding-bottom: 5px;
}
.ss-caret-up-red {
  color: #E03131!important;
  font-size: 20px;
  margin-left: 5px;
}
.ss-caret-down {
  color: rgba(30,166,27,0.9)!important;
  font-size: 20px;
  margin-left: 5px;
}
.pricehist .pc{
  display: flex;
  align-items: center;
  justify-content: center;
}

.ss-circle {
  color: #c3c3c3;
  padding-right: 5px;
  font-size: 7px;
  position: absolute;
  left: 11px;
  margin-top:4px;
}
.row {
  display: flex;
  justify-content: normal;
  align-items: center;
}
.stp {
  padding: 2px 4px;
  margin-left: 5px;
  font-size: 12px;
  border-radius: 2px;
  line-height: 14px;
  color: #999;
  background-color: #f1f1f1;
}
.stp.active {
  background: rgba(30,166,27,0.9);
  color: white;
}
 .stp.sold{
  background: #e03131;
  opacity: 0.9
}
 .stp.inactive{
  background: #f1f1f1;
  color:#777;
}
.current .price {
  color: #000;
  float: right;
  font-size: 14px;
  margin-left: auto;
  font-weight: normal;
}
.on-off-market .current, .on-off-market .all-records {
  padding: 0 10px 8px 10px;
}
.on-off-market .current .ts {
  color:black;
}
.on-off-market .current .pricehist .ts {
  color: #777;
  font-weight: normal;
}
.on-off-market .current .pricehist .desc{
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-left: 3px;
}
.on-off-market .current label, .on-off-market .all-records label {
  color: #000;
  font-size: 15px !important;
}

.current .row.entry:before{
  content: '';
  float: left;
  display: inline-block;
  padding-left: 10px;
  height: 18px;
  padding-top: 0px;
  border-left: 0.5px solid #dddddd;
  margin-top: 15px;
}
.current .row.entrysp:before{
  content: '';
  float: left;
  display: inline-block;
  padding-left: 10px;
  height: 25px;
  padding-top: 0px;
  border-left: 0.5px solid #dddddd;
  margin-top: 15px;
}
.current > div.row.entry:nth-child(2):before{
  height: 18px;
  margin-top: 15px;
}
.current > div.row.entrysp:nth-child(2):before{
  height: 25px;
  margin-top: 15px;
}
.current > .row.entry:last-of-type:before{
  margin-bottom: 5px;
  height: 0px;
}
.current > .row.entrysp:last-of-type:before{
  margin-bottom: 5px;
  height: 0px;
}
.current .row, .current .pricehist{
  padding-left: 16px;
  line-height: 15px;
}
.current .row{
  align-items: normal !important;
}

.current .row.entry, .current .pricehist.entry, .current .row.entrysp{
  padding-left: 3.5px!important;
}
.pricehist .lp {
  color: #777;
  font-weight: normal;
}
.on-off-market .split {
  border-bottom: 2px solid #f1f1f1;
  margin: 5px;
  margin-right: 10px;
  margin-left: 15px;
}
.pricehist .sid span {
  padding-left: 10px;
  color: #999;
}
.through{
  text-decoration: line-through;
  line-height: 25px;
}
.listing-change {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.statusTag {
  font-size: 12px;
  padding: 0px 2px;
  border-radius: 2px;
  margin-left: 5px;
  color: #999;
  background-color: #f1f1f1;
}
.red-bg {
  background-color: #e03131;
  color: #fff;
}
.green-bg {
  background-color:rgba(30, 166, 27, 0.9);
  color: #fff;
}
.throughPrice{
  font-size: 14px;
  font-weight: normal;
  margin-left: auto;
  color: #999;
}
.marginLft15{
  margin-left: 5px;
  color: #777;
}
.showViewAll{
  height: 25px;
  text-align: center;
  margin: 7px 0 8px 0;
}
.showViewAll a {
  color: #a0a0a0;
  font-size: 13px;
  font-weight: normal;
  padding: 5px 8px 5px;
}
.showViewAll a .fa {
  margin-left: 3px;
  vertical-align: middle
}
</style>
