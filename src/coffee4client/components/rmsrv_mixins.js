var mixins = {
  created :  function () {
    var self = this;
    var hasSrv = typeof RMSrv !== "undefined" && RMSrv !== null;
    if (hasSrv) {
      // RMSrv.onReady(function() {
      self.ready = true;
      // });
    }
  },
  methods : {
    copyToClipboard(textToCopy){
    //  # navigator clipboard api needs a secure context (https)
      if (navigator.clipboard){
      //  # navigator clipboard api method'
        navigator.clipboard.writeText(textToCopy)
      }else{
      //  # text area method
        // if(verGTE('6.2.6') && RMSrv.copyToClipboard && !RMSrv.isIOS()){
        //   return RMSrv.copyToClipboard(textToCopy);
        // }
        var textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        textArea.id = 'IDArea';
        //  # make the textarea out of viewport
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy', true);
      }
      if(document.getElementById('IDArea')){
        document.getElementById('IDArea').remove();
      }
    },
    trackEventOnGoogle(category,action, label,value) {
      trackEventOnGoogle(category,action, label,value)
    },
    exMap (prop,url='') {
      if (!this.dispVar.isApp) {
        document.location.href='/adPage/needAPP';
      }
      var addr;//, url;
      if (prop.useMlatlng || (prop.daddr === "N" && prop.lat && prop.lng)) {
        addr = prop.lat + ',' + prop.lng;
      } else {
        //(prop.cmty_en || prop.cmty) +
        addr = (prop.city_en || prop.city || '') + ', ' + (prop.prov_en || prop.prov || '') + ', ' + (prop.cnty_en || prop.cnty || '');
        if (prop.daddr !== "N") {
          addr = (prop.addr || '')+ ", " + addr;
        } else {
          addr = addr + ', ' + prop.zip;
        }
      }
      // console.log(addr);
      url = url || this.dispVar.exMapURL;
      url = url + encodeURIComponent(addr);
      return RMSrv.showInBrowser(url);
    },
    goBack() {
      if (vars.src == 'nativeMap' || vars.src == 'nativeAutocomplete') {
        return window.rmCall(':ctx::cancel')
      }
      if(vars.d) {
        document.location.href = vars.d;
      } else {
        window.history.back();
      }
    },
    sprintf() {
      var args = arguments,
      string = args[0],
      i = 1;
      return string.replace(/%((%)|s|d)/g, function (m) {
        // m is the matched format, e.g. %s, %d
        var val = null;
        if (m[2]) {
            val = m[2];
        } else {
            val = args[i];
            // A switch statement so that the formatter can be extended. Default is %s
            switch (m) {
                case '%d':
                    val = parseFloat(val);
                    if (isNaN(val)) {
                        val = 0;
                    }
                    break;
            }
            i++;
        }
        return val;
      });
    },
    appendLocToUrl(url, city, opt){
      if (city.lat != null && city.lng != null) {
        var fChar = url.indexOf('?')>0?'&':'?';
        url += fChar+'loc='+city.lat+','+city.lng
        return url;
      }
      return url;
    },
    appendCityToUrl(url, city, opt={}){
      if (!city.o) {
        return url;
      }
      var fChar = url.indexOf('?')>0?'&':'?';
      url += fChar+'city='+city.o;
      if (city.p) {
        url += '&prov='+city.p;
      }
      if (city.n) {
        url += '&cityName='+city.n;
      }
      if (city.pn) {
        url += '&provName='+city.pn;
      }
      if (city.lat) {
        url += '&lat='+city.lat;
      }
      if (city.lng) {
        url += '&lng='+city.lng;
      }
      if (opt.saletp) {
        url += '&saletp='+opt.saletp;
      }
      if (opt.dom != null) {
        url += '&dom='+opt.dom;
      }
      if (opt.oh != null) {
        url += '&oh='+true;
      }
      if (opt.ptype) {
        url += '&ptype='+opt.ptype;
      }
      return url;
    },
    appendDomain(url){
      // var tmp    = document.createElement ('a');
      // tmp.href   = document.URL;
      // var domain = tmp.hostname;
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    clickedAd (b,category,index, tp) {
      // alert(tp);
      var url = b._id;
      // alert(url+':'+b.inapp)
      if (b.inapp) {
        return window.location = b.tgt;
      }
      if(category) {
        trackEventOnGoogle(category,'clickPos'+index);
      }
      url = this.appendDomain('/adJump/' + url);
      RMSrv.showInBrowser(url);
    },
    goTo(dr,prop={}) {
      // console.log(dr);
      if(dr.googleCat && dr.googleAction) {
        trackEventOnGoogle(dr.googleCat,dr.googleAction);
      }
      if(dr.t) {
        let action = dr.t;
        if (dr.t=='For Rent') {
          action = 'Lease'
        }
        var cat = dr.cat || 'homeTopDrawer'
        trackEventOnGoogle(cat,'open'+action);
      }
      var url = dr.url,
          ipb = dr.ipb;
      var self = this;
      if (!url) {
        return;
      }
      // alert(this.jumping)
      if (dr.login && !this.dispVar.isLoggedIn) {
        return window.location = '/1.5/user/login'
      }
      // NOTE: in IOS goto login and back didnot refresh page; this.jumping still true;
      if (this.jumping) {
        return;
      }
      if (dr.vipplus && !this.dispVar.isVipPlus) {
        return this.confirmVip();
      }
      if (dr.trebuser && !this.dispVar.hasAid) {
        return this.confirmTreb();
      }
      if (dr.t == 'Sold') {
        // alert(this.dispVar.showSoldPriceBtn && !this.dispVar.showSoldPrice)
        // if (this.dispVar.showSoldPriceBtn) {
        //   this.confirmNotAvailable();
        //   return;
        // }
      }
      if (dr.t == 'Agent' && !dr.direct) {
        if (!this.dispVar.isLoggedIn) {
          return window.location = '/1.5/user/login';
        }
        /**
         * 检查用户权限
         * 只有 isProdSales 或 marketAdmin 用户才能访问 Agent 页面
         * 其他用户返回 null
         */
        if (this.dispVar.isProdSales || this.dispVar.marketAdmin) {
          return window.location = dr.url;
        }
        return null;
        // if (!this.dispVar.isLoggedIn) {
        //   return window.location = '/1.5/user/login';
        // } else if (this.dispVar.hasFollowedVipRealtor && !this.dispVar.isRealtor) {
        //   return this.openDrawer(dr, 2, 0);
        // } else if (this.dispVar.isRealtor) {
        //   // return this.indexMode = 'realtor';
        //   //TODO: redirect
        //   return window.location = dr.url;
        // } else {
        //   url = '/1.5/user/follow';
        //   var city =  this.dispVar.userCity;
        //   url = this.appendCityToUrl(url, city);
        //   return window.location = url;
        // }
      }
      if (dr.t == 'Services') {
        // console.log('here');
        return window.location = url;
        // return this.openDrawer(dr, 2, 1);
      }
      if (ipb == 1) {
        // return this.tbrowser(url);
        var cfg2 = {
          backButton: {
            image: 'back',
            imagePressed: 'back_pressed',
            align: 'left',
            event: 'backPressed'
          }
        };
        if (dr.jumpUrl) {
          var url = dr.jumpUrl+'?url='+encodeURIComponent(dr.url);
        }
        return this.tbrowser(url, cfg2);
        // return RMSrv.showInBrowser(url);
      } else if (ipb == 3){
        return RMSrv.scanQR('/1.5/iframe?u=');
      } else if (ipb == 4) {
        return RMSrv.showInBrowser(url);
      }
      //public school special case set city
      if (dr.loc == true) {
        var city =  this.dispVar.userCity;
        url = this.appendCityToUrl(url, city);
      }
      if (dr.projQuery) {
        var q = this.dispVar.projLastQuery || {};
        url += '?';
        for (let i of ['city','prov','mode','tp1']) {
          if (q[i]) {
            url += i+'='+q[i];
            url += '&'+i+'Name='+q[i+'Name'];
            url += '&';
          }
        }
        // console.log(url);
        // return;
      }
      if (dr.gps == true) {
        var city =  this.dispVar.userCity;
        url = this.appendLocToUrl(url, city);
      }
      if (dr.loccmty == true) {
        url = this.appendCityToUrl(url, prop);
      }
      if (dr.tpName) {
        url += '&tpName='+this._(dr.t, dr.ctx);
      }
      this.jumping = false;
      // if (/mapSearch/.test(url) && /iPhone/.test(navigator.userAgent) && window.innerWidth > 413) {
      //   RMSrv.clearCache();
      // }
      // todo 如果没有coreVer，第一次可以跳转，后续不行，是否应该第一次就不让跳转
      if (!(self.isNewerVer(self.dispVar.coreVer, '5.8.0') && /mapSearch|autocomplete/.test(url) && !/mode=list/.test(url))) {
        // setTimeout(function () {
        self.jumping = true;
        // }, 90);
      }
      setTimeout(function () {
        window.location = url;
      }, 10);
    },
    clearCache(){
      // console.log('clearCache');
      if (typeof RMSrv !== "undefined" && RMSrv !== null && RMSrv.clearCache) {
        RMSrv.clearCache();
      }
    },
    confirmNoFnLn:function (lang,optTip,optTl,jpurl) {
      var optTip = optTip?optTip:"To be presented here, please complete your personal profile.";
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Later');
      var seemore = fn('Do it Now');
      var optTl = optTl?optTl:"";
      function _doShowEditProfile(idx) {
        var url = '/1.5/settings/editProfile';
        if (idx+'' == '2') {
          window.location = url;
        } else if (jpurl) {
          window.location = jpurl;
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowEditProfile, optTl, [later, seemore]);
    },
    confirmSettings:function (optTip,cb) {
      optTip = optTip?optTip:"To find nearby houses and schools you need to enable location";
      var fn = this._?this._:this.$parent._;
      
      var tip = fn(optTip);
      var later = fn('Later');
      var gotosettings = fn('Go to settings');
      var optTl = optTl?optTl:"";
      function _doShowGotoSettings(idx) {
        if (idx+'' == '2') {
           RMSrv.openSettings()
        } else {
          if ('function' == typeof cb) {
            cb()
          }
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowGotoSettings, optTl, [later, gotosettings]);
    },
    confirmNotAvailable(optTip){
      optTip = optTip?optTip:"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";
      // alert(optTip);
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      // var later = fn('Later');
      var gotosettings = fn('I Know');
      var optTl = optTl?optTl:"";
      function _doShowGotoSettings(idx) {
        // window.location = '/1.5/index';
      }
      return RMSrv.dialogConfirm(tip, _doShowGotoSettings, optTl, [gotosettings]);
    },
    confirmUpgrade: function(lang,optTip){
      optTip = optTip?optTip:"Only available in new version! Upgrade and get more advanced features.";
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Later');
      var seemore = fn('Upgrade');
      var url = this.appendDomain('/app-download');
      function _doShowVip(idx) {
        if (lang) {
          url += "?lang=" + lang;
        }
        if (idx+'' == '2') {
          RMSrv.closeAndRedirectRoot(url);
        }
      }
      // console.log('confirmUpgrade',tip,seemore);
      return RMSrv.dialogConfirm(tip, _doShowVip, "Upgrade", [later, seemore]);
    },
    confirmVip: function (lang, optTip) {
      optTip = optTip?optTip:"Available only for Premium VIP user! Upgrade and get more advanced features.";
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Later');
      var seemore = fn('See More');
      function _doShowVip(idx) {
        // var url = '/event?tpl=getvip';
        // if (lang) {
        //   url += "&lang=" + lang;
        // }
        var url = 'https://www.realmaster.ca/membership'
        if (idx+'' == '2') {
          RMSrv.showInBrowser(url);
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowVip, "VIP", [later, seemore]);
    },
    tbrowser : function (url, cfg2={}) {
      var cfg, ref, srv, self = this;
      cfg = {
        toolbar: {
          height: 44,
          color: '#E03131'
        },
        closeButton: {
          image: 'close',
          align: 'right',
          event: 'closePressed'
        },
        fullscreen: false
      };
      cfg = Object.assign(cfg, cfg2);
      // return alert(url);
      RMSrv.openTBrowser(url, cfg);
      // if(this.ready){
      //   ref = RMSrv.openTBrowser(url, cfg);
      //   return ref != null ? ref.addEventListener('closePressed', function() {
      //     if (ref != null) {
      //       ref.close();
      //     }
      //   }) : void 0;
      // }
    }
  }
}
export default mixins;
