

{{&
  var {css,js,tabs,id,d} = it;
}}
{{~ css :c:idx}}
<link rel="stylesheet" href="{{= c}}">
{{~}}
<div id="loader" class="loader-wrapper"><div class="loader"></div></div>
<div id="forumHome" style='height: 100%;overflow: hidden;' v-cloak>
  <div id="forum-main">
    <flash-message></flash-message>
    <div style="display:none" id="rmPageBarColor">:name:mainTheme</div>
    <header class="bar bar-nav {{? it.appmode == 'rm'}}rm{{?}}{{? it.appmode == 'mls'}}mls{{?}}" id="header-bar">
        <span class="bar-right pull-right" v-if="dispVar.forumAdmin" @click.stop="goToEdit()">{{-, POST,forum}}</span>
        <span class="fa fa-rmsearch pull-right" v-if="dispVar.isAdmin" @click.stop="openSearchBar()"></span>
        <h1 class="title" @click.stop="dropDownClicked()"><span>{{getTranslate(title)}}</span><a><span class="icon icon-caret"></span></a></h1>
    </header>
    <div class="forum-searchbar" v-show="showSearchbar"><span class="icon icon-close" @click="closeSearchbar()"></span><input v-model="search" :placeholder="getTranslate('enter')"><span class="icon icon-search" @click="searchForums()"></span></div>
    <div class="mask" v-show="showMask" @click="clickMask()"></div>
    <div class="popover" id="toolsPop" v-if="showMenu">
        <header class="bar bar-nav">
            <h1 class="title">{{-, Select Menu,forum}}</h1>
            <ul class="table-view" style="padding-top:44px;">
                <li class="table-view-cell" @click="selectFilter('all')">{{getTranslate('News')}}</li>
                <li class="table-view-cell" @click="selectFilter('topic')">{{getTranslate('Topic')}}</li>
                <li class="table-view-cell" @click="selectFilter('my_group')">{{getTranslate('My Group')}}</li>
                <li class="table-view-cell" @click="selectFilter('my_post')">{{getTranslate('My Post')}}</li>
                <li class="table-view-cell" @click="selectFilter('my_reply')">{{getTranslate('My Reply')}}</li>
                <li class="table-view-cell" @click="selectFilter('my_favourite')">{{getTranslate('My Favourite')}}</li>
                <li class="table-view-cell" v-if="dispVar.forumAdmin || dispVar.sessionUser.fas" @click="selectFilter('post_admin')">{{- My Admin Posts}}</li>
            </ul>
        </header>
    </div>
    <div class="frame" v-show="!showedForumTerms">
        <div class="iframe-wrapper" :style="{width:computedWith}">
            <div class="iframe">
                <p class="termTitle">Code of User Conduct</p>
                <P>You agree not to submit any information, material or content that:</P>
                <p>(i) may create a risk of harm, loss, physical or mental injury, emotional distress, death, disability, disfigurement, or physical or mental illness to you, to any other person, or to any animal;</p>
                <p>(ii) may create a risk of any other loss or damage to any person or property;</p>
                <p>(iii) seeks to harm or exploit children by exposing them to inappropriate content, asking for personally identifiable details or otherwise;</p>
                <p>(iv) may constitute or contribute to a crime or tort;</p>
                <p>(v) contains any information or content that we deem to be unlawful, harmful, abusive, racially or ethnically offensive, defamatory, infringing, invasive of personal privacy or publicity rights, harassing, humiliating to other people (publicly or otherwise), libelous, threatening, hostile, violent, or that which provokes violence or hostility, profane, or otherwise objectionable;</p>
                <p>(vi) contains any information which discriminates against others based on race, religion, sex, sexual orientation, age, disability, ancestry or national origin;</p>
                <p>(vii) contains any information or content that is illegal (including, without limitation, the disclosure of insider information under securities law or of another party’s trade secrets);</p>
                <p>(viii) contains any information or content that you do not have a right to make available under any law or under contractual or fiduciary relationships;</p>
                <p>or (ix) contains any information or content that you know is not correct and current.</p>
                <p>You agree that any User Content does not and will not violate third-party rights of any kind, including without limitation any Intellectual Property Rights (as defined below) or rights of privacy or publicity.</p>
                <p>Realmaster reserves the right, but is not obligated, to reject and/or remove any User Content that Realmaster believes, in its sole discretion, violates these provisions.</p>
            </div>
        </div>
        <div class="canReadLangs" v-if="dispVar.isLoggedIn"><span>{{- Can read}}</span><span><input type="checkbox" @click="handleForumLangs('Mandarin')" :checked="forumLangs.indexOf('Mandarin')&gt;-1"><label>中</label></span><span><input type="checkbox" @click="handleForumLangs('English')" :checked="forumLangs.indexOf('English')&gt;-1"><label>EN</label></span></div>
        <div class="btn-wrapper"><button class="btn btn-positive btn-block btn-long" @click="agreeTerms()">{{- Confirm}}</button></div>
    </div>
    <block-popup :flag-post='flagPost'></block-popup>
    <report-forum-form v-if="showReportForm" :owner="{vip:true}" :feedurl="reportForm.feedurl" :title="reportForm.title" :user-form="reportForm.userForm"></report-forum-form>
    <div id="forum-containter" v-on:scroll="listScrolled" :class="{news:title == 'News'}">
        <div class="xpull">
            <div class="xpull__start-msg">
                <div class="xpull__arrow"></div>
            </div>
            <div class="xpull__spinner">
                <div class="xpull__spinner-circle"></div>
            </div>
            <check-notification :class-name="'forum'"></check-notification>
            <div v-for="post in allPosts" track-by="$index" :key="post._id">
                <forum-summary-card parent-page="forum" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag"></forum-summary-card>
            </div>
        </div>
        <div style="padding-bottom: 20px">
            <div class="pull-spinner" v-if="loading" style="display:block"></div>
        </div>
    </div>
    <div class="bar bar-standard bar-tab tab-list" v-if="displayPage=='all'|| hasTag">
      <div class="tag-filter" v-if="hasTag">
        <span>{{-, Filter,forum}}:</span>
        <span class="red-button blue" @click="clearTag('tag')" style="padding-left:5px;" v-if="tag">
          <span v-if="tag=='HOT'">{{-, HOT,forum}}</span>
          <span v-else>{{tag}}</span>
          <span class="icon icon-close"></span>
        </span>
        <span class="red-button blue" @click="clearTag('gid')" style="padding-left:5px;" v-if="gid">
          <span>{{gnm}}</span>
          <span
            class="icon icon-close"></span>
          </span>
          <span class="red-button blue" @click="clearTag('src')" v-if="src=='post'">
            <span>{{-, Personal post,forum}}</span>
            <span class="icon icon-close"></span>
          </span>
          <span class="red-button blue" @click="clearTag('src')" v-if="src=='property'">
            <span>{{- Home Review}}</span>
            <span class="icon icon-close"></span>
          </span>
          <span class="red-button blue" @click="clearTag('src')" v-if="src=='sch'||src=='psch'">
            <span>{{- School Review}}</span>
            <span class="icon icon-close"></span>
          </span>
          <span class="red-button blue" @click="clearTag('city')" v-if="curCity.o||curCity.p||curCity.cnty">
            <span>{{ $_(curCity.o||curCity.p||curCity.cnty)}}</span>
            <span class="icon icon-close"></span>
          </span>
          <span class="pull-right icon icon-close padding-right-10" style="line-height: 44px; padding-top:0px;padding-bottom:0px;" @click="clearTag('all')"></span>
        </div>
      <div v-else>
        <div class="tag-filter all-filter" v-if="displayPage=='all'" style="overflow: auto; padding-right: 10px; white-space: nowrap;">
          <span @click="filterByTag('HOT')">{{-, HOT,forum}}</span>
          <span @click="filterByTag('post')">{{-, Personal Post,forum}}</span>
          <span @click="filterByTag('property')">{{-, Home Review,forum}}</span>
          <span @click="filterByTag('sch')">{{-, School Review,forum}}</span>
          <span v-for="tag in tagList" v-show="tag.key!='HOT'" @click="filterByTag(tag.key)">{{tag.key}}</span>
        </div>
      </div>
    </div>
  </div>
</div>
{{? it.isApp}}
{{* 'bottomNav' it}}
{{?}}


<script type="text/javascript">
  var TRANSLATES = {
      /* forum type translation*/
    "News":"{{-, News,forum}}",
    "Topic":"{{-, Topic,forum}}",
    "My Post":"{{-, My Post,forum}}",
    "My Reply":"{{-, My Reply,forum}}",
    "My Group":"{{-, My Group,forum}}",
    "My Favourite":"{{-, My Favourite,forum}}",
    "My Admin Posts":"{{-, My Admin Posts,forum}}",
    "enter":"{{- Enter forum id or url}}",

  };
</script>

{{~ js :j:idx}}
<script type="text/javascript" src="{{= j}}"></script>
{{~}}