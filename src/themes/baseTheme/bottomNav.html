<style>
  .bar-tab .tab-item.active, .bar-tab .tab-item:active {
    color: black;
  }
  .tab-item{
    position: relative;
  }
  .tab-item .icon-left-nav{
    vertical-align: middle;
    font-size: 18px !important;
    width: 16px !important;
    color: #1fa61d;
    position: absolute;
    left: calc(50% - 37px);
    top: 29% !important;
  }
  .home-sprite{
    border-radius: 50%;
    width: 40px !important;
    height: 40px !important;
    top: 0 !important;
    vertical-align: middle;
  }
  .tab-item .icon-left-nav.market-red{
    color: #e03131;
  }
  #bottomNavBar .tab-item.new .icon:after{
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #e03131;
    z-index: 10;
    position: absolute;
    right: -6px;
    top: -2px;
}
</style>
<div id="bottomNavBar" class="bar bar-tab">
  <a class="tab-item {{=it.page=='home'?'active':''}}" data-sub="nav home" onclick="gotoLink('/1.5/index',{'key':'bottomNavBar','val':'openHome'})">
    <span class="icon fa fa-fw fa-index{{=it.page=='home'?'-focus':''}}"></span>
    <span class="span tab-label">{{-, Home,menu}}</span>
  </a>
  {{? !it.isChinaIP}}
  <a class="tab-item  {{=it.page=='news'?'active':''}}" data-sub="nav news" onclick="gotoLink('/1.5/forum?d=/1.5/index',{'key':'bottomNavBar','val':'openForum'})">
    <span class="icon fa fa-fw fa-news{{=it.page=='news'?'-focus':''}}"></span>
    <span class="tab-label">{{-, News,menu}}</span>
  </a>
  {{?}}
  {{? it.appmode}}
  <a class="tab-item">
    {{? it.appmode != 'mls'}}
    <span class="icon home-sprite release_{{? it.lang == 'zh' || it.lang == 'zh-cn'}}zh{{??}}en{{?}}" data-sub="nav mls" onclick="gotoLink('/home/<USER>',{'key':'bottomNavBar','val':'openMls'})"></span>
    {{??}}
    <span class="icon home-sprite market_{{? it.lang == 'zh' || it.lang == 'zh-cn'}}zh{{??}}en{{?}}" data-sub="nav rm" onclick="gotoLink('/home/<USER>',{'key':'bottomNavBar','val':'openMarket'})"></span>
    {{?}}
  </a>
  {{?}}
  <a class="tab-item {{=it.page=='saves'?'active':''}}" data-sub="nav saved" onclick="gotoLink('/1.5/saves/properties?grp=0',{'key':'bottomNavBar','val':'openSaved'})">
    <span class="icon fa fa-fw fa-saves{{=it.page=='saves'?'-focus':''}}"></span>
    <span class="tab-label">{{-, Saved,menu}}</span>
  </a>
  <a class="tab-item {{=it.appUpgrade && it.appUpgrade.showAlert?'new':''}} data-sub="nav me" {{=it.page=='me'?'active':''}}" onclick="gotoLink('/1.5/settings',{'key':'bottomNavBar','val':'openMe'})">
    <span class="icon fa fa-fw fa-me{{=it.page=='me'?'-focus':''}}"></span>
    <span class="tab-label">{{-, Me,menu}}</span>
  </a>
</div>