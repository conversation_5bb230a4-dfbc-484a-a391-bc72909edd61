{{&
  var {dispVar,fAuthUrl,gAuthUrl} = it;
}}

<div id="loginPage" class="pop-up-login" style="display: none;">
  <span id="pop-up-close" class="fa fa-times"></span>
  <rednavbar curmenu="login"></rednavbar>
  <login cur-page="prop" page-mode="login"></login>
</div>
<div class="data" style="display: none;">
  <pre id="jsonDispVar" data-role="module-args">{{= it.jsonDispVar}}</pre>
  <pre id="fAuthUrl">{{= it.fAuthUrl}}</pre>
  <pre id="gAuthUrl">{{= it.gAuthUrl}}</pre>
</div>

<script defer src="/js/web/loginModal.min.js"></script>

<script src='/web/packs/commons.js'></script>
<script src='/web/packs/loginPage.js'></script>
{{? !dispVar.isCip}}
<script async src='https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit'></script>
<script>
  function onloadCallback() {
    window.recaptchaLoaded = true;
    window.bus.$emit('recaptcha-loaded')
  }
</script>
{{?}}