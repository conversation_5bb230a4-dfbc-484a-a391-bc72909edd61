<link rel="stylesheet" href="/css/web/saves.css">
<link rel="stylesheet" type="text/css" href="/css/sprite.min.css">
<div id="saves-root"></div>
<script src="/js/axios.min.js"></script>
<script src="/js/event/EventEmitter.min.js"></script>
<script type="text/javascript">
  var TRANSLATES = {
      savedTitle: "{{-, Saved,settings}}",
      saveTo: "{{- Save to}}",
      time: "{{- Time}}",
      name: "{{- Name}}",
      noImage: "{{- No Image}}",
      detail: "{{- Detail}}",
      homes: "{{- Homes}}",
      perConstruct: "{{- PreCons}}",
      search: "{{- Searches}}",
      agents: "{{- Agents & Businesses}}",
      noResult: "{{- No result found}}",
      tryElse:"{{- Try searching for something else}}",
      saved: "{{- Saved}}",
      unsaved: "{{- Unsaved}}",
      top: "{{- TOP}}",
      assignment: "{{- Assignment}}",
      exlisting: "{{- Exclusive}}",
      landlordRental: "{{- Landlord Rental}}",
      exclusiveRental:"{{- Exclusive Rental}}",
      print:"{{- Print}}",
      signature:"{{- Signature}}",
      diff:"{{- Diff}}",
      list:"{{- List}}",
      pleaseSelect:"{{- Please select a property}}",
      add:"{{- Add}}",
      searchId:"{{- Search}}",
      delete:"{{- Delete}}",
      cancel:"{{- Cancel}}"
    };
  var LANG = "{{= it.lang}}"
</script>
<script src="/js/<EMAIL>"></script>
<script src="/js/<EMAIL>"></script>
<script type="module" src="/js/react_components/saves/App.js"></script>