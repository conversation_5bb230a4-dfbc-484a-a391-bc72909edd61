###
  Mailer Instance
    - open a server for sending email
    - configuration the real smtp server
    - API
      sendMail(mail,cb)
        mail the real mail subject,text,html,from,to ...
        cb(err, message)
      queueMail(mail,cb)
      
  mail = {
    transport: smtp or sendmail
    from: "Chome.ca <<EMAIL>>"
    to: "#{body.fn or ''} #{body.ln or ''} <#{body.eml}>"

    subject: body.subject
    text: body.text
    html: body.html

    template: template_id
    lang: language or 'en'
    xxx other properties
    }

  Need group 1 + (2 or 3)
###
verbose = 2
helpers = require '../lib/helpers'
Json = require '../lib/json'
InterNode = require "../inter.node/internode"
nodemailer = require 'nodemailer'
base = require './base'
config = base.config

smtp_cfg = config?.mailEngine?.smtp or {
  service: "Gmail"
  auth: {user:"<EMAIL>",pass:"jiajiamail"} }

schema = new Json.JsonSchema {
  _trans:
    type: 'string'
    required: true
    enum: ['smtp','sendmail']
    default: 'smtp'
  from:
    type: 'string'
    required: true
    format: 'email'
  to:
    type: ['string','array']
    required: true
    format: 'email'
    items:
      type: 'string'
      required: true
      format: 'email'
  }
  
template_replacing = (s,d)->
  m = /d\[(?:([^\[\]\|]+)\:)?([a-zA-Z0-9_]+)(?:\|([^\[\]\:]+))?\]/g
  if aa = s.match m
    for a in aa
      m.lastIndex = 0
      c = m.exec a
      [target,til,b,def] = c
      _replace_d = (b)->
        if (dd = d[b])? and ((Array.isArray(dd) and (dd = dd.join(', '))) or ((dd = helpers.trim(dd)).length > 0))
          s = s.replace target,if til then til + dd else dd
          return true
        false
      if _replace_d b
      else
        not_done = true
        if def and (def.length > 0) and ((bb = def.split('|')).length > 1)
          for b in bb
            if _replace_d b
              not_done = false
              break
            else
              def = b
        if not_done
          if def?
            s = s.replace(target,def) if not _replace_d def
          else
            s = s.replace target,''
  else
    if verbose then console.log s
  s
  
base.mongo (err,Mongo)->
  Template = new Mongo('jiajia','template')
  MailQueue = new Mongo('jiajia','mailqueue')
  MailDone = new Mongo('jiajia','mailsent')
  transports =
    smtp: nodemailer.createTransport('SMTP',smtp_cfg)
    sendmail: nodemailer.createTransport('sendmail',{path:'/usr/sbin/sendmail'})

  getTemplate = (mail,cb)->
    if mail._temp
      Template.find {
        $query: {tid:mail._temp,lang:{$in:[mail.lang,'en']},dm:{$in:[mail.dm,'~~~']}}
        $orderby: {dm:1}
        },(err,db)->
          if err then return cb err
          db.toArray (err,tpls)->
            console.log tpls # TODO: remove debug
            if err or (not tpls) or (tpls.length < 1)
              # Add template
              Template.insertOne {
                tid: mail._temp
                lang: (mail.lang or 'en')
                dm: mail.dm or '~~~'
                subject: mail.subject
                html: mail.html
                text: mail.text
              },{safe:true},(err,ntpl)->
                cb err,ntpl
            else
              if tpls.length is 1
                return cb null,tpls[0]
              else
                # find the one with correct lang
                default_t = tpls[0]
                for t in tpls
                  if t.lang is mail.lang
                    return cb null,t
                  if t.lang is 'en'
                    default_t = t
                return cb null,default_t
    else
      cb()
      
  # replace parameters in html & text,
  replaceParam = (mail,tpl)->
    if mail._rpd then return mail
    if s = (mail.subject or tpl?.subject)
      mail.subject = template_replacing s,mail
    if s = (mail.text or tpl?.text)
      mail.text = template_replacing s,mail
    if s = (mail.html or tpl?.html)
      mail.html = template_replacing s,mail
    mail._rpd = true # replace parameter done
    if not mail.from then mail.from = '<EMAIL>'
    if not mail.replyTo then mail.replyTo = mail.from
    mail.generateTextFromHTML = true
    mail

  # get template, apply parameter replace, replace html, text
  # callback with replaced mail
  applyParam = (p,cb)->
    if p._rpd then return cb null,p
    schema.validate p
    if schema.has_error()
      return cb schema.errors()
    if p._temp?
      return getTemplate p,(err,t)->
        if err or not t then return cb err or new Error "Not found"
        if t.keys
          for k in t.keys
            if not p[k]?
              return cb new Error "Insufficent parameters"
        return cb null,replaceParam(p,t)
    if p.subject? and (p.html or p.text)
      return cb null,replaceParam(p)
    cb new Error "No enough parameters"

  exports.sendMail = sendMail = (mail,cb)->
    applyParam mail, (err,nmail)->
      if err then return cb err
      if verbose then console.log nmail
      delete nmail._id
      MailQueue.insertOne nmail,{safe:true},(err2,r)->
        if err2 then return cb err2
        processMailNow r._id,cb
    
  exports.mailNow = mailNow = (mail,cb)->
    applyParam mail,(err,mail)->
      if err then return cb err
      transport = transports[mail._trans or 'smtp']
      transport.sendMail mail, cb

  exports.processMailNow = processMailNow = (id,cb)->
    if not cb?
      cb = id
      id = null
    q = {_st:{$exists:false}}
    if id then q._id = id
    MailQueue.findOneAndUpdate q,[],{$set:{_st:new Date()}},{returnDocument: 'before', w: 1},(err,mail)->
      if err?
        cb err if cb?
        return console.error err
      if mail
        mailNow mail,(err,resp)->
          mail._dn = new Date()
          if err
            mail._err = err.toString()
            console.error err
          MailQueue.deleteOne {_id:mail._id},{w:1},(err)->
            if err then console.error err
            MailDone.insertOne mail,{w:1},(err2)->
              if err2 then console.error err2
              if cb?
                cb err,mail._id
              else
                process.nextTick processMailNow

  exports.processMail = processMail = (cb)->
    process.nextTick processMailNow
    cb null

  process.nextTick processMailNow

  server = InterNode.service()
  server.reg 'jiajiaSendMail', sendMail
  server.reg 'jiajiaMailNow', mailNow
  server.reg 'jiajiaProcessMail', processMail


process.on 'uncaughtException',(err)->
  console.error "Caught exception: " + err
