###
Description:
  watch collection and import the changes to properties.
  mls_treb_master_records,mls_bcre_manual_records,mls_treb_manual_records,mls_crea_ddf_records
  should not run more then one batch for different source, might cause confilit due to parallel update.

Usage:
  For dev test:
  init nogeo:
  ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t oreb -t creb -t edm -t car -t reso -t bcreReso -t creaReso-t deleteEvow -h 1200 init nogeo force >> ./logs/watchAndImportToProperties.log 2>&1 &
  ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t bcre -h 48 init nogeo force >> ./logs/watchAndImportToProperties.log 2>&1 &

  -d "************************************************************"

  routine has geo:
  production:
  init: nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t  trebMan -t dta -t bcre -t rahb -t oreb -t creb -t edm -t car -t reso -t deleteEvow -h 12 init force> ./logs/watchAndImportToProperties.log 2>&1 &
  routine: nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t oreb -t creb -t edm -t car -t reso -t deleteEvow routine > ./logs/watchAndImportToProperties.log 2>&1 &

New config:
  ./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow -t oreb -t creb -h 1 routine force"

  ./start.sh  -t batch -n watchAndImportToProperties_edm -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model  -t edm -h 12000 init force"
#params:
  -t :      [trebMan|treb|ddf|bcreMan|dta|bcre|rahb|oreb|creb|edm|car|reso] (source)
  -h :      query back hours for init
  nogeo:    no real geocoding
  init:     import from backhours, -h is required.
  -m:       timeout, for unitTest purpose.
  routine:  import from token saved in db, when token invalid, import token.mt - halfhour
  rerun:    rerun from query, ignore db time stamp
  -q:       query parameter, eg: -q '{"treb":{"mt":{"$gte":{"$date":"2024-12-27T23:57:43.073Z"}}}}'
  nolog:    no write log to import_watched_records and properties_import_log
  
  noGeoForInactive: skip geo for inactive prop

#Author:         Julie
#Run duration:   none stop; for init: 5 day create db;
#Run frequency:  none stop watch

sysdata eg:
{
    "_id" : "bcre2prop",
    "mt" : ISODate("2021-02-01T18:48:16.206Z"),
    "status" : "complete",
    "val" : "{\"_data\":\"82601848EE000000052B0229296E04\"}"
}
./start.sh -t batch -n watchAndImportToProperties_reso -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee -t reso preload-model force rerun -q '{\"reso\":{\"_mt\":{\"\$gte\":{\"\$date\":\"2025-03-06T17:00:00.000Z\"},\"\$lte\":{\"\$date\":\"2025-03-06T23:59:00.000Z\"} }}}'"


watch token:
There's two type of token, savedToken(for sysdata) and currentToken(for finished changed event)

when init, get New currentToken,currentTokenTs,
process old props and then save currentToken,currentTokenTs and sart watch from currentToken,currentTokenTs

when routine, get savedToken and start watch from savedToken.

every time has change, get new currentToken and currentTokenTs
every one minute, save the last currentToken and currentTokenTs as saveToken, savedTokenTs

when invalidate event. get New currentToken, currentTokenTs, then do gracefulExit

when gracefulExit, save currentToken,currentTokenTs as savedToken,savedTokenTs
###

watchHelper = require '../../libapp/watchHelper'
# mongodb = require('mongodb')
# MongoClient = mongodb.MongoClient
libProperties = require '../../libapp/properties'
addLogs = libProperties.addLogs
helpersFunction = require '../../lib/helpers_function'
{relog} = require '../../lib/helpers_string'
# NOTE: LOAD -> INCLUDE
saveToMaster = INCLUDE('../../libapp/saveToMaster')
speed = INCLUDE 'lib.speed'
functionHelper = INCLUDE 'lib.helpers_function'
statImportHelper = require '../../libapp/statImport'

init = AVGS.indexOf('init') >= 0
rerun = AVGS.indexOf('rerun') >= 0
NOLOG = AVGS.indexOf('nolog') >= 0
debug = DEBUG(1)
# NOTE: cant use CONFIG as var, will conflict with config namespace
config = CONFIG(['serverBase','contact', 'mailEngine', 'mailEngineList','log'])
sendMailLib = INCLUDE 'lib.sendMail'
sendMailObj = sendMailLib.getSendMail(config)

GeoCoder = MODEL 'GeoCoder'
# TODO: 最好命名与其它一致, tmp -> TMP_DB_NAME, RNI_DB_NAME = 'rni'

# TODO: 一年过期有些长，后续需要检查 db usage
importWatchedRecordsCol = COLLECTION('tmp','import_watched_records')
importWatchedRecordsCol.createIndex {_mt:1},{expireAfterSeconds: 31536000,background:true} # 一年过期
Properties = COLLECTION('vow','properties')
Properties.createIndex {'uaddr':1,'onD':1},{background:true}
Properties.createIndex {'merged':1},{background:true}
SysData = COLLECTION('vow','sysdata')
PropertiesDeleted = COLLECTION('vow','properties_deleted')

TrebRecords =       COLLECTION('rni','mls_treb_master_records')
TrebEvowDeleteIds = COLLECTION('rni','mls_treb_evow_delete_ids')
TrebDtaRecords =    COLLECTION('rni','mls_treb_dta_records')
TrebManualRecords = COLLECTION('rni','mls_treb_manual_records')
BcreManualRecords = COLLECTION('rni','mls_bcre_manual_records')
BcreRecords =       COLLECTION('rni','mls_bcre_records')
DDFRecords =        COLLECTION('rni','mls_crea_ddf_records')

if config.serverBase?.createRNIIndex
  TrebRecords.createIndex {mt:1},{background:true}
  TrebRecords.createIndex {_mt:1},{background:true}
  TrebDtaRecords.createIndex {mt:1},{background:true}
  TrebDtaRecords.createIndex {_mt:1},{background:true}
  TrebManualRecords.createIndex {mt:1},{background:true}
  TrebManualRecords.createIndex {_mt:1},{background:true}
  BcreManualRecords.createIndex {mt:1},{background:true}
  BcreManualRecords.createIndex {_mt:1},{background:true}
  BcreManualRecords.createIndex {'Displayid':1},{background:true}
  BcreRecords.createIndex {mt:1},{background:true}
  BcreRecords.createIndex {_mt:1},{background:true}
  DDFRecords.createIndex {mt:1},{background:true}
  DDFRecords.createIndex {_mt:1},{background:true}

RahbRecords = COLLECTION('rni','mls_rahb_records')
CrebRecords = COLLECTION('rni','mls_creb_master_records')
OrebRecords = COLLECTION('rni','mls_oreb_master_records')
EdmRecords = COLLECTION('rni','mls_rae_master_records')
CarRecords = COLLECTION('rni','mls_car_master_records')
RESORecords = COLLECTION('rni','reso_treb_evow_merged')
BcreResoRecords = COLLECTION('rni','bridge_bcre_merged')
CreaResoRecords = COLLECTION('rni','reso_crea_merged')
# Not used
#RESOIdxRecords = COLLECTION('rni','reso_treb_idx_filtered')

AgentCol = COLLECTION('vow', 'agents')
OfficeCol = COLLECTION('vow', 'offices')

SqftMiddle = COLLECTION 'tmp','sqft_middle'
SqftsAggregate = COLLECTION 'vow','sqfts_aggregate'

SqftToProcess = COLLECTION('vow','sqft_to_process')
ChomeSysData  = COLLECTION('chome','sysdata')
# SQFT_TO_PROCESS_NAME = 'sqft_to_process'

CollPropertyImportLog = COLLECTION('tmp','properties_import_log')
CollPropertyImportLog.createIndex {_mt:1},{expireAfterSeconds: 31536000,background:true} # 一年过期
TransitStop = COLLECTION('vow','transit_stop')
Census2016 = COLLECTION('vow','census2016')
ProcessStatusCol = COLLECTION('vow','processStatus')
School = COLLECTION('vow','sch_rm_merged')
Boundary = COLLECTION('vow','boundary')
ProvCityCmty = COLLECTION('vow','prov_city_cmty')
Census2016ReversKeys =COLLECTION('vow','census2016ReversKeys')
Census2021 = COLLECTION('vow','census2021')
# GeocodingStat    = COLLECTION 'vow','geo_coding_stat'
# geoCoder.setGeocodingStat GeocodingStat
gUpdateTokenFns = {}
TransitCities = null
gWatchedStream = {}
gStat = {}
gQueue = []
TOKEN_DELAY_MS = 60000*5
gCallbackFunction = {}
inQueue = false

{
  processKillSignal,
  RUNNING_NORMAL,
  RUNNING_WARNING,
  FINISH_WITHOUT_ERROR
  RUNNING_ERROR
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = require '../../libapp/processHelper'

yargs = require('yargs')(AVGS) # eslint-disable-line

yargs
  .option 'type', { alias: 't', description: 'source typ'}
  .option 'hour', { alias: 'h', description: 'back hours'}
  .option 'timeout', { alias: 'm', description: 'timeout minutes'}
  .option 'database', { alias: 'd', description: 'target database connection string',\
    required: false}
  .option 'query', { alias: 'q', description: 'query parameter'}
  .option 'noGeoForInactive', { alias: 'g', description: 'noGeoForInactive', required:false}
  .option 'nolog', { alias: 'n', description: 'no write log to import_watched_records and properties_import_log', required:false}
  .help('help')


gImportCount = {}
gImportCountHalfDay = {}
HALF_DAY = 12*60*60*1000
# gImportIds = {}

# 检查不同src房源12小时的import次数,没有import时notifyAdmin
functionHelper.repeat HALF_DAY,()->
  debug.info 'reset gImportCountHalfDay, imported count', gImportCountHalfDay
  noImportSrcTypes = []
  for srcType,count of gImportCountHalfDay
    if count is 0
      debug.warn "#{srcType} has not import for 12 hours"
      noImportSrcTypes.push srcType
    gImportCountHalfDay[srcType] = 0 # reset
  # notifyAdmin
  if noImportSrcTypes.length
    msg = "#{noImportSrcTypes.join(',')} no import for 12 hours, please check"
    sendMailObj.notifyAdmin(msg)

IMPORT_SRC_TYPES = yargs.argv.type
QUERY_PARAM = yargs.argv.query  # 新增的查询参数
NO_GEO_FOR_INACTIVE = yargs.argv.noGeoForInactive

if NOGEO
  debug.info 'NOGEO, set GeoCoder.noGeocoding true'
  GeoCoder.noGeocoding(true)
if NOLOG
  debug.info 'NOLOG, set importWatchedRecordsCol and CollPropertyImportLog null'
  importWatchedRecordsCol = null
  CollPropertyImportLog = null

if not IMPORT_SRC_TYPES
  return EXIT('Please specify import source,usage: -t ddf')

if typeof IMPORT_SRC_TYPES is 'string'
  IMPORT_SRC_TYPES = [IMPORT_SRC_TYPES]

TIMEOUT_SECOND = yargs.argv.timeout

debug.info 'IMPORT_SRC_TYPES',IMPORT_SRC_TYPES
PROCESS_STATUS_ID = 'watchAndImportToProperties'

PROCESS_STATUS_DELAY = {
  'treb':  60000 * 10
  'ddf':  60000 * 10
  'bcre' : 60000 * 10
  'rahb' : 60000 * 10
  'creb' : 60000 * 10
  'oreb' : 60000 * 10
  'edm' : 60000 * 10
  'car' : 60000 * 10
  'bcreMan':  3600 * 1000 * 30
  'trebMan':  3600 * 1000 * 30
  'dta':  3600 * 1000 * 2
  'deleteEvow': 60000 * 10
  'reso': 60000 * 10
  'bcreReso': 60000 * 10
  'creaReso': 60000 * 10
}
if TIMEOUT_SECOND
  setTimeout ()->
    debug.debug 'exit by timeout'
    gracefulExit 'Exit according to timeout params'
  ,TIMEOUT_SECOND * 1000

SRC_TYPE_COLL_MAPPING =
  'mls_treb_master_records':'treb'
  'mls_treb_manual_records':'trebMan'
  'mls_bcre_manual_records':'bcreMan'
  'mls_bcre_records':'bcre'
  'mls_crea_ddf_records':'ddf'
  'mls_treb_dta_records':'dta'
  'mls_rahb_records':'rahb'
  'mls_creb_master_records':'creb'
  'mls_oreb_master_records':'oreb'
  'mls_rae_master_records':'edm'
  'mls_car_master_records':'car'
  'bridge_bcre_merged':'bcreReso'
  'reso_treb_evow_merged':'reso'
  'reso_crea_merged':'creaReso'
setUpdateSysFun = ()->
  for tp in IMPORT_SRC_TYPES
    params = {mongo:1,SysData:SysData, sysDataId: tp+'ToProp'}
    fn = watchHelper.getUpdateSysdataFunction params
    fnAsync = helpersFunction.addAsyncSupport fn
    debug.debug 'getUpdateSysdataFunction',fnAsync,params
    gUpdateTokenFns[tp] = fnAsync
setUpdateSysFun()


gProcessHandler = {}

#get process handler for each type
for srcType in IMPORT_SRC_TYPES
  gImportCount[srcType] = 0
  gImportCountHalfDay[srcType] = 0
  # gImportIds[srcType] = []
  gProcessHandler[srcType]= getProcessHandler {
    ProcessStatusCol,
    id: PROCESS_STATUS_ID+'_'+srcType,
    filePath: BATCH_FILE_NAME,
    updateSysData: gUpdateTokenFns[srcType],
    exitFn:EXIT
  }

###
将对象中的 $date 字段转换为 Date 对象
@param {object} obj - 需要处理的对象
@return {object} - 处理后的对象
###
convertDateFields = (obj) ->
  return obj unless obj? and typeof obj is 'object'
  
  for key, value of obj
    if value? and typeof value is 'object'
      if '$date' of value
        obj[key] = new Date(value['$date'])
      else
        convertDateFields(value)
  return obj

###
解析查询参数字符串为对象
# NOTE: 时间戳字段需要使用$date,eg:{"mt":{"$gte":{"$date":"2024-01-01T00:00:00.000Z"}}}
# NOTE: 如果多个src共用一个query，则需要使用_连接，eg:{"type1_type2":{"field":"value"}}
@param {string} queryStr - 格式: '{"type1":{"field":"value"},"type2":{"field2":"value2"}}
@return {object} - {type1: {field:"value"}, type2: {field2:"value2"}}
###
parseQueryParam = () ->
  return {} unless QUERY_PARAM
  queryStr = QUERY_PARAM.replace(/'/g, '"').replace(/([{,])\s*([a-zA-Z0-9_$]+)\s*:/g, '$1"$2":')
  queryObj = JSON.parse(queryStr)

  result = {}
  for key,query of queryObj
    keys = key.split('_')
    for k in keys
      result[k] = convertDateFields query
  return result

updateProcessStatusForWatch = ({srcType,startTs})->
  nextTs = Date.now() + PROCESS_STATUS_DELAY[srcType]
  # debug.info 'gProcessHandler[srcType]',srcType,gProcessHandler[srcType]
  debug.info 'update process status', srcType, 'imported count', gImportCount[srcType]
    # 'imported ids:', gImportIds[srcType]
  gImportCountHalfDay[srcType] += gImportCount[srcType]
  gImportCount[srcType] = 0
  # gImportIds[srcType] = []
  try
    await gProcessHandler[srcType].handleProcessStatus \
      {status: RUNNING_NORMAL, nextTs: nextTs,startTs}
  catch error
    debug.error error

getUpdateProcessStatusFn = (srcType)->
  return ()->
    updateProcessStatusForWatch {srcType}

###
# wait all job finished and exit.
# @param {string} errMsg
# @param {function} cb, for unit test usage
###
exports.gracefulExit = gracefulExit = (errMsg,cb) ->
  debug.critical errMsg if errMsg
  index = 0
  exitOne = ()->
    if index <  IMPORT_SRC_TYPES.length
      srcType = IMPORT_SRC_TYPES[index] # use index loop
      index++
      if not gWatchedStream[srcType] #如果没有启动watch，不需要处理。
        return exitOne()
      # debug.info 'gracefulExit gWatchedStream',gWatchedStream
      gWatchedStream[srcType]?.end()
      cbOne = (err,ret)->
        debug.info 'gracefulExitOne next'
        debug.critical err if err
        exitOne()
      gProcessHandler[srcType].handleExit {
        error: errMsg,
        watchedObject: gWatchedStream[srcType],
        exitFn: cbOne
      }
    else
      debug.info 'exit main process'
      statImportHelper.stop() # 停止统计服务
      if cb
        return cb()
      return EXIT(errMsg)
  
  exitOne()


if not IMPORT_SRC_TYPES
  return gracefulExit('Please specify import source,usage: -t ddf')

#打印输入类型，导入时间段
debug.info 'IMPORT_SRC_TYPES',IMPORT_SRC_TYPES

if typeof IMPORT_SRC_TYPES is 'string'
  IMPORT_SRC_TYPES = [IMPORT_SRC_TYPES]




#检查输入类型
for srcType in IMPORT_SRC_TYPES
  srcTypesList = ['treb','trebMan','bcreMan','ddf','dta', 'deleteEvow','bcre', 'rahb', 'oreb', 'creb', 'edm','car','reso','bcreReso','creaReso']
  if srcType not in srcTypesList
    gracefulExit("please specify import type #{srcTypesList}; invalidSrc:#{srcType}")
  gStat[srcType] = {lastTokenPrintTs: new Date(),srcType}

hour = yargs.argv.hour
NO_IMPORT_BACK_SECOND = 'Please specify import back hour when init -h (1 or 0.5)'
if init and (not parseFloat(hour))
  return gracefulExit(NO_IMPORT_BACK_SECOND)

gImportBackSecond = (yargs.argv.hour or 0) * 3600 * 1000

debug.info 'IMPORT_BACK_SECOND', gImportBackSecond




#kill -SIGUSR1 $pid
process.on 'SIGUSR1', ->
  debug.warn 'process receive SIGUSR1'
  debugHelper.increseModuleThreshhold(1)

process.on 'SIGUSR2', ->
  debug.warn 'process receive SIGUSR2'
  debugHelper.increseModuleThreshhold(-1)


processKillSignal 'watchAndImportToProperties',gracefulExit

process.on 'uncaughtException', (err)->
  debug.error 'uncaughtException',err
  gracefulExit err

###
Returns add item to queue function of the input srcType
@param {string} srcType - 'treb','trebMan','bcreMan','ddf','dta'
###
addItemToQueue = (srcType) ->
  (changedObj,watchedColl, cb)->
    # helpers_string.relog 'add item to queue'+gQueue.length
    gQueue.push {srcType,changedObj,watchedColl,cb}
    debug.debug 'gWatchedStream[srcType]',srcType, \
    'processingCounter', gWatchedStream[srcType].processingCounter,\
    'gQueue.length',gQueue.length
    _callListener()

# skip add to queue, use high/low watermark from helpers streaming/watch
processItemSaveToMaster = (srcType) ->
  (changedObj,watchedColl, cb)->
    _id = changedObj?.documentKey?._id
    if not _id
      debug.error 'changedObj no id',changedObj
      # NOTE: src/libapp/watchHelper.coffee#605 没有id只error log,不cb(err)
      return cb()
    if not prop = changedObj?.fullDocument
      try
        prop = await watchedColl.findOne {_id}
      catch err
        debug.error err,changedObj,"_id:#{_id}"
        return cb(err)
    log = {m:"#{srcType}: processItemSaveToMaster"}
    addLogs {id:"#{_id}",step:'watchOnChange',log,prop,collPropertyImportLog:CollPropertyImportLog},(err)->
      gCallbackFunction[srcType] {changedObj,watchedColl},(err)->
        cb err # cb for onChange event.

###
shift item in queue and process by item src type
NOTE: process 1 at a time, not parallel.
###
_callListener = ()->
  return if inQueue
  inQueue = true
  item = gQueue.shift()
  if not item?
    inQueue = false
    return
  gCallbackFunction[item.srcType] \
    {changedObj:item.changedObj,watchedColl:item.watchedColl},(err)->
      item.cb err # cb for onChange event.
      inQueue = false
      return if gQueue.length <= 0
      # relog 'process next item in queue'+gQueue.length
      process.nextTick _callListener


###
set global function when item changed.
@param {string} srcType - 'treb','trebMan','bcreMan','ddf','dta'
###
setListener = (srcType)->
  ignoreDelete= true #import不能删除properties里已有的房源
  updateOneFn = _updateOne(srcType)
  gCallbackFunction[srcType] = ({changedObj,watchedColl}, cb)->
    # debug.debug 'updateOne',changedObj,srcType
    params = {
      changedObj,
      watchedColl,
      ignoreDelete,
      updateOneFn:updateOneFn,
      replaceOneFn:updateOneFn#replaceOne is update.
      watchedStream:gWatchedStream[srcType]
      skipChangeMtOnly:true
    }
    if srcType in ['treb','ddf','dta']
      params.changeStatusOnlyFn = _changeStatusOnly
    watchHelper.processChangedObject params, (err, ret)->
      if err
        debug.critical 'processChangedObject error',err
        return cb err #return call back will send err back to onchange event.
      gStat[srcType][changedObj.operationType]?=0
      gStat[srcType][changedObj.operationType]++
      return cb()


_updateDeleteEvow = (prop,cb)->
  unless prop?._id
    debug.error 'invalid prop', prop
    return cb 'no id'
  debug.debug '_updateOne',prop._id
  log = {m:'delete evow property'}
  addLogs {id:"TRB#{prop._id}",log,collPropertyImportLog:CollPropertyImportLog},(err)->
    libProperties.deleteAndBackupEvowProp \
      {id:'TRB'+prop._id, db: Properties, db_deleted:PropertiesDeleted},cb

#convert and import one from source to properties
#动态load saveToMaster
_updateOne =  (srcType) ->
  if srcType is 'deleteEvow'
    return _updateDeleteEvow
  return (prop,cb)->
    gImportCount[srcType] += 1
    # gImportIds[srcType].push(prop._id or prop.id) if gImportIds[srcType]
    # debug.info '_updateOne',prop._id or prop.id
    # LOAD require node 14.3.0 above
    addLogs {id:"#{prop._id}",log:{m:"#{srcType}: _updateOne"},step:'updateOne',prop,collPropertyImportLog:CollPropertyImportLog},(err)->
      saveToMaster.convertAndSaveRecord {
      #require('../../libapp/saveToMaster').convertAndSaveRecord {
        srcType,
        noGeoForInactive:NO_GEO_FOR_INACTIVE
        GeoCoder,
        targetProperties:Properties,
        # collPropertyImportLog:CollPropertyImportLog,
        PropertiesImportLog:CollPropertyImportLog,
        record:prop
        transitCities:TransitCities,
        TransitStop: TransitStop,
        School: School,
        Boundary: Boundary,
        Census2016: Census2016,
        Census2016ReversKeys: Census2016ReversKeys,
        Census2021: Census2021,
        SqftMiddle,
        SqftsAggregate,
        SqftToProcess,
        AgentCol,
        OfficeCol,
        ProvCityCmty
        },cb
          # debug.debug 'updateone finished'
          #cb(err)

getSrcTypeByWatchedCollName = (collname)->
  if src = SRC_TYPE_COLL_MAPPING[collname]
    return src
  else
    debug.error "#{collname} is  not supported"
    return

#更新stauts和mt到数据库
_updatePropStatusToDB=(_id,prop,watchedCollName,cb)->
  # only update status of U.
  if (not prop.status) or (prop.status is 'A')
    return cb()
  set = {status:prop.status}
  if prop.mt
    set.mt = new Date(prop.mt)
  srcType = getSrcTypeByWatchedCollName watchedCollName
  log = {status:prop.status,m:"#{srcType}: _changeStatusOnly"}
  addLogs {id:"#{_id}",log,collPropertyImportLog:CollPropertyImportLog},(err)->
    Properties.updateOne {_id:_id},\
      {$set:set},(err,ret)->
        debug.debug 'update status for', _id, prop.status
        return cb(err, ret)
###
#只有状态更新时的处理，ddf，treb直接按_id更新，bcreMan找到对应的_id在更新。
@param {string} id
@param {object} prop
@param {object} watchedCollName
###
_changeStatusOnly = ({id,prop,watchedCollName},cb)->
  if /mls_bcre_manual_records/.test watchedCollName
    return BcreManualRecords.findOne {_id:id},{fields:{Displayid:1}},(err,ret)->
      return cb(err) if err
      return cb("No bcre record #{id} found") if not ret
      _updatePropStatusToDB("BRE#{ret.Displayid}",prop,watchedCollName,cb)
  else
    return _updatePropStatusToDB(id,prop,watchedCollName,cb)
  return cb("#{watchedCollName} is  not supported")

###
return function when token updated, save token to sysdata, selbingkey, update process status
@param {string} token
@patam {string} tokenClusterTs: last token updated cluster ts
@patam {string} tokenChangeTs: last token updated ts
@param {date} resumeMt: timestamp for resume
###
onTokenUpdate = (srcType)->
  ({token,tokenClusterTs,tokenChangeTs,resumeMt})->
    try
      debug.info '[updateToken]', srcType,token,'gStat:',gStat[srcType]
      gStat[srcType] = {srcType}
      gStat[srcType].lastTokenPrintTs = new Date(tokenClusterTs)
      updateTokenFn = gUpdateTokenFns[srcType]
      await updateTokenFn({token,tokenClusterTs,tokenChangeTs,resumeMt})
    catch error
      debug.critical error
      return gracefulExit error
      

onWatchError = (err)->
  if err
    debug.error 'watchProp error', err
    return gracefulExit err

# startWatch
startWatch = ({savedToken,savedTokenClusterTs,fromCol,srcType,resumeMt},cb) ->
  if resumeMt
    startTs = new Date(resumeMt)
  else if savedTokenClusterTs
    # NOTE: fred -> tokenClusterTs不需要往前推12个小时
    startTs = new Date(savedTokenClusterTs)
  else
    startTs = new Date(new Date() - 12*60*60*1000)
  queryWhenInvalidToken = {_mt:{$gte: startTs}}
  debug.info 'queryWhenInvalidToken:',queryWhenInvalidToken
  opt =
    importWatchedRecordsCol: importWatchedRecordsCol
    watchedColl: fromCol,
    onChange: processItemSaveToMaster(srcType),
    # onChange: addItemToQueue(srcType)
    onTokenUpdate: onTokenUpdate(srcType),
    queryWhenInvalidToken: queryWhenInvalidToken,
    tokenDelayMs: TOKEN_DELAY_MS
    savedToken: savedToken
    lowWaterMark: 10
    highWaterMark: 50
    onError: onWatchError
    updateProcessStatusFn: getUpdateProcessStatusFn(srcType)
  
  watchHelper.watchTarget opt, (err, retObject)->
    if err
      debug.critical 'watchTarget error',err
      return cb err
    gWatchedStream[srcType] = retObject if retObject
    debug.info 'start watch Target',srcType,'savedToken',savedToken,\
    'queryWhenInvalidToken',queryWhenInvalidToken, 'retObject',retObject
    
    return cb()

###
start watch for specified srcType
@param {string} srcType
@param {object} fromCol - collections
###
processOneSource = ({srcType,fromCol,queryObj},cb)->
  sysDataId = srcType+'ToProp'
  isForce = AVGS.indexOf('force')>=0
  try
    hasRunningProcess = await gProcessHandler[srcType].checkRunningProcess()
  catch err
    debug.error error
  # NOTE: do not try cb
  if (not isForce) and hasRunningProcess
    return cb HAS_RUNNING_PROCESS
  try 
    await updateProcessStatusForWatch({srcType,startTs:new Date()})
  catch error
    debug.error error
  if rerun
    query = queryObj?[srcType]
    if not query
      debug.warn "rerun has no query for type:#{srcType}"
      return cb()
    debug.info 'start rerun',query,fromCol.toString()
    processOneFn = _updateOne(srcType)
    watchHelper.getNewTokenAndProcessRemainingDocs {
      query,
      fromCol,
      processOneFn,
      highWaterMark: 10,
    },(err,{token,tokenClusterTs})->
      debug.info 'rerun finished', srcType,'currentToken',\
        token,'currentToken tokenClusterTs',tokenClusterTs
      return cb()
  else if init#必须有IMPORT_BACK_HOUR，mt>IMPORT_BACK_HOUR
    # use mt here as first time treb_master_records is new collection, _mt is all too large.
    # NOTE:mls_treb_evow_delete_ids不存在mt字段,需要用ts来筛选
    if srcType is 'deleteEvow'
      query = {ts:{$gte:new Date(Date.now()- gImportBackSecond)}}
    else
      query = {del:{$exists:false},mt:{$gte:new Date(Date.now()- gImportBackSecond)}}
    debug.info 'start init',query,fromCol.toString()
    processOneFn = _updateOne(srcType)
    # TODO: add sort
    watchHelper.getNewTokenAndProcessRemainingDocs {
      query,
      fromCol,
      processOneFn,
      highWaterMark: 10,
    },(err,{token,tokenClusterTs})->
      debug.debug 'init finished', srcType,'currentToken',\
        token,'currentToken tokenClusterTs',tokenClusterTs
      #init结束保存token
      if not (token and tokenClusterTs)
        return cb('No token get in getNewTokenAndProcessRemainingDocs')
      onTokenUpdatefn = onTokenUpdate(srcType)
      try
        await onTokenUpdatefn {
          token,
          tokenClusterTs
          token
          ChangeTs: new Date()
        }
      catch err
        debug.error err
        return cb(err)
      #use saved token to start watch
      startWatch({
        savedToken:token,
        savedTokenClusterTs:tokenClusterTs,
        fromCol,
        sysDataId,
        srcType},
      cb)
  else
    debug.info 'start routine'
    SysData.findOne {_id:sysDataId}, {},(err,ret)->
      debug.info '[SysData]', sysDataId,ret
      return cb err if err
      token= ret?.token
      if (not token) or not (ret?.tokenClusterTs)
        NO_TOKEN_FOUND_WHEN_ROUTINE = 'no token found when do routine, please do init'
        debug.error NO_TOKEN_FOUND_WHEN_ROUTINE
        return cb NO_TOKEN_FOUND_WHEN_ROUTINE
      try
        savedToken = JSON.parse(token)
      catch e #if token is in bad format.
        INVALID_TOKEN = 'invalid token when do routine,please do init'
        debug.error INVALID_TOKEN,token
        return cb INVALID_TOKEN
      startWatch({
        savedToken,
        savedTokenClusterTs:ret?.tokenClusterTs,
        fromCol,
        sysDataId,
        srcType,
        resumeMt:ret?.resumeMt
      },cb)
      

gFromColMapping = {
  'treb':TrebRecords # {_id: 'TRBW166671'}
  'ddf':DDFRecords   # {_id: 'DDF13995054'}
  'trebMan':TrebManualRecords # {_id: 'TRBE5140959'}
  'bcreMan':BcreManualRecords # {_id: '262591091:0'}
  'bcre':BcreRecords   # {_id: 'BRER2284052'}
  'dta':TrebDtaRecords # {_id: 'TRBC5452188'}
  'deleteEvow':TrebEvowDeleteIds # { _id: 'E5580796'}
  'rahb': RahbRecords  # {_id: 'RHBH4151975'}
  'creb': CrebRecords  # {_id: 'CLGA2159918'}
  'oreb': OrebRecords  # {_id: 'OTW731271'}
  'edm': EdmRecords  # {_id: 'EDME4402593'}
  'car': CarRecords  # {_id: 'CAR40670704'}
  'reso': RESORecords  # {_id: 'CAR40600522'/'TRBN10311569'/'OTW1415582'}
  'bcreReso': BcreResoRecords
  'creaReso': CreaResoRecords
}

main = ()->
  # 验证rerun 是否存在query参数
  if rerun and (not QUERY_PARAM)
    return gracefulExit('No query parameter for rerun')
  TransitStop.distinct 'city', {}, (err, ret) ->
    debug.error err if err
    return gracefulExit(err) if err
    TransitCities = ret
    index = 0

    try
      queryObj = parseQueryParam()
      debug.info 'queryObj',queryObj
    catch err
      debug.error 'Failed to parse query parameter:', err,QUERY_PARAM
      return gracefulExit(err)

    # 初始化统计服务
    if logDir = config?.log?.path
      statImportHelper.init(logDir)
    else
      debug.error 'No log directory provided, statImport initialization skipped'
    doOne = ()->
      if index < IMPORT_SRC_TYPES.length
        srcType = IMPORT_SRC_TYPES[index]
        index++
        fromCol = gFromColMapping[srcType]
        setListener srcType
        return gracefulExit("no collection: #{srcType}") unless fromCol
        # NOTE: should not excute if rerun now, execute rerun done in cb
        return processOneSource {srcType,fromCol,queryObj}, (err,ret)->
          return gracefulExit(err) if err
          doOne()
      # rerun 结束, 退出并提示用户重新跑routine/init
      if rerun and (index >= IMPORT_SRC_TYPES.length)
        debug.info 'all srcType finished, please restart watchAndImportToProperties by routine/init'
        statImportHelper.stop() # 停止统计服务
        EXIT 0
    doOne()

main()
