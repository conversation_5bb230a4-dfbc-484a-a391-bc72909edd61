###
Description:    修复trebReso的status mapping，将MlsStatus和StandardStatus改为MlsStatus和ContractStatus

Usage:          ./start.sh -n fix_resoStatus -cmd "lib/batchBase.coffee batch/prop/fix_resoStatus.coffee preload-model dryrun"
Create date:    2025-05-21
Author:         xiaoweiLuo
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
reImportHelper = INCLUDE './reImportHelper'
impMappingRESO = INCLUDE 'libapp.impMappingRESO'
TrebResoCol = COLLECTION 'rni', 'reso_treb_evow_merged'
PropertiesCol = COLLECTION 'vow', 'properties'
TransitStop = COLLECTION('vow','transit_stop')

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 限制只处理这些MlsStatus值的房源
RESO_STATUS_TO_FIX = [null, 'ACT', 'CAN', 'Previous Status', 'Draft']
GeoCoder = MODEL 'GeoCoder'
TransitCities = null

dryrun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  TransitCities = await TransitStop.distinct 'city', {}
  cur = await TrebResoCol.find {MlsStatus:{$in:RESO_STATUS_TO_FIX}}
  stream = cur.stream()
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      return EXIT 1, err if err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (resoRecord, cb) ->
      speedMeter.check { processed: 1 }

      # 1. 获取RESO记录的status和lst
      recordCopy = JSON.parse(JSON.stringify(resoRecord))
      
      try
        impMappingRESO.formatStatusAndLst(recordCopy)
      catch err
        debug.error "Error formatting status and lst for propId:#{resoRecord._id}", err
        return cb(err)
      
      # 2. 跳过特定lst值的房源
      if recordCopy.lst in ['Draft', 'Unavailable', 'Previous Status']
        debug.warn "Skipping record with lst='#{recordCopy.lst}', propId:#{resoRecord._id}"
        speedMeter.check { skipped: 1 }
        return cb()
      
      # 3. 在properties表中查找记录
      existingProp = null
      try
        existingProp = await PropertiesCol.findOne {_id: resoRecord._id}, {projection: {status: 1, lst: 1}}
      catch findErr
        debug.error "Error finding property in properties table, propId:#{resoRecord._id}", findErr
        # 查询出错时，继续进行import尝试
      
      # 4. 只有当找到房源且status和lst一致时才跳过
      if existingProp and (existingProp.status is recordCopy.status) and (existingProp.lst is recordCopy.lst)
        debug.info "Status and lst already matching, propId:#{resoRecord._id}, status:#{existingProp.status}, lst:#{existingProp.lst}"
        speedMeter.check { unchanged: 1 }
        return cb()
      
      # 5. 当找不到房源或status与lst不一致时，重新import
      if not existingProp
        debug.error "Property not found in properties table, propId:#{resoRecord._id}, will reimport"
        speedMeter.check { notFound: 1 }
      else
        debug.info "Reimporting property with different status/lst, propId:#{resoRecord._id}, old(status:#{existingProp.status}, lst:#{existingProp.lst}), new(status:#{recordCopy.status}, lst:#{recordCopy.lst})"
      
      if dryrun
        speedMeter.check { dryRun: 1 }
        debug.info "dryrun, propId:#{resoRecord._id}, MlsStatus:#{resoRecord.MlsStatus}, ContractStatus:#{resoRecord.ContractStatus}, status:#{recordCopy.status}, lst:#{recordCopy.lst}"
        return cb()

      try
        await reImportHelper.convertRecord {
          record: resoRecord,
          srcType: 'reso',
          transitCities: TransitCities,
          targetProperties: PropertiesCol,
          GeoCoder
        }
        speedMeter.check { reImported: 1 }
      catch importErr
        debug.error "Error reimporting property, propId:#{resoRecord._id}", importErr
        return cb(importErr)

      return cb()

  helpers.streaming obj

main()