###
usage:         ./start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee
new config: ./start.sh -t batch -n addBuiltYear-monthly -cmd "lib/batchBase.coffee batch/prop/addBuiltYear.coffee" 
  createDate:    2022-04-25
  Author:        Fred
  Run frequency: when need，once a month
###
PROPERTIES = COLLECTION 'vow','properties'
SQFTS_AGGREGATE = COLLECTION 'vow','sqfts_aggregate'
propSqftLib = INCLUDE 'libapp.propSqft'
debug = DEBUG()
gIsDebug = false
helpers = INCLUDE 'lib.helpers'
speed = INCLUDE 'lib.speed'
speedMeter = speed.createSpeedMeter()

FORCE_UPDATE = false
DRYRUN = false
GAP_THRESHOLD = 20
WHOLE_RANGE_THRESHOLD = 20
GAP_EXTENSION_THRESHOLD = 5

# Check if dryrun mode is enabled
if (AVGS.length > 0)
  if AVGS.indexOf('force') >= 0
    debug.info 'force update aggregated collection'
    FORCE_UPDATE = true
  if AVGS.indexOf('dryrun') >= 0
    debug.info 'dryrun mode enabled - no database changes will be made'
    DRYRUN = true

FIELDS = {_id:1,sqft:1,sqft1:1,sqft2:1,addr:1,\
  uaddr:1,ptype2:1,city:1,cmty:1,prov:1,unt:1,sid:1}

r2 = (value,years1,years2)->
  sum = 0
  for year,count of years1
    sum += ((year - value)**2 * count)
  for year,count of years2
    sum += ((year - value)**2 * count)
  sum

lowestR2Year = (curR2,cur,end,year1,year2,step)->
  prevR2 = curR2
  while (curR2 <= prevR2) and (cur isnt end)
    # continue this direction if r2 declined
    prevR2 = curR2
    cur += step
    curR2 = r2(cur,year1,year2)
  return if cur is end then cur else (cur - step)


# calcBuiltYearForOneAddress: used to calculate the built year for one address,
# if there are multiple segments, it will return the lowest r2 year for each segment
# input: doc is the document of the address
# output: an array of calculated built years, [year, year, ...]
calcBuiltYearForOneAddress = (doc)->
  return [] unless doc.unt?.length >= 15
  blt1Years = Object.keys(doc.blt1).sort()
  blt2Years = Object.keys(doc.blt2).sort()
  searchRange = [parseInt(blt1Years[blt1Years.length - 1]),parseInt(blt2Years[0])].sort()
  # low/high is the same year
  return [searchRange[0]] if searchRange[0] is searchRange[1]
  # low/high years has only one number, and they don't agree with each other
  return [] if blt1Years.length is 1 and blt2Years.length is 1
  segments = []
  segments = splitYearRangesByGap(doc.bltHash, doc.blt1, doc.blt2)
  if segments.length == 0
    return []
  if segments.length == 1
    return findLowestR2YearFromSearchRange(searchRange, doc.blt1, doc.blt2)
  else # multiple segments
    results = []
    for segment in segments
      segBlt1 = extractYearsForSegment(doc, segment, 'blt1')
      segBlt2 = extractYearsForSegment(doc, segment, 'blt2')
      if (Object.keys(segBlt1).length == 0) or (Object.keys(segBlt2).length == 0)
        debug.info 'no years for segment', segment
        continue

      segBlt1Years = Object.keys(segBlt1).map(Number).sort()
      segBlt2Years = Object.keys(segBlt2).map(Number).sort()

      segSearchRange = [segBlt1Years[segBlt1Years.length - 1], segBlt2Years[0]].sort()
      results = results.concat findLowestR2YearFromSearchRange(segSearchRange, segBlt1, segBlt2)
    debug.info 'there are multiple segments', segments, results
    return results

# extractYearsForSegment: used to extract the bltKey which is in a segment which is {from: year, to: year, index: index}
# input: doc is the document of the address
# input: segment is the segment of the years {from: year, to: year, index: index}
# input: bltKey is the key of the blt1 or blt2, 'blt1' or 'blt2'
# output: an object of years and counts {year: count, year: count, ...}
extractYearsForSegment = (doc, segment, bltKey) ->
  result = {}
  for year, count of doc[bltKey]
    result[year] = count if parseInt(year) >= segment.from and parseInt(year) <= segment.to
  result

# findLowestR2YearFromSearchRange: used to find the lowest r2 year from the search range
# input: searchRange is the search range of the years [year, year]
# input: blt1 is the doc.blt1
# input: blt2 is the doc.blt2
# output: an array of calculated built years, [year]
findLowestR2YearFromSearchRange = (searchRange, blt1, blt2) ->
  middleLow = Math.floor((searchRange[0] + searchRange[1]) / 2.0)
  middleHigh = middleLow + 1

  middleLowR2 = r2(middleLow, blt1, blt2)
  middleHighR2 = r2(middleHigh, blt1, blt2)

  if middleLowR2 < middleHighR2
    return [lowestR2Year middleLowR2, middleLow, searchRange[0], blt1, blt2, -1]
  else if middleLowR2 > middleHighR2
    return [lowestR2Year middleHighR2, middleHigh, searchRange[1], blt1, blt2, 1]
  else
    return [middleLow]


# split the year ranges by gap
# input: bltHash is the doc.bltHash
# input: blt1 is the doc.blt1
# input: blt2 is the doc.blt2
# output: an array of segments, [{from: year, to: year, index: index}, {from: year, to: year, index: index}, ...]
splitYearRangesByGap = (bltHash, blt1, blt2) ->
  return [] unless Array.isArray(bltHash) and blt1? and blt2?

  yearFreq = buildYearFrequency(bltHash, blt1, blt2)
  allYears = Object.keys(yearFreq).map(Number).sort((a, b) -> a - b)
  return [] if allYears.length == 0

  allYrReturn = [from: allYears[0], to: allYears[allYears.length - 1], index: 0]
  return allYrReturn if allYears[allYears.length - 1] - allYears[0] < WHOLE_RANGE_THRESHOLD

  highFreqYears = allYears.filter((y) -> yearFreq[y] > 1)
  return allYrReturn if highFreqYears.length == 0

  segments = splitByLargeGaps(highFreqYears)
  return allYrReturn if segments.length < 2

  gapSegmentRanges = buildGapSegments(segments.map((s) -> s.from), allYears)
  mergedSegments = mergeSegments(Object.values(gapSegmentRanges))

  finalSegments = mergedSegments.map (s, i) ->
    from: s.from
    to: s.to
    index: i

  debug.debug 'finalSegments', finalSegments
  return finalSegments

# build the year frequency of the bltHash
# input: bltHash is the doc.bltHash
# input: blt1 is the doc.blt1
# input: blt2 is the doc.blt2
# output: an object of years and counts {year: count, year: count, ...}
buildYearFrequency = (bltHash, blt1, blt2) ->
  yearFreq = {}
  for entry in bltHash
    parts = entry.split(':')
    continue unless parts.length == 3
    y1 = parseInt(parts[1])
    y2 = parseInt(parts[2])
    continue if isNaN(y1) or isNaN(y2)
    count1 = blt1[String(y1)] or 0
    count2 = blt2[String(y2)] or 0
    count = Math.min(count1, count2)
    continue if count == 0

    for i in [0...count]
      for y in [y1..y2]
        yearFreq[y] = (yearFreq[y] or 0) + 1
  return yearFreq

# split the years by large gaps using GAP_THRESHOLD
# input: years is the array of years [year, year, ...]
# output: an array of segments, [{from: year, to: year, index: index}, {from: year, to: year, index: index}, ...]
splitByLargeGaps = (years) ->
  segments = []
  currentStart = years[0]
  index = 0
  for i in [1...years.length]
    prev = years[i - 1]
    curr = years[i]
    if curr - prev >= GAP_THRESHOLD
      segments.push({ from: currentStart, to: prev, index: index++ })
      currentStart = curr
  segments.push({ from: currentStart, to: years[years.length - 1], index: index++ })
  return segments

# build the gap segments according to the gap starts
# input: gapStarts is the array of gap starts [year, year, ...]
# input: allYears is the array of all years [year, year, ...], result of buildYearFrequency
# output: an object of ranges {year: {from: year, to: year}, year: {from: year, to: year}, ...}
buildGapSegments = (gapStarts, allYears) ->
  ranges = {}
  for gapStart in gapStarts
    from = gapStart
    to = gapStart

    # forward
    i = allYears.indexOf(gapStart)
    while i > 0
      prev = allYears[i - 1]
      curr = allYears[i]
      if curr - prev <= GAP_EXTENSION_THRESHOLD
        from = prev
        i -= 1
      else
        break

    # backward
    i = allYears.indexOf(gapStart)
    while i < allYears.length - 1
      curr = allYears[i]
      next = allYears[i + 1]
      if next - curr <= GAP_EXTENSION_THRESHOLD
        to = next
        i += 1
      else
        break

    ranges[gapStart] = { from, to }
  return ranges

# merge the segments if they are continuous or overlap
# input: segments is the array of segments [{from: year, to: year, index: index}, {from: year, to: year, index: index}, ...]
# output: an array of merged segments [{from: year, to: year, index: index}, {from: year, to: year, index: index}, ...]
mergeSegments = (segments) ->
  segments.sort (a, b) -> a.from - b.from
  merged = []
  for seg in segments
    if merged.length == 0
      merged.push(seg)
    else
      last = merged[merged.length - 1]
      if seg.from <= last.to + 1
        last.to = Math.max(last.to, seg.to)
      else
        merged.push(seg)
  return merged


needCount = (storeys,storeyAccuracy=0.95)->
  if 0.945 < storeyAccuracy < 0.955
    return storeys * 3 - 1.5
  if 0.985 < storeyAccuracy < 0.995
    return storeys * 4.6 - 2.3
  return Math.log10(1-storeyAccuracy)/Math.log10((storeys-1)/storeys)

ACCURACY_NEEDED = 0.95

calcStorey = (doc)->
  if not ((bltCnt = doc.bltCnt) and (lvls = doc.lvls))
    return null
  lvlsCnt = lvls.length
  needCnt = needCount lvlsCnt,ACCURACY_NEEDED
  if (bltCnt < needCnt) or (lvlsCnt < 4)
    return null
  # we have enough count to ensure the storeyAccuracy
  errorPossibility = (lvlsCnt-1)/lvlsCnt
  storeyAccuracy = 1 - errorPossibility**bltCnt
  numLvls = []
  for lvl in lvls
    if /^\d+$/.test lvl
      l = parseInt lvl
      numLvls.push(l) if not (l in numLvls)
    else if m2 = lvl.match(/^(\D+)(\d+)$/) # PH9
      l = parseInt m2[2]
      numLvls.push(l) if not (l in numLvls)
    else
      numLvls.push lvl
  numLvls = numLvls.sort (a,b)->
    if 'number' is typeof a
      if 'number' is typeof b
        return a - b
      else
        return -1
    else
      if 'number' is typeof b
        return 1
      else
        if a > b
          return 1
        else if a < b
          return -1
        return 0
  rmStoreys = numLvls.length
  if numLvls[0] isnt 1
    rmStoreys += (numLvls[0] - 1)
  missingLvls = []
  l = 2
  for lvl in numLvls
    if 'number' isnt typeof lvl
      continue
    while l < lvl
      missingLvls.push l
      l++
    l++
  ret = {rmStoreys,storeyAccuracy,numLvls}
  if missingLvls.length > 0
    ret.missingLvls = missingLvls
  return ret


AGGREGATED_FIELDS = {_id:1,blt1:1,blt2:1,bltCnt:1,lvls:1,rmBltYr:1,rmStoreys:1,unt:1,rmBltYr0:1,bltHash:1}
calcBuiltYearOnAggregatedAddress = (cb)->
  SQFTS_AGGREGATE.createIndex {bltCnt:1},(err)->
    return EXIT 1,err if err
    speedMeter.reset()
    query = {bltCnt:{$gte:1}}
    SQFTS_AGGREGATE.find query,{projection:AGGREGATED_FIELDS},(err,cur)->
      streamProcessor =
        verbose: 1
        high: 50
        stream: cur.stream()
        end: (err)->
          debug.info 'Finished Aggregated Address',query,{gProcessed},speedMeter.toString()
          return EXIT 1,err if err
          cb()
        process: (doc,callback)->
          gProcessed++
          if 0 is (gProcessed % 1000)
            helpers.relog gProcessed+', '+speedMeter.toString()
          rmBltYrs = calcBuiltYearForOneAddress doc
          rmStoreys = calcStorey doc
          return callback() if not (FORCE_UPDATE or rmBltYrs?.length or rmStoreys)
          set = rmStoreys or {}
          if rmBltYrs?.length
            # Set the last year as rmBltYr
            set.rmBltYr = rmBltYrs[rmBltYrs.length - 1]
            # Set all other years as a list in rmBltYr0
            if rmBltYrs.length > 1
              set.rmBltYr0 = rmBltYrs[...-1]
          if FORCE_UPDATE or (set.rmBltYr isnt doc.rmBltYr) or (set.rmStoreys isnt doc.rmStoreys)
            # update it only when changed
            update = $set:set
            if not rmStoreys?.missingLvls
              update.$unset = {missingLvls:1}
            if not rmBltYrs?.length
              update.$unset ?= {}
              update.$unset.rmBltYr = 1
              update.$unset.rmBltYr0 = 1
            debug.debug 'update',update
            
            if DRYRUN
              try
                debug.info 'DRYRUN: Would update document', {
                  _id: doc._id
                  current: {
                    _id: doc._id
                    rmBltYr: doc.rmBltYr
                    rmBltYr0: doc.rmBltYr0
                    blt1: doc.blt1
                    blt2: doc.blt2
                    bltHash: doc.bltHash
                  }
                  new: set
                  update: update
                }
              catch logError
                debug.error 'DRYRUN logging error:', logError
              callback()
            else
              SQFTS_AGGREGATE.updateOne {_id:doc._id},update,(err)->
                category = {}
                category[doc.bltCnt] = 1
                speedMeter.check category
                callback err
          else
            callback()
      helpers.streaming streamProcessor

gProcessed = 0
gNoAddress = 0
gFound = 0
gNotFound = 0

updateProperties = ()->
  speedMeter.reset()
  gProcessed = 0
  query = {ptype:'r',uaddr:{$ne:null}}
  PROPERTIES.find query,{projection:{rmBltYr:1,uaddr:1, unt:1, untStorey:1}},(err,cur)->
    obj =
      verbose: 1
      high: 50
      stream: cur.stream()
      end: (err)->
        debug.info 'FINISHED',query,{gProcessed,gNoAddress,gFound,gNotFound},speedMeter.toString()
        if err
          return EXIT 1,err
        EXIT 0
      process: (prop,cb)->
        gProcessed++
        if 0 is (gProcessed % 1000)
          helpers.relog gProcessed+', '+speedMeter.toString()+', '\
          +"NoAddress:#{gNoAddress},Found:#{gFound},NotFound:#{gNotFound}"
        debug.verbose prop if gIsDebug
        if not prop.uaddr
          gNoAddress++
          speedMeter.check {NoAddress:1}
          return cb()
        try
          aggregated = await propSqftLib.getBuildingInfoAsync prop,SQFTS_AGGREGATE
          # aggregated = await SQFTS_AGGREGATE.findOne {_id:prop.uaddr,rmBltYr:{$ne:null}},\
          #   {projection:{rmBltYr:1,rmStoreys:1}}
          if not aggregated?
            gNotFound++
            speedMeter.check {NotFound:1}
            return cb()
          speedfeed = {Found:1}
          update = {}
          if aggregated.rmBltYr and (prop.rmBltYr isnt aggregated.rmBltYr)
            update.$set ?= {}
            update.$set.rmBltYr = aggregated.rmBltYr
          if (not aggregated.rmBltYr) and prop.rmBltYr # to fix old data
            update.$unset ?= {rmBltYr : 1}
          if aggregated.untStorey and (prop.untStorey isnt aggregated.untStorey)
            update.$set ?= {}
            update.$set.untStorey = aggregated.untStorey
          if update.$set or update.$unset
            await PROPERTIES.updateOne {_id: prop._id}, update
            speedfeed.updated = 1
          speedMeter.check speedfeed
          gFound++
          return cb()
        catch error
          debug.error error
          EXIT 1,error

    helpers.streaming obj


main = ()->
  calcBuiltYearOnAggregatedAddress ->
    if DRYRUN
      debug.info 'DRYRUN: Would update properties'
      EXIT 0
    else
      updateProperties()

main()