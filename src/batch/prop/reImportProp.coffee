###
  Description: 从properties数据库找出40天未更新的房源，把prop._id 加入到队列中，重新导入
  db.properties.find({onD:{$lte:20240223},status:'A',ptype:'r',mt:{$lte:ISODate("2024-03-06T19:49:19.180Z")}}).count()

  Options:
      -d --date:        query date gte ts, format YYYY-MM-DD
      -a --days:        query days ago, number
      -b --business:    if set, query business properties
  New config:
    ./start.sh -t batch -n reImportProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model dryrun -d 2024-01-01 -a [days] -b"
  createDate:    2024-02-21
  Author:        xiaoweiLuo
  Run frequency: when need

1. 对于reso(TRB),bcreReso(BRE),creaReso(DDF)房源,找到未更新的active房源之后
   1. 状态一致(rni为A),通过addToQueue重新 download
   2. 状态不一致(rni为U),error log,重新import
2. 对于creb(CLG),car(CAR),edm(EDM)房源,找到未更新的active房源之后
   1. 状态一致(rni为A),warning log
   2. 状态不一致(rni为U),error log,重新import


###


debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
ResourceDownloadQueue = require '../../libapp/mlsResourceDownloadQueue'
speed = INCLUDE 'lib.speed'
{inputToDateNum} = INCLUDE 'lib.helpers_date'
reImportHelper = INCLUDE './reImportHelper'
config = CONFIG(['contact', 'mailEngine', 'mailEngineList'])
sendMailLib = INCLUDE 'lib.sendMail'
sendMailObj = sendMailLib.getSendMail(config)

yargs = require('yargs')

PropertiesCol = COLLECTION 'vow', 'properties'
TransitStop = COLLECTION('vow','transit_stop')
TrbQueueCol = COLLECTION('rni','reso_treb_evow_property_queue')
BreQueueCol = COLLECTION('rni','bridge_bcre_property_queue')
CreaQueueCol = COLLECTION('rni','reso_crea_property_queue')
TRBRniCol = COLLECTION('rni','reso_treb_evow_merged')

GeoCoder = MODEL 'GeoCoder'
TransitCities = null
REIMPORT_PROP_PRIORITY = 20000

yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query date gte rmmt,formate:YYYY-MM-DD', required: false, type: 'string'}
yargs.option 'days', { alias: 'a', description: 'query days ago', required: false, type: 'number'}
yargs.option 'business', { alias: 'b', description: 'if set, query business properties', required: false, type: 'boolean'}

START_DATE = yargs.argv.date
DAYS = yargs.argv.days
IS_BUSINESS = yargs.argv.business
dryRun = AVGS.indexOf('dryrun') >= 0
NOW = new Date()
# TEN_DAYS_AGO = new Date(NOW - 10 * 24 * 3600 * 1000)
FOURTY_DAYS_AGO = new Date(NOW - 40 * 24 * 3600 * 1000)
SIXTY_DAYS_AGO = new Date(NOW - 60 * 24 * 3600 * 1000)
ONE_HUNDRED_DAYS_AGO = new Date(NOW - 100 * 24 * 3600 * 1000)
FIFTY_DAYS_AGO = new Date(NOW - 50 * 24 * 3600 * 1000)
ONE_YEAR_AGO = new Date(NOW - 365 * 24 * 3600 * 1000)

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

QUEUES_MAP = {
  'TRB': new ResourceDownloadQueue('property', TrbQueueCol),
  'BRE': new ResourceDownloadQueue('property', BreQueueCol),
  'DDF': new ResourceDownloadQueue('property', CreaQueueCol),
}

startTs = new Date()
###
NOTE: db.properties.count({
  onD:{$lte:20240417},
  status:'A',
  ptype:'r',
  src:'TRB',
  mt:{$lte:ISODate("2024-04-17T05:00:00.000Z")}
}) = 20DAYS = 20639
30DAYS = 13735
10DAYS = 31286
40DAYS = 4256
###
# NOTE: 没找到的房源不在这里处理，在另一个脚本处理
gNotFound = ''
printNotFound = (propId)->
  # debug.error 'prop has no orig record,propId:',propId
  gNotFound += '.'
  if gNotFound.length > 100
    debug.error 'prop has no orig record:',gNotFound
    gNotFound = ''

main = ()->
  # reImport默认不做geocoding
  GeoCoder.noGeocoding(true)
  TransitCities = await TransitStop.distinct 'city', {}
  queryDays = if DAYS then new Date(NOW - DAYS * 24 * 3600 * 1000) else SIXTY_DAYS_AGO
  # NOTE: average dom is between 12-38, now is 20.4
  query = {
    onD:{$lte:inputToDateNum(queryDays)},
    status:'A',
    ptype: 'r',
    src:{$ne:'RM'},
    mt:{$lte:queryDays}
  }

  if IS_BUSINESS
    query.src = 'TRB'
    query.ptype = 'b'
    queryDaysBusiness = if DAYS then new Date(NOW - DAYS * 24 * 3600 * 1000) else ONE_HUNDRED_DAYS_AGO # 100 days for business properties
    queryMt = if DAYS then new Date(NOW - DAYS * 24 * 3600 * 1000/2) else FIFTY_DAYS_AGO
    query.onD = {$lte:inputToDateNum(queryDaysBusiness)}
    query.mt = {$lte:queryMt}
    debug.info "query: #{query}"

  if START_DATE
    query.rmmt = {$gte:new Date(START_DATE)}
    # query.ts = {$gte:ONE_YEAR_AGO}
  projection = {status:1,lst:1,src:1,sid:1,lp:1,lpr:1,mt:1,ptype2:1,bcf:1,ListingKey:1}
  debug.info 'start running batch with query:',query
  cur = await PropertiesCol.find query,{projection}
  stream = cur.stream()
  obj =
    verbose: 1
    high:10
    stream: stream
    end: (err) ->
      if err
        msg = "reImportProp error: #{err}"
        debug.error msg
        sendMailObj.notifyAdmin(msg)
        return EXIT 1, err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Total process time #{processTs} mins #{speedMeter.toString()}"
      if speedMeter.values.watchError > 20
        msg = "reImportProp watch error > 20, watch&rni different state"
        debug.error msg
        sendMailObj.notifyAdmin(msg)
        return EXIT 0, msg
      debug.info speedMeter.toString()
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check {processCount:1}

      try
        result = await reImportHelper.getRniRecord prop
      catch err
        debug.error 'getRniRecord error,propId:', err, prop._id
        speedMeter.check {getRniRecordError:1}
        return cb(err)

      if not result?.rniRecord
        printNotFound(prop._id)
        speedMeter.check {notFound:1}
        return cb()

      if not result?.srcType
        debug.error "prop:#{prop._id} is not found in srcTypeMapping"
        speedMeter.check {notFoundInSrcTypeMapping:1}
        return cb()

      try
        formatedProp = await reImportHelper.convertRecord {
          record:result.rniRecord,
          srcType:result.srcType,
          transitCities:TransitCities,
          targetProperties:PropertiesCol,
          GeoCoder,
          onlyConvert:true
        }
      catch err
        debug.error err," propId:#{prop._id}"
        speedMeter.check {convertError:1}
        return cb(err)

      if not formatedProp
        debug.error "prop:#{prop._id} is not formated"
        speedMeter.check {notFormated:1}
        return cb()
      
      # 对比rni与properties房源数据,判断问题来源
      if reImportHelper.isSamePropAndOrigProp(prop,formatedProp)
        # rni与properties房源一致，watch没有问题，重新import确认问题
        # addToQueue只处理TRB,BRE,DDF的房源
        if prop.src not in ['TRB','BRE','DDF']
          speedMeter.check {notAddToQueue:1}
          debug.warn "prop:#{prop._id} is same to rni, but prop.src is not 'TRB','BRE','DDF'"
          return cb()

        # For business properties, check TRBRniCol first
        if IS_BUSINESS
          trbRniRecord = await TRBRniCol.findOne {_id: prop._id}
          if not trbRniRecord
            debug.info "Business property not found in reso rni, setting status to U", prop._id
            if not DRYRUN
              await PropertiesCol.updateOne {_id: prop._id}, {$set: {status: 'U'}}
            speedMeter.check {businessStatusUpdated:1}
            return cb()

        queueId = null
        if prop.src is 'DDF'
          # 历史的ddf房源，没有ListingKey，用_id.slice(3)代替
          queueId = if prop.ListingKey then prop.ListingKey else prop._id.slice(3)
        else
          queueId = prop.sid

        if not queueId
          debug.error "prop:#{prop._id} is same to rni, but not found queueId, sid:#{prop.sid}, ListingKey:#{prop.ListingKey}"
          speedMeter.check {noQueueId:1}
          return cb()

        speedMeter.check {addedToQueue:1}
        debug.info "prop:#{prop._id} is same to rni, add to queue"
        if not DRYRUN
          try
            # queueId可能会被转为number,需要改为string
            await QUEUES_MAP[prop.src].addToQueue({_id:queueId.toString()},REIMPORT_PROP_PRIORITY)
          catch e
            debug.error 'addToQueue error:',e
            return cb(e)
      else
        # watch问题 log
        obj = {}
        for key of projection
          obj[key] = formatedProp[key]

        debug.error "prop:#{prop._id} is different to rni , vow:",prop,' rni:',obj
        speedMeter.check {watchError:1}
        # 重新import
        if not DRYRUN
          try
            await reImportHelper.convertRecord {
              record:result.rniRecord,
              srcType:result.srcType,
              transitCities:TransitCities,
              targetProperties:PropertiesCol,
              GeoCoder
            }
            speedMeter.check {reImported:1}
          catch err
            debug.error err," propId:#{prop._id}"
            return cb(err)
      return cb()

  helpers.streaming obj

main()