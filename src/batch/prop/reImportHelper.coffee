###
* @description 房源重新导入辅助模块，提供获取RNI记录和相关集合的功能
* @module reImportHelper
* <AUTHOR>
* @created 2025-03-06
###

debug = DEBUG()
{ getFloat } = INCLUDE 'libapp.properties'
{ dayDiff } = INCLUDE 'lib.helpers_date'
{addAsyncSupport} = INCLUDE 'lib.helpers_function'
{convertAndSaveRecord} = INCLUDE('libapp.saveToMaster')
convertAndSaveRecordAsync = addAsyncSupport convertAndSaveRecord

# RNI相关集合定义
ResoTrebCol = COLLECTION 'rni', 'reso_treb_evow_merged'
TrebCol = COLLECTION 'rni', 'mls_treb_master_records'
TrebManualCol = COLLECTION 'rni', 'mls_treb_manual_records'
ResoBcreCol = COLLECTION 'rni', 'bridge_bcre_merged'
BcreCol = COLLECTION 'rni', 'mls_bcre_records'
BcreManualCol = COLLECTION 'rni', 'mls_bcre_manual_records'
CarCol = COLLECTION 'rni', 'mls_car_master_records'
EdmCol = COLLECTION 'rni', 'mls_rae_master_records'
Edm2Col = COLLECTION 'rni', 'mls_rae_records_old'
CrebCol = COLLECTION 'rni', 'mls_creb_master_records'
RahbCol = COLLECTION 'rni', 'mls_rahb_records'
OrebCol = COLLECTION 'rni', 'mls_oreb_master_records'
CreaResoCol = COLLECTION 'rni', 'reso_crea_merged'
CreaCol = COLLECTION 'rni', 'mls_crea_ddf_records'


PropertiesImportLog = COLLECTION('tmp','properties_import_log')
TransitStop = COLLECTION('vow','transit_stop')
School = COLLECTION('vow','school')
Boundary = COLLECTION('vow','boundary')
Census2016 = COLLECTION('vow','census2016')
Census2016ReversKeys =COLLECTION('vow','census2016ReversKeys')
Census2021 = COLLECTION('vow','census2021')
SqftMiddle = COLLECTION('tmp','sqft_middle')
SqftsAggregate = COLLECTION('vow','sqfts_aggregate')
SqftToProcess = COLLECTION('vow','sqft_to_process')
AgentCol = COLLECTION('vow', 'agents')
OfficeCol = COLLECTION('vow', 'offices')
ProvCityCmty = COLLECTION('vow','prov_city_cmty')

# 导入映射模块
impMappingTreb = INCLUDE 'libapp.impMappingTreb'
impMappingDdf = INCLUDE 'libapp.impMappingDdf'
impMappingBcreManual = INCLUDE 'libapp.impMappingBcreManual'
impMappingBcre = INCLUDE 'libapp.impMappingBcreAuto'
impMappingBcreReso = INCLUDE 'libapp.impMappingBcreReso'
impMappingProperty = INCLUDE 'libapp.impMappingProperty'
impMappingRahb = INCLUDE 'libapp.impMappingRahb'
impMappingCreb = INCLUDE 'libapp.impMappingCreb'
impMappingOreb = INCLUDE 'libapp.impMappingOreb'
impMappingEdm = INCLUDE 'libapp.impMappingEdm'
impMappingCar = INCLUDE 'libapp.impMappingCar'
impMappingRESO = INCLUDE 'libapp.impMappingRESO'
impMappingCreaReso = INCLUDE 'libapp.impMappingCreaReso'

# 源类型与集合名称的映射关系
SRC_TYPE_COLL_MAPPING =
  'mls_treb_master_records': 'treb'
  'mls_treb_manual_records': 'trebMan'
  'mls_bcre_manual_records': 'bcreMan'
  'mls_bcre_records': 'bcre'
  'mls_crea_ddf_records': 'ddf'
  'mls_treb_dta_records': 'dta'
  'mls_rahb_records': 'rahb'
  'mls_creb_master_records': 'creb'
  'mls_oreb_master_records': 'oreb'
  'mls_rae_master_records': 'edm'
  'mls_rae_records_old': 'edm'
  'mls_car_master_records': 'car'
  'bridge_bcre_merged': 'bcreReso'
  'reso_treb_evow_merged': 'reso'
  'reso_crea_merged': 'creaReso'

###
* @description 源类型与对应的RNI集合映射关系
* @type {Object.<string, Array>}
###
RNI_COLLECTIONS_MAP = {
  'TRB': [ResoTrebCol, TrebCol, TrebManualCol]        # Try RESO format first, then legacy
  'BRE': [ResoBcreCol, BcreCol, BcreManualCol]        # Try RESO format first, then legacy
  'CAR': [CarCol]                                     # Single collection
  'EDM': [EdmCol, Edm2Col]                            # Current format first, then old format
  'CLG': [CrebCol]                                    # Single collection
  'RHB': [RahbCol]                                    # Single collection
  'OTW': [OrebCol]                                    # Single collection
  'DDF': [CreaResoCol, CreaCol]                       # Try RESO format first, then legacy
}

###
* @description 根据房源来源获取对应的RNI集合
* @param {string} src - 房源来源代码
* @returns {Array|null} - 返回对应的集合数组，按优先级排序，如果不支持则返回null
###
module.exports.getRniCollection = getRniCollection = (src) ->
  if not src?
    return null
    
  return RNI_COLLECTIONS_MAP[src] or null

###
* @description 获取房源在RNI中的记录及其源类型
* @param {Object} prop - 房源对象
* @returns {Object|undefined} - 返回包含rniRecord和srcType的对象，如果未找到则返回undefined
###
module.exports.getRniRecord = getRniRecord = (prop) ->
  if not prop?
    return
  collections = getRniCollection(prop.src)
  if not collections
    debug.error "No collections found for source: #{prop.src}, property: #{prop._id}"
    return
  # 查找RNI记录
  rniRecord = null
  srcType = null
  
  for collection in collections
    # creb/oreb查找时sid，其余prop._id
    if prop.src in ['CLG', 'OTW'] and prop.sid
      query = {sid: prop.sid}
    else if collection is BcreManualCol
      query = {Ml: prop.sid}
    else
      query = {_id: prop._id}

    rniRecord = await collection.findOne query
      
    if rniRecord
      srcType = SRC_TYPE_COLL_MAPPING[collection.collname]
      break

  return {rniRecord, srcType}

module.exports.convertRecord = convertRecord = ({
  record,
  srcType,
  transitCities,
  targetProperties,
  GeoCoder,
  onlyConvert=false
})->
  return await convertAndSaveRecordAsync {
    srcType,
    GeoCoder,
    targetProperties,
    PropertiesImportLog,
    record,
    transitCities,
    TransitStop,
    School,
    Boundary,
    Census2016,
    Census2016ReversKeys,
    Census2021,
    SqftMiddle,
    SqftsAggregate,
    SqftToProcess,
    AgentCol,
    OfficeCol,
    ProvCityCmty,
    onlyConvert
  }

module.exports.isSamePropAndOrigProp = isSamePropAndOrigProp = (prop,origProp)->
  debug.debug 'origPropNew:',origProp
  debug.debug 'prop:',prop

  # origProp的mt需要确认
  if (dayDiff(prop.mt,origProp.mt)) > 1
    return false
  if prop.status isnt origProp.status
    return false
  if prop.lst isnt origProp.lst
    return false
  # Lp_dol lp lpr
  origLp = getFloat origProp.lp or origProp.lpr
  nowLp = getFloat prop.lp or prop.lpr
  if origLp isnt nowLp
    return false
  return true
