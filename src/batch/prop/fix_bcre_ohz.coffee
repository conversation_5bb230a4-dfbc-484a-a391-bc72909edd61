###
Description:    修复BCRE房源的open house时区

Usage:          ./start.sh -n fix_bcre_ohz -cmd "lib/batchBase.coffee batch/prop/fix_bcre_ohz.coffee dryrun"
                
Create date:    2025-05-19
Author:         <PERSON><PERSON><PERSON><PERSON>ei
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
PropertiesCol = COLLECTION 'vow', 'properties'
BcreCol = COLLECTION 'rni', 'bridge_bcre_merged'
{formatOpenHouse} = INCLUDE 'libapp.impMappingBcreReso'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryrun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 从bridge_bcre_merged表获取所有包含openHouse字段的记录
  cur = await BcreCol.find {openHouse:{$ne:null}}
  stream = cur.stream()
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      return EXIT 1, err if err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (bcreProp, cb) ->
      speedMeter.check { processed: 1 }
      
      # 使用新的时区转换函数重新格式化open house时间
      formatOpenHouse(bcreProp)
      newOpenHouseTimes = bcreProp.ohz
      if not newOpenHouseTimes
        speedMeter.check { skip: 1 }
        return cb()

      # 检查properties表中是否存在对应记录
      prop = await PropertiesCol.findOne {_id:bcreProp._id}
      unless prop
        debug.info "#{bcreProp._id} not found in properties table"
        speedMeter.check { skip: 1 }
        return cb()

      debug.info "#{bcreProp._id} change open house times"+JSON.stringify newOpenHouseTimes
      update = {
        noModifyMt:true,
        $set: { ohz: newOpenHouseTimes }
      }

      if dryrun
        debug.info "#{bcreProp._id} dryrun, skip update", newOpenHouseTimes
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne { _id: bcreProp._id }, update
        speedMeter.check { updated: 1 }
      catch err
        debug.error err," propId:#{bcreProp._id}"
        speedMeter.check { updateFailed: 1 }
        return cb()

      return cb()

  helpers.streaming obj

main()