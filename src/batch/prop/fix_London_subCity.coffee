###
Description:    修复房源London的subCity问题
Usage:          ./start.sh -n fix_London_subCity -cmd "lib/batchBase.coffee batch/prop/fix_London_subCity.coffee dryrun -d [date]"
Create date:    2025-06-16
Author:         xiaoweiLuo
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{subCityNameList} = INCLUDE 'lib.staticCityFields'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query date gte mt', required: false, type: 'string'}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 查询非RM房源
  query = {city:/^London /,src:{$ne:'RM'}}
  if yargs.argv.date
    query.mt = {$gte: new Date(yargs.argv.date)}
  projection = {city:1,subCity:1}
  
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      if prop.city not in subCityNameList['ON']['London']
        debug.warn "propId: #{prop._id} city is not in London",prop
        speedMeter.check { notLondon: 1 }
        return cb()

      update = {noModifyMt: true,$set:{city:'London',subCity:prop.city}}
        
      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, update:",update['$set']
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()