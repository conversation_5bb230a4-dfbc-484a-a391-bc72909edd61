###
  Description: 检索特定source的房源，查找并删除相同的log文件
  Options:
      --source:    Source of prop [TRB|BRE|DDF|RHB|OTW|CLG]
      all:         run all mt

  new config:    sh start.sh -t batch -n rmDupLog -cmd 'lib/batchBase.coffee batch/prop/rmDupLog.coffee preload-model [dryrun] [debug]  -s DDF all'
  createDate:    2024-10-22
  Author:        Maggie
  Run frequency: when needed
###
helpers = INCLUDE 'lib.helpers'
speed = INCLUDE 'lib.speed'
{ guessSrc } = INCLUDE 'libapp.properties'
{ getLogDocsForDiffSrc,sameDataExceptLastUpdated,PROP_SRC_TS_MAPPING } = INCLUDE 'libapp.impCommon'
{ processKillSignal} = INCLUDE 'libapp.processHelper'
debug = DEBUG()
PropertiesCol = COLLECTION('vow', 'properties')
TrebEvowLogs = COLLECTION('rni','mls_treb_evow_logs')
TrebIdxLogs = COLLECTION('rni','mls_treb_idx_logs')
BcreLogs = COLLECTION('rni','mls_bcre_logs')
DDFLogs = COLLECTION('rni','mls_crea_ddf_logs')
RahbLogs = COLLECTION('rni','mls_rahb_logs')
OrebLogs = COLLECTION('rni','mls_oreb_logs')
CrebLogs = COLLECTION('rni','mls_creb_logs')
SysdataCol = COLLECTION('rni','sysdata')
TmpCol = COLLECTION('tmp','rmDupLogTmp')
TmpCol.createIndex {'_exp30d':1},{expireAfterSeconds:0}

yargs = require('yargs')(AVGS)
yargs.option 'source', { alias: 's', description: 'source'}
dryRun = AVGS.indexOf('dryrun') >= 0
runAll = AVGS.indexOf('all') >= 0
RM_DUP_LOG_ID = 'rmDupLog'
STREAM_HIGH = 25
STREAM_LOW = 20
gEmptyCount = 0
gSysdataId = null
gOldestMt = null
ALLOWED_SOURCE = ['TRB', 'BRE', 'DDF', 'RHB', 'OTW', 'CLG']

COLL_SRC_LOGS_MAP = {
  TRB:TrebEvowLogs
  TRBX:TrebIdxLogs
  BRE:BcreLogs,
  DDF:DDFLogs,
  RHB:RahbLogs,
  OTW:OrebLogs,
  CLG:CrebLogs
}

EXPIRE_DAYS_TIME = 30 * 24 * 60 * 60 * 1000

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 100,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
    updateSysdata()
}
startTs = new Date()


deleteLog =  (logDoc) ->
  if dryRun
    debug.info 'dryRun, skip delete log:', (logDoc._source).toString(),logDoc._id
    speedMeter.check {logDeleted:1}
  else
    try
      debug.info 'deleting log:', (logDoc._source).toString(),logDoc._id
      logDoc._exp30d = new Date(Date.now() + EXPIRE_DAYS_TIME) # 设置一个月后过期
      await TmpCol.insertOne logDoc
      await COLL_SRC_LOGS_MAP[logDoc._source].deleteOne {_id: logDoc._id}
      debug.info 'log deleted:', logDoc._id
      speedMeter.check {logDeleted:1}
    catch error
      debug.error 'Failed to delete log:', logDoc._id, 'Error:', error.message
      speedMeter.check {logDeleteFailed:1}


isLastUpdatedTsEqual = (baseLogDoc, logDoc, src) ->
  timestampPath = PROP_SRC_TS_MAPPING[src]
  if not timestampPath
    debug.error "Unknown source type: #{src}"
    throw new Error("Unknown source type: #{src}")
  # Function to retrieve the nested value based on path array
  getNestedValue = (obj, path) ->
    path.reduce((acc, key) ->
      if acc? then acc[key] else undefined
    , obj)
  # Retrieve timestamps using the defined paths for the given source
  baseTimestamp = getNestedValue(baseLogDoc.data, timestampPath)
  logTimestamp = getNestedValue(logDoc.data, timestampPath)
  # Handle undefined or invalid timestamps
  if not baseTimestamp? or not logTimestamp?
    debug.error 'Invalid timestamp values', {
      baseId: baseLogDoc._id
      logId: logDoc._id
      baseTimestamp: baseTimestamp
      logTimestamp: logTimestamp
    }
    return false
  return baseTimestamp is logTimestamp
 

rmDupLog = (prop)->
  logsInOrder = await getLogDocsForDiffSrc(prop,COLL_SRC_LOGS_MAP)
  debug.debug 'logsInOrder:',logsInOrder.length, prop._id
  speedMeter.check {Log:logsInOrder.length}
  if logsInOrder.length is 0
    gEmptyCount++
  else
    gEmptyCount = 0
  if logsInOrder.length < 2
    return
  # loop through the logs to find and delete the same log
  baseLogDoc = logsInOrder[0]
  sameCount = 0
  sameExceptLastUpdatedCount = 0
  for i in [1...logsInOrder.length]
    logDoc = logsInOrder[i]
    # same log except lastUpdated
    if sameDataExceptLastUpdated(baseLogDoc.data, logDoc.data)
      if isLastUpdatedTsEqual(baseLogDoc,logDoc,prop.src)
        sameCount++
      else
        sameExceptLastUpdatedCount++
      debug.debug 'same log found:', logDoc._id, prop._id
      await deleteLog(logDoc)
      continue
    baseLogDoc=helpers.deepCopyObject logDoc
  speedMeter.check {sameCount,sameExceptLastUpdatedCount}
  debug.info prop._id,\
    'sameCount:',sameCount,\
    'sameExceptLastUpdatedCount:',sameExceptLastUpdatedCount,\
    'total logs:',logsInOrder.length


updateSysdata = () ->
  await SysdataCol.updateOne {_id:gSysdataId},{$set:{oldestMt:gOldestMt}},{upsert:true}


gracefulExit = (error)->
  debug.error 'gracefulExit error', error if error
  updateSysdata()
  EXIT 0
processKillSignal gSysdataId,gracefulExit


main = ()->
  unless yargs.argv.source in ALLOWED_SOURCE
    throw new Error "Invalid source: '#{yargs.argv.source}'. Allowed values are: #{ALLOWED_SOURCE.join(', ')}"
  # check sysdata
  gSysdataId = yargs.argv.source + '_' + RM_DUP_LOG_ID
  # build query
  query = {
    src: yargs.argv.source
  }
  if runAll # fresh run
    await SysdataCol.updateOne {_id:gSysdataId},{$set:{oldestMt:null}},{upsert:true}
  else # continue from previous point
    sysdata = await SysdataCol.findOne {_id:gSysdataId}
    if not sysdata
      await SysdataCol.insertOne {_id:gSysdataId,newestMt: new Date()}
    if sysdata?.oldestMt
      debug.info 'run from oldestMt:',sysdata.oldestMt
      query.mt = {$lte:sysdata.oldestMt}
    else
      debug.info 'no previous run record. start from beginning.'

  projection = {sid:1,src:1,trbtp:1,mt:1,SystemID:1,origSrc:1,phosrc:1}
  debug.info 'start running batch with query:',query
  cur = await PropertiesCol.find query,{projection,sort:{mt:-1}} #limit:1000
  stream = cur.stream()
  obj =
    verbose: 3
    # NOTE: too many will cause slow query in rni
    high: STREAM_HIGH #1000=1min 300000=300min
    low: STREAM_LOW
    stream: stream
    end: (err) ->
      return EXIT 1, err if err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      updateSysdata()
      debug.info "Total process time #{processTs} mins #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check {property:1}
      if gEmptyCount > 1000
        debug.info 'gEmptyCount > 1000, exit'
        updateSysdata()
        return EXIT 0
      if not prop.src
        prop.src = guessSrc(prop)
        debug.error "#{prop._id} has no src. Guessed: #{prop.src}"
        if not prop.src
          speedMeter.check {noSrc:1}
          return cb()

      try
        await rmDupLog(prop)
        gOldestMt = prop.mt
        speedMeter.check {successProcess:1}
      catch err
        debug.error err," rmDupLog propId:#{prop._id}"
        speedMeter.check {rmDupLogFailed:1}

      return cb()

  helpers.streaming obj

main()