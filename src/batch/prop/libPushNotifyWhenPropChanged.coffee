###
NOTE: this batch does not run directly
run
```
./start.sh lib/batchBase.coffee batch/prop/libPushNotifyWhenPropChanged.coffee preload-model
```
for test
###
config = CONFIG(['contact', 'mailEngine', 'mailEngineList','apn','serverBase','fcmv1_config'])
functionHelper = INCLUDE 'lib.helpers_function'
shouldSendMessage = functionHelper.shouldSendMessage
stringHelper = INCLUDE 'lib.helpers_string'
{renderSavedSearchTxt} = INCLUDE 'libapp.savedSearch'
{ getSources,
  SOURCE_TYPE,
  getPnMsg,
  shouldSendPnToUser,
  SOURCE_NO_EDM_MATCH,
  SOURCE_NO_PN_MATCH
} = INCLUDE 'libapp.pushNotifyMsg'
helpers = INCLUDE 'lib.helpers'
{getProvAbbrName} = INCLUDE 'lib.cityHelper'
{I18N_CTX} = INCLUDE 'libapp.common'
{
  getStatusLongName,
  STATUS_CATEGORIES,
  getStandardTypes,
  getPtypeAbbr,
  isSale,
  propPassSearchCondition,
} = INCLUDE 'libapp.properties'
sendMailLib = INCLUDE 'lib.sendMail'
i18n  = INCLUDE 'lib.i18n'
pushNotifyModel = MODEL 'PushNotify'

speed = INCLUDE 'lib.speed'
# priorityQueue = INCLUDE 'lib.priorityQueue3'

debug = DEBUG()

I18n = COLLECTION('chome','i18n')
ErrColl    = COLLECTION 'chome','pn_error'
UserProfile = COLLECTION 'chome', 'user_profile'
User = COLLECTION 'chome', 'user'
CrmCol = COLLECTION 'chome', 'crm'
UserModel = MODEL 'User'
ShowingModel = MODEL 'Showing'
UserWatchModel = MODEL 'UserWatch'
PropertiesModel = MODEL 'Properties'

sendMailObj = sendMailLib.getSendMail(config)
NOTIFY_VIEWED_PROPERTIES = AVGS.viewed

SEARCH_INTERVAL_MS = 5 * 60 * 1000 # 5min

gIsDryRun = false
gIsNoPn = false
gIsDev = false
gChangedProperties = {}
# gChangedProperties = {
#   TRBC5781246:{prop:{},lastHis:{},changeType:{},changeCategory:{}}
# }
###
data structure of gChangedProperties:
{propId:
  {
    prop:{_id,lp},
    lastHis:{s:'Pc',ts},
    changeType:'Price Increased',
    changeCategory: 'Price Changed'
  }
}
###
# currently processing savedSearches
gInSearchProcessing = false
gSavedSearchesCount = 0
gInSearchProcessingCount = 0
TEN_MINUTES = 10*60*1000
NINE_MINUTES = 9.5*60*1000
HALF_DAY = 12*60*60*1000
# ONE_MINUTES = 60*1*1000
# TEN_SECONDS = 10*1000

gTimeoutCount = 0
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 3000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# reset every HALF_DAY
functionHelper.repeat HALF_DAY,()->
  debug.info 'savedSearch 12hr timeout count = ',gTimeoutCount
  gTimeoutCount = 0
# invole every 10 minutes for saved search, will wait for the previous one to finish
functionHelper.repeat TEN_MINUTES,()->
  debug.info 'setInterval call savedSearch'#,Object.keys(gChangedProperties)
  if gInSearchProcessing
    gInSearchProcessingCount++
    debug.info 'Warning: savedSearch gInSearchProcessing=',gInSearchProcessing
    if (gSavedSearchesCount is 0) and (gInSearchProcessingCount > 3)
      err = 'Error: PN savedSearch no prop processed in long time '+gInSearchProcessingCount+'x10 minutes'
      errString = err.toString()
      errKey = errString.substr(0,50)
      # key,msg
      {shouldSend,msg} = shouldSendMessage(errKey,errString)
      if shouldSend
        sendMailObj.notifyAdmin(msg)
      debug.error err
    return
  # in async processing, we need to take a snapshot of the changed properties
  _changedProperties = gChangedProperties
  gChangedProperties = {}
  if (propIds = Object.keys(_changedProperties)).length > 0
    gInSearchProcessing = true
    debug.info 'savedSearch propIds',propIds
    _processSavedSearch propIds,_changedProperties,(err)->
      debug.error err if err
      gInSearchProcessingCount = 0
      gInSearchProcessing = false

_processSavedSearch = (propIds, changedProps, cb)->
  findUserSaveSearch {propIds,changedProps},(err)->
    cb(err)

CATEGORY_TO_EDM = {
  'New Sale': 'sale',
  'Sold': 'sold'
}
USER_PROFILE_FIELDS = {
  pnNoSearch:1,
  edmNoSearch:1,
  savedSearch:1,
  _mt:1
}
# TODO: need more fields than this
USER_FIELDS = {
  edmNo:1,
  local:1,
  eml:1,
  roles:1,
  pn:1,
  edmNoBounce:1
}
findUserSaveSearch = ({propIds,changedProps},cb)->
  processUserSaveSearchStartTs = Date.now()
  query = {'savedSearch.sbscb':true}
  # TODO(future): sort by user.lts
  gSavedSearchStartTs = Date.now()
  UserProfile.find query,{fields:USER_PROFILE_FIELDS,sort:{'savedSearch.ts':-1}},(err,cur)->
    return cb(err) if err
    stream = cur.stream()
    obj =
      verbose: 1
      high: 96
      stream: stream
      end: (err)->
        processUserSaveSearchTs = Date.now() - processUserSaveSearchStartTs
        debug.info \
          "Total time to process user savedSearches: #{processUserSaveSearchTs/(1000*60)} mins"
        debug.info "Process #{gSavedSearchesCount} savedsearches"
        gSavedSearchesCount = 0
        debug.info speedMeter.toString()
        speedMeter.reset()
        # timeout is not an error, log how many timeout
        if err and (err is MSG_STRINGS.TIMEOUT)
          gTimeoutCount++
          debug.warn 'findUserSaveSearch timeout',gTimeoutCount,(Date.now() - gSavedSearchStartTs)/1000
          return cb()
        cb(err)
      process: (userP,callback)->
        # return callback() if (userP.pnNoSearch and userP.edmNoSearch)
        return callback() if savedSearches?.length is 0
        savedSearches = userP.savedSearch?.reverse()
        # dump processing req after 9.5 mins, log how many dumped
        user = await User.findOne {_id:userP._id} #,{projection:USER_FIELDS}
        # if user?.edmNo
        #   return callback()
        # if user?.edmNoBounce > 7
        #   return callback()
        # 过滤sbscb=false记录
        savedSearches = savedSearches.filter (val)->
          return val.sbscb is true
        return callback() if savedSearches.length is 0
        # user.roles vip 30,other 3
        if not UserModel.accessAllowed('vipUser',user)
          savedSearches = savedSearches.slice(0,3)
        if (Date.now() - gSavedSearchStartTs) > NINE_MINUTES
          debug.info "savedSearch.ts:#{savedSearches[0]?.ts},_mt:#{userP._mt}"
          return callback(MSG_STRINGS.TIMEOUT)
        speedMeter.check { userProfileProcessed: 1 } #80000
        # TODO: find user and pass along #239
        processPropsWithSavedSearches {propIds,changedProps,savedSearches,userP,user},callback
    helpers.streaming obj

processPropsWithSavedSearches = ({propIds,changedProps,savedSearches,userP,user},cb)->
  savedSearches ?= []
  # for savedSearch in savedSearches
  #   return continue if savedSearch.sbscb isnt true
  #   gSavedSearchesCount++
  #   PropertiesModel.findListingsBySavedSearch {propIds,savedSearch},(err,savedSearchProps)->
  #     return cb(err) if err
  #     if not savedSearchProps?.length
  #       return cb()
  #     for prop in savedSearchProps
  #       try
  #         await logPropChangeStatus {obj:changedProps[prop._id],savedSearch,userP} unless gIsDryRun
  #       catch err
  #         break
  #         return cb err
  #     cb()
  doSearchProps = ()->
    # TODO: savedSearches.filter -> promise.all
    if savedSearch = savedSearches.pop()
      return doSearchProps() if savedSearch.sbscb isnt true
      # return doSearchProps() if savedSearch.ts < SIX_MONTH_AGO
      gSavedSearchesCount++
      speedMeter.check { searchProcessed: 1 } #20000
      # TODO(future): use Promise.allSettled? every 100 batch?
      passPropIds = []
      for k,v of changedProps
        if propPassSearchCondition(v.prop,savedSearch.k,false)
          passPropIds.push k
      if not passPropIds.length
        speedMeter.check { propNotPassed: 1 }
        return doSearchProps()
      PropertiesModel.findListingsBySavedSearch {propIds:passPropIds,savedSearch},(err,savedSearchProps)->
        if err
          debug.error err
          # return cb(err)
        if savedSearchProps?.length
          for prop in savedSearchProps
            try
              speedMeter.check { propProcessed: 1 } #8000
              await logPropChangeStatus {obj:changedProps[prop._id],savedSearch,userP,user} unless gIsDryRun
            catch err
              debug.error err
            # return cb err
        else
          speedMeter.check { propNotFoundBySavedSearchCond: 1 }
        doSearchProps()
    else
      return cb()
  doSearchProps()

getSortedSavedSearchFilters = (filters)->
  fields = ['price','bdrms','bthrms','gr','dom','city','map']
  kvFilters = {}
  for filter in filters
    kvFilters[filter.k] = filter
  sortedSavedSearch = []
  otherFieldsFilters = []
  for field in fields
    if kvFilters[field]
      sortedSavedSearch.push kvFilters[field]
    else
      otherFieldsFilters.push kvFilters
  sortedSavedSearch = sortedSavedSearch.concat otherFieldsFilters
  return sortedSavedSearch

getSavedSearchPnName = ({savedSearch,user})->
  locale = user.locale or 'en'
  if not (trans = fnTransactions[locale])
    trans = i18n.getFun locale
    fnTransactions[locale] = trans
  savedSearch.r = getSortedSavedSearchFilters savedSearch.r
  nm = savedSearch.nm or renderSavedSearchTxt({savedSearch,l10n:trans,src:'pn'})
  if locale is 'en' and nm.length > 30
    nm = nm.substr(0,30)+'...'
  if locale isnt 'en' and nm.length > 20
    nm = nm.substr(0,20)+'...'
  return nm

logPropChangeStatus = ({obj, savedSearch, userP, user})->
  try
    status = CATEGORY_TO_EDM[obj.changeCategory] or 'chgd'
    prop = obj.prop
    # user = await User.findOne {_id:userP._id}
    if gIsDev and (not shouldNotifyUser(user))
      #saved search test mode
      return
    nm = savedSearch.nm or helpers.dateFormat(new Date(savedSearch.ts), 'YYYY-MM-DD')
    v=
      nm:nm
    if savedSearch.clnt
      client = await CrmCol.findOne {_id:savedSearch.clnt},{fields:{nm:1,eml:1}}
      v.clnt = savedSearch.clnt
      v.cNm = client.nm
    src = [{k:SOURCE_TYPE.SAVED_SEARCH,v:[v]}]
    # BUG: notifyUser already logged change status
    # console.log 'logPropChangeStatus: ',prop._id,status,src
    # ??? src and status diff from getSources {sources,user,trans}???
    ret = await UserModel.logPropChangeStatus {uid:user._id,propId:prop._id,status,src}
    unless (userP.pnNoSearch or ret.updatedExisting)
      # notify users by locale
      # sources = [{source:SOURCE_TYPE.SAVED_SEARCH,param:getSavedSearchPnName({savedSearch,user})}]
      saveName = getSavedSearchPnName({savedSearch,user})
      v.nm = saveName
      sources = [{source:SOURCE_TYPE.SAVED_SEARCH,param:saveName,paramAll:[v]}]
      {statusLongName:changeType,category:changeCategory} = getLastHis {prop,updatedFields:prop}
      # console.log 'notify user: ',sources,changeType
      notifyUser {user,prop,sources,changeType}
  catch err
    debug.error err
    throw err

i18n.init {coll:I18n},(err)->
  debug.error err if err


sendPN = ({sender,pnToken,msg,url})->
  unless pnToken?.length > 8
    return
  payload =
    to: pnToken
    from: sender
    msg: msg
  if url
    payload.url = url
  debug.info "Send #{msg} to: #{pnToken}"
  # TODO: use model so that we can collect remove error_pn_token
  try
    ret = await pushNotifyModel.send payload
  catch err
    debug.error err

ROLES_TO_SEND_PN = ['_admin','_dev']

fnTransactions = {} # key by locale
# sendMailObj.notifyAdmin('test error')
notifyUsers = ({prop,usersObject,changeType})->
  # notify users by locale
  for id,container of usersObject
    user = container.user
    sources = container.sources
    # TODO: use promise.all?
    notifyUser {user,prop,sources,changeType}

notifyUser = ({user,prop,sources,changeType})->
  locale = user.locale or 'en'
  if not (trans = fnTransactions[locale])
    trans = i18n.getFun locale
    fnTransactions[locale] = trans
  if not trans
    debug.error 'No translation function for language',locale
    trans = (orig)-> orig
  # NOTE: baseMsg not used anywhere?
  # baseMsg = trans baseMsg
  {logSrc,strSource} = getSources {sources,user,trans}
  unless gIsDryRun
    try
      propChangeLogParam = {propId:prop._id,uid:user._id,changeType,src:logSrc,status:'chgd'}
      await UserModel.logPropChangeStatus(propChangeLogParam)
    catch err
      # if /MongoError|MongoServerSelectionError|interrupted/i.test err.toString()
      errString = err.toString()
      errKey = errString.substr(0,50)
      # key,msg
      {shouldSend,msg} = shouldSendMessage(errKey,errString)
      if shouldSend
        sendMailObj.notifyAdmin(msg)
      debug.error err
  if not user.pn
    return
  msg = getPnMsg {prop,changeType,trans}
  notice = {
    sender: strSource,
    msg,
    pnToken:user.pn,
    user,
    url:"/1.5/prop/detail/inapp?lang=#{locale}&d=/1.5/index&id=#{prop._id}&src=pn&mode=list"
  }
  debug.info 'notice',{
    msg:notice.msg,
    pnToken:notice.pnToken,
    user:notice.user?.eml,
    url:notice.url
  }
  shouldSendPn = shouldSendPnToUser(user,sources)
  unless ((not shouldSendPn) or gIsNoPn)
    sendPN notice, ErrColl

shouldNotifyUser = (user)->
  if user.roles
    for role in user.roles
      if ROLES_TO_SEND_PN.indexOf(role) >= 0
        return true
  return false

THREE_DAYS = 3600*1000*24*3

getLastHis = ({prop,updatedFields})->
  return {} if not updatedFields.spcts?
  # if spects is earlier than three days ago, ignore the prop change notify
  if (new Date(updatedFields.spcts).getTime()) < (Date.now() - THREE_DAYS)
    return {}
  #when insert, his = [], when update his.
  # if /his\.\d+/.test k
  #   lastHis = v
  if Array.isArray updatedFields.his
    his = updatedFields.his
  else
    his = []
    for k,v of updatedFields
      if /^his/.test k
        his.push v
  return {} if not his.length
  for lastHis in his
    # timestamp has to be the same or bigger than spcts
    # N9362707 spcts > Sld.ts, causes not notified
    # dayDiff is not in ms, null < 1
    dayDiff = helpers.dayDiff(lastHis.ts, updatedFields.spcts)
    if dayDiff? and dayDiff < 1
      # check is has status change
      {statusLongName,category} = getStatusLongName {prop,historyRecord:lastHis}
      if category isnt STATUS_CATEGORIES.UNKNOWN
        return {statusLongName,category,lastHis}
  return {}

exports.spctsChanged = spctsChanged = ({
  isDryRun,
  isNoPn,
  isDev,
  prop,
  updatedFields,
  priorityQueue,
  collPriorityQueue,
  })->
    gIsDryRun = isDryRun
    gIsNoPn = isNoPn
    gIsDev = isDev
    # TODO: try catch scope too big
    try
      changeType = ''
      # debug.debug prop,prop._id,prop.addr,updatedFields
      # TODO(future): refactor big function
      {statusLongName:changeType,category:changeCategory,lastHis} = getLastHis {prop,updatedFields}
      # console.log 'spctsChange: ',prop._id,prop.addr,updatedFields
      # console.log 'lastHis:',lastHis,changeType,changeCategory
      if not lastHis
        return false
      # if not changeCategory
      #   debug.error 'Nochange Category'
      #   return false

      gChangedProperties[prop._id] = {prop,lastHis,changeType,changeCategory}

      # search showing, faved, watched building/location/community
      usersObject = {}
      # showed properties. Showing.
      showingUserIDs = await ShowingModel.getUserIDsAsync prop._id
      if showingUserIDs?.length > 0
        showingUsers = await UserModel.getUsersByIDsAsync {ids:showingUserIDs,idKey:'uid'}
        debug.info "#{prop._id} found #{showingUsers.length} showing users"
      else
        showingUsers = []
      if showingUsers?.length > 0
        sourceParams = {}
        for u in showingUserIDs
          v = {nm:u.dt}
          if u.cNm
            v.cNm = u.cNm
          if sourceParams[u.uid]
            sourceParams[u.uid].push v
          else
            sourceParams[u.uid] = [v]
        mergeUserArray {
          isDev,
          mergedUserObject:usersObject,
          userArray:showingUsers,
          source:SOURCE_TYPE.SHOWING,
          sourceParams,
          }
      else
        debug.verbose "#{prop._id} no showing user"

      # favorites properties
      favUsers = await UserModel.getPropFavUsersWithFavNamesAsync prop._id
      if favUsers?.length > 0
        debug.info "#{prop._id} found #{favUsers.length} favorite users"
        sourceParams = {}
        for u in favUsers
          sourceParams[u._id] = u.favNames
        mergeUserArray {
          isDev,
          mergedUserObject:usersObject,
          userArray:favUsers,
          sourceParams,
          source:SOURCE_TYPE.FAV}
      else
        debug.verbose "#{prop._id} no favorite user"
      
      # watched building or locations or communities
      for source in [SOURCE_TYPE.LOCATION,SOURCE_TYPE.BUILDING,SOURCE_TYPE.COMMUNITY]
        searchParams = {prop,changeCategory,tp:source.toLowerCase()}
        if source is SOURCE_TYPE.COMMUNITY
          if cmtyID = stringHelper.getCommunityId prop
            searchParams.searchCmtyID = cmtyID
            searchParams.tp = 'cmty'
          else
            continue
        # else
        #   #search for building and location
        watchingUserIDs = await UserWatchModel.getWatchingUsersByPropertyAsync searchParams
        if watchingUserIDs?.length > 0
          watchingUsers = await UserModel.getUsersByIDsAsync {
            ids:    watchingUserIDs,
            idKey:  'uid',
            fields: UserModel.NOTIFY_FIELDS,
          }
        else
          watchingUsers = []
        if watchingUsers?.length > 0
          debug.info "#{prop._id} found #{watchingUsers.length} #{source} users"
          sourceParams = {}
          for watchItem in watchingUserIDs
            if sourceParams[watchItem.uid]
              sourceParams[watchItem.uid].push {nm:watchItem.nm} #nm: watching address or community name
            else
              sourceParams[watchItem.uid] = [{nm:watchItem.nm}]
          mergeUserArray {
            isDev,
            mergedUserObject:usersObject,
            userArray:watchingUsers,
            source,
            sourceParams}
        else
          debug.verbose "#{prop._id} no #{source} #{searchParams.searchCmtyID or ''} user"
      
      if NOTIFY_VIEWED_PROPERTIES
        # viewed properties
        viewedUsers = await UserModel.getPropViewedUsersAsync prop._id,null,(24*7)
        debug.verbose "#{prop._id} found #{viewedUsers.length} viewed users"
        if viewedUsers.length > 0
          mergeUserArray {
            isDev,
            mergedUserObject: usersObject,
            userArray: viewedUsers,
            source: SOURCE_TYPE.VIEWED,
            IgnoreDuplicated: true}
        else
          debug.verbose "#{prop._id} no viewed user"
      # inc prop priority in photo download queue
      if priorityQueue
        priorityQueue.increaseJobPriority prop._id,{collPriorityQueue}
      await notifyUsers {
        prop,
        usersObject,
        changeType,
      }
      debug.debug 'to sent',Object.keys(usersObject).length
      return true
    catch err
      debug.error err
      throw err

mergeUserArray = ({
  isDev,
  mergedUserObject,
  userArray,
  source,
  sourceParams, # based on user._id
  ignoreDuplicated,
})->
  for user in userArray
    # TODO: check if user_log.uid can be array to save space?
    if (isDev and (not shouldNotifyUser user))
      # dev test mode
      continue
    userHasNoFlag = user[SOURCE_NO_PN_MATCH[source]] and user[SOURCE_NO_EDM_MATCH[source]]
    if userHasNoFlag #if pnNo and edmNo, then do not save log
      continue
    # can pushnotify
    sourceItem = {source}
    if paramAll = sourceParams?[user._id]
      param = []
      for p in paramAll
        param.push p.nm
      sourceItem.param = param
      sourceItem.paramAll = paramAll
    if mergedUserObject[user._id]
      continue if ignoreDuplicated
      # merge user pnNo flag
      mergedUserObject[user._id].user = Object.assign(user,mergedUserObject[user._id].user)
      mergedUserObject[user._id].sources.push sourceItem
    else
      mergedUserObject[user._id] = {
        user:user,
        sources:[sourceItem],
      }

###
  new records has following structure:
  his: [
    { s: 'New', lp: 999900, ts: 2022-03-22T17:22:47.630Z },
    { s: 'chM', ts: 2022-03-22T17:22:47.630Z, d: 20220322 },
    { s: 'chVturl', ts: 2022-03-22T17:22:47.630Z, d: 20220322 }
  ]
###
