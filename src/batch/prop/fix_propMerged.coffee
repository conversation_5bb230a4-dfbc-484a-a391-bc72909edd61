###
  Description:   Merge duplicate properties, merge logic is same as saveToMaster.coffee
                 NOTE: import merge逻辑发生变更时，需要执行当前batch对房源做处理
                 NOTE(2024-03-29): 添加prop.src筛选，应对BRE与DDF onD差距在一天以上未merge的情况。

  Usage:         nohup ./start.sh lib/batchBase.coffee batch/prop/fix_propMerged.coffee dryrun skipGeoCoding -d [YYYY-MM-DD] -s [propSrc] > ./logs/fix_propMerged.log 2>&1 &
  CreateDate:    2024-02-19
  Author:        xiaoweiLuo
  Run frequency: When need

 nohup ./start.sh lib/batchBase.coffee batch/prop/fix_propMerged.coffee -d 2023-01-01 >> ./logs/fix_propMerged.log 2>&1 &
 new config: sh start.sh -t batch -n fix_propMerged -cmd "lib/batchBase.coffee batch/prop/fix_propMerged.coffee -d 2023-01-01"
###

debug = DEBUG()
PropertiesCol = COLLECTION 'vow', 'properties'
speed = INCLUDE 'lib.speed'
saveToMaster = INCLUDE 'libapp.saveToMaster'
helpers = require '../../lib/helpers'
yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query start date, format:YYYY-MM-DD', required: false, type: 'string'}
yargs.option 'src', { alias: 's', description: 'query prop src, eg:TRB,BRE', required: false, type: 'string'}

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
# 存在skipGeoCoding代表只需要查询跳过geoCoding的房源(keepPropGeocoding == true)
keepPropGeocoding = AVGS.indexOf('skipGeoCoding') >= 0
startTs = new Date()

main = ()->
  # RM房源不merge
  query = { merged: null, del: null , src:{$ne:'RM'}}
  if keepPropGeocoding
    query.keepPropGeocoding = true
  if yargs.argv.date
    query.ts = {'$gte':new Date(yargs.argv.date)}
  if yargs.argv.src and (yargs.argv.src.toUpperCase() isnt 'RM')
    query.src = yargs.argv.src.toUpperCase()
  cur = await PropertiesCol.find query
  cur.maxTimeMS 3600000*6
  stream = cur.stream()

  obj =
    verbose: 1
    stream: stream
    high: 1
    end: (err) ->
      return EXIT 1, err if err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check {processed:1}
      metaInfo = {}
      try
        { propToKeep, propToMerge } = await saveToMaster.findDuplicatePropAndDeterminePriority({
          prop,
          # srcType,
          metaInfo,
          Properties:PropertiesCol
        })
      catch err
        debug.error err,"propId:#{prop._id}"
        return cb err

      if not propToMerge
        speedMeter.check { noNeedMerge :1 }
        return cb()
      
      if propToKeep._id is propToMerge._id
        debug.error "keepId is the same as mergeId, propId:#{propToMerge._id}"
        return cb()

      # stream处理,避免已经merge的房源再次merge
      newPropToMerge = await PropertiesCol.findOne {_id:propToMerge._id},{merged:1}
      if newPropToMerge.merged is propToKeep._id
        debug.debug "propId:#{propToMerge._id} already merged"
        return cb()

      debug.info 'merge', propToMerge._id, 'with', propToKeep._id
      if dryRun
        speedMeter.check { needMerge :1 }
        return cb()

      try
        await saveToMaster.mergePropAndSaveToDB {
          propToKeep,
          propToMerge,
          metaInfo,
          Properties:PropertiesCol,
        }
      catch err
        debug.error err,"keepId:#{propToKeep._id},mergeId:#{propToMerge._id}"
        return cb err

      speedMeter.check { merged :1 }
      return cb()

  helpers.streaming obj

main()