###
Description:    修复TREB RESO房源中缺失字段映射的问题
params:
  -d:           查找mt大于输入日期的props  YYYY-MM-DD
Usage:          ./start.sh -n fix_reso_missFields -cmd "lib/batchBase.coffee batch/prop/fix_reso_missFields.coffee -d [date] dryrun"
Create date:    2025-01-29
Author:         xiaowei <PERSON>
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 2000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query date gte mt', required: false, type: 'string'}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

# 字段映射配置
FIELD_MAPPINGS = {
  ApproximateAge: 'age'
  Directions: 'dir_desc'
  StreetDirPrefix: 'st_dirpfx'
}

###*
# 处理ApproximateAge字段的特殊逻辑
# @description 当ApproximateAge为"New"时，根据onD计算年龄
# @param {Object} prop - 房源对象
# @returns {number|string} 处理后的年龄值
###
processApproximateAge = (prop) ->
  approximateAge = prop.ApproximateAge
  
  # 如果ApproximateAge为"New"，根据onD计算年龄
  if approximateAge is 'New'
    if prop.onD
      currentYear = new Date().getFullYear()
      onDYear = parseInt(prop.onD.toString().substr(0,4))
      age = currentYear - onDYear
      return age
    else
      debug.error "prop:#{prop._id} has no onD"
      return
  return

main = () ->
  projection = {
    ApproximateAge: 1
    Directions: 1
    StreetDirPrefix: 1
    onD: 1  # 用于ApproximateAge的计算
  }
  
  query = {src: 'TRB'}
  if yargs.argv.date
    query.mt = {$gte: new Date(yargs.argv.date)}

  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 * 3
  stream = cur.stream()
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      return EXIT 1, err if err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Completed, Total process time #{processTs} mins. ",speedMeter.toString()
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      set = {}
      unset = {}

      # 处理字段映射
      for sourceField, targetField of FIELD_MAPPINGS
        if prop[sourceField]?
          set[targetField] = prop[sourceField]
          # 标记删除原字段
          unset[sourceField] = 1
          if sourceField is 'ApproximateAge'
            # 特殊处理ApproximateAge字段
            processedValue = processApproximateAge(prop)
            set['bltYr'] = processedValue if processedValue
      
      # 如果没有需要更新的字段，跳过
      if Object.keys(set).length is 0
        speedMeter.check { skipped: 1 }
        return cb()
      
      update = {$set: set, $unset: unset, noModifyMt: true}
      
      if dryRun
        debug.info "dryRun: #{prop._id}, update: ", update
        speedMeter.check { needUpdate: 1 }
        return cb()
      
      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
      catch error
        debug.error error, " #{prop._id}, update: ", update
        return cb()

      return cb()

  helpers.streaming obj

main()