###
  description:监听properties,查找成交房源的上一次售价,记录上一次售价高的价格与差额百分比
  Options:
    init:           查找所有成交房源，依次查找上次成交记录，对比售价 更新字段
    routine:        从上次resumtoken开始watch
    force:          忽略running watch
    preload-model:  预先加载model(required)
  Usage:
    nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndCompareSoldPrice.coffee preload-model init force > ./logs/watchSoldProps.log 2>&1 &
    nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndCompareSoldPrice.coffee preload-model routine > ./logs/watchSoldProps.log 2>&1 &
  New Config:
    ./start.sh -t batch -n watchPropAndCompareSoldPrice_routine -cmd 'lib/batchBase.coffee batch/prop/watchPropAndCompareSoldPrice.coffee preload-model routine'
###

helpers = INCLUDE 'lib.helpers'
debug = DEBUG()
speed = INCLUDE 'lib.speed'
watchHelper = INCLUDE 'libapp.watchHelper'
{isCondo,minSalePrice,maxSalePrice,getFloat} = INCLUDE 'libapp.properties'
Properties = COLLECTION('vow','properties')
SysData = COLLECTION('vow','sysdata')
ProcessStatusCol =  COLLECTION('vow','processStatus')
PropertiesModel = MODEL 'Properties'

PROCESS_STATUS_ID = 'watchPropAndCompareSoldPrice'
SYSDATA_ID = 'propertiesCompareSoldPrice'
UPDATE_PROCESS_STATE_INTERNAL = 10 * 60 * 1000
isForce = AVGS.indexOf('force')>=0
gWatchedObject = null
gLastInitTime = Date.now()


PROJECTION = {id:1,sid:1,status:1,lst:1,addr:1,prov:1,city:1,unt:1,sp:1,lp:1,lat:1,\
  lng:1,onD:1,st_num:1,uaddr:1,ptype2:1,saletp:1,lsp:1,lspDifPct:1,llpDifPct:1,ptype:1}

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 10000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

{
  processKillSignal,
  updateProcessState,
  RUNNING_NORMAL,
  FINISH_WITHOUT_ERROR
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = INCLUDE 'libapp.processHelper'

gUpdateSysData = helpers.addAsyncSupport (watchHelper.getUpdateSysdataFunction({
  mongo:1,
  SysData:SysData,
  sysDataId:SYSDATA_ID
  }))

{
  handleProcessStatus,
  checkRunningProcess,
  handleExit,
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
  updateSysData:gUpdateSysData
}

before_exit = (errMsg)->
  debug.warn 'before_exit errMsg', errMsg
  if gWatchedObject
    gWatchedObject.end()
  handleExit {error:errMsg,watchedObject: gWatchedObject,exitFn:EXIT}

processKillSignal PROCESS_STATUS_ID,before_exit

process.on 'uncaughtException', (err)->
  before_exit err

onWatchError = (err)->
  if err
    debug.error 'watchSoldProp error', err
    return before_exit err

updateProcessStatusForWatch = (startTs)->
  debug.info 'update Process Status'
  try
    await handleProcessStatus {
      startTs: startTs
      status: RUNNING_NORMAL,
      nextTs: Date.now()+UPDATE_PROCESS_STATE_INTERNAL,
    }
  catch error
    debug.error 'updateProcessStatusForWatch',error

# 检查出售房源售价是否填错
isValidSalePrice = (prop)->
  if prop.status is 'U'
    price = prop.sp
  else
    price = prop.lp
  return (price >= minSalePrice) and (price <= maxSalePrice)

###
# @description 检查是否可以忽略查找房源历史
# @param  {object} prop
# @return {boolean} ignore  - Whether to ignore find history
# @return {object} update - need update prop
###
checkIgnoreFindHis = (prop)->
  # 忽略非Residential房源
  if prop.ptype isnt 'r'
    return {ignore:true}
  # condo房源且没有unt 不查找历史房源
  if (isCondo(prop)) and (not prop.unt)
    return {ignore:true}
  # 价格可能标错,不查找历史房源,移除lspDifPct,llpDifPct
  if not (isValidSalePrice prop)
    if prop.llpDifPct? or prop.lspDifPct?
      return {ignore:true,update:{$unset:{llpDifPct:1,lspDifPct:1}}}
    else
      return {ignore:true}

  # 非active房源 只计算成交有售价的房源
  if (prop.status is 'U') and ((prop.lst not in ['Sld','Pnd','Cld']) or (not prop.sp))
    return {ignore:true}
  
  # 没有上次成交价格，需要查找历史房源获取上次成交价格
  if not prop.lsp?
    return

  prop.lsp = getFloat prop.lsp
  # 存在lsp 计算diffPrice
  if prop.status is 'U'
    diffPrice = getFloat(prop.sp) - prop.lsp
  else
    diffPrice = getFloat(prop.lp) - prop.lsp

  difPct = parseFloat((diffPrice/prop.lsp).toFixed(4))

  update = {}
  # 根据diffPrice判断是否需要更新/移除 llpDifPct/lspDifPct
  if diffPrice >= 0
    # 没有卖亏时，需要移除 llpDifPct/lspDifPct
    if prop.llpDifPct? or prop.lspDifPct?
      update['$unset'] = {llpDifPct:1,lspDifPct:1}
  else # 卖亏
    if prop.status is 'U'
      # 成交房源需要移除 llpDifPct
      if prop.llpDifPct?
        update['$unset'] = {llpDifPct:1}
      # lspDifPct 不存在或者售价改变时需要更新 lspDifPct
      if (not prop.lspDifPct?) or (difPct isnt prop.lspDifPct)
        update['$set'] = {lspDifPct:difPct}
    else # status is 'A'
      # llpDifPct 不存在或者价格改变时需要更新 llpDifPct
      if (not prop.llpDifPct?) or (difPct isnt prop.llpDifPct)
        update['$set'] = {llpDifPct:difPct}
  
  if Object.keys(update).length > 0
    return {ignore:true,update}
  else
    return {ignore:true}
  

containAny = (arr,arrC)->
  ret = arr.find (item)->
    for str in arrC
      if str and (item?.indexOf(str) >= 0)
        return true
  return ret

# 查找当前房源的历史成交记录，对比售价，更新当前房源
# lsp       - last sold price
# lspDifPct - different percent betweent current sold price and last sold price
# llpDifPct - different percent betweent current listing price and last sold price
getPropHisAndUpdateDiffPrice = (prop,cb)->
  # 确认是否要查找房源成交历史
  ret = checkIgnoreFindHis prop
  if ret?.ignore
    # 不需要查找房源历史
    if ret.update
      try
        await Properties.updateOne {_id:prop._id},ret.update
        speedMeter.check {updated:1}
      catch err
        debug.error err,'src propId:',prop._id
        speedMeter.check {updateFailed:1}
    return cb()
  else
    # 需要查找房源历史
    params =
      status:'U',
      lst:'Sld',
      zip: prop.zip,
      addr:prop.addr
      prov:prop.prov,
      city:prop.city,
      unt: prop.unt,
      _id: prop._id,
      lat: prop.lat,
      lng: prop.lng,
      uaddr: prop.uaddr,
      st_num: (''+prop.st_num) or ''
    if Array.isArray(prop.ptype2) and prop.ptype2?.length
      if containAny prop.ptype2,['Semi-Detached','1/2 Duplex']
        params.type = 'Semi-Detached'
      else if containAny prop.ptype2,['Townhouse','Stacked']
        params.type = 'Townhouse'
      else if containAny prop.ptype2,['Apartment','Condo']
        params.type = 'Apartment'
      else
        params.type = 'Detached'
    else
      debug.error "prop:#{prop._id} no ptype2: ",prop.ptype2

    PropertiesModel.findAddrHistory params,(err,ret)->
      if err
        debug.error err,'src propId:',prop._id
        speedMeter.check {getHisError:1}
        return cb()
      # 有历史成交房源
      if ret?.l?.length
        lastSldProp = null
        # 确认上次成交房源
        for p in ret.l
          p.st_num = ''+(p.st_num or '')
          if (p.st_num is params.st_num) and (not lastSldProp) \
          and (p._id isnt prop._id) and (p.onD < prop.onD)
            lastSldProp = p
            break
        if not lastSldProp
          # 未找到上次成交记录
          debug.verbose "prop #{prop._id} no sold history"
          return cb()
        if not lastSldProp.sp
          # 缺少售价
          debug.error 'last sold listing had no sold price, current prop:',prop._id,' lastProp:',lastSldProp
          return cb()
        if not (isValidSalePrice lastSldProp)
          # sold price小于2w,认为是出租,不进行卖亏计算
          debug.verbose 'last sold listing price less than 2w, current prop:',prop._id,' lastProp:',lastSldProp
          return cb()
        # 更新上次成交价格
        update = {lsp:lastSldProp.sp,lspId:lastSldProp._id}
        if prop.status is 'U'
          # 成交房源比对sp  lst !== 'Sld'函数头已经过滤
          diffPrice = prop.sp - lastSldProp.sp
          update.lspDifPct = parseFloat((diffPrice/lastSldProp.sp).toFixed(4))
        else
          # active房源比对lp 与 sp
          diffPrice = prop.lp - lastSldProp.sp
          update.llpDifPct = parseFloat((diffPrice/lastSldProp.sp).toFixed(4))
        if diffPrice >= 0
          # 本次售价大于上次售价,不记录差值
          return cb()
        try
          await Properties.updateOne {_id:prop._id},{$set:update}
        catch err
          debug.error err,'src propId:',prop._id
          speedMeter.check {updateFailed:1}
          return cb()
        speedMeter.check {updated:1}
        return cb()
      else
        debug.verbose "prop #{prop._id} no history"
        return cb()

# 只处理成交或active房源(status==='U' && lst==='Sld' or status==='A')
onChange = (changedObj,watchedColl,cb)->
  isValidType = changedObj.operationType in ['insert','update','replace']
  if not isValidType
    debug.verbose "ignore: #{changedObj.operationType}",changedObj
    return cb()
   id = changedObj.documentKey?._id
  if not id
    debug.error 'no id',changedObj
    return cb()
  if changedObj.fullDocument
    prop = changedObj.fullDocument
  else
    try
      prop = await watchedColl.findOne {_id:id},{projection:PROJECTION}
    catch err
      debug.error err," propId:#{id}"
      return cb()
  if prop
    # 判断saletp is 'Sale',ptype === 'r'
    if (prop.saletp.indexOf('Sale') >= 0) and (prop.ptype is 'r')
      # 查找历史成交记录并更新
      getPropHisAndUpdateDiffPrice prop,cb
    else
      debug.verbose "ignore propId:#{prop._id},saletp:#{prop.saletp}"
      return cb()
  else
    debug.error "no fullDocument,can not find #{id} in watchedColl"
    return cb()

onTokenUpdate = ({token,tokenClusterTs,tokenChangeTs,end,resumeMt})->
  debug.info 'onTokenUpdate',token,\
    'gStat:',gStat,'from:',lastTokenPrintTs,'resumeMt:',resumeMt
  lastTokenPrintTs = new Date(tokenClusterTs)
  gStat = {}
  try
    await gUpdateSysData {token,tokenClusterTs,tokenChangeTs,end,resumeMt}
  catch error
    debug.critical error
    return before_exit error

watchProp = (token,tokenClusterTs,resumeMt) ->
  query = {saletp:'Sale'}
  if resumeMt
    startTs = new Date(resumeMt)
  else if tokenClusterTs
    startTs = new Date(tokenClusterTs)
  else
    startTs = new Date(new Date() - 12*60*60*1000)
  query['_mt'] = {$gte:startTs}
  opt =
    watchedColl: Properties,
    onChange: onChange,
    onTokenUpdate: onTokenUpdate,
    queryWhenInvalidToken:query,
    tokenDelayMs: 60000*5
    savedToken: token
    lowWaterMark: 20
    highWaterMark: 50
    onError:onWatchError
    updateProcessStatusFn: updateProcessStatusForWatch

  debug.debug 'start wartch',opt
  
  watchHelper.watchTarget opt, (err, retObject)->
    if err
      debug.critical 'watchProp error',err
      return before_exit err
    gWatchedObject = retObject if retObject

# init时查找所有Sale,Residential房源，依次更新卖亏房源 lsp,lspDifPct,llpDifPct字段
compareAllSoldPrice = (cb)->
  startTs = new Date()
  sort = {onD:-1}
  Properties.find {saletp:'Sale',ptype:'r'},{projection:PROJECTION,sort}, (err,cur)->
    return cb(err) if err
    stream = cur.stream()
    obj =
      verbose:1
      high:10
      stream:stream
      end:(err)->
        return cb(err) if err
        processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
        debug.info "Total process time #{processTs} mins",speedMeter.toString()
        speedMeter.reset()
        return cb()
      process: (p,callback)->
        speedMeter.check {processed:1}
        getPropHisAndUpdateDiffPrice p, callback
    helpers.streaming obj

# init时创建token，处理所有成交房源，开启watch
getTokenAndCompareSoldPrice = ()->
  watchHelper.getToken Properties,(err, {token,tokenClusterTs})->
    #console.log token
    return before_exit err if err
    compareAllSoldPrice (err)->
      return before_exit err if err
      try
        await onTokenUpdate({token,tokenClusterTs})
      catch err
        debug.error err
        return before_exit err
      watchProp(token,tokenClusterTs)


main = ()->
  try
    hasRunningProcess = await checkRunningProcess()
    if (not isForce) and hasRunningProcess
      return before_exit HAS_RUNNING_PROCESS
    startTs = new Date()
    await updateProcessStatusForWatch(startTs)
  catch error
    debug.error error
    EXIT(error)
  
  SysData.findOne {_id:SYSDATA_ID},(err,ret)->
    return before_exit err if err
    debug.verbose "sysData of #{SYSDATA_ID}", ret
    if ('routine' in AVGS)
      unless token= ret?.token
        throw new Error('Token is not found')
        EXIT 1
      try
        token  = JSON.parse(token)
        tokenClusterTs = ret?.tokenClusterTs or new Date()
      catch e # if no token, exit
        debug.error 'invalid token ',token
        EXIT 1
      watchProp(token,new Date(tokenClusterTs),ret?.resumeMt)
    else if ('init' in AVGS)
      if not ret?.token
        debug.info 'no Token'
      debug.info "init  at #{new Date()}"
      getTokenAndCompareSoldPrice()
    else
      EXIT 0, 'No Action found'

main()