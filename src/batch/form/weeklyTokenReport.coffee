###
Description:   统计token获取数量和看房管理数量到表格，发送给email, 具体细节可参考 docs/Change_requirements/20250509_batch_dumpToken.md

需求调整：1. 时间计算方式，从跑的时间计算出上周，从上周一0:0:0到周日23:59:59； 
2. 计算deal获取数量使用房源卖出的时间，而不是经纪自己claim的时间
2025.06.10 需求调整：showing查询看房的日期而不是创建的日期

Options:
-d --date: 统计数据的结束时间，如果未提供，则使用当前时间，格式 YYYY-MM-DD
-f --file: 输出路径+文件名，默认文件名是 date-tokenReport.xlsx
-e --email(require): email地址

usage:         sh start.sh -t batch -n weeklyTokenReport -cmd "lib/batchBase.coffee batch/form/weeklyTokenReport.coffee -d xxxx -e xxxx -f xxxx"

createDate:    2025-05-12
Author:        zhang man
###

os = require 'os'
fs = require 'fs'
path = require 'path'
xlsx = require 'xlsx'
yargs = require('yargs')
debug = DEBUG()

# 项目内依赖
{fullNameOrNickname} = require '../../libapp/user'
{saletpIsSale} = require '../../libapp/propertiesTranslate'
ObjectId = INCLUDE('lib.mongo4').ObjectId
helpers = INCLUDE 'lib.helpers'
speed = INCLUDE 'lib.speed'
sendMail = SERVICE 'sendMail'
{isEmailArray} = INCLUDE 'lib.helpers_string'
{dateFormat, date2num} = INCLUDE 'lib.helpers_date'

# 数据库集合
TokenCol = COLLECTION('chome', 'token')
UserCol = COLLECTION('chome', 'user')
ShowingCol = COLLECTION('chome', 'showing')
PropertiesCol = COLLECTION 'vow','properties'
RmGroupCol = COLLECTION('chome', 'rm_group')

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000
}
processStartTs = new Date()
# 参数解析
yargs
  .option 'date', { alias: 'd', description: 'date as yyyy-mm-dd, default is current time'}
  .option 'file', { alias: 'f', description: 'customize output file name, default is __dirname/date-tokenReport.xlsx'}
  .option 'email', { alias: 'e', array:true, description: 'email address'}
  .help('help')

argv = yargs.argv
startDate = null
endDate = null
startDateNum = null
endDateNum = null

# 计算时间范围
getDateRange = (dateStr) ->
  # 检查argv.date是否可以转换为时间格式
  if dateStr
    referenceDate = new Date(dateStr)
    if isNaN(referenceDate.getTime())
      return [null, null]
  else
    referenceDate = new Date()
  
  # 计算上一周的周一和周日
  dayOfWeek = referenceDate.getDay() # 0是周日，1是周一...6是周六
  
  # 计算到上一周周一的天数
  # 如果是周日(0)，上一周周一是7天前
  # 如果是周一(1)，上一周周一是7天前  
  # 如果是周二(2)，上一周周一是8天前
  # ...以此类推
  daysToLastMonday = if dayOfWeek is 0 then 7 else dayOfWeek + 6
  
  # 计算上一周周一 00:00:00
  start = new Date(referenceDate)
  start.setDate(referenceDate.getDate() - daysToLastMonday)
  start.setHours(0, 0, 0, 0)
  
  # 计算上一周周日 23:59:59
  end = new Date(start)
  end.setDate(start.getDate() + 6)
  end.setHours(23, 59, 59, 999)
  
  return [start, end]

# check path input
isDir = (path)->
  try
    stats = fs.lstatSync(path)
    return stats.isDirectory()
  catch err
    return false

# 输出文件名
gOutputPath = null
gOutputFileName = null
stats = {}
uidsSet = {}
propSet = {}
showingOrign = {}
# 用于存储claim信息，等待后续处理
claimData = []
allClaimedProperties = {}
KEY_MAP = {
  claimSaleSelf: 'SaleSelf',
  claimSaleFirsthand: 'SaleRMDirect',
  claimSaleSecondary: 'SaleRMReferral',
  claimRentSelf: 'LeaseSelf',
  claimRentFirsthand: 'LeaseRMDirect',
  claimRentSecondary: 'LeaseRMReferral',
  createNote: 'CreateNote',
  praiseNote: 'PraiseNote'
}
# 统计token表（认领积分、Note积分），将数据按照uid和key存储到stats中
collectTokenStats = (cb) ->
  try
    tokenCur = await TokenCol.find({ ts: { $gte: startDate, $lte: endDate } })
  catch err
    debug.error err
    return cb(err)
  stream = tokenCur.stream()
  obj =
    verbose: 1
    high: 1
    stream: stream
    end: (err) ->
      if err
        debug.error err
        return cb(err)
      debug.info speedMeter.toString()
      processTs = (new Date().getTime() - processStartTs.getTime())/(1000*60)
      debug.info "Token total process time #{processTs} mins #{speedMeter.toString()}"
      return cb()
    process: (token, callBack) ->
      speedMeter.check {processCount:1}
      uid = token.uid?.toString()
      k = token.key
      if (not uid) or (not KEY_MAP[k])
        return callBack()
      # 处理笔记相关的token，直接统计
      if k in ['createNote', 'praiseNote']
        stats[uid] ?= {}
        stats[uid][KEY_MAP[k]] ?= 0
        stats[uid][KEY_MAP[k]] += (token.t or 0) / 100
      # 处理claim相关的token，先保存信息，不直接统计
      else if k.startsWith('claim')
        claimData.push(token)
        allClaimedProperties[token.m] = 1
      uidsSet[uid] = 1
      speedMeter.check {updated:1}
      return callBack()
  helpers.streaming obj

# 处理claim token统计，根据房源实际卖出时间筛选
processClaimTokens = () ->
  # 获取所有claim的房源ID
  propIds = Object.keys(allClaimedProperties)
  unless propIds.length
    return
  # 查询properties表，筛选在时间范围内卖出的房源
  soldProperties = await PropertiesCol.findToArray(
    {
      _id: { $in: propIds },
      sldd: { $gte: startDateNum, $lte: endDateNum }
    },
    { projection: { _id: 1 } }
  )
  # 创建有效房源ID的集合
  validPropertyIds = {}
  for prop in soldProperties
    validPropertyIds[prop._id] = 1
  # 处理每个用户的claim数据
  for claimInfo in claimData
    uid = claimInfo.uid?.toString()
    unless validPropertyIds[claimInfo.m]
      continue
    k = claimInfo.key
    # 只有在有效房源ID内的claim才进行统计
    stats[uid] ?= {}
    stats[uid].claimedProperties ?= {}
    stats[uid].claimedProperties[claimInfo.m] = 1
    stats[uid][KEY_MAP[k]] ?= 0
    stats[uid][KEY_MAP[k]] += (claimInfo.t or 0) / 100
  return

# 统计showing表
collectShowingStats = (cb) ->
  speedMeter.reset()
  try
    # 先获取rm_group中:inReal的成员uid
    rmGroup = await RmGroupCol.findOne({ nm: ':inReal' })
  catch err
    debug.error err
    return cb(err)
  if not rmGroup?.members?.length
    debug.error 'No inReal group members found'
    return cb()
  # 提取所有成员的uid
  inRealUids = rmGroup.members.map (member) -> member.uid
  try
    # 使用inRealUids作为查询条件
    showingCur = await ShowingCol.find({ 
      dtNum: { $gte: startDateNum, $lte: endDateNum },
      'uid.0': { $in: inRealUids },
      exp:null
    }, { projection: { uid: 1, props: 1 } })
  catch err
    debug.error err
    return cb(err)
  stream = showingCur.stream()
  obj =
    verbose: 1
    high: 1
    stream: stream
    end: (err) ->
      if err
        debug.error err
        return cb(err)
      debug.info speedMeter.toString()
      processTs = (new Date().getTime() - processStartTs.getTime())/(1000*60)
      debug.info "Showing total process time #{processTs} mins #{speedMeter.toString()}"
      return cb()
    process: (showing, callBack) ->
      uid = showing.uid?[0]?.toString() # showing表的uid是数组，取第一个代表主经纪
      if not uid
        return callBack()
      stats[uid] ?= {}
      stats[uid].ShowingCount ?= 0
      stats[uid].ShowingCount += 1
      uidsSet[uid] = 1
      # 收集所有props的_id并更新propSet
      propsIds = showing.props.map (prop) -> 
        propSet[prop._id] = 1
        prop._id
      showingOrign[uid] ?= {}
      showingOrign[uid][showing._id] = propsIds
      speedMeter.check {updated:1}
      return callBack()
  helpers.streaming obj

# 查询所有props获取saletp，对showing进行分类
processShowingTypes = () ->
  allPropsIds = Object.keys(propSet)
  if (not Object.keys(showingOrign).length) and (not allPropsIds.length)
    return
  # 查询PropertiesCol表，获取每个房源的saletp
  properties = await PropertiesCol.findToArray({ _id: { $in: allPropsIds } }, { projection: { saletp: 1 } })
  # 创建saletp映射
  saletpMap = {}
  for prop in properties
    saletpMap[prop._id] = saletpIsSale(prop.saletp)
  # 循环处理判断每个showing的类型
  for uid, showings of showingOrign
    stats[uid] ?= {}
    stats[uid]['saleProp'] ?= 0
    stats[uid]['leaseProp'] ?= 0
    stats[uid]['mixedProp'] ?= 0
    for showingId, propsIds of showings
      saleCount = 0
      leaseCount = 0
      for propId in propsIds
        if saletpMap[propId]
          saleCount++
        else
          leaseCount++
      # 根据saleCount和leaseCount判断showing类型
      if saleCount > 0 and leaseCount > 0
        stats[uid]['mixedProp']++
      else if saleCount > 0
        stats[uid]['saleProp']++
      else if leaseCount > 0
        stats[uid]['leaseProp']++
  return

# 获取用户信息，邮箱和姓名, 计算总积分
processUserAndStatsTotalTokens = () ->
  uids = Object.keys(uidsSet).map (id) -> new ObjectId(id)
  if (not Object.keys(stats).length) and (not uids.length)
    return
  users = await UserCol.findToArray({ _id: { $in: uids } }, { projection: { eml: 1, nm_en: 1,nm_zh: 1,nm: 1 } })
  userMap = {}
  for u in users
    userMap[u._id.toString()] = u
  for uid, obj of stats
    user = userMap[uid]
    obj.eml = if Array.isArray(user?.eml) then user.eml[0] else user?.eml or ''
    obj.name = fullNameOrNickname('en', user)
    obj.dealTotalToken = ((obj.SaleSelf or 0) + (obj.SaleRMDirect or 0) + (obj.SaleRMReferral or 0) + (obj.LeaseSelf or 0) + (obj.LeaseRMDirect or 0) + (obj.LeaseRMReferral or 0)).toFixed(2)
    obj.noteTotalToken = ((obj.CreateNote or 0) + (obj.PraiseNote or 0)).toFixed(2)
    obj.totalToken = (Number(obj.dealTotalToken) + Number(obj.noteTotalToken)).toFixed(2)
    obj.dateRange = "#{dateFormat(startDate,'YYYY-MM-DD HH24:MI:SS')} - #{dateFormat(endDate,'YYYY-MM-DD HH24:MI:SS')}"
  return

# 输出XLSX文件
writeXLSX = () ->
  headers = [
    'Name'
    'Email'
    'Date Range'
    'Total Token'
    'Total Deals Token'
    'Sale RM Direct Token'
    'Sale RM Referral Token'
    'Sale Self Token'
    'Lease RM Direct Token'
    'Lease RM Referral Token'
    'Lease Self Token'
    'Total Deals Properties'
    'Total Notes Token'
    'Create Notes Tokens'
    'Praise Notes Tokens'
    'Total Showings'
    'Sale Property Showings'
    'Lease Property Showings'
    'Mixed Property Showings'
  ]
  # 将stats转换为数组并排序
  statsArray = Object.values(stats)
  if statsArray.length
    statsArray.sort (a, b) ->
      # 按totalToken降序排序
      return Number(b.totalToken) - Number(a.totalToken)
  
  # 准备数据
  rows = [headers]
  for data in statsArray
    row = [
      data.name or ''
      data.eml or ''
      data.dateRange or '0'
      data.totalToken or '0'
      data.dealTotalToken or '0'
      data.SaleRMDirect or '0'
      data.SaleRMReferral or '0'
      data.SaleSelf or '0'
      data.LeaseRMDirect or '0'
      data.LeaseRMReferral or '0'
      data.LeaseSelf or '0'
      Object.keys(data.claimedProperties or {}).length
      data.noteTotalToken or '0'
      data.CreateNote or '0'
      data.PraiseNote or '0'
      data.ShowingCount or 0
      data.saleProp or 0
      data.leaseProp or 0
      data.mixedProp or 0
    ]
    rows.push row

  # 创建工作簿和工作表
  wb = xlsx.utils.book_new()
  ws = xlsx.utils.aoa_to_sheet(rows)
  xlsx.utils.book_append_sheet(wb, ws, "Token Report")

  # 写入文件
  gOutputPath = path.join(gOutputPath, gOutputFileName)
  xlsx.writeFile(wb, gOutputPath)
  return

deleteFile = (filePath,cb)->
  fs.unlink filePath,(err)->
    if err
      debug.warn "#{filePath} deleted faild"
    return cb()

# 发送文件
sentFile = (cb) ->
  if not argv.email
    return cb()
  debug.info 'Preparing Email', 'outputFile', gOutputPath
  fs.readFile gOutputPath, (err, file) ->
    if err
      debug.error err
    base = new Buffer(file).toString('base64')
    engine = 'rmMail'
    emailConfig = {
      engine: engine
      from: sendMail.getFromEmailByEngine(engine)
      to: argv.email,
      subject: 'Token Report',
      html: 'Please see attachment. Showings: Counted based on when they were created. Deals: Counted based on when the sold date of the property',
      attachments: [
        {
          filename: gOutputFileName
          content: base
          encoding: 'base64'
        }
      ],
      listId: 'TokenReport'
      eventType: 'BatchTokenReport'
    }
    sendMail.sendMail emailConfig, (error, result) ->
      if error
        debug.error 'Sent email error', error, argv.email
      else
        debug.info 'Email sent', argv.email
      # keep the file if argv.file
      deleteFile gOutputPath, ->
        return cb()

# 主流程
main = ->
  unless argv.email
    debug.error 'NOT Email Parameters', argv.email
    return EXIT 0
  unless isEmailArray(argv.email)
    debug.error 'Invalided Email formate', argv.email
    return EXIT 0
  [startDate, endDate] = getDateRange(argv.date)
  # 检查时间范围是否有效
  if (not startDate) or (not endDate)
    debug.error 'Invalid date format',startDate,endDate
    return EXIT 0, 'Invalid date range'
  # 将开始和结束日期转换为数字格式
  startDateNum = date2num(startDate)
  endDateNum = date2num(endDate)
  # 检查文件路径
  if argv.file?.length > 0
    regex = /\/(?:.(?!\/))+$/
    if isDir(argv.file)
      gOutputPath = argv.file
      gOutputFileName = new Date(endDate).toFormat('YYYYMMDD') + '-tokenReport.xlsx'
    else if isDir(argv.file.replace(regex,''))
      gOutputPath = argv.file.replace(regex,'')
      gOutputFileName = "#{argv.file.match(regex)[0].replace('/','')}"
      if not /\.xlsx$/.test(gOutputFileName)
        gOutputFileName = "#{gOutputFileName}.xlsx"
    else
      debug.error 'Invalided file path', argv.file
      return EXIT 0
  else
    gOutputPath = os.tmpdir()
    gOutputFileName = new Date(endDate).toFormat('YYYYMMDD') + '-tokenReport.xlsx'
  
  collectTokenStats (err) ->
    return EXIT 0,err if err
    try
      await processClaimTokens()
    catch err
      debug.error err
      return EXIT 0,err
    collectShowingStats (err) ->
      return EXIT 0,err if err
      try
        await processShowingTypes()
        await processUserAndStatsTotalTokens()
      catch err
        debug.error err
        return EXIT 0,err
      try
        writeXLSX()
      catch err
        debug.error err
        return EXIT 0,err
      sentFile (err) ->
        return EXIT 1, err if err
        return EXIT 0
main() 