###
https://www.npmjs.com/package/yargs
https://www.npmjs.com/package/argparse

mongoWatch.coffee
-c --console: output to console, default.
 When -c, debug and message shall use console.error
-o --output: output folder
-f --file: output file name
-u --user: user name for database
-P --password: password
-d --database: database name. When db not specified, use the MongoClient watch.
 https://docs.mongodb.com/manual/changeStreams/ A deployment
-h --host: host name
-p --port: db port number
-e --exclude: collections to be excluded. separated by ,.
-i --include: collections to be included. separated by ,.
 When use -i, all others are excluded. -i takes precedence.
-s --start: startAtOperationTime timestamp. 20200504T120000(Z) format.
 Without Z, use location timezone.
http://mongodb.github.io/node-mongodb-native/3.5/api/Db.html#watch
-z --gzip: output gziped file (filename would be .gz after rotation)
https://nodejs.org/api/zlib.html#zlib_class_zlib_gzip or
 use shell command "gzip filename"
-C --command: run a shell command for yesterday's file.
 filename(folder if no -f) will at the end of the command.
https://stackabuse.com/executing-shell-commands-with-node-js/
-v --verbose: verbose mode.
-Z --compress-update: Do not write fullDocuments when update.
--ignore-error: ignore error on change stream. May get > 16M records sometime.

-r --relay: relay target mongo connection URI.
-R --relayconfigfile: relay target configuration file.
-u --update-upsert: taggle update upsert

All parameters can be read from environment settings
process.env.RMW_CONSOLE/OUTPUT/FILE/USER/PASSWORD/DATABASE
/HOST/PORT/EXCLUDE/INCLUDE/GZIP/START/VERBOSE

-c/-o/-f can be used at the same time
-o will generate separated files for each collection
filenames for -o: folder-20200508/dbname/collection.watch[.gz]
filenames for -f: filename-20200508db.watch[.gz]

File rotation will happen after midnight, when a new record starts
 with a new date.
1. create new file for the new day.
2. close yesterday's file. gzip it and rename to .gz if -z parameter supplied.
3. run a shall command on the file(folder if no -f) if supplied -C parameter.

watch file format: change object json for each line

expected output format:
{optp:'update/insert/delete...',ns:{db:'..',call:'...'},
  key:{_id:'...'},up:{},fd:{}}
up: for update
fd: for fullDocument
rm: for remove fields
to: for rename event
cts: clusterTime
lsid: session id
txn: txnNumber
have a look of  https://docs.mongodb.com/manual/reference/change-events/

sample change:
{
  _id: {
    _data: '825EB5D2EA000000082B022C0100296E5A10049D723B698B734
      801BFF8A538CEFE8B1C463C5F6964003C5452425734363938373433000004'
  },
  operationType: 'update',
  clusterTime: Timestamp { _bsontype: 'Timestamp', low_: 8, high_: 1588974314 },
  ns: { db: 'listing', coll: 'properties' },
  documentKey: { _id: 'TRBW4698743' },
  updateDescription: {
    updatedFields: { vc: 239, vcapp: 194, vcappc: 168, vcd: 239 },
    removedFields: []
  }
}

db.createRole({role:'watch',privileges:[{resource:{db:"",collection:""},actions:["find","changeStream"]}],roles:[]})
db.grantRolesToUser('pgusr',['watch']);


How this file works:
push the founction you need into outputFunctions array, it will be execute when watched database changed


sample object after changeKeyWords
insert
  {"optp":"insert",
  "ns":{"db":"test","coll":"listing"},
  "cts":"6935860381852106753",
  "fd":{"_id":"604120454aa503fe0c2dd505","a":1111},
  "key":{"_id":"604120454aa503fe0c2dd505"}}
update
  {"optp":"update",
  "ns":{"db":"test","coll":"listing"},
  "cts":"6935863091976470529",
  "fd":{"_id":"60411fdf4aa503fe0c2dd4e1","a":1,"lastModified":"2021-03-04T18:11:08.537Z","size":{"uom":"cm"},"status":"P"},
  "key":{"_id":"60411fdf4aa503fe0c2dd4e1"},
  "up":{"lastModified":"2021-03-04T18:11:08.537Z","size.uom":"cm","status":"P"}}
replace
  {"optp":"replace",
  "ns":{"db":"test","coll":"listing"},
  "cts":"6935860708269621249",
  "fd":{"_id":"604120454aa503fe0c2dd505","a":2222},
  "key":{"_id":"604120454aa503fe0c2dd505"}}
delete
  {"optp":"delete",
  "ns":{"db":"test","coll":"listing"},
  "cts":"6935861687522164738",
  "key":{"_id":"604120454aa503fe0c2dd505"}}
###

fs = require 'fs'
path = require 'path'
mongodb = require('mongodb')
MongoClient = mongodb.MongoClient
zlib = require('zlib')
helpers   = require '../../lib/helpers'
debugHelper = require '../../lib/debug'
bson = require 'bson'
yargs = require('yargs') # eslint-disable-line
yargs
  .option 'console', { alias: 'c', description: \
    'Debug and message shall use console.error'}
  .option 'folder', { alias: 'o', description: 'output folder'}
  .option 'file', { alias: 'f', type: 'string', \
    description: 'Run with verbose logging'}
  .option 'user', { alias: 'u', description: 'user name for database'}
  .option 'password', { alias: 'P', description: 'password'}
  .option 'database', { alias: 'd', description: 'database name'}
  .option 'replicaset', { alias: 'r', description: 'replicaset name'}
  .option 'host', { alias: 'h', description: 'host name'}
  .option 'port', { alias: 'p', description: 'db port number'}
  .option 'exclude', { alias: 'e', description: 'collections to be excluded'}
  .option 'include', { alias: 'i', description: 'collections to be included'}
  .option 'start', { alias: 's', description: 'startAtOperationTime timestamp'}
  .option 'gzip', { alias: 'z', description: 'output gziped file'}
  .option 'compress-update', { alias: 'Z', description: 'do not save full document for update'}
  .option 'ignore-error', {alias: 'I', description: 'ignore change error and continue watch'}
  .option 'command', { alias: 'C', description: \
    "run a shell command for yesterday's file"}
  .option 'verbose', { alias: 'v', description: 'verbose mode'}
  .option 'relay', { alias: 'l', array:true, description: 'relay target mongo connection URI'}
  .option 'relay-config', { alias: 'L', description: 'relay target config file(js)'}
  .option 'update-upsert', {alias:'U', type:'boolean', description: \
  'taggle upsert when update'}
  .help('help')

opt = yargs.argv
debug = debugHelper.getDebugger opt.verbose or 0

openedFiles = {}
outputFunctions = []
dateNum = helpers.date2num new Date()
dbConnections = []
relayConfig = opt['relay-config']
updateUpsert = if opt['update-upsert']? then true else false

# To caculate relay count
counterHolder = {}

exitWithError = (err)->
  debug.critical err
  process.exit 1

# 设置文件输出方式
closeFiles = () ->
  for fnm,f of openedFiles
    f.end()
  openedFiles = {}

openFile = (filePath)->
  if opt.gzip
    filePath = filePath+'.gz'
  unless f = openedFiles[filePath]
    debug.verbose 'Open file',filePath
    stream = fs.createWriteStream filePath
    f = openedFiles[filePath] = stream
    if opt.gzip
      gzip = zlib.createGzip()
      gzip.pipe stream
      f = openedFiles[filePath] = gzip
  f
if opt.folder
  curDatedFolder = null
  curFolders = {}
  ensureDatedFolder = (folder,cb)->
    return cb() if folder is curDatedFolder
    debug.verbose 'Create folder',folder
    helpers.mkdir folder,(err)->
      return exitWithError err if err
      curDatedFolder = folder
      curFolders = {}
      cb()
  ensureDBFolder = (folder,dbName,cb)->
    ensureDatedFolder folder,->
      folderPath = path.join folder,dbName
      return cb folderPath if curFolders[folderPath]
      debug.verbose 'Create DB folder',folderPath
      helpers.mkdir folderPath,(err)->
        return exitWithError err if err
        curFolders[folderPath] = true
        cb folderPath
  outputFunctions.push (jsonString,obj)->
    newDateNum = helpers.date2num new Date()
    if dateNum isnt newDateNum
      closeFiles()
      dateNum = newDateNum
    #folder-20200508/dbname/collection.watch
    ensureDBFolder (opt.folder + '_' + dateNum),obj.ns.db,(folder)->
      filePath = path.join folder,((obj.ns.coll or '_system')+'.watch')
      openFile(filePath).write jsonString
if opt.file
  outputFunctions.push (jsonString,obj)->
    newDateNum = helpers.date2num new Date()
    if dateNum isnt newDateNum
      closeFiles()
    filePath = opt.file + '-' + dateNum + 'db.watch' #filename-20200508db.watch
    openFile(filePath).write jsonString

#############################
### Relay section
#############################
###
Convert incoming parameter to an array of configObject
Params:
  connString: string[] || string
###
connectToRelay = (connectionStrings)->
  if not Array.isArray connectionStrings
    connectionStrings = [connectionStrings]
  for connectionString in connectionStrings
    setupRelay {connectionString}

###
log relay count every 1000 operations or new collection added
###
relayCounter = (dbName,collection,operationType)->
  key = "#{dbName}:#{collection}-#{operationType}"
  if counterHolder[key]
    n = counterHolder[key]++
    if 0 is (counterHolder[key] % 1000)
      debug.verbose key,counterHolder[key]
    return
  counterHolder[key] = 1
  debug.info key

###
Connect to database and register watch function
Params:
  configObject : {
    connectionString:string,
    formatter: function
  }
###
setupRelay = ({ connectionString, formatter = null } )->
  MongoClient.connect connectionString, {},(err,client)->
    debug.info 'Connecting relay db:',connectionString
    return exitWithError err if err
    relayDb = client.db()
    dbName = relayDb.databaseName
    debug.info "Relaied database #{dbName} connected successfully"
    dbConnections.push client
    outputFunctions.push (json,object,originalObject)->
      # check if object is valid
      if not object
        debug.warn 'bad object',object
        return
      if formatter
        object = formatter object
        return if not object? # to skip object purposely
      if not (object?.optp and object.ns?.db and object.ns.coll and object.key?._id)
        debug.warn '(formated) object missing fields',object
        return
      collection = relayDb.collection(object.ns.coll)
      if object.optp is 'insert'
        collection.insertOne object.fd,(err)->
          return debug.error "Relaied database #{dbName} insert failed",err,object if err
          relayCounter(dbName,object.ns.coll,object.optp)
      
      else if object.optp is 'replace'
        collection.replaceOne object.key, object.fd,{ upsert:opt['update-upsert'] }, (err)->
          return debug.error "Relaied database #{dbName} replace failed",err,object if err
          relayCounter(dbName,object.ns.coll,object.optp)
      
      else if object.optp is 'update'
        set = if updateUpsert then object.fd else object.up
        collection.updateOne object.key , {$set: set},
        { upsert:updateUpsert },(err)->
          return debug.error "Relaied database #{dbName} update failed",err,object if err
          relayCounter(dbName,object.ns.coll,object.optp)
      
      else if object.optp is 'delete'
        collection.deleteOne object.key,(err)->
          return debug.error "Relaied database #{dbName} delete failed",err,object if err
          relayCounter(dbName,object.ns.coll,object.optp)
      else
        debug.warn 'operationType not supported',object

if opt.relay
  connectToRelay(opt.relay)

###
Convert incoming parameter to an array of configObject
Params:
  connString: string[] || string
###
readRelayConfigures = (configPath)->
  # convert path into an array
  pathArray = if Array.isArray configPath then configPath else [configPath]
  for path in pathArray
    {connectionString,formatter} = require path
    setupRelay({connectionString,formatter})

if relayConfig
  readRelayConfigures opt['relay-config']


if opt.console or (not opt['relay-config'] and not opt['relay'] and outputFunctions.length is 0)
  debug.dontUserConsoleError true
  consoleOutput = process.stdout
  if opt.gzip
    consoleGzip = zlib.createGzip()
    consoleGzip.pipe consoleOutput
    consoleOutput = consoleGzip
  outputFunctions.push (jsonString,obj)->
    consoleOutput.write jsonString



###
Do not touch the following code unless you know what you are doing
###
#region

# close relay db connection
closeConnections = ()->
  doOne = ->
    if client = dbConnections.pop()
      client.close doOne
      doOne()
    else
    return
  doOne()

closeNExit = (code=0)->
  closeFiles()
  closeConnections()
  setTimeout (-> process.exit code),5000

beforeExit = (sig)->
  process.on sig, ->
    debug.info 'Get signal',sig
    closeNExit 0
beforeExit 'SIGINT'
beforeExit 'SIGQUIT'
beforeExit 'SIGTERM'
beforeExit 'exit'

debug.debug opt

IGNORE_ERROR = opt['ignore-error']

# conn = '*************************************'
# 设置连接url
host = opt.host or 'localhost'
port = opt.port or '27017'
conn = host + ':' + port
connParams = []
if opt.database
  conn = conn+'/'+opt.database
if opt.user and opt.password
  conn = 'mongodb://'+encodeURIComponent(opt.user)+':'+\
    encodeURIComponent(opt.password)+'@'+conn
  connParams.push 'authSource=admin'
else
  conn = 'mongodb://'+conn
if opt.replicaset
  connParams.push 'replicaSet='+opt.replicaset
if connParams.length > 0
  conn += '?'+connParams.join('&')

COMPRESS_UPDATE = opt['compress-update'] or process.env.COMPRESS_UPDATE
debug.debug 'compress-update',COMPRESS_UPDATE

watchOpt = {fullDocument:'updateLookup',batchSize:1,readPreference:mongodb.ReadPreference.NEAREST}

includeCollArray = null
includeColls = null
excludeCollArray = null
excludeColls = null
# 设置监听的表
if opt.include
  includeColls = {}
  includeCollArray = opt.include.split ','
  for coll in includeCollArray
    includeColls[coll] = 1
if opt.exclude
  if includeColls
    debug.error 'Can not specify exclude together with include'
    process.exit 1
  excludeColls = {}
  excludeCollArray = opt.exclude.split ','
  for coll in excludeCollArray
    excludeColls[coll] = 1

# 设置开始时间
if opt.start
  unless /^\d{8}T\d{6}Z?$/i.test opt.start #20200504T120000(Z)
    return exitWithError "Unknown timestamp formate.\
        Need like 20200504T120000Z. Got #{opt.start}"
  startTime =  opt.start
  if startTime.indexOf '-' < 0
    year = startTime.slice(0,4)
    month = startTime.slice(4,6)
    day = startTime.slice(6,8)
    contact = startTime.slice(8,9)
    hour = startTime.slice(9,11)
    minute = startTime.slice(11,13)
    second = startTime.slice(13)
    startTime = year+'-'+ month+'-'+ day+ contact+ hour+':'+ minute+':'+ second
  dt = Date.parse new Date(startTime)
  startTs = new bson.Timestamp(1,dt/1000 - 10*3600)
  debug.verbose 'start time',startTs
  watchOpt.startAtOperationTime = startTs


# simplify incoming event and return jsonString
changeKeyWords = (obj) ->
  return unless Object.keys(obj).length
  abbr =
    optp : obj.operationType
    ns : obj.ns
    cts : obj.clusterTime
  if obj.fullDocument and ((obj.operationType isnt 'update') or not COMPRESS_UPDATE)
    abbr.fd = obj.fullDocument
  abbr.key = obj.documentKey if obj.documentKey?
  abbr.to = obj.to if obj.to?
  if obj.updateDescription?
    abbr.up = obj.updateDescription.updatedFields if obj.updateDescription.updatedFields
    abbr.rm = obj.updateDescription.removedFields if obj.updateDescription.removedFields?.length > 0
  abbr.lsid = obj.lsid if obj.lsid?
  abbr.txn = obj.txnNumber if obj.txnNumber?
  jsonString = JSON.stringify(abbr)+'\n'
  return {json:jsonString,object:abbr}



# DB connection
debug.info 'Connect',conn
MongoClient.connect conn, {}, (err,db)->
  return exitWithError err if err
  debug.verbose 'MongoClient connect succeed'
  dbWatch = db
  if opt.database
    dbWatch = db.db(opt.database)
    debug.info 'watch','database',opt.database
  else
    debug.info 'watch','mongo instance'
  filter = []
  if includeCollArray
    filter.push {$match:{'ns.coll':{$in:includeCollArray}}}
  else if excludeCollArray
    filter.push {$match:{'ns.coll':{$nin:excludeCollArray}}}
  debug.info 'watchOption',filter,watchOpt
  changeStream = dbWatch.watch filter,watchOpt
  changeStreamMethods changeStream

# loop through function in outputFunctions and execute it
changeFns = (json,obj)->
  for fn in outputFunctions
    fn json,obj


changeStreamMethods = (changeStream)->
  changeStream.on 'change',(originalObject)->
    # transform
    {json,object} = changeKeyWords originalObject
    if includeColls
      if includeColls[originalObject.ns.coll]
        changeFns json,object,originalObject
    else if excludeColls
      if not excludeColls[originalObject.ns.coll]
        changeFns json,object,originalObject
    else
      changeFns json,object,originalObject

  changeStream.on 'close',()->
    debug.verbose 'stream closed'
    process.exit 0
  changeStream.on 'error',(err)->
    if IGNORE_ERROR
      debug.warn 'ignore error',err
      return
    debug.error 'stream error',err
    closeNExit 1
  changeStream.on 'resumeTokenChanged',(rtoken)->
    debug.debug 'tokenChange',rtoken



process.on 'uncaughtException',(e)->
  if IGNORE_ERROR
    debug.warn 'ignore unexpected error',e
    return
  debug.critical e
  closeNExit 1
#endregion
