###
  Description: Test read, write, and delete operations on four databases (vow, chome, rni, tmp)
  Options:
      dryrun:     dry run mode, no actual database operations
  New config:
    ./start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee dryrun"
  createDate:    2025-06-24
  Author:        Maggie
  Run frequency: cronjob

Tests basic MongoDB operations (insert, find, delete) on four databases:
- vow.test
- chome.test  
- rni.test
- tmp.test

###

debug = DEBUG()
os = require('os')
helpers = INCLUDE 'lib.helpers'
config = CONFIG(['contact', 'mailEngine', 'mailEngineList'])
sendMailLib = INCLUDE 'lib.sendMail'
sendMailObj = sendMailLib.getSendMail(config)

# Database collections
VowTestCol = COLLECTION 'vow', 'test'
ChomeTestCol = COLLECTION 'chome', 'test'
RniTestCol = COLLECTION 'rni', 'test'
TmpTestCol = COLLECTION 'tmp', 'test'

dryRun = AVGS.indexOf('dryrun') >= 0

# Test data
TEST_DATA = [
  {name: 'test1', value: 100, timestamp: new Date()},
  {name: 'test2', value: 200, timestamp: new Date()},
  {name: 'test3', value: 300, timestamp: new Date()}
]

###
  Description: Send notification via email and SMS when tests fail
  Params: errorMessage
  Return: Promise<void>
###
sendFailureNotification = (errorMessage) ->
  try
    debug.error errorMessage
    
    # Send email notification
    sendMailObj.notifyAdmin(errorMessage)
    debug.info "Failure notification email sent"
    
  catch emailErr
    debug.error 'Failed to send failure notification:', emailErr

###
  Description: Test write operations (insert) on a collection
  Params: collection, collectionName
  Return: Promise<void>
###
testWrite = (collection, collectionName) ->
  debug.info "Testing WRITE operations on #{collectionName}"
  
  if dryRun
    debug.info "[DRY RUN] Would insert #{TEST_DATA.length} documents into #{collectionName}"
    return
  
  try
    # Insert single document
    result = await collection.insertOne TEST_DATA[0]
    debug.info "Inserted single document into #{collectionName}, _id: #{result.insertedId}"
    
    # Insert multiple documents
    result = await collection.insertMany TEST_DATA.slice(1)
    debug.info "Inserted #{result.insertedIds.length} documents into #{collectionName}"
    
  catch err
    debug.error "Write test failed for #{collectionName}: #{err.message}"
    throw err

###
  Description: Test read operations (find) on a collection
  Params: collection, collectionName
  Return: Promise<void>
###
testRead = (collection, collectionName) ->
  debug.info "Testing READ operations on #{collectionName}"
  
  try
    # Find all documents
    allDocs = await collection.findToArray()
    debug.info "Found #{allDocs.length} documents in #{collectionName}"
    
    # Find with filter
    filteredDocs = await collection.findToArray({name: 'test1'})
    debug.info "Found #{filteredDocs.length} documents with name 'test1' in #{collectionName}"
    
    # Count documents
    count = await collection.countDocuments({})
    debug.info "Total document count in #{collectionName}: #{count}"
    
    # Find with projection
    projectedDocs = await collection.findToArray({}, {projection: {name: 1, value: 1, _id: 0}})
    debug.info "Found #{projectedDocs.length} documents with projection in #{collectionName}"
    
  catch err
    debug.error "Read test failed for #{collectionName}: #{err.message}"
    throw err

###
  Description: Test delete operations on a collection
  Params: collection, collectionName
  Return: Promise<void>
###
testDelete = (collection, collectionName) ->
  debug.info "Testing DELETE operations on #{collectionName}"
  
  if dryRun
    debug.info "[DRY RUN] Would delete documents from #{collectionName}"
    return
  
  try
    # Delete documents with specific name
    result = await collection.deleteMany({name: 'test1'})
    debug.info "Deleted #{result.deletedCount} documents with name 'test1' from #{collectionName}"
    
    # Delete remaining test documents
    result = await collection.deleteMany({name: {$in: ['test2', 'test3']}})
    debug.info "Deleted #{result.deletedCount} remaining test documents from #{collectionName}"
    
    # Verify all test documents are deleted
    remainingCount = await collection.countDocuments({name: {$in: ['test1', 'test2', 'test3']}})
    if remainingCount > 0
      debug.warn "Warning: #{remainingCount} test documents still exist in #{collectionName}"
    else
      debug.info "All test documents successfully deleted from #{collectionName}"
      
  catch err
    debug.error "Delete test failed for #{collectionName}: #{err.message}"
    throw err

###
  Description: Test database connection and basic operations
  Params: collection, collectionName
  Return: Promise<void>
###
testDatabase = (collection, collectionName) ->
  debug.info "=== Testing database: #{collectionName} ==="
  
  try
    # Test write operations
    await testWrite(collection, collectionName)
    
    # Test read operations
    await testRead(collection, collectionName)
    
    # Test delete operations
    await testDelete(collection, collectionName)
    
    debug.info "=== Database #{collectionName} test completed successfully ==="
    
  catch err
    debug.error "=== Database #{collectionName} test failed: #{err.message} ==="
    throw err

###
  Description: Main function to run all database tests
  Return: Promise<void>
###
main = () ->
  debug.info "Starting MongoDB test batch"
  debug.info "Dry run mode: #{dryRun}"
  
  # Define test collections
  testCollections = [
    {collection: VowTestCol, name: 'vow.test'},
    {collection: ChomeTestCol, name: 'chome.test'},
    {collection: RniTestCol, name: 'rni.test'},
    {collection: TmpTestCol, name: 'tmp.test'}
  ]
  
  successCount = 0
  errorCount = 0
  failedDatabases = []
  
  for testCol in testCollections
    try
      await testDatabase(testCol.collection, testCol.name)
      successCount++
    catch err
      errorCount++
      failedDatabases.push(testCol.name)
      debug.error "Failed to test #{testCol.name}: #{err.message}"
  
  debug.info "=== Test Summary ==="
  debug.info "Successful tests: #{successCount}"
  debug.info "Failed tests: #{errorCount}"
  debug.info "Total databases tested: #{testCollections.length}"
  
  if errorCount > 0
    errorMessage = "batch/test/mongoTest.coffee MongoDB test batch failed on #{os.hostname()}. Failed databases: #{failedDatabases.join(', ')}. Total failures: #{errorCount}/#{testCollections.length}"
    await sendFailureNotification(errorMessage)
    debug.error "Some database tests failed. Please check the logs above."
    EXIT 1, errorMessage
  else
    debug.info "All database tests completed successfully!"
    EXIT 0

main() 