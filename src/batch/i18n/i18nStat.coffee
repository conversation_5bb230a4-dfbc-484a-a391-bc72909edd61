###
Description:    Statistic i18n table records and weekly new records, send email notification if abnormal.

Usage:          ./start.sh -t batch -n i18nStat -cmd "lib/batchBase.coffee batch/i18n/i18nStat.coffee " -cron 'Sat, 03:00:00'
Create date:    2025-03-25
Author:         Maggie
Run frequency:  Weekly
###

debug = DEBUG()
I18nCol = COLLECTION 'chome','i18n'
config = CONFIG(['contact', 'mailEngine', 'mailEngineList'])
sendMailLib = INCLUDE 'lib.sendMail'
sendMailObj = sendMailLib.getSendMail(config)

# Constants for thresholds
THRESHOLD = {    
  MAX_WEEKLY: 100 # Maximum expected weekly new records
  MAX_TOTAL: 10000 # Maximum expected total records
}

# Get current date and date from 1 week ago
getDateRange = ->
  now = new Date()
  weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  return {now, weekAgo}

# Check if data is abnormal
isAbnormal = (totalCount,weeklyNew) ->
  if (weeklyNew > THRESHOLD.MAX_WEEKLY) or (totalCount > THRESHOLD.MAX_TOTAL)
    return true
  return false

# Main function to collect and update i18n statistics
main = ->
  {now, weekAgo} = getDateRange()
  
  # Get total count
  totalCount = await I18nCol.countDocuments({})
  
  # Get count of records from last week
  weeklyCount = await I18nCol.countDocuments({
    ts: {$gte: weekAgo}
  })
  
  # Prepare statistics data
  stats = {
    total: totalCount
    weekly_new: weeklyCount
  }
    
    # Check for abnormal data and notify if necessary
  if isAbnormal(totalCount,weeklyCount)
    message = "i18nStat abnormal data detected:\nTotal: #{totalCount}\nWeekly new: #{weeklyCount} (exceeded max threshold: total:#{THRESHOLD.MAX_TOTAL}, weekly:#{THRESHOLD.MAX_WEEKLY})"
    try
      sendMailObj.notifyAdmin(message)
      debug.log "i18nStat abnormal data detected:", message
    catch error
      debug.error "Error in i18nStat:", error
      throw error
  else
    debug.log "i18nStat data is normal:", stats
  EXIT 0

main()