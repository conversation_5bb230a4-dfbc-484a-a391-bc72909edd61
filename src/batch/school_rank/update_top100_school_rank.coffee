#!/usr/bin/env coffee

###*
 * 根据Excel文件内容更新学校排名数据
 * 
 * @description 
 * 读取Excel文件中的学校数据,包括:
 * - 专业分布数据
 * - 大学录取数据
 * - 成绩分布数据
 * 将数据保存到数据库中对应学校的记录
 * 
 * <AUTHOR>
 * @date 2025-04-03
 * 
 * @usage sh start.sh -t batch -n update_top100_school_rank -cmd "lib/batchBase.coffee batch/school_rank/update_top100_school_rank.coffee -f 文件名"
 * @param {string} fileName - 数据文件路径
 * @param {boolean} [dryrun] - 是否试运行模式

 导入时的注意事项：
 由于两张表(School-University-data-summary-major和School-University-data-summary-university)的更新学校以及顺序都一致，所以将数据合并到一张表上，更新数据
 1. 检查是否所有学校都存在，没有更新的学校找一下数据库中保存的学校名称，核对学校信息，更新表格。
 2. 检查表头名称，由于更新的字段只会获取对应的表数据，发过来的表头名称需要和batch中取的表头名称一致。
  基础字段：
    school name - 学校名称
    total - 总学生数
    sample amount - 样本量
    sample% - 样本百分比
    graduate year - 毕业年份
    Total (存为total2)
    Ivy old
    Ivy new
    Ivy total
  专业分布字段及其百分比：
    Liberal Arts 及 Liberal Arts%
    Fine Arts 及 Fine Arts%
    Performing arts 及 Performing arts%
    Arts and Design 及 Arts and Design%
    Total Arts 及 Total Arts%
    Computer Science 及 Computer Science%
    Applied Science and Engineering 及 Applied Science and Engineering%
    Pure Science 及 Pure Science%
    Health Science 及 Health Science%
    Total Sience & Engineering 及 Total Sience & Engineering%
    Business School 及 Business School%
    Other Business 及 Other Business%
    Total Business 及 Total Business%
    Other 及 Other%
  大学录取数据及其百分比：
    University of Toronto 及 University of Toronto%
    University of British Columbia 及 University of British Columbia%
    McGill University 及 McGill University%
    McMaster University 及 McMaster University%
    University of Alberta 及 University of Alberta%
    Universite de Montreal 及 Universite de Montreal%
    University of Waterloo 及 University of Waterloo%
    University of Calgary 及 University of Calgary%
    University of Ottawa 及 University of Ottawa%
    Western University 及 Western University%
    Dalhousie University 及 Dalhousie University%
    Queens University 及 Queens University%
    York University 及 York University%
    University of Guelph 及 University of Guelph%
    Carleton University 及 Carleton University%
    Concordia University 及 Concordia University%
  成绩分布数据：
    90+ 及 90+%
    85+ 及 85+%
    80+ 及 80+%
###

# 命令行参数解析
yargs = require('yargs')(AVGS)
yargs.option('year', { alias: 'y', description: '数据年份'})
yargs.option('dryrun', { alias: 'n', description: '试运行模式'})
yargs.option('fileName', { alias: 'f', description: 'File Name'})

# 获取参数值
dryrun = yargs.argv.dryrun
fileName = yargs.argv.fileName

# 初始化
fs = require 'fs'
path = require 'path'
xlsx = require 'xlsx'
debug = DEBUG()
startTs = new Date()
totalUpdateCount = 0
updated = 0
updatedTime = new Date().getTime()
unmatchedSchools = [] # 存储未匹配的学校
speed = INCLUDE 'lib.speed'

# 参数校验
if not fileName
  return debug.error '需要指定文件路径'
# 创建speed统计实例
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 初始化数据库集合
SchoolMergedCol = COLLECTION 'vow', 'sch_rm_merged'
SchoolRankCol = COLLECTION 'vow', 'sch_rm_rank'
PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合

{mergeArrayByYearmark,generateSchoolName,delSchoolCommWord} = INCLUDE 'libapp.schoolHelper'

schoolNameMap = {
  'St Michael\'s Choir S': 'St. Michael\'s Choir School',
  'St Robert Catholic HS': 'St. Robert Catholic High School',
  'White Oaks SS': 'White Oaks Secondary School',
  'Garth Webb Secondary School': 'Garth Webb',
  'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
  'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
  'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman',
  'Danforth Collegiate and Technical Institute': 'Danforth Collegiate And Technical Institute',
  'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary School'
}

PrivateSchoolNameMap = {
  'Tanenbaum Chat Wallenberg Campus':'Tanenbaum Community Hebrew Academy of Toronto',
  'Crescent School':'Crescent School',
  'Hamilton District Christian High School':'Hamilton District Christian High'
}

###*
 * 将百分比转换为整数(放大10000倍)
 * @param {string|number} value - 原始百分比值
 * @returns {number} - 转换后的整数
###
convertPct = (value) ->
  return value unless value
  Math.round(parseFloat(value) * 10000)

###*
 * 判断字段值是否大于0
 * @param {string|number} value - 原始值
 * @returns {boolean} - 是否大于0
###
isPositive = (value) ->
  parseFloat(value) > 0

###*
 * 读取Excel文件
 * @param {string} filePath - 文件路径
 * @returns {Object} - 包含headers和data的对象
###
readExcel = (filePath) ->
  try
    # 读取工作簿
    workbook = xlsx.readFile(filePath)
    # 获取第一个工作表
    worksheet = workbook.Sheets[workbook.SheetNames[0]]
    debug.info "读取工作表: #{workbook.SheetNames[0]}"
    
    # 转换为JSON
    data = xlsx.utils.sheet_to_json(worksheet)
    
    # 获取所有列标题
    headers = Object.keys(data[0] or {})
    
    debug.info "找到 #{data.length} 条记录, #{headers.length} 个列"
    return { headers, data }
  catch error
    debug.error "读取Excel文件 #{filePath} 错误:", error
    throw error

###*
 * 构建专业分布数据数组
 * @param {Object} school - 学校数据对象
 * @returns {Array} - 专业分布数据数组
###
buildMajorData = (school) ->
  majors = []
  for major in ['Liberal Arts','Fine Arts','Performing arts','Arts and Design','Total Arts','Computer Science',\
    'Applied Science and Engineering','Pure Science','Health Science','Total Sience & Engineering',\
    'Business School','Other Business','Total Business','Other']
    pct = major + '%'
    majors.push({ major: major, cnt: parseInt(school[major] or 0), pct: convertPct(school[pct]) or 0 })

  return majors

###*
 * 构建大学录取数据数组
 * @param {Object} school - 学校数据对象
 * @returns {Array} - 大学录取数据数组
###
buildTopSchoolsData = (school) ->
  topSchools = []
  for sch in ['University of Toronto', 'University of British Columbia', 'McGill University', 'McMaster University',\
    'University of Alberta', 'Universite de Montreal', 'University of Waterloo', 'University of Calgary',\
    'University of Ottawa','Western University', 'Dalhousie University', 'Queens University',\
    'York University', 'University of Guelph', 'Carleton University','Concordia University']
    pct = sch + '%'
    if isPositive(school[pct])
      topSchools.push({ school: sch, cnt: parseInt(school[sch]), pct: convertPct(school[pct]) })
  return topSchools

optimizeSchoolName = (name) ->
  return null unless name
  
  # 先使用generateSchoolName处理特殊缩写和格式
  name = generateSchoolName(name)
  
  # 再使用delSchoolCommWord移除常见词
  name = delSchoolCommWord(name)
  
  # 移除多余空格并转换为小写
  name = name?.toLowerCase()?.trim()?.replace(/\s+/g, ' ')
  
  return name

###*
 * 查找匹配的学校信息
 * @param {string} schoolName - 学校名称
 * @returns {Promise<Object>} - 匹配的学校信息或null
###
findMatchedSchool = (schoolName) ->
  # 首先检查是否为私立学校
  if privateSchoolName = PrivateSchoolNameMap[schoolName]
    try
      privateSchool = await PrivateSchoolCol.findOne(
        { nm: { $regex: privateSchoolName, $options: 'i' } },
        { projection: { _id: 1, nm: 1 } }
      )
      
      if privateSchool
        return {
          _id: privateSchool._id
          nm: privateSchool.nm
          isPrivate: true
        }
    catch err
      debug.error "查询私立学校 #{privateSchoolName} 时出错:", err

  # 如果不是私立学校或私立学校未找到，则查询公立学校
  # 检查是否需要映射学校名称
  if mappedName = schoolNameMap[schoolName]
    schoolName = mappedName

  optimizedName = optimizeSchoolName(schoolName)
  
  query = 
    $or: [
      { nm: schoolName },
      { nm: { $regex: schoolName, $options: 'i' } }
    ]
  
  if optimizedName and optimizedName isnt schoolName.toLowerCase()
    query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }

  # 在sch_rm_merged中查找匹配的学校
  matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1, nm: 1} })
  
  if matchedSchool
    return {
      _id: matchedSchool._id
      nm: matchedSchool.nm
      isPrivate: false
    }
  
  return null

###*
 * 处理学校数据
 * @param {Object} majorData - 包含学校数据的对象
 * @returns {Promise<void>}
###
processSchoolData = (majorData) ->
  # 遍历所有学校数据
  for school in majorData.data
    # 跳过无效数据
    continue unless school.total and parseInt(school.total) > 0 and (not isNaN(parseInt(school.total)))

    speedMeter.check {processSchool:1}
    schoolName = school['school name']
    
    # 查询匹配学校
    matchedSchool = await findMatchedSchool(schoolName)
    
    # 如果找到匹配的学校,则处理和保存数据
    if matchedSchool
      speedMeter.check {matchedSchool:1}
      
      # 构建专业分布数据和大学录取数据
      majors = buildMajorData(school)
      topSchools = buildTopSchoolsData(school)

      # 准备要保存的基础数据
      topSchoolsData =
        total: parseInt(school.total)
        smplSz: parseInt(school['sample amount'])
        smplPct: convertPct(school['sample%'])
        gradYr: parseInt(school['graduate year'])
        majors: majors
        topSchools: topSchools
        total2: parseInt(school['Total'])
        ivyOld: parseInt(school['Ivy old']) or 0
        ivyNew: parseInt(school['Ivy new']) or 0
        ivyTotal: parseInt(school['Ivy total']) or 0
      # 处理年份,年份为毕业年份
      topSchoolsData.yearmark = (topSchoolsData.gradYr - 1) + '-' + topSchoolsData.gradYr

      # 只保存百分比大于0的成绩数据
      for score in ['90+', '85+', '80+']
        pct = score + '%'
        if isPositive(school[pct])
          topSchoolsData[score] = parseInt(school[score])
          topSchoolsData[score+'Pct'] = convertPct(school[pct])
      rankSchool = await SchoolRankCol.findOne {_id: matchedSchool._id}
      ivyRank = rankSchool?.ivyRank or []
      if ivyRank?.length is 0
        ivyRank = [topSchoolsData]
      else
        ivyRank = mergeArrayByYearmark(ivyRank, [topSchoolsData])


      # 如果是试运行模式,则只打印数据而不更新数据库
      if dryrun
        debug.info "DRYRUN: Would update school #{school['school name']}"
        debug.info JSON.stringify(topSchoolsData, null, 2)
      else
        # 更新学校排名数据
        await SchoolRankCol.updateOne {_id: matchedSchool._id},{$set: {nm: matchedSchool.nm,ivyRank: ivyRank,top100: 1}},{upsert: true}
        
        speedMeter.check {updateSchoolRank:1}
        debug.info "Updated school: #{school['school name']}"
        totalUpdateCount++
    else
      speedMeter.check {unmatchedSchool:1}
      unmatchedSchools.push(school['school name'])

###*
 * 主函数 - 程序入口点
 * @returns {Promise<void>}
###
main = ->
  try
    debug.info "Starting school rank update process..."
    debug.info "File: #{fileName}"
    debug.info "Dry run: #{dryrun}"

    # 读取Excel文件
    majorData = await readExcel(fileName)

    # 处理数据
    await processSchoolData(majorData)

    # 计算并显示处理时间和结果
    processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
    debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
    
    # 打印所有未匹配的学校
    if unmatchedSchools.length > 0
      debug.warn "\n未匹配的学校列表 (#{unmatchedSchools.length} 所):"
      for school in unmatchedSchools
        debug.warn "- #{school}"
    
    EXIT 0
  catch error
    debug.error "Error updating school ranks:", error
    EXIT 1, error

# 执行主函数
main() 