###*
# 从Excel文件中读取学校EQAO排名数据并更新到数据库
# 
# @module school_rank_from_xlsx
# <AUTHOR>
# @usage 
#   sh start.sh -t batch -n school_eqao_rank -cmd "lib/batchBase.coffee batch/school_rank/school_rank_from_xlsx.coffee -f 文件路径 -y 年份"
# @param {string} -f/--fileName Excel文件路径
# @param {number} -y/--year 数据年份,输入截止年份,如2023代表2022-2023学年
# @param {boolean} -d/--dryrun 是否仅测试运行,不实际更新数据库

 导入时的注意事项：
 1. 检查是否所有学校都存在，没有更新的学校找一下数据库中保存的学校名称，核对学校信息，更新表格。
 2. 只更新表格中以'房大师'开始的字段字段，其他不需要，检查文件中的字段，其他字段如果以'房大师'开始的，需要删除'房大师'
 g3更新：学生总人数	G3FstLangEn	G3FstLangFr	Born in Canada 房大师总分(2位小數點）	房大师总分排名	房大师Reading良率	房大师Reading优率	房大师Reading得分(2位小數點）	房大师READING排名	房大师Writing 良率	房大师Writing优率	房大师WRITING得分2(up to 10)	房大师WRITING排名	房大师Math良率	房大师Math优率	房大师Math得分(2位小數點）	房大师Math排名
 g6更新：学生总人数	G6FstLangEn	G6FstLangFr	Born in Canada 房大师总分(2位小數點）	房大师总分排名	房大师Reading良率	房大师Reading优率	房大师Reading得分(2位小數點）	房大师READING排名	房大师Writing 良率	房大师Writing优率	房大师WRITING得分2(up to 10)	房大师WRITING排名	房大师Math良率	房大师Math优率	房大师Math得分(2位小數點）	房大师Math排名
 g9更新：学生总人数	G9FstLangEn	G9FstLangFr	Born in Canada	房大师Math参与率 房大师总分(2位小數點）	房大师总分排名	房大师Math良率	房大师Math优率	房大师Math得分(2位小數點）	房大师Math排名	房大师OSSLT参与率	房大师OSSLT通过率	房大师OSSLT得分(2位小數點）	房大师OSSLT排名
###

# 导入依赖模块
yargs = require('yargs')(AVGS)
XLSX = require('xlsx')
debug = DEBUG()
speed = INCLUDE 'lib.speed'
{deepCopyObject} = INCLUDE 'lib.helpers_object'
{judgeIsSameYearmark} = INCLUDE 'libapp.schoolHelper'
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}
# 命令行参数配置
yargs.option('fileName', { alias: 'f', description: 'File Name (Inclusion file path)'})
yargs.option('year', { alias: 'y', description: 'The year of the data, input the cut-off year'})
yargs.option('dryrun', { alias: 'd', description: 'dry run'})

# 获取命令行参数
fileName = yargs.argv.fileName
yearmark = (yargs.argv.year - 1) + '-' + yargs.argv.year
dryrun = yargs.argv.dryrun

# 初始化变量
startTs = new Date()
totalUpdateCount = 0
NotFound = []
updated = 0
gradeTotal = {
  G3: 0
  G6: 0
  G9: 0
}

# 参数校验
if not fileName
  return debug.error 'need fileName'

# 数据库集合
SchoolEqaoCol = COLLECTION 'vow', 'sch_eqao'
SchoolRmMergedCol = COLLECTION 'vow', 'sch_rm_merged'
SchoolRmRankCol = COLLECTION 'vow', 'sch_rm_rank'

# eqao部分新增数据
eqaoDataMap = {
  '学生总人数': 'Studnts'
  'G3FstLangEn': 'FstLangEn'
  'G3FstLangFr': 'FstLangFr'
  'G6FstLangEn': 'FstLangEn'
  'G6FstLangFr': 'FstLangFr'
  'G9FstLangEn': 'FstLangEn'
  'G9FstLangFr': 'FstLangFr'
  'Born in Canada': 'BornInCanada'
}
###*
# 判断数据列是否需要处理
# @param {string} key - 数据列名
# @param {string} grade - 年级
# @return {boolean} 是否需要处理该列
###
isNeedValue = (key, grade) ->
  keyReg = new RegExp('^房大师(w|r|m|o|总分)', 'i')
  return keyReg.test(key)

###*
# 格式化学校数据
# @param {Object} schoolItem - 学校原始数据
# @param {string} grade - 年级
# @return {Object} 格式化后的数据
###
formatData = (schoolItem, grade) ->
  returnVal = {}
  for k,v of schoolItem
    if k in (Object.keys eqaoDataMap)
      # 只将数据保存到rankData中
      returnVal[grade + eqaoDataMap[k]] = v
      continue
    k = k.toLowerCase().replace(/[ （）()_]/g, '').trim()
    if not isNeedValue(k, grade)
      continue
    key = grade + 'rm'
    if /osslt/i.test(k)
      key = 'OSSLTrm'
    if /math|数学/i.test(k)
      key += 'Math'
    if /reading/i.test(k)
      key += 'Reading'
    if /writing/i.test(k)
      key += 'Writing'
    if /良率/.test(k)
      key += 'Lv3'
    if /优率/.test(k)
      key += 'Lv4'
    if /排名/.test(k)
      key += 'Ranking'
    else if /参与率/.test(k)
      key += 'Pt'
    else if /通过率/.test(k)
      key += 'P'
    else if /(得分|总分)/.test(k)
      key += 'Score'
    if /Score/.test(key)
      if isNaN(Number(v))
        v = 0
      else
        v = Math.round(Number(v) * 100) #存成整数
    returnVal[key] = Number(v)
  return returnVal

###*
# 构建学校查询条件
# @param {Object} schoolItem - 学校数据
# @return {Object} 查询条件
###
buildQuery = (schoolItem) ->
  eqaoUrl = (schoolItem.网站ID or schoolItem.网站id or schoolItem.网站 or schoolItem.eqaourl)
  eqaoId = eqaoUrl.match(/mident=(\d+)/)[1]
  return eqaoId

# 学校ID映射配置
SchoolIdMapping = {
  # 合并学校数据配置
  # 格式: '原ID': {targetId: '目标ID', excludeGrades: ['排除的年级']}
  '834718': {targetId: '834718', excludeGrades: ['g3', 'g6']}  # St Michael's Choir
  '835200': {targetId: '834718'}  # 合并到St Michael's Choir
  '645525': {targetId: '924008'}  # 645525和924008是同一个学校，现在eqao官网中已经没有645525了，需要更新为924008
}

###*
# 保存单个学校数据
# @param {Object} schoolItem - 学校数据
# @param {string} grade - 年级
###
findOneByMatchId = (schoolItem, grade) ->
  rankData = formatData(schoolItem, grade)
  rankData.yearmark = yearmark
  rankData[grade+'total'] = gradeTotal[grade]
  eqaoId = buildQuery(schoolItem)

  # 处理学校ID映射
  if mapping = SchoolIdMapping[eqaoId]
    if mapping.excludeGrades?.includes(grade)
      return
    eqaoId = mapping.targetId
  query = {_id: eqaoId}
  school = await SchoolEqaoCol.findOne(query)
  if not school
    debug.info "排名学校不存在: #{query._id}"
    NotFound.push(query._id)
    speedMeter.check {notFound:1}
    return
  # 使用MongoDB聚合管道lookup获取sch_rm_rank表中对应_id的rmRank字段
  # 这样可以确保rankData中包含数据库已有的rmRank历史数据，便于后续合并
  existsRmRankData = await SchoolRmRankCol.findOne query,{ rmRank: 1 }
  # rmRankAgg为数组，取第一个结果的rmRank
  if existsRmRankData?.rmRank
    school.rmRank = existsRmRankData.rmRank
  else
    school.rmRank = []

  await updateOne {school, rankData, grade}


###*
# 根据yearmark合并数组数据，并按年份排序
# @param {Array} oldArray - 原始数组
# @param {Object} newData - 新数据对象
# @returns {Array} 合并后的数组
# @description 如果原数组中存在相同yearmark的数据,则合并数据,否则将新数据添加到数组中，并按yearmark降序排序
###
mergeArrayByYearmark = (oldArray, newData) ->
  # 创建数组副本以避免直接修改原数组
  result = []
  
  # 如果oldArray存在，先处理它
  if oldArray?.length > 0
    # 过滤掉所有包含rmRank字段的项，确保数据扁平化
    result = oldArray.filter (item) -> not item.rmRank
    
    # 查找是否存在相同yearmark的数据
    currentYearIndex = result.findIndex((el) -> judgeIsSameYearmark(el.yearmark, yearmark))
    
    if currentYearIndex > -1
      # 如果存在相同yearmark的数据，则合并数据
      result[currentYearIndex] = {...result[currentYearIndex], ...newData}
    else
      # 如果不存在，则添加新数据
      result.push(newData)
  else
    # 如果oldArray不存在或为空，直接添加新数据
    result.push(newData)
    
  # 按yearmark降序排序（最新年份在前）
  result.sort((a, b) ->
    yearA = parseInt(a.yearmark.split(/–|-/)[0])
    yearB = parseInt(b.yearmark.split(/–|-/)[0])
    return yearB - yearA
  )
  
  return result


###*
# 更新学校数据
# @param {Object} params - 参数对象
# @param {Object} params.school - 学校数据
# @param {Object} params.rankData - 排名数据
# @param {string} params.grade - 年级
###
updateOne = ({school, rankData, grade}) ->
  rmRank = school.rmRank or []
  updateRmRank = {eqaoId:school._id}
  ranking = {}

  # 将原来保存到merged表的数据现在保存到eqao表中
  if rankData[grade+'rmRanking']
    ranking['rm' + grade.toUpperCase() + 'Ranking'] = rankData[grade+'rmRanking']
    
  # 处理rmRank数据以保存到sch_rm_rank表
  if rmRank.length is 0
    rmRank = [rankData]
  else
    rmRank = mergeArrayByYearmark rmRank, rankData
      
    if rmRank.length > 1
      if rmRank[0][grade+'rmRanking'] and rmRank[1][grade+'rmRanking']
        rmRank[0][grade+'rmRankChange'] = rmRank[0][grade+'rmRanking'] - rmRank[1][grade+'rmRanking']
      if rmRank[0][grade+'rmScore'] and rmRank[1][grade+'rmScore']
        rmRank[0][grade+'rmRatingChange'] = rmRank[0][grade+'rmScore'] - rmRank[1][grade+'rmScore']
  
  updateRmRank.rmRank = rmRank
  updateRmRank = Object.assign(updateRmRank, ranking)
  if dryrun
    return
  
  # 更新sch_eqao表，保留rmG3Ranking,rmG6Ranking,rmG9Ranking字段，不再保存rmRank
  retEqao = await SchoolEqaoCol.updateOne {_id:school._id}, {$set: ranking}

  retMerged = await SchoolRmMergedCol.findOne {eqaoId:school._id}
  if retMerged
    await SchoolRmMergedCol.updateOne {_id:retMerged._id},{$set: ranking}
  

  # 更新或插入sch_rm_rank表数据
  retRank = await SchoolRmRankCol.updateOne _id:school._id, {$set: updateRmRank}, {upsert: true}
  
  if retEqao.modifiedCount > 0 or retRank.upsertedCount > 0 or retRank.modifiedCount > 0
    totalUpdateCount++
    updated++

main = ()->
  try
    workbook = XLSX.readFile(fileName, {})
  catch err
    return EXIT 1, err
    
  Sheets = workbook.Sheets
  
  # 清除旧排名数据
  await SchoolRmMergedCol.updateMany {}, {$unset:{'rmG3Ranking':1,'rmG6Ranking':1,'rmG9Ranking':1}}
  await SchoolEqaoCol.updateMany {}, {$unset:{'rmG3Ranking':1,'rmG6Ranking':1,'rmG9Ranking':1}}
  await SchoolRmRankCol.updateMany {}, {$unset:{'rmG3Ranking':1,'rmG6Ranking':1,'rmG9Ranking':1}}
  
  # 处理每个工作表
  for key,v of Sheets
    # 如果key中没有数字 return
    if not key.match(/\d+/)
      continue
    worksheet = Sheets[key]
    grade = 'g' + key.match(/\d+/)[0]
    updated = 0
    sheetJSONArray = XLSX.utils.sheet_to_json(worksheet)
    gradeTotal[grade] = sheetJSONArray.length
    
    total = sheetJSONArray.length
    for schoolItem, index in sheetJSONArray
      # 检查学校名称是否存在，如果不存在则跳过当前学校处理下一条数据
      if not schoolItem?.name
        continue
      speedMeter.check {processCount:1}
      gradeMap = {}
      gradeMap[grade] = 1
      speedMeter.check gradeMap
      try
        await findOneByMatchId(schoolItem, grade)
      catch err
        debug.error err
        return EXIT 1, err
      if (index + 1) % 100 is 0
        debug.info "Grade #{grade}: 已处理 #{index + 1}/#{total} 所学校"
    debug.info "Grade #{grade} 完成处理: 共更新 #{updated} 所学校"

  # 输出处理结果
  processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
  debug.info "Completed, Total process time #{processTs} mins. ",speedMeter.toString()
  debug.info "NotFound eqao IDs: #{NotFound.join(',')}" if NotFound.length > 0
  EXIT 0

main()
