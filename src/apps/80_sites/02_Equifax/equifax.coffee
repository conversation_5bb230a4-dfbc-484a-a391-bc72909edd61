debug = DEBUG()
config = CONFIG(['equifax'])
equifax = INCLUDE 'lib.equifax'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{dateFormat} = INCLUDE 'lib.helpers_date'
{respError} = INCLUDE 'libapp.responseHelper'
cityHelper = INCLUDE 'lib.cityHelper'
helpers = INCLUDE 'lib.helpers'
libproperties = INCLUDE 'libapp.properties'
{removeHostFromUrl,replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
objectCache = INCLUDE 'libapp.objectCache'
EquifaxModel = MODEL 'Equifax'
UserModel = MODEL 'User'
TokenModel = MODEL 'Token'
GroupModel = MODEL 'Group'
SysNotifyModel = MODEL 'SysNotify'
CreditReportKey = 'creditReport'

EQUIFAX_CONFIG = config.equifax?.api
CLIENT_ID = config.equifax?.clientId
CLIENT_SECRET = config.equifax?.clientSecret
CUSTOMER_CONFIG = config.equifax?.customer

equifaxInstance = new equifax.Equifax(CLIENT_ID, CLIENT_SECRET, EQUIFAX_CONFIG)
FieldsAbbr = EquifaxModel.FieldsAbbr
ScoresMark = [
  {poor: 'Poor',value: '300-559'},
  {poor: 'Fair',value: '560-659'},
  {poor: 'Good',value: '660-724'},
  {poor: 'Very Good',value: '725-759'},
  {poor: 'Excellent',value: '760-900'}
]
TradeType = ['revolving', 'mortgage', 'installment', 'open']

MenuRegistryModel = MODEL 'MenuRegistryIns'
getEquifaxMenu = ({req,user}) ->
  isInRealGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
  if isInRealGroup or req.isAllowed('admin')
    return {name: 'Credit Report', url: '/equifax/history', index: 1, isIfrm: 1}
  return null
MenuRegistryModel.registerMenu('creditReport', getEquifaxMenu)

APP 'equifax'

# 保存失败信息，并且更新报告状态
saveErrorMsgAndUpdateStatus = (recordId, errmsg, resp)->
  try
    recordId = await EquifaxModel.update({baseInfo:{recordId: recordId, status: 'Failed',errmsg: errmsg}})
  catch err
    debug.error err
  return

# 判断是否登录和用户权限是否为admin和inreal group
isAuthedToUseEquifax = ({user, isAdmin, route}) ->
  unless user
    return {result: false,msg: MSG_STRINGS.NEED_LOGIN}
  try
    isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
  catch err
    debug.error err
    return {result: false, msg: MSG_STRINGS.DB_ERROR}
  unless (isGroup or isAdmin)
    debug.error {msg:"#{route}:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
    return {result: false, msg: MSG_STRINGS.NO_AUTHORIZED}
  return {result: true, msg: ''}

# 处理调用report接口，前端传入的参数
dealConsumerData = (body) ->
  unless body?.consumer
    return false
  body.pic = removeHostFromUrl(body.pic) if body.pic
  if body.consumer.address?.provinceCode
    body.consumer.address.provinceCode = cityHelper.getProvAbbrName(body.consumer.address.provinceCode)
  else
    return false
  body.provinceCode = cityHelper.getProvAbbrName(body.provinceCode) if body.provinceCode
  body.status = 'Response'
  return body

# 初步解析报告，查找uniqueNumber，如果uniqueNumber存在则说明根据填写的信息找到了报告，反之没有找到，给管理员发送信息提示；
getUniqueNumberAndSendAdmin = (dbParams, adminFailMsg) ->
  consumerCreditReport = parseConsumerCreditReport(dbParams.reportJs)
  reportBaseInfo = {}
  hasUniqNum = false
  if Object.keys(consumerCreditReport).length
    reportBaseInfo = parseCnHeader(consumerCreditReport.CNHeader?[0])
  if reportBaseInfo.uniqueNumber
    score = parseScoreData(consumerCreditReport.CNScores?[0])
    dbParams.baseInfo.score = score
    hasUniqNum = true
  else
    dbParams.baseInfo.status = 'Failed'
    dbParams.baseInfo.errmsg = 'No credit report was found for the tenant.'
    hasUniqNum = false
    SysNotifyModel.notifyAdmin "Equifaix report not uniqueNumber\n#{adminFailMsg}"
  return {hasUniqNum, dbParams}

# 保存token记录，如果没有找到报告不扣除token, 如果扣除失败则发送邮件给admin
aplyToken = ({memo, uid, recordId, eml}) ->
  tokenParam =
    uid: uid
    key: CreditReportKey
    id: recordId
    memo: memo
  adminTokenFailMsg = "Equifaix spend token saved faild. Remember to manually deduct credit report token\nuserInfo: #{uid}, eml:#{eml}\ntokenParam: #{helpers.stringify(tokenParam)}\nerror message:"
  try
    {err} = await TokenModel.applyToken(tokenParam)
  catch err
    debug.error err
    SysNotifyModel.notifyAdmin "#{adminTokenFailMsg}#{err.toString()}"
  if err
    debug.error err
    SysNotifyModel.notifyAdmin "#{adminTokenFailMsg}#{err.toString()}"
  return

# equifax/report 调用第三方接口获取报告信息，对于这个类型的token可以从0开始往负值扣分，因此调取前不需要判断token余额是否充足。
# 情况一：第三方返回错误信息，则不扣除token，返回给前端进行修改；
# 情况二：官方返回报告，保存自己数据库失败，则发送邮件给admin，并告知用户不要在点击Pull Credit Report，请联系管理员；
# 情况三：官方返回报告，但是报告中没有unique number,认为没有根据信息查找到相关报告，此时修改状态为Failed，保存error信息，并发送邮件给admin，不扣除token；
POST 'report', (req,resp)->
  body = dealConsumerData(req.body)
  unless body
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  consumerData = body.consumer
  UserModel.auth {req,resp},(user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'post equifax/report'})
    unless result
      return respError {clientMsg: req.l10n(msg), resp}
    uid = user._id
    # 将前端填写的信息先保存至数据库
    try
      recordId = await EquifaxModel.update({uid:uid,baseInfo: body})
    catch err
      debug.error err
      return respError { clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), resp }
    # 调用第三方获取报告数据
    try
      [requestXml,reportXml, reportJs] = await equifaxInstance.getEquifaxReport(CUSTOMER_CONFIG, consumerData)
    catch err
      debug.error 'getEquifaxReport error:',err.toString(),'\nuser:',user.eml,'\nconsumer:', consumerData
      await saveErrorMsgAndUpdateStatus(recordId, err.toString(), resp)
      return respError {clientMsg:"Get Equifax report failed: #{err.toString()}",resp}
    dbParams = {
      baseInfo: { recordId: recordId, status: 'Success'},
      requestXml,
      reportXml,
      reportJs
    }
    adminFailMsg = "userInfo: #{user._id}, eml:#{user.eml}\nrecordId: #{recordId}\nconsumer data: #{helpers.stringify(consumerData)}\noutJson:#{JSON.stringify(reportJs)}"
    {hasUniqNum, dbParams} = getUniqueNumberAndSendAdmin(dbParams, adminFailMsg)
    # 将第三方返回的数据保存至数据库，如果数据库保存失败，给admin发送邮件，告知前端不需要再次点击，请联系管理员。
    try
      recordId = await EquifaxModel.update(dbParams)
    catch err
      debug.error err, "recordId: #{recordId}\ninxml:#{requestXml}\noutxml: #{reportXml}"
      SysNotifyModel.notifyAdmin "Equifaix report saved failed, remember to manually deduct credit report token.\n#{adminFailMsg}\n Error message: #{err.toString()}"
      msg = "The report has been queried but failed to be saved. Please do not click query again and contact the administrator error Id.\nError Id:#{recordId}"
      return respError {clientMsg: msg, resp}
    unless hasUniqNum
      return respError {clientMsg:"Get Equifax report failed: #{dbParams.baseInfo.errmsg}",resp}
    tokenMemo = "#{body.firstName} #{body.lastName}"
    # 保存token记录，如果没有找到报告不扣除token, 该函数不应该影响报告的返回，所以不用加await
    aplyToken({memo: tokenMemo, uid, recordId, eml: user.eml})
    return resp.send {ok:1,recordId}

# POST 'testReport', (req,resp)->
#   _error = (e)->
#     return resp.send {ok:0,err:e}
#   consumerData = req.param 'consumer'
#   return _error('No consumer info.') unless consumerData
#   try
#     [requestXml,reportXml, reportJs] = await equifaxInstance.getEquifaxReport(CUSTOMER_CONFIG, consumerData)
#   catch err
#     debug.error 'getEquifaxReport error:',err.toString(),'\nuser:',user.eml,'\nconsumer:', consumerData
#     return resp.send {ok:0,error: "Get Equifax report failed: #{err.toString()}"}
#   return resp.send {ok:1,reportJson:reportJs, reportXml: reportXml}

# 将报告中每个模块的数据根据打开方式app/web处理成前端需要的格式
dealDataAccordEquipmnt = (dataArry, isApp) ->
  unless isApp
    return dataArry
  tmpObj = {}
  result = []
  for item in dataArry
    parseDate = item.date.slice(0, 7)
    if tmpObj[parseDate]
      tmpObj[parseDate].detail.push(item)
    else
      tmpObj[parseDate] = {date: parseDate, detail:[item]}
  result = Object.values(tmpObj)
  return result

# 解析Local Inquiries模块数据
parseLocalInquiriesData = (localInquiries, isApp) ->
  unless localInquiries?.length
    return {localInquCnt: 0, localInqu: []}
  localInqu = []
  for item in localInquiries
    tmpObj =
      date: item['$']?.date or '-'
    customerId = item.CustomerId?[0]
    telephone = customerId?.Telephone?[0]
    pasTelephone = telephone?.ParsedTelephone?[0]
    if pasTelephone
      telNum = pasTelephone.Number?[0] or '-'
      telArcode = pasTelephone.AreaCode?[0] or '-'
      tmpObj.tel = "#{telArcode}-#{telNum}"
    else
      tmpObj.tel = '-'
    tmpObj.memberNum = customerId?.CustomerNumber?[0] or '-'
    tmpObj.memberName = customerId?.Name?[0] or '-'
    localInqu.push(Object.assign({}, tmpObj))
  localInquCnt = localInqu.length
  return {localInquCnt, localInqu: dealDataAccordEquipmnt(localInqu, isApp)}

# 解析 Bankruptcies 模块数据
parseBankruptcyOrActsData = (bankruptcyOrActs, isApp) ->
  unless bankruptcyOrActs?.length
    return {bnkrptcyActsCnt: 0, bnkrptcyActs: []}
  bnkrptcyActs = []
  for item in bankruptcyOrActs
    tmpObj =
      date: item.DateFiled?[0] or '-'
    courtId = item.CourtId?[0]
    tmpObj.custNumber = courtId?.CustomerNumber?[0] or '-'
    intentDisp = item.IntentOrDisposition?[0]
    tmpObj.inteOrDisposi = intentDisp?.$?.description or '-'
    if liabilityAmnt = item.LiabilityAmount?[0]
      tmpObj.liabilityAmnt = libproperties.currencyFormat(liabilityAmnt,'$',0)
    else
      tmpObj.liabilityAmnt = 'N/A'
    type = item.Type?[0]
    tmpObj.type = type?.$?.description or '-'
    tmpObj.caseNumTrus = item.CaseNumberAndTrustee?[0] or '-'
    if assetAmt = item.AssetAmount?[0]
      tmpObj.assetAmt = libproperties.currencyFormat(assetAmt,'$',0)
    else
      tmpObj.assetAmt = 'N/A'
    filer = item.Filer?[0]
    tmpObj.filer = filer?.$?.description or '-'
    bnkrptcyActs.push(Object.assign({}, tmpObj))
  bnkrptcyActsCnt = bnkrptcyActs.length
  return {bnkrptcyActsCnt, bnkrptcyActs: dealDataAccordEquipmnt(bnkrptcyActs, isApp)}

# 解析 Collection 模块数据
parseCollectionsData = (collections) ->
  unless collections?.length
    return {collectionCnt: 0, collctions: []}
  resultCollections = []
  for item in collections
    tmpObj = {}
    if /unpaid collection/i.test(item['$']?.description)
      tmpObj.type = 'Unpaid Collection'
    else
      tmpObj.type = 'Paid Collection'
    tmpObj.assignedDate = item.AssignedDate?[0] or '-'
    if originalAmt = item.OriginalAmount?[0]
      tmpObj.originalAmt = libproperties.currencyFormat(originalAmt,'$',0)
    else
      tmpObj.originalAmt = 'N/A'
    if balanceAmt = item.BalanceAmount?[0]
      tmpObj.balanceAmt = libproperties.currencyFormat(balanceAmt,'$',0)
    else
      tmpObj.balanceAmt = 'N/A'
    creditorInfo = item.CollectionCreditor?[0]
    if creditorInfo
      tmpObj.creditorName = creditorInfo.AccountNumberAndOrName?[0] or '-'
    resultCollections.push(Object.assign({}, tmpObj))
  collectionCnt = resultCollections.length
  return {collectionCnt, collctions: resultCollections}

# 解析 Special Services 模块数据
parseSpecialServicesData = (specialServices, isApp) ->
  unless specialServices?.length
    return {specialServiceCnt: 0, spcialSrvics: []}
  specialService = []
  for item in specialServices
    tmpObj =
      date: item.DateReported?[0] or ''
    tmpObj.desc = item['$']?.description or '-'
    customerId = item.CustomerId?[0]
    tmpObj.custNum = customerId?.CustomerNumber?[0] or '-'
    tmpObj.custName = customerId?.Name?[0] or '-'
    telephone = customerId?.Telephone?[0]?.ParsedTelephone?[0]
    if telephone
      acode = telephone.AreaCode?[0] or '-'
      num = telephone.Number?[0] or '-'
      tmpObj.telNum = "#{acode}-#{num}"
    else
      tmpObj.telNum = "-"
    specialService.push(Object.assign({}, tmpObj))
  specialServiceCnt = specialService.length
  return {specialServiceCnt, spcialSrvics: dealDataAccordEquipmnt(specialService, isApp)}

# 解析 Accounts 模块数据
parseRevolvingData = (tradeData, portfolioCode, portfolioDescription) ->
  tmpObj =
    lastRept: tradeData.DateReported?[0] or '-'
  creditorId = tradeData.CreditorId?[0]
  if creditorId
    tmpObj.custNum = creditorId.CustomerNumber?[0] or '-'
    tmpObj.custName = creditorId.Name?[0] or '-'
  else
    tmpObj.custNum = '-'
    tmpObj.custName = '-'
  telephone = creditorId?.Telephone?[0]?.ParsedTelephone?[0]
  if telephone
    telAc = telephone.AreaCode?[0] or '-'
    telNum = telephone.Number?[0] or '-'
    tmpObj.tel = "#{telAc}-#{telNum}"
  else
    tmpObj.tel = '-'
  accountNum = tradeData.AccountNumber?[0]
  if accountNum
    tmpObj.accountNumOrig = accountNum
    tmpObj.accountNum = '***' + accountNum.slice(-3)
  else
    tmpObj.accountNum = '-'
  tmpObj.asscition = tradeData.Association?[0]?.$?.description or '-'
  if highCreditAmount = tradeData.HighCreditAmount?[0]
    tmpObj.highAmunt = libproperties.currencyFormat(highCreditAmount,'$',0)
  else
    tmpObj.highAmunt = 'N/A'
  if balanceAmount = tradeData.BalanceAmount?[0]
    tmpObj.balncAmt = libproperties.currencyFormat(balanceAmount,'$',0)
  else
    tmpObj.balncAmt = 'N/A'
  if pastDueAmount = tradeData.PastDueAmount?[0]
    tmpObj.pastDueAmt = libproperties.currencyFormat(pastDueAmount,'$',0)
  else
    tmpObj.pastDueAmt = 'N/A'
  tmpObj.monthsRviwd = tradeData.MonthsReviewed?[0] or '-'
  tmpObj.dateOpened = tradeData.DateOpened?[0] or '-'
  tmpObj.lastActvtyPay = tradeData.DateLastActivityOrPayment?[0] or '-'
  paymtRate = tradeData.PaymentRate?[0]?.$
  if paymtRate
    tmpObj.paymtRateDesc = "#{portfolioDescription} - #{paymtRate.description or '-'}"
    tmpObj.paymtRateCode = "#{portfolioCode}#{paymtRate.code or '-'}"
  else
    tmpObj.paymtRateDesc = '-'
    tmpObj.paymtRateCode = '-'
  if pymtTerm = tradeData.PaymentTermAmount?[0]
    tmpObj.pymtTerm = libproperties.currencyFormat(pymtTerm,'$',0)
  else
    tmpObj.pymtTerm = 'N/A'
  narrative = tradeData.Narratives?[0]?.Narrative
  if narrative?.length
    notes = ''
    for item in narrative
      if desc = item['$']?.description
        notes += "#{desc}, "
    if notes.length > 1
      tmpObj.notes = notes.slice(0, -2)
  preHighPaymentRate = tradeData.PreviousHighPaymentRates?[0]?.PreviousHighPaymentRate
  if preHighPaymentRate?.length
    delinquencies = ''
    for item in preHighPaymentRate
      date = item.Date?[0]
      desc = item['$']?.description
      if date and desc
        delinquencies += "#{date}: #{desc}, "
      else
        delinquencies += "#{date or desc}, "
    if delinquencies.length > 1
      tmpObj.delinquencies = delinquencies.slice(0, -2)
  return tmpObj

# 解析 Judgements 模块数据
parseJudgementsData = (judgements,isApp) ->
  unless judgements?.length
    return {judgmntCnt: 0, judgmnts: []}
  judgement = []
  for item in judgements
    tmpObj =
      date: item.DateFiled[0]
    courtIdOri = item.CourtId?[0]
    if courtIdOri
      tmpObj.custName = courtIdOri.Name?[0] or '-'
    else
      tmpObj.custName = '-'
    tmpObj.caseNum = item.CaseNumber?[0] or '-'
    tmpObj.statusDes = item.Status?[0]?['$']?.description
    tmpObj.dateSatis = item.DateSatisfied?[0] or '-'
    if amount = item.Amount?[0]
      tmpObj.amount = libproperties.currencyFormat(amount,'$',0)
    else
      tmpObj.amount = 'N/A'
    judgement.push(Object.assign({}, tmpObj))
  judgmntCnt = judgement.length
  return {judgmntCnt, judgmnts: dealDataAccordEquipmnt(judgement, isApp)}

# 解析 Extended Trades 模块数据
parseExtendedTradesData = (extendedTrades) ->
  unless extendedTrades?.length
    return []
  extTrades = []
  for item in extendedTrades
    tmpObj = {}
    portfolioType = item.PortfolioType?[0]?.$?.description
    continue unless portfolioType
    tmpObj.portfolioType = portfolioType.toLowerCase()
    accountNum = item.AccountNumber?[0]
    if accountNum
      tmpObj.accountNumOrig = accountNum
    creditLimit = item.CreditLimitAmount?[0]
    if creditLimit
      tmpObj.creditLimit = libproperties.currencyFormat(creditLimit,'$',0)
    writtenOﬀ = item.WrittenOffAmount?[0]
    if writtenOﬀ
      tmpObj.writtenOﬀ = libproperties.currencyFormat(writtenOﬀ,'$',0)
    actualPayment = item.ActualPaymentAmount?[0]
    if actualPayment
      tmpObj.actualPayment = libproperties.currencyFormat(actualPayment,'$',0)
    dateClosed = item.DateClosed?[0]
    if dateClosed
      tmpObj.dateClosed = dateClosed
    extTrades.push(Object.assign({}, tmpObj))
  return extTrades

# 解析 Score 模块数据
parseScoreData = (cnScores) ->
  unless (scores = cnScores?.CNScore?[0])
    return '-'
  unless (result = scores.Result?[0])
    return '-'
  unless (value = result.Value?[0])
    return '-'
  return Number(value)

# 解析曾住地址
parseAddressDate = (addresses) ->
  unless addresses?.length
    return []
  addressArry = []
  for item in addresses
    civicNum = item.CivicNumber[0] if item.CivicNumber?[0]
    streetName = item.StreetName[0] if item.StreetName?[0]
    street = "#{civicNum or ''} #{streetName or ''}"
    prov = item.Province?[0]?['$']?.code or '-'
    city = item.City?[0]?['_'] or '-'
    zip = item.PostalCode?[0] or '-'
    if zip.length is 6
      zip = zip.slice(0, 3) + ' ' + zip.slice(3)
    address = "#{street}, #{city} #{prov}, #{zip}"
    tmpObj = {
      addr: street,
      city: city,
      prov: prov,
      postalCode: zip,
      value: address
    }
    if item['$']?.description is 'Current Address'
      tmpObj.type = 'Current'
      addressArry.unshift(Object.assign({}, tmpObj))
    else
      tmpObj.type = 'Previous'
      addressArry.push(Object.assign({}, tmpObj))
  return addressArry

# 解析 Employments
parseEmploymentsDate = (employments) ->
  unless employments?.length
    return []
  employmentArry = []
  for emplym in employments
    value = emplym.Employer?[0] or ''
    desp = emplym['$']?.description or ''
    if /current employment/i.test(desp)
      employmentArry.unshift({
        title: 'Current',
        value: value
      })
    else
      employmentArry.push({
        title: 'Previous',
        value: value
      })
  return employmentArry

# 解析用户报告正文部分
parseConsumerCreditReport = (reportJson) ->
  report = {}
  unless (efxReport = reportJson.EfxTransmit?.EfxReport?[0])
    return report
  unless (consumerCreditReports = efxReport.CNConsumerCreditReports?[0])
    return report
  unless (consumerCreditReport = consumerCreditReports.CNConsumerCreditReport?[0])
    return report
  return consumerCreditReport

# 解析用户报告正文的头部基础信息
parseCnHeader = (cnHeader) ->
  result = {}
  unless cnHeader
    return {}
  unless (creditFile = cnHeader.CreditFile?[0])
    return {}
  if uniqueNum = creditFile.UniqueNumber?[0]
    result.uniqueNumber = uniqueNum
  if fileSinceDate = creditFile.FileSinceDate?[0]
    result.fileSinceDate = fileSinceDate
  if dateOfLastActivity = creditFile.DateOfLastActivity?[0]
    result.dateOfLastActivity = dateOfLastActivity
  if dateOfRequest = creditFile.DateOfRequest?[0]
    result.dateOfRequest = dateOfRequest
  return result

# 解析用户基本信息
parseCnBaseInfo = (cnBaseInfo) ->
  result = {}
  unless cnBaseInfo
    return {}
  if (request = cnBaseInfo.Request?[0])
    result.persNumber = request.CustomerNumber?[0] or '-'
  if (subject = cnBaseInfo.Subject?[0])
    if subjectName = subject.SubjectName?[0]
      lastName = subjectName.LastName?[0]
      firstName = subjectName.FirstName?[0]
      middleName = subjectName.MiddleName?[0]
      if lastName and firstName
        result.name = "#{firstName} #{lastName}"
        if middleName
          result.name = "#{firstName} #{middleName} #{lastName}"
    if soclInsrnNum = subject.SubjectId?[0]?.SocialInsuranceNumber?[0]
      result.soclInsrnNum = soclInsrnNum.slice(-3).padStart(soclInsrnNum.length, '*')
  return result

# 解析 Trade 模块数据
parseTradeData = (trades,extTrade) ->
  result = {
    revolvingCnt: 0,
    revolving: [],
    mortgageCnt: 0,
    mortgage: [],
    installmentCnt: 0,
    installment: [],
    openCnt: 0,
    open: []
  }
  unless trades?.length
    return result
  for item in trades
    portfolioType = item.PortfolioType?[0]?.$
    continue unless portfolioType
    portfolioCode = portfolioType.code
    portfolioDescription = portfolioType.description
    tradeData = parseRevolvingData(item, portfolioCode, portfolioDescription)
    # 根据code进行分组，L和C放在R分组中
    if portfolioCode in ['R', 'L', 'C']
      result.revolving.push(Object.assign({}, tradeData))
    else if portfolioCode is 'M'
      result.mortgage.push(Object.assign({}, tradeData))
    else if portfolioCode is 'I'
      result.installment.push(Object.assign({}, tradeData))
    else if portfolioCode is 'O'
      result.open.push(Object.assign({}, tradeData))
  for key in TradeType
    result[key + 'Cnt'] = result[key].length or 0
  parseExtTrades = parseExtendedTradesData(extTrade)
  unless parseExtTrades?.length
    return result
  # 处理扩展交易数据，查找并合并相同accountNumOrig的记录
  for extItem in parseExtTrades
    continue unless extItem.accountNumOrig
    # 在交易类型中查找匹配的记录
    foundIndex = result[extItem.portfolioType].findIndex (item) -> item.accountNumOrig is extItem.accountNumOrig
    if foundIndex >= 0
      # 找到匹配记录，合并数据
      Object.assign(result[extItem.portfolioType][foundIndex], extItem)
  return result

# 将json格式的报告转成前端需要的格式
delReportInfoForFrontEnd = (reportJson, isApp) ->
  report = {}
  consumerCreditReport = parseConsumerCreditReport(reportJson)
  unless Object.keys(consumerCreditReport).length
    return report
  # 解析前端需要展示的模块信息
  # 解析头部基础信息
  report = parseCnHeader(consumerCreditReport.CNHeader?[0])
  # 解析用户基本信息
  baseInfo = parseCnBaseInfo(consumerCreditReport.CNHeader?[0])
  report = Object.assign(report, baseInfo)
  # 解析曾住地址
  cnAddresses = consumerCreditReport.CNAddresses?[0].CNAddress
  addressData = parseAddressDate(cnAddresses)
  report.addressData = addressData if addressData.length
  # 解析 Employment
  cnEmployments = consumerCreditReport.CNEmployments?[0]?.CNEmployment
  employmentData = parseEmploymentsDate(cnEmployments)
  report.emplymntData = employmentData if employmentData.length
  # 解析 Bankruptcy
  bankruptcyOrActs = consumerCreditReport.CNBankruptciesOrActs?[0]?.CNBankruptcyOrAct
  {bnkrptcyActsCnt, bnkrptcyActs} = parseBankruptcyOrActsData(bankruptcyOrActs, isApp)
  report.bankruptcyData = bnkrptcyActs if bnkrptcyActs.length
  report.bnkrptcyActsCnt = bnkrptcyActsCnt
  # 解析 Collections
  collections = consumerCreditReport.CNCollections?[0]?.CNCollection
  {collectionCnt, collctions} = parseCollectionsData(collections)
  report.collectionData = collctions if collctions.length
  report.collectionCnt = collectionCnt
  # 解析 Judgements
  judgements = consumerCreditReport.CNLegalItems?[0]?.CNLegalItem
  {judgmntCnt, judgmnts} = parseJudgementsData(judgements, isApp)
  report.judgementsData = judgmnts if judgmnts.length
  report.judgmntCnt = judgmntCnt
  # 解析 Trade
  trade = consumerCreditReport.CNTrades?[0]?.CNTrade
  extTrade = consumerCreditReport.CNExtendedTrades?[0]?.CNExtendedTrade
  tradeInfo = parseTradeData(trade,extTrade)
  for key in TradeType
    if tradeInfo[key]?.length
      report[key] = tradeInfo[key]
    report[key + 'Cnt'] = tradeInfo[key + 'Cnt']
  # 解析 Local Inquiries
  localInquiries = consumerCreditReport.CNLocalInquiries?[0]?.CNLocalInquiry
  {localInquCnt, localInqu} = parseLocalInquiriesData(localInquiries, isApp)
  report.localInquData = localInqu if localInqu.length
  report.localInquCnt = localInquCnt
  # 解析 Special Services
  specialServices = consumerCreditReport.CNSpecialServices?[0]?.CNSpecialService
  {specialServiceCnt, spcialSrvics} = parseSpecialServicesData(specialServices, isApp)
  report.spcilServData = spcialSrvics if spcialSrvics.length
  report.specialServiceCnt = specialServiceCnt
  return report

# /equifax/edit
GET 'edit', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'get equifax/edit'})
    unless result
      return resp.redirect '/1.5/settings'
    systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys', CreditReportKey
    token = systemTokenCache.t
    if token < 0
      token = token * -1
    tkCreditRpt = (token / 100).toFixed(2)
    recordId = req.param('recordId')
    param =
      tkCreditRpt: tkCreditRpt
    unless recordId
      return resp.ckup 'equifax/editInfo',param
    try
      detail = await EquifaxModel.getDetail(recordId)
    catch err
      debug.error err
      return resp.redirect '/1.5/settings'
    unless detail
      return resp.redirect '/1.5/settings'
    for k,v of FieldsAbbr
      param[v] = detail[k] if detail[k]
    if param.provinceCode
      param.provinceCode = cityHelper.getProvFullName(param.provinceCode)
    if param.pic
      param.pic = replaceRM2REImagePath(param.pic)
    if detail.errmsg
      param.errmsg = detail.errmsg
    param.recordId = detail._id
    return resp.ckup 'equifax/editInfo',param

# equifax/history
GET 'history', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'get equifax/history'})
    unless result
      return resp.redirect '/1.5/settings'
    historyList = []
    try
      historyList = await EquifaxModel.getHistory(user._id)
    catch err
      debug.error err
      return resp.redirect '/1.5/settings'
    if historyList?.length
      historyList = dealHistoryDetailFrontEnd(historyList)
    param = { historyList }
    return resp.ckup 'equifax/historyList', param

# 将数据库查询到的数据转换成前端需要的格式
dealHistoryDetailFrontEnd = (historyList)->
  historyObj = {}
  historyList.forEach((list) ->
    date = dateFormat(list._mt, 'YYYY-MM-DD')
    name = "#{list.fn or  '-'} #{list.ln or '-'}"
    list.name = name
    list.memo = list.m or ''
    if historyObj[date]
      historyObj[date].his.push(list)
    else
      historyObj[date] = {
        date: date,
        his: [list]
      }
  )
  return Object.values(historyObj)

# /equifax/report app端打开报告页面
GET 'report', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    recordId = req.param('recordId')
    paramIsApp = req.param('isApp')
    lineLength = req.param('lineLength') or 750
    isApp = false
    if paramIsApp is 'true'
      isApp = true
    unless recordId
      return resp.redirect '/1.5/settings'
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'get equifax/report'})
    unless result
      return resp.redirect '/1.5/settings'
    try
      detail = await EquifaxModel.getDetail(recordId,{outJson:1,ln:1,fn:1,dob:1,score:1,sin:1})
    catch err
      debug.error err
      return resp.redirect '/1.5/settings'
    unless detail?.outJson
      return resp.redirect '/1.5/settings'
    report = delReportInfoForFrontEnd(detail.outJson, isApp)
    unless report.name
      report.name = "#{detail.fn or ''} #{detail.ln or ''}"
    if (not report.soclInsrnNum?.length) and detail.sin?.length
      report.soclInsrnNum = detail.sin.slice(-3).padStart(detail.sin.length, '*')
    report.dateOfBirth = detail.dob.replace /(\d{4})-\d{2}-(\d{2})/, "$1-xx-$2"
    {score, min, max, num} = getPhaseForScore(detail.score)
    leftPos = caclCirclePosition({score, min, max, num, lineLength})
    report.leftPos = leftPos
    report.score = score
    return resp.ckup 'equifax/creditReport',{report, ScoresMark, isApp}

# /equifax/webHistory web端打开报告页面
GET 'webHistory', (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'get equifax/webHistory'})
    unless result
      return resp.ckup 'webGeneralError',{err: MSG_STRINGS.NO_AUTHORIZED}
    encodeUrl = encodeURIComponent('/equifax/history')
    return resp.redirect "/adminindex?d=#{encodeUrl}"

# /equifax/delete 删除草稿或查询失败的报告记录
POST 'delete',(req,resp)->
  body = req.body
  unless body?.recordId
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'post equifax/delete'})
    unless result
      return respError {clientMsg: req.l10n(msg), resp}
    try
      result = await EquifaxModel.deleteHistory(body.recordId)
    catch err
      debug.error err
      return respError { clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), resp }
    return resp.send {ok:1,msg: req.l10n('Deleted')}

# /equifax/update 保存草稿或更新memo
POST 'update',(req,resp)->
  body = req.body
  unless body
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    {result, msg} = await isAuthedToUseEquifax({user, isAdmin: req.isAllowed('admin'), route: 'post equifax/update'})
    unless result
      return respError {clientMsg: req.l10n(msg), resp}
    try
      result = await EquifaxModel.update({baseInfo:body,uid:user._id})
    catch err
      debug.error err
      return respError { category: MSG_STRINGS.DB_ERROR, resp }
    return resp.send {ok:1,msg: req.l10n('Saved'), recordId: result}

# 根据分数获取对应的阶段范围，进行计算游标的位置
getPhaseForScore = (score)->
  unless score
    return {score: '-', max: 0, min: 0, num: 0}
  scoreNum = Number(score)
  max = 0
  min = 0
  num = 0
  for item,index in ScoresMark
    splitValue = item.value.split('-')
    lowValue = Number(splitValue[0])
    hightValue = Number(splitValue[1])
    if (score <= hightValue and score >= lowValue)
      max = hightValue
      min = lowValue
      num = index
      break
  return {score: scoreNum, max, min, num}

# 计算报告中的直线根据信誉报告分数的坐标位置
caclCirclePosition = ({score, min, max, num, lineLength}) ->
  # 如果没有分数，返回默认位置
  unless score
    return 'left: 0px'
  # 计算分数在min和max之间的百分比
  percentage = 0
  if min is max
    return 'left: 0px'
  averLength = Number(lineLength) / 5
  percentForPx = Number(averLength) / 100
  # 计算百分比：(score - min) / (max - min)
  percentage = (score - min) / (max - min) * 100
  positionForPx = percentage * percentForPx
  # 百分比直接映射到直线长度
  leftPx = averLength * num + positionForPx
  leftPos = 'left:' + leftPx + 'px'
  return leftPos
