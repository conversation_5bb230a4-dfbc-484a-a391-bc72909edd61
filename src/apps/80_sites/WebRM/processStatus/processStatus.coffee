ProcessStatusModel = MODEL 'ProcessStatus'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{respError} = INCLUDE 'libapp.responseHelper'

APP 'proccess'
# apis
POST (req,resp)->
  try
    #check header secrect
    debug.info req.headers?['secrect']
    if req.headers?['secrect'] isnt 'S-qq8vm'
      return respError {clientMsg:MSG_STRINGS.ACCESS_DENIED, resp}
    status = await ProcessStatusModel.checkStatus()
    return resp.send {ok:1, status}
  catch err
    return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp}
