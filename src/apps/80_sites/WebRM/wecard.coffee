Wecard = COLLECTION "chome", 'wecard'
# accessAllowed = DEF '_access_allowed'
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
{formatUrlStr} = INCLUDE 'lib.helpers_string'
getBanners = DEF 'getBanners'
UserModel = MODEL 'User'

VIEW 'contact-us-vip', ->
  text """
  <div class="bar bar-header absolute"><a id="menu-toggle" href="#menu-toggle" class="btn btn-default glyphicon glyphicon-menu-hamburger"></a></div>
  <div style="padding-top:54px" class="container-fluid">
    <div class="row">
      <div class="col-lg-12">
        <p>#{_ 'Vip Realtors only, Become a VIP today?'}</p>
        <a href='https://www.realmaster.ca/'>https://www.realmaster.ca/</a>
      </div>
    </div>
  </div>
    """
  coffeejs ->
    document.querySelector('#menu-toggle').onclick = (e) ->
      e.preventDefault()
      document.querySelector('#wrapper').classList.toggle 'toggled'
      return
    null

APP 'wepage'

GET (req,resp)->
  if req.redirectHTTP()
    return
  UserModel.auth {req,resp,url:'/login'},(user)->
    isZh = req.locale() in ['zh' , 'zh-cn']
    # resp.ckup 'wecard-list'
    if req.isAllowed 'webEdit'
      # resp.ckup 'wecard-list-page2', {isZh:isZh}, '_', {noref:true}
      encodeUrl = encodeURIComponent('/1.5/wecard')
      resp.redirect "/adminindex?d=#{encodeUrl}"
    else
      resp.ckup 'contact-us-vip', {}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

# VIEW 'wecard-list-page2', ->
#   text '''<style>#page-content-wrapper{cursor:not-allowed}</style>'''
#   iframe src:'/1.5/wecard',
#   width:'700px',
#   height:'700px',
#   onload:'this.height=document.body.offsetHeight;'



APP 'wecards', true

POST 'recommend',(req, resp, next)->
  # rcmd:1,
  Wecard.findToArray {'meta.vc' : {$gte:10}, $or: [{tp:'blog'}, {tp: 'topic'}], del: {$ne: true}, noWeb: {$ne: true}, 'meta.ts': {$gte: new Date(Date.now() - 24 * 60 * 60 * 30 * 1000)}},
  {fields: {seq: 0}, sort: {'meta.vc': -1}, limit: 10},(err, wecards) ->
    if (err)
      return resp.send { ok: 0, error: err }
    if wecards?.length > 0
      return resp.send { ok: 1, wecards: wecards }
    else
      return resp.send { ok: 0, error: 'wecards is not found' }

GET 'count',(req, resp, next)->
  # rcmd:1,
  Wecard.countDocuments {'meta.vc': {$gte:10}, $or: [{tp:'blog'}, {tp: 'topic'}], noWeb: {$ne: true}, del: {$ne: true}},(err, count) ->
    if (err)
      return resp.send { ok: 0, error: err }
    resp.send { ok: 1, count: count }


POST (req, resp, next)->
  if req.body.limit
    limit =  req.body.limit
  else
    limit =  20
  if req.body.page
    page =  req.body.page
  else
    page = 0
  # rcmd:1,
  Wecard.findToArray {'meta.vc' : {$gte:10}, $or: [{tp:'blog'}, {tp: 'topic'}], del: {$ne: true}, noWeb: {$ne: true}},
  {fields: {seq: 0}, sort: {'meta.ts': -1}, skip: page * 20, limit: limit},(err, wecards) ->
    if (err)
      return resp.send { ok: 0, error: err }
    if wecards?.length > 0
      return resp.send { ok: 1, wecards: wecards }
    else
      resp.send { ok: 0, error: 'wecards is not found' }

# NOTE: 有些wpnews的微图文侵权，暂时关闭该接口，该接口只在sitemap里使用
# APP 'wpnews'
# GET ':id/:title',(req, resp, next)->
#   if helpers.isSearchEngine req
#     return resp.send {err:'not found'}, 404
#   req.setLocale lang if lang = req.param 'lang'
#   getWecardDetails req, resp, next

# GET ':id',(req, resp, next)->
#   if helpers.isSearchEngine req
#     return resp.send {err:'not found'}, 404
#   req.setLocale lang if lang = req.param 'lang'
#   getWecardDetails req,resp,next

# webErrPage = DEF 'webErrPage'
# done = (req, resp, html, wecard)->
#   ctx = { html, req}
#   ctx.title = "#{wecard.meta?.title} | #{req.l10n "Realmaster"}"
#   ctx.author = req.l10n('Realmaster Technology Inc.')
#   ctx.description = "#{wecard.meta?.desc || wecard.meta?.title}"
#   ctx.lang = req.locale()
#   ctx.url = "http://#{req.host}/wpnews/#{formatUrlStr wecard.meta?.title}"
#   ctx.images = ['http://'+req.host+'/web/imgs/logo2.png']
#   if wecard.meta?.img
#     ctx.images.push wecard.meta.img

#   indexSpWeb = getBanners(req.locale(),'webSideBanner') or []
#   if ad = indexSpWeb[0]
#     ad.impImgUrl = ad.impImgUrl.replace('[timestamp]',(''+Date.now())).replace('[ts]',(''+Date.now())) if ad.impImgUrl
#     ctx.ad = ad
#   layoutOpt = {hasQrcode:1}
#   templateName = 'wecard-detail'
#   layoutName = 'layout'
#   return resp.ckup templateName,ctx,layoutName, layoutOpt unless wecard?.uid

#   Wecard.findToArray {'uid' : wecard.uid, _id: {$ne: wecard._id}, 'meta':{ $exists: true }, noWeb: {$ne: true}},{fields: {seq: 0}, sort: {'meta.ts': -1}, limit: 25},(err, wecards) ->
#     return webErrPage req,resp if err
#     if wecards?.length
#       html.recommendCardsPart1 = wecards?.slice(0,5)
#       html.recommendCardsPart2 = wecards?.slice(5)
#     UserModel.findById wecard.uid,{},(err, user) ->
#       return webErrPage req,resp if err
#       if user
#         html.userInfo = user
#         html.isVipUser = UserModel.accessAllowed 'vipUser',user
#         html.userInfo.fnm = libUser.fullNameOrNickname req.locale(),user
#         html.userInfo.tel = user.mbl or user.tel
#         html.userInfo.avt = user.avt
#         html.userInfo.eml = UserModel.getEmail user
#         html.userInfo.desc = user.desc
#         # TODO: libUser.userCpny req.locale(),user
#         html.userInfo.cpny = user.cpny
#       resp.ckup templateName,ctx,layoutName, layoutOpt

# getWecardDetails = (req, resp, next) ->
#   id =  req.param 'id'
#   Wecard.findOne {_id:id, noWeb:{$ne: true}}, (err, wecard)->
#     return webErrPage req, resp,'NA' if err or !wecard or (wecard.del is true)
#     html = {content: "", host: req.host,isAdmin: req.isAllowed 'newsAdmin'}
#     if wecard?.meta?
#       html.author = wecard.meta.editor
#       html.title = wecard.meta.title
#       if wecard.meta.ts
#         year = wecard.meta.ts.getFullYear()
#         month = wecard.meta.ts.getMonth() + 1
#         date = wecard.meta.ts.getDate()
#         html.time = "#{year}-#{month}-#{date}"
#       html.hasFb = wecard.meta.fb is 1
#       html.wpid = id
#       html.img = wecard.meta.img or null
#       html.fbtl = wecard.meta.fbtl

#     if wecard?.seq?.length > 0
#       for i in [0 ... wecard.seq.length]
#         html.content += wecard.seq[i].m
#       Wecard.updateOne {_id:id},{$inc:{"meta.vc": 1, "meta.vcd": 1}},{upsert:false},(err) ->
#         return webErrPage req, resp if err
#         done req, resp, html, wecard
#     else
#       html.content = req.l10n "Empty!"
#       done req, resp,html, wecard

# POST 'update', (req, resp, next)->
#   findError = (e)->
#     resp.send {ok:0,e:e}
#   UserModel.auth {req,resp},(user)->
#     return findError 'Access Denied' unless user and UserModel.accessAllowed 'newsAdmin',user
#     id = req.param 'id'
#     cmd = req.param 'cmd'
#     unless id and cmd == 'del'
#       return findError 'Bad Parameter'

#     Wecard.updateOne {_id:id},{$set:{"noWeb": true}},(err)->
#       if err then return findError err.toString()
#       resp.send {ok:1}

# VIEW 'wecard-detail', ->
#   css '/web/css/common.css'
#   css '/web/css/wecards.css'
#   js '/js/jquery-2.1.1.min.js'
#   div id: "wecardDetails",style:'opacity: 0;', ->
#     div id: "navbar", ->
#       text """<rednavbar></rednavbar>"""
#     div class: "qrcode-container",->
#       div id: "qrcode"
#       div class: "qrcode-text",->
#         text _ "Scan QR-Code Open in Mobile"
#     div class: "container detail-container", ->
#       div class: "detail-left col-sm-8 col-xs-12",->
#         h1 style:'font-size: 30px;', ->
#           text @html.title
#         p ->
#           span -> text @html.author
#           span class: "margin-left-5", ->
#             text @html.time
#         if @html.isAdmin
#           p class:'delete', ->
#             text 'Delete'
#         div class: "detail-content", ->
#           text @html.content
#       div class: "col-sm-4 col-xs-12", ->
#         if @html.recommendCardsPart1 && @html.recommendCardsPart1.length
#           if @html.userInfo
#             div class: "recommend-user-info",->
#               div class: "center", ->
#                 img class: "recommend-avtar",src: @html.userInfo.avt
#               div class: "recommend-author",->
#                 span -> text @html.author
#               if @html.isVipUser
#                 if @html.userInfo.tel
#                   div class: "center",->
#                     a class: "phone", href:"tel:+1"+@html.userInfo.tel, ->
#                       text (@html.userInfo.tel)
#                 if @html.userInfo.eml
#                   div class: "center",->
#                     a class: "phone", href:"mailto:"+@html.userInfo.eml, ->
#                       text (@html.userInfo.eml)
#                 if @html.userInfo.cpny
#                   div class: "recommend-desc center",->
#                     text (@html.userInfo.cpny or "")
#               div class: "recommend-desc", ->
#                 text @html.userInfo.desc
#           div class: "recommend-list", ->
#             for wecard in @html.recommendCardsPart1
#               wecardTitle = (wecard.meta?.title|| "").replace(/\//g, "").replace(/%/g, "")
#               wecardLink = "http://#{@html.host}/wpnews/#{wecard._id}/#{wecardTitle}"
#               div class: "col-xs-12",->
#                 div class: "col-xs-3",->
#                   a href:wecardLink , target: "_blank", ->
#                     if wecard.meta?.img
#                       img class: "recommend-list-avtar",src: wecard.meta.img
#                 div class: "col-xs-9 recommend-list-title",->
#                   a href: wecardLink, target: "_blank", ->
#                     if wecard.meta
#                       text wecard.meta.title
#         if @ad
#           div class: "margin-top-20 pull-left", style:'line-height: 0;',->
#             url = '/adJump/' + @ad._id
#             a href: url, target: "_blank", style:'display:inline-block !important;',->
#               img src: @ad.src,  width: "100%", height: "100%"
#               if @ad.impImgUrl
#                 img src:@ad.impImgUrl, width:'1', height:'1',border:'0',style:'width:1px; height:1px; display:inline'
#         div class: "margin-top-20 margin-bottom-20 pull-left", ->
#           a href: "/app-download?lang="+@req.locale(), target: "_blank", ->
#             img src: "/web/imgs/download.png",  width: "100%", height: "100%"
#         if @html.recommendCardsPart2 && @html.recommendCardsPart2.length
#           div class: "recommend-list", ->
#             for wecard in @html.recommendCardsPart2
#               wecardLink = "http://#{@html.host}/wpnews/#{wecard._id}"
#               div class: "col-xs-12",->
#                 div class: "col-xs-3",->
#                   a href: wecardLink, target: "_blank", ->
#                     if wecard.meta?.img
#                       img class: "recommend-list-avtar",src: wecard.meta.img
#                 div class: "col-xs-9 recommend-list-title",->
#                   a href: wecardLink, target: "_blank", ->
#                     if wecard.meta
#                       text wecard.meta.title
#         if @html.hasFb
#           div class:'feedback-wrapper',->
#             text """<signupform :owner="owner" :user-form="userForm" :is-web="true" :title="title"></signupform>"""

#     coffeejs {vars:{
#       wpid:@html.wpid,
#       img:@html.img,
#       fbtl:@html.fbtl,
#       }}, ->
#       $('.delete').on 'click',(e)->
#         data = {cmd:'del', id:vars.wpid}
#         $.post '/wpnews/update',data,(ret)->
#           alert JSON.stringify ret
#       null
#   # css '/web/css/bootstrap.min.css'
#   js '/js/rmapp.min.js'
#   # js '/web/packs/qrcode.min.js'
#   js '/web/packs/commons.js'
#   js '/web/packs/wecardDetails.js'
#   # js '/web/packs/bootstrap.min.js'
