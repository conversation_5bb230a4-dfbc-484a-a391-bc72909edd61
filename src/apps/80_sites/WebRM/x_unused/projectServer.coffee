# path = require 'path'
# fs = require 'fs'
# #vueServerRenderer = require 'vue-server-renderer/build.js'
# { createBundleRenderer } = require('vue-server-renderer')
# serialize = require 'serialize-javascript'
# helpers = INCLUDE 'lib.helpers'
# Projects = COLLECTION 'chome', 'pre_constrct'
# ProjectModel = MODEL 'Project'
# getGoogleAPILink = DEF 'getGoogleAPILink'

# UserModel = MODEL 'User'
# config = CONFIG()
# serverBundleFilePath = path.join(config.media.path,'web/packs/bundle.projectList.js');
# serverBundleFileCode = fs.readFileSync(serverBundleFilePath, 'utf8');
# #bundleRenderer = vueServerRenderer.createBundleRenderer(serverBundleFileCode);
# bundleRenderer = createBundleRenderer(serverBundleFileCode);


# detailserverBundleFilePath = path.join(config.media.path,'web/packs/bundle.projectDetail.js');
# detailserverBundleFileCode = fs.readFileSync(detailserverBundleFilePath, 'utf8');
# #detailsBundleRenderer = vueServerRenderer.createBundleRenderer(detailserverBundleFileCode);
# detailsBundleRenderer = createBundleRenderer(detailserverBundleFileCode);

# getProjectLists = DEF "getProjectLists"
# getProjectDetails = DEF "getProjectDetails"
# webErrPage = DEF "webErrPage"

# RMlistingPics = DEF 'RMlistingPics'
# getProjOpt = DEF 'getProjOpt'

# APP 'projects'
# POST 'nm', (req, resp) ->
#   listMode = req.body.listMode
#   q = {status:'A'}
#   # if listMode is 'soon'
#   #   q.showtp = 'Complete Soon'
#   # else
#   #   q.showtp = 'New Release'
#   Projects.findToArray q, {fields:{'nm':1}}, (err, nm) ->
#     return resp.send {ok:0} if err
#     resp.send {ok:1, nm: nm}


# GET ':id', (req, resp)->
#   getDetails req, resp
# GET ':id/:nm', (req, resp)->
#   getDetails req, resp

# # deprecated url
# GET 'details/:id', (req, resp)->
#   getDetails req, resp

# getDetails = (req,resp) ->
#   req.setLocale lang if lang = req.param 'lang'
#   gmapStatic = getGoogleAPILink(req,{tp:'staticMap',cn:req.isChinaIP(), src:'mlstg'})
#   context = {webBackEnd:(req.isAllowed 'webBackEnd'),exMapURL:req.exMapURL(),isCip: req.isChinaIP(),gmapStatic: gmapStatic, lang:req.locale(), isVipUser:(req.isAllowed 'vipUser'), host: req.host}
#   id =req.param 'id'
#   return webErrPage(req, resp, 'no id', 'NA') unless id = id?.split('.')[0]
#   # TODO: _unused file no need modify
#   UserModel.auth {req,resp},(user)->
#     context.isLoggedIn = user?
#     UserModel.findPublicInfo {lang:req.locale(),user},(err,sessionUser)->
#       context.sessionUser= sessionUser
#       opt = getProjOpt req
#       opt.id = id
#       getProjectDetails opt, user, (err, proj)->
#         if err then return webErrPage req, resp, err, 'NA'
#         proj.picUrls = RMlistingPics proj.img
#         context.proj = proj
#         initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"
#         detailsBundleRenderer.renderToString context, (err, html) ->
#           if err then return webErrPage req, resp, err
#           ctx = { html:html, initialState: initialState}
#           ctx.title = "#{proj.nm}, #{proj.addr}, #{proj.city} #{req.l10n 'Latest Pre-Construction'} | #{req.l10n 'Realmaster.com'}"
#           ctx.isCip = req.isChinaIP()
#           ctx.lang = req.locale() || 'en'
#           desc = if ctx.lang == 'en'  then proj.desc_en else proj.desc
#           ctx.description = "#{desc || proj.desc}, #{req.l10n 'Check out the property photos, floor plans and amenities.'}"
#           ctx.author = req.l10n('Realmaster Technology Inc.')
#           ctx.url = "http://#{req.host}/projects/#{proj._id}/#{helpers.formatUrlStr proj.nm}"
#           ctx.images = proj.picUrls
#           resp.ckup 'project-view',ctx,'layout', {menu:'newCondos',page:'newCondos'}

# GET 'list', (req, resp)->
#   req.setLocale lang if lang = req.param 'lang'
#   limit = 60
#   page = parseInt req.param('page') or 1
#   context = {webBackEnd:(req.isAllowed 'webBackEnd'),currentPage: page, isCip: req.isChinaIP(), lang:req.locale(), newsAdmin:(req.isAllowed 'newsAdmin'), isVipUser:(req.isAllowed 'vipUser'), host: req.host}

#   listMode = req.param 'listMode'
#   nm = req.param 'nm'
#   return webErrPage(req, resp, 'no listMode or nm', 'NA') unless listMode or nm
#   UserModel.auth {req,resp},(user)->
#     context.isLoggedIn = user?
#     UserModel.findPublicInfo {lang:req.locale(),user},(err,sessionUser)->
#       context.sessionUser= sessionUser
#       getProjectLists {req: req, mode:listMode, nm: nm, page: page-1}, (err, projList)->
#         return webErrPage req, resp, err if err
#         context.projList = projList || []
#         for proj in projList
#           proj.src = RMlistingPics(proj.img)[0]
#         ProjectModel.getCountByFields {listMode,nm},(err,count)->
#           if err then return webErrPage req, resp, err
#           context.count = count
#           context.totalPage = Math.ceil(count / limit)
#           initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"
#           bundleRenderer.renderToString context, (err, html) ->
#             if err then return webErrPage req, resp, err
#             ctx = { html:html, initialState: initialState}
#             ctx.title = "#{req.l10n 'New Condo and Townhome Developments'} | #{req.l10n 'Realmaster.com'}"
#             ctx.description = req.l10n 'View new presale homes and developments in Canada. Realmaster.com provides photos, floorplans, and amenities of your favourite developments.'
#             ctx.isCip = req.isChinaIP()
#             ctx.author = req.l10n('Realmaster Technology Inc.')
#             ctx.lang = req.locale() || 'en'
#             urlbase = "http://#{req.host}/projects/list?listMode=#{listMode}#{if nm then '&nm='+nm else ''}"
#             ctx.url = urlbase + "#{if page then '&page='+page else ''}"
#             ctx.images = ['http://'+req.host+'/web/imgs/logo2.png']
#             if page && (page + 1) <= context.totalPage
#               ctx.next = urlbase + "&page=#{page + 1}"
#             if page && (page - 1) > 0
#               ctx.prev = urlbase + "&page=#{page - 1}"
#             resp.ckup 'project-list',ctx,'_', { description: 1, author: 1, noref:1, noAngular:1, noAppcss:1, noRatchetcss:1, viewport:1, nosmbcss:1, noSidebarcss:1,side:0,jquery:1,bootstrap:1,newHeaderFooter:1,menu:'projects'}

# VIEW 'project-list', ->
#   # css '/web/css/common.css'
#   # css '/web/css/wecards.css'
#   text @html
#   text @initialState
#   js '/web/packs/commons.js'
#   js '/js/lz-string.min.js'
#   js '/web/packs/projectList.js'
#   # css '/web/css/bootstrap.min.css'
#   # js '/js/jquery-2.1.1.min.js'
#   # js '/web/packs/bootstrap.min.js'

# VIEW 'project-view',->
#   # css '/web/css/common.css'
#   # css '/web/css/wecards.css'
#   js '/js/rmapp.min.js'
#   text @html
#   text @initialState
#   js '/js/lz-string.min.js'
#   js '/web/packs/commons.js'
#   js '/web/packs/projectDetail.js'
#   # css '/web/css/bootstrap.min.css'
#   # js '/js/jquery-2.1.1.min.js'
#   # js '/web/packs/bootstrap.min.js'
