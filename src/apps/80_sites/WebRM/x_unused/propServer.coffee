# path = require 'path'
# fs = require 'fs'
# #vueServerRenderer = require 'vue-server-renderer/build.js'
# { createBundleRenderer } = require('vue-server-renderer')
# serialize = require 'serialize-javascript'
# helpers = INCLUDE 'lib.helpers'
# User = COLLECTION "chome", 'user'
# libP = INCLUDE 'libapp.properties'
# # genListingPicReplaceUrls = libproperties.genListingPicReplaceUrls
# # listingPicUrlReplace = libproperties.listingPicUrlReplace
# getGoogleAPILink = DEF 'getGoogleAPILink'

# auth = DEF "_auth_auth"
# config = CONFIG()

# serverBundleFilePath = path.join(config.media.path,'web/packs/bundle.propList.js')
# serverBundleFileCode = fs.readFileSync(serverBundleFilePath, 'utf8')
# #bundleRenderer = vueServerRenderer.createBundleRenderer(serverBundleFileCode);
# bundleRenderer = createBundleRenderer(serverBundleFileCode)

# detailserverBundleFilePath = path.join(config.media.path,'web/packs/bundle.propDetail.js')
# detailserverBundleFileCode = fs.readFileSync(detailserverBundleFilePath, 'utf8')
# #detailsBundleRenderer = vueServerRenderer.createBundleRenderer(detailserverBundleFileCode);
# detailsBundleRenderer = createBundleRenderer(detailserverBundleFileCode)

# getPropDetails = DEF "getPropDetails"
# getPropList = DEF "getPropList"
# translatePtps = DEF "translatePtps"
# getUserEmail = DEF 'getUserEmail'
# getUserPrivateInfo = DEF 'getUserPrivateInfo'
# webErrPage = DEF 'webErrPage'


# RMlistingPics = DEF 'RMlistingPics'

# APP 'prop'

# renderHtml = (req, resp, context) ->
#   prop = context.prop
#   prop.webUrl = req.fullUrl libP.getPropDetailUrl(prop)
#   prov = prop.prov
#   city = prop.city
#   addr = prop.addr
#   zip = prop.zip
#   country = prop.cnty
#   bedrooms = prop.bdrms
#   bathrooms = prop.bthrms
#   type = if prop.ptype2 then  prop.ptype2[0] else prop.pstyl
#   saletp = if prop.saletp then prop.saletp[0] else req.l10n 'For Sale'
#   initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"

#   detailsBundleRenderer.renderToString context, (err, html) ->
#     if err then return webErrPage req, resp, err
#     ctx = { html:html, initialState: initialState}
#     title = "#{addr}, #{city}, #{prov} #{zip} #{type} #{saletp}"
#     description = title + " #{if prop.sid then '(MLS®'+prop.sid+')'}. Check out property details, home price, nearby schools and neighbourhood information."
#     ctx.isCip = req.isChinaIP()
#     ctx.author = req.l10n('Realmaster Technology Inc.')
#     ctx.lang = req.locale() || 'en'
#     id = if prop.ltp=='assignment' || prop.ltp == 'exlisting' then prop.id else prop._id
#     if prop.ltp is 'MLS'
#       menu = 'prop'
#     else
#       menu = prop.ltp
#     if prop?.addr
#       addr = " #{prop.addr}#{if prop.city then '-'+ prop.city }#{if prop.prov then '-'+prop.prov}#{if prop.zip then '-'+prop.zip}#{if prop.sid then '-'+prop.sid}"

#     ctx.url = "http://#{req.host}/prop/#{prop.id}/#{helpers.formatUrlStr addr}"
#     ctx.images = prop.picUrls
#     # resp.ckup 'prop-detail',ctx,'_', {keywords: 1, description: 1, author: 1, noref:1, noAngular:1, noAppcss:1, viewport:1, nosmbcss:1,noSidebarcss:1,side:0,jquery:1,bootstrap:1,newHeaderFooter:1,menu:menu}
#     resp.ckup 'prop-detail',ctx,'layout', {title,prop,description,menu:menu}


# GET ':id', (req, resp)->
#   getDetails req, resp

# GET ':id/:addr', (req, resp)->
#   getDetails req, resp
# # deprecated url
# GET 'details', (req, resp)->
#   getDetails req, resp

# getDetails = (req, resp) ->
#   req.setLocale lang if lang = req.param 'lang'
#   lang = req.locale()
#   gmapStatic = getGoogleAPILink(req,{tp:'staticMap',cn:req.isChinaIP(), src:'mlstg'})
#   host = req.host
#   context = {webBackEnd:(req.isAllowed 'webBackEnd'),exMapURL:req.exMapURL(),isCip: req.isChinaIP(),gmapStatic: gmapStatic, lang:req.locale(), isVipUser:(req.isAllowed 'vipUser'), host}
#   id = req.param 'id'
#   skipLogin = req.param('edm') || req.cookies['rltr'] || helpers.isSearchEngine(req)
#   return webErrPage req, resp, 'no id', 'NA' unless  id = id?.split('.')[0]
#   resp.cookie 'rltr',1,{path:'/',maxAge:1000*3600*24*365} if skipLogin
#   auth req,resp,null,(user)->
#     opt = libP.getBasePropDetailOpt req,user
#     opt.id = id
#     opt.share = 1 if skipLogin
#     # if /^RM/.test id
#     #   opt.id = id
#     #   delete opt._id
#     opt.isWebDetail = true
#     context.isLoggedIn = user?
#     context.sessionUser= getUserPrivateInfo(req, user)
#     context.userCity = user?.city
#     # context.sessionUser= getUserPrivateInfo(req, user)
#     getPropDetails opt, (err, prop)->
#       if not prop
#         return webErrPage req, resp, req.l10n(err.msg), 'NA'
#       if err
#         if (err.code is 1) and (not skipLogin)
#           return webErrPage req, resp, req.l10n(err.msg), 'origin', true
#       newPropDetailUrl = libP.getPropDetailUrl prop,lang,{protocol:req.getProtocol(),host}
#       return resp.redirect newPropDetailUrl
#       # TODO: @rain  fix this with genListingPicReplaceUrls
#       if /^RM/.test(prop.id)
#         prop.pic.ml_num = prop.sid or prop.ml_num
#         # prop.picUrls = RMlistingPics(prop.pic);
#         # console.log prop.picUrls
#       # else
#       #   prop.picUrls = listingPicUrls(prop, req.isChinaIP());
#       libP.genListingPicReplaceUrls(prop, req.isChinaIP(), {use3rdPic:config.use3rdPic,isPad:req.isPad()})
#       prop.picUrls = libP.listingPicUrlReplace(prop)
#       context.prop = prop
#       if (skipLogin || user?.roles?.indexOf('realtor')>=0) && prop.uid && (prop.ltp == 'exlisting' || prop.ltp=='assignment')
#         User.findOne {_id: prop.uid}, (err, u)->
#           if err then return webErrPage req, resp, err
#           return renderHtml req, resp, context if !u
#           context.userInfo = {}
#           context.userInfo.fnm = req.fullNameOrNickname u
#           context.userInfo.tel = u.mbl or u.tel
#           context.userInfo.avt = u.avt
#           context.userInfo.eml = getUserEmail u
#           context.userInfo.desc = u.desc
#           context.userInfo.cpny = if lang is 'en' then u.cpny_en else u.cpny_zh

#           renderHtml req, resp, context
#       else
#         renderHtml  req, resp, context

# GET 'list', (req, resp)->
#   getList req, resp
# GET 'list/:id', (req, resp)->
#   getList req, resp

# getList = (req,resp)->
#   arr = req.param('id')?.split('.') || []
#   params = {}
#   fields = ['mlsonly','city', 'bdrms', 'bthrms','gr', 'no_mfee', 'prov', 'ptype', 'saletp', 'src', 'ptp', 'pstyl', 'ltp', 'label', 'ohz', 'page','sort','status']

#   for obj in arr
#     key = obj.split('=')[0]
#     val = obj.split('=')[1]
#     if fields.indexOf(key)>=0
#       val = false if val == "false"
#       val = true if val == "true"
#       params[key] = val
#     if key =='ptype2'
#       params.ptype2 = val.split(',')
#     if key =='lang'
#       req.setLocale val
#     if ['max_lp','min_lp',"max_mfee"].indexOf(key)>=0
#       params[key] = Number(val)
#   context = {webBackEnd:(req.isAllowed 'webBackEnd'),isCip: req.isChinaIP(), lang:req.locale(), isVipUser:(req.isAllowed 'vipUser'), host: req.host}
#   if params.ltp == 'assignment'
#     params.mt = {$gt: new Date(Date.now() - 3600000 * 24 * 30)}

#   context.params = params
#   auth req,resp,null,(user)->
#     context.isLoggedIn = user?
#     city = req.l10n params.city if params.city
#     prov = req.l10n params.prov if params.prov
#     saletp = req.l10n params.saletp if params.saletp
#     ptp = req.l10n params.ptp if params.ptp
#     ptype = req.l10n (params.ptype || 'Residential')
#     pstyl = req.l10n params.pstyl if  params.pstyl
#     ptype2str = []
#     if params.ptype2
#       for key in params.ptype2
#         ptype2str.push req.l10n key

#     context.city = params.city

#     context.ptpTypes = translatePtps req
#     params.page--
#     getPropList req, resp, user, params, (err, ret, items)->
#       if err then return webErrPage req, resp, err
#       if items then context.propList = items else context.propList = ret.items
#       context.ul = ret.ul
#       context.totalnum = ret.cnt
#       page = Math.ceil(Number(ret.cnt)/50)
#       context.page = page
#       initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"
#       bundleRenderer.renderToString context, (err, html) ->
#         if err then return webErrPage req, resp, err
#         meta = {}
#         lang = req.locale() || 'en'
#         bdrms = if params.bdrms then (params.bdrms + req.l10n 'bedrooms') else ''
#         bthrms = if params.bthrms then (params.bthrms + req.l10n 'bathrooms') else ''

#         ctx = { html:html, initialState: initialState}
#         title = "#{city}, #{prov}, #{req.l10n 'ON MLS® Listings & Real Estate for'} #{saletp} | #{req.l10n 'Realmaster.com'}"
#         menu = 'prop'
#         if params.ltp == 'assignment'
#           title  =  "#{city}, #{prov}, #{req.l10n 'Assignments | Realmaster.com'}"
#           menu = 'assignment'
#         if params.ltp == 'exlisting'
#           title = "#{city}, #{prov}, #{req.l10n 'Exclusive listings | Realmaster.com'}"
#           menu = 'exlisting'
#         description = "Realmaster.com provides the latest MLS® listings of houses and condos, plus new homes for sale in Canada and real estate market news."
#         ctx.isCip = req.isChinaIP()
#         ctx.author = req.l10n('Realmaster Technology Inc.')
#         ctx.lang = req.locale() || 'en'
#         urlbase = "http://#{req.host}/prop/list/saletp=#{params.saletp}#{if params.src then '.src='+params.src}#{if params.ltp then '.ltp='+params.ltp}#{if params.ptype then '.ptype='+params.ptype}#{if params.city then '.city='+params.city}#{if params.prov then '.prov='+params.prov}#{if params.bdrms then '.bdrms='+params.bdrms}#{if params.bthrms then '.bthrms='+params.bthrms}"
#         ctx.url = urlbase + "#{if params.page then '.page='+params.page else ''}"
#         ctx.images = ['http://'+req.host+'/web/imgs/logo2.png']
#         if params.page && (params.page + 1) <= page
#           ctx.next = urlbase + ".page=#{params.page + 1}"
#         if params.page && (params.page - 1) > 0
#           ctx.prev = urlbase + ".page=#{params.page - 1}"

#         # resp.ckup 'prop-list',ctx,'_', {noRatchetcss:1, description: 1, author: 1, noref:1, noAngular:1, noAppcss:1, viewport:1, nosmbcss:1,noSidebarcss:1,side:0,jquery:1,bootstrap:1,newHeaderFooter:1,menu:menu}
#         resp.ckup 'prop-list',ctx,'layout', {title,description,menu:menu}


# VIEW 'prop-detail', ->
#   text @html
#   text @initialState
#   js '/js/rmapp.min.js'
#   css '/web/css/common.css'
#   # css '/web/css/bootstrap.min.css'
#   css '/web/css/wecards.css'
#   css '/web/css/details.css'
#   # js '/js/jquery-2.1.1.min.js'
#   if @isCip
#     js 'https://static.geetest.com/static/tools/gt.js'
#     js "https://ditu.google.cn/maps/api/js?libraries=geometry&sensor=true"
#   else
#     js "https://maps.googleapis.com/maps/api/js?libraries=geometry&sensor=true" #,places,weather&sensor=false"
#     text """<script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit"async defer>
#        </script><script>function onloadCallback() {window.bus.$emit('recaptcha-loaded');}</script>"""

#   js '/web/packs/qrcode.min.js'
#   js '/web/packs/commons.js'
#   js '/web/packs/propDetail.js'
#   # js '/web/packs/bootstrap.min.js'

# VIEW 'prop-list', ->
#   text @html
#   text @initialState
#   js '/js/rmapp.min.js'
#   css '/web/css/common.css'
#   # css '/web/css/bootstrap.min.css'
#   css '/web/css/wecards.css'
#   css '/web/css/listpage.css'
#   # js '/js/jquery-2.1.1.min.js'
#   js '/web/packs/qrcode.min.js'
#   js '/web/packs/commons.js'
#   js '/web/packs/propList.js'
#   # js '/web/packs/bootstrap.min.js'
#   if @isCip
#     js 'https://static.geetest.com/static/tools/gt.js'
#   else
#     text """<script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit"async defer>
#        </script><script>function onloadCallback() {window.bus.$emit('recaptcha-loaded');}</script>"""
