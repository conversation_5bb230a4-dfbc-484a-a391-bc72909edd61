# path = require 'path'
# fs = require 'fs'
# #vueServerRenderer = require 'vue-server-renderer/build.js'
# { createBundleRenderer } = require('vue-server-renderer')
# serialize = require 'serialize-javascript'

# Wecard = COLLECTION "chome", 'wecard'
# Forum = COLLECTION "chome", 'forum'
# Wecard.createIndex 'meta.ts':-1
# auth = DEF "_auth_auth"
# config = CONFIG()
# webErrPage = DEF "webErrPage"
# helpers = INCLUDE 'lib.helpers'

# serverBundleFilePath = path.join(config.media.path,'web/packs/bundle.wpnews.js');
# serverBundleFileCode = fs.readFileSync(serverBundleFilePath, 'utf8');
# #bundleRenderer = vueServerRenderer.createBundleRenderer(serverBundleFileCode);
# bundleRenderer = createBundleRenderer(serverBundleFileCode);
# query = {'meta.vc': {$gte:10}, $or: [{tp:'blog'}, {tp: 'topic'}], del: {$ne: true}, noWeb: {$ne: true}}

# APP 'wpnews'

# getTotalCount = (cb) ->
#   Wecard.countDocuments query,(err, count) ->
#     return cb(err, count)

# getLatestNews = (page, limit, cb) ->
#   Wecard.findToArray query, {fields: {seq: 0}, sort: {'meta.ts': -1}, skip: (page-1) * 20, limit: limit}, (err, wecards) ->
#     return cb(err, wecards)

# getRecommendWecards = (cb) ->
#   recommendQuery = Object.assign({}, query, {'meta.ts': {$gte: new Date(Date.now() - 3600000 * 24 * 30)}});
#   Wecard.findToArray recommendQuery, {fields: {seq: 0}, sort: {'meta.vc': -1}, limit: 30}, (err, wecards) ->
#     return cb(err, wecards)

# getLatestForum = (cb) ->
#   Forum.findToArray {'ts': {$gte: new Date(Date.now() - 3600000 * 24 * 30)}}, {fields: {tl: 1, fornm:1, ts:1, thumb:1}, sort: {'ts': -1}, limit: 30}, (err, posts) ->
#     return cb(err, posts)

# GET (req, resp, next)->
#   if helpers.isSearchEngine req
#     return resp.send {err:'not found'}, 404
#   req.setLocale lang if lang = req.param 'lang'
#   limit = 20 #parseInt req.param('limit') or 20
#   page = parseInt req.param('page') or 1
#   context = {webBackEnd:(req.isAllowed 'webBackEnd'), currentPage: page, isCip: req.isChinaIP(), lang:req.locale(), newsAdmin:(req.isAllowed 'newsAdmin'), isVipUser:(req.isAllowed 'vipUser'), host: req.host}

#   auth req,resp,null,(user)->
#     context.isLoggedIn = user?
#     getLatestNews page, limit, (err, wecards) ->
#       if err then return webErrPage req, resp, err
#       context.wecards = wecards || []
#       getTotalCount (err, count) ->
#         if err then return webErrPage req, resp, err
#         context.count = count
#         context.totalPage = Math.ceil(count / limit)
#         getLatestForum (err, posts) ->
#           if err then return webErrPage req, resp, err
#           context.posts = posts || []
#           initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"
#           bundleRenderer.renderToString context, (err, html) ->
#             if err then return webErrPage req, resp, err
#             ctx = { html:html, initialState: initialState}
#             ctx.title = "#{req.l10n('Latest Real Estate & Property Market News','wpnews')} | #{req.l10n "Realmaster"}"
#             ctx.author = req.l10n('Realmaster Technology Inc.')
#             ctx.description = req.l10n('Realmaster.com delivers the latest news of Canada Real Estate & Property Market ','wpnews')
#             ctx.lang = req.locale()
#             ctx.url = "http://#{req.host}/wpnews"
#             ctx.images = ['http://'+req.host+'/web/imgs/logo2.png']
#             resp.ckup 'wpnews',ctx,'_', {noref:1, keywords: 1,noindex:true, noAngular:1, noAppcss:1, noRatchetcss:1, viewport:1, nosmbcss:1, nofontcss:1, noSidebarcss:1,side:0,jquery:1,bootstrap:1,newHeaderFooter:1,menu:'wpnews'}
# VIEW 'wpnews', ->
#   text @html
#   text @initialState
#   js '/js/rmapp.min.js'
#   # css '/web/css/common.css'
#   # css '/web/css/wecards.css'
#   # css '/web/css/bootstrap.min.css'
#   # js '/js/jquery-2.1.1.min.js'
#   js '/web/packs/commons.js'
#   js '/web/packs/wpnews.js'
#   # js '/web/packs/bootstrap.min.js'
