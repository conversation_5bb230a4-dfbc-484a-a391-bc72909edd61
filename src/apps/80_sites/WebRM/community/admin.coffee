UserModel = MODEL 'User'
Boundary = MODEL 'Boundary'
boundaryHelper = INCLUDE 'libapp.boundaryHelper'
mapServer =  INCLUDE 'lib.mapServer'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{respError} = INCLUDE 'libapp.responseHelper'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req,user}) ->
  if req.isAllowed('devGroup', user)
    return {name: 'Generate Community Image', url: '/cmty/cmtyStaticMap', index: 1, isIfrm: 0}
  return null
MenuRegistryModel.registerMenu('generCommuImg', getMenu)

APP 'cmty'

# /cmty/cmtyStaticMap
GET 'cmtyStaticMap', (req,resp)->
  id = req.param 'id'
  redirectUrl = '/1.5/index'
  UserModel.appAuth {req,resp,url:redirectUrl},(user)->
    return resp.redirect redirectUrl unless (user and req.isAllowed 'admin')
    isApp = req.getDevType() is 'app'
    mapboxkey = mapServer.getMapboxAPIKey {isApp}
    Boundary.findOneByName {id}, (err, cmty)->
      resp.ckup 'admin/cmtyStaticMap',{cmty,jsonCmty:encodeObj2JsonString(cmty)\
      ,id},'_',{mapboxkey}

POST 'cmtyStaticMap',(req,resp)->
  UserModel.appAuth {req,resp,''},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'admin'
    {bbox,zoom,id,skip,city,prov,targetCol} = req.body
    Boundary.findCmtyBnds id,(err,boundary)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.INTERNAL_ERROR), resp,sysErr:err} if err
      boundary ?= {}
      boundary.bbox = bbox
      boundary.zoom = zoom
      boundaryHelper.downloadCmtyImages boundary,(boundaryImageData)->
        Boundary.saveCmtyImageInfo boundaryImageData,(err,ret)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.INTERNAL_ERROR), resp,sysErr:err} if err
          return resp.send {ok:1} unless skip
          Boundary.findOneByName {skip,city,prov,targetCol}, (err, cmty)->
            return respError {clientMsg:req.l10n(MSG_STRINGS.INTERNAL_ERROR), resp,sysErr:err} if err
            resp.send {ok:1,skip,cmty}