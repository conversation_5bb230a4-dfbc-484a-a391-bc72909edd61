debug = DEBUG()
PreConstruction = COLLECTION 'chome','pre_constrct'
helpers = INCLUDE 'lib.helpers'
{genWebUrl} = INCLUDE 'libapp.project'
{filterJSONSpecialChar} = INCLUDE 'lib.helpers_string'
{sprintf,vsprintf}  = INCLUDE 'lib.sprintf'

UserModel = MODEL 'User'
getProjectLists = DEF "getProjectLists"
getProjOpt = DEF 'getProjOpt'
ProvAndCity = MODEL 'ProvAndCity'
WecardModel = MODEL 'Wecard'
ProjectModel = MODEL 'Project'
PropertiesModel = MODEL 'Properties'
StatModel = MODEL 'Stat'

ADDSTDFN 'getPreConstructionCondos',{needReq:true},(opt,cb)->
  page= 0
  condos={}
  projOpt =
    mode:'new',
    city:opt.city,
    # prov:opt.prov,
    tp1:'All',
    req:opt.req
  if nm = opt.nm
    regNm = helpers.str2reg(nm,true)
    projOpt.nm = regNm
  if opt.page then projOpt.page = Number(opt.page)-1
  if opt.tp
    projOpt.tp1 = opt.tp
    projOpt.tp1Name = "Type"
  if opt.mode
    projOpt.mode = opt.mode
  getProjectLists projOpt, (err, list)->
    if err
      console.error err
      return cb err
    cb null,list

# MSG_STRINGS = DEF 'MSG_STRINGS'
# ADDSTDFN 'fuzzyQueryCondosName',(opt,cb)->
#   unless opt.nm?.length > 2
#     return cb MSG_STRINGS.BAD_PARAMETER
#   select = $or:[{nm:{$regex: opt.nm , $options:'i'}},{nm_en:{$regex: opt.nm, $options:'i'}}]
#   PreConstruction.findToArray select,{nm:1,nm_en:1,_id:1,img:1,prov:1,city:1},(err,project)->
#     if err
#       console.error err
#       return cb MSG_STRINGS.DB_ERROR
#     # deal with error
#     cb null,project


globalCityList=[]
_new_condos_prov_city_list="new_condos_prov_city_list"
PLSWAIT _new_condos_prov_city_list
# order ?
fields = {prov: "$prov" ,city: "$city", status: 'A' }
PreConstruction.aggregate [{$group : {_id : fields, count: { $sum: 1 } }}],(err,results)->
  if err then throw err
  list={}
  for data in results
    city = {}
    pr = ''
    city.city = data._id.city
    city.count = data.count
    pr = ProvAndCity.getProvAbbrName data._id.prov
    provFull = ProvAndCity.getProvFullName data._id.prov
    # if data._id.prov is "Ontario" then pr = "ON"
    # else if data._id.prov is "British Columbia" then pr = "BC"
    # else if data._id.prov is "Alberta" then pr = "AL"
    city.url = city.city?.split(" ").join("-")+"-"+ pr
    list[data._id.prov] ?= {prov: data._id.prov, provFull:provFull, p_ab:pr, count:0, city:[]}
    list[data._id.prov].city.push(city)
    list[data._id.prov].count += Number(data.count)
  for citys of list
    list[citys].city.sort((a,b)->
      s = a.count
      t = b.count
      if s > t then return -1 else return 1
    )
    globalCityList.push list[citys]
  globalCityList.sort((a,b)->
    s = a.count
    t = b.count
    if s > t then return -1 else return 1
  )
  PROVIDE _new_condos_prov_city_list


APP 'new-condos'
setIfEn = (lang,proj) ->
  if lang == 'en'
    flds = ['nm','desc','logo','builder','amen','html','mfee_m','tax_m','deposit_m','spuNm']
    for  fld in flds
      proj[fld] = proj[fld+'_en'] or proj[fld]
    if proj.agentsWeb
      for agent in proj.agentsWeb
        agent.cpny = agent.cpny_en || agent.cpny_zh

  return proj

pagingStatus = (paging)->
  if paging.page is undefined or Number(paging.page) is 1
    paging.prev = false
    paging.page = 1
    if paging.length < 60
      paging.next = false
    else
      paging.next = Number(paging.page)+1
  else
    paging.prev = paging.page-1
    if paging.length < 60
      paging.next = false
    else
      paging.next = Number(paging.page)+1
  posip = paging.pageUrl.indexOf "page"
  posiQ = paging.pageUrl.indexOf "?"
  if posiQ == -1
      paging.pageUrl = paging.pageUrl+"?page="
  else
    if posip is -1
      paging.pageUrl = paging.pageUrl+"&page="
    else
      page = paging.pageUrl.slice(posip)?.split('=')[1]
      paging.pageUrl = paging.pageUrl.slice(0,posip)
      paging.pageUrl = paging.pageUrl+"page="
  return paging

addUrl = (lang,list,type) ->
  projs = []
  rest = []
  condoList = []
  return [] unless list?.length
  for proj in list
    continue unless proj
    proj.pr = ProvAndCity.getProvAbbrName proj.prov
    setIfEn(lang,proj)
    if proj.topWeb
      projs.push proj
    else
      rest.push proj
    projs = helpers.shuffle projs
    condoList = projs.concat rest
  return condoList

layout=
  title:'Canada Toronto Real Estate - Toronto Homes New Condos - MLS Listings | Realmaster.com'
  year: new Date().getFullYear()
layoutParams = {menu:'newCondos',page:'newCondos'}

# LIST_TITLE = '%s楼花-大牌新盘楼花经纪入驻, VVIP抢先拿房| 房大师找房 | Realmaster.com'
LIST_TITLE = '%s New Condos For Sale | Realmaster.com'
# LIST_DESC = '房大师找房提供%s楼花查询, 热门新楼盘都有大牌楼花经纪入驻，提供VVIP登记，抢先以优惠价拿到满意户型，是华人首选的New Condos网站和APP.'
LIST_DESC = 'Search all New Condos and pre-constructions for Sale in %s. %s\'s Favourite New Condo Website.'
# LIST_KEYWORD = '%s新盘楼花,新盘楼花价格,新盘楼花户型图,豪华公寓,复式公寓,尾盘'
LIST_KEYWORD = '%s New Condos For Sale, %s Lofts, %s Luxury Condos, New Condos in %s'
getMetaData = (req, place)->
  title = filterJSONSpecialChar(sprintf req.l10n(LIST_TITLE,'newcondos'),place)
  description = filterJSONSpecialChar(sprintf req.l10n(LIST_DESC,'newcondos'),place,place)
  keywords = filterJSONSpecialChar(sprintf req.l10n(LIST_KEYWORD,'newcondos'),place,place,place,place)
  {title,description,keywords}

GET (req,resp)->
  crumbs=
    list:[{name:req.l10n('Home','menu'),link:req.fullUrl '/'}]
    last:req.l10n 'New Condos'
  # checkUser req
  layout.subTit= 'Canada New condos'
  {title,description,keywords} = getMetaData req, req.l10n('Canada')
  layoutCtx = {title,description,keywords,crumbs,menu:'newCondos',page: 'cityListCount'}
  resp.ckup 'newcondos/index',{globalCityList,crumbs},'layout',layoutCtx

GET ':cityProv',(req,resp)->
  cityProv=req.param('cityProv',"Toronto-ON")
  if cityProv.indexOf '--' > 0
    ## handle old url Toronto--ON
    cityProv = cityProv.replace('--','-')
  layoutParams.title = req.l10n(layout.title)
  cityProv=cityProv?.split("-")#['Toronto','ON'],['Richmond', 'Hill' , 'ON']
  lastIdx = cityProv.length - 1
  pr = cityProv[lastIdx]
  if pr.length is 2
    # handle Canada prov
    pr = pr.toUpperCase()
  city = cityProv.splice(0,lastIdx).join(' ')
  tp=req.param("tp")
  mode=req.param("mode")
  nm=req.param("nm")
  page=req.param("page")
  prov=""
  prov = ProvAndCity.getProvFullName pr
  cp = city+"-"+pr
  opt = {city,req,rmsrc:'web'}
  if tp then opt.tp1=tp
  if mode then opt.mode=mode
  if nm then opt.nm=nm
  if page then opt.page = Number(page) - 1
  paging=
    pageUrl : req.fullUrl()
    count : 0
    page : page
  selectModeOption =
    new:req.l10n('New Release','Pre-Construction'),
    soon:req.l10n('Complete Soon','Pre-Construction'),
    # u:req.l10n('Unavailable','Pre-Construction')
  cpStr=cp?.split(" ").join("-")
  for prov of globalCityList
    for city in globalCityList[prov].city
      #handle case in url
      if city.url?.toLowerCase() is cpStr?.toLowerCase()
        thisCityInfo = city
        thisCityInfo.pr = pr
        thisCityInfo.mode =
          k: mode
          v: selectModeOption[mode] or selectModeOption.new
        #get standard case of city name, toronto => Toronto
        opt.city = city.city
  return resp.ckup 'webGeneralError', {err_tran:req.l10n('There are no Pre-Construction in the current city')}, 'layout', layoutParams unless thisCityInfo
  # req.callStdFn 'getPreConstructionCondos',opt,(err,list)->
  getProjectLists opt, (err, list)->
    if err
      return  resp.ckup 'generalError',{err},'_',layoutParams
    thisCityInfo?.noCondos = false
    if list?.length is 0
      if tp is undefined and nm is undefined and page is undefined and mode is undefined
        return  resp.ckup 'generalError',{err:"We can not find what you want"},'layout',layoutParams
      else
        thisCityInfo?.noCondos = true
    condoList = addUrl(req.locale(),list)
    paging.length = list.length
    paging = pagingStatus(paging)
    unless cityInfoCity = thisCityInfo?.city
      cityInfoCity = 'Unknown'
      console.error "***DEBUG: No city for URL: #{req.url}"
    crumbs=
      list:[
        {name:req.l10n('Home','menu'),link:req.fullUrl '/'},
        {name:req.l10n('New Condos'),link:req.fullUrl '/new-condos'}
      ]
      last:"#{req.l10n('All')} #{req.l10n(cityInfoCity)}"
    bigTitle = sprintf req.l10n('New Condos in %s','newcondos'),req.l10n(cityInfoCity)
    layout.subTit= 'Canada ' + cityInfoCity + ' New condos'
    condoDesc = sprintf req.l10n("Find your new condos in %s by browsing the list below or using the search keywords.More filters will be avaliable soon.",'newcondos'),req.l10n(cityInfoCity)
    {title,description,keywords} = getMetaData req, req.l10n(cityInfoCity)
    layoutParams.title = title
    layoutParams.description = description
    layoutParams.keywords = keywords
    layoutParams.crumbs = crumbs
    resp.ckup 'newcondos/list',{tp,thisCityInfo,bigTitle,condoList,globalCityList,cp,paging,pr,crumbs,condoDesc,selectModeOption},'layout',layoutParams

GET ':city/:nm',(req,resp)->
  city=req.param('city',"Toronto")
  city=city?.split("-").join(" ")
  nm=req.param 'nm'
  url = req.fullUrl "/new-condos/"
  opt = getProjOpt req
  if id = req.param 'id' # handle no nmOrig/url proj
    opt.id = id
  else
    opt.url = nm
  rmsrc = req.param('rmsrc') || 'web'
  ldmode = req.param 'ldmode'
  if ldmode is '0'
    ldmode = 0
  highlight = req.param 'highlight'
  if highlight
    highlight = if (isNaN(highlight)) then 1 else parseInt(highlight)
  # req.callStdFn 'getPreConstructionDetail',{req,id,nm},(err,proj)->
  UserModel.auth {req,resp},(user)->
    try
      proj = await ProjectModel.getProjectDetails opt, user, PropertiesModel.updateListingStatus
    catch err
      debug.error err
      return resp.ckup 'generalError',{err:"We can not find what you want"},'layout',{}
    unless proj
      return resp.ckup 'generalError',{err:"We can not find what you want"},'layout',{}
    try
      await StatModel.updateStat {tp:'proj',id:proj._id,stat:rmsrc}
    catch err
      debug.error err
      debug.error "StatModel.updateStat error: tp:proj,id:#{proj._id},stat:#{rmsrc}"
    lang = req.locale() # en, zh, zh-cn
    proj.isChinaIP = req.isChinaIP()
    proj.nm = if lang is 'en' then proj.nm_en or proj.nm else proj.nm or proj.nm_en
    proj.nm ?= proj.nmOrig
    data =
      pjid : proj._id
      id : proj._id
    data.img = proj.img?.l?[0]
    if proj.nm then data.projnm=proj.nm
    if proj.nm_en then data.projnm_en=proj.nm_en
    data = JSON.stringify(data)
    if proj.wepage
      try
        result = await WecardModel.injectWecardDetail lang, proj.wepage
      catch err
        debug.error err,' wecardDetail:',req.body
      proj.html = result if result
    setIfEn(lang,proj)
    if proj.amen
      if lang == 'en'
        proj.amenS=proj.amen.split(', ')
      else
        proj.amenS=proj.amen.split('，')
    proj.saleStatus=proj.saleStatus.toUpperCase() if proj.saleStatus
    cityUrl = url+city.split(' ').join('-')+'-'+ProvAndCity.getProvAbbrName proj.prov_en
    proj.cityUrl = cityUrl
    crumbs=
      list:[
        {name:req.l10n('Home','menu'),link:req.fullUrl '/'},
        {name:req.l10n('New Condos'),link:url},
        {name:req.l10n(city),link:cityUrl}
      ]
      last:proj.nmOrig||proj.nm
    # checkUser req
    condoName = proj.nmOrig || proj.nm_en || proj.nm
    projCity = proj.city
    addr = proj.addr + ', ' + projCity
    seoTitle = '%s at %s, %s VVIP Registration, Prices & Plans | New Condos | Realmaster.com'
    # title = sprintf '%s, %s - VVIP 抢先登记,索取价格 & 户型图 | 房大师找房 | Realmaster.com',condoName,projCity
    title = filterJSONSpecialChar(sprintf req.l10n(seoTitle,'newcondos'),condoName,proj.addr,projCity)
    description = filterJSONSpecialChar(proj.desc_en || proj.desc)
    description = filterJSONSpecialChar(proj.desc || proj.desc_en) if lang isnt 'en'
    proj.title = title
    proj.description = description
    proj.webUrl = genWebUrl proj, lang
    layoutParams.title = title
    layoutParams.description = description
    layoutParams.keywords = filterJSONSpecialChar(addr+','+condoName)
    layoutParams.geoRegion = "CA-#{proj.prov}"
    layoutParams.geoPlaceName = "#{addr} #{projCity}"
    layoutParams.geoPosition = "#{proj.lat},#{proj.lng}"
    layoutParams.swiper = true
    layoutParams.proj = proj
    layoutParams.crumbs = crumbs
    layoutParams.ldmode = ldmode #isLandingMode
    resp.ckup 'newcondos/detail',{lang,city,proj,data,crumbs,highlight,ldmode},'layout',layoutParams

# GET 'fuzzyQuery\/',(req,resp)->
#   nm = req.param 'nm'
#   lang = req.locale() # en, zh, zh-cn
#   condoList =[]
#   req.callStdFn 'fuzzyQueryCondosName',{lang,nm},(err,proj)->
#     if err
#       return resp.send {ok:0, err:err}
#     condoList = addUrl(req,proj,"query")
#     return resp.send condoList
