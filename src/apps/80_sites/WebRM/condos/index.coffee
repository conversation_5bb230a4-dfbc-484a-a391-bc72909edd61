###
Building
schema:{
  _id:      (uaddr: CANADA:ONTARIO:VAUGHAN:41 ABBEYWOOD GATE),
  status:   (1:open, 2:draft, 3:finish, 4:not found),
  nm:       building name
  units:    total nuits of building
  stories:  total level of building
  age:      built year, start from 1950,
  desc:     description,
  img: {
    l[]: path only
  },
  flrpln[]:{ floor plan
    nm:      name of the plan
    unit:    unit number
    bdrms:   bedroom counts
    bthrms:  bathroom counts
    sqft:    size of the unit
    price:   price per sqft
    img:     image of the plan
  }
}
###

UserModel = MODEL 'User'
CondoModel = MODEL 'Condo'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{respError} = INCLUDE 'libapp.responseHelper'
{ queryBuilder } = INCLUDE 'libapp.condos'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req, user}) ->
  if req.isAllowed 'condoAdmin'
    return {name: 'Buildings', url: '/buildings', index: 1, isIfrm: 0}
  return null
MenuRegistryModel.registerMenu('Buildings', getMenu)

VIEW 'no-authorized', ->
  text '''
  <div class="bar bar-header absolute"><a id="menu-toggle" href="#menu-toggle" class="btn btn-default glyphicon glyphicon-menu-hamburger"></a></div>
  <div style="padding-top:54px" class="container-fluid">
    <div class="row">
      <div class="col-lg-12">
        <p>Not Authorized</p>
      </div>
    </div>
  </div>
    '''
  coffeejs ->
    document.querySelector('#menu-toggle').onclick = (e) ->
      e.preventDefault()
      document.querySelector('#wrapper').classList.toggle 'toggled'
      return
    null

APP 'buildings'
# view
GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    if not UserModel.accessAllowed('condoAdmin',user)
      return resp.ckup 'no-authorized', {}, '_', \
      {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}
    return resp.ckup 'condos/index', {}, '_', {}

# /buildings/list
POST 'list', (req,resp)->
  if not (req.body)
    return resp.send {ok:0, err: MSG_STRINGS.BAD_PARAMETER}
  CondoModel.findAndCount req.body,(err,result)->
    return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp} if err
    response = Object.assign({ok:1}, result)
    CondoModel.countFinishedCondo (err,count)->
      return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp} if err
      response = Object.assign(response, count) if count
      return resp.send response

GET 'detail/:id', (req,resp)->
  if (not id = req.param 'id')
    return respError {clientMsg: MSG_STRINGS.BAD_PARAMETER, resp:resp}
  try
    result = await CondoModel.findById id
  catch err
    debug.error "id:#{id},error:",err
    return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp} if err
  return respError {clientMsg: MSG_STRINGS.NOT_FOUND, resp:resp} if not result
  return resp.send {ok:1, data:result}

POST 'detail/:id', (req,resp)->
  if (not req.body) or (not id = req.param 'id')
    return resp.send {ok:0, err: MSG_STRINGS.BAD_PARAMETER}
  UserModel.auth {req,resp,url:'/login'},(user)->
    if not UserModel.accessAllowed('condoAdmin',user)
      return resp.ckup 'no-authorized', {}, '_', \
      {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}
    try
      {err,result} = await CondoModel.updateOne id, req.body
    catch error
      debug.error error
      return respError {clientMsg: MSG_STRINGS.INTERNAL_ERROR, resp:resp}
    return respError {clientMsg: MSG_STRINGS.BAD_PARAMETER, resp:resp} if err
    return resp.send {ok:1, data:result}
