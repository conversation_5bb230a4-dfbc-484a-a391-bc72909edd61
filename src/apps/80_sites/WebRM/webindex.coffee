# Properties = MODEL 'Properties'
# # sysdata
# #   {_id:'location',list:[]}
# #   {_id:'type',list:[]}
#
# # ?????? this file not used?
# APP 'xml'
# GET (req,resp)->
#   return resp.send {locations:[],types:[],styles:[]}
#   findError = (err)->
#     resp.send('error')
#   Properties.getXMLresponse {},(err,response)->
#     return findError err if err
#     resp.send response
#
#
# APP 'searchBox'
# GET (req,resp) ->
#   return resp.send {locations:[],types:[],styles:[]}
#   findError = (err)->
#     resp.send('error')
#   HouseList.distinct 'location', (err, items) ->
#     response = {}
#     return findError err if err
#     response.locations = items
#     HouseList.distinct 'type', (err, items) ->
#       return findError err if err
#       response.types = items
#       HouseList.distinct 'style', (err, items) ->
#         return findError err if err
#         response.styles = items
#         resp.send response
