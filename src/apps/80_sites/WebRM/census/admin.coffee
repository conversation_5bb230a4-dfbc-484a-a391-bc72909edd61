UserModel = MODEL 'User'
Census2016 = COLLECTION 'vow','census2016'
mapServer =  INCLUDE 'lib.mapServer'
boundaryHelper = INCLUDE 'libapp.boundaryHelper'
{respError} = INCLUDE 'libapp.responseHelper'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req,user}) ->
  if req.isAllowed('devGroup', user)
    return {name: 'Generate Census Image', url: '/census/generateMapImage', index: 1, isIfrm: 0}
  return null
MenuRegistryModel.registerMenu('genCensusImg', getMenu)

APP 'census'

GET 'generateMapImage',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.redirect '/1.5/index' unless (user and req.isAllowed 'admin')

    isApp = req.getDevType() is 'app'
    mapboxkey = mapServer.getMapboxAPIKey {isApp}

    query = {bnds:{$ne:null}}
    censusId = req.param 'censusId'
    query._id = censusId if censusId

    Census2016.findOne query,{sort:{_mt:-1},skip:0},(err,census)->
      censusId ?=census._id
      resp.ckup 'admin/censusMapImage',{census:encodeObj2JsonString(census or {}),censusId},'_',{mapboxkey}

POST 'generateMapImage',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'admin'
    {bbox,censusId,skip} = req.body
    Census2016.findOneAndUpdate {_id:censusId},{$set:{bbox}},{returnDocument:'after'},(err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.INTERNAL_ERROR), resp,sysErr:err} if err
      census = ret.value
      {mapUrl} = boundaryHelper.buildMapboxMapUrl census,{hasBoundary:1,padding:20}
      fileName = census._id.replace('.','_')
      boundaryHelper.downloadStaticMap mapUrl,fileName,'census',()->
        Census2016.findOne {bnds:{$ne:null}},{sort:{_mt:-1},skip},(err, census)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.INTERNAL_ERROR), resp,sysErr:err} if err
          resp.send {ok:1,census}