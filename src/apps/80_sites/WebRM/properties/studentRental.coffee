getPropList = DEF "getPropList"
UserModel = MODEL 'User'
SchoolModel = MODEL 'School'
config = CONFIG(['serverBase','share'])
debug = DEBUG()
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{ getDispVar,getPropField,getPaging,PROP_PSIZE} = INCLUDE 'libapp.propWeb'
{getSortOptions,getDistances} = INCLUDE 'libapp.studentRental'
{getOauthUrls} = INCLUDE 'libapp.oauth'

GET 'student_rental_web',(req,resp)->
  UserModel.auth {req,resp}, (user)->
    param = {
      ptype:'r',
      saletp:'lease',
      src:'mls',
      psize:PROP_PSIZE,
      sort:'dist-asc',
      dist:500,
      centerLat:43.66090860000001,
      centerLng:-79.39595179999999 #default to uoft downtown campus
    }
    filters = {}
    for filter in ['sort','dist','page','centerLat','centerLng']
      if req.param(filter)
        param[filter] = req.param(filter)
        filters[filter] = req.param(filter)
    try
      param.centerLat = parseFloat param.centerLat
      param.centerLng = parseFloat param.centerLng
    catch err
      debug.error "Fail to parse: ", param.centerLat,param.centerLng
    opt = {
      title:req.l10n('Student Rental Season'),
      propDesc:"#{req.l10n('Quick')}, #{req.l10n('Security')}, #{req.l10n('Only for GTA')}"
      isAssgnmentExclusive:1,
      distances:getDistances(),
      sortOptions:getSortOptions(req)
    }
    getPropList req, resp, user, param,(err,ret)->
      return errorPage err, resp if err
      opt.param = param
      items = ret.items
      opt.propList = getPropField(items,{
        redirect:req.fullUrl(),
        isCip:req.isChinaIP(),
        lang:req.locale(),
        isPad:req.isPad(),
        formPage:'student_rental',
        use3rdPic:config.serverBase?.use3rdPic,
        hostNameCn:config.share?.hostNameCn
      })
      opt.paging = getPaging items.length,'/student_rental_web',filters
      opt.dispVar = getDispVar req
      opt.jsonDispVar = encodeObj2JsonString(opt.dispVar)
      {gAuthUrl,fAuthUrl} = getOauthUrls(req,req.url)
      opt.gAuthUrl = gAuthUrl if gAuthUrl
      opt.fAuthUrl = fAuthUrl if fAuthUrl
      layoutParams = {page: 'properties'}
      layoutParams.propItems = opt.propList
      try
        schoolList = await SchoolModel.getSchools {curSchoolType:'university',prov:'ON'}
      catch err
        return errorPage err, resp
      campusFilter = []
      for school in schoolList
        campusFilter = campusFilter.concat school.campus if school?.campus?.length
      opt.campusFilter = campusFilter
      resp.ckup 'properties/propList',opt,'layout',layoutParams