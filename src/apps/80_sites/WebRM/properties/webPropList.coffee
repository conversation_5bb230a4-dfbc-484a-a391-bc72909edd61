helpers = INCLUDE 'lib.helpers'
helpers_string = INCLUDE 'lib.helpers_string'
{ sprintf }  = INCLUDE 'lib.sprintf'
{ genListingThumbUrlForList,isRMListing,ptypeArray} = INCLUDE 'libapp.properties'
{getDispVar,getPropField,getPaging,PROP_PSIZE} = INCLUDE 'libapp.propWeb'
{getOauthUrls} = INCLUDE 'libapp.oauth'
debug = DEBUG()
{ buildWebCityUrl,
  buildWebCmtyUrl,
  buildWebHistoryUrl,
  FOR_SALE,
  FOR_RENT,
  SOLD_PRICE,
  OPEN_HOUSE,
  LEASE_PRICE,
  ASSIGNMENT,
  EXCLUSIVE_FOR_SALE,
  EXCLUSIVE_FOR_RENT,
  isSaletpValid,
  isSaletpInMyListing,
  TRUSTED_ASSIGNMENT
} = INCLUDE 'libapp.propWeb'
{getProvAbbrName} = INCLUDE 'lib.cityHelper'
{language_list} = INCLUDE 'lib.i18n'
getPropList = DEF "getPropList"
getCpm = DEF 'getCpm'
ProvAndCity = MODEL 'ProvAndCity'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger() #3, 3
HOT_CITIES_LIMIT = 50
config = CONFIG(['serverBase','share'])
UserModel = MODEL 'User'
PropertyModel = MODEL 'Properties'
domFilterValsShortArr = DEF 'domFilterValsShortArr'
webErrPage = DEF 'webErrPage'
# maybe used in future
# domFilterValsShortArr = [
#   {k:'0',v:'Today'}
#   {k:'1',v:'Less than 1 day'},
#   {k:'2',v:'Less than 2 days'},
#   {k:'3',v:'Less than 3 days'},
#   {k:'7',v:'Less than 1 week'}
# ]
# ohFilterValsArr = [
#   {k:'7',v:'Before this weekend'},
#   {k:'14',v:'Before next weekend'},
#   {k:'30',v:'1 month'},
#   {k:'90',v:'3 months'},
#   {k:'180',v:'6 mounths'},
#   {k:'-1',v:'All'},
# ]
# soldFilterValsArr = [
#   {k:'0',v:'Sell today'},
#   {k:'-1',v:'Sold yesterday'},
#   {k:'-7',v:'Sold this week'},
#   {k:'-30',v:'Sold this month'},
#   {k:'-365',v:'Sold in the past year'},
# ]
errorPage = (err, resp) ->
  resp.ckup 'generalError', {err:err}

DEFAULT_PROV = 'ON'

#add s for seo
SOLD_PRICE_SEO = 'Sold Prices'
OPEN_HOUSE_SEO = 'Open Houses'

SALETP_2_SEO_TITLE =
  'for-sale': 'Homes For Sale'
  'for-rent': 'Homes For Rent'
  'sold-price': SOLD_PRICE_SEO
  'open-house': OPEN_HOUSE_SEO
  'assignment': 'Assignment'
  'exclusive-for-sale': 'Exclusive For Sale'
  'exclusive-for-rent': 'Exclusive For Rent'
  'trusted-assignment': 'Trusted Assignment'
SALETP_2_KW =
  'for-sale': 'Asking Price'
  'for-rent': 'Asking Price',
  'sold-price': 'Sold Price'
  'open-house': 'Open House'

SEO_CITY_LIST_TITLE = '%s %s - Search MLS® listings & Real Estate | Realmaster.com'
SEO_PROP_LIST_TITLE = '%s %s - Search %s MLS® listings & Real Estate | Realmaster.com'
SEO_DESC = 'Search all %s %s. View listing photos, review sales history, sold price & sale price estimation, community info & commute guide, and use our advanced real estate filters to find the perfect home.'
#'<加拿大+城市名><风格>,<加拿大+城市名><买房/租房>,<加拿大+城市名><房价/成交价/开放日>,<加拿大+城市名>找房,APP,MLS,CREA,TREB,<加拿大+城市名>地产局,加拿大地产协会'
SEO_KEYWORDS = '%s %s, %s %s, %s %s, %s search homes, APP, MLS, CREA, TREB'

getCurrentMenu = (saletp) ->
  menu = null
  switch saletp
    when FOR_SALE then menu = 'sale'
    when FOR_RENT then menu = 'lease'
    when OPEN_HOUSE then menu = 'sale'
    when TRUSTED_ASSIGNMENT then menu = 'trustedAssign'
  menu

genSEO = (req,saletp,location,page)->
  saleTpSEO = req.l10n (SALETP_2_SEO_TITLE[saletp] or saletp)
  locSEO = location or req.l10n('Canada')
  #keywords
  kwPtype = req.l10n 'Homes'
  kwSaleTp = if saletp is 'for-rent' then req.l10n('Rent','menu') else req.l10n('Buy','menu')
  kwPageTp = req.l10n SALETP_2_KW[saletp],'prop'
  switch page
    when 'citylist' then title = sprintf req.l10n(SEO_CITY_LIST_TITLE),locSEO,saleTpSEO
    when 'proplist' then title = sprintf req.l10n(SEO_PROP_LIST_TITLE),locSEO,saleTpSEO,kwPtype
  description = sprintf req.l10n(SEO_DESC),locSEO,saleTpSEO
  keywords = sprintf req.l10n(SEO_KEYWORDS),location,kwPtype,location,kwSaleTp,location,kwPageTp,location
  layoutParams = {
    title,
    description,
    keywords,
    page: 'properties'
  }

genPageTitle = (req,saletp,location)->
  location = req.l10n('Canada') unless location
  sprintf req.l10n('%s %s','list_title'),location,req.l10n(SALETP_2_SEO_TITLE[saletp] or saletp)

getHotCities = (req,saleTp,limit=50,prov) ->
  # 选择城市页面，会根据不同的省份获取热门城市，房源列表页面获取所有的热门城市
  ProvAndCity.getTranslatedPopularCityList req.locale(),limit,prov

GET ':saleTp',(req,resp,next)->
  cityListPage req,resp,next

GET ':saleTp\/:prov',(req,resp,next)->
  cityListPage req,resp,next

exclusiveAssignmentCityCount = {}
_exclusive_assignment_city_count = 'exclusive_assignment_city_count'
PLSWAIT _exclusive_assignment_city_count
PropertyModel.findCityCountRMListings {}, (err,ret)->
  exclusiveAssignmentCityCount = ret
  PROVIDE _exclusive_assignment_city_count

cityListPage = (req,resp,next)->
  saletp = req.param('saleTp')
  unless isSaletpValid(saletp)
    return next()
  if isSaletpInMyListing saletp
    title = genPageTitle req,saletp
    crumbs =
      list:[{name:req.l10n('Home','menu'),link:"/"}]
      last: title
    ctx = {
      title,
      cityList: exclusiveAssignmentCityCount[saletp] or null,
      crumbs
    }
    layoutCtx = {crumbs,page:'cityListCount'}
    if saletp is TRUSTED_ASSIGNMENT
      layoutCtx.menu = 'trustedAssign'
    return resp.ckup 'properties/cityListCount',ctx,'layout',layoutCtx
  prov = req.param 'prov'
  #如果是http://www.realmaster.com/en/for-sale/Toronto-ON,匹配下一个url,进入房源检索页面
  return next() if prov and (prov.split('-')?.pop()?.length is 2) #
  # handle case if prov is not full name of prov
  
  provNow = prov or DEFAULT_PROV #if prov is empty
  #'British-Columbia' to 'British Columbia'
  provNow = provNow.replace(/\-/g,' ')
  provList = ProvAndCity.getAllTranslatedProvs req.locale()

  if not ProvAndCity.isValidProvince(provNow)
    provNow = DEFAULT_PROV
  if supportedProvs = req.setting.supportedProvs
    provNow = supportedProvs[0]
    provList = provList.filter (prov)->
      return prov.o_ab is provNow

  ctx = {
    buildWebCityUrl,
    buildWebHistoryUrl,
    saletp,
    lang:req.locale(),
    provNow:ProvAndCity.getProvAbbrName(provNow)
  }
  saleTpUp = helpers.strCapitalize saletp.replace(/\-/g,' ')
      
  ctx.provList = Object.assign [],provList

  ctx.cityList = ProvAndCity.getCitiesByProv( \
    req.locale(), \
    {
      provName:ctx.provNow,
      needLoc:req.param('loc')
    })
  ctx.hotCities = getHotCities(req,saletp,HOT_CITIES_LIMIT,ctx.provNow)
  history = req.cookies['cityhist'] or ''
  if history is '' then ctx.history=[] else ctx.history = JSON.parse(history)
  ctx.title = genPageTitle req,saletp
  ctx.crumbs=
    list:[{name:req.l10n('Home','menu'),link:"/"}]
    last: req.l10n saleTpUp,'menu'
  for p in ctx.provList
    p.url = "/#{saletp}/"
    p.url += ProvAndCity.getProvFullName(p.o).replace(/\s/g,'-')
  ctx.cityList = sortArrayWithFirstLetter ctx.cityList,ctx.lang
  layoutParams = genSEO req,saletp,req.l10n(provNow),'citylist'
  layoutParams.menu = getCurrentMenu saletp

  resp.ckup 'properties/cityList',ctx,'layout',layoutParams

#TODO: refactor
#may use in future
#regex group,
# parseCityPrDate = (param) ->
#   arr = param.split '-'
#   len = arr.length
#   obj = {}
#   if (not isNaN(arr[len-1]))
#     if arr[len-1].length isnt 4
#       if (arr[len-1].length is 2) and (arr[len-2].length is 2)
#         obj.day = arr.pop()
#       obj.month = arr.pop()
#     obj.year = arr.pop()
#   obj.pr = arr.pop()
#   obj.city = arr.join(' ').replace(/\./, '-')
#   obj

parseCityProv = (cityProv='')->
  # Whitchurch-Stouffville-ON -> {Whitchurch-Stouffville,ON}
  # Toronto-ON -> {Toronto,ON}
  city = ''
  prov = ''
  if seperatorIndex = cityProv.lastIndexOf('-')
    city = cityProv.substr(0,seperatorIndex)
    prov = cityProv.substr(seperatorIndex+1)
  return {city,prov}

parsePropListUrl = (url,hasCmty) ->
  ###
  url: en/for-sale/Toronto-ON/Agincourt%20North
  url: en/for-sale/Toronto-ON/Willowdale%20East/18%20Spring%20Garden%20Ave
  hasCmty: true
  return {'for-sale','Toronto','ON','Agincourt%20North'}
  ###
  #remove leading and tailing slash
  urlPart = url.replace(/^\/|\/$/g, '').split('?')
  #has ?dom=0&oh=1
  if urlPart.length > 1
    filters = urlPart.pop()
  infoPart = urlPart.pop()
  infoPart = infoPart.replace(/^\/|\/$/g, '').split('/')
  lang = infoPart.shift()
  if language_list[lang]
    saleTp = infoPart.shift()
  else
    saleTp = lang
  {city,prov} = parseCityProv(infoPart.shift())
  debug.debug 'hasCmty',hasCmty,infoPart
  if infoPart.length #has cmty part
    cmty = infoPart.shift()
    cmty = null if cmty is 'NAN'
    if infoPart.length > 0
      addr = infoPart.shift()
  debug.debug 'cmty',cmty,addr
  {lang,saleTp,city,prov,cmty,filters,addr}

renderPropList = (req, resp, next, hasCmty = false) ->
  {lang,saleTp,city,prov,cmty,filters,addr} = parsePropListUrl req.url,hasCmty
  unless isSaletpValid(saleTp)
    return next()
  unless ProvAndCity.isValidCity {city,prov}
    cityNotFound = 'The current city is not found, please click other city!'
    return webErrPage req, resp, req.l10n(cityNotFound), 'origin', false
  if cmty and (not addr) and (not ProvAndCity.isValidCmty {city,prov,cmty})
    return next()
  debug.debug 'query renderPropList'
  domFilterVals = Object.assign [],domFilterValsShortArr
  domFilterVals.push {k:'-720',v:'Off Market 2 year', vv:'Last 2 year',sv:'2y'}
  domFilterVals.push {k:'-1080',v:'Off Market 3 year', vv:'Last 3 year',sv:'3y'}
  UserModel.auth {req,resp}, (user)->
    filterOptions = {
      domFilterValsShortArr:domFilterVals,
      ptypeArray: ptypeArray.slice(0,3)
    }
    opt = {
      webRmInfoDisp:req.getConfig('webRmInfoDisp'),
      saleTp,
      tab: 'all',
      filters: {page:0},
      buildWebCityUrl,
      buildWebCmtyUrl,
      filterOptions
    }
    #city Prov
    opt.cityProv = cityProv = "#{city.split(' ').join('-')}-#{prov}"
    opt.cityNow =
      # BUG: this city could be 토론토/多伦多, 会导致翻译问题
      city : decodeURIComponent city
      #prov : ProvAndCity.getProvFullName prov
      prov: ProvAndCity.getProvAbbrName prov
      pr : prov
      url : "/"+opt.saleTp+"/"+cityProv
    if cmty
      opt.cityNow.cmty = decodeURIComponent(cmty)
      opt.cityNow.url = "/"+opt.saleTp+"/"+cityProv+"/"+cmty
    if addr
      opt.cityNow.addr = decodeURIComponent(addr)

    # code can be use in future
    # TODO: refactor
    # dateUrl = ''
    # opt.dateUrls = []
    # dateBaseUrl = opt.cityNow.url
    # if not year
    #   for i in [0..10]
    #     opt.dateUrls.push {url:"#{dateBaseUrl}-#{2020 - i}",date:2020 - i}
    # else
    #   dateUrl += "-#{year}"
    #   if not month
    #     for i in [1..12]
    #       month = if i < 10 then "0#{i}" else i
    #       opt.dateUrls.push {url:"#{dateBaseUrl}-#{year}-#{month}",date:"#{year}-#{month}"}
    #   else if not day
    #     dateUrl += "-#{month}"
    #     #Day 0 is the last day in the previous month
    #     days = new Date(year, month, 0).getDate()
    #     for i in [1..days]
    #       day = if i < 10 then "0#{i}" else i
    #       opt.dateUrls.push {url:"#{dateBaseUrl}-#{year}-#{month}-#{day}",date:"#{year}-#{month}-#{day}"}
    #   else
    #     showProps = true
    #     dateUrl += "-#{month}-#{day}"
    #     if (saleTp is FOR_SALE)
    #       param.lstd = parseInt("#{year}#{month}#{day}")
    #     else if saleTp is SOLD_PRICE
    #       param.sldd = parseInt("#{year}#{month}#{day}")
    #     else if (saleTp is OPEN_HOUSE)
    #       param.ohDate = "#{year}-#{month}-#{day}"
    #TODO:append address here
    cityProvCmtyUrl = "/#{cityProv}"
    cityProvCmtyUrl += "/#{cmty}" if cmty
    cityProvCmtyUrl += "/#{addr}" if addr
    opt.tabs = [
      { url: "/#{FOR_SALE}#{cityProvCmtyUrl}", nm: 'All', selected: 'all' },
      # { url: "#{FOR_SALE}#{cityProvCmtyUrl}?tab=today", nm: 'New', selected: 'today' },
      { url: "/#{OPEN_HOUSE}#{cityProvCmtyUrl}", nm: 'Open House', selected: 'oh' },
      { url: "/#{SOLD_PRICE}#{cityProvCmtyUrl}", nm: 'Sold Price', selected: 'sold' },
    ]
    if not req.setting.hideRmlisting
      opt.tabs.push { url: "/#{EXCLUSIVE_FOR_SALE}#{cityProvCmtyUrl}", \
      nm: 'Exclusive', selected: 'exclusive' }
      opt.tabs.push { url: "/#{ASSIGNMENT}#{cityProvCmtyUrl}", \
      nm: 'Assignment', selected: 'assignment' }
    
    exclusiveForRentUrl = {
      url: "/#{EXCLUSIVE_FOR_RENT}#{cityProvCmtyUrl}",
      nm: 'Exclusive',
      selected: 'exclusive'
    }
    forRentUrl = "/#{FOR_RENT}#{cityProvCmtyUrl}"
    menu = getCurrentMenu opt.saleTp
    opt.isAssgnmentExclusive = isSaletpInMyListing opt.saleTp
    switch opt.saleTp
      when FOR_SALE
        titleTp = 'MLS&REG; Listings'
        # opt.domFilterValsShortArr = domFilterValsShortArr
      when FOR_RENT
        opt.tabs[0].url = forRentUrl
        # opt.tabs[1].url = "#{FOR_RENT}#{cityProvCmtyUrl}?tab=today"
        titleTp = 'Rent Listings'
        opt.tabs = opt.tabs.splice(0, 1)
        opt.tabs.push exclusiveForRentUrl
      when EXCLUSIVE_FOR_RENT
        opt.tabs[0].url = forRentUrl
        # opt.tabs[1].url = "#{FOR_RENT}#{cityProvCmtyUrl}?tab=today"
        titleTp = 'Rent Listings'
        opt.tab = 'exclusive'
        opt.tabs = opt.tabs.splice(0, 1)
        opt.tabs.push exclusiveForRentUrl
      when OPEN_HOUSE
        titleTp = 'Open House'
        opt.tab = 'oh'
        opt.filters.oh = 7
        # opt.ohFilterValsArr = ohFilterValsArr
      when LEASE_PRICE
        titleTp = 'Lease Listings'
        opt.tab = 'lease'
        opt.filters.dom = 0
      when SOLD_PRICE
        titleTp = 'Sold Listings'
        opt.tab = 'sold'
        opt.filters.dom = -30
      when ASSIGNMENT
        opt.tab = 'assignment'
        # opt.soldFilterValsArr = soldFilterValsArr
      when EXCLUSIVE_FOR_SALE
        opt.tab = 'exclusive'

    #filters -> dom/oh/page
    if filters
      filters = filters.split('&')
      for f in filters
        f = f.split('=')
        opt.filters[f[0]] = f[1] if f[1]
        opt.tab = f[1] if f[0] is 'tab'
      # add default dom if tab is today
      opt.filters.dom = 7 if (opt.tab is 'today') and (not opt.filters.dom)

    #community list
    opt.cmtyList = ProvAndCity.getCmtyByProvAndCity(req.locale(), {provName:opt.cityNow.prov, cityName:opt.cityNow.city})
    opt.cmtyTitle = sprintf req.l10n('Communities in %s'), req.l10n(opt.cityNow.city)

    #title/desc
    saleTpUp = helpers.strCapitalize opt.saleTp.replace(/\-/g,' ')
    titleLocation = opt.cityNow.addr or opt.cityNow.cmty or req.l10n(opt.cityNow.city)
    #todo，titleLocation set to addr if has addr
    opt.title = genPageTitle req,saleTp,titleLocation
    propDesc = 'You are searching homes %s in %s.  You can further refine your search for %s real listing using filters by price range, beds, baths, and property type. E.g., house, condo or land in %s.'
    if opt.saleTp is TRUSTED_ASSIGNMENT
      opt.propDesc = sprintf req.l10n('You are searching %s in %s.'),req.l10n(saleTpUp.toLowerCase()),titleLocation
    else
      opt.propDesc = sprintf req.l10n(propDesc),req.l10n(saleTpUp.toLowerCase()),titleLocation,titleLocation,titleLocation

    #crumbs
    opt.crumbs =
      list:[
        {name:req.l10n('Home','menu'),link:'/'},
        {name:req.l10n(saleTpUp,'menu'),link:'/'+opt.saleTp}
      ]
      last:req.l10n(opt.cityNow.city)
    if opt.cityNow.cmty
      opt.crumbs.list.push {name:req.l10n(opt.cityNow.city),link:"/#{opt.saleTp}/#{cityProv}"}
      opt.crumbs.last = opt.cityNow.cmty
    if opt.cityNow.addr
      opt.crumbs.list.push {name:opt.cityNow.cmty,link:"/#{opt.saleTp}/#{cityProv}/#{opt.cityNow.cmty}"}
      opt.crumbs.last = opt.cityNow.addr
    #TODO:might need to support mulitple provs
    if supportedProvs = req.setting.supportedProvs
      opt.hotCities = getHotCities(req,saleTp,HOT_CITIES_LIMIT, supportedProvs[0])
    else
      #hot city all prov, for SEO
      opt.hotCities = getHotCities req,saleTp

    history = req.cookies['cityhist'] || ""
    history = addHistory history,opt.cityNow
    resp.cookie 'cityhist',history,{path:'/',maxAge:1000*3600*24}
    param = filterSaletp opt.saleTp,opt.cityNow,opt.filters
    
    opt.mapUrl = "/#{opt.saleTp}/#{opt.cityNow.city}/view=map.prov=#{opt.cityNow.pr}.ptype=Residential"
    opt.mapUrl += ".src=#{param.src}"
    if cmty
      opt.mapUrl += ".cmty=#{cmty}"
    for k,v of opt.filters
      opt.mapUrl += ".#{k}=#{v}"
    getPropList req, resp, user, param,(err,ret)->
      return errorPage err, resp if err
      updateParamValues param
      opt.filtersValues = getFilterDisplayValues req,param,domFilterVals
      opt.param = param
      items = ret.items
      opt.cnt = ret.cnt or 0
      opt.propList = getPropField(items,{
        redirect:req.fullUrl(),
        isCip:req.isChinaIP(),
        lang:req.locale(),
        isPad:req.isPad(),
        use3rdPic:config.serverBase?.use3rdPic,
        hostNameCn:config.share?.hostNameCn
      })
      opt.paging = getPaging items.length,opt.cityNow.url,opt.filters
      opt.dispVar = getDispVar req
      opt.jsonDispVar = helpers.encodeObj2JsonString(opt.dispVar)
      {gAuthUrl,fAuthUrl} = getOauthUrls(req,req.url)
      opt.gAuthUrl = gAuthUrl if gAuthUrl
      opt.fAuthUrl = fAuthUrl if fAuthUrl
      layoutParams = genSEO req,saleTp,(opt.cityNow.cmty or req.l10n(opt.cityNow.city)),'proplist'
      layoutParams.propItems = opt.propList
      for p in layoutParams.propItems
        p.addr = helpers_string.filterSpecialCase(p.addr)
      layoutParams.menu = menu
      layoutParams.showBroker = true

      resp.ckup 'properties/propList',opt,'layout',layoutParams

updateParamValues = (param)->
  #update param for filter usage
  {src,ltp,soldOnly} = param
  if src is 'rm'
    if (ltp is 'assignment')
      param.ptype = 'Assignment'
    else if (ltp is 'exlisting')
      param.ptype = 'Exclusive'
    else if (ltp is 'rent')
      param.ptype = 'Exclusive'
  param.saletp = 'Sold' if soldOnly

getFilterDisplayValues = (req, param, domFilterVals)->
  # get filter tags
  getDomFilter = (dom)->
    for d in domFilterVals
      return d.v if dom is d.k
  filterArray = [
    {fld:'ptype',translate:1},
    {fld:'saletp',translate:1},
    {fld:'city',translate:1},
    {fld:'cmty'},
    {fld:'key'} #addr
    {fld:'dom',fn:getDomFilter},
    {fld:'oh',label:'Open House Before'},
    {fld:'bdrms',icon:'fa fa-rmbed'},
    {fld:'bthrms',icon:'fa fa-rmbath'},
    {fld:'gr',icon:'fa fa-rmcar'},
    {fld:'min_lp',label:'min price',fn:helpers.numberReduce},
    {fld:'max_lp',label:'max price',fn:helpers.numberReduce}
  ]
  filtersValues = []
  for f in filterArray
    {fld,label,icon,fn,translate} = f
    if val = param[fld]
      filterValue = {}
      if fn
        val = fn val
      else if translate
        val = req.l10n val
      filterValue.label = req.l10n label if label
      filterValue.icon = icon if icon
      filterValue.val = val
      filtersValues.push filterValue
  return filtersValues

  
#for-sale/Toronto-ON(-year(-mm(-dd)))
GET /(for-sale|for-rent|sold-price|open-house|assignment|trusted-assignment|exclusive-for-(sale|rent)){1}\/[a-zA-Z\%\-\(\)\'\.0-9]+\-[A-Z]{2}(\-[0-9]{4}(\-[0-9]{2})?(\-[0-9]{2})?)?(\?[a-z\&\=0-9])?/,(req,resp,next)->
  debug.debug 'match url city'
  renderPropList req, resp, next

#for-sale/city-pr/c-m-t-y-year-mn-dd?dom=0
GET /(for-sale|for-rent|sold-price|open-house|assignment|exclusive-for-(sale|rent)){1}\/[a-zA-Z\%\-\(\)\'\.0-9]+\-[A-Z]{2}\/[0-9A-Za-z\-]+(\-[0-9]{4}(\-[0-9]{2})?(\-[0-9]{2})?)?(\?[a-z\&\=0-9])?/,(req,resp,next)->
  debug.debug 'match url cmty'
  hasCmty = true
  renderPropList req, resp, next, hasCmty

# statistics 数据统计模块

#判断saletp查询数据库
filterSaletp = (saleTp,cityNow,filters) ->
  {page,saletp,dom,oh,bdrms,bthrms,gr,no_mfee,min_lp,max_lp,max_mfee} = filters
  {city,prov,cmty,addr} = cityNow
  option =
    # mls_only: 1
    label: 1
    src: 'mls'
    ptype: 'Residential'
    # sort: "auto"
    oh: false
    soldOnly: false,
    page: page or 0
    city: city
    prov: prov
    psize: PROP_PSIZE
  switch saleTp
    when FOR_SALE then option.saletp= 'Sale'
    when FOR_RENT then option.saletp= 'Lease'
    when SOLD_PRICE
      option.saletp= 'Sale'
      option.soldOnly= true
    when ASSIGNMENT
      option.src = 'rm'
      option.ltp = ASSIGNMENT
    when EXCLUSIVE_FOR_SALE
      option.src = 'rm'
      option.ltp = 'exlisting'
      option.saletp = 'Sale'
    when EXCLUSIVE_FOR_RENT
      option.src = 'rm'
      option.ltp = 'rent'
      option.saletp = 'Lease'
      option.marketListPage = true
    when TRUSTED_ASSIGNMENT
      option.src = 'rm'
      option.ltp = ASSIGNMENT
      option.rmProp = true
  option.dom = dom+'' if dom or dom is 0
  #用来计算oh的filter，if oh is number，then parse as date filter，
  # filter.oh 只等于7 获取未来7天oh信息
  option.oh = getWeekend oh if oh
  option.cmty = cmty if cmty
  if addr
    cityNow.cnty = MSG_STRINGS.CNTY_CANADA
    option.uaddr = helpers_string.unifyAddress cityNow
  option.bdrms = bdrms if bdrms
  option.bthrms = bthrms if bthrms
  option.gr = gr if gr
  option.min_lp = min_lp if min_lp
  option.max_lp = max_lp if max_lp
  option.no_mfee = no_mfee
  option.max_mfee = max_mfee
  return option

#获取prop需要数据，如图片
# use imported
# getPropField = (list,{isCip,lang,redirect, isPad})->
#   return [] unless list?.length
#   if not initedImages(list[0])
#     genListingThumbUrlForList(list, isCip, isPad,config.serverBase?.use3rdPic, config.share?.hostNameCn)
#   for p in list
#     continue unless p
#     if p.adrltr
#       {nm_en,nm,nm_zh} = p.adrltr
#       if lang is 'en'
#         p.adrltr.nm = nm_en or nm or nm_zh
#       else
#         p.adrltr.nm = nm_zh or nm or nm_en
#     p.isTop = (p.status is 'A') and (new Date(p.topTs) >= new Date())
#     p.lp_price = helpers.currency (p.lp or p.lpr),'$',0
#     p.fullAddress = if p.addr then "#{p.unt or ''} #{p.addr}," else ''
#     p.sp_price = helpers.currency (p.sp),'$',0 if p.sp
#     p.webUrl = "#{p.webUrl}?d=#{redirect}"
#   return list


#按照首字母进行排序，返回带有字母的列表
sortArrayWithFirstLetter =(arr,lang) ->
  data = arr
  map = {}
  firstCharUpper=''
  code = "ABCDEFGHIJKLNMOPQRSTUVWXYZ#"
  for c in code
    map[c] = []
  for item in data
    firstCharUpper = item.o?[0]?.toUpperCase()
    if (map.hasOwnProperty(firstCharUpper))
      map[firstCharUpper].push(item)
    else
      map['#'].push(item)
  # 排序
  for key in map
    if map[key].length isnt 0
      map[key].sort (a, b)->
        return a.o.localeCompare(b.o, 'zh-CN-u-co-pinyin')
  return map

#添加历史记录
addHistory = (hist,cityNow) ->
  if hist is ''
    hist = []
  else
    try
      hist = JSON.parse(hist)
    catch error
      hist = []
  hasCity = -1
  hist.forEach (elem,i)->
    if cityNow.cmty and elem?.cmty is cityNow.cmty
      #if elem and elem.cmty is cityNow.cmty
      hasCity = i
    else if elem?.city is cityNow.city
      hasCity = i
  if hasCity >-1 then hist.splice hasCity,1
  hist.unshift cityNow
  if hist.length > 10 then hist.length = 10
  return JSON.stringify(hist)

getWeekend = (num) ->
  if num > 0
    now = new Date()
    nowTime = now.getTime()
    day = now.getDay()
    oneDayTime = 24*60*60*1000
    SundayTime = nowTime + (num-day)*oneDayTime
    return helpers.dateFormat(SundayTime,"YYYY-MM-DD")