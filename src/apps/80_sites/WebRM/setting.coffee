config = CONFIG(['p58'])
UserModel = MODEL 'User'
User = COLLECTION 'chome', 'user'
UsersExtra = COLLECTION 'chome', 'user_extra'
# UserModel = MODEL 'Users'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{respError} = INCLUDE 'libapp.responseHelper'
helpers = INCLUDE 'lib.helpers'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({user}) ->
  if user
    return {name: 'Personal Setting', url: '/setting', index: 1,isIfrm: 0}
  return null
MenuRegistryModel.registerMenu('settings', getMenu)

APP 'setting'

GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    resp.ckup 'setting', {user:user}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

VIEW 'setting',->
  p -> a href:"/setting/profile",-> _ "Edit Profile"
  p -> a href:"/setting/changePwd",-> _ "Change Password"
  p -> a href:"/setting/58connect",-> _ "Connect 58 Account"

GET ':target',(req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    target = req.param 'target'
    switch target
      when 'changePwd'
        url = '/1.5/user/changePwd'
      when 'profile'
        url = '/1.5/settings/editProfile'
      when '58connect'
        return resp.redirect "http://openapi.58.com/oauth2/authorize?client_id=#{config.p58?.wbcid}&response_type=code&redirect_uri=http://#{req.host}/callback/58"
      when '58connected'
        return resp.ckup "58connected", {user:user}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}
      else
        url = '/1.5/settings/editProfile'
    resp.ckup 'iframe', {url:url}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

VIEW 'iframe',->
  iframe src:@url, width:'700px', height:'700px', onload:"this.height=document.body.offsetHeight;"

VIEW '58connected',->
  p -> _ "58 Connected"
  p -> JSON.stringify @user.wbprof

gError = (resp, err)->
  return resp.ckup 'generalError', {err: err.toString()}, '_', {keywords: 1, description: 1, author: 1, noAngular:1, noAppcss:1, viewport:1, side:0}

# #TODO: @julie
# GET 'unsub', (req,resp)->
#   id = req.param 'i'
#   req.setLocale lang if lang = req.param 'lang'
#   col = if req.param('exuser')=="true" then UsersExtra else User
#   return gError(resp, req.l10n('Bad Parameter')) unless id
#   col.findOne {_id:id},(err,user)->
#     return gError(resp, req.l10n('No user')) unless user
#     resp.ckup 'unsubscription',{title:req.l10n(" Subscription"),exuser: req.param 'exuser'},'_',{}

# VIEW 'unsubscription', ->
#   div id: "unsubscribe", ->
#     div id: "navbar", ->
#       text """<rednavbar></rednavbar>"""
#     div class: "container", ->
#       text """<unsubscribe-page></unsubscribe-page>"""
#     div id: "navbar", ->
#       text """<footermultiline></footermultiline>"""
#   coffeejs {vars:{exuser:@exuser}}, ->
#     null
#   css '/web/css/common.css'
#   js '/web/packs/commons.js'
#   js '/web/packs/unsubscribe.js'
#   css '/web/css/bootstrap.min.css'
#   js '/js/jquery-2.1.1.min.js'
#   js '/web/packs/bootstrap.min.js'


notificationOption = UserModel.getNotificationOption()

renderNotificationPage = (req,resp, {user,code,lang})->
  option = notificationOption.email.flds
  emailStatus = helpers.deepCopyObject notificationOption.email.emailStatus
  flds={}
  savedSearchSrc = null
  UserModel.savedSearchCount {user},(err,ret)->
    debug.error err if err
    savedActCount = ret.act
    for i in option
      if i.tag #tag:'edmNoStat'
        i.status = user[i.tag] or 0
        flds[i.tag] = i.status
        if helpers.isNumber(i.number)
          i.number = savedActCount
      #需要addpend i 和lang to src。
      if i.src
        if code
          i.src = i.src + '&i='+code
        if lang
          i.src = i.src + '&lang='+lang

    for k,status of emailStatus
      status.word = req.l10n(status.word)
      status.description = req.l10n(status.description)
    try
      emailStatusResult = await UserModel.getEmailStatus user._id
      curStatus = emailStatusResult.status
      showBackdrop = emailStatusResult.showBackdrop
    catch err
      debug.error err
      return resp.ckup 'generalError',{err:err}
    cfg =
      user:user
      flds:flds
      option: option
      curStatus: curStatus
      showBackdrop: showBackdrop
      emailStatus:emailStatus
      isRealtor: req.hasRole 'realtor',user
      i:code
      isDev:req.isAllowed 'devGroup'
    resp.ckup 'subscription',cfg,'layout',{angular:1,ratchet:1}


redirectLogin = DEF 'redirectLogin'

#TODO: @julie,
# edm 过来的link有{uid,ts}加密。如果link过期一周不能使用
GET 'notificationEml',(req,resp)->
  #eml用户redirect到login界面, extra user没有login界面。
  req.setLocale lang if lang = req.param 'lang'
  error=()->
    redirectLogin {resp,state:'/setting/notificationEml',lang,isWeb:true}

  UserModel.appAuth {req,resp},(user)->
    if user
      UserModel.getEdmSettingsByUserId user._id,(err,user)->
        return error() unless user
        return renderNotificationPage req, resp, {user}
    else if i = req.param 'i'
      {err,uid,ts} = UserModel.decodeUserFromParam i
      return error() unless uid
      if (exuser = req.param('exuser')) and (exuser isnt 'undefined')
        UsersExtra.findOne {_id:uid},(err,user)->
          user.exuser = 1
          return error() unless user
          return renderNotificationPage req, resp, {user,code:i,lang}
      else
        UserModel.getEdmSettingsByUserId uid,(err,user)->
          return error() unless user
          return renderNotificationPage req, resp, {user,code:i,lang}
    else
      return error() unless user

VIEW 'subscription', ->
  div ngApp:'appProfile',ngController:'ctrlNotifications', class:'ng-cloak',->

    div class: 'container',id:'subscription', ->
      div class:'field-header' , style:'',->
        div class:'field-name',style:'width:60%;font-size: 14px;font-weight: normal;padding-top: 10px;', ->
          div -> text _ 'Subscriptions'
          div class:'explain',->
            text '{{emailStatus.description}}'

        div class:'field-input',ngClass:'emailStatus.action',ngClick:"emailAction()", ->
          span ngClass:'emailStatus.cls',ngShow:'(emailStatus.action == "blocked") || (emailStatus.action == "verify")' ,ngStyle:"{color:emailStatus.color}",->
          span style:'padding-left: 5px;',->
            text '{{emailStatus.word}}'

        # span ngClick:'turnOffAll()',style:'float: right;color: #5CB85C;font-weight: bold;font-size: 13px;padding:0 10px;',->
        #   text _ 'TURN OFF ALL'
      div class:'fields',->
        divParam2 = class:'backdrop', ngShow:"showBackdrop"
        div divParam2,->
        for n,i in @option
          div class:'row',ngIf:"('#{n.agent}' == 'false' || ('#{n.agent}' == 'true' &&  #{@isRealtor})) || ('#{n.devGroup}' == 'true' &&  #{@isDev})",->
            div class:'backdrop',ngShow:"(formData.edmNo == 1) && ('#{n.tag}' != 'edmNo')"
            div class:'field-name', ->
              label ngClass:'',->
                span text _ n.title
                span class:'',ngIf:"'#{n.number}' != 'undefined'",->
                  span ngIf:"formData['#{n.tag}'] == 1",->
                    text '('+0+')'
                  span ngIf:"formData['#{n.tag}']!= 1",->
                    text '('+n.number+')'
                div class:'explain',->
                  text _ n.description
                div ngIf:"'#{n.src}' != 'undefined'",\
                    ngClick:"linkTo('#{n.src}')",style:'color: #5CB85C;font-weight: bold;font-size: 13px;padding-top:10px;',->
                  text _ 'CUSTOMIZE'
            div class:'field-input', ->
              div class:'toggle pull-right', href:"#" ,ngClass:"{'active' : formData['#{n.tag}'] != 1 }", ngIf:"'#{n.tag}' != 'undefined'",ngSrc:"#{n.tag}",\
                  ngClick:"setBool('#{n.tag}', formData['#{n.tag}'])",->
                div class:'toggle-handle'
              #for saved search， click link
              # div class:'icon icon-right-nav',ngIf:"'#{n.tag}'=='undefined'",ngClick:"linkTo('#{@savedSearchSrc}')",style:'color: #999; font-size: 16px; padding: 10px; padding-right: 0; width: 100%;'

  #IOS show loading
  coffeejs {
    vars:{
      fld:@flds,
      option:@option,
      isRealtor:@isRealtor,
      exuser:@user.exuser,
      emailStatus:@emailStatus
      curStatus: @curStatus
      showBackdrop: @showBackdrop
      i:@i
    }},->
      null
  js '/js/subscription.min.js'
  css '/web/css/common.css'

#web subscritpion
# subscription = DEF 'subscription'
# '/setting/subscription'
POST 'subscription', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    done = (user) ->
      UserModel.updateSubscription {isExuser:req.param('exuser'),body:req.body,id:user._id},(err,nUser)->
        if err then return resp.send {ok:0, err:err.toString()}
        resp.send {ok:1}
    if user
      return done(user)
    if i = req.param 'i'
      {err,uid,ts} = UserModel.decodeUserFromParam i
      return respError {clientMsg:req.l10n(MSG_STRINGS.USER_NOT_FOUND), resp} unless uid
      user = {_id:uid}
      return done(user)
    respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}