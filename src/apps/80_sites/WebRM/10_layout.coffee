# helpers = INCLUDE 'lib.helpers'
# request = INCLUDE 'request'
config = CONFIG(['share'])
gShareHostNameCn = config.share?.hostNameCn
# wpHeaderFooterbyDomain = {}

# getWPHeaderFooter = (source,opt,cb)->
#   curDomain = source.host
#   if (opt.forceGetNew is false and ret = wpHeaderFooterbyDomain[curDomain])
#     return cb ret
#   request curDomain, (err, response, body) ->
#     ##helpers.http_get curDomain,{encode: 'utf-8', timeout: 10000},(err,body) ->
#     if err
#       return cb err
#     else
#       header = body.match(/<header id="masthead"[^>]+?>([^$]+?)<\/header>/g)
#       footer = body.match(/<footer id="sidebar-footer"[^>]+?>([^$]+?)<\/footer>/g)
#       customStyle = body.match(/<style id="sydney-style-inline-css"[^>]+?>([^$]+?)<\/style>/g)
#       wpHeaderFooter = {h: '', f: ''}
#       if header
#         wpHeaderFooter.h = header[0]
#       if footer
#         wpHeaderFooter.f = footer[0]
#       if customStyle
#         wpHeaderFooter.s = customStyle[0]
#       wpHeaderFooterbyDomain[curDomain] = wpHeaderFooter
#       return cb wpHeaderFooterbyDomain

# if config.wpBackEndDomain
#   WP_RESOURCE = '_wp_resource'
#   PLSWAIT WP_RESOURCE
#   hfReady = false
#   autoGetWPHeaderFooter = (forceGetNew) ->
#     getWPHeaderFooter {host:config.wpBackEndDomain},{forceGetNew: forceGetNew},->
#       PROVIDE WP_RESOURCE unless hfReady
#       hfReady = true
#   autoGetWPHeaderFooter(false)
#   setInterval autoGetWPHeaderFooter, 5*60000, true
# else
#   console.log "*** No config config.wpBackEndDomain ***"



# ADDPLUGIN 'getWPHF',->
#   wpHeaderFooterbyDomain[config.wpBackEndDomain] or {h:"",f:""}

# ADDPLUGIN 'getDomain',->
#   {en:config.wwwDomainNoWp,zh:config.wwwDomain}

# NOTE: layout not used, use WebRMNoWp 10_layout
LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:'referrer', content:'no-referrer' if @noref
      meta name:'X-UA-Compatible', content:'IE=edge'
      meta name:'HandheldFriendly', content:'true'
      meta name:'robots', content:'noodp' unless @noindex
      meta name:'robots', content:'noindex' if @noindex
      g4tags js:new Date()
      meta itemprop:'description', content: @vctx.description if @vctx.description
      meta itemprop:'inLanguage', content: @vctx.lang if @vctx.lang
      meta itemprop:'articleSection',content: @vctx.section if @vctx.section
      meta name:'viewport', content:'width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0' unless @noviewport
      meta name:'apple-mobile-web-app-capable', content:'yes'
      meta name:'apple-mobile-web-app-status-bar-style', content:'black-translucent'
      meta name:'format-detection', content:'telephone=no'
      meta name:'keywords', content: @vctx.keywords if @vctx.keywords
      meta name:'description', content: @vctx.description if @vctx.description
      meta name:'author', content: @vctx.author if @vctx.author
      meta name:'geo.region', content: @vctx.geoRegion if @vctx.geoRegion
      meta name:'geo.placename', content: @vctx.geoPlaceName if @vctx.geoPlaceName
      meta name:'geo.position', content: @vctx.geoPosition if @vctx.geoPosition
      # link rel: 'canonical', itemprop: 'url', href: @vctx.url if @vctx.url
      meta property: 'og:type', content: 'website'
      meta property: 'og:site_name', content: 'RealMaster.com'
      meta property: 'og:title', content: @vctx.title if @vctx.title
      meta property:'og:image', content: @vctx.shareImage or '/img/logo_s.png'
      meta property: 'og:description', content: @vctx.description if @vctx.description
      meta property: 'og:url', content: @vctx.url if @vctx.url
      if @vctx.images
        for img in @vctx.images
          meta property: 'og:image', content: img
          meta property: 'twitter:image', content: img

      meta property: 'twitter:site', content: 'RealMaster.com'
      meta property: 'twitter:title', content: @vctx.title if @vctx.title
      meta property: 'twitter:description', content: @vctx.description if @vctx.description
      meta property: 'twitter:url', content: @vctx.url if @vctx.url
      meta property: 'twitter:creator', content: 'RealMaster'
      link rel:'next', href:@vctx.next if @vctx.next
      link rel:'prev', href:@vctx.prev if @vctx.prev

      title -> @vctx.title or _ 'RealMaster'
      meta name:'description', content:"#{@desc}" if @desc

      ASSETVERSION = '1.2.3'
      appendAsset = (url) ->
        "#{url}?v=#{ASSETVERSION}"
      if @mapboxkey
        text """<script> key= {mapboxKey: '#{@mapboxkey}'};</script>"""
      block 'customHeader', ->
        js '/js/jquery-2.1.1.min.js' if @jquery
        css '/css/smb.min.css' unless @nosmbcss
        js '/js/rmapp.min.js' unless @noRMApp
        js '/js/angular.min.js' unless @noAngular
        css '/css/ratchet.min.css' unless @noRatchetcss
        css '/css/app.min.css' unless @noAppcss
        css '/css/font-awesome.min.css' unless @nofontcss
        css '/css/web/sidebar.min.css' unless @noSidebarcss
        css '/css/bootstrap.min.css' if @bootstrap
        if @req.isChinaIP()
          js 'https://s.realmaster.cn/logger.lib.min.js'
        else
          js 'https://s.realmaster.com/logger.lib.min.js'
        text ckup 'cordovaJs', {wxcfg:@wxcfg, pubOnly:@pubOnly} unless @noCordova

        if @newHeaderFooter
          css '/web/css/common.css' unless @url
          css '/web/css/wecards.css'
          css '/web/css/listpage.css'
          css '/web/css/details.css'
          css 'https://fonts.googleapis.com/css?family=Source+Sans+Pro%3A400%2C400italic%2C600%7CRaleway%3A400%2C500%2C600'
          css appendAsset('/css/web/style-common.css')
          css appendAsset('/fonts/home/<USER>')
          css appendAsset('/css/web/redesign.css')
          css appendAsset('/css/mapbox.css')
          js '/web/packs/qrcode.min.js'
    block 'body', ->
      body ->
        g4body()
        user = @req.session.get('user')
        if @side
          div id:'wrapper', ->
            div id:'sidebar-wrapper', ->
              ul class:'sidebar-nav', ->
                if @menuList
                  for menu in @menuList
                    li class:"#{if menu.class then menu.class else ''}", ->
                      a href:"javascript:;",onclick: "gotoPage('#{menu.isIfrm}','#{menu.url}')", -> 
                        text "#{_ menu.name, menu.ctx or ''}"
                        if menu.isIfrm? and (menu.isIfrm is 0)
                          span class:'fa fa-rmshare',style: 'font-size:11px;margin-left:5px;'
                if user
                  li class:'sidebar-padding-top', ->
                    a href:'/logout', ->
                      text "#{_ 'Logout'}"
                else
                  li class:'sidebar-padding-top', ->
                    a href:'/login', ->
                      text "#{_ 'Login'}"
            div id:'page-content-wrapper', ->
              text @content()
        else
          # wpHF = @req.getWPHF()
          # # domains = @req.getDomain()
          # if wpHF.s
          #   text wpHF.s
          # wpHeader = wpHF.h
          # if @menu is 'Sale'
          #   wpHeader = wpHeader.replace('>买房','class="current-page">买房')
          # if @menu is 'Lease'
          #   wpHeader = wpHeader.replace('>租房','class="current-page">租房')
          # if @menu is 'assignment'
          #   wpHeader = wpHeader.replace('>楼花转让','class="current-page">楼花转让')
          # if @menu is 'exlisting'
          #   wpHeader = wpHeader.replace('>暗盘','class="current-page">暗盘')
          # if @menu is 'projects'
          #   wpHeader = wpHeader.replace('>新盘','class="current-page">新盘')
          # if @menu is 'wpnews'
          #   wpHeader = wpHeader.replace('>博客','class="current-page">博客')
          # if @menu is 'login'
          #   wpHeader = wpHeader.replace('>登录','class="current-page">登录')
          # if @menu is 'register'
          #   wpHeader = wpHeader.replace('>注册','class="current-page">注册')
          # if @pageTitle
          #   wpHeader = wpHeader.replace('<h1 id="page-title"></h1>','<h1 id="page-title">'+@pageTitle+'</h1>')
          # if user
          #   if @req.isAllowed 'vipUser', user
          #     wpHeader = wpHeader.replace('www/register', 'adminindex').replace('注册','VIP后台').replace('www/login', 'logout').replace('登录', '注销')
          #   else
          #     wpHeader = wpHeader.replace('www/register', 'logout').replace('注册', '注销').replace('<a href="/www/login">登录</a>', '')
          # if @newHeaderFooter
          #   text wpHeader
          div class: 'page-container', ->
            # if @newHeaderFooter
            #   text ckup 'propQRcode', {}
            text @content()
          # if @newHeaderFooter
          #   if ['map','list'].indexOf(@url) is -1
          #     text wpHF.f
        # if @newHeaderFooter
        #   js '/web/packs/bootstrap.min.js'
        #   js '/js/home/<USER>'
        #   js '/js/home/<USER>'
        #   js '/js/home/<USER>'
        #   js '/js/home/<USER>'
#UA-75789502-1


VIEW 'webGeneralError', ->
  div id: "errorPage", style:"display:flex;align-items:center;height:calc(100vh - 71px);", ->
    div class: "container", ->
      img src:'/web/imgs/oops.jpg'
      h3 ->
        span ->
          text if @err then _ @err else if @err_tran then @err_tran else  _ 'Page Not Found'
        if @showlogin
          span style: 'font-size: 12x;', ->
            a style: '', href: '/www/login', -> _ 'Login'
      unless @noReturn
        div style:'font-size: 16px;', ->
          # a style: '', href: 'javascript:void(0);', onclick: 'javascript:window.history.back();', -> _ 'Go Back'
          a style: 'display:inline-block;background-color:#dd1700;color:#fff;padding:10px;border-radius:5px;', href: '/', -> _ 'Go To Homepage'
  css '/web/css/common.css'
  css '/web/css/bootstrap.min.css'
  js '/js/jquery-2.1.1.min.js'
  js 'https://www.google.com/recaptcha/api.js'
  js 'https://static.geetest.com/static/tools/gt.js'
  js '/web/packs/commons.js'
  js '/web/packs/errorPage.js'
  js '/web/packs/bootstrap.min.js'


VIEW 'E404', ->
  text ckup 'webGeneralError', {err_tran:__("'%s' is not found on the server %s", @url, @host)}

# type 'NA' not found. 'origin': show origin error to user
webErrPage = (req, resp, err, type, showlogin)->
  if type is 'NA'
    err = req.l10n 'We can not find what you want'
  else if type isnt 'origin'
    # err = req.l10n 'Something is wrong, <NAME_EMAIL> for help'
    err = req.l10n 'Something is wrong, <NAME_EMAIL> for help'
  
  unless err
    err = req.l10n 'The page may have been removed or is temporarily unavailable. Let\'s try something different:'

  return resp.ckup 'webGeneralError', {err_tran:err, showlogin}, 'layout', {}

DEF 'webErrPage', webErrPage
