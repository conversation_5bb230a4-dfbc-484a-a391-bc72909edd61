config = CONFIG(['share','contact','serverBase'])
User = COLLECTION 'chome', 'user'
SendMail = COLLECTION 'chome', 'email_template'
EdmLog = COLLECTION 'chome','edmlog'
helpers = INCLUDE 'lib.helpers'
UsersExtra = COLLECTION 'chome', 'user_extra'

getDotHtml = DEF 'getDotHtml'
UserModel = MODEL 'User'
UserEmailModel = MODEL 'UserEmail'
debug = DEBUG()

edmSendMailhelper = INCLUDE 'libapp.edmSendMailhelper'
EdmLog.createIndex {"ts":-1},{expireAfterSeconds:30*24*60*60} # 30*24*60*60
SHARE_SECRET = config.share?.secret

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req, user}) ->
  if (req.isAllowed 'edmAdmin',user) or (req.isAllowed 'edmApprove',user)
    return {name: 'EDM list', url: '/edm/list', index: 1,isIfrm: 0}
  return null
MenuRegistryModel.registerMenu('edmList', getMenu)

APP 'edm'

header='<table style="background-color:#f5f6f7; width:100%; height:100%;margin:auto; "><tr><td>'

GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    return resp.send 'No auth' unless (req.isAllowed 'edmAdmin') or (req.isAllowed 'edmApprove')
    cfg = {}
    cfg.testEml = UserModel.getEmail user
    done = ()->
      resp.ckup 'edm', cfg, '_', {noAngular:1, noAppcss:1, bootstrap:1}
    if id = req.param 'id'
      SendMail.findOne {_id:id}, (err, ret)->
        if ret
          cfg.tv = ret.tv
          cfg.nm = if req.param('copy') then ret.nm + ' copy' else ret.nm
          cfg.id = id unless req.param 'copy'
          cfg.curLang = ret.curLang
          cfg.grp = ret.grp
          cfg.roles = ret.roles
          cfg.exteml = ret.exteml
          cfg.type = ret.type
          cfg.fromEml = ret.fromEml or config.contact?.defaultEmail
          cfg.engine = ret.engine
          cfg.st = ret.st
          cfg.state = ret.state
          cfg.sentQty = ret.sentQty
          cfg.checkEdmNo = ret.checkEdmNo
          cfg.sortBylts = ret.sortBylts
          cfg.lstNum = ret.lstNum
          getUserRoles cfg.grp, (err, ret)->
            cfg.userRoles = ret
            return done()
        else
          cfg.tv = {}
          return done()
    else
      done()

GET 'list',(req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    return resp.send 'No auth' unless (req.isAllowed 'edmAdmin') or (req.isAllowed 'edmApprove')
    SendMail.findToArray {},{fields:{_id:1, nm:1, ts:1, stts:1, st:1,err:1, endts:1,state:1 ,sentQty:1},sort: {'ts': -1, 'stts': -1}}, (err, ret)->
      return resp.ckup 'edmList', {list:ret, helpers:helpers}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

#TODO:move to userModel.
getUserRoles = (grp, cb)->
  col = if grp == 'roles' then User else UsersExtra

  col.aggregate ([{ $unwind : "$roles" },{$group : {_id : "$roles", count: { $sum: 1 }}} ]), {cursor:{batchSize:0}}, (err, ret) ->
    return cb err if err
    return cb null, ret
POST 'roles',(req, resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    return resp.send 'No auth' unless (req.isAllowed 'edmAdmin') or (req.isAllowed 'edmApprove')
    resp.send {ok:0, err:'no grp select'} unless grp = req.param 'grp'
    resp.send {ok:0, err:'incorrect grp'} unless grp in 'role' or 'dbExtraRole'
    getUserRoles grp, (err, ret)->
      resp.send {ok:0, err:err} if err
      resp.send {ok:1, roles:ret}

LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

validateSendMailTemplate = (tpl)->
  langs = ['default'].concat(LANGUAGE_LIST)
  for lang in langs
    if langTpl = tpl[lang]
      unless langTpl.title
        return false
      unless langTpl.contents
        return false
  return true

POST 'resetSendStauts', (req,resp)->
  UserModel.auth {req,resp},(user)->
    error = (err) ->
      resp.send {ok:0, err:err}
    return error(MSG_STRINGS.NOT_AUTHORIZED) unless user and (req.isAllowed 'edmAdmin') or (req.isAllowed 'edmApprove')
    id = req.param 'id'
    update = {$set:{resetUid:user._id, resetTs: new Date()}, $unset:{state:1,stts:1, endts:1}}
    SendMail.findOneAndUpdate {_id:id},update,{upsert:false,returnDocument:'after'},(err,r)->
      return error err if err
      resp.send {ok:1}

POST 'getSendUserNum',(req, resp)->
  UserModel.auth {req,resp},(user)->
    error = (err) ->
      resp.send {ok:0, err:err}
    # NOTE: edmAdmin与edmApprove可以点击count button
    return error(MSG_STRINGS.NOT_AUTHORIZED) unless user and ((req.isAllowed 'edmApprove') or (req.isAllowed 'edmAdmin'))
    id = req.param 'id'
    return error(MSG_STRINGS.BAD_PARAMETER) unless id

    SendMail.findOne {_id:id}, (err, job)->
      # params =
      #   job: job
        # User: User
        # UsersExtra: UsersExtra
      # edmSendMailhelper.generateSendMailTos params, (err, ret)->
      try
        {emails,query,source} = edmSendMailhelper.buildQueryForSendMailToUsers job
      catch err
        debug.error err,"emailId:#{id}"
        return error err.message
      if emails
        return resp.send {ok:1, cnt: emails.length}
      else
        try
          users = await UserEmailModel.getUsersForEdm {query,source,needProfile:true}
        catch err
          debug.error err,"query:#{query},source:#{source},emailId:#{id}"
          return error err.message
        return resp.send {ok:1, cnt: users.length}

POST (req, resp)->
  UserModel.auth {req,resp},(user)->
    error = (err) ->
      resp.send {ok:0, err:err}
    ready = req.param 'ready'
    if ready
      # NOTE: only ready need edmApprove,other need edmApprove or edmAdmin
      return error(MSG_STRINGS.NOT_AUTHORIZED) unless user and req.isAllowed 'edmApprove'
    else
      return error(MSG_STRINGS.NOT_AUTHORIZED) unless user and ((req.isAllowed 'edmApprove') or (req.isAllowed 'edmAdmin'))

    return error('No data') unless templateVals = req.param 'templateVals'
    return error('Missing Title or Contents') unless validateSendMailTemplate(templateVals)

    done = ()->
      resp.send ret
    #param = req.body # Rain: you can use this one directly
    ret = {ok:1}
    id = req.param 'id'
    nm = req.param 'nm'
    engine = req.param('engine') or 'SES'
    testEml = req.param 'testEml'
    # testEml = UserModel.getEmail user
    fromEml = req.param 'fromEml'
    curLang = req.param 'curLang'
    doSave = (cb) ->
      return cb 'No name' unless nm
      q = {nm:nm}
      update =
        tv: templateVals
        mt: new Date()
        st: req.param 'st'
        engine: engine
        grp: req.param 'grp'
        roles: req.param 'roles'
        exteml: req.param 'exteml'
        type: req.param 'type'
        testEml: testEml
        fromEml: fromEml
        curLang: curLang
        checkEdmNo: req.param 'checkEdmNo'
        sortBylts: req.param 'sortBylts'
      if req.param 'lstNum'
        update.lstNum = parseInt req.param 'lstNum'
        if isNaN update.lstNum
          return cb 'Error: lstNum is not a number'

      update.ts = new Date() unless id
      if id
        q = {_id:id}
        update.nm = nm
      if ready
        update.state = 'ready'
      SendMail.findOne q, (err, exists) ->
        opts = {upsert:true,returnDocument:'after'}
        if exists
          opts.upsert = false
        if exists?.stts or exists?.endts or (exists?.state in ['sending', 'done'])
          return cb 'Err: cant modify once send'
        SendMail.findOneAndUpdate q,{$set:update},opts,(err,r)->
          return cb err if err
          ret.id = r?._id or r?.value?._id
          cb null
    if req.param 'remove'
      return error('no id') unless id
      SendMail.deleteOne {_id:id},(err,r)->
        return error(err) if err
        return done()
      return
    if req.param 'save' and not ready
      doSave (err, dbret)->
        return error(err) if err
        return done()
      return
    # do save, set state to ready
    if testEml and req.param 'test'
      sendMail = SERVICE 'sendMail'
      return error('failed to get send service!') unless sendMail
      langTpl = templateVals.default
      # console.log templateVals
      # console.log curLang
      hosts = 'http://'+config.serverBase?.wwwDomain
      data =
        isExuser: false
        uid : user._id
        hosts: hosts
        lang : user.locale
        eml: testEml
        unsubscribeUrl: hosts+edmSendMailhelper.getUnsubscribeLink {uid:user._id,isExuser:false,locale:user.locale,secret:SHARE_SECRET}
      obj = getDotHtml req, 'edmMailFooter', data
      if obj.err then return error(req.l10n(obj.err))
      test_email_footer = obj.html
      contents = header + (langTpl.contents or '') + test_email_footer
      mail = {
        subject: langTpl.title
        html:    contents
        text:    contents
        from:    fromEml or sendMail[engine + 'From'] or config.contact?.defaultEmail
        to:      testEml
        engine:  engine
        isNeedValidate: true
        eventType: 'EdmTestEmail'
      }
      # unqa =
      #   unique_args: #sendgrid webhook calback jobid
      #     jobid: id or 'testEml'
      # email     = new sendMail.sendGrid.Email(mail)
      # email.setUniqueArgs(unqa)
      #replace
      nmail = sendMail.replaceParam(mail, user)
      # console.log mail
      sendMail.sendMail nmail, (err, sendRet)->
        return error(err.toString()) if err
        console.log sendRet if sendRet
        ret.ret = sendRet
        return done()
      return #done()
    doSave (err, dbret)->
      return error(err) if err
      return done()

error = (resp, err)->
  return resp.ckup 'generalError', {err: err.toString()}, '_', {keywords: 1, description: 1, author: 1, noAngular:1, noAppcss:1, viewport:1, side:0}


GET 'track',(req, resp) ->
  return resp.send {ok:0} unless (uid = req.param 'uid') and (emlid = req.param 'emlid') and (logid = req.param 'logid')
  UserModel.logEdmHistory uid, {emlid,logid}, (err, ret)->
    if err
      debug.error err
    return resp.send {ok:1}

gError = (resp, err)->
  return resp.ckup 'generalError', {err: err.toString()}, '_', {}


VIEW 'edmList', ->
  div class: 'edmList', ->
    div class:'tr', style:'',->
      a href:'/edm', style:'color: green;', -> 'Create new template'

    div class:'tr',->
      div class: 'nm', ->
        text 'Name'
      div class: 'ts', ->
        text 'Create time'
      div class: 'sch', ->
        text 'Scheduled'
      div class: 'ts', ->
        text 'Start time'
      div class: 'ts', ->
        text 'end time'
      div class: 'error', ->
        text 'Error'
      div class: 'state', ->
        text 'State'
      div class: 'quantity', ->
        text 'Quantity'
      div class: 'ts', ->
        text ''

    if @list
      for tpl in @list
        div class: "tr #{tpl.state}",->
          div class: 'nm' , ->
            a href:"/edm?id=#{tpl._id}", ->
              text "#{tpl.nm}"
          div class: 'ts', ->
            if tpl.ts
              text "#{@helpers.dateFormat(tpl.ts)}"
          div class: 'sch', ->
            if tpl.st
              text "#{@helpers.dateFormat(tpl.st)}"
          div class: 'ts', ->
            if tpl.stts
              text "#{@helpers.dateFormat(tpl.stts)}"
          div class: 'ts', ->
            if tpl.endts
              text "#{@helpers.dateFormat(tpl.endts)}"
          div class: 'error' , ->
            text "#{tpl.err or''}"
          div class: 'state' , ->
            text "#{tpl.state or 'Editing'}"
          div class: 'quantity' , ->
            text "#{tpl.sentQty or 0}"
          div class: 'ts' , ->
            a href:"/edm?id=#{tpl._id}&copy=1", style:'color:red; padding-left:20px;',->
              text _ 'make a copy'

  css '/css/web/edm.css'

VIEW 'edm',->
  # css '/css/font-awesome.min.css'
  div id:'vueBody',->
    text '''<edm></edm>'''
  coffeejs {vars:{
    templateVals:@tv,
    nm:@nm,
    id:@id,
    grp:@grp,
    type:@type,
    roles:@roles,
    userRoles: @userRoles,
    state:@state or 'Editing',
    sentQty:@sentQty,
    exteml:@exteml,
    engine:@engine,
    testEml:@testEml,
    fromEml:@fromEml,
    curLang:@curLang,
    checkEdmNo:@checkEdmNo,
    sortBylts:@sortBylts,
    lstNum:@lstNum,
    st:@st}}, ->
      null
  js '/libs/jquery-2.2.3.min.js'
  js '/libs/bootstrap-3.3.6.min.js'
  js '/js/sn2.mi.js'
  css '/css/summernote-bs3.css'
  css '/css/summernote.css'
  js '/js/entry/commons.js'
  js '/js/entry/edm.js'
