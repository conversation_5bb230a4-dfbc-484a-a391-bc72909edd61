# internal search api for sqldb use
# helpers = INCLUDE 'lib.helpers'
# UserListings = COLLECTION 'chome', 'user_listing'
# Users = COLLECTION 'chome', 'user'
# config = CONFIG()
# sqlDBs = INCLUDE 'lib.sqlDBs'
#
# PropertiesLib = INCLUDE 'libapp.properties'

#TODO: move funcs and delete this
# DEF 'generateAddrRegex',generateAddrRegex



# DEF 'setupPropPicUrls',setupPropPicUrls


# searchById =(opt,cb)->
#   input = opt.input?.toUpperCase()
#   opt.qAnd = [{nm:'_id'}]
#   values = [input]
#   # if use mongo
#   mongoQ = { _id: input}
#   queryDB mongoQ, opt,values,cb


#list页显示fav，topagent avt，支持分页。
#autocomplete
# inputSearch = (opt,cb)->
#   return Properties.inputSearch opt,cb
#
# DEF 'inputSearch',inputSearch
