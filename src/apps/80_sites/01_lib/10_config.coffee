###
App wide variables. Read from sysdata or other source periodically

Use getConfig([config-name]) to get the value.
The value may change when program is running, so do not save the value in other.

Do not handle big value yet. DB access lag will create issue for initialization.
###

cfgObject =
  cpm:
    val: 15
    id:'cpmConfig'
    field: 'cpm'
    dbName: 'chome'
    coll: 'sysdata'
  cpmCnt:
    val: 1000
    id:'cpmConfig'
    field: 'cpmCnt'
    dbName: 'chome'
    coll: 'sysdata'
  cpmExp:
    val: 30
    id:'cpmConfig'
    field: 'cpmExp'
    dbName: 'chome'
    coll: 'sysdata'
  useMysql:
    val: 1
    id:'sqlConfig'
    field: 'useMysql'
    dbName: 'chome'
    coll: 'sysdata'
  usePostgresql:
    val: 0
    id:'sqlConfig'
    field: 'usePostgresql'
    dbName: 'chome'
    coll: 'sysdata'
  forumHotThreshold:
    val: 400
    id:'forumConfig'
    field: 'hotThreshold'
    dbName: 'chome'
    coll: 'sysdata'
  webRmInfoDisp:
    val: false
    id:'propConfig'
    field:'webRmInfoDisp'
    dbName: 'chome'
    coll: 'sysdata'
  showSoldPrice:
    val: false
    id:'propConfig'
    field:'showSoldPrice'
    dbName: 'chome'
    coll: 'sysdata'
  # showSoldPriceBtn:
  #   val: false
  #   id:'propConfig'
  #   field:'showSoldPriceBtn'
  #   dbName: 'chome'
  #   coll: 'sysdata'
  transDDF:
    val: false
    id:'propConfig'
    field:'transDDF'
    dbName: 'chome'
    coll: 'sysdata'
  blockUAFields:
    val: 'SM-G900P'
    id:'propConfig'
    field:'blockUAFields'
    dbName: 'chome'
    coll: 'sysdata'
  # showIndexReno:
  #   val: true
  #   id:'indexConfig'
  #   field:'showIndexReno'
  #   dbName: 'chome'
  #   coll: 'sysdata'
  # now use as show paytop switch, if off dont show any one except admin
  isPaytopAll:
    val: false
    id:'propConfig'
    field:'isPaytopAll'
    dbName: 'chome'
    coll: 'sysdata'
  translateClear:
    val: false
    id:'translateConfig'
    field:'clear'
    dbName: 'chome'
    coll: 'sysdata'
  vueCacheExpireDate:
    val: 1582922479034
    id:'translateConfig'
    field: 'vueCacheExpireDate'
    dbName: 'chome'
    coll: 'sysdata'
  visitorUseForm:
    val: false
    id:'contactConfig'
    field:'visitorUseForm'
    dbName: 'chome'
    coll: 'sysdata'
  showForum:
    val: false
    id:'forumConfig'
    field: 'showForum'
    dbName: 'chome'
    coll: 'sysdata'
  rltrTopAd:
    val: false
    id:'propConfig'
    field: 'rltrTopAd'
    dbName: 'chome'
    coll: 'sysdata'
  # DDF_DISPLAY_POLICY:
  #   val:'all'
  #   id:'propConfig'
  #   field: 'ddfShowable'
  #   dbName: 'chome'
  #   coll: 'sysdata'
  topListingLimit:
    val: 5
    id:'propConfig'
    field: 'topListingLimit'
    dbName: 'chome'
    coll: 'sysdata'
  #   val: false
  #   id:'propConfig'}
  #   field: 'showBcPropSp'
  #   coll: 'sysdata'
  #   timeout: 5 # 5min
  showingListLimit:
    val: 5
    id:'showingConfig'
    field: 'showingListLimit'
    dbName: 'chome'
    coll: 'sysdata'
  showingPropsLimit:
    val: 15
    id:'showingConfig'
    field: 'showingPropsLimit'
    dbName: 'chome'
    coll: 'sysdata'
  vipShowingUpcoimgLimit:
    val: 16
    id:'showingConfig'
    field: 'vipShowingUpcoimgLimit'
    dbName: 'chome'
    coll: 'sysdata'
  isntVipShowingUpcoimgLimit:
    val: 3
    id:'showingConfig'
    field: 'isntVipShowingUpcoimgLimit'
    dbName: 'chome'
    coll: 'sysdata'
  maxImageSize:
    val: 12 * 1000 * 1000
    id: 'imageConfig'
    field: 'maxImageSize'
    dbName: 'chome'
    coll: 'sysdata'
  brokerRmrkToVip:
    val: false
    id:'propConfig'
    field: 'brokerRmrkToVip'
    dbName: 'chome'
    coll: 'sysdata'

cfgDBObject = {}
for k,v of cfgObject
  db = (cfgDBObject[v.dbName] ?= {})
  coll = (db[v.coll] ?= {})
  idObj = (coll[v.id] ?= {})
  #idObj.timeout = Math.min((v.timeout or 60),(idObj._timeout or 60))
  idObj.coll ?= v.coll
  idObj.dbName ?= v.dbName
  (idObj.fields ?= {})[v.field] = 1
  values = (idObj.values ?= {})
  fieldObj = (values[v.field] ?= {})
  fieldObj.initVal = v.val
  fieldObj.key = k
  v.dbObject = idObj

util = INCLUDE 'util'
console.log util.inspect cfgDBObject,true,5
CONFIG_RESOURCE = 'DB_Config'
PLSWAIT CONFIG_RESOURCE
toInitialize = true
loadDBValues = ->
  toLoad = done = 0
  for dbName,colls of cfgDBObject
    for coll,idObjs of colls
      for id,idObj of idObjs
        do (dbName,coll,id,idObj)->
          toLoad++
          Coll = COLLECTION(dbName,coll)
          Coll.findOne {_id:id},{fields:idObj.fields},(err,doc)->
            if err
              console.error err
              return
            # console.log "***** #{id}"
            # console.log doc
            if doc?
              for field,fieldObj of idObj.values
                if (newVal = doc[field])? and (newVal isnt cfgObject[fieldObj.key].val)
                  console.log "CONFIG[#{fieldObj.key}] #{cfgObject[fieldObj.key].val} => #{newVal}"
                  cfgObject[fieldObj.key].val = newVal
                # else
                #   console.log "CONFIG No change: #{field} #{fieldObj.key} new:#{newVal} exist:#{cfgObject[fieldObj.key].val}"
            done++
            if toInitialize and (done >= toLoad)
              toInitialize = false
              PROVIDE CONFIG_RESOURCE
  # for key,obj of cfgObject
  #   continue unless obj.coll and obj.field and obj.query
  #   getNewValue obj,key
loadDBValues()
setInterval loadDBValues, 60000

###
setConfig:
  name: key
  val:  new value
  cb:   callback optional
  return oldVal or null
###
setConfig = (name,val,cb)->
  if (cfg = cfgObject[name])?.id
    Coll = COLLECTION cfg.dbName,cfg.coll
    update = {mt:new Date()}
    update[cfg.field] = val
    oldVal = cfg.val
    Coll.updateOne {_id:cfg.id},{$set:update},{upsert:true},(err)->
      if err
        console.error err
      else
        cfg.val = val
      return cb err,oldVal if cb
    return oldVal
  else
    cb new Error("No exists"),null if cb
    return null

DEF 'setConfig',setConfig
DEF 'getConfig',(name)-> cfgObject[name]?.val
DEF 'reloadAllConfig',loadDBValues
