# GOOGLE_API_LINKS = {
#   # staticMapCn:'https://ditu.google.cn/maps/api/staticmap'
#   # staticMap:'https://maps.googleapis.com/maps/api/staticmap'
#   # geocodeCn:'https://ditu.google.cn/maps/api/geocode/json'
#   geocode:'https://maps.googleapis.com/maps/api/geocode/json'
#   # mapsApiCn:'https://ditu.google.cn/maps/api/js'
#   mapsApi:'https://maps.googleapis.com/maps/api/js'
#   autoComplete:'https://maps.googleapis.com/maps/api/place/autocomplete/json'
#   # autoCompleteCn:'http://ditu.googleapis.cn/maps/api/place/autocomplete/json'
#   placeApi:'https://maps.googleapis.com/maps/api/place/details/json'
#   streetViewMeta:'https://maps.googleapis.com/maps/api/streetview/metadata?size=600x352&heading=270',
#   streetView:'https://maps.googleapis.com/maps/api/streetview?size=600x352&heading=270'
# }
# TODO: add auto-complete api
# DEF 'GOOGLE_API_LINKS',GOOGLE_API_LINKS
mapServer =  INCLUDE 'lib.mapServer'
config = CONFIG(['google','contact','here','mapbox','mapquest','abTest'])
if not config.google
  err = 'No google key defined in config'
  console.error err
  throw new Error(err)

mapServer.init(config)
getGoogleAPILink = mapServer.getGoogleAPILink

SysData = COLLECTION 'chome','sysdata'
helpers = INCLUDE 'lib.helpers'
commomLib = INCLUDE 'libapp.common'
libUser = INCLUDE 'libapp.user'
debug = DEBUG()
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
{formatCmntTs,getMonthAndYearArrayForClaim} = INCLUDE 'lib.helpers_date'
# normalizeVer = DEF 'normalizeVer'
# @need eg. '5.8.4'
# if pass(cur >= need) return true
#
# DUAL_HOMEPAGE_ENABLED = appConfig.dualHomepage or false
DUAL_HOMEPAGE_ENABLED = true

verGTE = (need,ver) ->
  req = @
  cookies = req?.cookies or {}
  apsv = ver or cookies['apsv']
  unless apsv
    # debug.error 'Error: verGTE: Missing req or ver, apsv= '+apsv+' cookies='+JSON.stringify(cookies)+'\n'
    return false
  return true if apsv is 'appDebug'
  return commomLib.isNewVersion(apsv, need)

# NOTE: need this inside a VIEW
ADDPLUGIN 'verGTE',verGTE

# {id:'en',link:'/en'+it.urlNoLang,title:'English'},
# {id:'zh-cn',link:'/zh-cn'+it.urlNoLang,title:'简体中文'},
# {id:'zh',link:'/zh'+it.urlNoLang,title:'繁体中文'},
# {id:'kr',link:'/kr'+it.urlNoLang,title:'한국어'}
getLangName = (lang)->
  return 'English' if lang is 'en'
  return '简体中文' if lang is 'zh-cn'
  return '繁體中文' if lang is 'zh'
  return '한국어' if lang is 'kr'
  return lang

# website footer or header setting
ADDPLUGIN 'getWebSiteSetting',(it) ->
  req = @
  it.assetVersion = '2.4.3'
  it.lang = req.locale()
  it.url = req.url
  it.host = req.host
  it.isChinaIP = req.isChinaIP()
  it.urlNoLang = it.url.replace(/\/(zh-cn|en|zh|kr)/,'')
  it.languages = req.setting?.languages or {}
  # langPrefix = req.setting.langPrefix
  it.canonicalUrl = it.canonicalUrl or req.getCanonicalUrl()
  it.webRmInfoDisp = req.getConfig('webRmInfoDisp')
  it.mainMenus = {
    sale:{link:'/for-sale',title:'Buy'},
    lease:{link:'/for-rent',title:'Rent'},
    newCondos:{link:'/new-condos',title:'New Condos'},
    trustedAssign:{link:'/trusted-assignment',title:'Trusted Assignment'},
    forum:{link:'/forum/list',title:'Forum',className:'menu-item-seperator'},
    saves:{},
    register:{link:'/www/register',title:'Sign Up'},
    login:{link:'/www/login',title:'Log In',className:''},
    download:{link:'#',title:'APP Download',\
    className:'menu-item-download menu-item-has-children',submenus:[
      {link:'https://itunes.apple.com/us/app/realmaster/id978214719?ls=1&mt=8',\
      title:'Apple Store',target:'_blank'},
      {link:'https://play.google.com/store/apps/details?id=com.realmaster',\
      title:'Google Play',target:'_blank'}
    ]},
    language:{
      link:'#',title:'Language',icon:'fa fa-language',
      className:'menu-item-lang menu-item-has-children',
      submenus:[]
    }
  }
  for k,v of it.languages
    it.mainMenus.language.submenus.push({id:k,link:'/'+k+it.urlNoLang,title:getLangName(k)})
  if not it.webRmInfoDisp
    delete it.mainMenus.download
    it.mainMenus.forum.link = '/forum/list'
    #it.privacyLink = req.setting.privacyLink
    it.privacyLink = '/terms'
  else
    it.privacyLink = 'https://realmaster.ca/about-us?lang=' \
    + (if it.lang isnt 'en' then 'zh' else 'en')

  if req.setting?.hideForum or it.isChinaIP or (it.lang is 'en') or (req.host.indexOf('.cn') > -1)
    delete it.mainMenus.forum
    it.mainMenus.trustedAssign.className = 'menu-item-seperator'
  
  # @fred 需求，如果是中国IP，不隐藏语言选择
  # @allen
  # 1. 当时更换网站备案主体，为了减少认证的麻烦给英文的去掉了。
  # 2. 两个不同域名但相同的英文网站内容，对于google收录有影响，所以去掉国内的。
  # if it.isChinaIP
  #   delete it.mainMenus.language.submenus[0]
  
  it.user = req.session.get('user')
  if it.user
    if req.isAllowed('vipUser', it.user)
      it.mainMenus.register = {link:'/adminindex',title:'VIP Console'}
    else
      delete it.mainMenus.register
    it.mainMenus.saves = {link:'/saves',title: 'Saved'}
    it.mainMenus.login = {link:'/logout',title:'Logout'}

  it.contact = req.setting.contact
  if it.webRmInfoDisp
    it.contact.eml = '<EMAIL>'# '<EMAIL>'
  it.footerTrademark = req.setting.footerTrademark
  it.brokerName = req.setting.brokerName
  it.brokerContact = req.setting.brokerContact
  it.logoPath = req.setting.logoPath
  if req.setting.reBoard is 'BC'
    it.footerLogoUrl = req.setting.mlsReciprocityPath
  
  it.companyTl = req.l10n(req.setting.companyTl)
  it.companyDesc = req.l10n(req.setting.companyDesc)
  it.title = it.companyTl
  if not it.description
    it.description = it.companyDesc
  
  if it.description
    it.description = it.description.replace(/\n/g, ' ').replace(/[&\/\\#+()~%.'":*?<>{}]/g,'')
  it.isLandingMode = if it.ldmode then 'isLandingMode' else ''
  it.searchPlaceHolder = req.l10n('Enter address, MLS') + '&#174;'
  # the reason for isSafari is because it has bug rendering json-ld, see: https://stackoverflow.com/questions/76995140/safari-throws-when-parsing-json-ld
  it.isSafari = req.isSafari()
  return it
  # isWeChat = req.isWeChat();

# normalize version to float : 2.5.1 => 2.5001
# don't support 2.15 (2 digits in 2ndary) or 2.5.1234 (4 digits in 3rd)
# NOTE: deprecated
# normalizeVer = (appver)->
#   return 0 unless ('string' is typeof appver) and /^\d+(\.\d+)+$/.test(appver) and (v = appver.split '.')
#   appver = parseFloat(v[0] + '.' + v[1])
#   if v[2]
#     third = parseInt(v[2])
#     appver += (third / 10000)
#   appver
# DEF 'normalizeVer',normalizeVer

# appendKeyValueToUrl=(url,k,v)->
#   unless v?
#     return url
#   url += if url.indexOf('?') > 0 then '&' else '?'
#   url += k+'='+v
#   return url

# AIzaSyBqTqwj8qIus9s8R3GmjnBAQwiesyF9du8
# AIzaSyAf3JCbnfA5Eze2DMBBnEm_WmHo1tvsb2E
# GOOGLE_API_KEY = 'AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA'
# GOOGLE_API_KEY_HOUSEMAX = 'AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU'
# GOOGLE_API_KEYS = []
# googleSysdataId = 'googleApiKeyStat'

# initGoogleKeys = ()->
#   SysData.findOne {_id:googleSysdataId}, (err,ret)->
#     if err or not ret
#       console.error err if err
#     else if ret.keys?.length
#       GOOGLE_API_KEYS = ret.keys
# NOTE: google keys round robin not used for now
# initGoogleKeys()
# googleApiKeyStat = {
#   vc:0
#   vcapp:0
#   vcmap:0
#   # vcmapapp:0
#   vcmix:0
#   vcmixapp:0
# }
# updateMapCounter = ()->
#   date = helpers.dateFormat new Date(), 'YYYY_MM_DD'
#   if googleApiKeyStat.vc > 0
#     inc = {}
#     for k,v of googleApiKeyStat
#       if v > 0
#         inc[k] = v
#         inc["stat.#{date}vc"] = v
#     SysData.updateOne {_id:googleSysdataId},{$inc:inc},{w:1},(err)->
#       for k,v of googleApiKeyStat
#         googleApiKeyStat[k] = 0
#       if err then console.error err
# helpers.repeat 60000, ->
#   updateMapCounter()


# getGoogleAPIKey = (req, opts)->
#   unless GOOGLE_API_KEYS.length
#     return GOOGLE_API_KEY
#   googleApiKeyStat.vc += 1
#   isApp = req.getDevType() is 'app'
#   if opts.src is 'map'
#     googleApiKeyStat.vcmap += 1
#   else if opts.src is 'mapmix'
#     googleApiKeyStat.vcmix += 1
#     googleApiKeyStat.vcmixapp += 1 if isApp
#   else
#     googleApiKeyStat['vc'+opts.src] ?= 0
#     googleApiKeyStat['vc'+opts.src] += 1
#   if isApp
#     googleApiKeyStat.vcapp += 1
#   return GOOGLE_API_KEYS[googleApiKeyStat.vc%2]

# getGoogleAPILink = (req,opts={})->
#   if src = req.param 'src'
#     opts.src = src
#   tp = opts.tp or 'error'
#   isCn = opts.cn #bool
#   isKey = opts.key #bool
#   cb = opts.cb
#   lib = opts.lib or 'geometry'
#   lang = opts.lang #en/zh
#   sensor = if opts.sensor then true else false
#   return null unless tp in ['staticMap','geocode','googleapi','autocomplete','placeApi','streetViewMeta','streetView']
#   ret = ''
#   if tp is 'staticMap'
#   #TODO: add key?
#     if isCn
#       ret = GOOGLE_API_LINKS.staticMapCn
#     else
#       ret = GOOGLE_API_LINKS.staticMap
#     ret = appendKeyValueToUrl(ret, 'key', getGoogleAPIKey(req,opts))
#     return ret
#   else if tp is 'geocode'
#     if isCn
#       ret = GOOGLE_API_LINKS.geocodeCn
#     else
#       ret = GOOGLE_API_LINKS.geocode
#     appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY)
#     return ret
#   else if tp is 'autocomplete'
#     ret = GOOGLE_API_LINKS.autoComplete
#     ret = appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY)
#     ret = appendKeyValueToUrl(ret, 'language', 'en')
#     ret = appendKeyValueToUrl(ret, 'components', 'country:ca')
#     return ret
#   else if tp is 'placeApi'
#     ret = GOOGLE_API_LINKS.placeApi
#     ret = appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY)
#     fields = 'address_component,formatted_address,geometry,icon,id,name,photo,place_id'
#     ret = appendKeyValueToUrl(ret, 'fields', fields)
#     return ret
#   else if tp is 'streetViewMeta'
#     ret = GOOGLE_API_LINKS.streetViewMeta
#     ret = appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY)
#     return ret
#   else if tp is 'streetView'
#     ret = GOOGLE_API_LINKS.streetView
#     ret = appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY)
#     return ret
#   else
#     if isCn
#       ret = GOOGLE_API_LINKS.mapsApiCn
#     else
#       ret = GOOGLE_API_LINKS.mapsApi
#     # if opts.src isnt 'map'
#     if opts.key == 'housemax'
#       ret = appendKeyValueToUrl(ret, 'key', GOOGLE_API_KEY_HOUSEMAX)
#     else
#       ret = appendKeyValueToUrl(ret, 'key', getGoogleAPIKey(req,opts))
#     ret = appendKeyValueToUrl(ret,'language',lang)
#     ret = appendKeyValueToUrl(ret,'libraries',lib)
#     ret = appendKeyValueToUrl(ret,'sensor',sensor)
#     ret = appendKeyValueToUrl(ret,'callback',cb) if cb
#     return ret

# DEF 'getGoogleAPILink',getGoogleAPILink
# return according to page
# need .indexOf > 0 so l != object list, v=1 means need vipUser
# NOTE: remove ishare from shares
shareLinkStrategy = (page, req)->
  ret = {l:[],v:[]}
  return ret unless page
  if page in ['listSearch','mapSearch']
    return {l:['vt','blog'], v:[1,1,1,1,1]} #market,'58'
    #NOTE: no longer allow mls->market prop
  else if page in ['wepage']
    return {l:['news'],v:[1,1]}
  else if page is 'dlpage'
    return ret 
  else if page is 'market'
    return ret
  else if page in ['iframe','wecard']
    return ret
  else if page is 'news'
    return {l:['blog'],v:[0]}
  else if page in ['mylisting']
    return {l:['market'],v:[1,1,1]} #'58'
  else
    return ret
# DEF 'shareLinkStrategy',shareLinkStrategy
ADDPLUGIN 'shareLinkStrategy',(page, req)->
  shareLinkStrategy(page, req)


getConfig = DEF 'getConfig'


# NOTE: all (req,user)-> user refers to session user (app_auth req,resp,null,(user)->)
dataMethods =
  #if user logged in
  'isLoggedIn':(req, user)-> user?
  'isChatBlocked':(req, user, cb)->
    UserModel.isChatBlocked {user}, (ret) ->
      blocked = ret[0]
      return cb null,ret[0]
  'edmDetail':(req, user, cb)->
    UserModel.findEdmLastUpdateTs user?._id,(err,edmLog)->
      l10n = (a,b)->req.l10n a,b
      if edmLog?.ts
        edmLog.mlsMt = formatCmntTs(edmLog.ts,l10n)
      return cb null,edmLog
  'isEmailVerified':(req, user, cb)->
    try
      verified = await UserModel.checkVerify user?._id
    catch err
      debug.error err
    return cb null,verified?.emlV
  # user share avt
  'isPaytop':(req, user)->req.isAllowed 'topup'
  'isProdSales':(req,user)->req.isAllowed 'productSales'
  'isDevGroup':(req, user)->req.isAllowed 'devGroup'
  'isProjAdmin':(req, user)->req.isAllowed 'createProject'
  'isCpmAdmin':(req, user)-> req.isAllowed 'cpmAdmin'
  'isTransitAdmin':(req, user)-> req.isAllowed 'transitAdmin'
  'isBoundaryAdmin':(req, user)-> req.isAllowed 'boundaryAdmin'
  'isRealGroup':(req, user,cb)->
    return cb null,false if not user?
    GroupModel.isInGroup {uid:user._id,groupName:':inReal'},(err,ret)->
      return cb null, ret
  'fubAgentId':(req, user)->
    return user?.fubAgentId?
  'isOntarioRealtor':(req, user)->
    return libUser.isOntarioRealtor(req,user)
  'shareAvt':(req, user)-> if user?.avt then user.avt else ("http://" + req.host + "/img/logo.png")
  'shareUID':(req, user)-> req.getShareUID(user)
  'projShareUID':(req, user, cb)->
    req.getProjShareUID user, (err,ret)->
      ret ?= ''
      return cb null, ret
  'lang':(req, user)-> req.locale()
  'rlang':(req, user)->
    rlang = req.session.get('rlang') or []
    rlang = user?.rlang or rlang
    return rlang
  'isApp':(req, user)-> req.getDevType() is 'app'
  'hasPn':(req, user)-> user?.pn?
  'isCip':(req, user)-> return req.isChinaIP()
  'isRealtor':(req, user)-> req.hasRole 'realtor',user
  'isVisitor':(req, user)->
    # used at assignment & exclusive listing.
    return true unless user?
    # to control if show form to us. return true will show form
    return false unless getConfig('visitorUseForm')
    if user.flwng?.length or (user.roles?.indexOf('realtor') >= 0)
      return false
    true
  'isMerchant':(req, user)-> req.hasRole 'merchant',user
  'isAllowedPredict':(req, user)-> req.isAllowed 'predict',user
  'isVipAlliance':(req, user)-> req.hasRole 'vip_alliance',user
  'showForum':(req,user)-> getConfig('showForum')
  'cpm':(req,user)-> getConfig('cpm')
  'newsAdmin':(req, user)-> req.isAllowed 'newsAdmin', user
  'edmAdmin':(req, user)-> req.isAllowed 'edmAdmin', user
  'edmApprove':(req, user)-> req.isAllowed 'edmApprove', user
  'splashAdmin':(req, user)-> req.isAllowed 'splashAdmin', user
  'marketAdmin':(req, user)-> req.isAllowed 'marketAdmin', user
  'forumAdmin':(req, user)-> req.isAllowed 'forumAdmin', user
  'formOrganizer':(req, user)-> req.isAllowed 'formOrganizer', user
  'allowedShareSignProp':(req, user)-> req.isAllowed 'shareSignProp', user
  'allowedPromoteProp':(req, user)-> req.isAllowed('listing2wecard')
  'exMapURL':(req, user)-> req.exMapURL()
  'mapboxKey':(req, user)-> mapServer.getMapboxAPIKey({isApp:req.getDevType() is 'app'})
  #confirm gmapStatic? used in  propDetail
  'gmapStatic':(req, user)-> getGoogleAPILink({tp:'staticMap'})#if req.isChinaIP() then GOOGLE_API_LINKS.staticMapCn else GOOGLE_API_LINKS.staticMap
  # 'gmapGeocode':(req, user)-> getGoogleAPILink({tp:'geocode'})#if req.isChinaIP() then GOOGLE_API_LINKS.geocodeCn else GOOGLE_API_LINKS.geocode
  'autocomplete':(req, user)-> getGoogleAPILink({tp:'autocomplete'})
  'streetViewMeta':(req, user)-> getGoogleAPILink({tp:'streetViewMeta'})
  'streetView':(req, user)-> getGoogleAPILink({tp:'streetView'})
  'jsGmapUrl':(req)-> getGoogleAPILink({tp:'googleapi',isChina:req.isChinaIP()})
  # 'placeApi':(req)-> getGoogleAPILink({googleConfig:GOOGE_CONFIG, tp:'placeApi'})
  'reqHost':(req)-> req.host
  'shareHost':(req)-> req.shareHost()
  'reqUrl':(req)-> req.url
  'isVipRealtor':(req)-> req.isAllowed 'vipRealtor'
  'isTrebUser':(req)-> req.isAllowed 'trebUser'
  'isTrebUser2':(req)-> req.isAllowed 'trebUser2'
  # 'canShowSoldPrice':(req)-> req.isAllowed 'showSoldPrice'
  'hasAid':(req, user)-> user?.aid?
  'projLastQuery':(req, user)->
    return req.session.get('projLastQuery') or {}
  'profileStars':(req, user)->
    return user?.stars or 0
  'noFnLn':(req, user)->
    # TODO: this logic needs update
    return false unless user
    return false unless role = req.verifiedRole()
    if user.nm_zh or user.nm_en
      return false
    return true
  'isVipPlus':(req)-> req.isAllowed 'vipPlus'
  'isVipUser':(req)-> req.isAllowed 'vipUser'
  'canWebComment':(req)-> req.isAllowed 'webComment'
  'isAdmin':(req)-> req.isAllowed 'admin'
  'isCrmAdmin':(req) -> req.isAllowed 'crmAdmin'
  'isPropAdmin':(req)-> req.isAllowed 'propAdmin'
  'webBackEnd':(req)-> req.isAllowed 'webBackEnd'
  'shareLinks':(req, user)->
    page = req.param 'page'#which page/function user is in
    # (roles, page)-> allowed funcs [{t:'58', d:true, h:false}, {t:'market'}]
    return shareLinkStrategy(page)
  'jsCordova':(req,user)->
    # TODO: DRY with getCordovaJs
    appVer = req.getAppVer()
    # appVerNum = req.getAppVerNumber()
    inFrame = req.param 'inFrame'
    if (req.getDevType() is 'app')
      ret = []
      # TODO: remove this support for legacy system
      # TODO: this exists multiple places, DRY
      # if ('app' isnt appVer) and ('appDebug' isnt appVer) and (not req.verGTE('5.0.0')) and not inFrame# simulate app from browser, use this key
      #   if req.isIOS()
      #     if req.verGTE '3.2.0'
      #       ret.push '/js/ios32.min.js' #'/js/ios32/cordova.js'
      #     else
      #       ret.push '/js/ios.min.js' # '/js/ios/cordova.js'
      #   else if req.isAndroid()
      #     ret.push '/js/android.min.js' # '/js/android/cordova.js'
      if req.verGTE '5.7.0'
        ret.push '/js/rmsrv5wk.min.js'
      else if req.verGTE '5.0.0'
        ret.push '/js/rmsrv5.min.js'
      else
        ret.push '/js/rmsrv.min.js'
      return ret
    else
      return ['/js/srv.min.js']
  'jsWechat':(req,user)->
    if req.isWeChat()
      return ['/js/wx/jweixin-1.6.0.js', '/js/wechatsrv.min.js']
    []
  'defaultEmail':()->
    # 默认接收邮箱,contact us
    return config.contact?.defaultReciveEmail or '<EMAIL>'
  'defaultSendEmail':()->
    # 默认发送邮箱, use for edm
    return config.contact?.defaultEmail or '<EMAIL>'
  'isStigmaAdmin':(req, user)-> req.isAllowed 'stigmaAdmin'
  'isGeoAdmin':(req, user)-> req.isAllowed 'geoAdmin'
  'appmode':(req)->req.cookies[libUser.APPMODE]
  # TODO: change this to true when [dual_homepage] onlined
  'hideDownloadAppInWeb': (req)-> req.setting.hideDownloadAppInWeb
  'dualHomepage':(req, user)-> DUAL_HOMEPAGE_ENABLED
  'isAgreementAdmin':(req, user)-> req.isAllowed 'agreementAdmin'
  'isNoteAdmin':(req,user)-> req.isAllowed 'noteAdmin'
  'isInrealAdmin':(req)-> req.isAllowed 'inrealAdmin'
  'isAssignAdmin':(req)-> req.isAllowed 'assignAdmin'
  'isShowingAdmin':(req)-> req.isAllowed 'showingAdmin'
  'isClaimAdmin':(req, user)-> req.isAllowed 'claimAdmin'
  'userRoles':(req, user)->
    if not user
      return 'guest'
    else if req.hasRole('realtor',user)
      return 'realtor'
    else
      return 'user'
  'homePageABTest':()->
    return config.abTest?.homepage or false
  'isTokenAdmin':(req,user)-> req.isAllowed 'tokenAdmin'
  'isEditorAdmin':(req,user)-> req.isAllowed 'editorAdmin'
  'monthAndYearArray':()-> getMonthAndYearArrayForClaim()
  'isWaitingVerifyRealtor':(req,user,cb)->
    try
      userInfo = await UserModel.findByIdAsync user?._id, {projection:{waitingVerifyAgent:1}}
    catch err
      debug.error err
    return cb null,userInfo?.waitingVerifyAgent or false
DEF 'dataMethods',dataMethods

# shareLinks = dataMethodCall(req, user, ['shareLinks'], ()->)
# req, shareLinks, type -> true/false
checkIsAllowedToShare = (req, list, tp)->
  return false unless list?.length
  l = list?.l or []
  v = list?.v or []
  idx = l.indexOf(tp)
  allowed = req.isVipUser or v[idx] is 0
  return (idx > -1) and allowed



# #data is recorded every time user open app
# LOC_LIMIT = -100
# logUserPos = (req,user)->
#   loc = req.body.loc
#   # console.log 'xxxxxxxxxx',loc,isValidLoc(loc)
#   unless isValidLoc(loc)
#     return
#   loc.ts = new Date()
#   loc.lat = loc.latitude
#   loc.lng = loc.longitude
#   delete loc.latitude
#   delete loc.longitude
#   if UA = (req.headers?['user-agent'] or req.headers?['User-Agent'])
#     loc.ua = UA
#   if user?._id
#     UserModel.setLastLocation user._id, {loc,LOC_LIMIT},(err,ret)->
#       if err
#         console.error err
#   else
#     if sid = req.session?.id()
#       loc.ip = req.remoteIP()
#       UserLoc.updateOne {_id:sid}, {$push:{locHis:
#         $each:[loc],
#         $slice:LOC_LIMIT,
#         }}, {upsert:true}, (err,ret)->
#           if err
#             console.error err

#need this in wecard and userlistings
dataMethodCall = (req, user, datas, cb)->
  cfg = {}
  # console.log '+++++++',req.body
  # if req.headers?
  #   console.log req.headers
  if req?.body?.loc
    UserModel.logPosition(req,user)
  doOne = ->
    unless d = datas.pop()
      ret = {datas:cfg}
      # if tlmt = req.body.tmlt and d = new Date(tlmt) and (new Date() - d > 3 * 24 * 3600 * 1000)
      #   ret.clearCache = true
      return cb null, ret
    # console.log '++++'+d
    func = dataMethods[d]
    if (not func) or ('function' isnt typeof func)
      return cb null, {e:'Not supported', method:typeof(func), fld:d}
    if func.length < 3
      cfg[d] = func req,user
      doOne()
    else
      func req,user,(err,data)->
        if err
          console.error err
          return cb null, {e:err.toString()}
        cfg[d] = data
        doOne()
  doOne()
DEF 'dataMethodCall',dataMethodCall
