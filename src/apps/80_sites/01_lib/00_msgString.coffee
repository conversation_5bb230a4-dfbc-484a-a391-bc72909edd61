MSG_STRINGS.def { # for human reading
  USER_NOT_FOUND: 'User Not Found'
  NEED_LOGIN: 'Need login'
  NOT_FOUND: 'Not Found'
  BAD_PARAMETER: 'Bad Parameter'
  ACCESS_DENIED: 'Access Denied'
  DB_ERROR: 'DB Error'
  UNKNOWN_ITEM: 'Unknown Item'
  INTERNAL_ERROR: 'Internal error'
  TOO_MANY_PROPERTIES: 'Too many properties'
  SYSTEM_ERROR: 'System Error'
  NO_PARAM:     'No parameter'
  NEED_VIP:     'Need VIP Permission'
  STRING_TOO_SHORT_OR_LONG: 'String is too short or too long.'
  YOU_ARE_MEMBER_ALREADY: 'You are a member already.'
  YOUR_MEMBERSHIP_PENDING: "Your membership is pending admin's approval."
  CANNOT_CREATE_SYSTEM_GROUP: 'Can not create system group.'
  NO_GROUP_SELECTED: 'No group selected.'
  NO_GROUP_OR_NO_RIGHT: 'Group not exists or no right to operate on it.'
  ERR_MODIFY_SELF: 'Cannot complete action.'
  NO_MATCHED_USER: 'No matched users.'
  PARSE_ERROR: 'Can not parse oauth info',
  NO_AGENT_ID: 'Agent ID not set yet!',
  NO_AGENT_BID:'Agent BID not set yet!',
  DEV_FORUM_BLOCKED:'Forum Blocked',
  GEO_CODING_ERROR:'Geo Coding Error'
  CNTY_CANADA:'CA'
  DEFAULT_EMAIL_DOMAIN_NAME: 'RealMaster'
  DEFAULT_EMAIL_REGISTER_SUBJECT: 'RealMaster Registration Confirmation'
  DEFAULT_RESETPWD_SUBJECT:'Reset Your RealMaster Password'
  DEFAULT_CONTACT_EML: '<EMAIL>'# '<EMAIL>'
  DEFAULT_CONTACT_TEL: '************'
  DEFAULT_EMAIL_LOGO: 'https://www.realmaster.cn/img/download/RealMaster-web-logo.png'
  NA: 'N/A',
  SUCCESS:'success'
  UNKNOWN_APPID:'Unknown AppID',
  INVALID_SIGNATURE:'Invalid Signature',
  REVOKE_FAILED:'Revoke Failed',
  TIMEOUT:'Timeout',
  CONNECT_RNI_ERROR:'Cannot perform this operation',
  REGISTERED_BEFORE:'You have been registered with us before.',
  NO_COOP_AGENT:'Cannot find the co-op agent.',
  INVALID_EMAIL_ADDRESS:'Invalid email address, try another email.'
  CANT_DELETE:'Cannot delete except for owner.'
  DELETED_COOP_AGENT:'Deleted co-op agent'
  ALREADY_EXISTS:'Already exists'
  INPUT_ERROR: 'Input value is incorrect'
  BALANCE_BELOW_ZERO: 'Balance will below zero'
  CANT_CLAIM: "This property can't be claimed."
  CLAIMOVER100: 'The total claimed amount of the property is above 100%.'
  TOKEN_ERROR: 'Something was wrong when updating the token, please contact the admin.'
  TOKEN_DELETE_ERROR: 'Something was wrong when deleting the token, please manually delete.'
  PHONE_BLOCKED: 'Your phone number is blocked.'
  CONTACT_SERVICES: 'Something is wrong, <NAME_EMAIL> for help'
}

