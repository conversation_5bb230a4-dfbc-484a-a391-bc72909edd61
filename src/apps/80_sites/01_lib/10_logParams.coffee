helpers = INCLUDE 'lib.helpers'
ADDPLUGIN 'logParam',(k,v)->
  this._logParams ?= {}
  this._logParams[k] = v

ADDPLUGIN 'getLogParams',(obj = {})->
  return ''
  # if 'string' is typeof obj
  #   obj = e:obj
  # if this._logParams
  #   for k,v of this._logParams
  #     obj[k] = v
  #   delete this._logParams
  # obj.h = this.host
  # obj.r = this.url
  # obj.ip = this.remoteIP()
  # if uid = this.user?._id
  #   obj.u = uid
  # obj.cts = helpers.dateFormat 'YYYYMMDDTHH24MISS'
  # strs = []
  # for k,v of obj
  #   strs.push  k + ":" + v
  # 'https://l.realmaster.com/a.gif?' + strs.join ';;'
