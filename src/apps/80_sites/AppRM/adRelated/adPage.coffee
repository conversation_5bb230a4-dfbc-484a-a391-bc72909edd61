# User = COLLECTION 'chome', 'user'
libUser = INCLUDE 'libapp.user'

UserModel = MODEL 'User'
LAYOUT 'adPage',->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:"viewport",content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      #meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'
      meta name:"apple-mobile-web-app-capable",content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"
      meta name:"format-detection",content:"telephone=no"
      if @jump
        meta httpEquiv:"refresh", content:"1;URL=#{@url}"
      g4tags js:new Date()
      title -> _ "RealMaster"

    block 'body',->
      body style:'background-color: white;
    height: 100%;
    width: 100%;
    position: absolute;
    margin: 0;',->
        g4body()
        text @content()

# also useed in wecard
VIEW 'NewsMiddleAdPageEn',->
  css '/css/ratchet.min.css'
  css '/css/middlePage.css'
  css '/css/font-awesome.css'
  div id:'jumpPageBg',->
    unless @noLoading
      div class:"loader top-align-group", ->
    div id:'loadingText', class:'top-align-group',->
      if @avt
        img src:"#{@avt}", id:'userAvt'
      else
        span style:'padding-left:5px',->
          text 'Loading...'
    if @hasU
      divParam = ""
      if @err
        divParam = style:'padding-left:32px; padding-right:20px; '
      div id:'shareInfo', divParam, ->
        if @extraInfo
          div ->
            text @extraInfo || ""
        div ->
          text @shareInfoText || _ 'RealMaster'
          unless @noWeCard
            # div id:'shareUserLinkWrapper', ->
            a href:"#{@cardUrl}", class:'fa fa-list-alt', id:'shareUserLink'
    iframe src:@url,style:"display:none;width:100%;height:100%;"

  unless @noGetApp
    div id:'getAppBar', class:"bar bar-standard bar-footer", style:"background: white;  line-height: 1em; border-top:1px none;", ->
      img src:"/img/logo.png" , class:"pull-left",height:"30px", width:"30px", style:'margin-top:8px'

      div style:"display:inline-block; max-width: 63%",->
        div style:"color:black; font-size:1em; margin-left: 10px; padding-top: 6px;",->
          text _ "RealMaster" #房大师
          span style:' margin-left: 10px; font-size: 10px;', ->
            text "Canada"
        div style:"color:#777;     font-size: 12px; margin-left: 10px; ",->
          text _ "Your Dream House Starts Here" #实时权威房源，轻松找房找学校
      if @uid
        aParam = href:"/app-download?uid=#{@uid}"
      else
        aParam = href:"/app-download"
      a class:'pull-right btn ', id:'adLinkHref', style:"opacity:1; border:1px solid #ff3802; border-radius: 0; background-color:white; color:black;", aParam,->
        text _ 'Get App' #免费下载



APP 'adPage'

GET 'needAPP',(req,resp)->
  ctx = {
    hasU:   1
    avt:    "/img/icon_Alert.png"
    nm:     'RealMaster'
    cardUrl:  req.fullUrl('/getapp')
    noWeCard: 1
    err:    1
    noLoading:  1
    shareInfoText: req.l10n "This feature is available in APP only"
    noGetApp: if (req.getDevType() is 'app') then 1 else false
  }
  url = '/getapp'
  if uid = req.param 'uid'
    url += '?uid='+uid
  return resp.ckup 'NewsMiddleAdPageEn', ctx, 'adPage', {jump:1, url:url}

GET 'news',(req,resp)->
  jumpMeta = ''
  if jurl = req.param 'url'
    jurl = decodeURIComponent(jurl) #"http://"+req.host+
    jumpMeta = "<meta http-equiv='refresh', content='1;URL=#{jurl}'>"
  resp.send '
  <!DOCTYPE html>
  <html>
   <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
      <meta name="apple-mobile-web-app-capable" content="yes">
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
      <meta name="format-detection" content="telephone=no">
  ' + jumpMeta + '
      <title>RealMaster</title>
   </head>
   <body style="background-color: white; height: 100%; width: 100%; position: absolute; margin: 0;">
      <img id="loadingSpinner"  src="/img/ajax-loader.gif" style="height: 100px; width: 150px; position: absolute; top: 50%; left: 50%; margin-left: -68px; margin-top: -50px;">
   </body>
  </html>
  '
  # resp.send '''
  # <!DOCTYPE html>
  # <html>
  #  <head>
  #     <meta charset="utf-8">
  #     <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  #     <meta name="apple-mobile-web-app-capable" content="yes">
  #     <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  #     <meta name="format-detection" content="telephone=no">
  #     <title>RealMaster</title>
  #  </head>
  #  <body style="background-color: white; height: 100%; width: 100%; position: absolute; margin: 0;">
  #     <img id='loadingSpinner'  src="/img/ajax-loader.gif" style="">
  #     <div id='errBlock' style='display:none;'>
  #       Load failure, Please
  #       <br>
  #       Try Open In
  #       <br>
  #       Browser
  #       <!--
  #       <button class='btn btn-primary' type='button'>
  #         Open In Browser
  #       </button>
  #       -->
  #     </div>
  #     <style>
  #       #loadingSpinner, #errBlock{
  #         height: 100px; width: 150px; position: absolute; top: 50%; left: 50%; margin-left: -68px; margin-top: -50px;
  #       }
  #       #errBlock{
  #         text-align: center;
  #       }
  #       .btn{
  #         position: relative;
  #         display: inline-block;
  #         padding: 6px 8px 7px;
  #         margin-bottom: 0;
  #         font-size: 12px;
  #         font-weight: 400;
  #         line-height: 1;
  #         color: #333;
  #         text-align: center;
  #         white-space: nowrap;
  #         vertical-align: top;
  #         cursor: pointer;
  #         background-color: white;
  #         border: 1px solid #ccc;
  #         border-radius: 3px;
  #       }
  #     </style>
  #     <script>
  #       setTimeout(function(){
  #         document.getElementById("errBlock").style.display = 'block';
  #         document.getElementById("loadingSpinner").style.display = 'none';
  #       }, 10000);

  #     </script>
  #  </body>
  # </html>
  # '''


GET 'news/jump',(req,resp)->
  done = ()->
    cfg.shareInfoText = req.l10n("Shared From ") + cfg.nm
    resp.ckup 'NewsMiddleAdPageEn', cfg, 'adPage', {jump:1, url:url}
  findError = (err)->
    console.log err.toString()

  url = (req.param 'url') or ( encodeURIComponent(req.fullUrl('/app-download')) )
  url = decodeURIComponent( url )
  lang = (req.param 'lang') or 'en' # TODO:
  req.setLocale lang
  uid = req.param 'uid'

  cfg = {url:url}
  if not uid
    cfg.hasU = 0
    cfg.avt  = null
    cfg.nm   = 'RealMaster'
    return done()
  UserModel.findById uid,{},(err,ruser)->
    if err
      done()
      return findError err
    if ruser
      cfg.hasU = 1
      cfg.avt  = ruser.avt
      cfg.nm   = libUser.fullNameOrNickname req.locale(),ruser
      cfg.cardUrl  = req.fullUrl('/1.5/wesite/') + ruser.id
      cfg.uid  = uid
    return done()
