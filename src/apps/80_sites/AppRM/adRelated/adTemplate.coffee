VIEW 'cpmBanner',->
  div id:"cpmAdsWrapper", style:"margin-bottom:20px", ->
    div id:"bannerMask", class:"mask", style:"display:none",onclick:"clickMask(event)",->->
    div style:"position:relative", ->
      if @cpm.big
        a id:"bigBanner", class:"big",  href:@cpm.computedHref,onclick:"openCpmAd()",->
          img src:@cpm.big,->
      if @cpm.std
        a class:"std", href:@cpm.computedHref,onclick:"openCpmAd()",->
          img src:@cpm.std, style:"height:100%",->
      if @cpm.card
        div class:"cpm-card", style:"height:92px;",->
          div class:"img", style:"background-image: url('"+@cpm.card+"');height:45px;width:45px",->
          div class: "card-right",  style:"width: calc(100% - 45px)",->
            div class:"slogan top",->
              div class: "tl trim", ->
                text @cpm.tl
              div class: "trim", ->
                span class:'ad',->
                  text  _('AD')
                span class: "desc trim", ->
                  text @cpm.slogan
            div style:"padding-right: 20px; float:right;",onclick:"openCpmAd()",->
              if @cpm.action == "tel"
                a class: "btn btn-positive", href:@cpm.computedHref,->
                  text @cpm.label
              else if @cpm.action == "eml"
                a class:"btn btn-positive", href:@cpm.computedHref,->
                  text @cpm.label
              else if @cpm.action=="url"
                a class:"btn btn-positive",->
                  text @cpm.label

      div id:"more", class:"more "+@cpm.moreDivClass,onclick:"clickMore(event)",->
        div class:"icon-wrapper",->
          if @cpm.card
            i class:"fa fa-ellipsis-v", ->
          else
            i class:"fa fa-ellipsis-h", ->
      div id:"showHelp", class:"show-help top",onclick:"openAdService(event)",style:"display:none",->
        img src:'/img/cpm-check.png',->
        span ->
          text _('Your AD Here','cpm')
