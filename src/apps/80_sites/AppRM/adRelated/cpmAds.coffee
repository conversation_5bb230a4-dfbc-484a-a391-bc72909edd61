#first reload ads, find budet left >0 or new ad, or new hours or new day. do not update db even ad has new balance left.
#only update db and balance when ad is displayed.
UserModel = MODEL 'User'
###
cpm keys：
NB1_zh-cn_Toronto_Ontario_Other_otherUser
NB1_en_Ajax_Ontario_Other_realtor
NB1_zh-cn_Toronto_Ontario_China_spUser #who has followed realtor

cpm广告可以设置位置，城市，国家，投放user的role。
编辑管理cpm。more->toollist->manage cpm
投放广告时会选择位置，城市，语言，用户的role
用户不管是哪个城市的，将地图拖到广告选择的城市就能看到cpm广告
论坛里面的banner是根据用户自己的城市来判断，就是首页左上角显示的城市。这个城市在广告投放范围内才会显示。
###

helpers = INCLUDE 'lib.helpers'
Cpm = COLLECTION 'chome','cpm'
CpmStat = COLLECTION 'chome','cpm_stat'
CpmStatView = COLLECTION 'chome','cpm_stat_view'
SysNotifyModel = MODEL 'SysNotify'
{respError} = INCLUDE 'libapp.responseHelper'
getUserCity = DEF 'getUserCity'
getConfig = DEF 'getConfig'
statHelper = INCLUDE 'libapp.stat_helper'
# cpms:{'"#{loc}_#{ptype}_#{lang}_#{userCity.city}_#{userCity.prov}_#{cstm_loc}_#{cstm_type}"',{[ad array]， cur:10}}
cpms = {}
#cpmlist [adslist:{_id:'', loc:''...}]
cpmList = []
# for click update {_id: ad},for quicky search
cpmByIDs = {}
#save all the showings for advertising place
stat_views = {}
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
cityHelper = INCLUDE 'lib.cityHelper'

async = require 'async'

CPM = getConfig('cpm')
CPM_CNT = getConfig('cpmCnt')
CPM_EXP = getConfig('cpmExp')
# update cpm.vc, cc, dailyvc, dailycc
CpmStat.createIndex( { 'mt': 1 }, { expireAfterSeconds: 3600 * 24 * 365 } )
CpmStat.createIndex( { '_id.date': -1 })
CpmStat.createIndex( { '_id.city': 1 })
Cpm.createIndex( { 'status': 1 })
CpmStatView.createIndex( { "mt": 1 }, { expireAfterSeconds: 3600 * 24 * 365 } )
EQUAL_TIME_WEIGHT = '111111111111111111111111'

# save clicks or showing of all available spot
saveAllViews = (date, hour)->
  views = stat_views
  #reset stat_views after save balance, stat.hour.[1,2,3,4] (vc, vcapp, cc, ccapp)
  stat_views = {}
  for key, view of views
    prefix = 'stat.'+hour
    inc ={"stat.#{hour}.0": view.vinc, "stat.#{hour}.1": view.vincapp}
    inc["stat.#{hour}.2"] = view.cinc if  view.cinc
    inc["stat.#{hour}.3"] = view.cincapp if  view.cincapp

    update = {$inc:inc, $set:{mt: new Date()}}
    _id =
      loc:view.loc,
      lang:view.lang,
      city:view.city,
      prov:view.prov,
      cstm_loc: view.cstmLoc,
      cstm_type: view.cstmType,
      date: date
    _id.ptype = view.ptype if view.ptype

    CpmStatView.updateOne {_id: _id}, update, {upsert:true}, (err) ->
      if err then console.error err

budgetToCurrentHour=(ad, hour)->
  weights = getWeightArray(ad)
  if weights[hour] is '0'
    return 0
  totalWeight = getSumWeight weights, 0 ,23
  weightToCurrent = getSumWeight weights, 0 ,hour
  budgetToCurrent = ad.daily_bdgt*(weightToCurrent/totalWeight)
  return budgetToCurrent


getSumWeight = (weights,from, to)->
  totalWeight = 0
  for i in [from..to]
    totalWeight = totalWeight + parseInt(weights[i])
  return totalWeight

getWeightArray = (ad)->
  weights = []
  if ad.time_weight
    weights =  ad.time_weight.split('')
  else
    for i in [0..23]
      weights.push 1
  return weights

#reset daily left and hourly left
getBudgetLeft = (ad, date, hour)->
  todayVc = date+"vc"
  percost = (CPM/CPM_CNT) * ad.discount
  ad['stat']?={}
  todayCost = percost* (ad['stat'][todayVc] or 0)
  # console.log "todayVc #{ad['stat'][todayVc]} percost #{percost}, todayCost: #{todayCost}"
  ad.daily_bdgt_left =  Math.min(ad.daily_bdgt, ad.balance) - todayCost
  hourBdgt = budgetToCurrentHour(ad, hour)

  # for weight is 0
  if hourBdgt is 0
    ad.hourly_bdgt_left = 0
  else
    ad.hourly_bdgt_left = Math.min(hourBdgt, ad.balance) - todayCost

# when update db, daily_bdgt_left is for current hour. update cpm counter, cpm_stat records. reset counter
updateADCounterAndBalance = (cb)->
  # use date and hour of current timestamp
  now = new Date()
  date = helpers.date2num now
  hour = now.getHours()
  async.eachSeries cpmList, ((ad, next) ->
    ad.vinc?=0
    ad.cinc?=0
    if ad.vinc <= 0 and ad.cinc <= 0
      return next()
    vinc = ad.vinc
    cinc = ad.cinc
    vincapp = ad.vincapp
    cincapp = ad.cincapp
    id = ad._id
    ad.vinc = 0
    ad.cinc = 0
    ad.vincapp = 0
    ad.cincapp = 0
    views = ad.views
    ad.views = {}
    #TODO: ALL INTEGER.
    currentCost = vinc * (CPM/CPM_CNT) * ad.discount
    ad.balance = helpers.round(ad.balance-currentCost, 5)
    inc =
      vc: vinc
      vcapp: vincapp
      cc: cinc
      ccapp: cincapp
      "stat.#{date}vc": vinc
      "stat.#{date}vcapp": vincapp
      "stat.#{date}cc": cinc
      "stat.#{date}ccapp": cincapp
      balance: -(currentCost)

    update = {$inc:inc}

    Cpm.findOneAndUpdate {_id:id},update,{returnDocument:'after'},(err, ret)->
      if err
        console.error err
        return next(err)
      todayVc = date+"vc"
      ad["stat"]?={}
      ad["stat"][todayVc]?= 0
      ret = ret.value
      ad["stat"][todayVc] = ret.stat?[todayVc]
      getBudgetLeft ad, date, hour
      for key, view of views
        # inc= vc
        # vc, vcapp, cc, ccapp
        inc =
          "stat.#{hour}.0":view.vinc
          "stat.#{hour}.1": view.vincapp
          "stat.#{hour}.2": view.cinc
          "stat.#{hour}.3": view.cincapp
        update = {$inc:inc, $set:{mt: new Date()}}
        _id =
          cpmid: id,
          loc:view.loc,
          lang:view.lang,
          city:view.city,
          prov:view.prov,
          cstm_loc: view.cstmLoc,
          cstm_type: view.cstmType,
          date: date
        _id.ptype =  view.ptype if view.ptype

        CpmStat.updateOne {_id: _id}, update, {upsert:true}, (err) ->
          if err then console.error err
      next()
    ), (err) ->
      saveAllViews(date,hour)
      cb(err) if cb


DEF 'updateADCounterAndBalance', updateADCounterAndBalance

CALLBEFOREEXIT updateADCounterAndBalance

# geneate id for every ad, ad can belong to multiple id. used for lookup.
#  for ad in map.  card->mapStd, forum, big->std->card
genKeys = (ad) ->
  return [] unless ad
  result = []
  results = []
  arr = []
  locs = []

  combineArray = (arr, depth)->
    for obj in arr[depth]
      result[depth] = obj
      if depth != (arr.length-1)
        combineArray(arr, depth + 1)
      else
        results.push(result.join('_'))

  #if want to show ad in map, need to specify ptype.
  #ad should have ptype. if  loc and (std or card) insert_to_ptype
  if ad.loc?.length
    for loc in ad.loc
      if loc =='MP1' and (ad.mapStd or ad.card)
        for type in ad.ptype
          locs.push "#{loc}_#{type}"
      else if (loc == 'NB1' or loc=='NB2') and (ad.big or ad.std or ad.card)
        locs.push loc

    arr.push locs

  arr.push ad.lang if ad.lang?.length

  if ad.sas?.length
    cities = []
    for city in ad.sas
      cities.push "#{city.city}_#{city.prov}"
    arr.push cities
  arr.push ad.cstm_loc if ad.cstm_loc?.length
  arr.push ad.cstm_type if ad.cstm_type?.length

  combineArray(arr, 0)
  return results


#reload ad from db, insert to cpmList.
reloadADs = (cb)->
  sort = {}
  now = new Date()
  date = helpers.date2num now
  hour = now.getHours()
  cpmFields =
    "action" : 1,
    "ad_eml" : 1,
    "balance" : 1,
    "big" : 1,
    "card" : 1,
    "cstm_loc" :1
    "cstm_type" : 1
    "daily_bdgt" : 1,
    "discount" : 1,
    "eml" : 1,
    "end_dt" : 1,
    "label_en" : 1,
    "label_zh" : 1,
    "lang" : 1
    "loc" : 1
    "mapStd" : 1,
    "mbl" : 1,
    "nm" : 1,
    "note" : 1,
    "ptype" : 1
    "sas" : 1,
    "std" : 1,
    "time_weight" : 1,
    "url" : 1,
    "slogan_en" : 1,
    "slogan_zh" : 1,
    "tl_en" : 1,
    "tl_zh" : 1,

  cpmFields["stat."+date+"vc"]=1

  #new ads has no daily_bdgt_left，hourly_bdgt_left，
  #if last show was yesterday or last hour, need to query again.

  # new date,
  query = {
    $and :[
      {status:'A'},
      {start_ts:{$lte:now}},
      {daily_bdgt:{$gt:0}},
      {balance:{$gt:0}},
      {$or:[{end_ts:{$gte:now}},{end_ts:{$exists:false}}]}
      # {$or:[
      #   {today_dt:{$lt:date}},
      #   {$and:[
      #     {today_dt:date},
      #     {today_hour:{$lt:hour}},
      #     $or:[
      #       {"daily_bdgt_left":{$gt:0}},
      #       {"daily_bdgt_left":{$exists:false}}
      #     ]
      #   ]},
      #   {"hourly_bdgt_left":{$gt:0}},
      #   {"hourly_bdgt_left":{$exists:false}}
      # ]}
    ]
  }

  debug.debug 'cpm reload ADS',query
  Cpm.findToArray query, {projection:cpmFields}, (err, ret)->
    return console.error err if err
    cpms = {}
    cpmByIDs = {}

    # reset date and daily budget left and hourly_bdgt_left for new date or new hour or new ads
    for ad in ret
      #continue to next if today and left_bdgt <0
      getBudgetLeft ad, date, hour
      percost = (CPM/CPM_CNT) * ad.discount
      if ad.daily_bdgt_left < percost
        continue
      #continue to next if current hour and left_bdgt <0
      if ad.hourly_bdgt_left < percost
        continue

      ad.views = {}
      ad.vinc=0
      ad.vincapp = 0
      ad.cinc=0
      ad.cincapp = 0
      keys = genKeys ad
      # console.log keys
      # cpms = { 'map_en_toronto_ontario_china' : {cur:0,ads:[]} }
      # if in same order, NB1 AND NB2 might have same order and duplicate when showing.make nb2 reverse
      for key in keys
        obj = cpms[key]?={}
        obj.cur = 0
        if key.indexOf('NB2') < 0
          (obj.ads?=[]).push ad
        else
          (obj.ads?=[]).unshift ad
      cpmByIDs[ad._id?.toString()] = ad
    cpmList = ret
    debug.debug 'cpmList',cpmList
    cb() if cb

getCstmLoc = (req)->
  return if req.isChinaIP() then 'China' else 'Other'

getCstmType =(req) ->
  if  req.hasRole 'realtor'
    return 'realtor'
  #isSpUser user who has followed realtor
  if req.session?.get('user')?.flwng?.length
    return 'spUser'
  return 'otherUser'


getInitObj =(userCity, lang, loc, cstmLoc, cstmType,ptype) ->
  city: userCity.city,
  prov: userCity.prov,
  lang:lang,
  loc: loc,
  cstmLoc:cstmLoc,
  cstmType:cstmType,
  ptype:ptype,
  vinc: 0
  cinc: 0
  cincapp: 0
  vincapp: 0

sessionsIDs = {}
#clear every 5min
helpers.repeat 300000, ->
  sessionsIDs = {}
#if loc from map, add sesssions to sessionsIDs, empty every 30s.
#for request from map, if find session id, no vc add.
#
countAd = (sid, adId)->
  return true unless ads = sessionsIDs[sid]
  ad = ads.find((el)->
    return (el.id.toString()==adId.toString()) and (el.exp > Date.now())
    )
  return if ad then false else true

#增加loc web，可以返回多个。广告计数问题。
getCpm = (req, loc, user)->
  ret = []
  lang = req.locale() or 'en'
  #userCity is user.city in DB.difference with dispVar.usercity
  if req.param('city') and req.param('prov')
    userCity = {
      city: req.param('city'),
      prov: cityHelper.getProvAbbrName(req.param('prov'))
    }
  else
    userCity = getUserCity(req,user,null)
  userProv = userCity.prov
  cstmLoc = getCstmLoc req
  ptype = req.param('ptype')
  cstmType= getCstmType req
  isapp = req.getDevType() is 'app'

  #ads may not identfy a city. ptype is required for map
  if ptype
    key = "#{loc}_#{ptype}_#{lang}_#{userCity.city}_#{userCity.prov}_#{cstmLoc}_#{cstmType}"
    key1= "#{loc}_#{ptype}_#{lang}_#{cstmLoc}_#{cstmType}"
  else
    key = "#{loc}_#{lang}_#{userCity.city}_#{userCity.prov}_#{cstmLoc}_#{cstmType}"
    key1= "#{loc}_#{lang}_#{cstmLoc}_#{cstmType}"

  stat_views[key] ?= getInitObj userCity, lang, loc,cstmLoc,cstmType,ptype
  stat_views[key].vinc++
  stat_views[key].vincapp++ if isapp

  #merge ads. copy key1 to key, copy no city to all city.
  if cpms[key] and (!cpms[key].merged) and (cpms[key1]?.ads)
    # console.log 'merge ads'
    for ad in cpms[key1].ads
      #use unshift to aviod same order of nb1 and nb2
      if loc =='NB2' then cpms[key].ads.unshift ad else cpms[key].ads.push ad
    cpms[key].merged = true


  return {} unless currentList = cpms[key] or cpms[key1]
  ads = currentList?.ads
  return {} unless ads.length
  ad = ads[currentList.cur]
  return {} unless ad

  sid = req.session?.id()
  #check if ad exists in the saved ads for this sid. and ts < current time
  #if exists, go on, if not exists, add counter.and add ads to sessionsIDs, set expts = now()+CPM_EXP*1000

  #for no map or map has new ad, update counter
  if loc !='MP1' or countAd(sid, ad._id)
    ad.views = ad.views or {}
    # for stat
    ad.views[key]?= getInitObj userCity, lang, loc,cstmLoc,cstmType,ptype
    ad.views[key].vinc++
    ad.views[key].vincapp++ if isapp

    #for ad
    ad.vinc++
    ad.vincapp++ if isapp
    percost = (CPM/CPM_CNT) * ad.discount
    ad.hourly_bdgt_left = ad.hourly_bdgt_left - percost
    #update cpms, remove hourly_bdgt_left<=0
    if ad.hourly_bdgt_left < percost
      for key, l of cpms
        objs = l.ads or []
        index = objs.findIndex((el)-> return el._id.toString() is ad._id.toString())
        if index>=0
          l.ads.splice(index,1)
  #update sesssionad exp timestamp
  if loc =='MP1'
    exp = Date.now()+CPM_EXP*1000
    sessionsIDs[sid]?= []
    existAd = sessionsIDs[sid].find((el)->
      return el.id.toString() == ad._id.toString()
      )
    if existAd
      existAd.exp = exp if existAd.exp< Date.now()
    else
      sessionsIDs[sid].push({id:ad._id,exp:exp})

  #move current ad index
  currentList.cur++
  if currentList.cur > (ads.length-1)
    currentList.cur = 0

  #copy needed fields
  ret = {_id:ad._id}
  #if big
  #if card
  #
  ret.url = ad.url
  ret.mbl = ad.mbl
  ret.ad_eml = ad.ad_eml
  ret.label = if lang is 'en' then (ad.label_en or ad.label_zh) else (ad.label_zh or ad.label_en)
  ret.tl = if lang is 'en' then (ad.tl_en or ad.tl_zh) else (ad.tl_zh or ad.tl_en)
  ret.slogan = if lang is 'en' then (ad.slogan_en or ad.slogan_zh) else (ad.slogan_zh or ad.slogan_en)
  ret.action = ad.action
  if (loc is 'NB1' or loc is 'NB2') and ad.big
    ret.big = ad.big
  else if loc is 'MP1' and ad.mapStd and not ad.card
    ret.mapStd = ad.mapStd
  else if ad.std and showStd loc, ad
    ret.std = ad.std
  else if ad.card and showCard loc, ad
    ret.card = ad.card
  return ret

DEF 'getCpm',getCpm

# in map, show card or standard, in forum post, show big -> std -> card
showStd = (loc, ad) ->
  return (loc is 'NB1' or loc is 'NB2') and (not ad.big)

showCard = (loc, ad) ->
  return loc is 'MP1' or ((loc is 'NB1' or loc is 'NB2') and (not ad.big) and (not ad.std))


cpmClicked = (id, req)->
  lang = req.locale()
  city = req.param 'city'
  prov = cityHelper.getProvAbbrName req.param 'prov'
  loc = req.param 'loc'
  ptype = req.param('ptype')
  isApp = req.getDevType() is 'app'
  cstmLoc = getCstmLoc(req)
  cstmType = getCstmType(req)

  ad = cpmByIDs[id]
  return null unless ad
  if ad.url or ad.mbl or ad.ad_eml
    ad.cinc++
    if ptype
      key = "#{loc}_#{ptype}_#{lang}_#{city}_#{prov}_#{cstmLoc}_#{cstmType}"
    else
      key = "#{loc}_#{lang}_#{city}_#{prov}_#{cstmLoc}_#{cstmType}"

    view = ad.views[key]
    if view
      view.cinc++
      view.cincapp++ if isApp
    if stat_views[key]
      stat_views[key].cinc++
      stat_views[key].cincapp++ if isApp
    return ad
  null


reloadADs()
countDown = 10
helpers.repeat 60000, ->
  updateADCounterAndBalance ->
    if countDown-- < 0
      countDown = 10
      reloadADs()

APP '1.5'
APP 'cpm',true
GET 'services',(req, resp)->
  resp.ckup 'services',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

POST 'click',(req, resp) ->
  err = "Inactive or Unknown ADs"
  if id = req.param 'id'
    ad = cpmClicked(id, req)
    return resp.send {ok:0, err:err} unless ad
  return resp.send {ok:1}

#params:city=Toronto&loc=MP1&prov=Ontario&ptype=Residential
#params:city=Toronto&loc=MP1&prov=ON&ptype=Residential
POST 'hasCpm', (req, resp)->
  loc = req.param 'loc'
  propId = req.param 'propId'
  UserModel.appAuth {req,resp},(user)->
    cpm = getCpm req,loc,user
    hascpm = if cpm._id then true else false
    ret = {ok:1, hascpm: hascpm}
    # TODO: @liurui check propId in showing
    # if propId
    #   ret.hascpm = true
    #   ret.showingUrl = 'https://f.realmaster.cn/P/DPD/RC.jpeg'
    resp.send ret

#http://app.test:8080/1.5/cpm/currentAD?city=Toronto&loc=MP1&prov=Ontario&ptype=Residential
#http://app.test:8080/1.5/cpm/currentAD?city=Toronto&loc=MP1&prov=ON&ptype=Residential
GET 'currentAD', (req, resp)->
  loc = req.param 'loc'
  UserModel.appAuth {req,resp},(user)->
    cpm = getCpm req,loc,user
    resp.ckup 'currentAD',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

#get current ad to display.
#params: location, city,  prov
#get from session: lang, cstmLoc (china or other).
#logic: search by key loc_lang_city_prov_china, get current ad, increase vc, vcapp, update ad.views for later.
POST 'currentAD', (req, resp)->
  loc = req.param 'loc'
  UserModel.appAuth {req,resp},(user)->
    cpm = getCpm req,loc,user
    resp.send {ok:1, cpm:cpm}

GET 'manage',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    resp.ckup 'manage',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

GET 'edit',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    if req.redirectHTTP()
      return
    maxImageSize = getConfig('maxImageSize')
    resp.ckup 'edit',{maxImageSize},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

getUpdatedCpm = (set)->
  # do not change balance when edit from client side.
  cpmFields = [ 'eml','uid', 'clientNm','nm','loc','lang','cstm_loc','cstm_type','ptype','start_dt','end_dt','discount','daily_bdgt',\
  'big','std','mapStd','card','tl_en','tl_zh','slogan_en','slogan_zh','label_en','label_zh','note','action', 'url','mbl','ad_eml','time_weight']
  cpm = {}
  for i in cpmFields
    if (v = set[i])?
      if i in ['discount','daily_bdgt']
        v = +(v)
      if i is 'uid'
        v = new Cpm.ObjectId(v)
      cpm[i] = v
  if set.sas
    for city in set.sas
      city.prov = cityHelper.getProvAbbrName city.prov
    cpm.sas = set.sas
  cpm.time_weight = EQUAL_TIME_WEIGHT unless cpm.time_weight
  if set.status in ['D', 'A']
    cpm.status = set.status
  now = new Date()
  cpm.mt = now
  date = helpers.date2num now
  hour = now.getHours()
  # cpm.today_dt = date
  # cpm.today_hour = hour
  return cpm


updateDB = (params, cb)->
  req = params.req
  resp = params.resp
  id = params.id
  update = params.update
  chageStatus = params.changeStatus

  obj = update.$set
  #should update balance and counter first
  updateADCounterAndBalance ->
    id = params.id
    update = params.update
    chageStatus = params.changeStatus
    cfg = {returnDocument:'after'}
    cfg.upsert = if chageStatus then  false else true
    Cpm.updateOne {_id: id}, update, cfg, (err,ret)->
      return cb(err) if err
      reloadADs ->
        cb()

POST 'edit',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'cpmAdmin'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless set = req.body
    cpm = getUpdatedCpm set
    if set.oldDiscount or set.oldDailyBdgt
      history = {mt:new Date(), uid:user._id}
      history.oldDiscount = set.oldDiscount if set.oldDiscount
      history.oldDailyBdgt = set.oldDailyBdgt if set.oldDailyBdgt

    cpm.ts = new Date() unless set._id #just for new cpm
    cpm.start_ts = new Date(cpm.start_dt) if cpm.start_dt
    #add 24 hours for end_dt, should display until 0:00 of next day
    update = {}
    if cpm.end_dt
      cpm.end_ts = new Date(new Date(cpm.end_dt).getTime() + 24 * 3600 * 1000)
    else
      update.$unset = {end_ts:1}
    update.$set = cpm
    update.$push = {history:history} if history
    id = set._id or new Cpm.ObjectId()
    if cpm.status is 'A'
      msg = isNotValidCpm req, cpm
      return resp.send {ok:0,err: msg} if msg
    params =
      req: req
      resp:resp
      id:id
      update:update
      changeStatus: false

    updateDB params, (err)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
      return resp.send {ok:1,_id: id}

addTransaction = DEF 'addTransaction'

POST 'addCpmBalance',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'cpmAdmin'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?.cpmid
    amount = -(req.param 'amount')
    Cpm.findOne {_id:id}, (err, cpm)->
      return resp.send {ok:0, err: err} if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless cpm
      addTransaction req, resp, user,(err,ret)->
        if err
          return resp.send {ok:0, err: err}
        Cpm.updateOne {_id: id}, {$inc: {balance:amount}, $push:{history:{uid:user._id, ts:new Date(), addBalance:amount} }},->
          return resp.send {ok:1,_id: id}

isNotValidCpm = (req, cpm)->
  unless cpm.nm
    return req.l10n 'Please specify a name for your ad','cpm'
  unless cpm.eml
    return req.l10n  'No user email entered','cpm'
  unless cpm.loc.length
    return req.l10n  'Please select at least one ads location','cpm'
  if (cpm.loc.indexOf('MP1')>=0) and (not cpm.ptype.length)
    return req.l10n 'Please select at least one property type','cpm'
  unless cpm.lang.length
    return req.l10n 'Please select at least one Language','cpm'
  unless cpm.cstm_loc.length
    return req.l10n 'Please select at least one User location','cpm'
  unless cpm.cstm_type.length
    return req.l10n 'Please select at least one User Type','cpm'
  unless cpm.start_dt
    return req.l10n 'Please select start date','cpm'
  if (not cpm.daily_bdgt) or cpm.daily_bdgt<=0
    return req.l10n 'Please fill in max daily Budget','cpm'
  if (not cpm.big) and (not cpm.std) and (not cpm.card) and (not cpm.mapStd)
    return req.l10n 'Please upload at least one image','cpm'
  if cpm.card and ((not cpm.label_en) or (not cpm.label_zh))
    return req.l10n 'Please fill in click action type','cpm'
  unless cpm.action
    return req.l10n 'Please fill in click action','cpm'
  if (not cpm.mbl) and (not cpm.url) and (not cpm.ad_eml)
    return req.l10n 'Please fill in click action type','cpm'
  return false

#action: publish, resume, pause, terminate, refund when delete or terminate.
POST 'changeStatus',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'cpmAdmin'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?._id
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless action = req.body?.action
    update ={$set:{}}
    update.$set.mt = new Date()
    params =
      req: req
      resp:resp
      id:id
      changeStatus: true
    if action is 'pause'
      update.$set.status = 'U'
      params.update = update
      updateDB params, (err)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
        return resp.send {ok:1,_id: id}

    else
      Cpm.findOne {_id:id}, (err, cpm)->
        return resp.send {ok:0, err: err} if err
        return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless cpm
        if action is 'publish' or action is 'resume'
          update.$set.status = 'A'
          msg = isNotValidCpm req, cpm
          return resp.send {ok:0,err: msg} if msg
          params.update = update
          updateDB params,(err)->
            return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
            return resp.send {ok:1,_id: id}
        else if action=='terminate' or action=='delete'
          update.$set.status = 'U'
          # use db balance.
          update.$set.balance = 0
          update.$set.status = 'del' if action is 'delete'
          params.update = update

          if !cpm.balance or cpm.balance<=0
            return updateDB params, (err)->
              return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
              return resp.send {ok:1,_id: id}
          else
            req.body.act = 'add-bal'
            req.body.pay_tp = 'Refund'
            req.body.cpmid = id
            req.body.uid = cpm.uid
            req.body.eml = cpm.eml
            # console.log cpm.balance
            req.body.amount = cpm.balance
            addTransaction req, resp, user,(err,ret)->
              if err
                return resp.send {ok:0, err: err}
              return updateDB params, (err)->
                return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
                return resp.send {ok:1,_id: id}
CPM_FIELDS =
  nm:1
  clientNm:1
  start_dt:1
  end_dt:1
  balance:1
  loc:1
  status:1
  tl_en:1
  tl_zh:1
  eml:1
  vc:1
  cc:1
  daily_bdgt:1

#noDraft is for statistic page list all no draft ads
POST 'all',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    query = {status:{$ne:'del'}}
    query.uid = user._id if !(req.isAllowed 'cpmAdmin') and !(req.isAllowed 'productSales')
    if uid = req.param 'uid'
      query.uid = uid
    flds = Object.assign {}, CPM_FIELDS
    if req.isAllowed('cpmAdmin') or req.isAllowed('productSales')
      flds.vcapp = 1
      flds.ccapp = 1
    if req.param 'noDraft'
      query.status={$nin:['D','del']}
    Cpm.findToArray query,{projection:flds,sort:{clientNm:1, ts:1}},(err,ret)->
      # console.log ret
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      return resp.send {ok:1, cpmList: ret}

# for edit preview
POST 'one',(req, resp)->
  now = new Date()
  date = helpers.date2num now
  hour = now.getHours()
  return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'cpmAdmin'
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless req.param 'id'
  id = req.param 'id'
  Cpm.findOne {_id: id}, {fields:{history:0}},(err,ret)->
    if err
      clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
      SysNotifyModel.notifyAdmin err?.toString()+clientMsg
      return respError {
        clientMsg,
        resp,
        sysErr:err}
    getBudgetLeft ret, date, hour
    ret.stat?={}
    ret.todayVc = ret.stat[date+'vc'] or 0
    return resp.send {ok:1, cpm:ret}

GET 'analytics',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'analytics',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

GET 'analytics/detail',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'analytics_detail',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

#get distinct cities of the selected ad, if all_ads or normal cpm ads, use cpmstat. is all_available use cpmstatview
POST 'statCities',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    # return respError MSG_STRINGS.NEED_LOGIN, resp
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id =  req.param 'id'
    if ((id is "all_available") or (id is 'all_ads')) and !(req.isAllowed 'cpmAdmin') and !(req.isAllowed 'productSales')
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    col = CpmStat
    group = { "_id": {city: "$_id.city", prov: "$_id.prov" }}
    query = [{'$group': group},  {$sort: {'_id.city': 1}}]

    if id is "all_available"
      col = CpmStatView
    else if id is "all_ads"
    else
      query = [
        {$match: {'_id.cpmid': new Cpm.ObjectId(id)}},
        {'$group': group},
        {$sort: {'_id.city': 1}}
      ]
    col.aggregate (query),{cursor:{batchSize:0}},(err,results)->
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      cities = []
      for ret in results
        cities.push ret._id
      return resp.send {ok:1, cities: cities}

computeStartDate = (d)->
  if d=='all'
    d = '365' # maxium for one year.
  else if d in ['7', '30', '60', '90', '183', '365']
    ts = new Date(new Date() - parseInt(d) * 24 * 3600 * 1000)
  else if d=='thisM'
    today = new Date()
    ts = new Date(today.getFullYear()+'-'+(today.getMonth()+1)+'-1')
  else if d == 'lastM'
    today = new Date()
    ts1 = new Date(today.getFullYear()+'-'+(today.getMonth()+1)+'-1')
    ts = statHelper.getLastMonth(ts1)

  ret = {}
  ret.gte= helpers.date2num(ts) if ts
  ret.lte = helpers.date2num(ts1) if ts1
  return ret

groupByDate = (list)->
  ret = {}
  for cpm_stat in list
    continue unless cpm_stat.stat?.total
    date = cpm_stat._id.date
    #for total each day
    ret[date] = {} unless ret[date]
    # for hourly each day
    data = cpm_stat['stat']
    obj = {}
    for hour, value of data
      ret[date][hour] = {} unless ret[date][hour]
      ret[date][hour].vc = (ret[date][hour].vc or 0) + value['0']
      ret[date][hour].vcapp = (ret[date][hour].vcapp or 0) + value['1']
      ret[date][hour].cc = (ret[date][hour].cc or 0) + value['2']
      ret[date][hour].ccapp = (ret[date][hour].ccapp or 0) + value['3']
  ret

groupByHour = (list) ->
  ret = {}
  for date, cpm_stat of list
    for hour, value of cpm_stat
      ret[hour] = {} unless ret[hour]
      ret[hour].vc= (ret[hour].vc or 0) + (value.vc or 0)
      ret[hour].vcapp= (ret[hour].vcapp or 0) + (value.vcapp or 0)
      ret[hour].cc= (ret[hour].cc or 0) + (value.cc or 0)
      ret[hour].ccapp= (ret[hour].ccapp or 0) + (value.ccapp or 0)
  ret


sumHour = (cpm_stat) ->
  data = cpm_stat['stat']
  cnt = 0
  obj = {}
  for key, value of data
    obj['0'] = (obj['0'] or 0) + value['0']
    obj['1'] = (obj['1'] or 0) + value['1']
    obj['2'] = (obj['2'] or 0) + value['2']
    obj['3'] = (obj['3'] or 0) + value['3']
  data.total = obj

getCpmAnalytics = (req, resp)->
  id = req.param 'id'
  loc = req.param 'loc'
  city = req.param 'city'
  prov = cityHelper.getProvAbbrName req.param 'prov'
  lang = req.param 'lang'
  date = req.param 'date'
  byhour = req.param 'byhour'
  query = {}
  col = CpmStat
  if id is "all_available"
    #all_available can only see data for a month for now,to large
    col = CpmStatView
    date = '30' if parseInt(date) > 30
  else if id and (id isnt 'all_ads')
    id = new Cpm.ObjectId(id)
    query['_id.cpmid'] = id
  query['_id.loc'] = loc if loc and loc!='All'
  query['_id.city'] = city if city
  query['_id.prov'] = prov if prov
  query['_id.lang']= lang if lang and lang!='All'
  ts = computeStartDate date
  query['_id.date']={} if ts.gte or ts.lte
  query['_id.date'].$gte = ts.gte if ts.gte
  query['_id.date'].$lte = ts.lte if ts.lte

  col.findToArray query, {sort:{'_id.date':-1}},(err, ret)->
    if err
      clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
      SysNotifyModel.notifyAdmin err?.toString()+clientMsg
      return respError {
        clientMsg,
        resp,
        sysErr:err}
    # return respError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err
    for stat in ret
      sumHour stat
    result = groupByDate ret
    result = groupByHour result if req.param 'byhour'

    return resp.send {ok:1, stats: result}
#all_ads is all cpm, all_available is for cpm_stat_view
POST 'analytics',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:MSG_STRINGS.NEED_LOGIN, resp} unless user
    id = req.param 'id'
    if !id and !(req.isAllowed 'cpmAdmin') and !(req.isAllowed 'productSales')
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    # security problem，  check the ads is belonged to the user, or the user is admin
    if (req.isAllowed 'cpmAdmin') or (req.isAllowed 'productSales')
      return getCpmAnalytics req, resp
    Cpm.findOne {uid: user._id, _id: new Cpm.ObjectId(id)}, (err, ret)->
      # return respError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless ret
      return getCpmAnalytics req, resp

VIEW 'manage',->
  div id:'vueBody',->
    text """<app-cpm-ads></app-cpm-ads>"""
  js '/libs/jquery-1.9.1.min.js'
  css '/css/xpull.css'
  js '/js/xpull.js'
  js '/js/overthrow/overthrow.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appCpmAds.js'


VIEW 'services',->
  div id:'vueBody',->
    text """<app-cpm-services></app-cpm-services>"""
  js '/js/entry/commons.js'
  js '/js/entry/appCpmServices.js'


VIEW 'edit',->
  div id:'vueBody',->
    text """<app-cpm-edit></app-cpm-edit>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appCpmEdit.js'

VIEW 'analytics',->
  div id:'vueBody',->
    text """<app-cpm-analytics-modal></app-cpm-analytics-modal>"""
  js '/js/entry/commons.js'
  js '/js/entry/appCpmAnalytics.js'

VIEW 'analytics_detail',->
  div id:'vueBody',->
    text """<app-cpm-analytics-detail></app-cpm-analytics-detail>"""
  js '/js/entry/commons.js'
  js '/js/entry/appCpmAnalyticsDetail.js'

VIEW 'currentAD',->
  div id:'vueBody',->
    text """<cpm-banner></cpm-banner>"""
  js '/js/entry/commons.js'
  js '/js/entry/appCpmCurrentAd.js'
  css '/css/apps/cpmBanner.css'
