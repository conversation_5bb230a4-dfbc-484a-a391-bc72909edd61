# #NOTE: need event page for vip upgrade wecard
# UserModel = MODEL 'User'
# # processSpEventReq = DEF 'processSpEventReq'
# # updateUser = DEF 'updateUser'

# Wecard = COLLECTION 'chome', 'wecard'
# # Template = COLLECTION 'chome', 'wecardtemplate'
# # SpEvents =  COLLECTION 'chome', 'sp_events'

# # helpers = INCLUDE 'lib.helpers'

# APP 'event'
# POST 'shared',(req,resp)->
#   error = (err) ->
#     return resp.send {ok:0, err:err}
#   return error('No id') unless evtid = req.param('evtid')
#   Wecard.updateOne {tp:'evtad', _id:evtid},{$inc:{"meta.shr": 1, "meta.shrd": 1}},{upsert:false,returnDocument:'after'},(err)->
#     return error('Not Updated') if err
#     return resp.send {ok:1}

# GET (req,resp)->
#   UserModel.appAuth {req,resp},(user)->
#     error = (err) ->
#       resp.ckup 'generalError', {err:err}
#     ctx =
#       url:req.param('u') or '/event/shared'
#       tpl: req.param('tpl')
#       inframe: req.param 'inframe'
#     unless ctx.url and ctx.tpl
#       return error('This event does not exist')
#     ctx.owner = user if req.hasRole 'realtor'
#     #find tpl in wecard
#     #tpl = creepyHouse
#     lang = req.locale()
#     userLang = req.param('lang')
#     lang = userLang if userLang
#     q = {tp:'evtad', $or:[{'meta.editor':':'+ctx.tpl}, {'meta.editor':':'+ctx.tpl+':'+lang}]}
#     Wecard.findToArray q,(err, list) ->
#       ret = {}
#       return error(err.toString()) if err
#       # return error('This event does not exist') unless ret
#       unless list.length
#         if req.getDevType() isnt 'app'
#           return resp.redirect "/app-download?evt=#{ctx.tpl}&notpl=1"
#         return resp.ckup 'event',ctx
#       for i in list
#         ret = i if i.meta.editor is (':'+ctx.tpl)
#         if i.meta.editor is (':'+ctx.tpl+':'+lang)
#           ret = i
#           break
#       return error('This event does not exist') unless ret.seq?.length >= 3
#       if req.getDevType() isnt 'app'
#         return resp.redirect "/app-download?evt=#{ctx.tpl}&evtid=#{ret._id}&lang=#{lang}"
#       ctx.disp_subtitle   = ret.seq[0].m
#       ctx.disp_contents   = ret.seq[1].m
#       # ctx.contentsDl = req.l10n(ret.seq[2].m or ret.seq[1].m)
#       ctx.share_title = ret.meta.title or null
#       ctx.share_desc  = ret.meta.desc or null
#       ctx.share_url_append = '&evtid='+ret._id
#       ctx.noShare = req.param('noShare')
#       resp.ckup 'event',ctx

# VIEW 'event',->
#   unless @inframe
#     header class:'bar bar-nav', ->
#       #text ckup 'headerbarUserModalBtn'
#       h1 class:'title', -> @title or _("RealMaster")
#       a href:"/1.5/index",class:"icon icon-left pull-left"
#   div class:'content', ->
#     style -> """
#     p {font-size:18px;}
#     div.article {padding:0;}
#     .desc{
#       background:#EEE; padding: 15px;
#     }
#     .desc p{
#       font-size:14px; padding-top: 15px;
#     }
#     .btn-wrapper{margin-top:20px;}
#     """
#     div class:'article', ->
#       div class:"desc", ->
#         if @disp_subtitle
#           text @disp_subtitle
#         else
#           div class:'h4', ->
#             if @req.param("feature")
#               text _ "Share this feature to your friends."
#             else
#               text _ "This new feature can be instantly actived by sharing RealMaster APP to your friends."
#           p -> _ "Click the following button and share."
#       unless @noShare
#         div class:"btn-wrapper", ->
#           div class:'btn btn-block btn-positive btn-long',id:"wechatShareBtn",-> _ "Share to WeChat"
#           div class:'btn btn-block btn-positive btn-long',style:"display:none;",id:"facebookShareBtn",-> _ "Share to Facebook"
#       div style:"padding: 10px;",->
#         text @disp_contents
#     text ckup 'downloadSharingMeta', {
#       owner:@owner,lang:@req.locale(),
#       share_title:@share_title,
#       share_desc:@share_desc,
#       share_url_append:@share_url_append
#     }
#     if @url
#       div style:'display:none',id:'share-dnurl',-> text @url
#   coffeejs {},->
#     debug = (e)-> console?.log e
#     wxBtn = document.getElementById('wechatShareBtn')
#     fbBtn = document.getElementById('facebookShareBtn')
#     wxBtn?.addEventListener 'click',->
#       #post to server
#       xmlhttp = new XMLHttpRequest()
#       data = document.querySelector('#share-url').textContent?.split('?')[1]
#       updateUrl = RMSrv.origin() + '/event/shared?' + data
#       xmlhttp.open "POST",updateUrl, true
#       xmlhttp.send()
#       RMSrv.share 'wechat-moment'
#     fbBtn?.addEventListener 'click',-> RMSrv.share 'facebook-feed'
#     RMSrv.onReady ->
#       wechatInstalled = (installed)->
#         if installed
#           wxBtn?.style?.display = 'block'
#           fbBtn?.style?.display = 'none'
#         else
#           btn?.style?.display = 'none'
#           fbBtn?.style?.display = 'block'
#       wechatNotInstalled = (reason)-> debug reason
#       if Wechat?
#         Wechat.isInstalled wechatInstalled,wechatNotInstalled
#       else
#         wechatInstalled true
#     null

# # APP 'spevent'
# #
# #
# # GET ':evtid', (req, resp)->
# #   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
# #     processSpEventReq req, resp, user, (cfg)->
# #       resp.ckup 'ldrawPage', cfg, '_', {cordova:true}
# #
# # POST 'bid', (req, resp)->
# #   fnError = (err)->
# #     resp.send {ok:0, e:err}
# #   price = req.param 'price'
# #   evtid = req.param 'evtid'
# #   mbl   = req.param 'mbl'
# #   return fnError 'Need input price' unless price
# #   return fnError 'Number only' if isNaN(parseFloat(price))
# #   UserModel.appAuth {req,resp},(user)->
# #     return fnError 'Need Login' unless user
# #     #inset to db
# #     #findOne check experied, update
# #     # update mbl?
# #     update2 = {mbl:mbl}
# #     updateUser req, update2, (err,nUser)->
# #       if err then console.error err
# #       update = {$set:{}}
# #       key = 'bidUsr.'+user._id+'.'
# #       update.$set[key+'ts'] = new Date()
# #       update.$set[key+'bid'] = parseFloat(price) or 0
# #       update.$set[key+'nm_zh'] = user.nm_zn
# #       update.$set[key+'nm_en'] = user.nm_en
# #       update.$set[key+'avt'] = user.avt
# #       update.$set[key+'eml'] = user.eml
# #       update.$set[key+'mbl'] = mbl or user.mbl
# #       SpEvents.findOneAndUpdate {_id:evtid}, update, {upsert:false, returnDocument:'after'}, (err, ret)->
# #         console.error if err
# #         # console.log price
# #         # console.log evtid
# #         return resp.send {ok:1, msg:'åˆ†äº«ç»™å¥½å‹ï¼Œæé«˜èŽ·å¥–å‡ çŽ‡'}
