# normalizeVer = DEF 'normalizeVer'
conf = CONFIG(['share'])
gShareHostNameCn = conf.share.hostNameCn or 'realmaster.cn'
# gShareHost = conf.shareHost or 'www.realmaster.com'

phoneCntys = DEF 'phoneCntys'
setLang = DEF 'setLang'
getWXConfig = DEF 'getWXConfig'
robotPreventionSetup = DEF 'robotPreventionSetup'
UserModel = MODEL 'User'
getNewestCoreVer = DEF 'getNewestCoreVer'

Wecard = COLLECTION 'chome', 'wecard'
# TODO: get core version from config

#updateAppNotifier = "Update-App-Notifier"
#PLSWAIT updateAppNotifier
# User = COLLECTION 'chome', 'user'

{isEmail} = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'

# ADDPLUGIN 'needUpdateApp',->
#   if appVer = @cookies.apsv
#     if @isIOS()
#       return normalizeVer(appVer) < 3.2002 #3.0001 for after Beta
#     else if @isAndroid()
#       return normalizeVer(appVer) < 3.1
#   false

VIEW 'downloadSharingMeta',->
  div class:'WSBridge',style:'display:none',->
    if ('en' is @lang)
      share_title = (if @owner then (@owner.fnm + ' : ') else '') + \
        "RealMaster APP. Any time & Anywhere to find properties and schools around you"
      share_desc = "Your Property Search Starts Here：Any time & Anywhere to search properties and schools around you; Easy social sharing; Utility tools and calculators"
      share_url = "http://#{@req.hostname}/app-download?lang=en"
    else
      share_title = (if @owner then (@owner.fnm) else '') + \
        "房大师,手机找房App,快人一步房源全掌握随时随地找房（二手|出租|学区|新房|暗盘）,比官方房源更全更快！轻松简单选房，与家人朋友微信分享，全套买房工具（贷款|转让税|房屋面积测量|实时汇率"
      share_desc = "领先一步找房，二手|出租|学区|新房|暗盘，比官方房源更全更快！轻松简单选房，随时查看邻居上市房源，与家人朋友微信分享交流，全套实用买房工具。"
      share_url = "http://#{@req.hostname}/app-download?lang=zh-cn"
    if @owner?
      share_url = share_url +  "&uid=" + @owner.id
    if @share_url_append
      share_url += @share_url_append
    span id:'share-title',->
      text @share_title or share_title
    span id:'share-desc',->
      text @share_desc or share_desc
    span id:'share-url',->
      text share_url
    span id:'share-image',->
      text @owner?.avt or "http://#{@req.hostname}/img/logo.png"

#PROVIDE updateAppNotifier
APP '1.5'
APP 'api', true
POST 'isTaken', (req,resp)->
  findErr = (err)->
    ret = {ok:0}
    ret.err = err.toString()
    return resp.send ret

  fld = req.param 'fld'
  data = req.param 'data'

  if fld is 'eml'
    if data and isEmail(data)
      UserModel.findByEml data,{},(err,user)->
        findErr(err) if err
        # console.log user
        unless user
          return resp.send {ok:1, ret:0}
        else
          return resp.send {ok:1, ret:1}
    else
      return findErr "No Data or not email format!"
  else
    return findErr 'Error: not Supported type!'

APP 'getapp'
GET (req,resp)->
  extra = ''
  if m = req.url.match /getapp(\?.*)$/
    extra = m[1]
  resp.redirect '/app-download' + extra

APP 'app-download'





translate_ctnys = (req, ctnys) ->
  ret = []
  for i in ctnys
    tmp = {}
    tmp.cd = i.cd
    tmp.nm = req.l10n i.nm
    # tmp.nm = '+' + i.nm
    ret.push tmp
  # console.log ret
  return ret



VIEW 'inputAddrSMB', ->
  nav class: 'menu slide-menu-bottom smb-auto', id:'inputAddrSMB', ngShow:"dlInput", ->
    div id: 'input-pane', ngShow:'true', ngShow:"step === 1",->

      form name:'userForm', class:'inputs-wrapper', novalidate:'',->
        div class:"",->
          if @en
            text "Fill in your info to get access to all properties after RealMaster App installation."
          else
            text "填写信息"
        div style:" padding: 10px 0;",->
          input type:'text', name:'nm', placeholder:_('Full Name'), ngModel:"userData['nm']", ngRequired:"true", ngMinlength:'2'
          span class:"fa fa-check-circle-o pull-right icon-tip icon-tip-ok", ngShow:"userForm.nm.$valid && userForm.nm.$dirty  "
          span class:"fa fa-exclamation-circle pull-right icon-tip icon-tip-no", ngShow:"userForm.nm.$invalid && userForm.nm.$dirty || userForm.nm.$invalid && showErr"

        div style:"height: 85px;",->
          input type:'email', name:'eml',placeholder:_('Email'), ngModel:"userData['eml']", ngRequired:"true" #, onBlur:"checkEmail()"
          span class:"fa fa-check-circle-o pull-right icon-tip icon-tip-ok", ngShow:"userForm.eml.$valid && userForm.eml.$dirty && !emlTaken && !notValidEml"
          span class:"fa fa-exclamation-circle pull-right icon-tip icon-tip-no", ngShow:"userForm.eml.$invalid && userForm.eml.$dirty || emlTaken || userForm.eml.$invalid && showErr || notValidEml && userForm.eml.$dirty"
          div class:'err-tip', ngShow:"userForm.eml.$invalid && userForm.eml.$dirty || notValidEml && userForm.eml.$dirty",->
            text _ "Enter a valid email."
          div class:"err-tip", ngShow:"emlTaken",->
            if @en
              text "This email has already been used. Try again or skip registration and"
            else
              text "该邮箱已被注册，重新输入或"
            text " "
            a href:"#", ngClick:"step = 2",->
              if @en
                text "Download Now"
              else
                text "立即下载"
        div ->
          select ngOptions:"ctny.cd as ctny.nm for ctny in ctnys", ngModel:"userData['region']", ngRequired:"true", style:"", ngClass:"{'error' : !userData['region'] && userForm.mbl.$dirty }",->
            option value:'', ->
              if @en
                text  "Country/Region"
              else
                text "国家/地区"
        div class:'tel', style:" padding: 10px 0;",->
          input type:'number', class:'reg', ngModel:"userData['region']", ngClass:"{'focus':focused}", ngFocus:"focused = true", readonly:'', ngDisabled:'true'
          input type:'number', class:'mbl', name:"mbl", placeholder:_('Cell Phone'), ngFocus:"focused = true",  ngClass:"{'focus':focused}", ngModel:"userData['mbl']", ngRequired:"true", ngMinlength:'9'
          span class:"fa fa-check-circle-o pull-right icon-tip icon-tip-ok", ngShow:"userForm.mbl.$valid && userForm.mbl.$dirty"
          span class:"fa fa-exclamation-circle pull-right icon-tip icon-tip-no", ngShow:"userForm.mbl.$invalid && userForm.mbl.$dirty || userForm.mbl.$invalid && showErr"

      div class:"btns", ->
        div class:"btn btn-half btn-fill  btn-sharp btn-positive", ngClick:'nextStep(userForm.$valid)',->
          if @en
            text "Next"#,'step'
          else
            text "下一步"
        div class:"btn btn-half btn-fill btn-sharp cancle", ngClick:"share('hide')",->
          text _ "Cancel"

    div id:'download-pane', ngShow:"false", ngShow:"step === 2",->
      div class:"result-wrapper", ->
        div ngHide:"emlTaken",->
          if @en
            text "Congratulations! You are registered."
          else
            text "您已成功注册成为房大师会员!"
          text " "
          if @en
            text "After installation, login RealMaster:"
          else
            text ""
        div style:"background-color:#efefef", ngHide:"emlTaken",->
          div ->
            label ->
              text _ "Login ID"
              text ":"
            span class:"ret",->
              text "{{userData['eml']}}"
          div ->
            div ->
              label ->
                text _ "Password"
                text ":"
              span class:"ret",->
                text "{{userData['mbl']}}"
            div ->
              if @en
                text "Please remember your account info, you can change password anytime after login."
              else
                text "请记住以上用户账号信息，在登录后可以修改密码。"
        div style:" text-align: center;",->
          # ckup download button
          unless @req.isAndroid()
            text ckup 'downloadBtnIOSEn',{coreVer:@coreVer}
          unless @req.isIOS()
            if @req.isChinaIP() or @cn or @req.isWeChat()
              text ckup 'downloadBtnYYBEn', {fromApp:@fromApp, en:@en, coreVer:@coreVer}
            else
              text ckup 'downloadBtnAndroidEn', {fromApp:@fromApp, en:@en,  coreVer:@coreVer}
          # text ckup 'downloadVerEn'
        if @req.isWeChat()
          #show open in browser tip
          text ckup 'download-alt-google', {en:@en}
        else
          if (not (@req.isChinaIP() or @cn)) and not @req.isIOS()
            text ckup "cant-download-tip", {en:@en}
            # text ckup "download-alt-yyb", {en:@en}
          if @req.isAndroid()
            text ckup "download-alt-offcial", {en:@en,shareHostNameCn:@shareHostNameCn}

        # if @req.isAndroid()
        #   div ->
        #     text ckup 'androidDownloadDesc'

      div class:"btns", ->
        div class:"btn btn-long ", ngClick:"share('hide')",->
          text _ "Cancel"


VIEW 'downloadBtnIOSEn', ->
  appleLink = "https://itunes.apple.com/us/app/realmaster/id978214719?ls=1&mt=8"
  # if @req.isWeChat()
  #   appleLink = "https://a.app.qq.com/o/simple.jsp?pkgname=com.realmaster"
  if @fromApp
    aparam =  href: '#', onclick:"RMSrv.showInBrowser('#{appleLink}');"
  else
    aparam =  href: appleLink,target:"_blank"
  a class:'dl_btn_wrapper', aparam, ->
    div class: 'dl_btn_icon', ->
      i class: 'fa fa-apple', style: 'font-size: 39px; margin-left: 15px;'

    div class:'dl_btn_text', ->
      div style:"display: inline-block; text-align: left;",->
        span style:'font-size: 12px;',->
          if @en
            text 'Download on the'
          else
            text "苹果手机下载"
        br()
        span style: 'font-size:22px', ->
          if @en
            text 'App Store'
          else
            text "苹果应用商店"

  # text ckup 'downloadVerEn', {en:@en,  coreVer:@coreVer}


VIEW 'downloadBtnAndroidEn', ->
  if @fromApp
    aparam =  href: '#', onclick:'RMSrv.showInBrowser("https://market.android.com/details?id=com.realmaster");'
  else
    aparam =  href: 'https://market.android.com/details?id=com.realmaster',target:"_blank"
  a class: 'dl_btn_wrapper', aparam,->
    div class: 'dl_btn_icon', ->
      # i class: 'fa fa-android', style: 'font-size: 39px; '
      img src:"/img/download/Icon-Google-Play.png", style:"    width: 27px;
        height: 30px;
        margin: 6px 0 0 19px;"
    div class:'dl_btn_text',  ->
      div style:"display: inline-block; text-align: left;",->
        span style:'font-size: 12px;',->
          if @en
            text 'Get it on'
          else
            text "安卓手机下载"
        br()
        span style: 'font-size:22px', ->
          if @en
            text 'Google Play'
          else
            text "谷歌应用商店"


  # text ckup 'downloadVerEn', {en:@en, coreVer:@coreVer}

VIEW 'downloadBtnYYBEn', ->
  yyb = "https://a.app.qq.com/o/simple.jsp?pkgname=com.realmaster"
  unless @req.isMobile()
    yyb = "https://android.myapp.com/myapp/detail.htm?apkName=com.realmaster"
  if @fromApp
    aparam =  href: '#', onclick:"RMSrv.showInBrowser('#{yyb}');"
  else
    aparam =  href:yyb, target:"_blank" # TODO: try on WeChat
  a class: 'dl_btn_wrapper', style:"background-color:#82C93F", aparam,->

    div class: 'dl_btn_icon', ->
      # i class: 'fa fa-android', style: 'font-size: 39px;margin-left: -25px;'
      img src:"/img/download/icon-Tencent-App-store.png", style:"width: 27px;
        height: 27px;
        margin: 7px 0 0 5px;"
    div class:'dl_btn_text',  ->
      div style:"display: inline-block; text-align: left;",->
        span style:'font-size: 12px;',->
          if @en
            text 'Get it on'
          else
            text "安卓手机下载"
        br()
        span style: 'font-size:18px', ->
          if @en
            text 'Tencent App Store'
          else
            text "腾讯应用宝"

  # text ckup 'downloadVerEn', {en:@en, coreVer:@coreVer}

VIEW 'downloadVerEn', ->
  div class:"version", style: '', ->
    # unless @req.isAndroid()
    span style: 'display:inline-block;  width:50%; vertical-align: super;text-align: center;', ->
      strong ->
        ver = @coreVer or '5.6.3'
        if @req?.isIOS()
          ver = @coreVer or '5.6.3'
        if @en
          text "Version: #{ver}"
        else
          text "版本：#{ver}"
    # unless @req.isIOS()
    #   span style: 'display:inline-block;  width:50%; vertical-align: super;text-align: center;', ->
    #     strong ->
    #       text 'Version: 3.0.0'

VIEW 'androidDownloadDesc', ->
  strList = [
    '如果您无法从上面的按钮正常打开Google Play，可直接从下面的链接下载房大师安卓APP最新版本：',
    "立即下载",
    '如果您无法从上面的按钮正常打开Google Play，请用手机上的二维码扫描软件或者微信直接扫描下面的二维码下载房大师APP，或用手机浏览器访问：'
    "http://#{@req.hostname}/app-download?lang=zh-cn"
  ]
  strListEn = [
    'If you can not open Google Play, you can download RealMaster APP for Android here: '
    "Free Download"
    'If you can not open Google Play, please use a QR-Code scan tool in your mobile phone or Wechat to scan following QR-Code,
      or use your mobile browser to open following URL:'
    "http://#{@req.hostname}/app-download"
  ]
  if @en
    strList = strListEn
  div id:'android-desp',style: '', ->
    p ->
      if (@req.getDevType() is 'app') and @req.isAndroid()
        text strList[0]
        a onclick:"RMSrv.showInBrowser('https://dl.#{@shareHostNameCn}/RealMaster.apk');", style:'color:blue;',->
          text strList[1]
        div style:'text-align:center; display:none;',id:'qrcode',->
            img src:'/img/app-download.png'

      else if @req.isAndroid() #or @req.isIOS()
        text strList[0]
        a href:"https://dl.#{@shareHostNameCn}/RealMaster.apk",->
          text strList[1]
        div style:'text-align:center; display:none;',id:'qrcode',->
          img src:'/img/app-download.png'

      else if @req.isIOS()

      else
        text strList[2]
        text strList[3]
        div style:'text-align:center', id:'qrcode',->
            img src:'/img/app-download.png'

VIEW 'userBriefInfo',->
  if @owner
    div class:'user-brief-info',->
      div class:'pic',->
        img src:(@owner.avt or "/img/logo_s.png"),alt:@owner.fnm or @owner.id

      div class:'desc', ->
        div class:'info-title', ->
          text @owner.fnm
          img src:'/img/realtor.png'
          br()
          span ->
            text @owner.cpny_zh or @owner.cpny_en or @owner.cpny
        p class:'info-contents' ,->
          # text "安装完成后, 搜索 " + (@owner.mbl or '') + " 或者 " + @owner.eml + " 来关注."
          i class:"fa fa-phone"
          text (@owner.mbl or '')
          if @owner.mblalt
            text ' 或者 '
            text @owner.mblalt
          br()
          i class:"fa fa-envelope"
          text @owner.eml
          br()
          i class:"fa fa-wechat"
          text @owner.wx

VIEW 'userBriefInfoEn',->
  if @owner
    div class:'user-brief-info',->
      div class:'pic',->
        img src:(@owner.avt or '/img/logo_s.png'),alt:@owner.fnm or @owner.id

      div class:'desc', ->
        div class:'info-title', ->
          text @owner.fnm
          img src:'/img/realtor.png'
          br()
          span ->
            text @owner.cpny_en or @owner.cpny_zh or @owner.cpny
        p class:'info-contents' , ->
          # text "After installation, search " + (@owner.mbl or '') + " or " + @owner.eml + " to follow me."
          i class:"fa fa-phone"
          text (@owner.mbl or '')
          if @owner.mblalt
            text ' or '
            text @owner.mblalt
          br()
          i class:"fa fa-envelope"
          text @owner.eml
          br()
          i class:"fa fa-wechat"
          text @owner.wx


VIEW "downloadNow", ->
  div class: 'dl_btn_wrapper dl-now',  ->
    a ngClick:"showSMB()", class:'dl_btn_link', ->
      span style:'font-size: 18px; color:white; ',->
        if @en
          text 'Download Now'
        else
          text "立即下载"

VIEW "downloadWechatAuth", ->
  div class: 'dl_btn_wrapper dl-now',  ->
    a href:"/wechatauth/web?uid=#{@uid or ''}", class:'dl_btn_link', ->
      span style:'font-size: 18px; color:white; ',->
        if @en
          text 'Download Now'
        else
          text "立即下载"

VIEW 'cant-download-tip', ->
  div class:"align-left cant-download-tip",->
    if @en
      text "Can't download RealMaster for Android from Google Play? Choose one of following ways to download:"
    else
      text "无法从谷歌应用商店下载房大师安卓版？选择以下一种方式下载："

VIEW 'download-alt-yyb',->
  link = "https://a.app.qq.com/o/simple.jsp?pkgname=com.realmaster"
  unless @req.isMobile()
    link = "https://android.myapp.com/myapp/detail.htm?apkName=com.realmaster"
  if @fromApp
    aparam =  href: '#', onclick:"RMSrv.showInBrowser('#{link}');"
  else
    aparam =  href:link
  div class:"align-left download-alt",->
    a aparam, ->
      img src:"/img/icon_QQ_Appstore.png", class:"download-alt-icon", style:""
      if @en
        text "Tencent App Store"
      else
        text "腾讯应用宝"

VIEW 'download-alt-offcial',->
  link = "https://dl.#{@shareHostNameCn}/RealMaster.apk"
  if @fromApp
    aparam =  href:'#', onclick:"RMSrv.showInBrowser('#{link}');"
  else
    aparam =  href:link
  div class:"align-left download-alt",->
    a aparam, ->
      img src:"/img/icon_download.png", class:"download-alt-icon", style:""
      if @en
        text "Official Website"
      else
        text "官网直接下载"

VIEW 'download-alt-google',->
  div class:"align-left cant-download-tip",->
    if @en
      text "Download RealMaster from "
      # a href:"http://market.android.com/details?id=com.realmaster", style:"color:#FB0000",->
      b ->
        text "Google Play or Official Website"
      text "? Click up-right corner WeChat menu and select 'Open in Browser'."
    else
      text "要从"
      # a href:"http://market.android.com/details?id=com.realmaster", style:"color:#FB0000",->
      b ->
        text "谷歌应用商店或官网"
      text "下载房大师? 点击右上角的微信菜单，然后选择'在浏览器中打开'."

# according to isWeChat and isMobile and is IOS give different btns
VIEW "downloadBtnControl", ->
  div class:'dl_wrapper', ->
    if @req.isWeChat() and @uid
      text ckup 'downloadWechatAuth', {fromApp:@fromApp, en:@en, uid:@uid}
    #wechat no uid
    else if @req.isWeChat() and not @uid #@req.isWeChat()
      if @req.isAndroid()
        #show tecent download btn
        # text ckup 'downloadBtnYYBEn', {fromApp:@fromApp, en:@en, coreVer:@coreVer}
      else
        text ckup 'downloadBtnIOSEn', {fromApp:@fromApp, en:@en, coreVer:@coreVer}
    #has uid
    else if @uid and not @fromApp
      text ckup 'downloadNow', {en:@en}
    #in browser or APP
    else
      div class:"btn-inline-wrapper",->
        div ->
          if @req.isIOS()
            text ckup 'downloadBtnIOSEn', {fromApp:@fromApp, en:@en,  coreVer:@coreVer}

        div class:'second-line',->
          if @req.isAndroid()
            if @req.isChinaIP() or @cn
              #show yyb
              # text ckup 'downloadBtnYYBEn', {fromApp:@fromApp, en:@en, coreVer:@coreVer}
            else
              text ckup 'downloadBtnAndroidEn', {fromApp:@fromApp, en:@en, coreVer:@coreVer}

      if (@req.isMobile() and not @uid) or (@uid and @fromApp)   #and not @fromApp
        if (not (@req.isChinaIP() or @cn )) and not @req.isIOS()
          text ckup "cant-download-tip", {en:@en}
          # text ckup "download-alt-yyb", {en:@en, fromApp:@fromApp}
        if @req.isAndroid()
          text ckup "download-alt-offcial", {en:@en, fromApp:@fromApp, shareHostNameCn:@shareHostNameCn}
      unless @req.isMobile()
        if @req.isChinaIP() or @cn
          div class:"align-left", style:"margin-top:30px;",->
            img src:"/img/icon_download.png", class:"download-alt-icon", style:""
            if @en
              text "You can also open '"
            else
              text "您也可以选择在手机浏览器中打开 "
            b -> "https://#{@shareHostNameCn}/app-download"
            if @en
              text "' in your mobile browser to download."
            else
              text " 来下载"
        else
          text ckup "cant-download-tip", {en:@en}
          # text ckup "download-alt-yyb", {en:@en}
          text ckup 'download-alt-offcial', {en:@en,shareHostNameCn:@shareHostNameCn}
          div class:"align-left",->
            img src:"/img/icon_download.png", class:"download-alt-icon", style:""
            if @en
              text "Open '"
            else
              text "在手机浏览器中打开 "
            b -> "https://#{@shareHostNameCn}/app-download"
            if @en
              text "' in in your mobile browser to download."
            else
              text " 来下载"

    # if @req.isWeChat() #and not @uid
    #   div style:"color:red;font-weight:bold;",-> 'You are in WeChat now. Please click up-right corner WeChat menu, then choose open this page in a mobile browser to download RealMaster APP.'
    # else
    if @req.isWeChat() and @req.isAndroid() and  not @uid
      text ckup 'download-alt-google', {en:@en}


    # if not @req.isWeChat() and not @uid
    #   text ckup 'androidDownloadDesc', {en:@en}

   # unless @req.isWeChat() #and @uid
   #  div class:'dl_wrapper',  ->
   #    unless @req.isAndroid() or @uid
   #      text ckup 'downloadBtnIOSEn', {fromApp:@fromApp}

   #    unless @req.isIOS() or @uid
   #      text ckup 'downloadBtnAndroidEn', {fromApp:@fromApp}

   #    # if has uid let user register
   #    if @uid and not @fromApp #from outside app and has uid (from agent)
   #      div class: 'dl_btn_wrapper dl-now',  ->
   #        a ngClick:"showSMB()", class:'dl_btn_link', ->
   #          span style:'font-size: 18px;',->
   #            text _ 'Download Now'

   #    unless @uid
   #      text ckup 'downloadVerEn'


   #  if @req.isWeChat() #and not @uid
   #    div style:"color:red;font-weight:bold;",-> 'You are in WeChat now. Please click up-right corner WeChat menu, then choose open this page in a mobile browser to download RealMaster APP.'
   #  else
   #    if not @req.isWeChat() and not @uid
   #      text ckup 'androidDownloadDesc', {en:1}


#TODO: logic here need refactor, see user login
showDownloadPage = (req,resp,user)->
  lang = setLang req
  view = 'downloadPage'
  action = req.param 'action'
  uid = req.param 'uid'
  cn = if req.param 'cn' then 1 else 0
  evtid = req.param 'evtid'
  switch lang
    when 'zh','zh-cn'
      view = 'downloadPage'
    when 'en'
      view = 'downloadPageEn'
    else
      view = 'downloadPageEn'
  coreVer = getNewestCoreVer(req)
  ctx = {
    fromApp:(req.getDevType() is 'app'),
    action:action ,uid:uid, cn:cn, owner:null,
    ctnys:translate_ctnys(req, phoneCntys),
    coreVer:coreVer,
    shareHostNameCn:gShareHostNameCn,
  }

  getUser = (id,cb)->
    return UserModel.findById id,{projection:UserModel.DOWNLOAD_FIELDS},(err,owner)->
      if err
        console.error err
        # TODO: if always return an object, the caller's old logic shall match,
        #             this seems to be a bug. Old code return null
        return cb null
      cb owner

  always = (cfg)->
    resp.ckup view,ctx,'_',{noAngular:false,noUserModal:true,wxcfg:cfg,noIndex:true}

  show = ->
    robotPreventionSetup req,(err,rp)->
      ctx.robotP = rp
      getWXConfig req,req.fullUrl(),(err,cfg)->
        # console.log ctx
        if evtid
          Wecard.findOneAndUpdate {_id:evtid, tp:'evtad'},{$inc:{"meta.vc": 1, "meta.vcd": 1}},{upsert:false,returnDocument:'after'},(err, ret) ->
            return always(cfg) if err or (not ret.value) or (ret.value.seq?.length < 2)
            ret = ret.value
            ctx.share_title = ret.meta.title
            ctx.share_desc = ret.meta.desc
            ctx.share_url_append = '&evtid=' + evtid
            ctx.disp_subtitle = ret.seq[0].m
            # ctx.disp_contents = ret.seq[1].m #used in middle page
            ctx.disp_feat     = ret.seq[2].m
            ctx.disp_overwrite = ret.seq[3]?.m
            always(cfg)
        else
          always(cfg)
        # use global layout always

  done = ->
    if (owner = ctx.owner)
      owner.fnm = libUser.fullNameOrNickname(lang,owner)
      if (owner.roles?.indexOf('realtor') >= 0)
        owner.avt2 = owner.avt #this user share-avt is avt, else logo
        return show()
      else if realtorUID = owner.flwng?[0]?.uid
        return getUser realtorUID,(nOwner)->
          if nOwner
            ctx.owner = nOwner
          else
            delete ctx.owner
          show()
      else # not realtor, didn't follow any one either
        delete ctx.owner
    show()

  willShowDl = ()->
    return true if uid
    if user?.id and req.hasRole('realtor')
      uid = user.id
      return true
    false
  if willShowDl() #or user?.flwng?[0]?.uid
    return getUser uid,(owner)->
      ctx.owner = owner if owner
      done()
  done()




# get view
# usage : ?lang=en/zh
#         &action=showSMB    # show bottom slide share menu
#         &uid=user.id       # show user info at bottom
GET (req,resp)->
  # if req.redirectHTTP()
  #   # console.log 'redirected https'
  #   return
  UserModel.appAuth {req,resp},(user)->
    showDownloadPage req,resp,user
GET 'en',(req,resp)-> resp.redirect '/app-download?lang=en'

# GET 'test',(req,resp)->
#   UserModel.appAuth {req,resp},(user)->
#     showDownloadPage req,resp,user
# GET 'en',(req,resp)-> resp.redirect '/app-download?lang=en'


VIEW 'downloadPageEn',->
  div ngApp:'appDl', ngController:'ctrlDl',->
    css '/css/dlPage.css'
    if @req.isMiniProgram() or (@req.param('src') is 'miniapp')
      div id:'wxBackWrapper',->
        # div id:'wxBackAngle',->
        div id:'wxBack', ngClick:'miniappBack()', ->
          div class:'back',->
            img src:'/img/download/back-icon.png'
          div ->
            div -> text 'Back to'
            div class:'app',-> text 'WeChat Mini Program'
    hclass = 'bar bar-nav'
    if not @fromApp
      hclass += ' bar-share'
    header class: hclass, ->
      if @fromApp
        a class: 'icon fa fa-back pull-left',
        ngClick:'goBack()', dataIgnore: 'push'
      if @owner
        aParam = href: 'javascript: void 0',
        ngClick:"switchLang('zh-cn', '#{@owner.id}')"
      else
        aParam = href: 'javascript: void 0',
        ngClick:"switchLang('zh-cn')"
      a class: ' icon  pull-right', aParam, style:'
        border-radius: 4px;
        height: auto;
        padding: 3px;
        ', ->
        text '中'
      h1 class: 'title',->
        if not @fromApp
          img src:'/img/download/RealMaster-web-logo.png'
        else
          text 'RealMaster'
          if @share_title
            text @share_title.substr(0,10)
    if @fromApp
      div class: 'bar bar-standard bar-footer', ->
        #a class: 'icon icon-compose pull-left'
        a class: 'icon icon-share pull-right',
        href:'javascript:void(0)',
        onclick: "RMSrv.share('show');",
        style:'color:#E03131'
    if @fromApp
      text ckup 'shareDialog2',{page:'dlpage', noLang:1, noSign:1, hideSplit:1, req:@req}

    text ckup 'inputAddrSMB', {en:1, cn:@cn, shareHostNameCn:@shareHostNameCn}
    text ckup 'downloadSharingMeta', {
      owner:@owner,lang:'en',share_title:@share_title,
      share_desc:@share_desc,
      share_url_append:@share_url_append
    }
    div id: 'dl-share-content', class: 'content',  ->
      if @disp_feat
        div class:'article-wrapper', ->
          text @disp_feat
      div id:'download-wrapper', ->
        div class:'download card-square', ->
          div class:'download-content', ->
            div class:'top',->
              img class:'logo', src:'/img/logo_s.png'
              h3 ->
                text 'FREE DOWNLOAD'
              # span class:'langSelect ', style:'text-align:left; margin-top:0px; font-size:12px; color:#666;',->
              #   text 'Multi-Language Support: '
              #   br()
              #   text 'English / '
              #   text 'Simplified Chinese / '
              #   text 'Traditional Chinese '
            # text ckup 'graySplit'
            text ckup 'downloadBtnControl', {uid:@uid, en:1, fromApp:@fromApp, cn:@cn, coreVer:@coreVer, shareHostNameCn:@shareHostNameCn}
            div style:'margin-top:10px;',->
              # div ->
              #   h3 ->
              #     text '下载房大师免费体验版'
          if @owner
            text ckup 'userBriefInfoEn',{owner:@owner , mode:'noAPI'}
      # div id:'download-imgbg',->
      #   img src:'/img/download/Download-Backgroud.png'
      if @req.param 'openapp'
        div style:'padding:10px;', ->
          a href:'#', onclick:"window.location='/open-app'+window.location.search", ->
            text 'If already Installed, Open In App'
      if @disp_overwrite
        div style:'padding: 10px;', ->
          text @disp_overwrite
      else
        div class:'description', ->
          div class:'feature-wrapper',->
            h3 ->
              text 'Key Features'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-01-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'REALTIME LISTING DATA'
              div class:'card-content', ->
                text ' - Listing synchronize over MLS in real time<br>'
                text ' - Residential and commercial properties for sale and rent<br>'
                text ' - Pre-construction, assignment and exclusive'

          div class:'card-square school-card', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-02-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'Anytime & Anywhere Search'
              div class:'card-content', ->
                text ' - Nearby search based on your location<br>'
                text ' - Sold price search（only available in GTA）<br>'
                text ' - Listing transaction history<br>'
          # text ckup 'hrDiv'
          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-03-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'Exclusive price evaluation and market analysis'
              div class:'card-content', ->
                text ' - Estimate and forecast property price<br>'
                text ' - Market analysis based on property type<br>'
                text ' - Community ranking based on average price and more conditions'

          # text ckup 'hrDiv'
          div class:'card-square follow-card', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-04-EN.png' #, style:'height: 246px;'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'School ranking & School boundary'
              div class:'card-content', ->
                text '- School ranking and scores<br>'
                text '- Listing search based on home school boundary<br>'
                text '- Two-way search between listing and school<br>'
          # text ckup 'hrDiv'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-05-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'One stop landlord service'
              div class:'card-content', ->
                text ' - Renovation and maintenance<br>'
                text ' - Property management service, more profitability less time <br>'
                text ' - Property service provider’s yellow page'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-06-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'Daily news pick'
              div class:'card-content', ->
                text ' - Real estate highlight and hot local news<br>'
                text ' - Air views and property reviews <br>'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-07-EN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text 'Realtor membership'
              div class:'card-content', ->
                text ' - One stop tools to improve working performance<br>'
                text ' - Ranking advertisement,  generate more leads<br>'
                text ' - Listing Promotion(MLS Listing, Pre-construction, Assignment and Exclusive listing)'

          div class:'download card-square bottom', ->
            div class:'download-content', ->
              div class:'top',->
                h4 ->
                  text 'Get started with our mobile app!'
                # div class:'desc',->
                #   text 'Search and GO!'
              text ckup 'downloadBtnControl', {uid:@uid, en:1, fromApp:@fromApp, cn:@cn, coreVer:@coreVer, shareHostNameCn:@shareHostNameCn}
              div style:'margin-top:20px;',->

          if @req.getDevType() is 'app'
            text ''
          else #if @req.isWeChat() or @req.isAndroid() or @req.isIOS()
            div style:'text-align:center; margin-top:40px',->
              text 'Scan QR-Code to share with friends '
            div style:'text-align:center; margin-top:20px',id:'qrcode',->
              img src:'/img/app-download.png'
            div style:'text-align:center; color:#888; font-size:18px;margin-top:20px',->
              text 'RealMaster'

          div style:'height:44px'

    div id: 'backdrop',
    class: 'backdrop',
    style: 'display:none; background-color: rgba(0, 0, 0, .8);'
  text ckup 'dlPageJs', {action:@action, uid:@uid, ctnys:@ctnys, owner:@owner, robotP:@robotP}







VIEW 'downloadPage',->
  div ngApp:'appDl', ngController:'ctrlDl',->
    css '/css/dlPage.css'
    if @req.isMiniProgram() or (@req.param('src') is 'miniapp')
      div id:'wxBackWrapper',->
        div id:'wxBack', ngClick:'miniappBack()', ->
          div class:'back',->
            img src:'/img/download/back-icon.png'
          div ->
            div -> text '返回'
            div class:'app',-> text '微信小程序'
        # div id:'wxBack', ngClick:'miniappBack()', -> text '返回微信小程序'
    hclass = 'bar bar-nav'
    if not @fromApp
      hclass += ' bar-share'
    header class: hclass, ->
      if @fromApp
        a class: 'icon fa fa-back pull-left',
        ngClick:'goBack()', dataIgnore: 'push'
      if @owner
        aParam = href: 'javascript: void 0;',
        ngClick:"switchLang('en', '#{@owner.id}')"
      else
        aParam = href: 'javascript: void 0;',
        ngClick:"switchLang('en')"
      a class: ' icon  pull-right', aParam, style:'
        border-radius: 4px;
        height: auto;
        padding: 2px;
        ', ->
        text "En"
      h1 class: 'title',->
        if not @fromApp
          img src:'/img/download/RealMaster-web-logo.png'
        else
          text "房大师 "
          if @share_title
            text @share_title.substr(0,10)

    if @fromApp
      div class: 'bar bar-standard bar-footer', ->
        #a class: 'icon icon-compose pull-left'
        a class: 'icon icon-share pull-right', href:'javascript:void(0)',onclick: "RMSrv.share('show');",style:'color:#E03131'
    if @fromApp
      text ckup 'shareDialog2',{page:'dlpage', noLang:1, noSign:1, hideSplit:1, req:@req}
      # text ckup 'shareDialog',{noLang:true}

    text ckup 'inputAddrSMB', {en:0, cn:@cn, shareHostNameCn:@shareHostNameCn}
    text ckup 'downloadSharingMeta', {
      owner:@owner,lang:'zh-cn'
      share_title:@share_title,
      share_desc:@share_desc,
      share_url_append:@share_url_append
    }

    div id: 'dl-share-content', class: 'content',  ->
      if @disp_feat
        div class:"article-wrapper", ->
          text @disp_feat
      div id:'download-wrapper', ->
        div class:'download card-square', ->
          div class:'download-content', ->
            div class:'top',->
              img class:'logo',src:'/img/logo_s.png'
              h3 ->
                text '免费下载'
              # span class:'langSelect ', style:'text-align:left; margin-top:0px; font-size:12px; color:#666;',->
              #   text '多语言支持: '
              #   text 'English / '
              #   text '简体中文 / '
              #   text '繁体中文 '
            # text ckup 'graySplit'
            text ckup 'downloadBtnControl', {uid:@uid, en:0, fromApp:@fromApp, cn:@cn, coreVer:@coreVer, shareHostNameCn:@shareHostNameCn}
            div style:'margin-top:10px;',->
              # div ->
              #   h3 ->
              #     text '下载房大师免费体验版'
          if @owner
            text ckup 'userBriefInfo',{owner:@owner ,mode:'noAPI'}
      if @req.param 'openapp'
        div style:'padding:10px;', ->
          a href:'#', onclick:"window.location='/open-app'+window.location.search", ->
            text '如果已经安装， 在APP内打开'
      if @disp_overwrite
        div style:'padding: 10px;', ->
          text @disp_overwrite
      else
        div class:'description', ->
          div class:'feature-wrapper',->
            h3 ->
              text '主要特点'
          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-01-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '实时权威房源早知道，比官方系统更快更全'
              div class:'card-content', ->
                text ' - 房大师房源实时更新，快人一步看到买到！<br>'
                text ' - 房源覆盖多伦多、温哥华、卡尔加里、渥太华等城市<br>'
                text ' - 二手房、出租房、学区房、新房楼花、商业地产<br>'
                text ' - 独家房源：房东直租、经纪代租、暗盘和楼花转让'
          div class:'card-square school-card', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-02-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '随时了解挂盘和成交价格，买房卖房更有数'
              div class:'card-content', ->
                text ' - 随时随地查看周边上市房源和售出房源，身边房源全掌握！<br>'
                text ' - 查询售出房源成交价格（仅大多地区房源）<br>'
                text ' - 查看房源过往交易历史<br>'
          # text ckup 'hrDiv'
          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-03-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '独家房产估价和行情大数据，掌握房市趋势'
              div class:'card-content', ->
                text ' - 评估和预测房产出售价格<br>'
                text ' - 支持各类房型的出售和出租行情分析<br>'
                text ' - 按均价、涨幅等指标进行社区排名'

          # text ckup 'hrDiv'
          div class:'card-square follow-card', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-04-CN.png' #, style:'height: 246px;'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '学校排名&学校边界，找学区房更简单'
              div class:'card-content', ->
                text '- 提供大多和大温地区中小学最新排名和历史成绩<br>'
                text '- 按学区范围找房<br>'
                text '- 找房子查学区，选学校找房子双向互查<br>'
          # text ckup 'hrDiv'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-05-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '一站式业主服务，房产管理更轻松'
              div class:'card-content', ->
                text ' - 装修和维修服务，3项免费+5重保障<br>'
                text ' - 长租和短租托管服务，更少精力更多收益 <br>'
                text ' - 房产服务商家黄页'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-06-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '每日新闻精选，参与话题讨论'
              div class:'card-content', ->
                text ' - 每日精选房产市场动态和社会热点新闻<br>'
                text ' - 发表观点和房源点评 <br>'

          div class:'card-square', ->
            div class: 'row-pic', ->
              img src: '/img/download/Feature-07-CN.png'
            div class:'desc-wrapper',->
              div class: 'card-title', ->
                text '地产经纪专属特权，提升核心竞争力'
              div class:'card-content', ->
                text ' - 经纪工具，全面提升工作效率<br>'
                text ' - 经纪排名，有效获取潜在客户机会<br>'
                text ' - 推广MLS Listing、新房、楼花转让和暗盘等各类房源'

          div class:'download card-square bottom', ->
            div class:'download-content ', ->
              div class:'top',->
                h4 ->
                  text '找房就上房大师'
                # div class:'desc',->
                #   text 'Search and GO!'   {uid:@uid, en:0, fromApp:@fromApp, cn:@cn, coreVer:@coreVer}
              text ckup 'downloadBtnControl', {uid:@uid, en:0, fromApp:@fromApp, cn:@cn, coreVer:@coreVer, shareHostNameCn:@shareHostNameCn}
              div style:'margin-top:20px;',->

          if @req.getDevType() is 'app'
            text ''
          else #if @req.isWeChat() or @req.isAndroid() or @req.isIOS()
            div style:'text-align:center; margin-top:40px',->
              text '分享给好友扫描二维码'
            div style:'text-align:center; margin-top:20px',id:'qrcode',->
              img src:'/img/app-download.png'
            div style:'text-align:center; color:#888; font-size:18px;margin-top:20px',->
              text '房大师'
          div style:'height:44px'



    div id: 'backdrop',
    class: 'backdrop',
    style: 'display:none; background-color: rgba(0, 0, 0, .8);'
  text ckup 'dlPageJs', {action:@action, uid:@uid, ctnys:@ctnys, owner:@owner, robotP:@robotP}



VIEW 'hrDiv', ->
  div style: 'border-bottom: 2px solid #F0EEEE; margin-top:10px;margin-bottom:10px;'

VIEW 'graySplit',->
  div style: 'border-bottom: 2px solid #EEE; margin-top:10px; margin-bottom:10px;'

VIEW 'dlPageJs', ->
  coffeejs {
    vars: {
      action:@action,
      uid:@uid,
      ctnys:@ctnys,
      ownerid:@owner?._id,
      hk:@robotP.hk,
      hv:@robotP.hv
    }
  },->
    null
    null
  js '/js/dlPageJs.min.js'
