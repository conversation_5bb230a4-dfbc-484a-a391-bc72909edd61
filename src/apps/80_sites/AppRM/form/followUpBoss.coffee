helpers = INCLUDE 'lib.helpers'
followUpBossLib = INCLUDE 'libapp.followUpBoss'
functionHelper = INCLUDE 'lib.helpers_function'
config = CONFIG(['followUpBoss'])
UserModel = MODEL 'User'
FormInputModel = MODEL 'FormInput'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'
SysNotifyModel = MODEL 'SysNotify'
if not config.followUpBoss
  throw Error('followUpBoss config required')

xSystemKey = config.followUpBoss.xSystemKey
xSystem = config.followUpBoss.xSystem
fubToken = config.followUpBoss.token

APP '1.5'
APP 'fub', true

#1.5/fub/peopleUpdated

###
Example input
{"eventId":"7b57bca9-d2a9-4ce0-a73f-d615bdab8ba4","eventCreated":"2022-03-10T16:18:24+00:00",\
"event":"peopleCreated","resourceIds":[24],"uri":"https://api.followupboss.com/v1/people?id=24"}
header:
fub-signature:c1a76aeb2c84e5217755a86237d76bdef311de067168732bd6d8a84a4c40d6a5


 {"eventId":"7c7ea3a9-9af7-4f8e-84fc-b4b448a8d592","eventCreated":"2022-04-25T14:20:40+00:00",\
 "event":"peopleCreated","resourceIds":[11],"uri":"https:\/\/api.followupboss.com\/v1\/people?id=11"}

###
POST 'peopleUpdated',(req, resp) ->
  fubSignature = req.headers['fub-signature']
  body = req.body
  rawBody = req.rawBody

  #'user-agent': 'Follow Up Boss', check user agent
  debug.debug 'rawBody',rawBody
  isFromFollowUpBoss =  followUpBossLib.isFromFollowUpBoss \
    {jsonBody:rawBody,  fubSignature, xSystemKey}
  debug.debug 'isFromFollowUpBoss',  isFromFollowUpBoss
  if not isFromFollowUpBoss
    debug.error 'post is not from followUpBoss', body
    return resp.send {ok:0, err: MSG_STRINGS.ACCESS_DENIED}
  
  try
    await dealWithWebHookEvent body
    return resp.send {ok:1}
  catch err
    debug.error err
    return resp.send {ok:0,err}

###
deal with web hook event, set flwngRm for user
检查用户，在app里设置一个黑名单，不能分给_dev group,sales group etc
eg: batch update
{"eventId":"5575b3a2-d372-4f6e-a7ea-72232079f936",
"eventCreated":"2022-04-25T13:56:15+00:00","event":"peopleUpdated",
"resourceIds":[9,8],"uri":"https:\/\/api.followupboss.com\/v1\/people?id=9,8"}
NOTE: see https://docs.followupboss.com/reference/people-get
Events: https://docs.followupboss.com/reference/events-post
###
dealWithWebHookEvent =(event)->
  await FormInputModel.saveFubWebhookEventAsync(event)
  url = event.uri
  people = await followUpBossLib.getPeopleByUrlAsync {url,fubToken,xSystem}
  # save people in the event
  debug.debug 'people',people
  await FormInputModel.saveFubEventDetailAsync(event.eventId,people)
  for person in people
    #update user flwng
    debug.debug 'processing', person
    if not person.assignedUserId
      debug.error 'No assignedUserId',assignedUserId
      continue
    #检查role
    assignedUserId = person.assignedUserId
    projection = {_id:1}
    debug.debug 'assignedUserId',assignedUserId
    agent = await UserModel.findUserByFubAgentIdAsync \
      assignedUserId, {projection}
    if agent?.roles and ('_crmNoAssign' in agent.roles)
      await UserModel.removeUserFlwngRmByFubId {fubId:person.id}
    else if agent?._id
      await UserModel.setFlwngRmByFubIdAsync \
      {fubId:person.id,rmAgentId:agent._id}
    else
      errorMsg = "No user found for assignedUserId: #{assignedUserId}, name: #{person.name}, email: #{person.emails?[0]?.value}, assignedTo: #{person.assignedTo}"
      debug.error errorMsg
      fubAgentId = assignedUserId
      SysNotifyModel.notifyAdmin "#{errorMsg} Instruction: in app go to more page, then select Manage FollowUp Agent, sync one user with fubAgentId: #{fubAgentId}"
      continue

    
###
#1.5/fub/getFubAgentByFubEml
###
POST 'getFubAgentByFubEml',(req, resp) ->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    fubEml = req.body.fubEml
    if not fubEml
      return resp.send {ok:0, err:'no fubEml'}
    try
      agents = await followUpBossLib.getFubAgentByFubEml {fubToken,fubEml}
      # debug.debug 'agent',agents
      return resp.send {ok:1, agents:agents}
    catch error
      debug.error error
      return resp.send {ok:0, err: error}

###
#1.5/fub/getFubPeopleByFubId
###
POST 'getFubPeopleByFubId',(req, resp) ->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    fubId = req.body.fubId
    if not fubId
      return resp.send {ok:0, err:'no fubId'}
    try
      debug.debug 'fubId',fubId
      user = await followUpBossLib.getFubPeopleByFubId {fubToken,fubId}
      debug.debug 'getFubPeopleByFubId',user
      showFields = ['id','created','name','email','source','createdVia','updated',
      'assignedUserId','assignedTo','tags','emails','phones']
      ret = {}
      for fld in showFields
        ret[fld] = user[fld]
      return resp.send {ok:1, user:ret}
    catch error
      debug.error error
      return resp.send {ok:0, err: error}



###
example agent info from fub
{
  id: 5,
  created: '2022-02-10T14:24:58Z',
  updated: '2022-05-11T01:10:10Z',
  name: 'Fred Xiang',
  firstName: 'Fred',
  lastName: 'Xiang',
  email: '<EMAIL>',
  phone: '6472223733',
  role: 'Broker',
  status: 'Active',
  timezone: 'America/New_York',
  beta: true,
  picture: [],
  pauseLeadDistribution: false,
  lastSeenIos: '2022-04-22T16:41:47Z',
  lastSeenAndroid: null,
  lastSeenFub2: '2022-05-12T02:03:43Z',
  canExport: false,
  isOwner: false,
  groups: [ [Object] ],
  leadEmailAddress: '<EMAIL>'
}
set fubAgentId as unique, do not allow duplicate.
###
findFubAgentsByPage = ({offset,limit, uid})->
  idsFromFub = {}
  matchedByEml = []
  matchedByFubId = []
  matchedByFubIdButDiffEmail = []
  notMatched = []
  matchEmlButDiffFubId = []
  hasMore = true
  agents = await followUpBossLib.syncFubAgents {fubToken,offset,limit}
  if agents.length < 100
    hasMore = false
  else
    offset = offset + limit
  for agent in agents
    agent.email = agent.email.toLowerCase()
    idsFromFub[agent.id] = agent.email # people email is array
    projection = {_id:1,eml:1,fubEml:1,fubAgentId:1,fubId:1}
    agentInAppById = await UserModel.findByFubAgentIdAsync\
      (agent.id,{projection})
    #id 存在
    if agentInAppById
      if agentInAppById.eml isnt agent.email
        debug.warn 'user eml:',agentInAppById.eml,\
        ' not same as fub Eml:',agent.email
        matchedByFubIdButDiffEmail.push \
          {app:agentInAppById.eml[0],fub: agent.email}
        await UserModel.setFubToFixFlag agentInAppById.eml
      else
        matchedByFubId.push agent.email
      continue
    #id 不存在,自动匹配
    agentInAppByEmail = await UserModel.findByEmlAsync(agent.email,{projection})
    if not agentInAppByEmail # not found
      #save to unmatched_fub_users
      notMatched.push agent.email
      debug.error "agentInAppByEmail not found: #{agent.email}"
      continue
    #存在，检查有没有fubAgentId,有的话报错。
    fubAgentId = agentInAppByEmail?.fubAgentId
    if fubAgentId and \
    (fubAgentId isnt agent.id)
      matchEmlButDiffFubId.push {
        eml:agent.email,
        fubAgentIdInApp:fubAgentId,
        idFromFub:agent.id
        }
      await UserModel.setFubToFixFlag agentInAppByEmail.eml
      continue # do not auto correct.
    matchedByEml.push agent.email
    param = {
      eml: agent.email
      fubLeadEml: agent.leadEmailAddress
      fubEml: agent.email
      fubMbl: agent.phone
      fubAgentId: agent.id
      src: 'fubSyncRes'
      uid: uid
    }
    await UserModel.setUserFubAgentInfo(param)
  
  return  {offset,matchEmlButDiffFubId,total:agents.length,
  idsFromFub,matchedByEml,matchedByFubId,
  matchedByFubIdButDiffEmail,notMatched,hasMore}



###
#cases:
{
  "id": 1,
  "created": "2022-02-02T21:21:09Z",
  "updated": "2022-02-02T21:29:19Z",
  "createdVia": null,
  "lastActivity": "2022-05-17T04:55:14Z",
  "name": "Dan Corkill",
  "firstName": "Dan Corkill",
  "lastName": "",
  "stage": "Lead",
  "stageId": 2,
  "source": "Agent Generated",
  "sourceId": 10,
  "sourceUrl": "",
  "leadFlowId": null,
  "delayed": false,
  "contacted": 1,
  "price": 0,
  "assignedLenderId": null,
  "assignedLenderName": null,
  "assignedUserId": 1,
  "assignedPondId": null,
  "assignedTo": "Allen jiao",
  "tags": [
    "Follow Up Boss",
    "Import"
  ],
  "emails": [
    {
      "value": "<EMAIL>",
      "type": "work",
      "status": "Valid",
      "isPrimary": 1
    },
    {
      "value": "<EMAIL>",
      "type": "work",
      "status": "Valid",
      "isPrimary": 1
    }
  ],
  "phones": [
    {
      "value": "2183046145",
      "type": "test call / text",
      "status": "Not Validated",
      "isPrimary": 1,
      "normalized": "2183046145"
    },
    {
      "value": "(*************",
      "type": "work",
      "status": "Not Validated",
      "isPrimary": 1,
      "normalized": "8558889769"
    },
    {
      "value": "(*************",
      "type": "work",
      "status": "Not Validated",
      "isPrimary": 1,
      "normalized": "8556225311"
    }
  ],
  "addresses": [],
  "picture": {
    "small": "https://s.followupboss.com/pictures/dan.gif"
  },
  "socialData": {
    "name": "Follow Up Boss",
    "firstName": "",
    "lastName": "",
    "gender": "",
    "age": "",
    "location": "San Francisco",
    "company": "",
    "title": "",
    "bio": "",
    "topics": "Management, Real Estate, Foreclosures, Email Marketing, Marketing, Internet Marketing, Social Media, Entrepreneurship, Advertising, Business, Mortgages, Crm, Movies, Mailchimp",
    "facebook": "https://www.facebook.com/followupboss/",
    "twitter": "http://twitter.com/followupboss",
    "googleProfile": "",
    "googlePlus": "",
    "linkedIn": "https://www.linkedin.com/company/follow-up-boss"
  },
  "claimed": true,
  "dealStatus": "Active",
  "dealStage": "Buyers - Pending",
  "dealName": "My First Deal",
  "dealCloseDate": "2022-02-09 21:21:11",
  "dealPrice": 1400000,
  "firstToClaimOffer": false,
  "collaborators": [
    {
      "id": 1,
      "name": "Allen jiao",
      "assigned": true,
      "role": "Broker"
    }
  ],
  "teamLeaders": [],
  "pondMembers": []
},

people from fub
email duplicate, one user, more then one fubId
print agent and user info. Resolve confict.
Assigned in app != crm agent id
Count Crm user not in app
Count Crm user -> more then on app user same fubId
###
findFubPeopleByPage = ({emailToIdMap, offset,limit,uid})->
  idsFromFub = {}
  matchedByEml = []
  matchedByFubId = []
  notMatched = []
  matchEmlButDiffFubId = []
  matchedByFubIdButDiffEmail = []
  matchedByFubIdButDiffFlwngRm = []
  hasMore = true
  debug.debug 'offset,limit',offset,limit
  people = await followUpBossLib.syncFubPeople {fubToken,offset,limit}
  if people.length < 100
    hasMore = false
  else
    offset = offset + limit
  #用fub里的用户，逐一对应，检查app里是否存在
  #如果存在，是否一致，
  #如果不存在或者部分存在，自动管理缺失的用户
  for fubUser in people
    idsFromFub[fubUser.id] = [] # people email is array
    projection = {_id:1,eml:1,fubId:1,flwngRm:1}
    usersInAppByFubId = await UserModel.findByFubIdAsync\
      (fubUser.id,{projection})
    emailsMap = {}
    for obj in fubUser.emails
      emailsMap[obj.value.toLowerCase()] = 1
      emailToIdMap[obj.value.toLowerCase()] ?= []
      emailToIdMap[obj.value.toLowerCase()].push fubUser.id
    #id 存在
    debug.debug 'fubUser',fubUser,'emailsMap',emailsMap
    if fubUser.assignedUserId
      assignedUserInApp = await UserModel.findByFubAgentIdAsync\
        (fubUser.assignedUserId,{projection:{_id:1,eml:1}})
      debug.debug 'assignedUserInApp',assignedUserInApp
    if usersInAppByFubId?.length
      debug.debug 'usersInAppByFubId',usersInAppByFubId,\
        fubUser.emails,fubUser.id
      for u in usersInAppByFubId
        # user.eml should in fubUser.email.
        if emailsMap[u.eml] #found
          # check if user's followAgent is same with the fub assigned agent.
          # if not same, mark as error.
          if u.flwngRm?.uid and assignedUserInApp?._id and \
          (u.flwngRm?.uid.toString() isnt assignedUserInApp?._id.toString())
            matchedByFubIdButDiffFlwngRm.push {
              eml:u.eml
            }
            #flwngRm:u.flwngRm, assignedInCrm:assignedUserInApp
            await UserModel.setFubToFixFlag u.eml
          idsFromFub[fubUser.id].push u.eml
          delete emailsMap[u.eml]
          matchedByFubId.push u.eml
        else
          #a existing fubId is set to a user which is not in fub, should remove the fubId
          debug.warn 'matchedByFubIdButDiffEmail',u.eml
          await UserModel.setFubToFixFlag u.eml
          matchedByFubIdButDiffEmail.push u.eml
      if not Object.keys(emailsMap).length #全都已经存在。
        continue
    #emailsMap里剩下的是这个用户的其他的email
    #id 不存在,通过自动匹配,一个fubid可以有多个user。
    for eml of emailsMap
      userInAppByEmail = await UserModel.findByEmlAsync(eml,{projection})
      if not userInAppByEmail # not found
        #save to unmatched_fub_users
        notMatched.push eml
        continue
      #check if user has another fubId.如果该email有一个fubId, 由于没有在上一步检查出来，这个id
      #是一个已经被删除的id，需要重新建立关联关系。
      if userInAppByEmail.fubId and (userInAppByEmail.fubId isnt fubUser.id)
        matchEmlButDiffFubId.push {
          eml: eml,
          fubIdInApp: userInAppByEmail.fubId,
          idFromFub: fubUser.id
          }
        await UserModel.setFubToFixFlag userInAppByEmail.eml
        continue # do not auto correct.
      matchedByEml.push eml
      idsFromFub[fubUser.id].push eml
      #关联agent 必须存在，否则会出错，所以需要先同步agent。
      await UserModel.setUserFubInfo {
        eml:eml
        fubId: fubUser.id
        assignedUserId: fubUser.assignedUserId
        src: 'fubSyncRes'
        uid
      }

  return {
    offset,matchEmlButDiffFubId,total:people.length,
    idsFromFub,matchedByEml,matchedByFubIdButDiffEmail,\
    matchedByFubIdButDiffFlwngRm,
    matchedByFubId,notMatched,hasMore}

syncFubUsers = ({tp,ret,idsFromFub,emailToIdMap,user},cb)->
  err = null
  try
    hasMore = true
    offset = 0
    limit = 100
    # 异步处理
    while hasMore
      if tp is 'agent'
        pageResult = await findFubAgentsByPage({offset,limit, uid: user._id})
      else
        pageResult = await findFubPeopleByPage(\
          {emailToIdMap, offset,limit, uid: user._id})
      ret.total += pageResult.total
      if pageResult.idsFromFub
        idsFromFub = Object.assign idsFromFub, pageResult.idsFromFub
      hasMore = pageResult.hasMore
      offset = pageResult.offset
      
      for fld in ['matchedByFubIdButDiffFlwngRm','matchEmlButDiffFubId',\
      'matchedByEml','matchedByFubId',\
      'matchedByFubIdButDiffEmail','notMatched']
        if pageResult[fld]?.length
          ret[fld]?=[]
          ret[fld] = ret[fld].concat(pageResult[fld])
    #for cross check, check if user in app exists in fub
    #(eg:agent deleted in fub)
    userInAPP = await UserModel.getFollowUpCRMUsers {tp}
    for u in userInAPP
      if tp is 'agent'
        id = u.fubAgentId
      else
        id = u.fubId
      if not idsFromFub[id]
        ret.userInAppButNotFub ?=[]
        ret.userInAppButNotFub.push {eml:u.eml,id:id}
        await UserModel.setFubToFixFlag u.eml
    debug.debug 'emailToIdMap',emailToIdMap,Object.keys(emailToIdMap).length
    for eml,ids of emailToIdMap
      if ids.length>1
        ret.duplicatePeopleInFub ?=[]
        ret.duplicatePeopleInFub.push {eml: eml, ids: ids}
    return cb null,ret
  catch error
    debug.error error
    return cb error

POST 'syncFubUsers',(req, resp) ->
  resFunc = ({resp,respData})->
    resp.send respData
  done = helpers.callOnce resFunc
  tp = req.param 'tp'
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    ret = {total:0}
    idsFromFub = {}
    emailToIdMap = {}
    syncFubUsers {tp,ret,idsFromFub,emailToIdMap,user},(err,result)->
      return done {resp,respData:{ok:0, err: err}} if err
      return done {resp,respData:{ok:1,ret:result}}

    helpers.wait 25000,{resp,respData:{ok:1,ret:{wait:'The background is synchronizing, please wait for 5 minutes'}}},done
