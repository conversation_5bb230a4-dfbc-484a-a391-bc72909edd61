###
Internal page for showing statistics
Need '_admin' role
###
# isAllowed = DEF '_access_allowed'
prepSocket = DEF 'prepSocket'
helpers = INCLUDE 'lib.helpers'
statCollector = INCLUDE 'lib.statCollector'
libUser = INCLUDE 'libapp.user'

PnToken = COLLECTION 'chome', 'pn_token'
WeCard = COLLECTION 'chome', 'wecard'
SpEvents = COLLECTION 'chome', 'sp_events'
ProcessStatusCol =  COLLECTION('vow','processStatus')
collPriorityQueue = COLLECTION('rni', 'phoDownloadPriorityQueue')
# UserListings = COLLECTION 'chome', 'user_listing'
AdvertisementModel = MODEL 'Advertisement'
AdvertisementNewModel = MODEL 'AdvertisementNew'
Properties = MODEL 'Properties'
UserModel = MODEL 'User'
ADs = COLLECTION 'chome', 'ads'
# Splashscreen = COLLECTION 'chome', 'splash_screen'
debug = DEBUG()
formatDateTime = helpers.formatDateTime
WeCard.createIndex tp:1

notifyFns = null
MONITOR (req,cb)->
  return cb() unless notifyFns
  url = req.url
  ts = helpers.dateFormat new Date(),"MI:SS"
  uname = "0-User"
  if user = req.session?.get('user')
    # uname = if user.fn then user.fn + (user.ln or '') else user.id + " " + user.eml
    uname = if nm = libUser.fullNameOrNickname(req.locale(), user) then nm else user.id + " " + user.eml
  if sid = req.session?.id()
    uname = sid.substr(0,3) + ":" + uname
  ip = req.remoteIP()
  cb()
  for k,fn of notifyFns
    fn url,ts,ip,uname,req

# objectIdWithTimestamp = (timestamp)->
#     # Convert string date to Date object (otherwise assume timestamp is a date)
#     if 'string' is typeof timestamp
#         timestamp = new Date timestamp

#     # Convert date object to hex seconds since Unix epoch
#     hexSeconds = Math.floor(timestamp/1000).toString(16)

#     # Create an ObjectId with that hex timestamp
#     new Users.ObjectId hexSeconds + "0000000000000000"

sortor = (a,b)->
  if a.n < b.n
    return 1
  -1

getPnToken = (cb)->
  PnToken.aggregate [
    {$match:{ts:{$gt:new Date(Date.now() - 15 * 24 * 3600000)}}},
    {$group:{
      _id: {year:{$year:"$ts"},month:{$month:"$ts"},day:{$dayOfMonth:"$ts"}},
      v:{$sum:1},
      ios:{$sum:{$cond:[{$cmp:['ios',{$substr:["$_id",0,3]}]},1,0]}},
      cip:{$sum:{$cond:[{$cmp:['$cip',true]},1,0]}}
      }}
    ],{cursor:{batchSize:0}},(err,results_ts)->
      if err then console.error err
      if results_ts
        for n in results_ts
          n.n = n._id.year + "/" + d2(n._id.month) + "/" + d2(n._id.day) #helpers.dateFormat new Date(n.n),'YY/MM/DD'
          n.v = "#{n.cip}.#{n.v-n.cip}|#{n.ios}.#{n.v-n.ios}|#{n.v}"
          delete n._id
        results_ts.sort sortor
        results_ts.unshift {n:'as',v:"N.Ch|iOS.O|All"}
        cb results_ts
      else
        cb []

d2 = (n)-> if n < 10 then "0" + n else "" + n

getPnTokenLastAccess = (cb)->
  PnToken.aggregate [
    {$match:{lts:{$gt:new Date(Date.now() - 15 * 24 * 3600000)}}},
    {$group:{
      _id: {year:{$year:"$lts"},month:{$month:"$lts"},day:{$dayOfMonth:"$lts"}},
      v:{$sum:1},
      ios:{$sum:{$cond:[{$cmp:['ios',{$substr:["$_id",0,3]}]},1,0]}}
      cip:{$sum:{$cond:[{$cmp:['$cip',true]},1,0]}}
      }}
    ],{cursor:{batchSize:0}},(err,results_lts)->
      if err then console.error err
      if results_lts
        for n in results_lts
          n.n = n._id.year + "/" + d2(n._id.month) + "/" + d2(n._id.day) #helpers.dateFormat new Date(n.n),'YY/MM/DD'
          n.v = "#{n.cip}.#{n.v-n.cip}|#{n.ios}.#{n.v-n.ios}|#{n.v}"
          delete n._id
        results_lts.sort sortor
        results_lts.unshift {n:'as',v:"N.Ch|iOS.O|All"}
        cb results_lts
      else
        cb []

getWecard = (cb)->
  # WeCard.group ((doc)-> {n:doc.tp}),
  #   {tp:$ne:'user'},
  #   {total:0,shared:0,totalViewed:0,today:0,todayShared:0,todayTotalViewed:0},
  #   """
  #   function (d,c){
  #     var today=(d._id.getTimestamp().getTime() + 3600000*24) > Date.now();
  #     c.total++;
  #     c.shared += (d.meta.shr || 0);
  #     c.totalViewed += (d.meta.vc || 0);
  #     c.todayShared += (d.meta.shrd || 0);
  #     c.todayTotalViewed += (d.meta.vcd || 0);
  #     if(today){
  #       c.today++;
  #     };
  #   }
  #   """,
  #   (err,results)->
  WeCard.aggregate [
    {$match:{tp:$ne:"user"}},
    {$group:{
      _id: "$tp",
      total:{$sum:1},
      shared:{$sum:$ifNull:["$meta.shr",0]},
      totalViewed:{$sum:$ifNull:["$meta.vc",0]},
      todayShared:{$sum:$ifNull:["$meta.shrd",0]},
      todayTotalViewed:{$sum:$ifNull:["$meta.vcd",0]},
      #today:{$sum:}
      }}
    ],{cursor:{batchSize:0}},(err,results)->

      if err then console.error err
      if results
        ret = []
        #debug results
        for vc in results
          section = {n:"WeCard:#{vc._id}",v:[]}
          secData = []
          for k,v of vc
            if k is '_id' then continue
            secData.push {n:k,v:v}
          section.v = secData
          ret.push section
        #debug ret
        cb ret
      else
        cb []

getTotalMyListings = (cb)->
  Properties.countAllRMListings (err,c)->
    cb [{n:'Active',v:c}]

# NOTE: getData not used?
getData = (cb)->
  UserModel.getAreas (areaData)->
    UserModel.getLastAccess (ltsData)->
      UserModel.getRegisters (regData)->
        UserModel.getFollowers (flwngData)->
          UserModel.getRealtors (realtors)->
            #getViewCounts (res)->
            try
              ads = await AdvertisementNewModel.getADCounts('A')
            catch error
              debug.error error
              ads = []
            getTotalMyListings (mylistingCnt)->
              getPnToken (ptCnt)->
                getPnTokenLastAccess (ptData)->
                  #res.MyListing.unshift {n:"Total",v:mylistingCnt}
                  realtors.unshift {n:"PnToken",v:ptCnt}
                  data = [
                    {n:"Users",v:realtors}
                    {n:"Register",v:regData}
                    {n:"Not Registered",v:ptData}
                    {n:"Areas",v:areaData}
                    {n:"Last Access",v:ltsData}
                    {n:"Follow",v:flwngData}
                    #{n:"My Listing",v:res.MyListing}
                    #{n:"MLS Listings",v:res.MLSListing}
                    #{n:"WePages",v:res.WePage}
                  ]
                  data = data.concat ads
                  cb data
                  # getWecard (wecards)->
                  #   cb data.concat wecards

statCollector.addItem 'User',"Registers",(cb)-> UserModel.getRegisters (data)-> cb null,data
statCollector.addItem 'User',"Last Access",(cb)-> UserModel.getLastAccess (data)-> cb null,data
statCollector.addItem 'User',"Realtors",(cb)-> UserModel.getRealtors (data)-> cb null,data
statCollector.addItem 'User',"Areas",(cb)-> UserModel.getAreas (data)-> cb null,data
statCollector.addItem 'User',"Followers",(cb)-> UserModel.getFollowers (data)-> cb null,data
statCollector.addItem 'Push Notify',"Last Access",(cb)-> getPnTokenLastAccess (data)-> cb null,data
statCollector.addItem 'Push Notify',"Not Registered",(cb)-> getPnToken (data)-> cb null,data
statCollector.addItem 'Others',"WeCard",(cb)-> getWecard (data)-> cb null,data
statCollector.addItem 'Others',"My Listings(active)",(cb)-> getTotalMyListings (data)-> cb null,data
statCollector.addItem 'Others','AD Counts',(cb)->
# TODO: add properties aggregate, A/U by city/prov
  try
    data = await AdvertisementNewModel.getADCounts 'A'
  catch error
    debug.error error
    data = []
  finally
    cb null,data



SOCKET '/status',(socket)->
  prepSocket socket,(user)->
    unless UserModel.accessAllowed 'analytics',user
      #socket.disconnect() if socket.disconnect
      return
    socket.on 'statMenu',->
      stats = statCollector.getAllStat()
      data = {}
      for k,s of stats
        menu = {}
        for itemId,itemFn of s.items
          menu[itemId] = {id:itemId}
        data[k] = {id:k,n:s.n,menu:menu}
      socket.emit 'statMenu',data
    socket.on 'statItem',(itemKeys)->
      if sc = statCollector.getStatCollector itemKeys.secId
        if fn = sc.items[itemKeys.itemId]
          fn (err,v)->
            # console.log '+++++',itemKeys.itemId,v
            socket.emit 'statItem',{ts:itemKeys.ts,id:itemKeys.itemId,secId:itemKeys.secId,v:v,err:err}
    socket.on 'disconnect',->
      delete notifyFns[socket.id] if notifyFns
    rmSocketNotify = (url,ts,ip,uname,req)->
      log = {ip:ip,u:uname,v:url,ts:ts,m:req.method}
      #console.log log
      socket.emit 'statRealTime',log
    socket.on 'statRealTime',(isOn)->
      console.log "statRealTime: #{isOn}"
      if isOn
        notifyFns ?= {}
        notifyFns[socket.id] = rmSocketNotify
      else
        delete notifyFns[socket.id] if notifyFns

APP 'analytics'
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect '/1.5/index' unless UserModel.accessAllowed 'analytics',user
    resp.ckup 'statCollector',{}




###
Menu:
  secId:
  v: section
    n: section name
    menu:
      Id:
        id: id
        n: name
        v: []
          n: label
          v: value
          url: optional
RealTime:
  ip, u:user, v:url,ts:ts,m:method
###
VIEW 'statCollector',->
  div ngApp:'statApp',ngController:'statCtrl',class:'ng-cloak',->
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', href: '/1.5/index', dataIgnore: 'push'
      h1 class: 'title',-> _ 'RealMaster'
      span class: 'icon icon-refresh pull-right', ngClick:'reload()'
    div class: 'content',->
      div class: 'status',->
        h2 style:'text-align: center;',ngClick:'toggalMenu()',->
          'Conn:{{connMs}}ms Menu:{{menuMs}}ms' # on/off all menus
        ul class:'table-view',ngShow:'showMenu',->
          li class:'table-view-cell',style:'padding-right:10px;',ngRepeat:'(secId,sec) in data',->
            # section: name => on/off display
            h3 -> '{{sec.n}}'
            ul class:'table-view',->
              li class:'table-view-cell',style:'padding-right:10px;padding-left:0;',\
              ngRepeat:'(itemId,item) in sec.menu',->
                h4 ngClick:'toggalItem(secId,itemId)',->
                  span -> '{{item.id}}'
                  span ngIf:'item.ms',style:'float:right;',-> '{{item.ms}}ms'
                h4 ngIf:'item.err',style:'color:red;',-> '{{item.err}}'
                ul class:'table-view',ngShow:'item.v',-> # menu items
                  li class:'table-view-cell',style:'padding-right:5px;',\
                  ngRepeat:'itemDetail in item.v',->
                    img ngIf:'itemDetail.image',class:'',style:'width 100px;height:50px;', src:'{{itemDetail.image}}'
                    div class:'',style:'font-size: 12px; color: #666;',-> '{{itemDetail.n}}'
                    div class:'',style:'overflow-x: scroll;',-> '{{itemDetail.v}}'
                    # pre class:'',style:'',->
                    #   code -> '{{itemDetail.v}}'
                    a ngIf:'itemDetail.url',ngHref:'{{itemDetail.url}}',\
                      class:'pull-right btn',style:'margin: 0 100px;',-> '>'
      div class: 'realtime', -> # real Time users. On/Off
        style -> '''.opened {background-color:yellow;}'''
        h2 style:'text-align: center;',ngClick:'toggalRealTime()',->
          span -> 'Real Time '
          span ngShow:'showRealTime',-> '*'
        # list users
        ul class:'table-view',->
          li class:'table-view-cell',style:'padding-right:15px;',\
          ngRepeat:'(id,logs) in realtimeUsers',->
          # Click to open(ip/user)
            div ngClass:'{opened:logs.open}',ngClick:'rtuClick(id)',->
              '{{id}}({{logs.length}}){{logs[0].ts}}'
            div ngShow:'logs.open',->
              ul class:'table-view',->
                li class:'table-view-cell',style:'padding:0 15px;',ngRepeat:'item in logs',->
                  "{{item.ts | date : 'mm:ss'}} {{item.m}} {{item.v}}"
        # list ips
        ul class:'table-view',->
          li class:'table-view-cell',style:'padding-right:15px;',\
          ngRepeat:'(ip,logs) in realtimeIPs',->
            # Click to open(ip/user)
            div ngClass:'{opened:logs.open}',ngClick:'rtIpClick(ip)',->
              '{{ip}}({{logs.length}}){{logs[0].ts}}'
            div ngShow:'logs.open',->
              ul class:'table-view',->
                li class:'table-view-cell',style:'padding:0 15px;',ngRepeat:'item in logs',->
                  "{{item.ts | date : 'mm:ss'}} {{item.m}} {{item.v}}"
  js '/socket/socket.io.js'
  js '/js/statCollector.min.js'


APP 'analytics_spevent'
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect '/1.5/index' unless UserModel.accessAllowed 'analytics',user
    if role = req.param('r')
      return UserModel.getListByRole role,(err,ret)->
        resp.ckup 'analytics_spevent', {evt:JSON.stringify(ret,null,1)}
    SpEvents.findOne {}, {sort:{_id:-1}},(err, ret)->
      resp.ckup 'analytics_spevent', {evt:JSON.stringify(ret,null,1)}

VIEW 'analytics_spevent',->
  header class: 'bar bar-nav', ->
    a class: 'icon icon-left-nav pull-left', href: '/1.5/more', dataIgnore: 'push'
    h1 class: 'title',-> _ 'RealMaster'
    span class: 'icon icon-refresh pull-right', ngClick:"reload()"
  div class: 'content',->
    pre class:'wrapper', style:"font-size: 13px;",->
      code ->
        text @evt

APP 'online'
#TODO: maybe more funcs later
GET (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    SpEvents.findOne {}, {sort:{_id:-1}},(err, ret)->
      ret =
        db:1
        static:1
        server:1
      resp.send ret

APP 'batchanalytics'
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect '/1.5/index' unless UserModel.accessAllowed 'analytics',user
    opt = {l:[]}
    try
      opt.phoCount = await collPriorityQueue.countDocuments()
      opt.phoErrCount = await collPriorityQueue.countDocuments {err:{$ne:null}}
      opt.phoPriorityCount = await collPriorityQueue.countDocuments({priority:{$gte:210}})
      processStatusList = await ProcessStatusCol.findToArray()
      for i in processStatusList
        opt.l.push {
          _id:i._id,
          status:i.status,
          prevStatus:i.prevStatus,
          statClass:if /error/ig.test(i.status) then 'error' else '',
          prevClass:if /error/ig.test(i.prevStatus) then 'error' else '',
          nextTs:formatDateTime(i.nextTs or i._mt),
        }
    catch err
      debug.error err
      opt.err = err
    resp.ckup 'batchanalytics', opt

VIEW 'batchanalytics',->
  text """
  <style>
  .error {
    color: #e03131;
  }
  p {
    margin-bottom:3px;
  }
  </style>
  """
  header class: 'bar bar-nav', ->
    a class: 'icon icon-left-nav pull-left', href: '/1.5/more', dataIgnore: 'push'
    h1 class: 'title',-> _ 'RealMaster'
    span class: 'icon icon-refresh pull-right', ngClick:"reload()"
  div class: 'content',style:'padding-left:10px',->
    div ->
      label -> 'Photo Queue:'
      p -> text @phoCount
    div ->
      label -> 'Photo Priority>=210 Queue count:'
      p -> text @phoPriorityCount
    div ->
      label -> 'Photo Queue Error:'
      p -> text @phoErrCount
    for process in @l
      hr ->
      div ->
        label -> process._id
        p class:"#{process.statClass}",-> text "Status: #{process.status}"
        p class:"#{process.prevClass}",-> text "prev: #{process.prevStatus}"
        p -> text "next: #{process.nextTs}"

