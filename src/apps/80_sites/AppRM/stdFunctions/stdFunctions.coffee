###
Provide basic standard function features and HTTP GET support.

/1.5/stdfun
expected structure:
body:
  q:[]
    fn: function name
    p: parameters
    id: unique in this request. optional, will use 0+ index if omited
###
helpers = INCLUDE 'lib.helpers'
debug = DEBUG()

checkOptions = (opt,req,input,cb)->
  return cb() unless opt
  if opt.needLogin or opt.userObject
    if user = req.session.get('user')
      input.user ?= user
    if opt.needLogin and not user
      return cb MSG_STRINGS.NEED_LOGIN
  if opt.allowed
    if helpers.isArray opt.allowed
      for permission in opt.allowed
        unless req.isAllowed permission
          return cb MSG_STRINGS.ACCESS_DENIED
    else if 'string' is typeof opt.allowed
      unless req.isAllowed opt.allowed
        return cb MSG_STRINGS.ACCESS_DENIED
  if opt.needReq
    input.req ?= req
  cb()

callStdFn = (fnName,input,callback)->
  unless fnObj = GETSTDFN fnName
    return callback MSG_STRINGS.UNKNOWN_ITEM
  checkOptions fnObj.opt,@,input,(err)->
    return callback err if err
    fnObj.fn input,callback
ADDPLUGIN 'callStdFn',callStdFn

callStdFns = (inputs,cb)->
  # when input is not an array, use single function call method
  unless helpers.isArray inputs
    return @callStdFn inputs,cb
  req = @
  # when request inputs is an array
  index = 0
  retArray = []
  addResult = (q,err,ret)->
    ret = {fn:q.fn,id:(q.id or q._i),ret:ret}
    ret.err = err if err
    retArray.push ret
  doOne = (q,callback)->
    q._i = index++
    unless fn = GETSTDFN q.fn
      addResult q,MSG_STRINGS.UNKNOWN_ITEM
      return callback MSG_STRINGS.UNKNOWN_ITEM
    checkOptions fn.opt,req,q.p,(err)->
      if err
        addResult q,err
        return callback err
      fn.fn q.p,(err,ret)->
        addResult q,err,ret
        return callback()
  done = (err,rets)->
    cb err,retArray
  helpers.processArrayParallel inputs,doOne,done
ADDPLUGIN 'callStdFns',callStdFns

stdFunProcess = (req,resp,next)->
  return next() unless queries = (body = req.body)?.q
  req.callStdFns queries,(err,retObj)->
    if err
      debug.error 'callStdFns q=',queries,' err=',err
    try
      resp.send {r:retObj}
    catch e
      # console.log queries,retObj,e
      debug.error helpers.debugStringifyError retObj
      resp.send {r:[]}


APP '1.5'
POST 'stdfun', stdFunProcess
