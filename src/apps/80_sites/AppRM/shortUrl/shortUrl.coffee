UserModel = MODEL 'User'
ShortUrl = MODEL 'ShortUrl'
WeCardModel = MODEL 'Wecard'
{respError} = INCLUDE 'libapp.responseHelper'
libUser = INCLUDE 'libapp.user'
libWecard = INCLUDE 'libapp.wecard'
libProperties = INCLUDE 'libapp.properties'
{encodeObj2JsonString} = INCLUDE 'lib.helpers'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

debug = DEBUG()
MESSAGE_INFO = 'This share has not been clicked for a long time, and it has automatically expired.'
APP 's' #surl
# TODO: chrome open app a class: 'title', href:"intent://www.realmaster.cn/s/kWZk1myeis?lang=zh-cn&app=desktop#Intent;package=com.realmaster;scheme=https;end",->
# see: https://developer.chrome.com/docs/android/intents
process_listing_req  = DEF 'process_listing_req'
getWechatConfig = DEF 'getWechatConfig'
# processWecardReq = DEF 'process_wecard_req' 在wecard前加载，获取不到
responseFactory =
  favProps: (req, resp, data) ->
    resp.send { ok: 1, data }
  # get page for property share
  shareProp: (req,resp,data) ->
    req.query = Object.assign req.query,data
    process_listing_req {req,resp,redirectInapp:true}
  # get page for wecard share
  shareWecard: (req,resp,data) ->
    # 检查访问权限
    unless WeCardModel.checkAccessPermission({
      host: req.host,
      isWeChat: req.isWeChat(),
      isChinaIP: req.isChinaIP()
    })
      # return webErrPage req, resp, 'no id', 'NA'
      return resp.ckup 'generalError', {err_tran: req.l10n(MSG_STRINGS.CONTACT_SERVICES)}

    try
      result = await WeCardModel.getWecardDataForShareAsync {
        id:data.id,
        uid:data.uid,
        to:libProperties.get_shared_to_from_share(data.to)
      }
    catch error
      debug.error error,'data:',data
      return resp.ckup 'generalError', {err_tran:req.l10n(MSG_STRINGS.DB_ERROR)}
    wxconfig = getWechatConfig req,req.fullUrl()
    {view, layout, view_cfg, layout_cfg} = libWecard.getPageAndWecardDataForShare {
      hostname:req.hostname,
      lang:req.locale(),
      message:data,
      user:result?.user,
      wecard:result?.wecard,
      wxconfig
    }
    if view_cfg.cardUrl
      view_cfg.cardUrl = req.fullUrl(view_cfg.cardUrl)
    return resp.ckup view,view_cfg,layout,layout_cfg
      
# TODO: need develop frontend page and binding to respose function
# open short_url share link
GET ':id', (req, resp) ->
  UserModel.appAuth { req, resp }, (user) ->
    id = req.param 'id'
    doc = await ShortUrl.getById id
    if not doc
      return resp.ckup 'shareMessage', {msg:req.l10n(MESSAGE_INFO)}
    if not fn = responseFactory[doc.type]
      debug.error 'unknown type, doc:',doc
      return resp.ckup 'shareMessage', {msg:req.l10n(MSG_STRINGS.INTERNAL_ERROR)}

    devType = req.getDevType()
    isRealtor = req.hasRole 'realtor'
    try
      await ShortUrl.increaseClickCount {id, devType, isRealtor, user}
    catch err
      debug.error err,' doc:',doc
      return resp.ckup 'shareMessage', {msg:req.l10n(MSG_STRINGS.DB_ERROR)}
    # Error: https://www.realmaster.cn/s/XqxLpGPawQ?lang=en%E6%88%91%E6%9C%8B%E5%8F%8B%E8%A9%B1%E5%91%A2%E5%80%8B%E5%9C%B0%E9%BB%9E%E5%A5%BD%E6%96%B9%E4%BE%BF
    if lang = (req.param('lang') or req.param('locale'))
      if lang in LANGUAGE_LIST
        doc.data?.lang = lang
    return fn req, resp, doc.data

# TODO: implement checker function to check body data while create link
surlDataCheckerFactory =
  favProps: (data) ->
    return true

# POST ':type', (req, resp) ->
#   UserModel.appAuth { req, resp }, (user) ->
#     return respError { category: MSG_STRINGS.NEED_LOGIN, errorCode: 0, resp } unless user
#     type = req.param 'type'
#     checkerFn = surlDataCheckerFactory[type]
#     if not checkerFn or not checkerFn req.body
#       return { ok: 0, err: req.l10n(MSG_STRINGS.BAD_PARAMETER) }

#     surlData = { type, data: req.body, uid: user._id }
#     id = await ShortUrl.create surlData
#     url = "#{req.shareHost()}/surl/#{id}"
#     resp.send { ok: 1, url }

# TODO:favProps
TYPES = ['shareProp','shareWecard']
# get share statistics page,need login
GET 'share', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    isAdmin = req.isAllowed 'admin'
    isVipPlus = req.isAllowed 'vipPlus'
    unless isAdmin or isVipPlus
      return resp.redirect '/1.5/index'
    params = {}
    if not isAdmin
      params.uid = user._id
    try
      result = await ShortUrl.findAndCountRecords params
    catch err
      debug.error err,"uid:#{user._id},params:",params
      return resp.ckup 'generalError', {err_tran:req.l10n(MSG_STRINGS.DB_ERROR)}
    result.jsonTypes = encodeObj2JsonString(TYPES)
    result.jsonDocs = encodeObj2JsonString(result.docs)
    resp.ckup 'admin/shareCount',result

# NOTE:
###
# @description create short_url for share
# @param
  type body {
    type? : string #the type of shortUrl
    eml? : string #the email of user,search by regex
    ts? : date||string #ts condtion:ts(create time) should befroe input ts
    limit? : number
    needCount? : boolean #whether need a count for return
  }
# @return
  type {
    ok : number #0:error,1:success
    err : string # error message,only exists when ok is 0
    result : {
      docs : array #records of finding
      count? : number #the number of finding results by filter
      hasMore : boolean #the flag of have more records
    }
  }
###
POST 'share', (req, resp) ->
  if not params = req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    isAdmin = req.isAllowed 'admin'
    if not isAdmin
      params.uid = user._id
    try
      result = await ShortUrl.findAndCountRecords params
    catch err
      debug.error err,"uid:#{user._id},body:",params
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    resp.send {ok:1,result}