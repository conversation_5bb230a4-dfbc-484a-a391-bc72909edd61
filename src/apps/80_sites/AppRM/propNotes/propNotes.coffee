InRealNoteModel = MODEL 'InRealNoteInst'
PersonalNoteModel = MODEL 'PersonalNoteInst'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
Properties = MODEL 'Properties'
TokenModel = MODEL 'Token'
libUser = INCLUDE 'libapp.user'
libproperties = INCLUDE 'libapp.properties'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
{formatMtBasedOnYear} = INCLUDE 'lib.helpers_date'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{fullNameOrNickname} = INCLUDE 'libapp.user'
{removeHostFromUrls,replaceRM2REImagePathForArray,removeHostFromUrl,replaceRM2REImagePath,replaceRM2REImagePathCheckDataFormat} = INCLUDE 'libapp.propertyImage'
config = CONFIG(['serverBase','share'])
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'
APP '1.5'
APP 'notes', true

TAG_TEXT = [
  ['Building Entrance', 'Garage Entrance', 'Visitor Parking', 'Lockbox', 'Fob', 'Elevator', 'Amenities', 'Pool', 'Gym','Rooftop Deck', 'BBQ', 'Meeting Room', 'Party Room', 'Media Room', 'Library','Lounge & lobby', 'Concierge','Maintenance Fee','Built Year','Property Management', 'Hydro', 'Gas', 'Internet', 'Security', 'Rules'],
  ['Size','Condition', 'Repair & Reno', 'View', 'Layout', 'Furniture', 'Renovation', 'Appliances & Fixtures'],
  ['Subway', 'Rail', 'Landmark', 'Neighborhood', 'Safety']
]
CreateNoteTokenKey = 'createNote'
PraiseNoteTokenKey = 'praiseNote'
TokenMsg = 'Something was wrong when updating the token. Please contact the admin.'
# /1.5/notes Only ':inReal'/admin/app editor
GET '', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    try
      {isGroup,isNoteAdmin,isPersonal} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return resp.redirect '/1.5/index'
    if isPersonal
      debug.error {msg:"notes:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return resp.redirect '/1.5/index'
    return resp.ckup 'propNotes/index',{TAG_TEXT:encodeObj2JsonString(TAG_TEXT)}

# /1.5/notes/editNotes
GET 'editNotes', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    return resp.ckup 'propNotes/editNotes',{TAG_TEXT:encodeObj2JsonString(TAG_TEXT)}

# /1.5/notes/condoComplex  Only /admin/appeditor
# 没有放在condoComplex.coffee是因为要用 TAG_TEXT
GET 'condoComplex', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    if req.isAllowed 'noteAdmin'
      return resp.ckup 'propNotes/condoComplex',{TAG_TEXT:encodeObj2JsonString(TAG_TEXT)}
    return resp.redirect '/1.5/index'

# /1.5/notes/statistic Only noteAdmin
GET 'statistic', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    if req.isAllowed 'noteAdmin'
      return resp.ckup 'propNotes/statisticNotes',{}
    debug.error {msg:"get:notes/statistic:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
    return resp.redirect '/1.5/index'

# /1.5/notes/update admin/appEdit can top note，user can update note
POST 'update',(req,resp)->
  body = req.body
  type = body?.type
  noteDetail = body?.noteDetail
  propId = body?.propId
  noteId = body?.noteId
  if (not type) or (not noteDetail)
    debug.error '/1.5/notes/update: param:',req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  if (type is 'edit') and (not propId)
    debug.error '/1.5/notes/update: param:',req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  if ((type is 'edit') and (not noteDetail.uaddr) or (noteDetail.uaddr is 'undefined') or (noteDetail.uaddr is 'null'))
    debug.error '/1.5/notes/update: param:',req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  if ((type is 'edit') and ((noteDetail.propIds?.length is 0) or noteDetail.propIds?.includes('undefined') or noteDetail.propIds?.includes('null')))
    debug.error '/1.5/notes/update: param:',req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  if (type is 'praise') and (not noteDetail._id)
    debug.error '/1.5/notes/update: param:',req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  if type is 'edit'
    try
      propInfo = await Properties.findOneByIDAsync propId,{_id:1,uaddr:1,unt:1}
    catch err
      debug.error err,' param:',req.body
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless propInfo?.uaddr
      return respError {category:MSG_STRINGS.BAD_PARAMETER, resp}
    noteDetail.mt = new Date()
    noteDetail.uaddr = propInfo.uaddr
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal,useNoteModel} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    userInfo = {
      uid: user._id,
    }
    userInfo.avt = removeHostFromUrl(user.avt) if user.avt
    userInfo.nm_zh = user.nm_zh if user.nm_zh
    userInfo.nm_en = user.nm_en if user.nm_en
    userInfo.nm = user.nm if user.nm
    l10n = (a,b)->req.l10n a,b
    unless noteDetail.memo?.length
      delete noteDetail['memo']
    if noteDetail.pic?.length
      noteDetail.pic = removeHostFromUrls(noteDetail.pic)
    else
      delete noteDetail['pic']
    if isPersonal and propInfo.unt
      noteDetail.unt = propInfo.unt
    else
      delete noteDetail['unt']
    try
      result = await useNoteModel.updateNote {noteObj:noteDetail,userInfo,type}
    catch err
      debug.error err,' param:',req.body
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    # 房大师经纪新写的/点赞的笔记需要增加对应Token值
    modifyTokenParm = {
      uid: user._id,
      memo: propId,
      id: result._id,
      key: CreateNoteTokenKey
    }
    tokenMsg = ''
    if type is 'praise'
      modifyTokenParm.id = result._id
      modifyTokenParm.uid = result.uid
      modifyTokenParm.key = PraiseNoteTokenKey
    if (not isPersonal)
      if ((type is 'edit') and (not noteId)) or ((type is 'praise') and noteDetail.topMt?.length)
        try
          {err, tokenId} = await TokenModel.applyToken(modifyTokenParm)
          if tokenId
            await useNoteModel.updateTokenId(result._id,tokenId,type)
          if err
            tokenMsg = TokenMsg
        catch err
          debug.error err
          tokenMsg = TokenMsg
      if ((noteDetail.topMt?.length is 0) and result.prisTkId)
        try
          await TokenModel.cancelToken(result.prisTkId)
        catch err
          debug.error err
          tokenMsg = TokenMsg
    if result.pic?.length
      result.pic = replaceRM2REImagePathForArray(result.pic)
    if result.avt
      result.avt = replaceRM2REImagePath(result.avt)
    result.mt = formatMtBasedOnYear(result.mt,l10n)
    if result.editHis
      delete result['editHis']
    return resp.send {ok:1,result,tokenMsg}

# /1.5/notes/delete # 删除笔记
POST 'delete',(req,resp)->
  unless req.body?.id
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal, useNoteModel} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    try
      {editTkId, prisTkId} = await useNoteModel.deleteNote req.body.id
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    tokenMsg = ''
    try
      if (not isPersonal) and editTkId
        await TokenModel.cancelToken(editTkId)
      if (not isPersonal) and prisTkId
        await TokenModel.cancelToken(prisTkId)
    catch err
      debug.error err
      tokenMsg = TokenMsg
    return resp.send {ok:1,tokenMsg}

# /1.5/notes/detail 根据user判断是获取个人笔记还是房大师个人笔记
POST 'detail',(req,resp)->
  body = req.body
  id = body?.id
  uaddr = body?.uaddr
  unless (uaddr or id)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    param = {uaddr,uid: user._id}
    try
      {isGroup,isNoteAdmin,isPersonal,useNoteModel} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    if isPersonal and body.unt
      param.unt = body.unt
    else
      delete param.unt
    param = {id} if id
    try
      noteDetail = await useNoteModel.getNoteDetail param
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless noteDetail
      return resp.send {ok:1,result:{noteDetail}}
    if noteDetail.pic?.length
      noteDetail.pic = replaceRM2REImagePathForArray(noteDetail.pic)
    if noteDetail.avt
      noteDetail.avt = replaceRM2REImagePath(noteDetail.avt)
    l10n = (a,b)->req.l10n a,b
    noteDetail.mt = formatMtBasedOnYear(noteDetail.mt,l10n) if noteDetail.mt
    unless body.showList # 编辑笔记页面
      return resp.send {ok:1,result:{noteDetail}}
    unless noteDetail.propIds.length
      return resp.send {ok:1,result:{noteDetail}}
    propList = []
    opt = {
      isCip: req.isChinaIP(),
      isPad: req.isPad(),
      req,
      user,
      isGroup
    }
    try
      propList = await getPropByIds Object.assign(opt,{ids:noteDetail.propIds})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    noteDetailAddProp = mergeNotePropIds(propList,[noteDetail])[0]
    return resp.send {ok:1,result:{noteDetail: noteDetailAddProp}}

# /1.5/notes/count   针对uaddr查询个人笔记数量和全部笔记数量
ADDSTDFN 'notesCount',{needReq:true,userObject:1},(opt,cb)->
  notesCount opt,(err,msg)->
    cb(err,msg)

notesCount = (opt,cb)->
  {uaddr,user,unt,req} = opt
  unless user
    debug.warn 'notesCount not login'
    return cb(MSG_STRINGS.NEED_LOGIN)
  unless uaddr
    debug.warn 'notesCount BAD_PARAMETER'
    return cb(MSG_STRINGS.BAD_PARAMETER)
  try
    {isGroup,isNoteAdmin,isPersonal,useNoteModel} = await getInrealOrNoteAdminAuth(req, user)
    {hasSelfM,totalCount,noteId} = await useNoteModel.getNotesCount(uaddr,user._id,unt)
  catch err
    debug.error err
    return cb MSG_STRINGS.DB_ERROR
  cb(null,{hasSelfM,totalCount,noteId})

# /1.5/notes/savedList  用于saved模块中notes菜单显示笔记信息,_admin显示历史记录
POST 'savedList',(req,resp)->
  unless (body = req.body)
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  page = body?.page
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal,useNoteModel } = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    query = {}
    l10n = (a,b)->req.l10n a,b
    if isNoteAdmin
      query = {mode:1,page,isAdmin:req.isAllowed 'admin'}
      if body.filterOnlyPic
        query.filterOnlyPic = true
      if body.filterUid
        query.filterUid = body.filterUid
    else
      query = {mode:2,uid:user._id,page}
    try
      {noteList,limit} = await useNoteModel.getNoteList query
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    ids = []
    propList = []
    unless noteList.length
      return resp.send {ok:1,result:{noteList,propList,limit}}
    for list in noteList
      for item in list.propIds
        ids.push(item)
    opt = {
      isCip: req.isChinaIP(),
      isPad: req.isPad(),
      req,
      user,
      ids,
      isGroup
    }
    try
      propList = await getPropByIds opt
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    noteList = formatTsAndTranslate(noteList,l10n)
    noteListAddProp = mergeNotePropIds(propList,noteList)
    return resp.send {ok:1,result:{noteList: noteListAddProp,propList,limit}}

# /1.5/notes/findListingByID  根据房源的_id检测房源是否存在，并且获取uaddr
POST 'findListingByID',(req,resp) ->
  return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}  unless req.body?.id
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      result = await Properties.findOneByIDAsync req.body.id,{_id:1,uaddr:1,unt:1}
    catch err
      debug.error err," propId:#{req.body.id}"
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    if result
      return resp.send {ok:1,result}
    else
      return resp.send {ok:0}

formatTsAndTranslate = (list,l10n) ->
  for item in list
    if item.mt
      item.mt = formatMtBasedOnYear(item.mt,l10n)
  return list
# 根据给的一组 prop's _id 查询房源信息
getPropByIds = ({ids,isCip,isPad,req,user,isGroup}) ->
  opt = libproperties.getBaseForPropList req,user
  opt.isRealGroup = isGroup
  # libproperties.genSearchMergeOrDelProps opt
  list = await Properties.getAndSortPropListByIdsAsync {
    ids,
    obj:{coll:'Properties',fields:libproperties.MAP_PROP_FIELDS},
    searchMergeProps:opt.searchMergeProps,
    searchDelProps:opt.searchDelProps
  }
  libproperties.genListingThumbUrlForList(
    list, isCip, isPad, config.serverBase?.use3rdPic, config.share?.hostNameCn)
  list = propTranslate.translate_rmprop_list(req, list)
  for p in list
    Properties.addPropAdditionalFields({
      user,prop:p,
      isAllowedPropAdmin:opt.isAssignAdmin,
      isAllowedVipUser:opt.isVipUser,
      l10n:(a,b)->req.l10n(a,b),
      _ab:(a,b)->req._ab(a,b),
      apisrc:req.param('apisrc')
      simplfyPtp:false
    })
    Properties.deletePropPriviteFields({user,prop:p})
  return list

# /1.5/notes/statistic 统计当前年份的笔记个人数量，从大到小排列,只有noteAdmin可以查看
POST 'statistic',(req,resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"notes/statistic:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await InRealNoteModel.statisticSort()
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    statisticInfo = []
    allMonth = []
    formatMonth = {}
    ABBRMONTH = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sept','Oct','Nov','Dec']
    nameField = ['nm', 'nm_en', 'nm_zh']
    if result.length
      tempMonth = result[0].date
      allMonth = [tempMonth]
      uidMap = {}
      for item in result
        unless formatMonth[item.date]
          formatMonth[item.date] = req.l10n(ABBRMONTH[Number(item.date.split('-')[1])-1])
        unless item.date is tempMonth
          allMonth.push(item.date)
          tempMonth = item.date
        unless uidMap[item.uid]
          uidMap[item.uid] = {
            total: 0
            month: {}
          }
          for name in nameField
            unless item[name].join().length
              continue
            uidMap[item.uid].nm = item[name].join()
            break
        uidMap[item.uid].total += item.count
        uidMap[item.uid].month[item.date] = item.count
      statisticInfo = Object.values(uidMap)
      statisticInfo.sort((a, b) -> return b.total - a.total)
      statisticInfo.unshift({
        nm: '',
        total: 'Total'
        month: formatMonth
      })
    return resp.send {ok:1,result:{statisticInfo,allMonth}}

# /1.5/notes/listByUaddr 笔记index页面获取笔记列表，只有:inReal/noteAdmin权限可以同一uaddr下的其他经济的笔记
POST 'listByUaddr',(req,resp)->
  body = req.body
  uaddr = body?.uaddr
  unless uaddr
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    if isPersonal
      debug.error {msg:"listByUaddr:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    query = {uaddr,tagArray:body?.tagArray,uid: user._id}
    try
      {noteList,limit,totalCount} = await InRealNoteModel.getNoteList query
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    l10n = (a, b) -> req.l10n a, b
    noteList = formatTsAndTranslate(noteList, l10n)
    return resp.send {ok:1,result:{noteList,limit,totalCount}}

# /1.5/notes/getCondoComplexNotes # 获取相关联地址的笔记，只有inreal和noteAdmin有权限
POST 'getCondoComplexNotes',(req,resp)->
  body = req.body
  uaddr = body?.uaddr
  unless uaddr
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    if isPersonal
      debug.error {msg:"getCondoComplexNotes:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      {complexNotes,complexTags,addrInfo} = await InRealNoteModel.getCondoComplexNotes uaddr,user._id
    catch err
      debug.error err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    unless complexNotes.length
      return resp.send {ok:1,result:{complexNotes,complexTags,addrInfo}}
    allPropIds =[]
    for item in complexNotes
      if item.propIds?.length
        allPropIds = allPropIds.concat item.propIds
    propList = []
    opt = {
      isCip: req.isChinaIP(),
      isPad: req.isPad(),
      req,
      user,
      isGroup
    }
    try
      propList = await getPropByIds Object.assign(opt,{ids:allPropIds}) if allPropIds.length
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    l10n = (a, b) -> req.l10n a, b
    complexNotesAddProp = mergeNotePropIds(propList,complexNotes)
    complexNotesFormat = formatTsAndTranslate(complexNotesAddProp,l10n)
    return resp.send {ok:1,result:{complexNotes:complexNotesFormat,complexTags,addrInfo}}

# /1.5/notes/allNoteUserInfor 获取编辑过房大师笔记的所有用户信息
POST 'allNoteUserInfor',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"allNoteUserInfor:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      uidInfo = await InRealNoteModel.getNameByUidInTheNotes()
    catch err
      debug.error err
      return respError { category: MSG_STRINGS.DB_ERROR, resp }
    uidInfo = delUidInfoName(uidInfo)
    return resp.send {ok:1,result:{uidInfo}}

# /1.5/notes/showNotesPic 根据uaddr获取房大师经纪编辑的笔记图片
POST 'showNotesPic',(req,resp)->
  uaddr = req.body?.uaddr
  unless uaddr
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    try
      {isGroup,isNoteAdmin,isPersonal} = await getInrealOrNoteAdminAuth(req, user)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    if isPersonal
      debug.error {msg:"showNotesPic:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      notesImgList = await InRealNoteModel.getNotesPicByUaddr uaddr
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    notesImgDataFormat = dealNotesPicInfo(notesImgList, req.locale())
    return resp.send {ok:1,result:{notesImgs:notesImgDataFormat}}
    
# 将笔记的propIds给对应的房源信息
mergeNotePropIds = (propList,noteList)->
  propMap = {}
  for prop in propList
    propMap[prop._id] = prop
  for note in noteList
    propDetail = []
    note.isShowHis = false
    for ids in note.propIds
      if propMap[ids]
        propDetail.push(propMap[ids])
    note.propDetail = propDetail
  return noteList

# 将 aggregate 出来的数组nm，nm_en，nm_zh 各自jion
delUidInfoName = (uidInfo) ->
  delUidInfo = []
  nmTitle = ['nm', 'nm_en', 'nm_zh']
  for item in uidInfo
    obj = {
      uid: item._id,
      isSel: false
    }
    for name in nmTitle
      value = item[name].join(',')
      obj[name] = value if value.length
    delUidInfo.push(obj)
  return delUidInfo

# 判断是否是:inReal或者noteAdmin权限
getInrealOrNoteAdminAuth = (req,user,fnName) ->
  isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
  isNoteAdmin = req.isAllowed 'noteAdmin'
  isPersonal = true
  if isGroup or isNoteAdmin # 该角色是房大师经纪，所有操作都和房大师的笔记相关
    isPersonal = false
    useNoteModel = InRealNoteModel
  else
    useNoteModel = PersonalNoteModel
  return {isGroup, isNoteAdmin, isPersonal, useNoteModel}

dealNotesPicInfo = (notesImgs, lang) ->
  notesImgDataFormat = []
  for item in notesImgs
    user = {
      nm: item.nm
      nm_en: item.nm_en
      nm_zh: item.nm_zh
    }
    fnm = fullNameOrNickname lang, user
    objInfo = {
      fnm: fnm
    }
    if item.avt
      objInfo.avt = replaceRM2REImagePathCheckDataFormat(item.avt)
    unless item.pic?.length
      continue
    for pic in item.pic
      objInfo.img = replaceRM2REImagePathCheckDataFormat(pic)
      notesImgDataFormat.push(Object.assign({}, objInfo))
  return notesImgDataFormat
