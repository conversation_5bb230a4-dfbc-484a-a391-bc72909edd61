UserModel = MODEL 'User'

APP '1.5'

# this.dispVar.isAdmin||
# this.dispVar.isProdSales || 
# this.dispVar.forumAdmin || 
# this.dispVar.formOrganizer;
adminMangaePage = [
  # // {
  # //   t:'Prediction List',
  # //   img:'/img/icon_index_share.png',
  # //   ctx:'tools',
  # //   url:'/1.5/prop/predlist?d=/1.5/more',
  # //   show:'isAdmin'
  # // },  
  # // { t:'Email Test',
  # //   ctx:'tools',
  # //   url:'/sys/emailtest?d=/1.5/more',
  # //   show:'isAdmin'
  # // },

]
ADMIN_COMMANDS = {
  forumAdmin:{
    adminToolsPage:[{
      t:'Edit Wordpress Profile',
      ctx:'',
      icon:'fa fa-file-word-o',
      url:'/1.5/settings/editOtherProfile?d=/1.5/more',
      show:'forumAdmin',
    }],
  }
  formOrganizer:{
    adminToolsPage:[{
      t:'WeForm Manage',
      ctx:'tools',
      icon:'fa fa-keyboard-o',
      url:'/1.5/form?d=/1.5/more',
      show:'formOrganizer'
    }],
  },
  createProject:{
    adminToolsPage:[{
      t:'New Condo',
      ctx:'tools',
      icon:'fa fa-assignment',
      url:'/1.5/prop/projects?d=/1.5/more',
      show:'isProjAdmin'
    }]
  },
  stigmaAdmin:{
    adminToolsPage:[{
      t:'Stigmatized List',
      ctx:'tools',
      icon:'fa fa-ex-house',
      url:'/1.5/stigma/list?tp=approved&d=/1.5/more',
      show:'isStigmaAdmin'
    }],
  },
  marketAdmin:{
    adminToolsPage:[
      {
        t:'Banner Manage',
        icon:'fa fa-flag-o',
        ctx:'',
        color:'#aaa',
        url:'/1.5/banner/manage?d=/1.5/more',
      },
      {
        t:'Advertisement Manage',
        icon:'fa fa-flag-o',
        ctx:'',
        url:'/1.5/ads/list?d=/1.5/more',
      }
    ],
    adminInfo:[{
      t:'Project Statistics',
      icon:'fa fa-area-chart',
      ctx:'',
      url:'/sys/projstat?d=/1.5/more',
    },{
        t:'Agent',
        ctx:'',
        icon:'fa fa-user',
        url:'/1.5/user/follow?d=/1.5/more',
    }]
  },
  propAdmin:{
    adminToolsPage:[
      {
        t:'Manual Import',
        icon:'fa fa-residential',
        ctx:'',
        url:'/1.5/import/manual?d=/1.5/more',
      }
    ]
  },
  productSales:{
    adminInfo:[{
      t:'CPM',
      ctx:'tools',
      img:'/img/icon_index_share.png',
      icon:'fa fa-top',
      url:'/1.5/cpm/analytics?d=/1.5/more',
      show:'isProdSales'
    },{
      t:'Top Agent',
      ctx:'tools',
      icon:'fa fa-user',
      url:'/1.5/topagent/all?d=/1.5/more',
      show:'isProdSales'
    },{
      t:'Agent',
      ctx:'',
      icon:'fa fa-user',
      url:'/1.5/user/follow?d=/1.5/more'
      show:'isProdSales'
    }],
    adminUserToolsPage:[
      {
        t:'CRM',
        ctx:'tools',
        icon:'fa fa-users',
        url:'/1.5/crm/index?d=/1.5/more',
        show:'isProdSales'
      },{
        t:'Listing Agent',
        ctx:'tools',
        icon:'fa fa-user',
        url:'/1.5/listingagent/index?d=/1.5/more',
        show:'isProdSales'
      },
    ]
  },
  admin:{
    adminToolsPage:[{
      t:'TopAgent Manage',
      ctx:'',
      icon:'fa fa-top',
      url:'/1.5/topagent/manage?d=/1.5/more'
    },{
      t:'Tools List',
      ctx:'',
      icon:'fa fa-list',
      url:'/1.5/admin/toolsList?d=/1.5/more'
    },{
      t:'Home Alert Window',
      ctx:'tools',
      icon:'fa fa-rmlist',
      url:'/1.5/settings/alertwindow?d=/1.5/more',
    }]
  },
  # predict:{
  #   predict:{
  #     t:'Predict List',
  #     ctx:'',
  #     url:'/1.5/prop/predlist?d=/1.5/more',
  #   }
  # }
  devGroup:{
    devTools:[{
      t:'Email Test',
      icon:'fa fa-envelope-open-o',
      ctx:'',
      url:'/sys/emailtest?d=/1.5/more',
    },{
      t:'Test',
      ctx:'',
      url:'/1.5/test?d=/1.5/more',
    },{
      t:'pn test page',
      icon:'icon icon-edit',
      ctx:'',
      url:'/sys/pntest?d=/1.5/more',
    },{
      t:'Test Native API',
      ctx:'',
      url:'/diagnosePage?d=/1.5/more',
    }],
    adminToolsPage:[{
      t:'Edm',
      ctx:'',
      icon:'fa fa-envelope-o',
      url:'/sys/edm?d=/1.5/more',
    },{
      t:'Impression Ads Manage',
      ctx:'',
      icon:'fa fa-top',
      url:'/1.5/cpm/manage?d=/1.5/more&eml='
    },{
      t:'Set Proxy Email Domain',
      ctx:'',
      icon:'fa fa-envelope-o',
      url:'/sys/proxyemail?d=/1.5/more'
    }]
    adminCmds:[{
      t:'Wechat Auth',
      ctx:'',
      icon:'fa fa-wechat',
      url:'/wechatauth?d=/1.5/more',
    }]
  },
  userAdmin:{
    adminUserToolsPage:[
      {
        t:'User Manage Role',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url:'/sys/user?d=/1.5/more',
      },
      {
        t:'Manage FollowUp Agent',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/1.5/userfollowup?d=/1.5/more&tp=agent',
      },
      {
        t:'Manage FollowUp People',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/1.5/userfollowup?d=/1.5/more&tp=people',
      },
      {
        t:'Manage User Followed agent',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/1.5/userfollow?d=/1.5/more'
      },
      {
        t:'Manage Realor Followed Users',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/1.5/realtorfollow?d=/1.5/more'
      },
      {
        t:'Fub Management',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/fubManagement?d=/1.5/more'
      },
      {
        t:'Manage User Phone',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/sys/phone?d=/1.5/more'
      }
    ],
    adminToolsPage:[{
      t:'Contact City Setting',
      icon:'fa fa-envelope-open-o',
      ctx:'',
      url:'/sys/city?d=/1.5/more',
    },{
      t:'Customer Service Status Setting',
      icon:'fa fa-users',
      ctx:'',
      url:'/sys/cservice?d=/1.5/more',
    },{
      t:'General Service Status Setting',
      icon:'fa fa-sliders',
      ctx:'',
      url:'/sys/generalConfig?d=/1.5/more',
    },{
      icon:'fa fa-vcard-o',
      t:'Wecard Manage',
      ctx:'',
      url:'/1.5/user/wecardManage?d=/1.5/more'
    },
    {
      t:'Front Desk Admin',
      icon:'fa fa-user-circle-o',
      ctx:'',
      url: '/1.5/contactRealtor/manage?d=/1.5/more'
    }
    ]
    adminInfo:[
      {
        t:'view signups',
        icon:'fa fa-envelope-open-o',
        ctx:'',
        url:'/sys/signupRecords?d=/1.5/more',
      },
      {
        t:'View transactions',
        icon:'fa fa-calendar-check-o',
        ctx:'',
        url:'/sys/agentTransactions?d=/1.5/more',
      },
      {
        t:'View predict stats',
        icon:'fa fa-bar-chart-o',
        ctx:'',
        url:'/sys/predictStats?d=/1.5/more',
      },
      {
        t:'User Stats',
        icon:'fa fa-bar-chart-o',
        ctx:'',
        url:'/sys/userStats?d=/1.5/more',
      },
      {
        t:'Edm Report',
        ctx:'',
        icon:'fa fa-envelope-o',
        url:'/sys/edm/report?d=/1.5/more',
      },
      {
        t:'Moderation Logs',
        ctx:'tools',
        icon:'fa fa-warning',
        url:'/1.5/settings/moderation?d=/1.5/more',
      }
    ]
    devTools:[
      {
        t:'User Swtich',
        ctx:'',
        icon:'fa fa-user-circle-o',
        url:'/sys/switch?d=/1.5/more',
      }
    ],
  },
  inrealAdmin:{
    adminUserToolsPage:[{
      t:'Manage Inreal Group',
      icon:'fa fa-user-circle-o',
      ctx:'',
      url:'/sys/inreal?d=/1.5/more',
    }]
  }
  jumpSite:{
    adminCmds:[{
      t:'Site Jump CN',
      ctx:'',
      icon:'fa fa-globe',
      color:'#aaa',
      url:'/sitejump?r=cn?d=/1.5/more',
    },
    {
      t:'Site Jump CA',
      ctx:'',
      icon:'fa fa-globe',
      color:'#aaa',
      url:'/sitejump?r=ca?d=/1.5/more',
    },
    {
      t:'Site Jump',
      ctx:'',
      color:'#aaa',
      icon:'fa fa-globe',
      url:'/sitejump?r=null?d=/1.5/more',
    }],
  }
  checkServerStatus:{
    adminInfo:[{
      t:'Service Check',#TODO: show server status
      ctx:'',
      color:'#aaa',
      url:'/service_check?d=/1.5/more',
    }],
  }
  sysConfig:{
    adminToolsPage:[{
      icon:'fa fa-language',
      t:'Translation',
      ctx:'',
      url:'/sys/l10n?d=/1.5/more',
    }],
  },
  analytics:{
    adminInfo:[{
      t:'Analytics',
      icon:'fa fa-area-chart'
      ctx:'',
      url:'/analytics?d=/1.5/more',
    },
    {
      t:'Batch Status',
      icon:'fa fa-area-chart'
      ctx:'',
      url:'/batchanalytics?d=/1.5/more',
    },
    {
      t:'Analytics Spevent',
      icon:'fa fa-area-chart',
      ctx:'',
      color:'#aaa',
      url:'/analytics_spevent?d=/1.5/more',
    },
    {
      t:'Share Stats',
      ctx:'',
      icon:'fa fa-bar-chart',
      url:'/s/share?d=/1.5/more',
    }]
  },
  tokenAdmin:{
    adminUserToolsPage:[
      {
        t:'Token Management',
        icon:'fa fa-user-circle-o',
        ctx:'',
        url: '/token/list?d=/1.5/more'
      }
    ],
    adminToolsPage:[
      {
        t:'Token System Manage',
        icon:'fa fa-rmlist',
        ctx:'',
        url: '/token/systemKey?d=/1.5/more'
      }
    ]
  }
}

###
# 数组去重函数
# 基于对象的 url 字段进行去重，如果没有 url 则使用 t 字段
# @param {Array} arr - 需要去重的数组
# @return {Array} - 去重后的数组
###
uniqueArray = (arr) ->
  seen = new Set()
  return arr.filter (item) ->
    key = item.url or item.t or JSON.stringify(item)
    if seen.has(key)
      return false
    seen.add(key)
    return true

GET 'more',(req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    roles = Object.keys(ADMIN_COMMANDS)
    # simple cmd page, for unfollow/wechatauth etc
    adminCmds = []
    # a page to manae all fns
    adminToolsPage = []
    adminUserToolsPage = []
    # dev tools page, used for testing
    devTools = []
    # admin info for stats/analytics
    adminInfo = []
    for role in roles
      if req.isAllowed role,user
        # console.log '+++++',ADMIN_COMMANDS[role]
        adminCmds = adminCmds.concat(ADMIN_COMMANDS[role].adminCmds or [])
        adminToolsPage = adminToolsPage.concat(ADMIN_COMMANDS[role].adminToolsPage or [])
        adminUserToolsPage = adminUserToolsPage.concat(ADMIN_COMMANDS[role].adminUserToolsPage or [])
        devTools = devTools.concat(ADMIN_COMMANDS[role].devTools or [])
        adminInfo = adminInfo.concat(ADMIN_COMMANDS[role].adminInfo or [])
    # console.log('--------',adminCmds,adminToolsPage,devTools,adminInfo)
    resp.ckup 'indexMore', {
      adminCmds,
      adminTools:adminToolsPage,
      adminUserToolsPage,
      devTools,
      adminInfo: uniqueArray(adminInfo)}, '_', {noAngular:1, noAppCss:1, noUserModal:1}


VIEW 'indexMore', ->
  css '/css/apps/appIndex.css'
  div id:'vueBody',->
    text """
    <index-more-category></index-more-category>
    """
  coffeejs {vars:{
    adminCmds:@adminCmds,
    adminTools:@adminTools,
    adminUserToolsPage:@adminUserToolsPage,
    devTools:@devTools,
    adminInfo:@adminInfo
  }}, ->
    null
  js '/js/entry/commons.js'
  js '/js/entry/indexMoreCategory.js'