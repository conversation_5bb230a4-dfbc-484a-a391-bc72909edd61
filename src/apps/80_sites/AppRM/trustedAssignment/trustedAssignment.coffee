UserModel = MODEL 'User'
SysDataModel = MODEL 'SysData'
PropertiesModel = MODEL 'Properties'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'
{CONTACT_REALTOR_VALUES} = INCLUDE 'libapp.common'
cityHelper = INCLUDE 'lib.cityHelper'
objectCache = INCLUDE 'libapp.objectCache'

TRUSTED_ASSIGN_CITY_LIST = 'trustedAssignCityList'
TRUSTED_ASSIGN_STYLE_LIST = 'trustedAssignStyleList'
APP 'truAssigm'

# /truAssigm
GET '', (req, resp) ->
  return resp.ckup 'trustedAssignment/index',{}

# /truAssigm/cityAndPtype2List
POST 'cityAndPtype2List',(req,resp)->
  cityCached = objectCache.getObjectCacheList TRUSTED_ASSIGN_CITY_LIST
  styleCached = objectCache.getObjectCacheList TRUSTED_ASSIGN_STYLE_LIST
  if cityCached and styleCached
    return resp.send {ok:1,result:{city:cityCached,ptype2s:styleCached}}
  try
    trustedAssignCount = await PropertiesModel.statisTrustedAssignByProvAndcity()
    trustedAssignPtype2 = await PropertiesModel.statisTrustedAssignPtype2()
  catch err
    debug.error err
    return respError {category:MSG_STRINGS.DB_ERROR, resp}
  truthAssignCity = dealTrustedAssignCityList(trustedAssignCount)
  objectCache.setObjectCacheList {type:TRUSTED_ASSIGN_CITY_LIST,value:truthAssignCity}
  origStyle = []
  if trustedAssignPtype2.length
    for item in trustedAssignPtype2
      origStyle = origStyle.concat(item._id.ptype2) if item._id?.ptype2
    # remove duplicate
    origStyle = origStyle.filter(
      (item,index)-> origStyle.indexOf(item) is index
    )
    objectCache.setObjectCacheList {type:TRUSTED_ASSIGN_STYLE_LIST,value:origStyle}
  return resp.send {ok:1,result:{city:truthAssignCity,ptype2s:origStyle}}

# 将数据库查出的城市处理成前端需要的显示格式
dealTrustedAssignCityList = (results)->
  list={}
  cityList = []
  for data in results
    prov = cityHelper.getProvFullName data._id.prov
    city = data._id.city
    if prov and city
      list[prov]?= []
      unless list[prov].includes(city)
        list[prov].push(city)
  for k,v of list
    cityList.push({
      o: k,
      isProv: true
    })
    for item in v
      cityList.push({
        o: item,
        isProv: false
      })
  return cityList

APP 'fubManagement'

FUB_EMAIL_IDS= [
  CONTACT_REALTOR_VALUES.FUB_ASSIGNMENT_EMAIL,
  CONTACT_REALTOR_VALUES.FUB_PROJECT_EMAIL
]

FUB_EMAIL_TITLE= [
  {id:CONTACT_REALTOR_VALUES.FUB_ASSIGNMENT_EMAIL,title:'Fub Assignment Email'},
  {id:CONTACT_REALTOR_VALUES.FUB_PROJECT_EMAIL,title:'Fub PreCon Email'}
]

# /fubManagement
GET '', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    unless req.isAllowed 'admin'
      debug.error {msg:"fubManagement/updateEmail:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return resp.redirect '/1.5/index'
    return resp.ckup 'fubManagement/index',{}

# /fubManagement/updateEmail 保存followupboss指定邮箱到chrome的sysdata表
# 传入的参数 updataEml 是一个object
POST 'updateEmail',(req,resp)->
  unless (updataEml = req.body?.updataEml)
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'admin'
      debug.error {msg:"fubManagement/updateEmail:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      await SysDataModel.updateById updataEml.id,{eml:updataEml.value}
    catch err
      debug.error err,"updataEml:#{updataEml}"
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    return resp.send {ok:1,result:req.l10n('Saved')}

# /fubManagement/getEmail
POST 'getEmail',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'admin'
      debug.error {msg:"fubManagement/updateEmail:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    data = []
    try
      ret = await SysDataModel.findByIds FUB_EMAIL_IDS
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    for item in FUB_EMAIL_TITLE
      index = ret.findIndex((el)-> return el._id is item.id)
      if index > -1
        data.push {id: ret[index]._id,value: ret[index].eml,title:item.title}
      else
        data.push {id: item.id,value: '',title:item.title}
    return resp.send {ok:1,result:data}