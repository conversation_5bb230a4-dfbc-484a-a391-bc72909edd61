###
Discover channel, community map search controller
###
{dateFormat,monthNameAndDate,formatCmntTs} = INCLUDE 'lib.helpers_date'
{currency} = INCLUDE 'lib.helpers_string'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{getProvAbbrName} = INCLUDE 'lib.cityHelper'
{getLastMonth,getMonthDates} = INCLUDE 'libapp.stat_helper'
mapServer =  INCLUDE 'lib.mapServer'
PropStatsModel = MODEL 'PropStats'
{genListingThumbUrlForList,
  genListingPicReplaceUrls,
  MAP_PROP_FIELDS} = INCLUDE 'libapp.properties'
{
  processCmtys,
  formatCmty,
  MAX_CMTY_SEARCH_PRICE,
  ethnicsObj,
  formatEdmDate,
  getPtype2ForCmty} = INCLUDE 'libapp.community'
{translate_rmprop_list} = INCLUDE 'libapp.propertiesTranslate'
debug = DEBUG()
config = CONFIG(['serverBase','share'])
PropertyModel = MODEL 'Properties'
CommunityModel = MODEL 'Community'
UserModel = MODEL 'User'
defaultCityToronto = DEF 'defaultCityToronto'
{respError} = INCLUDE 'libapp.responseHelper'
ObjectId = INCLUDE('lib.mongo4').ObjectId
GroupModel = MODEL 'Group'

LAYOUT_OPT = {noAngular:1,noRatchetJs:1}

# 未使用
# processProps = (props,{isCip,shareHostNameCn,use3rdPic,isPad}) ->
#   return unless props?.length
#   for prop in props
#     prop.lp = currency (prop.lp),'$',0
#     prop.sp = currency (prop.sp),'$',0
#     genListingPicReplaceUrls(prop,isCip,\
#       {num:1,isThumb:true,use3rdPic,isPad,shareHostNameCn})
#     prop.addr = "#{prop.unt} #{prop.addr}" if prop.unt

formatUserCity = (req, user)->
  userCity = getUserCity user
  {city,prov} = userCity
  userCity.p_ab = getProvAbbrName(prov)
  userCity.o = city
  userCity.n = req.l10n city
  userCity

getUserCity = (user)->
  {city} = user
  if city and Object.keys(city).length > 0
    userCity = city
  else
    userCity = defaultCityToronto
  return userCity

APP '1.5'
APP 'community', true
libUser = INCLUDE 'libapp.user'

# /1.5/community/discover  unused
# GET 'discover', (req, resp)->
#   UserModel.appAuth {req,resp, url:'/1.5/user/login?d=/1.5/community/discover'}, (user)->
#     pageOpt = {
#       isChinaIP:req.isChinaIP()
#       jsPaths: ['/js/vue3.min.js','/js/community/discoverPage.js'],
#       page:'discover',
#       appmode:req.cookies[libUser.APPMODE],
#       lang:req.locale()
#     }
#     resp.ckup 'community/discover',pageOpt,'_',LAYOUT_OPT

# '/1.5/community/filter'
GET 'filter', (req,resp)->
  jsPaths = ['/js/vue3.min.js','/js/community/searchPage.js']
  lang=req.locale()
  ptype2s = PropertyModel.getAllTranslatedPtype2s({lang,ptype:'Residential'}).ptype2s
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    userCity = formatUserCity req, user
    d = req.param 'd'
    if d is 'back'
      d = ''
    else if not d
      d = '/home/<USER>'
    resp.ckup 'community/search',{MAX_CMTY_SEARCH_PRICE,userCity:encodeObj2JsonString(userCity),\
      jsPaths,ptype2s:encodeObj2JsonString(ptype2s),\
      ethnics:encodeObj2JsonString(ethnicsObj),d},'_',LAYOUT_OPT

# '/1.5/community/map'
GET 'map', (req,resp)->
  jsPaths = [
    '/js/vue3.min.js',
    'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js',
    '/js/map/mapbox.min.js',
    '/js/community/mapPage.js'
  ]
  lat = req.param 'lat'
  lng = req.param 'lng'
  bbox = req.param 'bbox'
  d = req.param 'd'
  if not d
    d = '/1.5/community/filter'
  url = '/1.5/user/login?d=/1.5/community/map'
  userPos = null
  if (lat and lng)
    url += "&lat=#{lat}&lng=#{lng}"
    userPos = [lng,lat]
  UserModel.appAuth {req,resp,url}, (user)->
    isApp = req.getDevType() is 'app'
    mapboxkey = mapServer.getMapboxAPIKey {isApp,isChina:req.isChinaIP()}
    mode = req.param 'mode' or 'discover'
    unless userPos
      if (lat = user?.city?.lat) and (lng = user?.city?.lng)
        userPos = [lng,lat]
      else
        userPos = [defaultCityToronto.lng, defaultCityToronto.lat]
    lang=req.locale()
    ptype2s = PropertyModel.getAllTranslatedPtype2s({lang,ptype:'Residential'}).ptype2s
    layoutOpt = Object.assign {mapboxkey},LAYOUT_OPT
    userCity = formatUserCity req, user
    ctx = {MAX_CMTY_SEARCH_PRICE,d,jsPaths,mode,lat,lng,bbox,userPos:encodeObj2JsonString(userPos),\
      userCity:encodeObj2JsonString(userCity),\
      ptype2s:encodeObj2JsonString(ptype2s),ethnics:encodeObj2JsonString(ethnicsObj)}
    resp.ckup 'community/map',ctx,'_',layoutOpt

searchCmtys = (opt,cb)->
  {bbox,search,req,user} = opt
  unless user
    debug.error 'searchCmtys no user'
    return cb(MSG_STRINGS.NEED_LOGIN)
  CommunityModel.findList {bbox,search},(err,cmtys)->
    if err
      debug.error 'searchCmtys findList',err
      return cb(MSG_STRINGS.DB_ERROR)
    UserModel.findFavCmtys user._id,(err,favCmtys)->
      if err
        debug.error 'searchCmtys findFavCmtys',err
        return cb(MSG_STRINGS.DB_ERROR)
      lang = req?.locale()
      processCmtys cmtys, {lang,favCmtys}
      cb(null,cmtys)

ADDSTDFN 'searchCmtys',{needReq:true,userObject:true},(opt,cb)->
  searchCmtys opt,(err,cmtys)->
    return cb(err) if err
    cb null,cmtys

ADDSTDFN 'searchCmtysByName',{needReq:true,userObject:true},(opt,cb)->
  # searchCmtys opt,(err,cmtys)->
  #   return cb(err) if err
  #   cb null,cmtys
  {name} = opt
  CommunityModel.findListByName {name},(err,cmtys)->
    if err
      debug.error 'searchCmtys findList',err
      return cb(MSG_STRINGS.DB_ERROR)
    lang = req?.locale()
    processCmtys cmtys, {lang,favCmtys:null}
    cb(null,cmtys)

findCmtyDetail = (opt,cb)->
  {id,ethnicKey,req,user} = opt
  unless user
    debug.info 'findCmtyDetail not login'
    return cb(MSG_STRINGS.NEED_LOGIN)
  lang = req.locale()
  CommunityModel.findOne {id},(err,cmty)->
    if err
      debug.error 'findcmtyDetail findOne', err
      return cb(MSG_STRINGS.DB_ERROR)
    unless cmty
      debug.warn 'findCmtyDetail no cmty', id
      return cb(null,{msg:MSG_STRINGS.NOT_FOUND})
    UserModel.findFavCmtys user._id,(err,favCmtys)->
      if err
        debug.error 'findCmtyDetail findFavcmtys',err
        return cb(MSG_STRINGS.DB_ERROR)
      formatCmty cmty,{lang,favCmtys,isDetail:true,ethnicKey}
      PropStatsModel.getCardInfo cmty,(err,cmty)->
        return cb(err) if err
        cmty.uaddr = cmty._id
        cmty.uid = user._id
        cb(null,cmty)

ADDSTDFN 'findCmtyDetail',{needReq:true,userObject:true},(opt,cb)->
  findCmtyDetail opt,(err,cmty)->
    return cb(err) if err
    cb(err,cmty)

favCmty = (opt,cb)->
  {id,fav,req,user} = opt
  unless user
    debug.error 'favCmty not login'
    return cb(MSG_STRINGS.NEED_LOGIN)
  CommunityModel.favorite {id,fav,uid:user._id},(err,ret)->
    if err
      debug.error 'favCmty favorite',err
      return cb(MSG_STRINGS.DB_ERROR)
    msg = req.l10n((if fav then 'Saved' else 'Unsaved'),'favorite')
    cb(null,msg)

ADDSTDFN 'favCmty',{needReq:true,userObject:1},(opt,cb)->
  favCmty opt,(err,msg)->
    cb(err,msg)

EDM_PROP_STATUS = ['new','off','chgd']

#1.5/community/edmProps
POST 'edmProps',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        type:MSG_STRINGS.NEED_LOGIN,
        resp}
    {date,tag,updateViewStatus} = req.body
    try
      edmLog = await UserModel.findPropChangeFeed {date,uid:user._id,tag,updateViewStatus,l10n:(a,b)->req.l10n a,b}
      getPropsFromEdmLog {edmLog,req,user},(err,edmLog)->
        return respError {clientMsg:req.l10n(err),type:err,resp} if err
        return resp.send {ok:1,edmLog}
    catch err
      debug.error 'edmProps', err
      return resp.send {ok:1,edmLog:null}

getPropsFromEdmLog = ({edmLog,req,user},cb)->
  unless edms = edmLog?.edm
    return cb(MSG_STRINGS.NOT_FOUND)
  propIds = Object.keys edms
  unless propIds.length
    return cb(MSG_STRINGS.NOT_FOUND)
  PropertyModel.findPartialListingsByIDs propIds,{projection:MAP_PROP_FIELDS},(err,props)->
    if err
      debug.error 'edmProps PropertyModel.findPartialListingsByIDs',err
      return cb(MSG_STRINGS.DB_ERROR)
    if props?.length
      isCip = req.isChinaIP()
      isPad = req.isPad()
      use3rdPic = config.serverBase?.use3rdPic
      shareHostNameCn = config.share?.hostNameCn
      genListingThumbUrlForList(props, isCip, isPad, use3rdPic, shareHostNameCn)
      props = translate_rmprop_list req,props
    req.setupL10n() unless req._ab and req._
    opts =
      user:user
      isAllowedPropAdmin:req.isAllowed('admin')
      isAllowedVipUser:req.isAllowed 'vipUser'
      _ab:(a,b)->req._ab a,b
      l10n:(a,b)->req.l10n a,b
      simplfyPtp:false
    GroupModel.isInGroup {uid:user?._id,groupName:':inReal'},(err,isRealGroup)->
      debug.error 'GroupModel.isInGroup',err if err
      opts.isRealGroup = isRealGroup
      edmLog = buildEdmProps edmLog, props, opts
      edmLog.edmId = edmLog.id
      edmLog.id = formatEdmDate edmLog.id,{split:'-'}
      today = dateFormat(new Date(), 'YYYY-MM-DD')
      edmLog.isToday = edmLog.id is today
      cb(null,edmLog)

# https://stackoverflow.com/questions/1960473/get-all-unique-values-in-a-javascript-array-remove-duplicates
getEdmPropIds = (edms)->
  ###
  [
    {
      ts: 2021-02-16T14:57:16.876Z,
      nm: '多伦多3房3浴室2车位',
      new: {
        TRBW5124320: [Object],
        TRBW4889435: [Object],
        TRBE5124321: [Object],
      }
    },
    {
      ts: 2021-01-17T21:17:10.948Z,
      nm: '万锦出租1+洗手间',
      new: {
        TRBN5124531: [Object],
        TRBN5123885: [Object],
      }
    }
  ]
  => ['TRBW5124320','TRBW4889435','TRBE5124321','TRBN5124531','TRBN5123885']
  ###
  propIds = []
  for log in edms
    for status in EDM_PROP_STATUS
      if (props = log[status]) and (ids = Object.keys(props)) and (ids.length)
        propIds = propIds.concat(ids)
  propIds.filter (value, index, self)->
    return self.indexOf(value) is index

buildEdmProps = (edmLog, props, {user,
  isAllowedPropAdmin,
  isAllowedVipUser,
  l10n,
  isRealGroup,
_ab})->
  # add prop detail to edm prop log
  for prop in props
    opts = {prop,user,isAllowedPropAdmin,isAllowedVipUser,l10n,_ab,isRealGroup}
    PropertyModel.addPropAdditionalFields opts
    edmLog.edm[prop._id] =  Object.assign prop,edmLog.edm[prop._id]
  for k,v of edmLog.edm
    if not v._id
      delete edmLog.edm[k]
  edmArray = Object.values(edmLog.edm)
  edmArray.sort (a,b)->
    return b.spcts - a.spcts
  edmLog.edm = edmArray
  return edmLog

# '/1.5/community/favList'
POST 'favList',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    UserModel.findFavCmtys user._id,(err,favCmtys)->
      if err
        debug.error 'favList UserModel.findFavCmtys ',err
        return respError {
          clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),
          type:MSG_STRINGS.DB_ERROR,
          resp}
      cmtyIds = Object.keys favCmtys
      allInfo = req.body?.full?
      lang = req.locale()
      CommunityModel.findList {cmtyIds,allInfo},(err,cmtys)->
        if err
          debug.error 'favList CommunityModel.findList ',err
          return respError {
            clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),
            type:MSG_STRINGS.DB_ERROR,
            resp}
        for cmty in cmtys
          formatCmty cmty,{lang,isDetail:true,favCmtys}
        PropStatsModel.getCardInfo cmtys,(err,cmtys)->
          if err
            debug.error 'favList PropStatsModel.getCardInfo',err
            return respError {
              clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),
              type:MSG_STRINGS.DB_ERROR,
              resp}
          resp.send {ok:1,cmtys}

#1.5/community/edmupdatets
POST 'edmupdatets',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        type:MSG_STRINGS.NEED_LOGIN,
        resp}
    UserModel.findEdmLastUpdateTs user?._id,(err,edmLog)->
      l10n = (a,b)->req.l10n a,b
      if edmLog?.ts
        edmLog.mlsMt = formatCmntTs(edmLog.ts,l10n)
      return resp.send {ok:1,edmLog}
