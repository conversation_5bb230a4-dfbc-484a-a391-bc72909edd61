# User = COLLECTION 'chome', 'user'
Wecard = COLLECTION 'chome', 'wecard'
UserModel = MODEL 'User'

APP '1.5'
APP 'user', true

VIEW 'wecardMgr', ->
  ###
  div#wecardMgr
    header.bar.bar-nav
      a.icon.icon-left.pull-left(href="/1.5/index")
      h1.title WePage Manage
    div.content
      div.input
        input(type="text", placeholder="url of wecard", v-model="url")
        button.btn.btn-primary(@click="getData()") Get Data
      div.content-preview
        div.id {{card.id}}
        div.tl {{card.meta.title}}
        div.ct {{card.meta.desc}}
        div.fn {{user.fn}}
        div.ln {{user.ln}}
        div.eml {{user.eml}}
      button.btn.btn-negative(@click="postAction('hide')") Hide(Delete)
      button.btn.btn-positive(@click="postAction('show')") Show(Put Back)
  ###
  text """
<div id="wecardMgr">
  <header class="bar bar-nav"><a href="/1.5/index" class="icon icon-left pull-left"></a>
    <h1 class="title">WePage Manage</h1>
  </header>
  <div class="content">
    <div class="input">
      <input type="text" placeholder="url of wecard/wpnews" v-model="url"/>
      <button @click="getData()" class="btn btn-primary">Get Data</button>
    </div>
    <div class="content-preview">
      <div class="id"><span class="col-6">Card.id:</span>
        {{card.id}}</div>
      <div class="tl"><span class="col-6">title:</span>
        {{meta.title}}</div>
      <div class="ct"><span class="col-6">desc:</span>
        {{meta.desc}}</div>
      <div class="fn"><span class="col-6">nm_zh:</span>
        {{user.nm_zh}}</div>
      <div class="eml"><span class="col-6">eml:</span>
        {{user.eml}}</div>
      <div class="del"><span class="col-6">del:</span>
        {{card.del}}</div>
      <img v-show="meta.img" :src="meta.img" style="width:50px; height:50px"/>
    </div>
    <select v-model="selected" @change="onChange()">
      <option value="">Please select one</option>
      <option >This content has been removed due to infringement.</option>
      <option >This content has been removed due to violent contents.</option>
    </select>
    <input type="text" v-model="deleteReason">
    <button @click="postAction('hide')" class="btn btn-negative">Hide(Delete)</button>
    <button @click="postAction('show')" class="btn btn-positive">Show(Put Back)</button>
    <button @click="postAction('delete')" class="btn btn-negative">DB delete(real delete)</button>
  </div>
</div>
  """
  js '/js/vue.min.js' #  # https://cdnjs.cloudflare.com/ajax/libs/vue/2.0.1/vue.js
  text """<script src="https://cdn.jsdelivr.net/vue.resource/1.0.3/vue-resource.min.js"></script>"""
  # js '/js/app/wecardCtrl.js'
  coffeejs { vars:{} }, ->
    null
  text """<script>
  app = new Vue({
    el: '#wecardMgr',
    data: {
      message: 'Hello Vue!',
      url:'',
      id:[],
      meta:{},
      card:{},
      user:{},
      deleteReason:'',
      selected:'',
    },
    http: {
      root: '/',
      headers: {
        Authorization: 'Basic YXBpOnBhc3N3b3Jk'
      }
    },
    methods:{
      onChange(e){
        this.deleteReason = this.selected;
      },
      initData () {
        var wecardId = this.id[0], self = this;
        if (!wecardId) {
          return;
        }
        this.$http.post('/1.5/wecard/prop/RM_1212/'+wecardId+'.json', {ml_num:wecardId}).then((ret) => {
          ret = ret.data;
          if (ret.e||ret.err) {
            RMSrv.dialogAlert(ret.e||ret.err);
          } else {
            self.card = ret.card;
            self.meta = ret.card.meta;
            self.user = ret.user;
          }
        }, (ret) => {
          // error callback
          console.error(ret);
        });
      },
      parseUrl (url="") {
        var ret = {ec:'',isEc:true};
        if (url.indexOf('/mp/') > 0) {
          var parser = document.createElement('a');
          parser.href = url;
          var path = parser.pathname;
          ret.ec = path.split('/')[2];
        } else {
          ret.isEc = false
          ret.id = url.match(/wpnews\\/([0-9A-F]{24})/i)[1];
          if(!ret.id){
            return false
          }
        }
        return ret;
      },
      getData: function () {
        if (!this.url) {
          return RMSrv.dialogAlert('No url');
        }
        var data = this.parseUrl(this.url), self = this;
        if(!data){
          return;
        }
        var url ='/mp/decData';
        if(!data.isEc){
          self.id=[data.id]
          return self.initData()
        }
        this.$http.post(url, {ec:data.ec}).then((ret) => {
          // success callback
          ret = ret.data;
          if (ret.e) {
            RMSrv.dialogAlert(ret.e);
          } else {
            if (ret.id.indexOf(',') > 0) {
              self.id = ret.id.split(',');
            } else {
              self.id = [ret.id];
            }
            self.initData();
          }
        }, (ret) => {
          // error callback
          console.error(ret);
        });
      },
      postAction( action ){
        this.$http.post('/1.5/user/wecardManage', {deleteReason:this.deleteReason, action:action, id:this.id[0]}).then((ret) => {
          // success callback
          ret = ret.data;
          if (!ret.ok) {
            RMSrv.dialogAlert(ret.e);
          } else {
            RMSrv.dialogAlert(JSON.stringify(ret));
          }
        }, (ret) => {
          // error callback
          console.error(ret);
        });
      }
    }
  });

  </script>"""

GET 'wecardManage',(req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) -> #sort by time?
    return resp.redirect '/1.5/index' unless req.isAllowed 'userAdmin', user
    resp.ckup 'wecardMgr', {}

POST 'wecardManage', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user) ->
    return resp.send {ok:0} unless req.isAllowed 'userAdmin', user
    id = req.param 'id'
    deleteReason = req.param 'deleteReason'
    return resp.send {ok:0, e:'No id'} unless id
    action = req.param('action')
    if action is 'delete'
      Wecard.deleteOne {_id:id},(err,ret)->
        if err
          resp.send {e:err, ok:false}
        resp.send {ok:true, ret:ret}
      return
    update = {$set:{del:true}}
    if deleteReason
      update.$set.del_rsn = deleteReason
    if action is 'show'
      update = {$unset:{del:1}}
    Wecard.findOneAndUpdate { _id:id },update,{upsert:false,returnDocument:'after'},(err,r) ->
      if err
        resp.send {e:err, ok:false}
      else if r?.value
        resp.send {ok:true}
      else
        resp.send {ok:false, e:"Not updated"}
