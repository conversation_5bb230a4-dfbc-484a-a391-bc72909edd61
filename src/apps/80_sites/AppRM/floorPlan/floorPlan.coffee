config = CONFIG('resourceLimiter')
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
InRealNoteModel = MODEL 'InRealNoteInst'
{respError} = INCLUDE 'libapp.responseHelper'
CondoModel = MODEL 'Condo'
FloorPlanModel = MODEL 'Floorplans'
PropertiesModel = MODEL 'Properties'
floorPlanLibapp= INCLUDE 'libapp.floorPlan'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
resourceLimiter = INCLUDE 'lib.resourceLimiter'
resourceLimiter._config(config)
Floor_Plan_Key = 'floorPlan'
{ stringify } = INCLUDE 'lib.helpers_string'
debug = DEBUG()

CALLBEFOREEXIT resourceLimiter.updateResourceLimiter

IMG_HOST = 'http://img.realmaster.com/FPA'
APP 'floorPlan'

# 判断是否登录和获取用户权限是否为inreal group
isAuthedToUseFloorPlan = (userId,isEditorAdmin) ->
  result = {result: false,msg: '',isInRealGroup:false}
  unless userId
    result.msg = MSG_STRINGS.NEED_LOGIN
    return result
  try
    isInRealGroup = await GroupModel.isInGroup {uid:userId,groupName:':inReal'}
  catch err
    debug.error err
    result.msg = MSG_STRINGS.DB_ERROR
    return result
  unless (isInRealGroup or isEditorAdmin)
    result.msg = MSG_STRINGS.NO_AUTHORIZED
    return result
  return {result: true, msg: '', isInRealGroup:true}

# 拆分处理building图片为前端需要的格式
splitBuildingPic = (buildingPicOrig) ->
  unless buildingPicOrig?.length
    return []
  buildingPic = floorPlanLibapp.splitArrayDataToSingleRecord({
    arrayOrig: buildingPicOrig,
    splitField:  'imgs',
    innerField: {img:'img',w:'w',h:'h'}
  })
  if buildingPic.length
    buildingPic = replaceImagePath(buildingPic)
  return buildingPic

# 拆分处理floorplan图片为前端需要的格式
splitFloorPlanPic = (floorPlanPicOrig,prop,userId) ->
  unless floorPlanPicOrig?.length
    return []
  floorPlanPic = floorPlanLibapp.splitArrayDataToSingleRecord({
    arrayOrig: floorPlanPicOrig,
    splitField: 'imgs',
    innerField: {img:'img',w:'w',h:'h'}
    outerField: {_id:'id',bdrms:'bdrms',bthrms:'bthrms',price:'price',sqft:'sqft',nm:'nm',matchUids:'matchUids',verified:'verified',unts:'unts',src:'src'}
  })
  if floorPlanPic.length
    floorPlanPicAddFceAndUnt = floorPlanLibapp.dealMatchUntAndFceAndBdrm(floorPlanPic,prop?.unt)
    floorPlanPicMatch = floorPlanLibapp.getMatchUidsAddUnt(floorPlanPicAddFceAndUnt,prop?.unt,userId)
    sortFloorPlanPic = floorPlanLibapp.sortFloorPlanPicAccordingProp(floorPlanPicMatch, prop)
    floorPlanPicPath = replaceImagePath(sortFloorPlanPic)
    return floorPlanPicPath
  return []

# /floorPlan/fpAndBuildingImage
# 获取收集的building，floor Plan图片和指定uaddr的经济上传的笔记图片，其中floor plan和笔记图片需要admin，editor和房大师经济权限
POST 'fpAndBuildingImage',(req,resp)->
  uaddr = req.body?.uaddr
  propId = req.body?.propId
  isFloorPlan = req.body?.isFloorPlan
  unless uaddr
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp},(user)->
    isEditorAdmin = req.isAllowed('editorAdmin')
    canAccess = false # 是否可以访问floorPlan
    try
      {result,msg,isInRealGroup} = await isAuthedToUseFloorPlan(user?._id,isEditorAdmin)
      if msg is MSG_STRINGS.NEED_LOGIN
        return respError {clientMsg:MSG_STRINGS.NEED_LOGIN,resp}
      if result and isFloorPlan # 如果直接从详情页点击layout直接显示floorplan，此时记一次资源访问
        canAccess = await resourceLimiter.applyAccess(user?._id, Floor_Plan_Key)
      else if result # 点击房源详情的图片，有资格查看，但是不一定要去看floorplan
        canAccess = await resourceLimiter.canAccess(user._id, Floor_Plan_Key)
      {buildingPicOrigin,floorPlanPicOrigin} = await FloorPlanModel.getFloorPlanAndBuildingImg({canAccess,uaddr,isAuthed: result})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    buildingPic = splitBuildingPic(buildingPicOrigin)
    unless result # 普通用户
      return resp.send {ok:1,result:{buildingPic}}
    unless propId
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    try
      prop = await PropertiesModel.findOneByIDAsync propId,{city:1,prov:1,addr:1,sid:1,saletp:1,ptype2:1,id:1,unt:1,bdrms:1,tbdrms:1,br_plus:1,fce:1}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    floorPlanPic = splitFloorPlanPic(floorPlanPicOrigin,prop,user?._id)
    return resp.send {ok:1,result:{prop,buildingPic,floorPlanPic: floorPlanPic}}

# /floorPlan/updateCondoDescZh
# 更新 Condo 描述的中文简介
POST 'updateCondoDescZh',(req,resp)->
  uaddr = req.body?.uaddr
  desc_zh = req.body?.desc_zh
  unless uaddr and desc_zh
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {clientMsg:MSG_STRINGS.NEED_LOGIN,resp}
    try
      result = await CondoModel.update uaddr, {desc_zh}
    catch error
      debug.error error
      return respError {clientMsg: MSG_STRINGS.INTERNAL_ERROR, resp:resp}
    return resp.send {ok:1}

# floorPlan/updateMatch
# 房大师经纪针对floorplan和unt点击match和取消自己点的match
POST 'updateMatch',(req,resp)->
  id = req.body?.id # 要更新的floorplan _id
  unt = req.body?.unt
  propId = req.body?.propId
  isMatch = req.body?.isMatch
  floopPlanId = req.body?.floopPlanId # 已经点过match的floorplan _id
  unless id and propId and unt
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp},(user)->
    try
      {result,msg,isInRealGroup} = await isAuthedToUseFloorPlan(user?._id,req.isAllowed('editorAdmin'))
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless result
      debug.error {msg:"updateMatch:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user?._id,eml:user?.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      {err,result} = await FloorPlanModel.updateMatch {id,unt,propId,uid:user._id,isMatch,floopPlanId}
    catch error
      debug.error error
      return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp}
    return respError {clientMsg:req.l10n(err), resp}  if err
    return resp.send {ok:1}

# floorPlan/condoBasicInfo 获取building基本信息给到前端进行显示
POST 'condoBasicInfo',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    returnInfo =
      hasCondoInfo:false
      canAccess: false
    return resp.send {ok:1, result:returnInfo} unless user
    unless uaddr = req.body?.uaddr
      return resp.send {ok:1, result:returnInfo}
    projection =
      units: 1
      nm: 1
      desc: 1
      dvlpr: 1
      age: 1
      stories: 1
      desc_zh: 1
      floorplan: 1
    try
      {result,msg,isInRealGroup} = await isAuthedToUseFloorPlan(user?._id,req.isAllowed('editorAdmin'))
      condoInfo = await CondoModel.findById(uaddr, projection)
      if result
        canAccess = await resourceLimiter.canAccess(user._id, Floor_Plan_Key)
        returnInfo.canAccess = canAccess
    catch err
      debug.error err
      return resp.send {ok:1, result:returnInfo}
    unless condoInfo
      return resp.send {ok:1, result:returnInfo}
    returnInfo.hasCondoInfo = true
    returnInfo.hasFloorPlan = false
    if condoInfo.floorplan?.length
      returnInfo.hasFloorPlan = true
    delete projection.floorplan
    for key of projection
      if condoInfo[key]?
        valueNm = 'condo' + key.charAt(0).toUpperCase() + key.slice(1)
        returnInfo[valueNm] = condoInfo[key]
    return resp.send {ok:1, result:returnInfo}

# floorPlan/exchange 增加floorplan资源使用次数
POST 'exchange',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    try
      {result,msg,isInRealGroup} = await isAuthedToUseFloorPlan(user?._id,req.isAllowed('editorAdmin'))
      unless result
        debug.error {msg:"exchange:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user?._id,eml:user?.eml}
        return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
      canAccess = await resourceLimiter.applyAccess(user?._id, Floor_Plan_Key)
    catch err
      debug.error err
      return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp}
    return resp.send {ok:1}

# 替换图片路径
replaceImagePath = (imgsArray) ->
  for item in imgsArray
    item.img = replaceRM2REImagePath(item.img, IMG_HOST)
  return imgsArray
