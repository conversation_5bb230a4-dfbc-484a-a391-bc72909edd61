SaveCMAModel = MODEL 'SaveCMA'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
{dateFormat} = INCLUDE 'lib.helpers_date'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'

APP '1.5'
APP 'saveCMA', true

# /1.5/saveCMA/getCMAIds
# 根据用户查找1天以内的ids
POST 'getCMAIds', (req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      result = await SaveCMAModel.getIdsByUid user._id
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    for item in result
      item._mt = dateFormat item._mt,'YYYY-MM-DD HH24:MI:SS'
    return resp.send {ok:1,result}

# /1.5/saveCMA/saveCMAIds
# 从手机端的CMA，选择房源保存ids，用于web端
# @input：ids {array} - A necessary param
POST 'saveCMAIds', (req, resp)->
  ids = req.body?.ids
  if (not ids) or (ids.length is 0) or (ids.includes('undefined'))
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or (req.isAllowed 'devGroup'))
      debug.error {msg:"saveCMA/saveCMAIds:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await SaveCMAModel.saveIds {uid:user._id,ids,_id:req.body?._id}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    return resp.send {ok:1,msg:req.l10n('CMA saved, ready to edit in web realmaster')}

# /1.5/saveCMA/saveCustomContent
# 保存app端CMA编写的自定义内容
# @input: content {array} - A necessary param
# eg:[{bltYr: "1987(Estimated)111",id:"TRBC5763599",remark: "1111",size:"800-899 ft²111"},{...}]
POST 'saveCustomContent', (req, resp)->
  unless content = req.body?.content
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or (req.isAllowed 'devGroup'))
      debug.error {msg:"saveCMA/saveCustomContent:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      await SaveCMAModel.saveCustomContent(user._id,content)
    catch err
      debug.error err
      return respError {category:req.l10n('CMA saved faild, please try again'),resp}
    return resp.send {ok:1}
