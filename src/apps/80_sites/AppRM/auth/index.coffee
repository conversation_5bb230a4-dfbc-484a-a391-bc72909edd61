###
Design: https://previews.dropbox.com/p/thumb/AAyubNB1jYgEvcBC_uS1Dd-lrEHgZpwZvC91VCWNTfJs2oHHkwfF3BM0bz6ggVtYEfo1_Q8qEI4Stycq_CgVhw5moR1u8vJPRGz7lOaVA4Dml-g1eDj6RLdqFIkLdxsiGi0mfZOpgZLBnmyuyysHU2t05yP0_pv5t7-gfLcdJnAcQVrDgM6EDyiL1vhiIavPmzn_fo3pnm3iWMrkjpYOoUIbX8v87x5BoaC5VDKIOGizGmoKT0z2srJ906OTWs2Tufwjh37RBjiKKuczY-beDbtR3ltR1ZE3A7deNhyQX4NMpEz6UPZCPVUcPiuF1fC5yoMwgZh2FevLYIqArp2cnNyod_cdBqmJqfae8E9fj-5ccQ/p.jpeg?fv_content=true&size_mode=5
###
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{
  getGoogleAccount,
  getFBAccount,
  getAppleAccount,
  setLastPageSession,
  getLastOauthUserData,
  handleOauthUser,
  redirectToUrl
} = INCLUDE 'libapp.oauth'
sendMailLib = INCLUDE 'lib.sendMail'
sendMailService = SERVICE 'sendMail'

config = CONFIG(['serverBase','mailEngine','contact'])
UserModel = MODEL 'User'

getDotHtml = DEF 'getDotHtml'

buildLoginPageUrl = (err)->
  loginPageUrl = '/www/login'
  if err
    loginPageUrl += "?err=#{err.toString()}"
  return loginPageUrl

APP 'oauth'

POST 'apple',(req,resp)->
  code = req.body.code
  user = req.body.user
  uri = "#{req.getProtocol()}://#{req.host}"
  getAppleAccount code,uri,(err, uData)->
    return redirectToUrl req,resp,buildLoginPageUrl(err) if err
    if user
      try
        user =  JSON.parse(user)
        if name = user.name
          uData.firstName = name.firstName
          uData.lastName = name.lastName
          uData.name = "#{name.firstName} #{name.lastName}"
      catch err
        return redirectToUrl req,resp,buildLoginPageUrl(err) if err
    handleOauthUser {sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
      return redirectToUrl req,resp,buildLoginPageUrl(err) if err
      redirectToUrl req,resp,redirectUrl

# /oauth/(facebook|google)?code=laksjdflas&state=asdklfjaldks
GET ':name', (req, resp) ->
  code = req.param 'code'
  name = req.param 'name'
  if name is 'thirdParty'
    uData = getLastOauthUserData(req)
    return redirectToUrl req,resp,buildLoginPageUrl() unless uData
    handleOauthUser {isThirdPartyAuth:1,sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
      return redirectToUrl req,resp,buildLoginPageUrl(err) if err
      return redirectToUrl req,resp,redirectUrl
    return
  return redirectToUrl req,resp,buildLoginPageUrl() unless code or (name not in ['google','facebook'])
  switch name
    when 'google' then getAccount = getGoogleAccount
    when 'facebook' then getAccount = getFBAccount
  uri = "#{req.getProtocol()}://#{req.host}"
  if not getAccount
    debug.error 'Error: Oauth: invalid callback name',name,code
    return resp.ckup 'generalError', {err_tran:req.l10n(MSG_STRINGS.BAD_PARAMETER)}
  getAccount code,uri, (err, uData) ->
    return redirectToUrl req,resp,buildLoginPageUrl(err) if err
    handleOauthUser {isThirdPartyAuth:1,sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
      return redirectToUrl req,resp,buildLoginPageUrl(err) if err
      redirectToUrl req,resp,redirectUrl

POST 'bind', (req, resp) ->
  return resp.redirect '/1.5/user/login' unless user = req.session.get 'user'
  resp.send {ok:1}
