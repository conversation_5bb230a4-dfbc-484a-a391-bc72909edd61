debug = DEBUG()
oauthJwtUtil = INCLUDE 'lib.oauthJwtUtil'
UserModel = MODEL 'User'
OAuthProviderGrantModel = MODEL 'OAuthProviderGrant'
config = CONFIG(['oauthProvider'])

# Validate essential OAuth configuration
unless config?.oauthProvider?.secret
  debug.error 'Missing OAuth provider secret'
  throw new Error('Missing OAuth provider secret. Please check your config file.')

unless Array.isArray(config?.oauthProvider?.clients) and config.oauthProvider.clients.length > 0
  debug.error 'Invalid OAuth provider clients configuration'
  throw new Error('OAuth provider clients configuration must be a non-empty array.')

# Add plugin for verifying OAuth access tokens
ADDPLUGIN 'hasValidAccessToken', (authHeader) ->
  unless authHeader?.startsWith('Bearer ')
    return {error: MSG_STRINGS.BAD_PARAMETER, status: 401}

  token = authHeader.substring(7)
  # Verify access token
  decoded = oauthJwtUtil.verifyAccessToken(token)
  unless decoded
    return {error: MSG_STRINGS.INVALID_SIGNATURE, status: 401}
  
  return {decoded}

oauthConfig = 
  secret: config.oauthProvider.secret
  clients: {}
  jwtOptions:
    algorithms: 'HS256'
    issuer: 'realmaster-app'
    audience: 'realmaster-api'

# Convert array of clients to object format expected by oauthProvider
for client in config.oauthProvider.clients
  oauthConfig.clients[client.clientId] = 
    clientId: client.clientId
    clientSecret: client.clientSecret
    redirectUri: client.redirectUri

debug.debug 'oauthConfig', oauthConfig

# Initialize OAuth provider with config
oauthJwtUtil.initialize(oauthConfig)


APP 'provider'

# Helper function to handle logged in user authorization
handleLoggedInAuthorization = (userId, clientId, redirectUri, state) ->
  debug.debug 'User already logged in, generating auth code for:', {userId, clientId}
  try
    # Validate redirect URI
    unless oauthJwtUtil.validateRedirectUri(clientId, redirectUri)
      debug.error 'Invalid redirect URI:', {clientId, redirectUri}
      return {error: MSG_STRINGS.BAD_PARAMETER}
      
    {redirectUrl} = await OAuthProviderGrantModel.generateAuthCodeAndRedirectUrl(userId, clientId, redirectUri, state)
    debug.debug 'Redirecting to client:', redirectUrl
    return {redirectUrl}
  catch err
    debug.error 'Error generating auth code:', err
    return {error: MSG_STRINGS.INTERNAL_ERROR}

# Helper function to build login URL
buildLoginUrl = (clientId, redirectUri, state) ->
  debug.debug 'User not logged in, redirecting to login page'
  loginUrl = "/www/login?client_id=#{clientId}&redirect_uri=#{encodeURIComponent(redirectUri)}"
  loginUrl += "&client_state=#{encodeURIComponent(state)}" if state
  debug.debug 'Login URL:', loginUrl
  return loginUrl

# /provider/authorize
# Authorization endpoint - Client redirects user here to login
GET 'authorize', (req, resp) ->
  clientId = req.query.client_id
  redirectUri = req.query.redirect_uri
  state = req.query.client_state
  
  debug.debug 'Authorization request received:', {clientId, redirectUri, state}

  # Validate required parameters
  unless clientId and redirectUri
    debug.error 'Missing required parameters:', {clientId, redirectUri}
    return resp.ckup 'generalError', {err: MSG_STRINGS.BAD_PARAMETER}

  # If user is already logged in, generate auth code and redirect
  # user could have k instead of session cmate.sid, should use authUser
  UserModel.appAuth {req,resp}, (user)->
    if not user
      # User not logged in - redirect to login with OAuth parameters
      loginUrl = buildLoginUrl(clientId, redirectUri, state)
      return resp.redirect loginUrl
    result = await handleLoggedInAuthorization(user._id, clientId, redirectUri, state)
    if result.error
      return resp.ckup 'generalError', {err: result.error}
    return resp.redirect result.redirectUrl


# Helper function to handle refresh token requests
handleRefreshTokenRequest = (refreshToken, clientId) ->
  debug.debug 'Refresh token request received'
  
  # Verify the refresh token
  decoded = oauthJwtUtil.verifyRefreshToken(refreshToken)
  unless decoded
    return {error: MSG_STRINGS.BAD_PARAMETER, status: 401}

  # Verify the refresh token was issued to this client
  unless decoded.clientId is clientId
    return {error: MSG_STRINGS.BAD_PARAMETER, status: 401}

  try
    # Use transaction to validate and use refresh token
    result = await OAuthProviderGrantModel.validateAndUseRefreshTokenWithTransaction(decoded)
    if result.error
      debug.error 'Refresh token validation failed:', result.error
      return {error: result.error, status: result.status}
      
    debug.debug 'Refresh token exchange successful'
    return {
      accessToken: result.accessToken,
      refreshToken: result.refreshToken
    }
  catch err
    debug.error 'Error during refresh token exchange:', err
    return {error: MSG_STRINGS.INTERNAL_ERROR, status: 500}

# Helper function to handle authorization code requests
handleAuthCodeRequest = (code, clientId, redirectUri) ->
  debug.debug 'Starting authorization code exchange', {code, clientId}
  
  try
    #确保在验证和移除授权码的过程中的数据一致性。如果验证授权码后但在移除前发生错误，会导致授权码可能被重用。
    result = await OAuthProviderGrantModel.exchangeAuthCodeWithTransaction(code, clientId, redirectUri)
    if result.error
      debug.error 'Authorization code exchange failed:', result.error
      return {error: result.error, status: result.status}
      
    debug.debug 'Authorization code exchange successful'
    debug.debug 'Generated access token:', result.accessToken.substring(0, 10)
    debug.debug 'Generated refresh token:', result.refreshToken.substring(0, 10)
    
    return {
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
    }
  catch err
    debug.error 'Error during authorization code exchange:', err
    return {error: MSG_STRINGS.INTERNAL_ERROR, status: 500}

# /provider/token
# Token endpoint - Client exchanges auth code for access token or refresh token for new tokens
POST 'token', (req, resp) ->
  {code, refresh_token, grant_type, client_id, signature, redirect_uri, timestamp} = req.body
  
  debug.debug 'Token request received:', {code, grant_type, client_id, redirect_uri, timestamp}
  
  # Validate client credentials using HMAC signature
  client = oauthJwtUtil.validateClient(client_id, signature, timestamp)
  unless client
    debug.error 'Invalid client credentials'
    return resp.send {ok:0, err:MSG_STRINGS.BAD_PARAMETER}, 401

  if grant_type is 'refresh_token'
    unless refresh_token
      debug.error 'Missing refresh token'
      return resp.send {ok:0, err:MSG_STRINGS.BAD_PARAMETER}, 400

    result = await handleRefreshTokenRequest(refresh_token, client_id)
    if result.error
      return resp.send {ok:0, err: result.error}, result.status

    return resp.send
      access_token: result.accessToken
      refresh_token: result.refreshToken

  else
    unless code and client_id and redirect_uri
      debug.error 'Missing required parameters for token request'
      return resp.send {ok:0, err:'Missing required parameters'}, 400

    debug.debug 'Authorization code request received'

    result = await handleAuthCodeRequest(code, client_id, redirect_uri)
    if result.error
      return resp.send {ok:0, err: result.error}, result.status

    return resp.send
      access_token: result.accessToken
      refresh_token: result.refreshToken

# /provider/userinfo
# User info endpoint - Client uses access token to get user info
GET 'userinfo', (req, resp) ->
  # Verify token using the plugin
  result = req.hasValidAccessToken(req.headers['authorization'])
  if result.error
    return resp.send {ok:0, err:result.error}, result.status

  # Get user info using findById with callback
  UserModel.findById result.decoded.userId, {}, (err, user) ->
    return resp.send {ok:0, err:MSG_STRINGS.DB_ERROR}, 500 if err
    return resp.send {ok:0, err:MSG_STRINGS.USER_NOT_FOUND}, 404 unless user

    # Return minimal user info
    resp.send
      id: user._id.toString()
      email: user.eml[0]
      name: user.nm or ''
      # phone: user.mbl or '' # not send phone for now