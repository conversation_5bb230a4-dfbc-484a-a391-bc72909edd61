###
_id: auto id
ts, mt, m
nm: group name (unique)
desc: group Description()
members:[] uid, eml, mbl, pn, nm_en, nm_zh, avt, ts, mt, lts, role:(admin)
pendings:[] uid, eml, mbl, pn, nm_en, nm_zh, avt, ts, lts
quit:[] uid, eml, mbl, pn, nm_en, nm_zh, avt, ts, lts
cnt: Members counter, sync with members whenever update members array
uid: owner id
pub: number (public group member upper limit. Public group can accept non-vip members, only organizer user can create. Can not give ownership to others)

index: members.uid + members.role
unique index: nm
group name (unique, Started with ':' for system group, can not be created by UI. First claimer for that system group becomes owner of that group.)

session: gids:[] 'group ids'


GroupForum: (different collection, same layout as forum)
gid: group id
uid: creater of this forum, must be a member of this group
readers:[] {uid,ts,mt,nm} record who read this forum

group admin or owner, and only them, can add form onto group forum
###
helpers = INCLUDE 'lib.helpers'#DEF 'helpers'
libUser = INCLUDE 'libapp.user'
libPropertyImage = INCLUDE 'libapp.propertyImage'

GroupModel = MODEL 'Group'
GroupForum = COLLECTION 'chome', 'group_forum'
User  = COLLECTION 'chome', 'user'#需要用到obiectid

UserModel = MODEL 'User'
ActionCodeModel = MODEL 'ActionCode'
SysNotifyModel = MODEL 'SysNotify'
{respError} = INCLUDE 'libapp.responseHelper'
debug = DEBUG()

dataMethods = DEF 'dataMethods'

MSG_STRINGS.def {
  SYSTEM_ERROR: 'System Error'
  NEED_LOGIN:   'Need login'
  NO_PARAM:     'No parameter'
  NEED_VIP:     'Need VIP Permission'
  STRING_TOO_SHORT_OR_LONG: 'String is too short or too long.'
  YOU_ARE_MEMBER_ALREADY: 'You are a member already.'
  YOUR_MEMBERSHIP_PENDING: "Your membership is pending admin's approval."
  CANNOT_CREATE_SYSTEM_GROUP: 'Can not create system group.'
  NO_GROUP_SELECTED: 'No group selected.'
  NO_GROUP_OR_NO_RIGHT: 'Group not exists or no right to operate on it.'
  ERR_MODIFY_SELF: 'Cannot complete action.'
  NO_MATCHED_USER: 'No matched users.'
}


dataMethods.userGroups = (req, user, cb)->
  return req.userGroups user,cb
# opt: sysOnly
dataMethods.isGroupAdmin = (req, user)->
  return req.isAllowed 'groupAdmin',user

# DEF 'getMyGroups',(uid,opt,cb)-> # TODO: return err,groups[]{_id,nm,cnt}
#   return []

# gid is ObjectId or string for group name
# findToArray members.uid = user._id
# DEF 'isInGroup',(uid,gidOrGnm,cb)->
#   unless uid?.length is 24
#     return cb 'Bad Uid'
#   q = {}
#   if gidOrGnm?.length is 24
#     q._id = gidOrGnm
#   else
#     g.nm = gidOrGnm
#   Group.findOne q,(err,group)->
#     return cb err if err
#     group.members ?= []
#     for m in group.members
#       if isSameObjectId uid,m.uid
#         return cb null,true
#     return cb null.false

APP 'group'

memberFields =
  _id: 1
  eml: 1
  mbl: 1
  pn:  1
  nm_en: 1
  nm_zh: 1
  avt: 1
  lts: 1
  wx:1

sendPendingNotify = (req,user,group)->
  uids = []
  if not group.members?.length
    return
  for m in group.members
    if m.role is 'admin'
      uids.push m.uid.toString()
  UserModel.getListByIds uids,{projection:UserModel.MEMBER_FIELDS},(err,toSendUsers)->
    return console.error err if err
    return console.log 'No user to notify' unless toSendUsers?.length > 0
    eml = GroupModel.firstElement user.eml
    cfg = {
      title:"User #{eml} wants to join group #{group.nm}",
      url:'/group',
      msg:"#{eml}(#{user.nm_zh}) in pending state",
    }
    methods = 'pn'
    notifyUserWithMessage cfg,toSendUsers,methods,(err,ret)->
      return console.error err if err
      console.log 'Notified pending state'

addUserToGroup = (req,user,group,opt={},cb)->
  unless cb?
    cb = opt
    opt = {}
  # TODO: notify admins
  if opt.pending
    sendPendingNotify(req,user,group)
  # use this one, if need to return updated group
  # Group.findOneAndUpdate {_id:group._id},update,{upsert:false,returnDocument:'after'},(err,newGroup)->
  #   cb err,newGroup
  GroupModel.addUserToGroup {id:group._id,user,opt},(err,result)->
    if result?.result?.nModified is 0
      cb 'No one was added'
    cb err,result


ActionCodeModel.regAction 'joinGroup',(params,cb)->
  return cb MSG_STRINGS.NO_PARAM unless params
  return cb "No request object" unless params.req
  return cb "No group specified" unless gid = params.data?.gid
  UserModel.appAuth {req:params.req,resp:params.req.resp}, (user)->
    return cb MSG_STRINGS.NEED_LOGIN unless user
    addUserToGroup params.req,user,{_id:gid},{pending:true},(err,result)->
      if err
        console.error err
        return cb MSG_STRINGS.SYSTEM_ERROR
      cb null,result
# TODO:
# when create action code, need parameters:
# {data:{gid:group}}
# procedure:
#  1. client post /qrcode/createAction  {data:{gid:gid}}, return {url:toJumpUrl}
#  2. client popup or jump to returned url, or show error
#  3. user use realmaster scanner scan qr, get realmaster://qrcode/act:code
#  4. popup webview to /qrcode/act:code


ajaxError = (resp,err,url)->
  ret = err:err
  ret.url = url if url
  resp.send ret

dbError = (req,resp,err)->
  console.error err
  req.logger?.error err
  return ajaxError resp, req.l10n(MSG_STRINGS.SYSTEM_ERROR)

getGroupAvt = (groupAvt)->
  unless groupAvt
    return '/img/icon_nophoto.png'
  return libPropertyImage.replaceRM2REImagePath groupAvt

# group list page
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    GroupModel.getMyGroups {uid:user._id,isGroupAdmin:req.isAllowed 'groupAdmin'},(err,myGroups)->
      myGroups ?= []
      popGroupListInfo myGroups,user,(err, ret)->
        return dbError req,resp,err if err
        resp.ckup 'group-list-page',{groups:ret}

#parse group list info
popGroupListInfo = (list,user, cb)->
  userIdMapping = {}
  gids = list.map((g)->
    return g._id
    )
  uids = list.map((g)->
    return g.uid
    )
  GroupForum.aggregate ([
    {$match: {'gid': {$in:gids} }},
    { $group : { _id:"$gid", count: { $sum: 1 } } } ]),{cursor:{batchSize:0}},(err,results)->
    return cb MSG_STRINGS.SYSTEM_ERROR if err
    UserModel.getListByIds uids,{},(err,userList)->
      for u in userList
        userIdMapping[u._id.toString()] = u
      for g in list
        group = results.find((e)->
          return isSameObjectId(e._id, g._id)
          )
        g.faCnt = group?.count || 0
        if isSameObjectId(g.uid, user._id)
          g.isOwner = true
          # g.img = user.avt
        if userIdMapping[g.uid]?.avt
          g.img = userIdMapping[g.uid].avt
        # console.log '+++',g,g.members
        g.isMember = false
        if g.members?.length
          for m in g.members
            if isSameObjectId(user._id,m.uid)
              g.isMember = true
              if (m.role is 'admin')#and isSameObjectId(user._id,m.uid)
                g.isAdmin = true
        g.img = getGroupAvt g.img
        delete g.members
        delete g.pendings
        delete g.quit
      cb(null, list)

fillGrpArrVal = (req,grp,user,userIdMapping,ret,i)->
  if grp[i]?.length
    for m in grp[i]
      for k,v of memberFields
        m[k] = GroupModel.firstElement userIdMapping[m.uid.toString()]?[k]
      m.avt = getGroupAvt m.avt
      m.fnm = libUser.fullNameOrNickname(req.locale(),m)
      if i isnt 'members'
        continue
      if isSameObjectId(m.uid, grp.uid)
        m.isOwner = true
        m.role = 'admin' #auto set admin if owner
        grp.img = m.avt
      if m.role is 'admin'
        m.isAdmin = true
        if isSameObjectId(m.uid, user._id)
          ret.user.isAdmin = true

# parse group members info
popGroupMemberInfo = (req,grp,user,cb)->
  userIdMapping = {}
  ids = []
  ret = {
    user:{uid:user._id}
    group:grp
  }
  for tp in ['members','pendings','quit']
    if grp[tp]?.length > 0
      for m in grp[tp]
        ids.push m.uid
  unless ids.length
    return cb null,ret
  if isSameObjectId(user._id, grp.uid)
    ret.user.isOwner = true
  UserModel.getListByIds ids,{projection:UserModel.MEMBER_FIELDS},(err,userList)->
    if err
      console.error err
      cb err
    for u in userList
      userIdMapping[u._id.toString()] = u
    for i in ['members','pendings','quit']
      fillGrpArrVal(req,grp,user,userIdMapping,ret,i)
    # grp.faCnt = 2
    ret.group.img = getGroupAvt ret.group?.img
    cb null,ret

POST 'list',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NEED_LOGIN),'/1.5/user/login' unless user
    body = req.body
    GroupModel.getGroupList {user,uids:body.uids,pg:body.pg,nm:body.name,\
        isGroupAdmin:req.isAllowed 'groupAdmin'},(err,myGroups)->
      return dbError req,resp,err if err
      myGroups ?= []
      popGroupListInfo myGroups,user, (err, ret)->
        return dbError req,resp,err if err
        resp.send {ok:1,list:ret}

POST 'update',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings','uid']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless nm = req.param 'nm'
    if not (isGroupOwner(user,group) or isGroupAdmin(user,group))
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
    # if /^:/.test nm
    #   return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
    desc = req.param 'desc'
    GroupModel.updateDesc {id:group._id,nm,desc},(err,ret)->
      return dbError req,resp,err if err
      GroupForum.updateMany {gid:group._id}, {$set:{gnm:nm}},{multi:true}, (err, ret)->
        return dbError req,resp,err if err
        resp.send {ok:1,msg:req.l10n('Updated')}

POST 'detail',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NEED_LOGIN),'/1.5/user/login' unless user
    id = req.param 'id'
    GroupModel.findDetail {id,user,isGroupAdmin:req.isAllowed 'groupAdmin'},(err,group)->
      if not group
        return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
      if req.param 'view'
        desc = group.desc or req.l10n('Group description not set.')
        return resp.send {ok:1,desc:desc}
      popGroupMemberInfo req,group,user,(err,ret)->
        return dbError req,resp,err if err
        # console.log ret.group
        resp.send {ok:1, group:ret.group,user:ret.user}

isSameObjectId = (a,b)->
  return a?.toString() is b?.toString()

# create new group or become system group admin
POST 'createOrJoin',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NEED_LOGIN),'/1.5/user/login' unless user
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless name = req.param 'name'
    # create new group
    if name.length < 3 or name.length > 50
      return ajaxError resp, req.l10n(MSG_STRINGS.STRING_TOO_SHORT_OR_LONG)
    # try become the first member and be owner of the system group
    GroupModel.findGroupsByName {nm:[name]},(err,groupArray)->
      return dbError req,resp,err if err
      # TODO: check if user is vipUser
      groupArray ?= []
      group = groupArray[0]
      if group? and group.uid
        if group.members?.length > 0
          # has members already, check if is a member
          for m in group.members
            if isSameObjectId(m.uid,user._id)
              return ajaxError resp, req.l10n(MSG_STRINGS.YOU_ARE_MEMBER_ALREADY)
        if group.pendings?.length > 0
          # if is a pending member
          for m in group.pendings
            if isSameObjectId(m.uid,user._id)
              return ajaxError resp, req.l10n(MSG_STRINGS.YOUR_MEMBERSHIP_PENDING)
        # try to insert pending
        return addUserToGroup req,user,group,{pending:true},(err)-> #,newGroup)->
          return dbError req,resp,err if err
          resp.send ok:1,msg:req.l10n('Wait for approval') #,group:newGroup
      if /^\:/.test name
        # system group, no members yet, become admin
        if group
          return addUserToGroup req,user,group,{owner:true},(err)-> #,newGroup)->
            return dbError req,resp,err if err
            resp.send ok:1,msg:req.l10n('Joined') #,group:newGroup
        return ajaxError resp,req.l10n(MSG_STRINGS.CANNOT_CREATE_SYSTEM_GROUP)
      return ajaxError resp,req.l10n(MSG_STRINGS.NEED_VIP) unless req.isAllowed 'vipUser'
      GroupModel.createGroup {user,name},(err,group)->
        return dbError req,resp,err if err
        return resp.send ok:1,group:group,msg:req.l10n('Created')

POST 'changeOwner',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings','uid']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless uid = req.param 'uids'
    if not isGroupOwner(user,group)
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
    if isSameObjectId(uid, group.uid)
      return ajaxError resp,req.l10n(MSG_STRINGS.ERR_MODIFY_SELF)
    unless group.members?.length
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER)
    if uid.length is 24
      uid = new User.ObjectId uid
    else
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER)
    target = -1
    for m,idx in group.members
      if isSameObjectId(uid,m.uid)
        target = idx
        break
    if target is -1
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER)
    # have to set role otherwise adminGroup will return ajaxError
    GroupModel.changeRole {id:group._id,uid,target,role:'admin'},(err,group)->
      return dbError req,resp,err if err
      # console.log '+++++',group.result
      msg = 'Set to owner'
      resp.send {ok:1, msg:req.l10n(msg)}

# return user is owner of a group
isGroupOwner = (user,group)->
  return isSameObjectId(user._id, group.uid)

# if user is admin of a group
isGroupAdmin = (user,group)->
  if group?.members?.length
    for m in group.members
      if (m.role is 'admin') and isSameObjectId(m.uid,user._id)
        return true
  return false

POST 'toggleAdmin',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings','uid']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless uid = req.param 'uids'
    if not (isGroupOwner(user,group) or isGroupAdmin(user,group))
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
    #find user.role, toggle
    if isSameObjectId(uid,user._id)
      return ajaxError resp,req.l10n(MSG_STRINGS.ERR_MODIFY_SELF)
    unless group.members?.length
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER)
    role = 'admin'
    target = -1
    for m,idx in group.members
      if isSameObjectId(uid,m.uid)
        target = idx
        if m.role is 'admin'
          # not owner modify admin status
          if (not isGroupOwner(user,group))
            return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
          role = ''
        break
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER) if target < 0
    GroupModel.changeRole {id:group._id,target:idx,role},(err,group)->
      return dbError req,resp,err if err
      # console.log '+++++',group.result
      msg = 'Set to admin'
      unless role
        msg = 'Revoked admin'
      resp.send {ok:1, msg:req.l10n(msg)}

adminGroup = (req,resp,opt,cb)->
  UserModel.appAuth {req,resp}, (user)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NEED_LOGIN),'/1.5/user/login' unless user
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_SELECTED) unless gid = req.param 'gid'
    unless cb
      cb = opt
      opt = {}
    fields = Object.assign {cnt:1},opt.fields
    GroupModel.getGroupById {gid,fields,user,isGroupAdmin:req.isAllowed 'groupAdmin'},(err,group)->
      return dbError req,resp,err if err
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT) unless group
      cb user,group


# add members by admin.
# Must block UI to prevent duplicated adding
POST 'addMembers',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings','quit']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless emls = req.param 'emls'
    emails = []
    for eml in emls.replace(/\n/g, ",").split /[\;\,\s]+/g
      if (eml = helpers.trim eml).length > 5
        emails.push eml
    # get all users， only vip can add in group
    UserModel.getMemberListByEml emails,(err,toAddUsers)->
      return dbError req,resp,err if err
      # insert to group
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER) unless toAddUsers?.length
      GroupModel.addMultipleUsersToGroup {group, users:toAddUsers},(err,result)-> #newGroup)->
        return dbError req,resp,err if err
        # return new group
        #resp.send ok:1,group:newGroup
        resp.send ok:1

# remove single or multiple members in pending or quit or members
# uids must be , separated strings of user._id
POST 'rmMembers',(req,resp)->
  adminGroup req,resp,{fields:['members','uid']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless uids = req.param 'uids'
    members = {}
    if group.members
      for v in group.members
        members[v.uid] = v
    userIds = []
    cntMinus = 0
    for u in uids.split ','
      if u.length is 24
        if isSameObjectId u,user._id
          return ajaxError resp,req.l10n(MSG_STRINGS.ERR_MODIFY_SELF)
        # cannot remove owner
        if isSameObjectId u,group.uid
          return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
        # cannot remove admin by admin(not owner)
        if (not isGroupOwner(user,group)) and (members[u]?.role is 'admin')
          return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
        uid = new User.ObjectId u
        # todo  does cntMinus equal to userIds.length?
        userIds.push uid
        cntMinus++ if members[uid]?
    GroupModel.removeMembers {id:group._id,userIds,cntMinus},(err)->
      return dbError req,resp,err if err
      resp.send ok:1,removedMembers:cntMinus,msg:req.l10n('Removed')

canRemoveGroup = (req, user, group)->
  if req.isAllowed('groupAdmin')
    return true
  if isGroupOwner(user,group)
    return true
  return false

# remove group
POST 'rmGroup',(req,resp)->
  adminGroup req,resp,{fields:['members','uid']},(user,group)->
    if not canRemoveGroup(req, user, group)
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_OR_NO_RIGHT)
    GroupModel.removeGroup {id:group._id},(err)->
      return dbError req,resp,err if err
      resp.send ok:1,msg:req.l10n('Removed')

notifyUserWithMessage = (reqBody={},toSendUsers,methods,cb)->
  methods = methods.split(',')
  return cb('No Method specified') unless methods.length
  body = reqBody['msg']
  tl = reqBody['title']
  return cb('No title or msg') unless (tl or body)
  opt = {
    subject:tl
    engine:'SES'
  }
  for i in ['pn','sms','mail']
    opt[i] = methods.indexOf(i) > -1
  doSend = ()->
    user = toSendUsers.pop()
    unless user
      return cb null,'Done'
    # console.log '++++',user,opt,body
    SysNotifyModel.sendToUser user,opt,body,(err,ret)->
      return cb err if err
      doSend()
  doSend()


POST 'sendNotification',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless uids = req.param 'uids'
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless methods = req.param 'methods'
    uids = uids.split ','
    UserModel.getListByIds uids,{projection:UserModel.MEMBER_FIELDS},(err,toSendUsers)->
      return dbError req,resp,err if err
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER) unless toSendUsers?.length > 0
      notifyUserWithMessage req.body,toSendUsers,methods,(err,ret)->
        return ajaxError resp,err if err
        resp.send ok:1,msg:req.l10n('Sent')

# approve pendings to members
# uids must be , separated strings of user._id
POST 'approveMembers',(req,resp)->
  adminGroup req,resp,{fields:['members','pendings']},(user,group)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_PARAM) unless uids = req.param 'uids'
    uids = uids.split ','
    UserModel.getListByIds uids,{projection:UserModel.MEMBER_FIELDS},(err,toAddUsers)->
      return dbError req,resp,err if err
      return ajaxError resp,req.l10n(MSG_STRINGS.NO_MATCHED_USER) unless toAddUsers?.length > 0
      GroupModel.addMultipleUsersToGroup {group, users:toAddUsers},(err)->
        return dbError req,resp,err if err
        resp.send ok:1,msg:req.l10n('Added')

# member quit the group
POST 'quit',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return ajaxError resp,req.l10n(MSG_STRINGS.NEED_LOGIN),'/1.5/user/login' unless user
    return ajaxError resp,req.l10n(MSG_STRINGS.NO_GROUP_SELECTED) unless gid = req.param 'gid'
    GroupModel.quitGroup {id:gid,user},(err,result)->
      return dbError req,resp,err if err
      resp.send ok:result.result.ok,changed:result.modifiedCount,msg:req.l10n('Quited')

VIEW 'group-list-page',->
  coffeejs {vars:{groups:@groups}},->
    null
  div id:'appGroupManage',->
    text """<app-group-manage></app-group-manage>"""
  js '/web/packs/commons.js'
  js '/web/packs/appGroupManage.js'

# get members by group name.
POST 'getMembers',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    if not req.isAllowed 'noteAdmin'
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    nm = req.body.groupName
    try
      users = await GroupModel.getMembersInfoByName nm
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    return resp.send {ok:1,users} if not users.length
    for u in users
      u.fnm = libUser.fullNameOrNickname(user.locale,u)
      u.avt = libPropertyImage.replaceRM2REImagePath u.avt if u.avt
    users.sort (a,b)-> a.fnm.localeCompare(b.fnm)
    return resp.send {ok:1,users}
