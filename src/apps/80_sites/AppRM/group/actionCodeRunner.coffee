ActionCodeModel = MODEL 'ActionCode'
{formatCmntTs} = INCLUDE 'lib.helpers_date'
debug = DEBUG()

APP 'qrcode'

# 创建qrcode
POST 'createAction',(req,resp)->
  returnError = (err,noL10n)->
    resp.send err:if noL10n then err else req.l10n(err)
  return returnError 'No Action Name' unless actionName = req.param 'action'
  return returnError 'Unknown Action Name' unless ActionCodeModel.hasAction actionName
  data = req.param 'data' # an object
  opt = data: data
  opt.exp = exp if exp = req.param 'exp'
  opt.once = once if once = req.param 'once'
  try
    code = await ActionCodeModel.createCode actionName,opt
  catch err
    debug.error err
    return returnError err,true
  return returnError 'Can not generate code.' unless code
  url = req.fullUrl("/qrcode/show#{code}")
  resp.send {url:url}


GET 'show:code',(req,resp)->
  showError = (err)->
    resp.ckup 'showError',{error:req.l10n(err)}
  return showError 'No Code' unless code = req.param('code')
  try
    action = await ActionCodeModel.getCode code
  catch err
    debug.error err
    return showError err
  return showError 'Can not find the code' unless action
  l10n = (a,b)->req.l10n a,b
  action.expAt = formatCmntTs action.expAt,l10n
  resp.ckup 'showQRCode',{
    url: "realmaster://qrcode/act#{code}"
    action:action
    bgimg: '/img/logo.png'
  }

# 如果结果返回中有url，跳转u至rl指定页面
GET 'act:code',(req,resp)->
  showError = (err)->
    resp.ckup 'showError',{error:req.l10n(err)}
  return showError 'No Code' unless code = req.param('code')
  try
    ret = await ActionCodeModel.performAction code,req
  catch err
    debug.error err
    return showError err,true
  if ret.url
    return resp.redirect ret.url + '&code=' + code
  resp.ckup 'done',{msg:req.param('msg')},'_',{no_angular:true}

VIEW 'showError',->
  header class: 'bar bar-nav', ->
    h1 class: 'title', ->  _ 'RealMaster'
    a href:'/1.5/index',-> _ 'Home'
  div class: 'content',style:'background-color: #F3F3F3;', ->
    div style: 'position: absolute;top: 20%;margin:10px;width:95%;', ->
      p style:'text-align:center;margin-bottom:50px;',->
        span class:'fa fa-exclamation-circle',style:'color:#F6CC31;font-size:100px;'
      p style:'text-align:center;color:#000;font-size: 15px;',->
        text @error
      a href:'/1.5/index',class:'btn btn-long btn-block btn-positive',style:'margin-top: 70px;', -> _ 'Go Home'

VIEW 'showQRCode',->
  div id:'qrholder',
  style:'width:300px;height:300px;padding:10px;margin-left:auto;margin-right:auto;margin-top: 20%;'
  div style:'margin: 20px;text-align: center;',-> _ 'Please scan the follow QRCode in RealMaster APP'
  div style:'margin: 20px;text-align: center;',->
    span -> _ 'Expiry Date'
    span -> ': '
    span -> text @action.expAt + ' '
    span -> _ 'After'
  js '/js/qrcode/require.js'
  coffeejs {vars:{url:@url,bgimg:@bgimg}},->
    __awesome_qr_base_path = '/js/qrcode'
    require [__awesome_qr_base_path + '/awesome-qr.js'],(aqr)->
      img = new Image()
      img.crossOrigin = 'Anonymous'
      img.onload = ->
        aqr.create {
          text:vars.url
          size: 300
          margin: 10
          bindElement: 'qrholder'
          backgroundImage: img
        }
      img.src = vars.bgimg

    null


VIEW 'done',->
  div style:'padding:20px;',->
    a href:'/1.5/index',-> _ 'Home'
    p -> text @msg or _ 'Done'
