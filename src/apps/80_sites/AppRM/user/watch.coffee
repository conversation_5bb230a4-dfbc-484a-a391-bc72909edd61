

UserWatchModel =MODEL 'UserWatch'
{respError} = INCLUDE 'libapp.responseHelper'
UserModel = MODEL 'User'
CommunityModel = MODEL 'Community'
dataMethods = DEF 'dataMethods'
{processCmtys} = INCLUDE 'libapp.community'
i18n = INCLUDE 'lib.i18n'

debug = DEBUG()

APP '1.5'
APP 'user',true

renderPopupTxt = (watch,lang)->
  _t = i18n.getFun(lang or 'en')
  # propType
  # watchEvent
  # range
  savedSearchTxtArray = []
  for filter in ['propType','watchEvent']
    if v = watch[filter]
      trans = []
      for val in v
        trans.push _t val
      v = trans.join(' • ')
      savedSearchTxtArray.push v
  return savedSearchTxtArray.join(' • ')


ADDSTDFN 'watchLocation',{needReq:true,userObject:1},(opt,cb)->
  watchLocation opt,(err,msg)->
    cb(err,msg)

watchLocation = (opt,cb)->
  {uaddr,nm,loc,range,ptype2,propType,cmty,extra,watchEvent,unwatch,tp,req,user} = opt
  unless user
    debug.error 'watchLocation not login'
    return cb(MSG_STRINGS.NEED_LOGIN)
  params = {uaddr,nm,loc,range,ptype2,propType,cmty,extra,watchEvent,unwatch,tp,uid:user._id}
  UserWatchModel.watchLocation params,(err,ret)->
    if err
      debug.error 'watchLocation favorite',params,err
      return cb(MSG_STRINGS.DB_ERROR)
    msg = req.l10n(if unwatch then 'Unwatch' else 'Watched')
    cb(null,msg)




# # /1.5/user/watch
# POST 'watch',(req, resp)->
#   UserModel.appAuth {req,resp}, (user)->
#     return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
#     return respError {category:MSG_STRINGS.BAD_PARAMETER,errorCode:0 ,resp} unless param = req.body
#     param.uid = user._id
#     msg = if param.unwatch then 'Unwatch' else 'Watched'
#     UserWatchModel.watchLocation param, (err,ret)->
#       return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp,sysErr:err} if err
#       return resp.send {ok:1, msg:req.l10n(msg)}

POST 'watching',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    tp = req.body?.tp or false
    getWatchingListByType {uid:user?._id,tp,lang:req.locale()},(err,list)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp,sysErr:err} if err
      return resp.send {ok:1, items:list}

dataMethods.favCmtys = (req,user,cb)->
  UserModel.appAuth {req,resp:null},(user)->
    return cb null,[] unless user
    getWatchingListByType {uid:user._id,tp:'cmty',lang:req.locale()},(err,favCmtys)->
      return cb MSG_STRINGS.DB_ERROR,null if err
      return cb null,[] unless favCmtys
      processCmtys favCmtys,{lang:req.locale()}
      return cb null,favCmtys


getWatchingListByType = ({uid,tp,lang},cb)->
  UserWatchModel.getUserWatchings {uid,tp}, (err,ret)->
    return cb err if err
    return cb null,[] if ret.length is 0
    watchingMap = {}
    cmtyIds=[]
    returnList =[]
    for watch in ret
      watch.isWatching = true
      watch.display = renderPopupTxt watch,lang
      watch.watching = UserWatchModel.formatWatching watch
      if watch.extra
        watch.isBuilding = watch.extra.propType in ['Buliding','Condo']
      if tp in ['cmty','location']
        cmtyIds.push watch.uaddr
      watchingMap[watch.uaddr] = watch
    return cb null,ret if cmtyIds.length is 0
    CommunityModel.findList {cmtyIds},(err,cmtys)->
      return cb err if err
      for cmty in cmtys
        watchingMap[cmty._id] = Object.assign watchingMap[cmty._id],cmty
      for k,v of watchingMap
        returnList.push v
      return cb null,returnList