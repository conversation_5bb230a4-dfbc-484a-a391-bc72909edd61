config = CONFIG(['wechat','share','wechatWeb','wechatApp'])
gShareHostNameCn = config.share?.hostNameCn
UserModel = MODEL 'User'
# request = require('request')
dataMethods = DEF 'dataMethods'

# 检查用户是否有openId(绑定过微信的用户)
dataMethods.hasWxOpenId = (req, user)->
  return (user.wxMobOID) or (user.wxAppOID)


# NOTE: this id has to be under same wx-platform as native appId
# NOTE: change this to new wechat appid when migrate to new wx-platform
gWechatWebAuthId = if config.wechatWeb? then config.wechatWeb.AppID
gWechatWebSecret = if config.wechatWeb? then config.wechatWeb.AppSecret

# NOTE: this appId is used for WeChat.registerApp(appId, domain)
dataMethods.nativeWechatAppId = (req,user)->
  return config.wechatApp?.AppID or 'wxcf33ce325754da25'
# NOTE: this should be the same as registerd on wx open platform
dataMethods.nativeWechatDomain = (req,user)->
  return config.wechatApp?.domain or 'https://realmaster.com/'

APP 'wechatauth'
GET (req,resp)->
  resp.ckup "wechatauth"
# POST (req,resp)->
#   code = req.param 'code'

VIEW 'wechatauth',->
  div style:"padding:20px;",id:"wechatAuth",-> "WeChat Auth"
  div -> a href:'/1.5/index',-> "Back"
  coffeejs ->
    document.getElementById('wechatAuth').addEventListener 'click',->
      #alert JSON.stringify WeChat
      #alert JSON.stringify Wechat
      Wechat.auth "snsapi_userinfo",((resp)->
        #alert JSON.stringify resp
        # got code here, will call /schema?code=xxx automatically
        # android don't go to /schema automatically, need jump
        if RMSrv.isAndroid() and resp?.code?.length > 10
          window.location.href = "/scheme?code=" + resp.code
      ),(reason)->
        alert 'Failed:' + reason
    null

# USAGE: download page in wechat, user click download and request user info then redirect to ret page
# @input {uid} user who performed share's id
GET 'web',(req,resp)->
  unless gWechatWebAuthId and config.wechat?.domain
    console.log "No wechatWeb Configuration"
    return resp.redirect "/1.5/index"
  STATE = req.param('uid') or "TEST-STATE"
  REDIRECT_URI = encodeURIComponent("https://#{config.wechat.domain}/wechatauth/ret")
  url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=#{gWechatWebAuthId}&redirect_uri=#{REDIRECT_URI}&response_type=code&scope=snsapi_userinfo&state=#{STATE}#wechat_redirect"
  console.log url
  resp.redirect url

GET 'ret',(req,resp)->
  console.log 'Code:' + (code = req.param 'code')
  console.log 'State:' + (state = req.param 'state')
  unless gWechatWebAuthId and gWechatWebSecret
    console.log "No wechatWeb Configuration"
    return resp.redirect "/1.5/index"
  unless code
    return resp.redirect '/wechatauth/web'
  # do redirect first, then get code
  UserModel.getInfoFromWechat {code:code,appId:gWechatWebAuthId,secret:gWechatWebSecret},(err,wxuser)->
    console.log err if err
    if wxuser
      console.log wxuser
      if state?.length > 4
        wxuser.flwng = state
      UserModel.ensureInfoFromWechat {wxuser},(err)->
        console.log err if err
        url = "http://#{gShareHostNameCn}/app-download"
        if req.isIOSDevice()
          url = "https://itunes.apple.com/us/app/realmaster/id978214719?ls=1&mt=8"
        resp.redirect url
        # "https://a.app.qq.com/o/simple.jsp?pkgname=com.realmaster"
    else
      return resp.redirect '/wechatauth/web'

APP 'miniprogram'
# wechat miniprogram auth init, update user/create user by decrypted unionid
# var WXBizDataCrypt = require('./WXBizDataCrypt')
WXBizDataCrypt = INCLUDE 'libapp.WXBizDataCrypt'
miniappId = 'wxcd2eff4d154ac65e'
miniappSecret = '7449b4c20e45e0c0c41ef9ef658a1463'
helpers = INCLUDE 'lib.helpers'

POST 'onLogin',(req,resp)->
  b = req.body
  code = b.code
  # unless appId
  #   return resp.send {ok:0, err:'No appId'}
  # unless secret
  #   return resp.send {ok:0, err:'No app secret'}
  unless code
    return resp.send {ok:0, err:'No code'}
  # wx7901d89487948f85
  # d332be1328ef76edec9c568bb665b680
  url = "https://api.weixin.qq.com/sns/jscode2session?appid=#{miniappId}&secret=#{miniappSecret}&js_code=#{code}&grant_type=authorization_code"
  # request url, (err, response, body) ->
  helpers.http_get url,{protocol:'https', timeout:10000},(err,data) ->
    if err
      return resp.send {ok:0, err:err.toString()}
    try
      body = JSON.parse(data)
    catch e
      return resp.send {ok:0, err:e}
    # console.log 'statusCode:', response and response.statusCode
    console.log 'body:', body
    # body: { session_key: 'p9AoukBjlBpA2vwxLy8Uwg==',
    # expires_in: 7200,
    # openid: 'oGWrt0D1T_twARd0E_ZoHbyfbhL4' }
    if (not body.openid) and body.errcode?
      # (not body.openid) and body?.errcode not in [0,'0']
      return resp.send {ok:0,err:body.errmsg,errcode:body.errcode, isCip:req.isChinaIP()}
    openId = body.openid
    session_key = body.session_key
    # ?不一定有
    unionid = body.unionid
    req.session.set 'wx_session_key',session_key,->
      return resp.send {ok:1, isCip:req.isChinaIP()}
    # sessionKey:session_key


# https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.login.html
# https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/signature.html
# http://www.wxapp-union.com/forum.php?mod=viewthread&tid=1394
POST 'wechatauth',(req,resp)->
  # var iv = 'r7BXXKkLb8qrSNn05n0qiA=='
  b = req.body
  iv = b.iv
  encryptedData = b.encryptedData
  # var sessionKey = 'tiihtNczf5v6AKRyjwEUhQ=='
  # sessionKey = b.sessionKey
  sessionKey = req.session.get('wx_session_key')
  encryptedData = b.encryptedData
  unless miniappId
    return resp.send {ok:0, err:'incomplete server data'}
  unless iv and encryptedData and sessionKey
    msg = 'incomplete post data:'
    unless iv
      msg += ' iv'
    unless encryptedData
      msg += ' encryptedData'
    unless sessionKey
      msg += ' sessionKey'
    return resp.send {ok:0, err:msg}
  try
    pc = new WXBizDataCrypt(miniappId, sessionKey)
    data = pc.decryptData(encryptedData , iv)
  catch err
    return resp.send {ok:0, err:err.toString()}
  # console.log('解密后 data: ', data)
  unless data.unionId
    return resp.send {ok:0,err:'no unionId in decrypted data'}
  # avt = data.avatarUrl
  # unionId = data.unionId
  data.unionid = data.unionId
  data.username = data.userName
  data.headimgurl = data.avatarUrl
  data.isFromMiniApp = true
  UserModel.getWechatAuth {req,wxuser:data},(err,ret)->
    if err
      console.error 'error: '+err
      return resp.send {ok:0, err:err.toString()}
    if ret?.needUser and ret?.wxuser
      # return resp.redirect '/1.5/user/bind/' + ret.wxuser.unionid
      req.session.set 'user',ret.wxuser
    return resp.send {ok:1, isCip:req.isChinaIP()}

###
# // 解密后的数据为
# //
# // data = {
# //   "nickName": "Band",
# //   "gender": 1,
# //   "language": "zh_CN",
# //   "city": "Guangzhou",
# //   "province": "Guangdong",
# //   "country": "CN",
# //   "avatarUrl": "http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0",
# //   "unionId": "ocMvos6NjeKLIBqg5Mr9QjxrP1FA",
# //   "watermark": {
# //     "timestamp": 1477314187,
# //     "appid": "wx4f4bc4dec97d474b"
# //   }
# // }
# var encryptedData =
# 	'CiyLU1Aw2KjvrjMdj8YKliAjtP4gsMZM'+
# 	'QmRzooG2xrDcvSnxIMXFufNstNGTyaGS'+
# 	'9uT5geRa0W4oTOb1WT7fJlAC+oNPdbB+'+
# 	'3hVbJSRgv+4lGOETKUQz6OYStslQ142d'+
# 	'NCuabNPGBzlooOmB231qMM85d2/fV6Ch'+
# 	'evvXvQP8Hkue1poOFtnEtpyxVLW1zAo6'+
# 	'/1Xx1COxFvrc2d7UL/lmHInNlxuacJXw'+
# 	'u0fjpXfz/YqYzBIBzD6WUfTIF9GRHpOn'+
# 	'/Hz7saL8xz+W//FRAUid1OksQaQx4CMs'+
# 	'8LOddcQhULW4ucetDf96JcR3g0gfRK4P'+
# 	'C7E/r7Z6xNrXd2UIeorGj5Ef7b1pJAYB'+
# 	'6Y5anaHqZ9J6nKEBvB4DnNLIVWSgARns'+
# 	'/8wR2SiRS7MNACwTyrGvt9ts8p12PKFd'+
# 	'lqYTopNHR1Vf7XjfhQlVsAJdNiKdYmYV'+
# 	'oKlaRv85IfVunYzO0IKXsyl7JCUjCpoG'+
# 	'20f0a04COwfneQAGGwd5oa+T8yO5hzuy'+
# 	'Db/XcxxmK01EpqOyuxINew=='

    { openid: 'oyQyduJxHQfAPvzAwpFdzloQyM1k',
  nickname: 'NickName AAA',
  sex: 1,
  language: 'zh_CN',
  city: '',
  province: '',
  country: 'CA',
  headimgurl: 'http://wx.qlogo.cn/mmopen/sTJptKvBQLIrGdo2tvPEWERgPay8NUicRa8mAniby58Mp0OILtRheK8nGPbKjrZweqxjzaNOf9UVhkvz9Eib8icMXlkuVB0Cmv3j/0',
  privilege: [],
  unionid: 'oSWWZt_Yua6F68JgIS5J9j8hL5hA---' }
###
