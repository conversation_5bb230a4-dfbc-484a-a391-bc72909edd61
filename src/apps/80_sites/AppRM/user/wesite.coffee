# conf = CONFIG()
propSearch = INCLUDE 'libapp.propSearch'
{replaceRM2REImagePathForArray,replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'

Wecard = COLLECTION 'chome', 'wecard'
Forum = COLLECTION 'chome', 'forum'
# UserListings = COLLECTION 'chome', 'user_listing'
Properties = MODEL 'Properties'
UserModel = MODEL 'User'

# BAD_PARAMETER = 'Bad Parameter'
# ACCESS_DENIED = 'Access Denied'
{respError} = INCLUDE 'libapp.responseHelper'

# getProcessUserInfoOpt = DEF 'getProcessUserInfoOpt'
LIST_PROP_FIELDS = DEF 'LIST_PROP_FIELDS'
getWXConfig = DEF 'getWXConfig'
libProperties = INCLUDE 'libapp.properties'
propTranslate = INCLUDE 'libapp.propertiesTranslate'

convertRMListingsDispToTreb = propSearch.convertRMListingsDispToTreb



# '/1.5/wesite'
APP '1.5'
APP 'wesite',true
GET '',(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    cfg = {noAngular:true, noref: true, desc: 'RealMaster'}
    getWXConfig req,req.fullUrl(),(err,wxcfg)->
      cfg.wxcfg = wxcfg
      uid = user._id
      return resp.ckup 'wesite',{uid:uid},'_', cfg

GET ':id', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  uid = req.param 'id'
  isWeb = req.param 'isWeb'
  return resp.ckup 'wesite',{uid:uid,isWeb:isWeb},'_',{noAngular:true, noref: true}

getUserInfo = (req, resp, cb)->
  UserModel.findById req.param('id').toString(),{projection:UserModel.WESITE_FIELDS},(err,user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless user
    # opt = getProcessUserInfoOpt req,user
    user = UserModel.getProcessedInfo {lang:req.locale(), user}
    return cb(null, user)

POST 'userInfo', (req,resp)->
  getUserInfo req, resp, (err, ret) ->
    UserModel.appAuth {req,resp}, (user)->
      return resp.send {ok:1, user:ret} unless user
      UserModel.findProfileById user._id,{projection:{favProviders:1}}, (err, profile) ->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        return resp.send {ok:1,user:ret} unless profile?.favProviders
        favProviders = profile?.favProviders
        ret.isfaved = if favProviders?.indexOf(ret._id.toString()) > -1 then true else false
        return resp.send {ok:1,user:ret}

getWecards = (uid, page, limit, cb) ->
  q = {
    $and:[
      {meta:{$exists: true }},
      {meta:{$ne:null}}
    ],
    uid,
    del:{$ne:true}
  }
  skip = (page-1) * (limit-1)
  sort = {'meta.ts': -1}
  Wecard.findToArray q, {fields: {seq: 0}, sort, skip, limit}, (err, wecards)->
    if err then return cb(err)
    if wecards?.length > 0
      for wecard in wecards
        wecard.meta.img = replaceRM2REImagePath wecardImage if wecardImage = wecard.meta?.img
    return cb(null, wecards)


POST 'listCount', (req, resp)->
  result = {}
  getUserInfo req, resp, (err, user) ->
    Wecard.countDocuments {'uid':user._id, 'meta':{ $exists: true }, del:{$ne:true}}, (err, cnt)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      result.wecardcnt = cnt
      Forum.countDocuments {'uid':user._id, del:{$ne:true}}, (err, cnt)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        result.forumcnt = cnt
        Properties.countUserListings user,(err,ret)->
          if err
            return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          result.exlistingcnt = ret[0]
          result.fealistingcnt = ret[1] if ret[1]
          return resp.send {ok:1,result:result}

convertRmListingToTreb = (locale, lret,transDDF)->
  lret = convertRMListingsDispToTreb(lret)
  tmpList = []
  # translateRmPropDetailOpt = {locale}
  for i in lret
    tmp = propTranslate.translate_rmprop_detail({locale,transDDF}, i)
    tmpList.push tmp
  return tmpList

# /1.5/wesite/list
POST 'list', (req, resp) ->
  req.setLocale lang if lang = req.param 'lang'
  type = req.param 'type'
  page = req.param 'pgNum'
  locale = lang or req.locale()
  limit = 21
  uid = req.param('id').toString()
  l10n = (a,b)->req.l10n a,b
  _ab = (a,b)->req._ab(a,b)
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless uid
  getUserInfo req, resp, (err, user) ->
    if type is 'blog'
      getWecards uid, page, limit, (err,wecards)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        return resp.send {ok:1,l:wecards}
    if type is 'fea'
      Properties.findFeatureListings {user, page, limit}, (err,listings)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        listings ?= []
        return resp.send {ok:1,l:[]} if (listings?.length is 0)
        for prop in listings
          prop.pic.l = replaceRM2REImagePathForArray(prop.pic.l) if prop?.pic?.l
          libProperties.setUpPropRMPlusFields prop,l10n,_ab
        return resp.send {ok:1,l:listings}
    if type is 'ex'
      Properties.findUserActiveListings {locale:req.locale(), user, page, limit}, (err,listings)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        listings ?= []
        return resp.send {ok:1,l:[]} if (listings?.length is 0)
        listings = convertRmListingToTreb(locale, listings,req.getConfig('transDDF'))
        for prop in listings
          prop.pic.l = replaceRM2REImagePathForArray(prop.pic.l) if prop?.pic?.l
          libProperties.setUpPropRMPlusFields prop,l10n,_ab
        return resp.send {ok:1,l:listings}

POST 'recent', (req, resp)->
  limit = 5
  result = {}
  uid = req.param('id').toString()
  getWecards uid, 1, limit, (err,listing)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    result.wecards = listing
    Forum.findToArray {'uid':uid, del:{$ne:true}}, {limit:limit,sort:{ts:-1}},(err, forums)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      result.forums = forums
      resp.send {ok:1,result:result}

VIEW "wesite",->
  coffeejs {vars:{
    uid:@uid
    isWeb:@isWeb
    }}, ->
    null
  div class:"#{if @isWeb then 'isWeb' else ''}", id: "appWesite",style:'height: 100%;',->
    text """<app-wesite></app-wesite>"""
  js '/js/entry/commons.js'
  js '/js/entry/appWesite.js'
