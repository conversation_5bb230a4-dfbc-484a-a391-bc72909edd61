###
Chat:
  _id: auto or uid1_uid2 , uid1 < uid2
  mbrs: [] 0 is for always for system
    {
    uid: user id
    nm: nickname
    avt: avator logo
    act: true/false is active
    qt: quite true/false
    top: move to top
    pn: push notification or null for don't notify
    eml: for email removed or unread messages
    mbl: cell-phone number or null for don't notify
    sc: send message counter
    ts: last added timestamp
    ms: last access timestamp
    syc: last synced chat idx
    rls: [] owner/listner...
    # may not needed:
    i: (0 or idx) if active
    mrk: (marker index), last viewed message index (removed + in chat)
    }
  chat:[] # max keep 1k
    {
    i: mbrs index
    m: message/text/html snapshot/sound binary base64
    ts: timestamp (optional)
    tp: t(txt)/s(sound)/p(picture)/u(url snap)
    r[]: referred member idxes. Mostly null
    }
  mc: total member added counter
  cc: total chat counter
  nm: show nicknames
  m: description
  ts: created timestamp
  mt: last update
  # follow fields may not be needed
  lchat: last chat index, may > 1k
  mbrsrmd: member removed (Member reshuffal, when 5% is removed. need to change chat i, but keep members in chat history)
  rmd: total removed chats, when reshuffal

View:
  frame
    avt
    nickname
    box
      arrow
      txt

Server Structure:
  Persistent Layer:
    to manage load/save/update timing/way to disk
    manage triming
    manage notification
    to sync with other servers (future plan)
  Trim Service:
    trim object
    save trimmed info to seperate collection
    may notify/email to users with proper roles
  Notification Service:
    notify pn
    send sms only when 1. has top flag 2. no access in 1 min

TODO:
  multi servers
###
Chat = COLLECTION 'chome','chat'
# config = CONFIG()
prepSocket = DEF 'prepSocket'
puts = console.log
BIGROOM = 'all'

# persistent layer are implemented as 3 classes
class ChatRoom
  constructor: ()->


class ChatRoomMember
  constructor: (@_id,uid)->
    @midx = 0 #TODO
  # enter and leave
  join: (socket)-> # return mbr idx
  leave: ->
  # for communications
  rng: (from,to)->
  say: (chat)->
  deli: (cidx)->
  read: (cidx)->
  # setting
  set: (msg)-> # set member info
  config: (msg)-> # Admin: set chat room info
  # member manageement
  exit: ()->
  del: (midx)-> # Admin: remove a member from chat room

class ChatRoomList
  constructor: (cfg)->
  list: (uid,cb)-> # return [{basic chat room info}]
  join: (rid,cb)-> # return ChatRoom object
  ensure: (uid,roomTplId,cb)-> # most likely created by other ways
    # need user-id, room template id or the other user's uid
    # ensure the ChatRoom is created/
    # return ChatRoom object



SOCKET (socket)->
  getNm = (user)->
    user?.nm_zh or user?.nm_en or user?.nm or user?.ln or socket.id
  prepSocket socket,(user)->
    puts "User connected #{user?.eml}"
    socket.join BIGROOM,(e)->
      obj =
        avt: user?.avt or '/img/logo.png'
        nm: getNm user
        msg: 'Joined'
      socket.to(BIGROOM).emit 'chat',obj
    socket.on 'disconnect',->
      obj =
        avt: user?.avt or '/img/logo.png'
        nm: getNm user
        msg: 'Left'
      socket.to(BIGROOM).emit 'chat',obj
    socket.on 'chat',(msg)->
      obj =
        avt: user?.avt or '/img/logo.png'
        nm: getNm user
        msg: msg
      socket.to(BIGROOM).emit 'chat',obj
      #socket.broadcast.emit 'chat',msg
      obj.me = 1
      socket.emit 'chat',obj


APP 'socket-test'
GET (req,resp)->
  date = new Date().toDateString()
  resp.ckup 'socket-test',{date:date}

LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:'viewport',
      content:'width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0'
      #meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'
      meta name:'apple-mobile-web-app-capable',content:'yes'
      meta name:'apple-mobile-web-app-status-bar-style',content:'black-translucent'
      meta name:'format-detection',content:'telephone=no'
      g4tags js:new Date()
      title -> @vctx.title or _ 'RealMaster'
      css '/css/ratchet.min.css?v=1.8' unless @noRatchet
      css '/css/font-awesome.min.css'
      css '/css/app.min.css?v=1.8'
      css '/css/smb.min.css'
      js '/js/angular.min.js' unless @noAngular
      js '/js/ratchet.js?v=1.8'
      js '/js/app.js?v=1.8'
      js '/js/rmapp.min.js?v=1.8'
      text ckup 'cordovaJs',{wxcfg:@wxcfg} unless @noCordova
    body ->
      g4body()
      text @content()
      #img src:@req.getLogParams(),id:'imgNotUsed',onload:'var _e$=document.getElementById('imgNotUsed');_e$.parentNode.removeChild(_e$);'


VIEW 'socket-test',->
  js '/socket/socket.io.js'
  css '/css/socket.css'
  div id:'chatList',class:'ext-table-view',->
    header class:'bar bar-nav',->
      a class:'icon icon-close  pull-right detailModal',
      dataIgnore:'push',
      ngClick:'closeFavExp()'
      h1 class:'title',-> _('Off Market Favourites')
    div class:'fullHeight content',->
      ul class: 'table-view', ->
        li class: 'list-item table-view-cell media detailModal', ngRepeat:'(id,r) in favExp', ->
          text '[{{r.id}}] {{r.addr}} {{r.lp | currency}}'
      p style:'padding: 0 20px;',->
        _ 'These properties were removed from your Favourites list.'
  div id:'chatRoom', class:'modal',->
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', href: '',onclick:'goBack(event);', dataIgnore: 'push'
      h1 class: 'title',->
        text 'Chat'
    div class:'content',->
      #pre style:'height:100%;width:100%; margin-top:0;',->
      ol id:'board',->
        li class:'util',->
          div class:'time',->
            p ->
              text @date or '30mins ago'
    div class:'bar bar-standard bar-footer',->
      input style:'width:100%;height:100%;',id:'input',onchange:'typing()'
      input type:'submit',style:'visibility:hidden;position:absolute'
  js '/js/socket_test.min.js'