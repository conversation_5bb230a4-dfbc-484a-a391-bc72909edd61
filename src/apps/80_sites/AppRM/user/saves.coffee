# saves 整合收藏房源，楼花，检索条件，社区，经济，商家, 笔记

# {genListingPicReplaceUrls,processProps} = INCLUDE 'libapp.properties'
# UserProfile = COLLECTION 'chome','user_profile'
# PropertyModel = MODEL 'Properties'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
debug = DEBUG()
libUser = INCLUDE 'libapp.user'
libAppVer = INCLUDE 'libapp.appVer'
{deepCopyObject} = INCLUDE 'lib.helpers_object'

CommunityModel = MODEL 'Community'
tabs = [
  { url: 'properties', nm: 'Homes', selected: 'properties' },
  { url: 'communities', nm: 'Communities', selected: 'communities' },
  { url: 'locations', nm: 'Locations', selected: 'locations' },
  { url: 'searches', nm: 'Searches', selected: 'searches' },
  { url: 'notes', nm: 'Notes', selected: 'notes' },
  # { url: 'building', nm: 'Buildings', selected: 'building' },
  { url: 'viewed', nm: 'Recently Viewed', selected: 'viewed' },
  { url: 'projects', nm: 'PreCons', selected: 'projects' },
  { url: 'agents', nm: 'Agents & Businesses', selected: 'agents' }
  { url: 'linkedagents', nm: 'Linked Agent', selected: 'linkedagents' }
]

js =[
  '/vue3.min.js',
  '/saves/savesIndexPage.js'
]
css = [
  '/apps/saves.css',
  '/apps/drawers.css'
  '/sprite.min.css'
]
APP '1.5'
APP 'saves', true

getOption = (req)->
  opt =
    tabs:tabs
    d:req.param('d') or '/1.5/index'
    isPopup:req.param 'isPopup' or ''
    js:js
    css:css
  return opt

#1.5/saves/properties
#1.5/saves/projects
#1.5/saves/searches
#1.5/saves/notes
#1.5/saves/communities
#1.5/saves/agents
#1.5/saves/viewed
GET ':feature', (req, resp,next)->
  feature = req.param('feature')
  if feature not in ['properties','projects','searches','communities',\
      'agents','viewed','locations','linkedagents','notes','deals']
    return resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp,url:"/1.5/user/login?d=/1.5/saves/#{feature}"}, (user)->
    opt = getOption req
    isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    if (((req.isAllowed 'noteAdmin') or isGroup) and (opt.tabs[5].url isnt 'deals'))
      opt.tabs = deepCopyObject tabs,1
      opt.tabs.splice(5, 0, { url: 'deals', nm: 'Deals', selected: 'deals' })
    opt.id = feature
    addJs = ["/saves/#{feature}Page.js"]
    opt.showGroupSelected = 1
    opt.js = opt.js.concat(addJs)
    opt.page = 'saves'
    opt.isChinaIP=req.isChinaIP()
    opt.appmode=req.cookies[libUser.APPMODE]
    opt.lang=req.locale()
    opt.os= if req.isIOSDevice() then 'ios' else 'android'
    opt.isNoteAdmin = req.isAllowed 'noteAdmin'
    libAppVer.getAppUpgradeSetting req, {os:opt.os}, (setting) ->
      opt.appUpgrade = setting
      resp.ckup 'saves/index',opt,'_',{noAngular:true,noUserModal:true,noRatchetJs:true}
