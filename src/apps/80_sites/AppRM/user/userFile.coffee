###
UserFile
  _id: user id
  base: i.realmaster.com
  ufld: user distinct folder name
  fldr: B  (all user uses this fldr)
  files: {[index]:{i:index(A...),e:ext,s:size,w,h,m:memo,ts:timestamp,}}
  fnum?: total current saved files
  cntr: file uploaded counter
SysData
  _id: 'UserFile'
  cntr: total user folder issued counter
  ts: last updated timestamp
  base: base url
###
config = CONFIG(['s3config','user_file'])
UserFile = COLLECTION 'chome', 'user_file'
SysData = COLLECTION 'chome', 'sysdata'

checksum = INCLUDE 'lib.checksum'
mime = INCLUDE 'mime'
helpers = INCLUDE 'lib.helpers'
crypto = INCLUDE 'crypto'
fileHelper = INCLUDE 'libapp.fileHelper'
getConfig = DEF 'getConfig'

UserModel = MODEL 'User'
debug = DEBUG()
fs = require 'fs'

# fileDriver = null
s3fileDriver = null
s3config = config.s3config
s3 = null
DISABLE_UPLOAD = config.user_file?.disableUpload
STRING_DISABLE = "Image upload service temporarily disabled, please try again later."
FILE_WEB_PROTOCOL = config.user_file?.protocol or 'https'
FILE_WEB_BASE_DOMAIN = config.user_file?.wwwbase or 'f.realmaster.com'
# fileFolderBase = appConfig.user_file?.folder or 'T'

if s3config
  s3fileDriver = INCLUDE('lib.fs_awss3').getDriver s3config
  s3 = s3fileDriver.s3
# else
#   throw new Error 'S3 config not found!'


# publicImgFilePath = DEF 'publicImgFilePath'
# fileDriver = INCLUDE('lib.fs_local').getDriver {rootFolder:(appConfig.publicImgFilePath or publicImgFilePath)}

# NOTE: FILE_LOCAL_FOLDER_BASE is not used here, recorded under userFile, incase changes
# FILE_LOCAL_FOLDER_BASE = DEF 'FILE_LOCAL_FOLDER_BASE'

# ensureUserFolder = DEF 'ensureUserFolder'
# reserveUserFile = DEF 'reserveUserFile'


# upsert userFile, with new files [{i,e,s,w,h,m,ts}]
upsertUserFile = (userFile,files,cb)->
  set =
    # base:(FILE_WEB_BASE_DOMAIN + FILE_LOCAL_FOLDER_BASE)
    # ufld:userFile.ufld
    ts:new Date()
  if Array.isArray files
    for f in files
      set['files.' + f.i] = f
  else
    set['files.' + files.i] = files
  UserFile.updateOne {_id:userFile._id},{$set:set},{},(err,r)->
    return cb err,r.value

APP 'file'

POST 'upOne',(req,resp)->
  findError = (e)->
    console.error e
    resp.send {e:e.toString()}
  if DISABLE_UPLOAD
    return findError(req.l10n STRING_DISABLE)
  UserModel.appAuth {req,resp},(user)->
    if not user then return findError "Unauthorized"
    # get/replace
    try
      console.log b = JSON.parse req.body.p
    catch e
      return findError e
    # if not (upfiles = req.files)
    #   return findError "No File Found"
    # console.log upfiles
    switch b.tp
      when 'avator'
        file = {i:'A',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
      when 'qrcd'
        file = {i:'B',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
      when 'grpqrcd'
        file = {i:'C',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
      else
        file = {i:'D',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
    # avator use 'A' as default, no need to reserve files
    UserModel.reserveUserFile {user,fileNum:0},(err,userFile,fileNames)->
      if err then return findError err
      # make sure folder
      folderPath = userFile.fldr+'/'+userFile.ufld
      # bucket = s3config.bucket or s3config.params.Bucket
      fileExt = req.param "ext",'jpg'
      key      = folderPath + '/' + file.i + "." + file.e
      contentType = mime.lookup fileExt,'image/jpeg'
      now   = new Date()
      exp   = new Date(now.getTime() + 2 * 60 * 1000) # 2 min
      date  = now.toISOString().replace(/\-|\:|\.\d{3}/g,'')
      date8 = date.substr(0,8)
      policy      = getPolicy(exp.toISOString(), key,      contentType, fileHelper.getUploadUrl(), date)
      signature = checksum.shareEncode(JSON.stringify(policy),fileHelper.getSharedSecret())
      ret =
        file:       {i:file.i, e:file.e}
        key:        key
        signature:  signature
        date:       date
        credential: fileHelper.getUploadUrl()
        contentType:contentType
      resp.send ret


POST 'upOneSuccess',(req, resp)->
  findError = (e)->
    console.error e
    resp.send {ok:0, e:e.toString()}
  UserModel.appAuth {req,resp},(user)->
    return findError "Unauthorized" if not user
    paramFile = req.param 'file',''
    # paramFile = paramFile.split(',')
    b = req.body
    # console.log '####'
    # console.log paramFile
    return findError 'No payload' unless paramFile?.i
    UserModel.reserveUserFile {user,fileNum:0},(err,userFile,fileNames)->
      if err then return findError err
      file = {i:paramFile.i,e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
      upsertUserFile userFile,file,(err)->
        if err then return findError err
        return findError('Empty base') unless userFile.base?.length
        baseAndFldr = fileHelper.getUserBaseUrlWithPath(userFile)# ImgServerBaseUrl+'/'+userFile.fldr+'/'+userFile.ufld
        avtUrl = baseAndFldr + '/' + file.i + '.' + file.e + '?v=' + Date.now().toString().slice(-6)
        UserModel.updateImage {req,tp:b.tp,avtUrl},(err,newUser)->
          if err then return findError err
          resp.send {ok:1,url:avtUrl}

APP '1.5'

removeFilesFromImgServer = (req,opt,cb)->
  sig = checksum.shareEncode(JSON.stringify(opt),fileHelper.getSharedSecret())
  postOpt = fileHelper.getFileServerOption true # remove
  helpers.http_post postOpt,{signature:sig},(err,ret)->
    return cb err if err
    try
      if 'string' is typeof(ret)
        ret = JSON.parse ret
    catch error
      return cb error
    if ret.ok
      cb null, ret.r
    else
      cb ret.err

# delete file from both user file and s3
POST 'deleteFiles',(req,resp)->
  if DISABLE_UPLOAD
    return resp.send {ok:0, err:(req.l10n STRING_DISABLE)}
  cleanUFiles = (uFiles)->
    ret = []
    for f in uFiles
      if 'string' is typeof f
        ret.push f
      else if 'object' is typeof f
        ret.push(f.nm or f.tA)
      else
        ret.push f.toString()
    return ret
  removeThumb = (fileName)-> #fileName has _, remove
    return fileName.replace('_', '')
  getName = (fileName)-> #name with ext 'abc_.jpg' -> 'abc'
    if 'string' isnt typeof(fileName)
      if fileName.nm
        fileName = fileName.nm or fileName.tA
      else
        console.error 'Error: fileName: '+fileName+'; by User:'+req.user?.eml
        fileName = "NotString" + fileName
    return removeThumb( fileName.split('.')[0] )
  getThumb = (fileName) ->
    return fileName.split('.')[0] + '_.' + fileName.split('.')[1]
  #require s3 do delete
  # if no error delete user_file
  UserModel.appAuth {req,resp},(user)->
    unless user then return resp.send {ok:0, err:'Unauthorized'}
    isForceDelete = req.param('isForceDelete') or false
    uFiles = req.param('files') or [] #array of file thumbNames have to replace
    uFldnm = req.param('fldr') or ''
    existedFIles = [] # list of all files including thumb
    files = [] #list of files to be delete
    fldnm = ''
    names = [] #list of exist file names

    if uFiles.length is 0
      return resp.send {ok:0, err:'nothing to do'}
    uFiles = cleanUFiles(uFiles)

    UserFile.findOne {_id:user._id}, (err,doc)->
      if err then return resp.send {ok:0, err:err.toString()}
      if uFldnm is doc.fldr + '/' + doc.ufld
        fldnm = uFldnm
      else
        return resp.send {ok:0, err:'You can only delete your files only'}
      update= {"$unset":{}}
      #pop existed files and name
      for k, v of doc.files
        names.push k
        existedFIles.push v.i + '.' + v.e
        if v.t?
          existedFIles.push v.i + '_.' + v.e
      # check user input valid
      foundInRecords = {}
      isAdmin = UserModel.accessAllowed 'forumAdmin', user
      processFile = (f)->
        #unset field in user_file
        name = getName(f)
        if names.indexOf(name) > -1
          update["$unset"]["files." + name] = 1
        #remove on s3
        if existedFIles.indexOf(f) > -1
          files.push(f)
          if doc.files[name]?.t in ['1',1,true]
            files.push(getThumb(f)); # add thumb to list of files to remove
      if uFiles.length > 9
        return resp.send {ok:0, err:req.l10n('You can select up to 9 images at a time!')}
      for f in uFiles
        if isAdmin and isForceDelete
          processFile f
        else
          try
            isFileUsedInfo = await UserModel.checkFileReference {uFldnm,fileName:f,uid:user._id}
            if isFileUsedInfo.length
              foundInRecords[f] = isFileUsedInfo
            else
              processFile f
          catch err
            debug.error err
      # resp.send {f:files, u:update, fldr:fldnm, ok:1}
      # return
      if Object.keys(foundInRecords)?.length
        return resp.send {ok:1,foundInRecords}
      removeFilesFromImgServer req,{files,fldnm},(err,result)->
        return resp.send {ok:0, err:err} if err
        UserFile.updateOne {_id:user._id}, update ,(err,doc)->
          if err then return resp.send {ok:0, err:err.toString()}
          # return resp.send {ok:doc.result.nModified or 0, nm: name}
          # errStr = ''
          if doc.result.nModified > 0
            resp.send {ok:1, r:result, n:result.length}
          else
            resp.send {ok:1, err:'', n:0}

#after user uploaded to img server, tell server update user_file
POST 'uploadSuccess',(req,resp)->
  findError = (e)->
    console.error e
    resp.send {ok:0, e:e.toString()}
  UserModel.appAuth {req,resp},(user)->
    return findError "Unauthorized" if not user
    fileNames = req.param 'fileNames',''
    fileNames = fileNames.split(',')
    # console.log fileNames
    return findError 'No payload' unless fileNames?.length
    UserModel.reserveUserFile {user,fileNum:0},(err,userFile,fileNames2)->
      if err then return findError err
      file = {}
      file.w  = req.param "w", 128
      file.h  = req.param "h", 128
      file.s  = req.param "s", 0
      file.ts = new Date()
      file.i  = fileNames[0] #fileNames[0]
      file.t  = req.param 't', 0
      file.e  = req.param 'ext', 'jpg'
      upsertUserFile userFile,[file],(err)->
        if err then return findError err
        resp.send {ok:1}


#TODO: if upload s3 failed
POST 'uploadFail',(req,resp)->
  #if user last uploaded file ts within 1s of Date(), use object.keys get files length
  #remove $unset that last obj, dont reduce cntr
  UserModel.appAuth {req,resp},(user)->
    unless user then return resp.send {ok:0, err: 'Unauthorized'}
    UserFile.findOne {_id:user._id}, (err,doc)->
      if not doc
        return resp.send {ok:0, err:MSG_STRINGS.NOT_FOUND}
      now = new Date()
      cntr = doc.cntr or 18
      name = helpers.number2chars(cntr-1)
      if err then return resp.send {ok:0, err:err.toString()}
      if doc.files? and doc.files[name] and (now - doc.files[name].ts < 10000)#within 10s
        update = {"$unset":{}}
        update["$unset"]["files." + name] = 1
        UserFile.updateOne {_id:user._id}, update ,(err,doc)->
          if err then return resp.send {ok:0, err:err.toString()}
          return resp.send {ok:doc.result.nModified or 0, nm: name}
      else
        resp.send {ok:0, name:name, cntr:cntr, now:now }

#get user files in array, array1 is thumbs, array2 is pic address
GET 'userFiles.json',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    UserFile.findOne {_id:user._id}, (err,doc)->
      if err then return resp.send {ok:0, err:err.toString()}
      picList = {}
      if not doc then return resp.send {ok:1, err:'no files', pl:[]}
      if doc.ufld
        base = FILE_WEB_PROTOCOL+'://'+FILE_WEB_BASE_DOMAIN+'/'+doc.fldr+'/'+doc.ufld
        fldr = doc.fldr+'/'+doc.ufld
      else
        base = doc.base + doc.fldr
        fldr = doc.fldr #+doc.ufld  #fileFolderBase
        # no ufld user shouldnt have files,
        if doc.files? and Object.keys(doc.files).length
          debug.error 'User has no ufld but has files',doc._id
      count = 0
      if doc.files?
        filekeys = Object.keys(doc.files).reverse()
        for k in filekeys
          v = doc.files[k]
          name = v.i + '.' + v.e
          thumbName = v.i + '_.' + v.e
          count++
          if v.t? and v.t is '1'
            picList[name] =  {nm:name, tA:thumbName, i:count}
          else
            picList[name] =  {nm:name, i:count}
      # save bandwidth, pass in fldr for deletion
      # if (baseName = appConfig.user_file?.useBase)? and (baseName isnt 'base')
      #   if doc[baseName]
      #     doc.base = doc[baseName]
        # baseUrl = appConfig.user_file[baseName]
        # if baseUrl
        #   doc.base = baseUrl+'/'+FILE_LOCAL_FOLDER_BASE
      # if doc.base[doc.base.length-1] isnt '/'
      #   doc.base=doc.base+'/'
      if /^http/.test doc.base
        console.error 'Error: UserFile base incorrect! includes protocol'
        # newBase = doc.base+doc.ufld
      newBase = fileHelper.getUserBaseUrlWithPath(doc)
      resp.send {ok:1, pl:picList , base:newBase, fldr:fldr, count}


#return policy object
getPolicy = (exp, key, contentType, credential, date)->
  return {
      exp
      key,
      contentType,
      credential,
      date,
    }

POST 'rmSign',(req,resp)->
  findError = (e)->
    console.error e
    resp.send {e:e.toString()}
  if DISABLE_UPLOAD
    return findError(req.l10n STRING_DISABLE)
  UserModel.appAuth {req,resp},(user)->
    unless user then return findError "Unauthorized"
    UserModel.reserveUserFile {user,fileNum:1},(err,userFile,fileNames)->
      if err then return findError err
      # make sure folder
      folderPath = userFile.fldr+'/'+userFile.ufld
      fileExt = req.param "ext",'jpg'
      thumbKey = folderPath + '/' + fileNames[0]  + '_' + "." + fileExt
      key      = folderPath + '/' + fileNames[0] + "." + fileExt
      contentType = mime.lookup fileExt,'image/jpeg'
      now   = new Date()
      exp   = new Date(now.getTime() + 2 * 60 * 1000) # 2 min
      date  = now.toISOString().replace(/\-|\:|\.\d{3}/g,'')
      date8 = date.substr(0,8)
      policy      = getPolicy(exp.toISOString(), key,      contentType, fileHelper.getUploadUrl(), date)
      # thumbPolicy = getPolicy(exp.toISOString(), thumbKey, contentType, fileHelper.getUploadUrl(), date)
      # stringToSignBuf = Buffer.from(JSON.stringify(policy))
      # stringToSign = stringToSignBuf.toString('base64')
      # signature = HMAC_SHA256 getS3SigningKey(date8),stringToSign,'hex'
      signature = checksum.shareEncode(JSON.stringify(policy),fileHelper.getSharedSecret())
      # thumbPolicySign = new Buffer(JSON.stringify(thumbPolicy)).toString('base64')
      # thumbSignature = HMAC_SHA256 getS3SigningKey(date8),thumbPolicySign,'hex'
      # thumbSignature = checksum.shareEncode(JSON.stringify(thumbPolicy),fileHelper.getSharedSecret())
      # thumbPolicySign = checksum.shareEncode(message,secret)
      ret =
        # thumbPolicy:   thumbPolicySign
        # thumbKey:      thumbKey
        # thumbSignature:thumbSignature
        # policy:  stringToSign
        fileNames:  fileNames
        key:        key
        signature:  signature
        date:       date
        credential: fileHelper.getUploadUrl()
        contentType:contentType
      resp.send ret

APP '1.5/user'
# @url: GET /1.5/user/verifyemail
#/1.5/prop/detail/inapp?id=TRBN11111

# GET 'verifyemail',(req,resp)->
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
#     return resp.ckup 'notification/verifyemail',{user:user}



# @url: GET /1.5/img/insert
APP '1.5/img'
GET 'insert', (req, resp) ->
  UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->
    maxImageSize = getConfig('maxImageSize')
    imgSize = 'l'
    # splash admin can go beyond maxImageWidth/Height(1600) to 2400
    if req.isAllowed('splashAdmin', user)
      imgSize = 'xl'
    isAdmin = UserModel.accessAllowed 'forumAdmin', user
    return resp.ckup 'imageInsert', { maxImageSize,isAdmin,imgSize }, "_", { noAngular: 1 }

VIEW 'imageInsert', ->
  # js '/js/ratchet/segmented-controllers.js'
  div id: 'vueBody', ->
    text """<image-insert></image-insert>"""
  coffeejs {vars:{
    maxImageSize: @maxImageSize
    isAdmin: @isAdmin,
    imgSize: @imgSize
  }}, ->
    null
  # js '/js/lz-string.min.js'
  # js '/js/calculate.min.js'
  js '/js/entry/commons.js'
  js '/js/sortable.min.js'
  js '/js/entry/imageInsert.js'
