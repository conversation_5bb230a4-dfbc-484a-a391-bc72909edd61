config = CONFIG(['serverBase','mailEngine','contact','recapcha','abTest','contact','secretkey'])

helpers = INCLUDE 'lib.helpers'
libPropertyImage = INCLUDE 'libapp.propertyImage'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
sendMail = SERVICE 'sendMail'
# https = require('https')
json = INCLUDE 'lib.json'
Geetest = require('geetest/gt-sdk')
Login = COLLECTION 'chome','login'
UserStats = COLLECTION 'chome', 'user_stats'
UserCityStats = COLLECTION 'chome', 'user_city_stats'
libOauth = INCLUDE 'libapp.oauth'
LimiterModel = MODEL 'Limiter'
rateLimiter = INCLUDE 'lib.rateLimiterV2'
sendEmailLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:3,source:'sendMailFromUserPost'}
sendEmailLimiterFilter = rateLimiter.createFilter sendEmailLimiterInstance
{APPMODE} = INCLUDE 'libapp.user'
{isProxyEmail} = INCLUDE 'lib.proxyEmail'
{getValidatedEmail} = INCLUDE 'lib.sendMailValidate'
crypto = INCLUDE 'crypto'
decryptEmlLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:30,source:'decryptEml'}
decryptEmlLimiterFilter = rateLimiter.createFilter decryptEmlLimiterInstance

# accessAllowed = DEF '_access_allowed'

# wechatBindDBUser = DEF 'wechatBindDBUser'
robotPreventionSetup = DEF 'robotPreventionSetup'
TYPE_STRINGS = DEF 'TYPE_STRINGS'
SysNotifyModel = MODEL 'SysNotify'

# updateUserWithSession = DEF 'updateUser'


loadDotTemplates = DEF 'loadDotTemplates'

UserModel = MODEL 'User'
UserRegisterModel = MODEL 'UserRegister'
UserEmailModel = MODEL 'UserEmail'
VcodeModel = MODEL 'Vcode'
ABTestModel = MODEL 'ABTest'

getDotHtml = DEF 'getDotHtml'

{respError} = INCLUDE 'libapp.responseHelper'
COMPLAINT_OR_HARD_BOUNCE_EMAIL = 'This email address is not available for sign up, try something else.'
SENT_MULTIPLE_TIMES = 'Too many attempts, please try again after 15 minutes.'

# send verification email
sendEmailVerification = ({req,user,neml},cb)->
  req.setupL10n() unless req._
  if neml
    eml = neml
  else
    eml = if Array.isArray user.eml then user.eml[0] else user.eml
  
  # email mx验证失败不需要通知admin,提前验证
  try
    validRet = await getValidatedEmail eml
  catch err
    debug.error err,"email:#{eml}"
    return cb err
  if not validRet?.length
    return cb MSG_STRINGS.INVALID_EMAIL_ADDRESS

  # 新注册用户24小时内没有验证邮箱，删除验证vcode
  try
    {vcode,vcodeObj} = await VcodeModel.create {tp:TYPE_STRINGS.VERIFY_EMAIL,id:user._id,expHours:24,eml}
  catch err
    debug.error err, ' user:',user,' neml:',neml
    return cb err
  if vcodeObj.lockedTs
    # 5mins内发送次数超过3次，需要等待15mins
    return cb(SENT_MULTIPLE_TIMES)
  # send to email
  domain = config.serverBase?.wwwDomain
  if not req.isChinaIP()
    domain = domain.replace 'cn','com'
  url = "http://#{domain}/#{req.locale()}/verifyEmail/#{vcode}"
  # subject = req.l10n "RealMaster Email Verification"
  #TODO:why randomcode here?
  vcode = helpers.randomCode()
  data =
    link: url
    email: eml
  data.vcode = vcode if user.emlVcode
  data = Object.assign(data, req.setting.emailSetting, req.setting.signUpEmail)

  domainName = req.setting.emailSetting?.domainName
  unless domainName
    debug.error 'Error: can not find req.setting.emailSetting?.domainName',req.hostname,req.remoteIP(),req.getDevType(),req.setting
    domainName = MSG_STRINGS.DEFAULT_EMAIL_DOMAIN_NAME
  subject = req.setting.signUpEmail?.subject
  unless subject
    debug.error 'Error: can not find req.setting.signUpEmail?.subject',req.hostname,req.remoteIP(),req.getDevType(),req.setting
    subject = MSG_STRINGS.DEFAULT_EMAIL_REGISTER_SUBJECT

  data.confirmMsg = req.l10n("Thanks for signing up to #{domainName}!")\
    +' '+req.l10n('Please confirm your email address by clicking the button below.')\
    +' '+req.l10n("If you didn't register with us, please ignore this email. \
    Your email address will be automatically deleted from our system in 24 hours.")
  # Avion/4salebc has not appName for now, only website
  spliter = ''
  if /realmaster/ig.test domainName
    data.appName ?= 'RealMaster app'
    spliter = ' &'
  data.webName ?= 'www.realmaster.com'
  data.accountMsg = req.l10n("You can use your account to sign in #{data.appName}")
  data.accountMsg += "#{spliter} #{data.webName}"
  data.contactEml ?= MSG_STRINGS.DEFAULT_CONTACT_EML
  data.contactTel ?= MSG_STRINGS.DEFAULT_CONTACT_TEL
  data.logo ?= MSG_STRINGS.DEFAULT_EMAIL_LOGO
  data.isChange = neml?

  ret = getDotHtml req, 'userRegister', data
  return cb ret.err if ret.err
  html = ret.html
  engine = config.mailEngine?.mailEngine or 'sendmail'

  mail =
    _trans: 'smtp'
    engine: engine
    priority: true
    from: "#{req.l10n(domainName)}<#{sendMail.getFromEmailByEngine(engine)}>"
    #_temp: 'rmapp-newpassword'
    replyTo: "<#{config.contact?.defaultRTEmail}>"
    to: eml
    #bcc: '<EMAIL>'
    subject: req.l10n(subject)
    text: req.l10n("Please verify your email by clicking the following link:") + "\n" + url
    # html: "<p>" + req.l10n("Please verify your email by clicking the following link:") + "<br> <a href='#{url}'>#{url}</a><p>"
    html:html
    sbj: subject
    eventType: 'Registration'
    isNeedValidate: true
    mailEngineListIndex:0
  #INCALL 'simpleMail',mail,(err,response)->
  sendMail.sendMail mail,(err,response)->
    # return resp.ckup 'forgotPwd',{err:"When Send Email:" + err} if err
    if err
      # debug.error 'Error: sendEmailVerification sendMail:',req.hostname,req.remoteIP(),req.getDevType(),req.setting
      req.logger?.error err
      SysNotifyModel.notifyAdmin(err.toString(), true)
      return cb err
    debug.debug "Send V Mail to #{user.eml}"
    # let user known send the email address
    if user.emlVcode
      UserModel.saveEmlVCode {vcode,user},(err)->
        return cb err if err
        cb null,'Verification code sent'
    else
      cb null,'Verification code sent'

APP 'verifyEmail'

BAD_VCODE_MSG = 'This link has expired.'

# 'verifyEmail/:vcode'
GET ':vcode',(req,resp)->
  lang = req.locale()
  badCode = ->
    if req.getDevType() is 'app'
      return resp.ckup 'generalError',{lang,err: BAD_VCODE_MSG}
    else
      return resp.ckup 'webGeneralError', {err: BAD_VCODE_MSG}, 'layout', {}
  goodCode = ->
    return resp.ckup 'generalMsg',{
      lang,
      msg:req.l10n('Successfully verified your email address.')
    },'layout',{}
  unless vcode = req.param 'vcode'
    return badCode()
  if vcode is 'test-vcode'
    return goodCode()
  #vcode是一次性的，存在表中，点击就删除。
  try
    vcodeObj = await VcodeModel.check vcode,true
  catch err
    debug.error err,' vcode:',vcode
  unless vcodeObj?.eml
    return badCode()
  try
    await UserModel.emailVerified {eml:vcodeObj.eml}
  catch err
    debug.error err,' email:',vcodeObj.eml
  return goodCode()

# 'verifyEmail/emailVCode'
# 发送带有数字验证码的邮件
POST 'emailVCode',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    # FLAG: 标记用户手动操作发送验证码
    # TODO: use req.session.set to set session.emlVcode = true
    user.emlVcode = true
    neml = req.body?.neml?.toLowerCase()
    if neml
      if neml is user.eml
        return resp.send {ok:0, err:req.l10n('Enter a different email address.')}
      try
        tologin = await UserModel.checkEmlIsExists neml
      catch err
        return resp.send {ok:0, err:req.l10n(err.toString())}
      if tologin
        return resp.send {ok:0,err:req.l10n(MSG_STRINGS.NEW_EMAIL_ALREADY_EXISTS)}

      try
        emailError = await UserModel.checkEmailAddress neml
      catch err
        debug.error err,' checkEmailAddress:',email
        return resp.send {ok:0,err:req.l10n(MSG_STRINGS.BAD_PARAMETER)}
      if emailError
        return resp.send {ok:0,err:req.l10n(emailError)}
    sendEmailVerification {req,user,neml},(err,ret)->
      if err
        if err is SENT_MULTIPLE_TIMES
          return resp.send {ok:0, err:req.l10n(err.toString()),ec:3}
        return resp.send {ok:0, err:req.l10n(err.toString())}
      return resp.send {ok:1, ret:req.l10n(ret)}

# 'verifyEmail/verifyEmailVCode'
# 校验当前验证码是否与邮箱保存验证码相符
POST 'verifyEmailVCode',(req,resp)->
  _error = (err)-> resp.send {msg:req.l10n(err), ok:0}
  UserModel.appAuth {req,resp},(user)->
    unless (user?.eml and (b = req.body))
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    UserModel.verifyEmailVCode {user,body:b},(err,ret)->
      if err
        return _error(err)
      return resp.send {ok:1,msg:req.l10n(ret)}



APP 'resetPasswd'
# 'resetPasswd/:vcode'
GET ':vcode',(req,resp)->
  lang = req.locale()
  badCode = ->
    return resp.ckup 'webGeneralError',{lang,err:BAD_VCODE_MSG}
  goodCode = (vcode) ->
    return resp.ckup 'auth/resetPassword',{vcode,lang},'_'
  unless vcode = req.param 'vcode'
    return badCode()
  return goodCode(vcode) if vcode is 'test-vcode'
  try
    vcodeObj = await VcodeModel.check vcode
  catch err
    debug.error err,' vcode:',vcode
  unless vcodeObj
    return badCode()
  goodCode(vcode)

# 'resetPasswd/:vcode'
POST ':vcode',(req,resp)->
  badCode = ->
    return resp.ckup 'webGeneralError',{err:BAD_VCODE_MSG}
  unless vcode = req.param 'vcode'
    return badCode()
  try
    vcodeObj = await VcodeModel.check vcode
  catch err
    debug.error err,' vcode:',vcode
  unless vcodeObj
    return badCode()
  unless pwd = req.param 'pwd'
    return resp.ckup 'auth/resetPassword',{err:req.l10n('Need Password!'),vcode:vcode}
  unless (pwd.length >= 5)
    return resp.ckup 'auth/resetPassword',{err:req.l10n('Password at least 5 characters'),pwd:pwd,vcode:vcode}
  UserModel.resetPassword {eml:vcodeObj.id,pwd},(err)->
    if err
      return resp.ckup 'auth/resetPassword',{err:err,pwd:pwd,vcode:vcode}
    try
      vcodeObj = await VcodeModel.clear vcode
    catch err
      debug.error err,' vcode:',vcode
      return badCode()
    resp.ckup "generalMsg",{lang:req.locale(),msg:req.l10n("Successfully updated your password!")}

VIEW 'resetPasswd',->
  form action:"/resetPasswd/#{@vcode}",method:'POST',id:'change-passwd',style:"padding:15px;",->
    if @err
      p class:'error',style:"padding:5px;color:red;",-> @err
    input type:'password',placeholder:_('New Password'),name:'pwd',value:(@pwd or '')
    button type:'submit',form:'change-passwd',class:"btn btn-positive btn-block btn-long",-> _ "Submit"

# dataMethods = DEF 'dataMethods'
# dataMethods.robotP = (req, user, cb)->
#   robotPreventionSetup req,(err,rp)->
#     return cb err, rp

APP '1.5/user'

#'1.5/user/resendEmail'
# docs/Dev_signin_signup/register.drawio
POST 'resendEmail',(req,resp)->
  unless (eml = req.body?.eml)
    return resp.send {ok:0,err:req.l10n(MSG_STRINGS.BAD_PARAMETER)}
  # NOTE: limit call count (front and back)
  sendEmailLimiterFilter req,resp,()->
    # TODO: add more debug.info to user regiter step
    # TODO: sns hook update vcode db/suspend(bounce)
    try
      user = await UserModel.login2user eml
    catch err
      debug.error err, ' email:',eml
      return resp.send {ok:0,err:req.l10n(MSG_STRINGS.DB_ERROR)}
    if not user
      # 未找到用户
      return resp.send {ok:0, err:req.l10n(MSG_STRINGS.USER_NOT_FOUND)}

    # check email hardBounce/complaint
    try
      suspendReason = await UserEmailModel.getLatestReasonOfSuspendByEmail eml
    catch err
      debug.error err, ' email:',eml
      return resp.send {ok:0,err:req.l10n(MSG_STRINGS.DB_ERROR)}
    if suspendReason
      debug.warn "#{eml} is hardBounce or Complaint,reason:#{suspendReason}"
      return resp.send {ok:0,err:req.l10n(COMPLAINT_OR_HARD_BOUNCE_EMAIL)}
    
    sendEmailVerification {req,user},(err,ret)->
      if err
        if err is SENT_MULTIPLE_TIMES
          return resp.send {ok:0, err:req.l10n(err.toString()),ec:3}
        return resp.send {ok:0, err:req.l10n(err.toString())}
      return resp.send {ok:1, ret:ret}

# '1.5/user/contact'
# # TODO:@sam web, comment if not used
POST 'contact', (req,resp) ->
  UserModel.appAuth {req,resp},(user)->
    # return resp.redirect '/1.5/user/login' unless user
    opt = []
    for i in ['eml','mbl','wxid','nm','m','uid','forumid','forumtl','src','evaluate_uaddr']
      if req.body[i] and ('string' is typeof req.body[i])
        opt[i] = helpers.htmlEscape req.body[i]
      else
        opt[i] = req.body[i]
    subjectPrefix = "Request Info"
    if opt.src == 'renovation'
      subjectPrefix = "Renovation Quote Request"
    if opt.src is 'toplisting'
      subjectPrefix = "TOP Listing Request"
    subject = req.l10n("#{subjectPrefix} from user:") + opt.eml
    html = "<p> messge from #{opt.nm}: #{opt.m} <br>  email: #{opt.eml||''} <br>  phone: #{opt.mbl||''} <br>wechatid: #{opt.wxid||''} <br>src: #{opt.src||''}</p> "
    text = "messge from #{opt.nm}: #{opt.m}, email: #{opt.eml},phone: #{opt.mbl},wechatid: #{opt.wxid}, src:#{opt.src}"
    if opt.src is 'myListingNonRealtor'
      subject = req.l10n("Post Listing Requet from user:") + opt.eml
    if user?
      html += "<br><p>realUid: #{user._id}<br>realEml:#{user.eml}</p>"
      text += ", realUid: #{user._id}, realEml: #{user.eml}"
    if opt.forumid
      html = html + "<p> forum id: #{opt.forumid} <br> forum Title: #{opt.forumtl} </p>"
      text = text + ",forum id: #{opt.forumid}, forum Title: #{opt.forumtl}"
    # debug.debug html
    # debug.debug text
    # return resp.send {ok:1}
    if opt.evaluate_uaddr
      html = html + "<p> evaluate uaddr: #{opt.evaluate_uaddr}  </p>"
      text = text + "evaluate uaddr: #{opt.evaluate_uaddr} "
    mail =
      _trans: 'smtp'
      engine: config.mailEngine?.mailEngine or 'sendmail'
      replyTo: "<#{config.contact?.defaultRTEmail}>"
      to: config.contact?.defaultEmail
      subject: subject
      text: text
      html: html
      sbj: subject
      eventType: 'ContactEmail'
    sendMail.sendMail mail,(err,response)->
      return resp.send {ok:0} if err
      resp.send {ok:1}

# '1.5/user/robotSetup'
POST 'robotSetup', (req, resp)->
  robotPreventionSetup req,(err,rp)->
    return resp.send {ok:0, e:err.toString()} if err
    resp.send {ok:1, robotP:rp}

# '/user/:UID.json'
# Get Cureent user detail, return json result of details, used in wecard and edit profile and setting page

# '1.5/user/userInfo.json'
# # TODO:@sam  comment if not used
POST 'userInfo.json',(req,resp)->
  # uid = req.param 'uid'
  # if uid
  #   # searcj user by uid
  #   User.findOne {id:uid, roles:'realtor'},(err,ret)->
  #     if err
  #       resp.send {err:err.toString, success:false}
  #     else if ret is null
  #       resp.send {err:'User not found!', success:false ,errorType:'USER_NOT_FOUND'}
  #     else
  #       delete ret.verify
  #       delete ret.verify
  #       delete ret._id
  #       delete ret.src
  #       delete ret.login_id
  #       # delete ret.rid
  #       delete ret.pn
  #       delete ret.site
  #       # do not let use see verify info
  #       resp.send {title:"User Info",user:ret, success:true}
  # else
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0, err:MSG_STRINGS.NEED_LOGIN} unless user
    ret = {}
    ret = helpers.shallowExtendObject ret,user
    for i in ['avt','qrcd','grpqrcd']
      ret[i] = libPropertyImage.replaceRM2REImagePath(ret[i]) if ret[i]
    # do not let use see verify info
    delete ret.verify
    # delete ret.verify
    delete ret._id
    delete ret.src
    delete ret.login_id
    # delete ret.rid
    delete ret.pn
    delete ret.site
    # delete ret.rid
    delete ret.locale
    delete ret.lts
    delete ret.ip
    delete ret.rmb
    delete ret.actpts
    delete ret.wsc
    if ret.sas #translate sas list
      for i in ret.sas
        i.n = req.l10n(i.city)
        i.o = i.city
        i.p = i.prov
    # get user other info from user_profile
    # suser = req.session.get('user') #user[key],ret[key],suser[key]
    # for key in ['twturl','wxgrp','wurl']
      # debug.debug key,user[key],ret[key],suser[key],req.session.id(),req.cookies['k']
    return resp.send {title:"Edit",user:ret} unless UserModel.accessAllowed 'forumAdmin', user
    UserModel.findProfileById user._id,{}, (err, profile)->
      return resp.send {ok:0} if err
      if profile
        ret.wpHost = profile.wpHost if profile.wpHost
        ret.wpUsername = profile.wpUsername if profile.wpUsername
        ret.wpPwd = profile.wpPwd if profile.wpPwd
        ret.wpSecret = profile.wpSecret if profile.wpSecret
      return resp.send {title:"Edit",user:ret}



# '/1.5/user/login'
#get login page, POST to login
GET 'login',(req,resp)->
  unless (req.getDevType() is 'app') or (req.hostname is 'app.test')
    return resp.redirect '/adPage/needAPP'
  #TODO: if already loggedin ? go index?
  robotPreventionSetup req,(err,rp)->
    resp.ckup 'loginNew',{robotP:rp,err:req.param('err')},'_',{noasync:1, noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1}

# '/1.5/user/forgotPwd'
#get reset pwd page, POST to forgotPwd
GET 'forgotPwd',(req,resp)->
  resp.ckup 'loginNew',{mode:'forgotPwd'},'_',{noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1}


# '/1.5/user/changePwd'
#get reset pwd page, POST to forgotPwd
GET 'changePwd',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'loginNew',{mode:'changePwd'},'_',{noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1}

# '/1.5/user/register'
#get register page, POST to register
GET 'register',(req,resp)->
  url = req.param 'url'
  if user = req.session?.get('user')
    if 'string' is typeof user._id
      return resp.redirect url or '/1.5/index'
  unless (req.getDevType() is 'app')#or (req.hostname is 'app.test')
    return resp.redirect '/getapp'
  # show register page
  robotPreventionSetup req,(err,rp)->
    resp.ckup 'loginNew',{mode:'register',robotP:rp},'_',{noasync:1, noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1} # use cordova, need to show policy in browser

GET 'success',(req,resp)->
  url = req.param 'url'
  if user = req.session?.get('user')
    if 'string' is typeof user._id
      return resp.redirect url or '/1.5/index'
  unless (req.getDevType() is 'app')#or (req.hostname is 'app.test')
    return resp.redirect '/getapp'
  # show register page
  eml = req.param 'eml'
  wxUnionid = req.param 'wxUnionid'
  resp.ckup 'loginNew',{mode:'success',eml:eml,wxUnionid},'_',{noasync:1, noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1} # use cordova, need to show policy in browser

# '/1.5/user/oauth'
# 第三方登录成功跳转页面，提示会收到邮件，里面有随机密码
GET 'oauth', (req, resp)->
  robotPreventionSetup req,(err,rp)->
    user = req.session.get 'user'
    if not user
      # NOTE: session should have user from oauthLoginUser
      debug.error "session id no user:#{req.session.id()}"
      return resp.redirect '/1.5/settings'
    goBackUrl = req.param 'd'
    user.eml = UserModel.getEmail user
    appmode = req.cookies[APPMODE]
    goHomeUrl = '/home/' + if appmode is 'rm' then 'marketplace'else 'mls'
    resp.ckup 'prompt-page', {goBackUrl,oauth:user,goHomeUrl}

# 第三方登录，没有邮箱的情况下跳转页面，绑定或注册邮箱
GET 'bindThirdParty', (req, resp)->
  robotPreventionSetup req,(err,rp)->
    # user = req.session.get 'user'
    goBackUrl = req.param 'd'
    uTp = req.param 'uTp'
    uTp = helpers.strCapitalize uTp
    resp.ckup 'bind-account', {goBackUrl,robotP:rp,uTp}

# 微信登录跳转页面，绑定或注册邮箱
GET 'bind/:wxid', (req, resp)->
  robotPreventionSetup req,(err,rp)->
    eml = req.param 'eml'
    goBackUrl = req.param 'd'
    uid = req.param 'uid'
    resp.ckup 'bind-account', {uid,goBackUrl,wxid:req.param('wxid'),robotP:rp,eml}

VIEW 'loginNew', ->
  coffeejs {vars:{
    err:@err,
    mode:@mode or 'login',
    hasUser:if @req.session.get('user') then true else false,
    isCip:@req.isChinaIP(),
    isIOSDevice:@req.isIOSDevice(),
    eml:@eml,
    wxUnionid:@wxUnionid,
    robotP:@robotP
    }}, ->
      null
  div id:'vueBody',->
    text """<app-login></app-login>"""
  js '/js/entry/commons.js'
  js '/js/emailMisspelled.min.js'
  js '/js/entry/appLogin.js'
###
VIEW 'login',->
  text ckup "busy-icon",{angular:true}
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: 'javascript:void(0);', dataIgnore: 'push',onclick:'goBack(event)'
    h1 class: 'title',-> _ 'RealMaster'
  div class: 'content', ->
    div id:'loginTable', ->
      img src:'/img/logo.png'
      div class: 'own-login', ->
        form class: 'input-group', name:'loginForm', action:'/1.5/user/login', method: 'POST',->
          div class:"inputs", ->
            if @ret
              <EMAIL>
                if @ret.err  and Array.isArray @ret.err
                  for e in @ret.err
                    p class:'error-block', -> text e.nmessage(_)
                else
                  p class:'error-block', -> em -> text _(@ret.err  or @ret.err.toString())
            input type: 'email',    name:'email',  placeholder: 'Email', value: (@ret?.email or '')
            input type: 'password', name:'pwd',    placeholder: 'Password', style:"margin-top:10px;"
            input type: 'hidden',   name:'pn',     id:'pn'
          button class: 'btn btn-block btn-positive btn-long', type: 'submit',onclick:'this.form.submit()',-> _ 'Login'
         div class:'other-action', ->
          span class: 'pull-left', style:'margin-left:6%;', ->
            #text 'Forgot pwd?'
            a href: '/1.5/user/forgotPwd', dataTransition: 'slide-in',-> _ 'Forgot Password?'
          span class:'pull-right', style:'margin-right:6%;', ->
            text _ "Not a user"
            text "? "
            a href: '/1.5/user/register', dataIgnore:'push',-> _  'Register Now'#'Sign Up'
      div id: 'loginSplit', style:"display:none;",->
        div class:'left', ->
        div class:'text', ->
          text _ "Or"
        div class:'right', ->
      div id: 'wechatLogin', style:"display:none; ",->
        button class:'btn btn-block btn-positive btn-long', onclick:"wechatAuth()", style:"padding-right: 25px;", ->
          i class:'fa fa-wechat pull-left'
          text _ 'WeChat Login'
      coffeejs ->
        RMSrv.hasWechat (has)->
          if has
            document.getElementById('wechatLogin').style.display = 'block'
            document.getElementById('loginSplit').style.display = 'block'
        wechatAuth = ->
          document.getElementById('busy-icon').style.display = 'block'
          RMSrv.wechatAuth()
        null
  coffeejs ->
    if localStorage.pn then document.getElementById('pn').value = localStorage.pn
    null
###
###
VIEW 'register',->
  header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', onclick:'goBack(event);', dataIgnore:'push' #dataTransition: 'slide-out'
      h1 class: 'title',-> _ 'RealMaster Register'

  div class: 'content', ngApp:'appReg',ngController:'regCtrl',->
    div id:"registerTable", ->
      if @err
        if @err.errors and Array.isArray @err.errors
          for e in @err.errors
            p class:'error-tip',-> text e.nmessage(_)
        else
          p class:'error-tip',-> em -> text @err.message or @err.toString()
      form class: 'input-group', name:'regForm', action:'/1.5/user/register', method: 'POST', novalidate:'',->
        div class:'inputs', ->

          input type: 'email', name:'email', placeholder: _('Email'),ngModel:"email",required:'true',ngInit:@req.body?.email
          p class:'error-block',ngShow:"regForm.email.$invalid && !regForm.email.$pristine",->
            text _ 'Enter a valid email.'

          input type: 'password', name:'pwd',  placeholder: _('Password'),ngModel:"pwd",ngMinlength:'5',ngMaxlength:'40',required:'',ngInit:@req.body?.pwd
          #i class:'fa fa-eye'
          p class:'error-block', ngShow:"regForm.pwd.$error.minlength",->
            text _ 'Password too short.'
          p class:'error-block', ngShow:"regForm.pwd.$error.maxlength",->
            text _ 'password too long.'
          input type: 'password', name:'pwdR', placeholder: _('Repeat Password'),ngModel:"pwdR",required:'',ngInit:@req.body?.pwdR
          p class:'error-block',ngShow:"pwd != pwdR && !regForm.pwdR.$pristine",->
            text _ 'Does not match password.'
          input type: 'number',   name:'mbl',     placeholder: _('Cell Phone(Number Only. No space/dash)'),ngInit:@req.body?.mbl
          input type: 'text',     name:'robtfill',  placeholder: 'robot fill',   ngShow:'false', style:'display:none' # TODO
          input type: 'text',     name:'jsfill',    placeholder: 'js fill',      ngShow:'false', style:'display:none'
          if @robotP
            input type: 'hidden',   name:@robotP.hk, value:@robotP.hv
          input type: 'hidden',   name:'pn',id:'pn'
        button class: 'btn btn-block btn-long btn-positive btn-mar-top', type: 'submit',ngDisabled:"regForm.$invalid",->
          text _ 'Register'
      #style:'margin-right: 10px;color: rgb(103, 155, 239);',
      a href: 'javascript:void(0)',onclick:'RMSrv.showInBrowser("https://www.realmaster.ca/#!about/skt89");',style:'margin-right: 10px;color: rgb(103, 155, 239);',class:'pull-right ',-> span -> _ 'Privacy Policy'
  coffeejs {},->
    app = angular.module('appReg', [])
    regCtrl = ($scope, $http) ->
    app.controller 'regCtrl', [
      '$scope'
      '$http'
      regCtrl
    ]
    if localStorage.pn then document.getElementById('pn').value = localStorage.pn
    null
###

VIEW 'bind-account',->
  text """<style type="text/css">
  .input-group input{
    padding-left:0;
    margin-top: 15px;
  }
  .inline-error{
    margin-top: -22px;
    height: 20px;
    width: 70px;
    font-size: 14px;
    display: block;
    position: absolute;
    right: 0;
    color: #e03131;
    white-space: nowrap;
    text-align: right;
    overflow: hidden;
  }
  .inline-error.fa{
    font-size: 17px;
  }
  .inline-error.fa-check-circle{
    color:#5CB85C;
  }
  .roles-select{
    border-bottom: 1px solid #f0eeee;
    margin-top: 23px;
  }
  .roles-select > div,.roles-select > select{
    display: inline-block;
  }
  .roles-select > div{
    width: 20%;
    color: black;
    font-size: 17px;
  }
  .roles-select > select{
    width: 80%;
    margin-bottom: 0;
    height: 26px;
    background: none;
    border: none;
    -webkit-appearance: menulist;
    box-shadow: none;
    padding-left: 0;
    color: black;
    font-size: 17px;
  }
  .account{
    width: 50%;
    display: inline-block;
    text-align: center;
    padding: 15px 0;
    border-bottom: 2px solid #929292;
    color: #929292;
  }
  .account.active{
    border-color:#000;
    color: #000;
    font-weight: bold;
  }
  .error-block{
    position: relative;
  }
  .forget{
    padding: 6px 8px 7px;
    margin-bottom: 0;
    font-weight: 400;
    line-height: 1;
    vertical-align: top;
    cursor: pointer;
    background-color: white;
    border-radius: 3px;
    position: absolute;
    font-size: 12px;
    display: block;
    white-space: nowrap;
    text-align: right;
    overflow: hidden;
    color: #656565;
    margin-top: -30px;
    right: 0px;
    border: 1px solid #5cb85c;
  }
  .shortPwd{
    padding-right: 115px !important;
  }
  </style>
  """
  header class: 'bar bar-nav', ->
    # if wrong pwd, this page in hist for multiple times; ,href:'/1.5/user/login'
    a class: 'icon fa fa-back pull-left', onclick:'onClickBack()', dataIgnore:'push' #dataTransition: 'slide-out'
    h1 class: 'title',-> _ 'RealMaster'
  div class: 'content', ngApp:'app',ngController:'appCtrl',->
    div class:'overlay loader-wrapper', id:'busy-icon', ngShow:'loading', style:'display:block',->
      div class:'loader'
    div id:'registerTable', ->
      form class: 'input-group', name:'bindForm', action:'{{action}}', method: 'POST', novalidate:'',->
        div class:'inputs', style:'margin-left: 6.5%; width: 87%;',->
          h1 style:'margin-top: 20px; font-weight:normal;text-align:center', ->
            # span ngShow:'isOauth', -> text _ 'Select a Role'
            # emlTaken检查eml是否存在
            span ngHide:'isOauth', ->
              span ngHide:'', ->
                text _ 'Bind Account'
          if @err
            div class:'err-block', style:'color: #e03131;',->
              text @err
          p style:'margin:0; font-size:13px;text-align: center;', ->
            if @oauth
              span ngHide:'',->
                text _ 'There is a random password in the email, which can be modified after logging in.'
            else
              span ngHide:'',->
                # 第三方登录4个入口公用同一个页面，uTp没有的情况为WeChat登录，所以不能给uTp默认值
                uTp = @uTp or 'Wechat'
                text sprintf _('Bind %s to your Realmaster account, sign in to Realmaster with %s.'),uTp,uTp

          div style:'margin-top:18px;margin-bottom:10px; font-size:15px;',->
            span class:'account',ngClass:"{'active':!isNew}",ngClick:'changeAccount("existing")',->
              text _ 'Existing Account'
            span class:'account',ngClass:"{'active':isNew}",ngClick:'changeAccount("new")',->
              text _ 'New Account'

          input type: 'email', name:'email', ngDisabled:'emailDisable', placeholder: _('Email'), ngModel:'email', required:'true', value: (@eml or ''),ngInit:'init()'
          p class:'error-block', style:'', ->
            span class:'inline-error fa fa-exclamation-circle',ngShow:'bindForm.email.$viewValue.length >0 && bindForm.email.$invalid && bindForm.email.$dirty'
            span class:'inline-error fa fa-check-circle',ngShow:'emlTaken'

          input type: 'password', name:'pwd',ngClass:'{"shortPwd":!isNew}',  placeholder: _('Password'), ngModel:'pwd', ngRequired:'true'
          div style:'position: relative;', ngShow:'!isNew',->
            a href:'/1.5/user/forgotPwd?d=1.5/index',class:'forget',->
              text _ 'Reset Password'
          p class:'error-block', style:'', ->
            span class:'inline-error',ngShow:'isNew && pwd.length<5 && pwd.length>0',->
              text _ 'Too short','password'
            span class:'inline-error',ngShow:'isNew && pwd.length>20',->
              text _ 'Too long','password'

          input type: 'password', name:'pwdR',  placeholder: _('Repeat Password'), ngModel:'pwdR', ngShow:'isNew', ngRequired:'isNew'
          p class:'error-block', style:'', ->
            span class:'inline-error',ngShow:'(pwd != pwdR) && isNew && pwdR.length>0 && bindForm.pwdR.$dirty',->
              text _ 'Not match','password'
          input type: 'number', name:'mbl',  placeholder: _('Cell Phone'), ngModel:'mbl', ngShow:'isNew',inputmode:"numeric", pattern:"[0-9]*"

          # 注释掉角色选择相关代码
          # div class:'roles-select',ngShow:'isNew', ->
          #   div -> _ "I'm a"
          #   select ngModel:'userRole',->
          #     userRoles = [
          #       {k:'visitor',v:'Buyer/Seller'},
          #       {k:'realtor',v:'Realtor'},
          #       {k:'mortgage',v:'Mortgage Specialist'},
          #       {k:'inspector',v:'Inspector'},
          #       {k:'lawyer',v:'Lawyer'},
          #       {k:'contractor',v:'Contractor'},
          #       {k:'insurance_agent',v:'Insurance Agent'}
          #     ]
          #     for r in userRoles
          #       option value:"#{r.k}", ->
          #         text _ r.v
          input type: 'hidden',   name:'wxid', id:'wxid',value:@wxid
          input type: 'hidden',   name:'pwdR', id:'pwd2'
          input type: 'hidden',   name:'pn',   id:'pn'
          if @robotP
            input type: 'hidden', id:'robotP', name:@robotP.hk, value:@robotP.hv
          div style: 'min-height: 30px;margin-top: 10px;font-size: 14px;color: rgb(239, 113, 113);line-height: 1em;text-align: left;height: 24px;margin-bottom: 10px;',->
            span class:'error-block', ngShow:'err', ->
              text '{{err}}'
            span style:'margin-left:10px',ngShow:"errType == 'EMAIL_VERIFY'",->
              span ngShow:'verificationCountdown > 0',-> text '{{verificationCountdown}}s'
              a ngShow:'verificationCountdown == 0', ngClick:'resendEmailVerification()', ->
                text _ 'Send Verification Email','EMAIL_VERIFY'
        # button type:'button', class: 'btn btn-block btn-long btn-positive btn-confirm', ngClick:"submit($event)", ngHide:"emlTaken || isNew", ngDisabled:"bindForm.$invalid", ->
        #   text _ 'Login'
        button type:'button', class: 'btn btn-block btn-long btn-positive btn-confirm', ngClick:"login($event)", ngHide:"isNew",  ngDisabled:"btnDisabled()", ->
          text _ 'Login'
        button type:'button', class: 'btn btn-block btn-long btn-positive btn-confirm', ngClick:"register($event)", ngShow:"isNew",  ngDisabled:"btnDisabled()", ->
          text _ 'Register'
      # a href: '/1.5/user/forgotPwd', style:'margin-right: 10px;color: rgb(103, 155, 239);  margin-left: 6.7%;',class:'pull-left ', ngShow:"emlTaken",-> span -> _ 'Forgot Password?'
  coffeejs {vars:{uid:@uid,oauth:@oauth,rpwd:@pwd,eml:@eml,goBackUrl:@goBackUrl,uTp:@uTp}}, ->
    null
  js '/js/bind_account.min.js'

VIEW 'prompt-page',->
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href:@goBackUrl, dataIgnore:'push'
    h1 class: 'title',-> _ 'RealMaster'
  div class: 'content',->
    p class:'fa fa-check-circle',style:'text-align: center;width: 86.6%;margin: 50px 6.7%;color: #5CB85C;font-size: 100px;'
    p style:'text-align: center;width: 86.6%;margin: 50px 6.7%;', ->
      span ngHide:'',->
        text sprintf _('Login successful for %s. An auto-generated password has been emailed for platform access.'),@oauth.eml
    a class: 'btn btn-block btn-long btn-positive btn-confirm', href:@goHomeUrl, ->
      text _ 'OK'

###
VIEW 'forgotPwd',->
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: 'javascript:void(0);',onclick:'goBack(event);', dataTransition: 'slide-out'
    h1 class: 'title',-> _ 'Retrieve Password'
  div class: 'content', ->
    div id: 'loginTable', ->
      if @err
        p class:'error-tip',-> em -> text @err.message or @err.toString()
      form class: 'input-group', action:'/1.5/user/forgotPwd',method:'POST',->
        div class:'inputs', ->
          input type: 'email', name:'email', placeholder: 'Email'
        button class: 'btn btn-positive btn-block btn-long btn-mar-top', type:'submit', -> _ 'Retrieve Password'
###
###
VIEW 'changePwd',->
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: 'javascript:void(0);',onclick:'goBack(event);', dataTransition: 'slide-out'
    h1 class: 'title',-> _ 'Change Password'
  div class: 'content', ngApp:'repassApp',ngController:'repassCtrl',->
    div id: 'registerTable', ->
      if @err
        p class:'error-tip',-> em -> text @err.message or @err.toString()
      form class: 'input-group',name:'repassForm', action:'/1.5/user/changePwd', method: 'POST', novalidate:'',->
        div class:'inputs', ->
          input type: 'password', placeholder: _('Current Password'), name: 'oldPwd',ngModel:"oldPwd",required:'',ngInit:@req.body?.oldPwd
          p class:'error-block',ngHide:"repassForm.oldPwd.$valid",->
            text _ 'Enter current password.'
          input type: 'password', name:'pwd',  placeholder: _('New Password'),ngModel:"pwd",ngMinlength:'5',ngMaxlength:'40',required:'true',ngInit:@req.body?.pwd
          #i class:'fa fa-eye'
          p class:'error-block', ngShow:"repassForm.pwd.$error.minlength",->
            text _ 'Password too short.'
          p class:'error-block', ngShow:"repassForm.pwd.$error.maxlength",->
            text _ 'password too long.'
          input type: 'password', name:'pwdR', placeholder: _('Repeat New Password'), ngModel:"pwdR",required:'',ngInit:@req.body?.pwdR
          p class:'error-block',ngShow:"pwd != pwdR && !repassForm.pwdR.$pristine",->
            text _ 'Does not match password.'
        button class: 'btn btn-block btn-long btn-positive btn-mar-top', type: 'submit',ngDisabled:"repassForm.$invalid",->
          text _ 'Change Password'
  coffeejs {},->
    app = angular.module('repassApp', [])
    app.controller 'repassCtrl', [
      '$scope'
      '$http'
      ($scope, $http) ->
    ]
    null
###
dataMethods = DEF 'dataMethods'

# syncUserBlock = ({user,blkUids,blkCmnts},cb)->
#   UserProfile.findOne {_id:user._id},{fields:{blkUids:1,blkCmnts:1}},(err,userProfile)->
#     return cb(err,null) if err
#     blkUidsDB = userProfile?.blkUids or {}
#     blkCmntsDB = userProfile?.blkCmnts or {}
#     blkCmnts ?= {}
#     blkUids ?= {}
#     if ((blkCmnts?.length is 0) and (blkUids?.length is 0))
#       return cb(null,{blkUids:blkUidsDB,blkCmnts:blkCmntsDB})
#     newBlkUids = Object.assign blkUids,blkUidsDB
#     #blkCmnts {"5f621a0e6ab23f3411963c74":{"16":1,"1":1},"5f64bbee6ab23f3411fc0a54":{"4":1}
#     newBlkCmnts = Object.assign {},blkCmntsDB
#     for k,v of blkCmnts
#       newBlkCmnts[k] = Object.assign v,newBlkCmnts[k]
#     set = {blkUids:newBlkUids,blkCmnts:newBlkCmnts}
#     update = {$set:set}
#     UserProfile.updateOne {_id:user._id},update,(err,ret)->
#       return cb(err,null) if err
#       cb(null,set)

# AFTER_LOGIN = [func]
# func: (req,user,ret,cb)
# cb(err,ret):
setupWechatUser = (req,b,user,ret,cb)->
  if b.wxid
    return UserModel.bindWechatNeedLogin {req,wxuid:b.wxid}, (err)->
      debug.error err if err
      cb err,ret
  else
    cb null,ret

setupUserPn = (req,b,user,ret,cb)->
  return cb(null,ret) unless b?.pn?.length > 0
  user = req.session.get('user')
  try
    await UserModel.updatePnLocation {
      req
      user: user
      pnloc: b.pn
    }
  catch err
    debug.error 'setupUserPn error:', err
  cb(null,ret)

setupUserForumBlock = (req,b,user,ret,cb)->
  ret.verifiedRole = dataMethods.verifiedRole(req, user)
  {blkCmnts,blkUids} = req.body
  UserModel.syncUserBlock {user,blkCmnts,blkUids},(err,userProfile)->
    debug.error 'sync user profile in login: ', err if err
    ret.blkUids = userProfile.blkUids if userProfile?.blkUids
    ret.blkCmnts = userProfile.blkCmnts if userProfile?.blkCmnts
    cb null,ret

setupThirdPartyUser = (req,b,user,ret,cb)->
  if b.uTp
    uData = libOauth.getLastOauthUserData(req)
    unless uData
      return cb MSG_STRINGS.USER_NOT_FOUND
    uData = libOauth.genUserFromThirdPartyObj uData
    return UserModel.oauthLoginUser {req,uData,uid:user._id}, (err)->
      debug.error err if err
      cb err,ret
  else
    cb null,ret

AFTER_LOGIN = [
  setupWechatUser,
  setupThirdPartyUser,
  setupUserPn,
  setupUserForumBlock
]

POST 'login',(req,resp)->
  _need_login = (err,extra)->
    return resp.send {ok:0, err:req.l10n(err.toString()),extra}
  unless req.getDevType() is 'app'
    return resp.send {ok:0, err:req.l10n("Please use APP")}

  unless (b = req.body).email and b.pwd
    return _need_login('No Email or Password')
  b.email = if Array.isArray b.email then b.email[0] else b.email

  UserModel.appLogin {req, u:b.email,p:b.pwd},(err,user,extraInfo)->
    if err or not user?
      b.err = err
      return _need_login(b.err,extraInfo)
    try
      oriUUid = await UserModel.bindUid {user,uuid:req?.cookies?.uuid}
      if oriUUid
        await req.session.updateUUID(req,oriUUid)
    catch err
      debug.error err
    ret = {ok:1, url:'/1.5/index'}
    # 判断用户是否等待验证经纪人
    if user?.waitingVerifyAgent is true
      ret.waitingVerify = true
    index = 0
    doOne = (ret)->
      if fn = AFTER_LOGIN[index]
        fn req,b,user,ret,(err,nRet)->
          # deal with error
          if err
            debug.error err
            return _need_login(err)
          index++
          doOne(nRet)
      else
        resp.send ret
    doOne ret

# '/1.5/user/logout'
GET 'logout',(req,resp)->
  # rm = req.query.rm
  UserModel.appLogout req,(err)->
    debug.debug err if err
    req.session.clearOldUUID(req)
    # not work, session changed
    # if (lang = req.param 'lang') and (lang in ['zh','zh-cn','en'])
    #   req.setLocale lang
    # if rm is '1'
    #   UserModel.removeRM (err)->
    #     debug.debug err if err
    #     resp.redirect '/1.5/index'
    # else
    resp.redirect '/1.5/index'
###
reg_schema = new json.JsonSchema {
  type: 'object'
  properties:
    email:
      type: 'string'
      format: 'email'
      required: true
    password:
      required: true
      type: 'string'
      minLength: 5
    phone:
      required: false
      type: 'string'
      #format: 'phone'
    #agree_terms:
     # required: true
  }
###
# createUniqueUserID = DEF 'createUniqueUserID'
###
'/1.5/user/register'
User register POST
  check robot
  validate data
  create/confirm user
  create/confirm login/auth
  if already has login, show Already has user, ask login
  when success show success, ask login
###
robotPreventionCheck = DEF 'robotPreventionCheck'
appVerifyRobot = DEF 'app_verify_robot'
appRegisterPcGeetest = DEF 'app_register_pcgeetest'

POST 'grecaptcha/register',(req, res)->
  res.send {ok: 1, siteKey: config.recapcha?.siteKey}

POST 'pc-geetest/register',(req, res)->
  appRegisterPcGeetest req, (err, data)->
    if err
      debug.error err
      res.send {err:err.toString()}
    else
      res.send data

#1.5/user/checkAccount
POST 'deleteAccount',(req,resp)->
  body = req.body or {}
  eml = body.email
  verifyEmlTime = req.session.get 'verifyEml'
  now = new Date().getTime()
  if not (req.getDevType() is 'app')
    return respError {clientMsg:'Not authed', resp}
  if now - verifyEmlTime > 10*60*1000
    req.session.set 'verifyEml',0
    return respError {clientMsg:'Session timeout', resp}
  LimiterModel.isOverLimit {type:LimiterModel.UNUSED_WECHAT,key:req.remoteIP()},(err,isUpperLimit)->
    return respError {clientMsg:err, resp} if err
    if isUpperLimit
      err = req.l10n 'The maximum number of users deleted today has been reached'
      return respError {clientMsg:err, resp}
    UserModel.deleteUnuseAccount {eml},(err)->
      if err
        debug.error err
        return resp.send {ok:0, err:req.l10n(err.message or err.toString())}
      resp.send {ok:1}

#1.5/user/checkAccount
POST 'checkAccount',(req,resp)->
  body = req.body or {}
  email = body.email
  isCip = req.isChinaIP()
  UserModel.ensureUser {req,data:body},(err,user)->
    return respError {clientMsg:err,resp} if err
    UserModel.hasLoginId email,(err,hadLogin)-> # check if has login already
      if hadLogin
        return respError {
          clientMsg:req.l10n(MSG_STRINGS.REGISTERED_BEFORE),
          resp,
          errorCode:2}
      else
        resp.send {ok:1}

###
# @description 检查能否注册
# @param {string} email - email for registeration
# @param {string} pwd - password for registeration
# @param {string} pwdR - password confirm
# @param {string} ip - ip of registeration
# @return {object|null} 不能注册时返回object，可以注册时返回null
    type result{
      ec    : number #错误码 [0,1];
               0:参数错误或邮箱bounce/compalint，显示错误信息;
               1:验证邮件已经发送，提示用户去查看邮箱或再次发送
               2:邮箱已经注册
      err   : string #错误信息
    }
###
verifyRegistrationConditons = ({email,pwd,pwdR,ip})->
  return {ec:0,err:'Need email'} unless email
  return {ec:0,err:'Need password'} unless pwd
  return {ec:0,err:'Need reinput password'} unless pwdR
  return {ec:0,err:'Passwords do not match'} unless pwd is pwdR
  return {ec:0,err:'Password is too short'} if pwd.length < 6
  email = email.toLowerCase()
  try
    emailError = await UserModel.checkEmailAddress email
  catch err
    debug.error err,' checkEmailAddress:',email
    return {ec:0,err:MSG_STRINGS.BAD_PARAMETER}
  if emailError
    return {ec:0,err:emailError}

  # verify count of ip registrations
  # robotV !== true 未进行人机验证，校验是否需要
  # NOTE: 暂时先不加人机验证校验
  # if not robotV
  #   try
  #     registerCount = await UserRegisterModel.countByIp ip
  #   catch err
  #     debug.error "count register ip error,ip:#{ip},err:",err
  #     return {ec:0,err:MSG_STRINGS.DB_ERROR}
  #   # 判断次数返回error,需要人机验证
  #   if registerCount > 2
  #     return {ec:1,err:'Need verify robot'}
  # 保存ip
  try
    await UserRegisterModel.saveRegisterIpAndEmail ip,email
  catch err
    debug.error "saveRegisterIpAndEmail error,ip:#{ip},email:#{email},err:",err
    return {ec:0,err:MSG_STRINGS.DB_ERROR}
  
  # check registered before/already have user
  try
    authResult = await UserEmailModel.checkEmailRegistered email
  catch err
    debug.error err,' email:',email
    return {ec:0,err:MSG_STRINGS.DB_ERROR}
  if authResult
    return {ec:2,err:MSG_STRINGS.REGISTERED_BEFORE}

  # check hardBounce or complaint
  try
    suspendReason = await UserEmailModel.getLatestReasonOfSuspendByEmail email
  catch err
    debug.error err,' email:',email
    return {ec:0,err:MSG_STRINGS.DB_ERROR}
  if suspendReason
    return {ec:0,err:COMPLAINT_OR_HARD_BOUNCE_EMAIL}

  # check proxy email
  # NOTE:proxy email can not register
  if isProxyEmail email
    return {ec:0,err:COMPLAINT_OR_HARD_BOUNCE_EMAIL}
  return null

# '1.5/user/register'
POST 'register',(req,resp)->
  body = req.body or {}
  isApi = body?.isApi
  isCip = req.isChinaIP()
  ip = req.remoteIP()

  findError = (err)->
    debug.error err,' body:',body
    if isApi
      #   return resp.ckup 'register',{err:err}
      return resp.send {ok:0, err:req.l10n(err.message or err.toString())}
    else
    # TODO 测试微信注册失败
      robotPreventionSetup req,(err2,rp)->
        debug.error err2 if err2
        eml = req.param 'eml'
        resp.ckup 'bind-account', {wxid:req.param('wxid'),robotP:rp,pwd:req.param('pwd'),eml:eml,err:err}
  
  checkRet = await verifyRegistrationConditons {...body,ip}
  if checkRet
    # 不能注册，返回原因
    checkRet.ok = 0
    checkRet.err = req.l10n(checkRet.err)
    return resp.send checkRet

  REGISTER_SUCCESS_MSG = 'Please verify your email'

  # done = (user,msg,ec)->
  #   ret = {ok:1, msg:req.l10n(msg)}
  #   if ec
  #     ret.ec = ec
  #     ret.ok = 0
  #     ret.err = ret.msg
  #   if body.pn?.length > 0
  #     UserModel.updatePnLocation {req,user,pnloc:body.pn},->
  #       if isApi then resp.send ret else resp.ckup 'authResult', {result:msg}
  #   else
  #     # incall followUser
  #     if isApi then resp.send ret else resp.ckup 'authResult', {result:msg}

  _bindWechat = (b,cb)->
    return cb null if not b.wxuid
    UserModel.bindWechatWithoutLogin {req,wxuid:b.wxuid,eml:b.eml},cb

  # check schema
  b = {}
  b.email = body.email.toLowerCase() if body.email
  b.password = body.pwd + '' #force to string
  b.mbl = body.mbl
  b.nm = body.nm or ""
  followerID = body.uid
  if b.wxuid = body.wxid
    b.src = 'WeChat Auth'
  else if body.src is 'download'
    b.src = 'download'
  b.uTp = body.uTp if body.uTp
  # debug.debug(b)
  appVerifyRobot body,isCip, (err) ->
    return resp.send {ok:0, err: err} if err
    try
      user = await UserModel.registerUser req,b
    catch err
      return findError(err)
    _bindWechat b,(err)->
      return findError err if err
      sendEmailVerification {req,user},(err)->
        if err and (err is SENT_MULTIPLE_TIMES)
          return resp.send {ok:0, err:req.l10n(err.toString()),ec:3}
        # 如果有abtest，则保存注册时间
        if config.abTest?.homepage
          uuid = req.cookies.uuid
          try
            await ABTestModel.update {uuid,groupName:'home',action:'signUp'}
          catch err
            debug.error err,"uuid:#{uuid}"
        if followerID
          #follow must come from API. no need to check isApi
          return UserModel.followSomeone {req, user, buzUserID:followerID},(err,nUser)->
            return findError err if err
            return resp.send {ok:1, ret:req.l10n(REGISTER_SUCCESS_MSG)}
        else
          return resp.send {ok:1, msg:req.l10n(REGISTER_SUCCESS_MSG)}

VIEW 'authResult',->
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: 'javascript:void(0);',onclick:'goBack(event);', dataTransition: 'slide-out'
    h1 class: 'title',-> _ 'RealMaster'
  div class: 'content',style:'margin:10px;',->
    div class: ' loginTable', ->
      p ->
        text _ @result
      if @req.session.get("user")
        a class: 'btn btn-positive btn-block btn-long', href: '/1.5/index',-> _ 'Go To Homepage'
      else
        a class: 'btn btn-positive btn-block btn-long', href: '/1.5/user/login',-> _ 'Go To Login'

# '1.5/user/forgotPwd'
POST 'forgotPwd',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  # return resp.ckup "forgotPwd",{err:"Need email address"}
  return error(req.l10n('Need email address')) unless (p = req.body) and p.email
  email = p.email.toLowerCase()
  # email mx验证失败不需要通知admin,提前验证
  try
    validRet = await getValidatedEmail email
  catch err
    debug.error err,"email:#{email}"
    return error(err.toString())
  if not validRet?.length
    return error(req.l10n(MSG_STRINGS.INVALID_EMAIL_ADDRESS))
  UserModel.hasLoginId email,(err,has)->
    return error err if err
    unless has
      return error req.l10n MSG_STRINGS.CANT_FIND_LOGIN_ID
    try
      {vcode,vcodeObj} = await VcodeModel.create {tp:TYPE_STRINGS.RESET_PASSWD,id:email,expHours:2}
    catch err
      debug.error err, ' neml:',email
      return error(err.toString())
    subject = req.l10n(req.setting.resetPassword?.subject or MSG_STRINGS.DEFAULT_RESETPWD_SUBJECT)
    domainName = req.setting.emailSetting?.domainName or MSG_STRINGS.DEFAULT_EMAIL_DOMAIN_NAME

    domain = config.serverBase?.wwwDomain
    if not req.isChinaIP()
      domain = domain.replace 'cn','com'
    url = "https://#{domain}/#{req.locale()}/resetPasswd/#{vcode}"

    data = {link: url}
    
    data = Object.assign(data, req.setting.emailSetting, req.setting.signUpEmail)
    data.confirmMsg = req.l10n("This email confirms your #{domainName} password change.")
    data.contactEml ?= MSG_STRINGS.DEFAULT_CONTACT_EML
    data.contactTel ?= MSG_STRINGS.DEFAULT_CONTACT_TEL
    data.logo ?= MSG_STRINGS.DEFAULT_EMAIL_LOGO

    ret = getDotHtml req, 'resetPassword', data
    if ret.err then return error(req.l10n(ret.err))
    html = ret.html
    engine = config.mailEngine?.mailEngine or 'sendmail'
    mail =
      _trans: 'smtp'
      engine: engine
      priority: true
      from: "#{req.l10n(domainName)}<#{sendMail.getFromEmailByEngine(engine)}>"
      #_temp: 'rmapp-newpassword'
      replyTo: "<#{config.contact?.defaultRTEmail}>"
      to: p.email
      #bcc: '<EMAIL>'
      subject: subject
      text: req.l10n(req.setting.resetPassword?.text or 'Please click the link to reset your RealMaster password.') + "\n" + url
      # html: "<p>" + req.l10n("Please click the link to reset your RealMaster password.") + "<br> <a href='#{url}'>" + url + "</a><p>"
      html: html
      sbj: subject
      isNeedValidate: true
      mailEngineListIndex:0
      eventType: 'ResetPasswordEmail'
    #INCALL 'simpleMail',mail,(err,response)->
    sendMail.sendMail mail,(err,response)->
      # return resp.ckup 'forgotPwd',{err:"When Send Email:" + err} if err
      if err
        req.logger?.error err
        SysNotifyModel.notifyAdmin(err.toString(), true)
        return error("When Send Email:" + err)
      # let user known send the email address
      resp.send {ok:1, msg:req.l10n("A password reset link was sent to your email. It will be valid for 2 hours.")}
      # resp.ckup 'authResult',{result:'A new password was sent to your email.'}

# TODO: still in use?
# '1.5/user/forgotPwdAutoReset'
POST 'forgotPwdAutoReset',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  # return resp.ckup "forgotPwd",{err:"Need email address"}
  return error(req.l10n('Need email address')) unless (p = req.body) and p.email
  newPass = UserModel.genPass {length:6}
  # reset password
  UserModel.resetPassword {id:p.email,pwd:newPass},(err)->
    # return resp.ckup 'forgotPwd',{title:'Retrieve Password',err:err} if err
    return error(err.toString()) if err
    # send to email
    mail =
      _trans: 'smtp'
      engine: config.mailEngine?.mailEngine or 'sendmail'
      #_temp: 'rmapp-newpassword'
      replyTo: "<#{config.contact?.defaultRTEmail}>"
      to: p.email
      #bcc: '<EMAIL>'
      subject: req.l10n req.setting.forgotPwdAutoReset?.subject
      text: req.l10n(req.setting.forgotPwdAutoReset.text) \
        + " " + newPass + " "
      html: "<p>" + req.l10n(req.setting.forgotPwdAutoReset.text)\
        + " <strong>" + newPass + "</strong><p>"
      sbj: req.l10n  req.setting.forgotPwdAutoReset.text
      isNeedValidate: true
      mailEngineListIndex:0
      eventType: 'ForgotPwdAutoResetEmail'
    #INCALL 'simpleMail',mail,(err,response)->
    sendMail.sendMail mail,(err,response)->
      # return resp.ckup 'forgotPwd',{err:"When Send Email:" + err} if err
      return error("When Send Email:" + err) if err
      # let user known send the email address
      resp.send {ok:1, msg:req.l10n('A new password was sent to your email.')}
      # resp.ckup 'authResult',{result:'A new password was sent to your email.'}

# '1.5/user/changePwd'
#TODO: use MSG_STRINGS
POST 'changePwd',(req,resp)->
  findError = (err)->
    # resp.ckup 'changePwd',{err:err}
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.NEED_LOGIN unless user
    return findError(req.l10n 'Need current password') unless (p = req.body) and p.oldPwd
    UserModel.isCurrentPassword {req,p:p.oldPwd},(match)->
      return findError(req.l10n 'Current password does not match') unless match
      return findError(req.l10n 'New password too short') unless p.pwd?.length >= 5
      return findError(req.l10n 'New passwords do not match') unless p.pwd is p.pwdR
      login_id = UserModel.getCurrentLoginIdOrEmail {req}
      UserModel.resetPassword {eml:login_id,pwd:p.pwd},(err)->
        return findError err if err
        resp.send {ok:1, url:'/1.5/settings'}
        # resp.redirect '/1.5/settings'


# '1.5/user/updPn'
POST 'updPn',(req,resp)-> # update PushNotification; Location information
  return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER, resp} unless pn = req.param 'pn'
  UserModel.appAuth {req,resp},(user)->
    return resp.send {e:'Do not update pn if switch user'} if req.session.get('realUser')
    # NOTE: should not block visitor, rmsrv5wk will send user pn
    # return resp.redirect '/1.5/user/login' unless user
    try
      await UserModel.updatePnLocation {req,user,pnloc:pn}
    catch err
      debug.error err
      return resp.send {e:err}
    return resp.send {r:'OK'}

# '1.5/user/language'
# 获取用户当前语言设置
POST 'language',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    if user.locale
      return resp.send {ok:1,lang: req.l10n(user.locale)}
    else
      return resp.send {ok:0}

###
# information to send
#  * UA, google service, iOS version
#  * App Core Version(getAppVer), server version?
 * User info: fn/ln/nm/eml/mbl/roles/lts/cip/ip/nm_zh/nm_en
 * loc if has
 * User langugage selections
 * pn token
 * test timestamp
###
# /1.5/user/sendErrReport
APP_VER = DEF 'APP_VER'
POST 'sendErrReport',(req,resp)->
  b = req.body
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    userInfo={}
    b.apsv = req.getAppVer()
    b.serverVersion = APP_VER
    msg=''
    userStr=''
    for fld in ['fn','ln','nm','eml','mbl','roles','lts','cip','ip','nm_zh','nm_en','loc']
      userInfo[fld]= user[fld] if (user[fld])?
    for u of userInfo
      userStr= userStr+"<p>#{u}:#{user[u]}</p>"
    b.user = userStr
    for p in ['ua','iosVersion','user','apsv','serverVersion',\
        'errorProblem','pn','ts','notification']
      msg= msg+"<p>#{p}:#{b[p]}</p>" if b[p]
    mail =
      engine: config.mailEngine?.mailEngine or 'sendmail'
      from: if Array.isArray user.eml then user.eml[0] else user.eml
      # to: '<EMAIL>'
      replyTo:'<<EMAIL>>'
      to: config.contact?.devEmails or '<EMAIL>'
      subject: 'Notification Diagnostic Report'
      text: msg
      html: msg
      eventType: 'ErrorReportEmail'
    sendMail.sendMail mail,(err,response)->
      debug.error err if err
      return resp.send {ok:1}

POST 'updateUserShowedTerm',(req,resp)->
  showedTerm = req.body?.showedTerm or false
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    devType = req.getDevType()
    UserModel.updateUserShowedTerm user._id,{showedTerm,devType},(ret)->
      resp.send {ok:1}

# 加密邮箱给到前端存入storage
POST 'encryptEml', (req,resp) ->
  UserModel.appAuth {req,resp},(user)->
    result = {ok:0,encryptedEml: null}
    return resp.send result unless user
    unless user.eml
      return resp.send result
    eml = UserModel.getEmail(user)
    encrypted = null
    try
      # 使用AES加密邮箱
      cipher = crypto.createCipheriv('aes-256-cbc', config.secretkey?.secret, config.secretkey?.iv)
      encrypted = cipher.update(eml, 'utf8', 'hex')
      encrypted += cipher.final('hex')
    catch err
      debug.error err, 'Encryption failed: email:',user.eml
      return resp.send result
    return resp.send {ok: 1,encryptedEml: encrypted}

# 给邮箱解密
POST 'decryptEml', (req,resp) ->
  decryptEmlLimiterFilter req,resp,()->
    result = {ok:0, decryptEml: null}
    unless (req.getDevType() is 'app')
      return resp.send result
    unless encryptedEml = req.body?.eml
      return resp.send result
    decrypted = null
    try
      decipher = crypto.createDecipheriv('aes-256-cbc', config.secretkey?.secret, config.secretkey?.iv)
      decrypted = decipher.update(encryptedEml, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
    catch err
      debug.error 'Decryption failed:', err
      return resp.send result
    return resp.send {ok: 1,decryptEml: decrypted}

APP '1.5'
APP 'stat',true
# propCommons = INCLUDE 'libapp.propCommons'
ProvAndCity = MODEL 'ProvAndCity'

groupByMonth = (v,ret2,chart)->
  # length = Object.keys(v).length
  # i = length - 150
  d = {}
  # d2 = {}
  for day,value of v
    # if i <= 0
    key = day.replace('_vc','')
    # .replace(/_/g,'')
    monthKey = key.substr(0,7)
    #2022_02_02_WESITE
    if /^\d+$/.test key.substr(8,2)
      typeKey = key.substr(10)
    else #2022_02_W_MBL no date
      typeKey = key.substr(7)
    # d2[key] = value
    d[monthKey+' '+typeKey] ?= 0
    d[monthKey+' '+typeKey] += value
    d[monthKey] ?= 0
    d[monthKey] += value
      # d[typeKey] ?=0
    # i--
  d2 = {}
  for k in Object.keys(d).sort()
    if /^20\d+\s?$/.test k
      chart.labels.push k
      chart.data.push d[k]
    else
      d2[k] = d[k]
  ret2.stat = d2
  return null

minifyRet = (ret)->
  ret2 = {stat:{}}
  chart = {labels:[],data:[]}
  #for k,v of ret
  for k in Object.keys(ret).sort()
    v = ret[k]
    if k is '_id'
    else if /_vc/.test k
      k2 = k.replace('_vc','') # migrate MBL_VC and MBL
      ret2[k2] = v
    else if k is 'stat'
      groupByMonth(v,ret2,chart)
    else
      if ret2[k]
        ret2[k] += v
      else
        ret2[k] = v
  [ret2,chart]

# '1.5/stat/realtorStats'
GET 'realtorStats',(req, resp)->
  # UserModel.appAuth {req,resp},(user)->
  unless req.isAllowed('productSales')
    return resp.ckup 'generalMessage',{msg:'Access denied'}
  uid = req.param 'id'
  UserStats.findOne {_id:uid},(err,ret)->
    if err
      debug.error err
      return resp.send {ok:0, err:err.toString()}
    [ret,chart] = minifyRet(ret)
    return resp.ckup 'userStats',{
      labels:chart.labels,
      data:chart.data,
      ret:ret,
      width:Math.max(400,chart.labels.length*50)
      stat:ret, uid: uid, isAdmin: req.isAllowed('userAdmin')
    }


VIEW 'userStats',->
  js '/js/Chart-3.6.2.min.js'
  div style:"padding:20px", ->
    div ->
      button class:'btn btn-primary', onclick:"openlink('/chat/list?inFrame=1&uid=#{@uid}')",->
        text 'Go to Chat'
      if @isAdmin
        button class:'btn btn-primary', onclick:"openlink('/sys/user?popup=1&id=#{@uid}')",style:"margin-left: 10px;",->
          text 'User Admin'
    div ->
      text 'WMBL,WEAMIL,WWEBSIT WWECHAT is from wesite page'
    pre ->
      text '
      example \n
        201905 CHAT : 26\n
        201905 CONTACT : 72\n
        201905 EMAIL : 6\n
        201905 MBL : 12\n
        201905 REALCHAT : 26\n
        W_EML：1,是weisite页面发的eml\N
        5月份用户打开contact button 72次，\n
        eml6次，电话12次，发消息26次，\n
        还有28次什么没做退出了。\n
        其中消息26次全都发送成功\n
        下方统计数据是总数\n
        realtor=contact+wesite \n
        vc = mbl + chat + email + wesite'
    for k,v of @stat
      if k is 'stat'
        div style:"padding:10px", ->
          text 'STAT:'
          for k1, v1 of v
            div ->
              text k1
              text " : "
              text v1
       else
         div ->
           text k
           text " : "
           text v
    canvas id:"myChart",width:@width, height:"240",->
  coffeejs {
    vars:{
      ret:@ret
      labels:@labels
      data:@data
    }
  },->
    data =
      labels: vars.labels,
      datasets: [{
        label: 'Total',
        backgroundColor: 'rgb(255, 99, 132)',
        borderColor: 'rgb(255, 99, 132)',
        data: vars.data #[0, 10, 5, 2, 20, 30, 45],
      }]
    config =
      type: 'line',
      data: data,
      options: {}
    ctx = document.getElementById('myChart')#.getContext('2d')
    myChart = new Chart(
      ctx,
      config)
    openlink = (url) ->
      window.RMSrv.openTBrowser(url)
    null

# '1.5/stat/realtorContact'
POST 'realtorContact',(req, resp)->
  error = (err)->
    return resp.send {ok:0, err:err}
  d = {}
  # tp in follow/contact/chat/email/mbl
  for i in ['tp','o','p','uid','role','src','saletp']
    d[i] = req.param i
    if d[i]
      d[i] = (d[i]+'').toUpperCase()
  # role in realtor/mortgage
  # o -> city original name
  # p -> prov short name, ON/AB/etc
  # uid -> realtor._id
  # debug.debug '+++++'
  # debug.debug d
  #wesite 页面没有role,收藏的经济第三方页面没有城市和省份
  return error('incomplete data') unless (d.tp and d.uid)

  if d.tp not in ['CONTACT','MBL','EMAIL','CHAT','WESITE','SHARE','WX','WEBSITE']
    return error('unsupport contact type')

  d.p = ProvAndCity.getProvAbbrName(d.p)

  #data from wesite use usercity as city and prov
  cityId = d.p+'-'+d.o if (d.o and d.p)
  userId = d.uid

  # date = helpers.dateFormat new Date(), 'YYYY_MM_DD'
  #for city, count by month but not day.
  dateMonth = helpers.dateFormat new Date(), 'YYYY_MM'
  inc = {}
  cityInc = {}

  #increase total
  if d.saletp
    cityInc = {"#{d.saletp}_#{d.tp}": 1}
  else
    cityInc = {"#{d.tp}": 1}

  #点击contact button 不算，点contact之后点击进入wesite算。
  #only add vc when real action happens
  #increase current month of real contact
  if d.tp isnt 'CONTACT'
    inc.vc = 1
    cityInc.vc = 1
    if d.saletp #real contact
      cityInc["stat.#{dateMonth}_#{d.saletp}"] =1
    else#real contact
      cityInc["stat.#{dateMonth}"]= 1

  #用户可以同时有多个role。agent点击contact时给role计数。
  # in realtor page, user click contact and then eml or phone to contact.\
  # to avoid count twice.only count role when click contact
  # in wesite page or properties detail top listing card, no contact button.
  # increase current month of contact only
  if (d.tp is 'CONTACT')
    if d.saletp
      cityInc["stat.#{dateMonth}_#{d.saletp}_#{d.tp}"] =1
    else
      cityInc["stat.#{dateMonth}_#{d.tp}"]= 1
    if d.role
      inc[d.role] = 1
      cityInc[d.role] = 1

  #for user stat summary
  if d.saletp
    inc["#{d.saletp}_#{d.tp}"] = 1
  else
    inc[d.tp] = 1
  #for user stat detail
  if d.src is 'WESITE'
    inc["stat.#{dateMonth}_W_#{d.tp}"] = 1
  else
    if d.saletp
      inc["stat.#{dateMonth}_#{d.saletp}_#{d.tp}"] = 1
    else
      inc["stat.#{dateMonth}_#{d.tp}"] = 1
  UserStats.updateOne { _id: userId },{$inc:inc},{upsert:true},(err)->
    if err then debug.error err
    return resp.send {ok:1} if not cityId
    UserCityStats.updateOne { _id: cityId },{$inc:cityInc},{upsert:true},(err)->
      if err then debug.error err
      resp.send {ok:1}
