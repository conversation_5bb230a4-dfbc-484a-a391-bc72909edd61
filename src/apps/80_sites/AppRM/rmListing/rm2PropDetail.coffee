#TO CONFIRM, STILL USED?
#promote
#base file for market and promote
# config = CONFIG()
helpers = INCLUDE 'lib.helpers'
# geo_coder = INCLUDE 'lib.geo_coder'
libproperties = INCLUDE 'libapp.properties'
Properties = MODEL 'Properties'
# UserListings = COLLECTION 'chome','user_listing'
# SysData = COLLECTION 'chome','sysdata'
debug = DEBUG()
# debug = (m)-> console.log m

setLang = DEF 'setLang'
# isAllowed = DEF '_access_allowed'
getWXConfig = DEF 'getWXConfig'
getExchangeRates = DEF 'getExchangeRates'
getExchangeNames = DEF 'getExchangeNames'
get_shared_to_from_share = DEF 'get_shared_to_from_share'
# updateListingStatus = DEF 'updateListingStatus'
UserModel = MODEL 'User'
ltpMap = DEF 'ltpMap'
libPropertyImage = INCLUDE 'libapp.propertyImage'
mapServer = INCLUDE 'lib.mapServer'
getGoogleAPILink = mapServer.getGoogleAPILink
propTranslate = INCLUDE 'libapp.propertiesTranslate'

gLAT = '37.869260'
gLNG = '-122.254811'

APP '1.5'
# Single Views, could be 3rd party, used in inAppBrowser
APP "SV", true
LZString = INCLUDE 'lib.lz-string'

LAYOUT ['SV', '_'], ->
  block 'customHeader', ->


SVSettings = {noAngular:1, noUserModal:1, noCordova:1, noRatchet:1, noRMApp:1}
#TODO: https://www.walkscore.com/score/3176-council-ring-rd-mississauga-on-canada
GET 'locallogic', (req, resp)->
  #force http
  # if req.getProtocol() is 'https'
  #   return resp.redirect req.fullUrl().replace /^https/,'http'
  lat = req.param 'lat' or gLAT
  lng = req.param 'lng' or gLNG
  if url = helpers.needRedirectUrl lng,req.url
    return resp.redirect url
  lang = req.locale()
  resp.ckup 'localLogic/index', {lat,lng,lang}, 'thirdPartyLayout'

GET 'streetview', (req,resp)->
  lat = req.param('lat') or gLAT
  lng = req.param('lng') or gLNG
  if url = helpers.needRedirectUrl lng,req.url
    return resp.redirect url
  emapiURL = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),cb:'initialize',v:3,src:'strtvw'})
  resp.ckup 'streetview', {lat:lat, lng:lng, emapiURL:emapiURL}, 'SV', SVSettings

GET 'googlemap', (req,resp)->
  lat = req.param('lat') or gLAT
  lng = req.param('lng') or gLNG
  if url = helpers.needRedirectUrl lng,req.url
    return resp.redirect url
  addr = req.param('addr') or ''
  zip = req.param('zip')
  emapiURL = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),cb:'initialize',v:3,src:'svmap'})
  resp.ckup 'googlemap', {lat:lat, lng:lng, addr:addr, zip:zip, emapiURL:emapiURL}, 'SV', SVSettings

# /1.5/SV/photoSwipe
GET 'photoSwipe', (req, resp)->
  findError = ->
    return resp.send "Error: not supported"
  data = req.param 'data',""
  return findError() unless data
  try
    data = JSON.parse(LZString.decompressFromEncodedURIComponent data)
  catch err
    # console.error  err.toString()
    return findError()
  # console.log data
  return findError() unless data?.urls and data.sizes
  if data.urls.length > data.sizes.length
    diff = data.urls.length - data.sizes.length
    for i in [0..diff]
      data.sizes.push data.sizes[0]
  SVSettings.noref = 1
  opt =
    data:data,
    isApp: req.getDevType() is 'app'
  opt.id = id if id = req.param 'id',''
  opt.untAddr = untAddr if untAddr = req.param 'untAddr',''
  opt.cmty = cmty if cmty = req.param 'cmty',''
  resp.ckup 'photoSwipe', opt, 'SV', SVSettings

VIEW "photoSwipe", ->
  css '/css/photoswipe.css'
  css '/css/default-skin.css'
  js '/js/photoswipe.min.js'
  js '/js/photoswipe-ui-default.min.js'
  if @req.getDevType() isnt 'app'
    css '/css/font-awesome.min.css'
  text """
  <!-- Root element of PhotoSwipe. Must have class pswp. -->
  <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">

      <!-- Background of PhotoSwipe.
           It's a separate element, as animating opacity is faster than rgba(). -->
      <div class="pswp__bg"></div>

      <!-- Slides wrapper with overflow:hidden. -->
      <div class="pswp__scroll-wrap">

          <!-- Container that holds slides. PhotoSwipe keeps only 3 slides in DOM to save memory. -->
          <div class="pswp__container">
              <!-- don't modify these 3 pswp__item elements, data is added later on -->
              <div class="pswp__item"></div>
              <div class="pswp__item"></div>
              <div class="pswp__item"></div>
          </div>

          <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
          <div class="pswp__ui pswp__ui--hidden">

              <div class="pswp__top-bar">

                  <!--  Controls are self-explanatory. Order can be changed. -->

                  <div class="pswp__counter top25"></div>
                  <span class='info top' id='infoTop'></span>

                  <button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>

                  <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                  <!-- element will get class pswp__preloader--active when preloader is running -->
                  <div class="pswp__preloader">
                      <div class="pswp__preloader__icn">
                        <div class="pswp__preloader__cut">
                          <div class="pswp__preloader__donut"></div>
                        </div>
                      </div>
                  </div>
              </div>

              <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                  <div class="pswp__share-tooltip"></div>
              </div>

              <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)">
              </button>

              <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)">
              </button>

              <div class="pswp__caption">
                  <div class="pswp__caption__center"></div>
              </div>

            </div>

          </div>
          <span class="fa fa-change-scroll" onclick='goToPhotoScroll(event)'></span>
          <div class="info bottom" id='infoBottom'></div>
  </div>
  <script type='text/javascript'>
    var pswpElement = document.querySelectorAll('.pswp')[0];
    var options = {
        history: false,
        focus: false,
        pinchToClose: false,
        closeOnScroll: false,
        closeOnVerticalDrag: false,

        showAnimationDuration: 0,
        hideAnimationDuration: 0
    };
  </script>
  """
  coffeejs {vars:{data:@data,id:@id,untAddr:@untAddr,cmty:@cmty}}, ->
    null
  text """
  <script type='text/javascript'>
    var data, finish, i, items, j, ref, ref1;
    data = vars.data;
    finish = data != null ? (ref = data.urls) != null ? ref.length : void 0 : void 0;
    window.items = [];
    if (data.index) {
      window.options.index = data.index;
    }
    for (i = j = 0, ref1 = finish; j <= ref1; i = j += 1) {
      if (data.urls[i] && data.sizes[i]) {
        items.push({
          src: "" + data.urls[i],
          w: data.sizes[i].split('x')[0],
          h: data.sizes[i].split('x')[1]
        });
      }
    }
    if (!items.length) {
      items = [
        {
          src: '/img/noPic.png',
          w: 640,
          h: 426
        }
      ];
    }
    if(vars.cmty){
      document.getElementById('infoBottom').innerHTML = vars.cmty;
    }
    if(vars.untAddr){
      document.getElementById('infoTop').innerHTML = vars.untAddr;
    }
    if(vars.id){
      var innerHTML = document.getElementById('infoTop').innerHTML
      document.getElementById('infoTop').innerHTML = innerHTML + ' ('+vars.id+')';
    }
    function goToPhotoScroll(event){
      event.stopPropagation();
      event.stopImmediatePropagation();
      var href = window.location.href;
      href = href.replace('photoSwipe','photoScroll');
      window.location.href = href;
    }
  </script>
  """
  text """
  <script type='text/javascript'>
  var gallery = new PhotoSwipe( pswpElement, PhotoSwipeUI_Default, items, options);
  gallery.init();
  </script>
  """


# /1.5/SV/photoScroll
GET 'photoScroll', (req, resp)->
  findError = ->
    return resp.send 'Error: not supported'
  data = req.param 'data',''
  opt = {
    isApp:req.getDevType() is 'app'
  }
  opt.id = id if id = req.param 'id',''
  opt.untAddr = untAddr if untAddr = req.param 'untAddr',''
  opt.cmty = cmty if cmty = req.param 'cmty',''
  return findError() unless data
  try
    data = JSON.parse(LZString.decompressFromEncodedURIComponent data)
  catch err
    # console.error  err.toString()
    return findError()
  return findError() unless data?.urls and data.sizes
  if data.urls.length > data.sizes.length
    diff = data.urls.length - data.sizes.length
    for i in [0..diff]
      data.sizes.push data.sizes[0]
  delete SVSettings.noRatchet
  delete SVSettings.noRMApp
  SVSettings.noref = 1
  num = data.urls?.length
  items=[]
  for i in [0...num]
    if data.urls[i] and data.sizes[i]
      items.push({
        src: '' + data.urls[i],
        w: data.sizes[i].split('x')[0],
        h: data.sizes[i].split('x')[1]
      })
  if not items.length
    items = [
      {
        src: '/img/noPic.png',
        w: 640,
        h: 426
      }
    ]
  opt.items = items
  opt.index = data.index
  resp.ckup 'properties/photoScroll',opt, 'SV', SVSettings

# data = vars.data
# finish = data?.urls?.length
# window.items = []
# # alert(JSON.stringify(data))
# window.options.index = data.index if data.index
# for i in [0..finish] by 1
#   if data.urls[i] and data.sizes[i]
#     items.push {
#       src:"#{data.urls[i]}",
#       w: data.sizes[i].split('x')[0], #with '100%' or 'auto' or undefined will not work
#       h: data.sizes[i].split('x')[1]
#     }
# unless items.length
#   items = [
#     {
#       src: '/img/noPic.png'
#       w: 640
#       h: 426
#     }
#   ]
#
VIEW "walkscore", ->
  text """
  <script type='text/javascript'>
  var widths = [window.outerWidth,window.innerWidth,document.documentElement.clientWidth,document.body.clientWidth];
  var widths2 = [];
  for(var i=0;i<widths.length;i++){
    if(widths[i]!=0){
      widths2.push(widths[i])
    }
  }
  var ws_wsid = 'ea1e0a5a3fd54afbaeedb23f9a359d34';
  var ws_address = "#{@addr}";
  var ws_width = Math.max.apply(null,widths2)-10+'';
  var ws_height = "" + (window.outerHeight || window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight);
  var ws_layout = 'vertical';
  var ws_commute = 'false';
  var ws_transit_score = 'true';
  var ws_map_modules = 'all';
  </script>
  <style type='text/css'>#ws-walkscore-tile{margin: 0 auto;position:relative;text-align:left}#ws-walkscore-tile *{float:none;}#ws-foottext, #ws-footer a,#ws-footer a:link{font:11px/14px Verdana,Arial,Helvetica,sans-serif;margin-right:6px;white-space:nowrap;padding:0;color:#000;font-weight:bold;text-decoration:none}#ws-footer a:hover{color:#000;text-decoration:none}#ws-footer a:active{color:#b14900}
  </style>
  <style >
  body{
    margin:0 auto;
    padding:0;
    width:100%;
    overflow-x:hidden;
  }
  a#qmark {
    display:none !important;
  }
  #ws-foottext {
    display:none !important;
  }
  #view-full-map {
    display:none !important;
  }
  </style>
  <div id='ws-walkscore-tile'><div id='ws-footer' style='position:absolute;'>
    <form id='ws-form'>
      <span id='ws-foottext' style='float: left;'>Score
        <a id='ws-a' href='https://www.redfin.com/how-walk-score-works' target='_blank'>Your Home</a>:
      </span>
    </form>
  </div>
  </div>
  <script type='text/javascript' src='#{@req.getProtocol()}://www.walkscore.com/tile/show-walkscore-tile.php'></script>
"""

VIEW "streetview", ->
  text """
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    #page-container {
      height: 100%;
    }
    #street-view {
      height: 100%;
    }
  </style>
  <div id="street-view"></div>
  <script>
    var panorama;
    var propLoc = {lat: #{@lat}, lng: #{@lng}};

    function initialize() {
      /*
      panorama = new google.maps.StreetViewPanorama(
        document.getElementById('street-view'),
        {
          position: propLoc,
          zoom: 1
        });
      */
      propLoc = new google.maps.LatLng(#{@lat}, #{@lng});
      var map = new google.maps.Map(document.getElementById('street-view'), {
        center: propLoc,
        zoom: 18,
        streetViewControl: false
      });
      var houseMarker = new google.maps.Marker({
          position: propLoc,
          map: map,
          icon: 'https://chart.apis.google.com/chart?chst=d_map_pin_icon&chld=home|E03131',
          title: '',
          optimized: false
      });
      panorama = map.getStreetView();

      map_panorama_service = new google.maps.StreetViewService();
      if (! map_panorama_service){
        return alert('Err no map_panorama_service');
      }
      // test point : 43.541039, -79.656266
      map_panorama_service.getPanoramaByLocation(propLoc, 100, function(d, status) {
         if(status !== google.maps.StreetViewStatus.OK){
           return alert('Error no streetview');
         }
         var opts;
         opts = {
           position: d.location.latLng,
           pov: {
             heading: google.maps.geometry.spherical.computeHeading(d.location.latLng, propLoc) || 0,
             pitch: 0,
             zoom: 1
           },
           visible: true,
           motionTracking:true,
           motionTrackingControl: true,
         };
         panorama.setOptions(opts);
      });
      // panorama.setPosition(propLoc);
      /*
      panorama.setPov(({
        heading: 265,
        pitch: 0
      }));
      */
      var toggle = panorama.getVisible();
      panorama.setVisible(true);
    }
  </script>
  <script async defer
    src="#{@emapiURL}">
  </script>

  """
#TODO : fix google map src
#key=YOUR_API_KEY&signed_in=true&
# pov: {heading: 165, pitch: 0},

VIEW 'googlemap', ->
  height = "100%"
  footer = ""
  # use lat lng
  addr = "#{@addr}"
  zip = "#{@zip}"
  if @req.getDevType() is 'app' and addr
    height = "calc(100% - 44px)"
    footer =
    "
    <div style=\"position:absolute; bottom:0; height:44px; width: 100%;  font-size: 17px;
      font-weight: 500;
      line-height: 42px;
      font-family: Arial, Helvetica, sans-serif;
      text-align: center;
      \">
      #{@addr}
    </div>"

  text """
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    #google-map {
      height: #{height};
    }
  </style>
  <div id="google-map"></div>
  #{footer}
  <script>
    function initialize() {
      var myLatLng = {lat: #{@lat}, lng: #{@lng}};

      // Create a map object and specify the DOM element for display.
      var map = new google.maps.Map(document.getElementById('google-map'), {
        center: myLatLng,
        scrollwheel: false,
        zoom: 15,
        streetViewControl: false
      });

      // Create a marker and set its position.
      var marker = new google.maps.Marker({
        map: map,
        position: myLatLng,
        // icon: 'http://chart.apis.google.com/chart?chst=d_map_pin_icon&chld=home|E03131',
        title: '',
        optimized: false
      });
    }

  </script>
  <script async defer
    src="#{@emapiURL}">
  </script>

  """

# 带有tab的图片预览
# /1.5/SV/photoScrollSwipe
GET 'photoScrollSwipe', (req, resp)->
  findError = ->
    return resp.send {ok: 0, err: 'Error: not supported'}
  # data = req.param 'data',''
  opt = {
    isApp:req.getDevType() is 'app'
  }
  # 需要房源_id当进入这个页面后，会根据_id去查找房源图片
  unless req.param 'id'
    return findError()
  opt.sid = sid if sid = req.param 'sid'
  opt.addr = decodeURIComponent(addr) if addr = req.param 'addr'
  # opt.cmty = cmty if cmty = req.param 'cmty'
  # opt.city = city if city = req.param 'city'
  opt.condoNm = decodeURIComponent(condoNm) if condoNm = req.param 'condoNm'
  opt.unt = decodeURIComponent(unt) if unt = req.param 'unt'
  opt.bdrm = decodeURIComponent(bdrm) if bdrm = req.param 'bdrm'
  opt.bthrm = decodeURIComponent(bthrm) if bthrm = req.param 'bthrm'
  opt.sqft = sqft if sqft = req.param 'sqft'
  opt.fce = fce if fce = req.param 'fce'
  return resp.ckup 'properties/photoSwipeScroll',opt,'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

APP '1.5'
# simple_translate_rmprop = DEF 'simple_translate_rmprop'

#used to get prop detail, however access from outside sigle page will query
#and check for status first, will not use this api
# used in list share result page, edit mylisting, rm1market
# TODO: fix this use model
POST 'rmprop/detail', (req, resp) ->
  rmid     = req.param 'rmid'
  isMl_num = req.param 'ml' #search by ml_num see if exists alreadt
  translate = req.param 't'
  # NOTE: need this to share to 58
  # TODO: see if can directly share to 58 instead of create mylisting
  # if isMl_num
  #   return resp.send {ok:0, err:req.l10n('No longer supported')}
  ret = {ok:0}
  # q = {id: rmid}
  unless req._ab
    req.setupL10n()
  # update = {$inc:{vc:1,vcd:1}}
  UserModel.appAuth {req,resp}, (user)->
    # if isMl_num
    #   q = {uid:user._id, sid:rmid}
    try
      prop = await Properties.findRMOneByID rmid
    catch err
      debug.error err
      ret.err = MSG_STRINGS.DB_ERROR
      return resp.send ret
    if (not prop)
      ret.err = 'Error: No listign found'
      return resp.send ret
    isAllow = (prop.ltp is 'assignment') and (req.isAllowed 'assignAdmin')
    unless ((user?.id is prop.uid) or isAllow) #if user 或者 楼花类型的assignAdmin权限的人可以修改他人写的
      delete prop['58']
      delete prop['market']
      delete prop['uhouzz']
      delete prop._id
      delete prop.uid
    ret.ok = 1
    if translate
      # translateRmPropDetailOpt = {_:req._,_ab:req._ab,l10n:req.l10n,locale:req.locale,headers:req.headers,cookies:req.cookies}
      prop = propTranslate.translate_rmprop_detail({
        locale: req.locale(),
        transDDF: req.getConfig('transDDF')
      }, prop) #simple_translate_rmprop(req,  prop)
    prop.pic.l = libPropertyImage.getRMListingsImages {sid:prop.sid,imgUrls:prop.pic.l}
    ret.prop = prop
    resp.send ret

# GET 'rmprop/detail/:rmid', (req,resp)->
#   return if req.redirectHTTPWhenCnDitu()
#   return process_mylisting_req({req,resp})
