BackendContact = MODEL 'BackendContact'
UserModel = MODEL 'User'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'
APP '1.5'
APP 'rmlisting', true

# /1.5/rmlisting/contactList
# 获取联系人信息 Only assignAdmin
POST 'contactList',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'assignAdmin'
      debug.error {msg:"saveContact:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await BackendContact.getContactList()
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    return resp.send {ok:1,result}

# /1.5/rmlisting/saveContact
# 保存/更新联系人信息 Only assignAdmin can save
POST 'saveContact',(req,resp)->
  if ((not req.body?.nm) or (not req.body?.mbl))
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'assignAdmin'
      debug.error {msg:"saveContact:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await BackendContact.saveContact req.body
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    # 不要把db结果直接返回，要返回前端需要的格式
    # type result = {value:{_id:xxx,nm:xxx,mbl:xxx}}
    return resp.send {ok:1,_id:result.value._id}

# /1.5/rmlisting/deleteContact
# 删除联系人信息 Only assignAdmin can delete
POST 'deleteContact',(req,resp)->
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'assignAdmin'
      debug.error {msg:"saveContact:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await BackendContact.deleteContact req.body.id
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    return resp.send {ok:1,n:result.n}