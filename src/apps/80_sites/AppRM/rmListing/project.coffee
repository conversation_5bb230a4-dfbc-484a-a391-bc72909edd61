# getBanners = DEF 'getBanners'
statHelper = INCLUDE 'libapp.stat_helper'
projectLib = INCLUDE 'libapp.project'
helpers = INCLUDE 'lib.helpers'
{genWebUrl} = INCLUDE 'libapp.project'
libUser = INCLUDE 'libapp.user'
libPropertyImage = INCLUDE 'libapp.propertyImage'

# bannerClicked = DEF 'bannerClicked'
Ads = COLLECTION 'chome','ads'
Projects = COLLECTION 'chome','pre_constrct'
Projects.createIndex {'url':1}, {unique:true,sparse:true}
Wecard = COLLECTION 'chome','wecard'

UserModel = MODEL 'User'
# updateListingStatus = DEF 'updateListingStatus'
# fnIsRealtor = DEF 'isRealtor'
sendOneMessage = DEF 'sendOneMessage'
checkVersionNShow = DEF 'checkVersionNShow'
createUniqueListingID = DEF 'createUniqueListingID'

PropertiesModel = MODEL 'Properties'
ProjectModel = MODEL 'Project'
WecardModel = MODEL 'Wecard'
ProvAndCity = MODEL 'ProvAndCity'
getConfig = DEF 'getConfig'
debug = DEBUG()
reloadBannerADs = DEF 'reloadBannerADs'
AdvertisementNewModel = MODEL 'AdvertisementNew'
StatModel = MODEL 'Stat'

getProjOpt = (req)->
  opt =
    hasViewed:(id)->req.hasViewed id
    devType:req.getDevType()
    nt:req.param 'nt'
    isAllowedMarketAdmin:req.isAllowed 'marketAdmin'
    isAllowedCreateProject:req.isAllowed 'createProject'
    hasRoleRealtor:req.hasRole 'realtor'
    isAllowedProductSales:req.isAllowed 'productSales'
    isSearchEngine:helpers.isSearchEngine req
    share:req.param('share')
    aid:req.param('aid')
    l10n:(a,b)->req.l10n a,b
    fullNameOrNickname:(u)->libUser.fullNameOrNickname(req.locale(),u)
    locale:req.locale()
    #extra
    # headers:req.headers
    # session:req.session
  opt
DEF 'getProjOpt',getProjOpt

getProjectLists = (param, cb)->
  try
    pList = await ProjectModel.findList param
  catch err
    return cb err
  projs = []
  rest = []
  # NOTE: shuffle spuser projects in list, req by @cloud
  # TODO: test by ads.pid for ad projects?
  # projs = ret.splice(0,idx)
  req = param.req
  if pList?.length
    for p in pList
      p.url = genWebUrl p,req.locale()
      if param.rmsrc
        separator = if p.url.includes('?') then '&' else '?'
        p.url += separator + 'rmsrc=' + param.rmsrc
      if p.top
        projs.push p
      else
        rest.push p
  projs = helpers.shuffle projs
  pList = projs.concat rest
  isApp = req?.getDevType() is 'app'
  try
    ret = await ProjectModel.injectProjSpUserAvtAndNm isApp, pList
  catch err
    return cb err
  # idx = 0
  return cb(null,ret) unless ret?.length
  isProjAdmin = UserModel.accessAllowed 'createProject'
  for p,idx in ret
    # if not isProjAdmin
    #   delete p.vcapp
    #   delete p.vcappc
    p.isProj = true
    projectLib.setupUserFav {hasRoleRealtor:req.hasRole 'realtor'},param.user,p
    p.img ?= {}
    p.img.l = libPropertyImage.replaceRM2REImagePathForArray p.img.l if p.img?.l
    if Array.isArray p.favUsr
      p.favU = p.favUsr.length if isProjAdmin
      delete p.favUsr
    if Array.isArray p.favRealtor
      p.favR = p.favRealtor.length if isProjAdmin
      delete p.favRealtor

      # projs.push translateProjectDetails req, null, p
  cb(null,ret)

DEF 'getProjectLists', getProjectLists

# getTopProjects = (cb) ->
#   ProjectModel.findList {top:{$ne:null}},{fields:PROJ_LIST_FIELDS,sort:{top:-1}},(err,projects) ->
#   # Projects.findToArray {top:{$ne:null}},{fields:PROJ_LIST_FIELDS,sort:{top:-1}},(err, projects) ->
#     cb(err, projects)
# DEF "getTopProjects", getTopProjects

filterHtml = (html)->
  unless html
    return ''
  #find a tag replece href
  re = new RegExp("<a([^>]* )href=\"([^\"]+)\"", "g")
  result = html.replace(re, "<a$1href=\"#\" onclick=\"showInBrowser('$2')\"")
  result

# injectWecardDetail = (opt, proj, cb)->
#   unless proj.wepage
#     return cb null, proj
#   if /^\w{24}/.test proj.wepage
#     q = {_id:proj.wepage}
#   if /^:/.test proj.wepage
#     locale = opt.locale()
#     editor = proj.wepage
#     editorLocale = editor+':'+locale
#     q = {
#       tp:{$in:['evtad','topic']}
#       'meta.editor':{$in:[editor, editorLocale]}
#     }
#   unless q
#     return cb null, proj
#   Wecard.findToArray q, (err,list=[])->
#     if err or not list.length
#       console.error err
#       return cb null,proj
#     for i in list
#       ret = i if i.meta.editor is editor
#       if i.meta.editor is editorLocale
#         ret = i
#         break
#     proj.html = filterHtml(ret?.seq[0]?.m)
#     cb null, proj


LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

APP '1.5'
APP 'prop', true
APP 'projects', true

#1.5/prop/projects/detail
GET 'detail',(req,resp)->
  #get project title and detail
  id = req.param 'id'
  maxImageSize = getConfig('maxImageSize')
  resp.ckup 'proj-detail-inapp', { id,maxImageSize }, '_', { noAngular: 1, noUserModal: 1 }

VIEW 'proj-detail-inapp', ->
  div id: 'vueBody', ->
    text "<proj-detail-page></proj-detail-page>"
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    }, ->
    null
  js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/projDetailPage.js'

#1.5/prop/projects/cities
# page for project cities select in map
GET 'cities',(req,resp)->
  resp.ckup 'proj-cities',{},'_',{noUserModal:1,wxcfg:{},noAppCss:1,noRatchetJs:1,noAngular:1}

VIEW 'proj-cities',->
  div id:'vueBody',->
    text """<proj-cities-modal></proj-cities-modal>"""
  # js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/projCitiesModal.js'


#1.5/prop/projects
GET '', (req, resp) ->
  maxImageSize = getConfig('maxImageSize')
  done = () ->
    resp.ckup 'projects-list', { maxImageSize }, '_', { noref: 1 }
  if req.param 'web'
    return done()
  checkVersionNShow req, resp, '5.6.1', ->
    # UserModel.appAuth {req,resp},(user)->
    done()
#1.5/prop/projects/edit
GET 'edit', (req, resp) ->
  UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->
    unless req.isAllowed 'createProject'
      return resp.redirect '/1.5/index'
    if req.redirectHTTP()
      return
    maxImageSize = getConfig('maxImageSize')
    resp.ckup 'project-edit', { maxImageSize }

error = (resp, err, ec, url)->
  resp.send {ok:0, err:err, url:url, ec:ec}

projCities = null
PROJECT_CREATE = 'create'
#admin create or update project
#1.5/prop/projects/manage
POST 'manage',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
    return error(resp,MSG_STRINGS.NO_AUTHED) unless req.isAllowed 'createProject'
    _id = req.param '_id'
    body = req.body or {}
    return error(resp,MSG_STRINGS.BAD_PARAMETER) unless ProjectModel.checkRequiredFields body
    isCreateMode = PROJECT_CREATE is req.param('mode')
    projCities = null
    if isCreateMode
      return createUniqueListingID (err,id)->
        ProjectModel.createProject {id,user,body},(err,ret)->
          return error(resp,err.toString()) if err
          return resp.send {ok:1, _id:ret._id}
    ProjectModel.updateProject {id:_id,user,body},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, _id:ret._id}


sendPnMessages = (opt, userList, cb)->
  getUsersPn userList, (err,pnList)->
    for u in pnList
      sendOneMessage({to:u, msg:opt.msg, url:opt.url})
  cb null, 'done' if cb

#simple implementation, TODO: use stream
sendPnAllUsers = (opt)->
  UserModel.getAllPn (err, userList)->
    for u in userList
      # console.log u.pn+'+++'
      sendOneMessage({to:u, msg:opt.msg, url:opt.url})

#send cust pn msg from editing page
sendCustPnMsg = (opt, cb)->
  _id = opt._id
  ProjectModel.findDetail {_id},(err,proj)->
    if err
      console.error err
      return cb err
    proj ?= {}
    if opt.isToAll
      sendPnAllUsers(opt)
      return cb null, 'sent'
    if opt.isToUser and Array.isArray proj.favUsr
      sendPnMessages(opt, proj.favUsr)
    if opt.isToRealtor and Array.isArray proj.favRealtor
      sendPnMessages(opt, proj.favRealtor)
    cb null,'sent'

#generate msg for new floorplan
generateFloorplanUpdateMessage = (proj, plan)->
  if plan.fnm
    msg = 'New wants by '+plan.fnm
  else
    msg = 'New floorplan '+(plan.id or 'No id')
  msg += ' for '
  msg += proj.nm+' has been added.'
  return msg

#after create a new floorplan, auto notify followed realtor
sysAutoSendFloorPlanUpdate = (opt, cb)->
  proj = opt.proj
  floorplan = opt.plan
  unless proj?.favRealtor?.length
    return cb null,'done' if cb
  opt = {}
  opt.msg = generateFloorplanUpdateMessage(proj,floorplan)
  opt.url = '/1.5/prop/projects?id='+proj._id if proj._id
  sendPnMessages opt, proj.favRealtor

#from user id list get pns
getUsersPn = (userList, cb)->
  userList ?= []
  # fields = {
  #   pn:1,
  #   fn:1,
  #   ln:1,
  #   eml:1,
  #   mbl:1
  # }
  #todo sendPnMessages 中用不到，是否需要除pn以外的参数
  UserModel.getListByIds userList, {pn:1},(err, ret)->
    if err
      console.error err
      return cb err
    cb null, ret

#admin send pn in proj manage/edit
#1.5/prop/projects/sendMessage
POST 'sendMessage',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'marketAdmin'
    _id = req.param '_id'
    return error(resp,'Need _id') unless _id
    opt = {
      _id:_id,
      msg:req.param 'msg'
      isToUser:req.param 'isToUser'
      isToRealtor:req.param 'isToRealtor'
      isToAll:req.param 'isToAll'
    }
    opt.url = if url = req.param('url') then url else '/1.5/prop/projects?id='+_id
    sendCustPnMsg opt, (err,ret)->
      if err
        console.log err
        return error(resp, err.toString())
      resp.send {ok:1, msg:'sent'}

#1.5/prop/projects/generateAd
POST 'generateAd',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'marketAdmin'
    _id = req.param '_id'
    return error(resp,'Need _id') unless _id
    return error(resp,'Need tgt') unless tgt = req.param('tgt')
    try
      AdvertisementNewModel.createProjAd {req,tgt,_id,LANGUAGE_LIST,uid:user._id}
    catch err
      console.error err
      return error resp,err.toString()
    reloadBannerADs()
    return resp.send {ok:1, msg:'插入成功，进入首页->更多->Advertisement Manage编辑'}

#1.5/prop/projects/fav
#admin top or rcmd proj
POST 'fav',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    id = req.param '_id'
    return error(resp,'Need _id') unless id
    hasRoleRealtor = req.hasRole 'realtor'
    fav = req.param 'fav'
    ProjectModel.addUserFavProject {hasRoleRealtor,fav,user,id},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, msg:req.l10n(ret.msg,'favorite')}


#1.5/prop/projects/myFav
POST 'myFav',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    hasRoleRealtor = req.hasRole 'realtor'
    page = req.param 'page'
    hasWechat = if params?.hasWechat? then params.hasWechat else true
    if user?.hasWechat?
      hasWechat = hasWechat and user.hasWechat
    ProjectModel.findUserFavProject {hasRoleRealtor,user,page},(err,[pList,count])->
      return error(resp,err.toString()) if err
      projs = []
      rest = []
      if pList?.length
        for p in pList
          p.img ?= {}
          p.img.l = libPropertyImage.replaceRM2REImagePathForArray p.img.l if p.img?.l
          p.url = genWebUrl p,req.locale()
          p.fav = true
          if p.top
            projs.push p
          else
            rest.push p
      projs = helpers.shuffle projs
      pList = projs.concat rest
      if not hasWechat
        return resp.send {ok:1, items:pList,cnt:count}
      isApp = req?.getDevType() is 'app'
      try
        ret = await ProjectModel.injectProjSpUserAvtAndNm isApp, pList
      catch err
        return error(resp,err.toString())
      return resp.send {ok:1, items:ret,cnt:count}

#1.5/prop/projects/edm
POST 'edm',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'edmAdmin'
    id = req.param '_id'
    return error(resp,'Need _id') unless id
    del = req.body.del
    ProjectModel.sendEdm {id,del},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, edm:ret.edmts}


#1.5/prop/projects/top
#admin top or rcmd proj
POST 'top',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'marketAdmin'
    id = req.param '_id'
    return error(resp,'Need _id') unless id
    top = req.param('top')
    rcmd = req.param('rcmd')
    ProjectModel.topOrRecommandProject {id,top,rcmd},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, msg:ret.msg}

#user post comment
#1.5/prop/projects/comment
POST 'comment',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Need Realtor') unless req.hasRole 'realtor'
    # return error(resp,'Need VIP plus') unless req.isAllowed 'vipPlus'
    return error(resp,'Need project id') unless id = req.param '_id'
    body = req?.body or {}
    ProjectModel.upsertComment {body,id,user},(err,ret)->
      return error(resp,err.toString()) if err
      proj = ret?.value or {}
      plan = {fnm:libUser.fullNameOrNickname(req.locale(),user)}
      opt = {
        proj:proj,
        plan:plan
      }
      sysAutoSendFloorPlanUpdate(opt)
      return resp.send {ok:1}

#remove proj
POST 'remove',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Need Admin') unless req.isAllowed 'marketAdmin'
    return error(resp,'Need project id') unless id  = req.param '_id'
    ProjectModel.removeProject {id},(err, ret)->
      projCities = null
      return error(resp,err.toString()) if err
      return resp.send {ok:1}

#delete comment
POST 'delete',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    id = req.param '_id'
    isMine = req.param 'isMine'
    isMarketAdmin = req.isAllowed 'marketAdmin'
    uid = req.param 'uid'
    msg = req.param('msg')
    undelete = req.param 'undelete'
    ProjectModel.removeComment {id,isMine,isMarketAdmin,uid,msg,undelete,user},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1,msg:req.l10n(ret.msg)}

#admin delete floorplan set del:del
POST 'deleteFloorplan',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Need Admin') unless req.isAllowed 'marketAdmin'
    id = req.param '_id'
    uid = req.param 'uid'
    msg = req.param 'msg'
    undelete = req.param 'undelete'
    return error(resp,'Need project id') unless id and uid
    idx = parseInt(req.body.idx)
    if isNaN idx
      return resp.send {ok:0, err:req.l10n('Need Index')}
    ProjectModel.deleteFloorplan {id,idx,undelete,uid,msg},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1}

#inc floorplan vc
POST 'floorplanViewed',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    # return error(resp,'Need login') unless user
    # return error(resp,'Need Admin') unless req.isAllowed 'marketAdmin'
    id = req.param '_id'
    return error(resp,'Need project id') unless id
    idx = parseInt(req.body.idx)
    if isNaN idx
      return resp.send {ok:0, err:req.l10n('Need Index')}
    ProjectModel.incFloorplanViewed {id,idx},(err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1}

#edit or create floorplan
POST 'floorplan',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Need VIP plus') unless req.isAllowed 'vipPlus'
    id = req.param '_id'
    return error(resp,'Need project id') unless id
    ProjectModel.upsertFloorplan {req,id,user},(err,ret)->
      return error(resp,err.toString()) if err
      #TODO: push notify followed realtors
      proj = ret?.value or {}
      plan = ret?.plan or {}
      if (req.body.mode isnt 'edit') and Array.isArray(proj.favRealtor) and proj.favRealtor.length
        opt = {
          proj:proj,
          plan:plan
        }
        sysAutoSendFloorPlanUpdate(opt)
      return resp.send {ok:1}

# if a[b] and x=a[b].c
#   console.log d
recordUserProjQuery = (req, user, opt)->
  temp = {}
  if opt.city
    temp.city = opt.city
    temp.cityName = opt.cityName
  if opt.prov
    temp.prov = ProvAndCity.getProvAbbrName opt.prov
    temp.provName = opt.provName
  if opt.mode
    temp.mode = opt.mode
    temp.modeName = opt.modeName
  if opt.tp1
    temp.tp1 = opt.tp1
    temp.tp1Name = opt.tp1Name
  if opt.prov and not opt.city
    temp.isProv = true
  req.session.set 'projLastQuery',temp

#list page get list
POST 'projectList', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    # return error(resp,'Need login') unless user
    lang = req.locale()
    body = req.body
    mode = body.mode
    nm   = body.nm
    city = body.city
    prov = body.prov
    prov = ProvAndCity.getProvAbbrName body.prov
    tp1  = body.tp1
    # apply user saved settings in index
    if user
      recordUserProjQuery(req, user, body)
    params = {req, user, mode, nm, city, prov, tp1}
    getProjectLists params, (err, projList)->
      if err
        console.error err
        req.logger?.error err
        return error resp,err
      resp.send {ok:1, l:projList}

#get all existed proj cities
POST 'projCities', (req, resp)->
  done = (cityList=[])->
    ret = []
    for c in cityList
      cID = c._id
      ret.push {
        o:cID.city,
        n:req.l10n(cID.city),
        p:req.l10n(ProvAndCity.getProvFullName cID.prov),
        prov:cID.prov,
        lat:c.lat,
        lng:c.lng
      }
    resp.send {ok:1, l:ret}
  # TODO: remove this cache somehow
  if projCities
    return done(projCities)
  UserModel.appAuth {req,resp}, (user)->
    ProjectModel.findCities (err,cityList)->
      if err
        console.error err
        return error(err)
      cityList ?= []
      cityList.sort (a,b)->
        return b.count - a.count
      projCities = cityList
      done(cityList)

#get proj detail and inject detail
# /1.5/prop/projects/detail
POST 'detail', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    # unless req.param 'share'
    #   return error(resp,'Need login',2,'/1.5/user/login') unless user
    return error(resp,'no id') unless _id = req.param '_id'
    #TODO: update vc and return error cooresponding
    if (locale = req.body?.locale) and (locale in LANGUAGE_LIST)
      req.setLocale locale
    req.setupL10n() unless req._ab and req._
    opt = getProjOpt req
    opt.id = _id
    opt.fromApp = true
    rmsrc = req.param('rmsrc') || 'app'
    try
      proj = await ProjectModel.getProjectDetails opt, user, PropertiesModel.updateListingStatus
    catch err
      debug.error err
      return error(resp,req.l10n(MSG_STRINGS.DB_ERROR))
    unless proj
      return error(resp, req.l10n(MSG_STRINGS.NOT_FOUND), 2, '/1.5/prop/error?id='+_id)
    try
      await StatModel.updateStat {tp:'proj',id:proj._id,stat:rmsrc}
    catch err
      debug.error err
      debug.error "StatModel.updateStat error: tp:proj,id:#{proj._id},stat:#{rmsrc}"
    return resp.send {ok:1, proj:proj} unless user
    resp.send {ok:1, proj:proj}
    # NOTE: no need to wait for history update
    UserModel.logProjectHistory user._id, {projId:_id}
    return
      # resp.send {ok:1, proj:proj}

# proj wecard detail
# /1.5/prop/projects/wecardDetail
POST 'wecardDetail',(req, resp) ->
  return error(resp,MSG_STRINGS.BAD_PARAMETER) unless req.body?.wepage
  UserModel.appAuth {req,resp}, (user)->
    locale = req.body?.locale or req.locale()
    try
      result = await WecardModel.injectWecardDetail locale, req.body.wepage
    catch err
      debug.error err,' wecardDetail:',req.body
      return resp.send {ok:0,result:''}
    if result
      return resp.send {ok:1,result}
    return resp.send {ok:1,result:''}

VIEW 'project-edit',->
  div id:'vueBody',->
    text """<app-proj-manage></app-proj-manage>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appProjManage.js'

VIEW 'projects-list',->
  div id:'vueBody',->
    text """<app-project-list></app-project-list>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appProjectList.js'
