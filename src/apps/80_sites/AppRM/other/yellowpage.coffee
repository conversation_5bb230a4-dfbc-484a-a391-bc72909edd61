helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'

debug = DEBUG()
# config = CONFIG()
SysNotifyModel = MODEL 'SysNotify'
{respError} = INCLUDE 'libapp.responseHelper'
UserModel = MODEL 'User'
allowed_category = DEF 'allowed_category'
allowed_sub = DEF 'allowed_sub'
getYellowpageCategory = libUser.getYellowpageCategory

# User.createIndex {'lts':1}

APP '1.5'
APP 'yellowpage',true

# GET '', (req,resp)->
#   req.setLocale lang if lang = req.param 'lang'
#   return resp.ckup "yellowpage-home",{},'_',{noAngular:true, noref: true}

css = [
  '/sprite.min.css'
  '/smb.min.css'
]

GET '', (req, resp) ->
  req.setLocale lang if lang = req.param 'lang'
  js = ['/js/vue3.min.js', '/js/yellowpage/indexPage.js']
  opt = {
    d: req.param('d') or '/1.5/index'
    js: js
    css: css
    }
  addCss = ['/apps/yellowpage.css']
  opt.css = opt.css.concat(addCss)
  resp.ckup 'yellowpage/index', opt, '_', { noAngular: true, noUserModal: true, noRatchetJs: true }

GET 'searchProvider', (req, resp) ->
  req.setLocale lang if lang = req.param 'lang'
  js = ['/js/vue3.min.js', '/js/yellowpage/searchPage.js']
  opt = {
    d: req.param('d') or '/1.5/index'
    js: js
    css: css
    }
  addCss = ['/apps/searchProvider.css']
  opt.css = opt.css.concat(addCss)
  resp.ckup 'yellowpage/searchProvider', opt, '_', { noAngular: true, noUserModal: true, noRatchetJs: true }

gTransCategoryList = {}
POST 'category.json', (req, resp) ->
  lang = req.locale()
  if gTransCategoryList[lang]
    return resp.send {ok:1, categories:gTransCategoryList[lang]}
  categoryList = getYellowpageCategory()
  categories = helpers.deepCopyObject categoryList,4
  if lang isnt 'en'
    for category in categories
      category.t = req.l10n category.t, 'yellowpage'
      for sub in category.sub
        sub.val = req.l10n sub.val, 'yellowpage'
  gTransCategoryList[lang] = categories
  return resp.send {ok:1, categories:categories}

GET 'category', (req,resp)->
  return resp.ckup "yellowpage-category",{},'_',{noAngular:true, noref: true}

POST 'addCategory', (req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless req.body.categories
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless sas = req.body.sas

  UserModel.appAuth {req,resp}, (user)->
    categories = req.body?.categories
    UserModel.updateCategory {req,categories,sas},(err)->
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      resp.send {ok:1,message:req.l10n('Saved')}


# calProviderPoint = (user,city)->
#   user.p = 0
#   weight = {avt:5,nm_zh:2,nm_en:2,cpny:2,cpny_en:2,cpny_zh:2,cpny_pstn:1,itr:1}
#   for key of weight
#     if user[key]
#       user.p = user.p + weight[key]
#   if user.roles.indexOf('vip_alliance')>-1
#     user.p = user.p + 100
#     user.rcmd = true
#   if user.category?.length>1
#     user.p = user.p-3
#   shr = user.shr || 0
#   shr = 100 if user.shr > 100
#   user.p = user.p + shr * 0.1
#   findCity = user.sas.find((c)->
#    return c.city == city
#   )
#   if (findCity)
#     user.p = user.p + 10

# compareUser = (u1, u2) ->
#   return u2.p - u1.p

#1.5/yellowpage/providers
POST 'providers', (req,resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless tp = req.body.tp
  body = req.body or {}
  city = body.city
  limit = body.limit or 20
  UserModel.getCategoryList {tp,body,page:body.page,limit},(err,users,count)->
    if err
      clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
      SysNotifyModel.notifyAdmin err?.toString()+clientMsg
      return respError {
        clientMsg,
        resp,
        sysErr:err}
    users?=[]
    # for user in users
    #   calProviderPoint user,city
    # users.sort(compareUser)
    totalPages =  Math.ceil(count / limit)
    UserModel.appAuth {req,resp}, (user)->
      return resp.send {ok:1,resultList:UserModel.formatList(req, users),totalPages} unless user
      UserModel.findProfileById user._id,{projection:{favProviders:1}}, (err, profile) ->
        if err
          clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
          SysNotifyModel.notifyAdmin err?.toString()+clientMsg
          return respError {
            clientMsg,
            resp,
            sysErr:err}
        favProviders = profile?.favProviders
        resp.send {ok:1,resultList:UserModel.formatList(req, users, favProviders),totalPages}


# GET 'providerPage',(req,resp)->
#   req.setLocale lang if lang = req.param 'lang'
#   tp = req.param 'tp'
#   return resp.ckup 'provider-page',{},'_',{noAngular:true, noref: true}


POST 'search',(req,resp)->
  tp = req.param('tp')
  unless name = req.param('name')
    return resp.send {err: req.l10n("Please input email or phone number or the name"), success:false} # TODO: front end shall deal with this
  UserModel.getFavoriteProvidersByName name,(err,list)->
    if err
      clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
      SysNotifyModel.notifyAdmin err?.toString()+clientMsg
      return respError {
        clientMsg,
        resp,
        sysErr:err}
    list?=[]
    UserModel.appAuth {req,resp}, (user)->
      return resp.send {ok:1,resultList:UserModel.formatList(req, list)} unless user
      UserModel.findProfileById user._id,{projection:{favProviders:1}}, (err, profile) ->
        if err
          clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
          SysNotifyModel.notifyAdmin err?.toString()+clientMsg
          return respError {
            clientMsg,
            resp,
            sysErr:err}
        favProviders = profile?.favProviders
        resp.send {ok:1,resultList:UserModel.formatList(req, list, favProviders)}

POST 'fav', (req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    uid = req.body.uid
    fav = req.body.fav
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless uid and typeof fav is 'boolean'
    # TODO: same thing as in crm, use UserModel['addFavorite' ...] instead
    if fav is true
      # fn = UserModel.addFavorite
      fn = 'addFavorite'
      msg = 'Saved'
    else
      msg = 'Unsaved'
      # fn = UserModel.deleteFavorite
      fn = 'deleteFavorite'
    UserModel[fn] user._id,{id:uid,tp:'Providers'},(err)->
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      return resp.send {ok:1,msg:req.l10n(msg,'favorite')}


GET 'myfav', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless user or req.param 'faved'
    # return resp.ckup 'myfav-page',{},'_',{noAngular:true, noref: true}
    url = '/1.5/saves/agent'
    if d = req.param 'd'
      url = url + d
    return resp.redirect url

POST 'myfav', (req, resp) ->
  faved = req.param 'faved'
  page = req.param 'page'
  limit = req.param 'limit'
  UserModel.getMyFavoriteProviders {req,resp,faved,page,limit},(err,list)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    list?=[]
    return resp.send {ok:1, resultList: []} unless list?.length
    return resp.send {ok:1,resultList:UserModel.formatList(req, list, [], true)}



VIEW "yellowpage-category",->
  div id: "appServicesCategory", ->
    text """<app-yellowpage-category></app-yellowpage-category>"""
  js '/js/entry/commons.js'
  js '/js/entry/appYellowpageCategory.js'

