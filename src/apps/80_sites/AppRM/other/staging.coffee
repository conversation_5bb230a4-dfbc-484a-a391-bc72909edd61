# Projects = COLLECTION 'chome', 'pre_constrct'
User = COLLECTION 'chome', 'user'
StagingImg = COLLECTION 'chome', 'staging_img'

UserModel = MODEL 'User'
checkVersionNShow = DEF 'checkVersionNShow'

APP '1.5'
APP 'staging', true

GET '', (req, resp)->
  done = ()->
    resp.ckup 'staging-intro',{},'_',{noref:1}
  if req.param 'web'
    return done()
  checkVersionNShow req,resp,'5.8.3',->
    # UserModel.appAuth {req,resp},(user)->
    done()

#admin edit staging photos
GET 'edit', (req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'createProject'
      return resp.redirect '/1.5/index'
    resp.ckup 'project-edit'

generateSetValue = (req,user,isCreateMode,cb)->
  set = {
    ts:new Date()
    uid:user._id
  }
  for i in ['style','space','rcmd','pho','mt','catalog','author']
    set[i] = req.body[i]
  cb set

error = (resp,err)->
  resp.send {ok:0, err:err}

#admin create or update staging
POST 'manage',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'createProject'
    _id = req.param '_id'
    req.body?={}
    q = {_id:_id}
    isCreateMode = if req.param('mode') is 'create' then true else false
    generateSetValue req, user, isCreateMode, (set)->
      # console.log set
      # console.log '++++'
      # return resp.send {ok:1}
      if isCreateMode
        StagingImg.insertOne set, (err, ret)->
          if err
            console.error err
            return error(resp,err.toString())
          return resp.send {ok:1, _id:set._id, msg:'inserted'}
      else
        set.mt = new Date()
        StagingImg.updateOne q, {$set:set}, {upsert:false}, (err,ret)->
          return error(resp,err.toString()) if err
          return resp.send {ok:1,msg:'updated'}

#admin remove imgset
POST 'remove',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Need Admin') unless req.isAllowed 'admin'
    _id = req.param '_id'
    return error(resp,'Need project id') unless _id
    StagingImg.remove {_id:_id}, (err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, msg:'removed'}

#list page get list
POST 'imgSet', (req, resp)->
  req.body ?= {}
  b = req.body
  UserModel.appAuth {req,resp}, (user)->
    # return error(resp,'Need login') unless user
    space = b.space
    catalog = b.catalog or 'Case'
    q = {
      space:space,
      catalog:catalog
    }
    if space is 'All'
      delete q.space
    StagingImg.findToArray q, (err,sets)->
      if err
        console.error resp,err
        return resp.send {ok:0,err:err}
      resp.send {ok:1, l:sets}


# VIEW 'staging-edit',->
#   text """<app-proj-manage></app-proj-manage>"""
#   js '/js/lz-string.min.js'
#   js '/js/entry/commons.js'
#   js '/js/entry/appProjManage.js'

VIEW 'staging-intro',->
  div id:'appStaging',->
    text """<app-staging></app-staging>"""
  js '/web/packs/commons.js'
  js '/web/packs/appStaging.js'
  js '/js/lz-string.min.js'
  css '/css/apps/forum.css'
