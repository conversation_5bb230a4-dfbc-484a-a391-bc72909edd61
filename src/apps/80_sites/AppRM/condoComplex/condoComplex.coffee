CondoComplex = MODEL 'CondoComplex'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
{respError} = INCLUDE 'libapp.responseHelper'
{unifyAddress} = INCLUDE 'lib.helpers_string'
cityHelper = INCLUDE 'lib.cityHelper'
propAddress = INCLUDE 'lib.propAddress'
debug = DEBUG()

APP 'condoComplex'

# /condoComplex/update
# 关联两个uaddr
POST 'update',(req,resp)->
  unless body = req.body
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
  unless body?.uaddrCur
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN)} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"condoComplex/add:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await CondoComplex.updateUaddr {uaddrCur:body.uaddrCur,uaddrSel:body.uaddrSel,tagArry:body.tags}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    return resp.send {ok:1,result}

# /condoComplex/delete
# 删除两个笔记之间的关联
POST 'delete',(req,resp)->
  unless uaddrSel = req.body?.uaddrSel
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"condoComplex/add:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await CondoComplex.deleteUaddr uaddrSel
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    return resp.send {ok:1,result}

# /condoComplex/nearbyUaddr
# 如果有uaddr查找已经关联的，不足10个，根据经纬度查找附近200m地址进行排序后补全剩余的; 只有noteAdmin有权限
# @param {Number} lat - A necessary param
# @param {Number} lng - A necessary param
# @param {String} uaddr
# @return {ok:1,result:{nearByCondos,tags}} - {要展示的10个地址，之前选择的tag}
POST 'nearbyUaddr',(req,resp)->
  lat = req.body?.lat
  lng = req.body?.lng
  uaddr = req.body?.uaddr
  addr = req.body?.addr
  unless (lat and lng)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"condoComplex/nearbyUaddr:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    unless uaddr #根据已知信息拼接Uaddr，用于查询
      info =
        addr: addr
        prov: req.body?.prov
        zip: req.body?.zip
        loc: req.body?.loc
        city: req.body?.city
        cnty: req.body?.cnty
      if info.city
        isSubCity = cityHelper.isSubCityAndGetParentCity info.city
        if isSubCity.result
          info.city = isSubCity.city
      info = propAddress.formatProvAndCity info
      unifyUaddr = unifyAddress info
      try
        uaddrAndAddr = await CondoComplex.findUaddrBasedOnAddr unifyUaddr
      catch err
        debug.error err
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
      if uaddrAndAddr
        lat  = uaddrAndAddr.lat or lat
        lng = uaddrAndAddr.lng or lng
        uaddr = uaddrAndAddr._id
      else
        return respError {clientMsg:req.l10n('Not Found Uaddr'), resp}
    try
      {nearByCondos,selUaddr} = await CondoComplex.getNearbyUaddr {lat,lng,uaddr,addr}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    tags = selUaddr?.tag or [] #if selUaddr?.tag then selUaddr.tag else []
    for item in nearByCondos
      if selUaddr?.uaddr and (item._id in selUaddr.uaddr)
        item.isSel = true
      else
        item.isSel = false
    return resp.send {ok:1,result:{nearByCondos,tags,uaddr}}