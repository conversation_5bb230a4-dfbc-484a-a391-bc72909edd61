helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
{topUpRange} = INCLUDE 'libapp.topUpAndTax'
{checkIsApp} = INCLUDE 'libapp.oauth'
{respError} = INCLUDE 'libapp.responseHelper'
debug = DEBUG()

fnSendSMS = SERVICE 'sendSMS'
sendCall = SERVICE 'sendCall'
ProvAndCity = MODEL 'ProvAndCity'
provAbbrName = ProvAndCity.getProvAbbrName
# User = COLLECTION 'chome', 'user'
TopupAgents = COLLECTION 'chome', 'topup_agents'
Properties = MODEL 'Properties'
# isAllowed = DEF '_access_allowed'

UserModel = MODEL 'User'
verify_roles_list = DEF 'verify_roles_list'
dataMethods = DEF 'dataMethods'
getTopUid = DEF 'getTopUid'

TWILIO_LANG_MATCH =
  'en': 'en-CA'
  'zh-cn': 'zh-CN',
  'zh': 'zh-TW'
  'kr': 'ko-KR'

notifyUser = (req,user,vcode,cb)->
  lang = req.locale()
  obj =
    To: '+1' + user.mbl2v #
    From: '+19056142609'#req.setting?.mbl or '+16472223733'
  notifyFunc = (obj)->
    console.error '[realtorVerify]Error notify type:',obj
  switch user.tp
    when 'sms'
      notifyFunc = fnSendSMS
      obj.Body = "#{req.l10n('RealMaster Verification Code','verifycall')}: #{vcode}"
    when 'call'
      notifyFunc = sendCall
      sayAttribute = "voice='alice' language='#{TWILIO_LANG_MATCH[lang] or 'en-CA'}'"
      vcode = ("#{c} " for c in vcode.toString())
      vcodeForCall =
        "<Say #{sayAttribute}>
          <say-as interpret-as='telephone'>#{vcode}</say-as>
        </Say>"
      obj.Twiml = "<Response>
                    <Say #{sayAttribute}>#{req.l10n('RealMaster Verification Code is','verifycall')} </Say>
                    #{vcodeForCall}
                    <Pause length='0.5'/>
                    <Say #{sayAttribute}>#{req.l10n('Repeat','verifycall')}</Say>
                    #{vcodeForCall}
                    <Pause length='0.5'/>
                    <Say #{sayAttribute}>#{req.l10n('Thank you goodbye','verifycall')}</Say>
                  </Response>"
  notifyFunc obj,(err,response)->
    if err then console.error err
    cb err

sendVCode = (req,body,id,cb)->
  _sendVCode = (callback)->
    body.verify = {}
    unless body.mbl2v
      return cb()
    # if body.mbl2v #user.mbl and req.param('mbl2v')
    vcode = helpers.randomCode()
    # TODO: verification time gap, should read db
    # if (body.verify?.mblvts?) and (Date.now() - body.verify.mblvts < 2000)
    #   return callback()
    notifyUser req,body,vcode,(err)->
      # To test revert sequence
      if err then return cb err.message or err
      body.verify.mblvcd = vcode
      # console.log text
      callback() #call back to _save

  _save = ->
    body.verify.mblvts = Date.now()
    #update user table here, mbl2v, mblvcd, mblvts
    #check roles and add realtor
    if (not body.roles?)
      body.roles = []
    UserModel.saveMblVerificationCode {req,body,id},(err,user)->
      # cb to send json success msg
      cb(err)
  # delete body.verify
  _sendVCode ->
    # console.log '===========body->'
    # console.log body
    _save()


#==============================GET====Pages======================
# '/1.5/user/identity'
APP '1.5'
APP 'user',true

dataMethods.verifiedRole = (req, user)->
  return null unless user?.roles?.length
  for i in verify_roles_list
    if user.roles.indexOf(i) > -1
      return i
  return null

ADDPLUGIN 'verifiedRole', ()->
  dataMethods.verifiedRole(@,@session.get('user'))

# TODO: remove need follow realtor stuff
GET 'identity',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    #console.log user
    resp.noCache()
    resp.ckup 'identity',{title:req.l10n("RealMaster Settings"),user:user}

# '/1.5/user/verify'
# verification page
GET 'verify',(req,resp)->
  if url = checkIsApp req
    return resp.redirect url
  # d = req.query.d || ''
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.noCache()
    #user.roles = [] #access point to manuly change user session used in debug
    # resp.ckup 'verify',{}
    resp.ckup 'verifyNew',{}, '_', {noAngular:1, noRatchetJs:1, noAppCss:1, noUserModal:1}


# '/1.5/user/unlock'
# unlock feature page
# TODO: no longer used
GET 'unlock',(req,resp)->
  # d = req.query.d || ''
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.noCache()
    #user.roles = [] #access point to manuly change user session used in debug
    resp.ckup 'unlock',{title:req.l10n('Unlock Feature'),user:user,d:d}

# '/1.5/user/follow'
# follow a realtor view
GET 'follow',(req,resp)->
  d = req.query.d || ''
  tp = req.param 'tp'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    # 检查管理员权限
    unless req.isAllowed('productSales') or req.isAllowed('marketAdmin')
      return resp.ckup 'generalError',{err:MSG_STRINGS.NOT_AUTHORIZED}
    # resp.noCache()
    ctx = {title:req.l10n("Follow Realtor"), user:user, d:d}
    done = ()->
      resp.ckup 'followRealtor', ctx
    if uid = user?.flwng?[0].uid
      # return UserModel.findById uid,{},(err, agent) ->
      #TODO: use post
      # ctx.uid = uid
      roles = tp if tp in verify_roles_list
      UserModel.findPublicInfo {lang:req.locale(), id:uid,roles},(err, agent) ->
        if err then console.error err
        ctx.rltr = agent
        return done()
    else
      done()
    #user.roles = [] #access point to manuly change user session used in debug
    # if user.flwng?.length > 0
    #   resp.ckup 'identity',{title:req.l10n("RealMaster Settings"),user:user}
    # else

#---------------------POSTS--------actions----------------

# [resource/json] search for some user by name or email or cell
# /1.5/user/search?name=name
appendCityToQuery = (q, opt)->
  if city = opt?.city
    q['sas.city'] = city
scoreUser = (u)->
  score = 0
  score -= 10 if u.isVip
  score

vipFirst = (a,b)->
  # console.log('++++++',a,b,scoreUser(a) - scoreUser(b))
  return scoreUser(a) - scoreUser(b)

POST 'search',(req,resp)->
  return resp.send {err:'Not in APP',success:false} unless req.getDevType() is 'app'
  role = req.param 'role','realtor'
  unless (name = req.param('name')) or (uid = req.param('uid'))
    return resp.send {err: req.l10n("Please input email or phone number or the name"), success:false} # TODO: front end shall deal with this
  #appendCityToQuery(query, req)
  # NOTE: if not admin can only search by role, dont expose user data except admin
  # topup need to select a user
  if (role is 'user') and req.isAllowed 'topup'
    role = null
  UserModel.getListByName {uid,name,role},(err,list)->
    if err
      resp.send {err:err, success:false}
    else if list?.length > 0
      retList = []
      for u in list
        uu = UserModel.parseU(req, u)
        # NOTE: uu is parsed, no roles info
        uu.isVip = req.isAllowed 'vipPlus', u
        retList.push uu
      retList.sort(vipFirst)
      if req.param 'range'
        realtorRange = topUpRange {isAdmin:true,isVip:false}
        vipRange = topUpRange {isAdmin:true,isVip:true}
        for u in retList
          if u.isVip
            u.topUpRange = vipRange
          else
            u.topUpRange = realtorRange
      resp.send {success:true,resultList:retList}
    else
      resp.send {success:false, err:'no user', errType:'USER_NOT_FOUND'}

# # randomized for vip_plus user
# getRcmdRealtorVipPlus = (req, limit, cb)->
#   q =
#     roles:{$all:['vip_plus','realtor'],$not:{$in:['_dev']}}
#     tstAc:{$exists:false}
#     avt:{$exists:true}
#   appendCityToQuery q, req.body
#   opt =
#     fields: {fn:1, ln:1, nm:1, _id:1, cpny:1, cpny_pstn:1, avt:1, roles:1, itr:1, mbl:1, eml:1}
#     limit: 100
#   UserModel.getList q,opt, (err,list)->
#     console.error err if err
#     return cb [] unless list?.length > 0
#     cb helpers.shuffle(list).slice 0,limit
# # [resource/json] get recommend list according to actpts[active points/活跃度] rmb[Real Master bi/积分] for other purpose
# getRcmdRealtor = (req, limit, cb)->
#   if not cb? and 'function' is typeof limit
#     cb = limit
#     limit = 50
#   getRcmdRealtorVipPlus req, limit, (list = [])->
#     return cb null,list if list.length >= limit
#     q =
#       roles:'realtor'
#       tstAc:{$exists:false}
#       avt:{$exists:true}
#       _id:{$nin:(u._id for u in list)}
#     appendCityToQuery q, req.body
#     opt =
#       fields: {fn:1, ln:1, nm:1, _id:1, cpny:1, cpny_pstn:1, avt:1, roles:1, itr:1, mbl:1, eml:1}
#       sort: [['wsc',-1],['rmb',-1]]
#       limit: limit - (list.length or 0)
#     UserModel.getList q,opt, (err,list2)->
#       rlist = list.concat list2 or []
#       cb err,rlist


TOP_AGENT_ROUND = 0
# logArray = (idx,l)->
#   console.log idx+'\t['
#   for i in l
#     console.log i.eml
#   console.log ']'


# /1.5/user/recommend
POST 'recommend',(req,resp)->
  # 检查管理员权限
  unless req.isAllowed('productSales') or req.isAllowed('marketAdmin')
    return resp.send {err:req.l10n(MSG_STRINGS.NOT_AUTHORIZED), success:false}
    
  param = req.body or {}
  retList = []
  UserModel.getTopAgents req,{
    city:param.city,
    prov:param.prov,
    subCity: param.subCity,
    role:(param.tp or param.role)},(err,tlist)->
      if err
        resp.send {err:err, success:false}
      if tlist?.length
        # for u in tlist
        #   uu = {fn:u.fn,ln:u.ln,nm:u.nm,_id:u._id,cpny:u.cpny,cpny_pstn:u.cpny_pstn,avt:u.avt,itr:u.itr,mbl:u.mbl}
        #   uu.eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
        #   uu.reco = req.isAllowed 'rcmdRealtor',u
        #   retList.push uu
        # ta1 = tlist.shift()
        # retList = helpers.shuffle tlist
        # retList.unshift ta1
        retList = retList.concat tlist
        retList.push {tp:'split'}
      UserModel.getRcmdProvider {limit:30,city:param.city,prov:param.prov,role:(param.tp or param.role)},(err,list)->
        if err
          resp.send {err:err, success:false}
        else if (retList.length is 0) and (list.length is 0)
          resp.send {err:req.l10n('No Recommendation Yet.'), success:false, ec:3} #该区域暂时没有推荐纪
        else
          for u in list
            uu = UserModel.parseU(req, u)
            retList.push uu
          ## get topup listing realtor
          # Properties.findOne {_id:param._id,topTs:{$gte:new Date()}},{fields:{topuids:1,_id:1}},(err,prop)->
          Properties.findTopUpUIDById param._id,(err,prop)->
            console.error err if err
            ret = {success:true,recommendList:retList}
            done = ()->
              resp.send ret
            if uid = getTopUid prop
              UserModel.findById uid,{},(err,topU)->
                console.error err if err
                if topU?.roles?.indexOf('realtor') > -1
                  ret.topU = UserModel.parseU(req, topU)
                done()
            else
              done()

  # # based on Weekly-Share-Counter
  # getRcmdRealtor req, 50,(err,list)->
  #   if err
  #     resp.send {err:err, success:false}
  #   else if list.length is 0
  #     resp.send {err:req.l10n('No Recommended Realtors.'), success:false, ec:3} #该区域暂时没有推荐经纪
  #   else
  #     retList = []
  #     for u in list
  #       uu = {fn:u.fn,ln:u.ln,nm:u.nm,_id:u._id,cpny:u.cpny,cpny_pstn:u.cpny_pstn,avt:u.avt,itr:u.itr,mbl:u.mbl}
  #       uu.eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
  #       uu.reco = req.isAllowed 'rcmdRealtor',u
  #       retList.push uu
  #     resp.send {success:true,recommendList:retList}



# [action/user] Follow some realtor
POST 'follow',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    findError = (err)-> resp.send {message: err.toString(),success:false}
    return findError "Bad Parameter" unless uid = req.param 'uid'
    UserModel.followSomeone {req, user, buzUserID:uid},(err,nUser)->
      if err then return findError err
      resp.send {success:true}

POST 'verifyCell',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    body = req.body
    #console.log body
    findError = (err)-> resp.send {message:req.l10n('Error :') + err.toString(),success:false}
    #invoke SMS service
    data =
      mbl2v:body.mbl2v
      tp:body.tp
    return findError MSG_STRINGS.BAD_PARAMETER if not (data.mbl2v or data.tp)
    sendVCode req,data,user._id,(err)->
      return findError err if err
      resp.send {success:true , message:req.l10n('Verification Code Sent')}
    #resp.send {success:true , message:'SMS Sent, Please Check inbox'}

POST 'verify',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    # console.log body
    # console.log '#######################user'
    # console.log user
    #find user and compare value
    _error = (err)-> resp.send {message:req.l10n(err), success:false, ok:0}
    # compare values mblvcd and time stamp, rid, reb, mbl
    if not(req.body?.tp) or (req.body?.tp not in ['realtor', 'merchant','visitor'])
      return _error(MSG_STRINGS.BAD_PARAMETER)
    UserModel.verifyMblVCode {req,resp,user},(err,ret)->
      if err
        return _error(err)
      return resp.send {ok:1,success:true,msg:req.l10n(ret)}

POST 'unlock',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    body = req.body
    # console.log '#######################body'
    # console.log body
    #find user and compare value
    findError = (err)-> resp.send {message:req.l10n('Error :') + err.toString(),success:false}
    _error = (err)->
      resp.send {message:req.l10n(err), success:false}
    # validate email addr
    validateEmail = (email) ->
      re = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i
      re.test email
    if not validateEmail body.speml
      return _error('Wrong Email Address Format')
    if body.speml is user.eml[0]
      return _error('You Cannot Sponse Yourself!')
    update = {}
    UserModel.findByEml body.speml,(err,r)->
      if err
        resp.send {err:err, id:wid}
      else if not r
        resp.send {message:req.l10n('This user does not exist.') ,speml:body.speml}
      else
        # if user type is realtor, this r(returned user(invitee) is realtor)
        if r.roles?.indexOf('realtor') >= 0
          # if some one used this emal already.
          UserModel.findBySponsorEmail speml,(err,spu)->
            if err
              resp.send {err:err, id:wid}
            else if not spu
              # success update user
              #add roles + 'realtor'
              if r.rid or r.mblvts # mblvts may not exist, inported data or old registed user
                # verify timestamp, invitee should have time stamp < inviter for mblvts
                # registration date
                #timestamp = r._id.toString().substring(0,8)
                #date = new Date( parseInt( timestamp, 16 ) * 1000 )
                date = new Date r._id.getTimestamp()
                unlock_date = new Date("Tue Oct 08 2013 00:00:00 GMT-0400 (EDT)")
                if date > unlock_date # r.mblvts (user.regTs < r.mblvts) registered after June 1, 2015, EDT
                  #update user has set roles
                  UserModel.updateBySponsorEmail req,body.speml,(err,user)->
                    if err then return findError err
                    resp.send {success:true,message:req.l10n('Unlocked')} #Congrates! You Have Unlocked Feature'
                else
                  resp.send {success:false,message:req.l10n('The sponsor must register after 2015-10-8.')} #The Unlock ID must be verified after your referrel, please refer another realtor and try again!
              else
                resp.send {success:false,message:req.l10n('This user is not available as a sponsor.')}
            else
              # this email address has been used
              resp.send {success:false,message:req.l10n('This realtor is already a sponsor of another user.')} #This Acoount Has Been Used, You Can Inivite Another Realtor!
          # resp.send {success:true,message:req.l10n('Unlocked')}
        else
          resp.send {message:'This user is not a Realtor.' ,speml:body.speml , success:false} #This user is not available.

POST 'deletePhone',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    try
      error = await UserModel.deletePhone {req,user}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    return respError {clientMsg:error.e,resp} if error
    return resp.send {ok:1,msg:req.l10n('Deleted')}


#-----------------------------VIEWs-------------------

VIEW 'identity',->
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: '/1.5/settings', dataIgnore: 'push'
    # text ckup 'headerbarUserModalBtn'
    h1 class: 'title',-> _ 'RealMaster'
  div class: 'content', ->
    ul class:"table-view ",->#content-padded " ,->
      # realtor verify
      li class:'table-view-cell identity-li-item',id:'id-id', style:'',->
        span class:'cell',->
          img  class:'verifyPageImg', src:'/img/realtor.png'
        span class:'cell',style:'font-size:0.8em;line-height: 1.2em;  padding-left: 5px;',->
          h5 style:'margin: 0px',->
            text _ "Realtor Verification"

          span style:'color:#666;',->
            text _ "As a realtor, you can upgrade to realtor membership for more services." #
          #0.8em
        span class:'cell',->
          aparam = class:'pull-right id-action',href:"/1.5/user/verify?d=identity",dataIgnore:'push'
          iparam = class:'fa fa-plus-square-o ', style:'padding-right: 3px;'
          if  Array.isArray(@user?.roles) and (@user.roles.indexOf('realtor') >= 0)
            aparam = class:'pull-right id-action',href:"#",dataIgnore:'push'
            iparam = class:'fa fa-check-square-o'
          a aparam ,->
            i iparam
            #i class:'fa fa-circle-o fa-stack-1x"'

      # unlock features
      li class:'table-view-cell identity-li-item',id:'id-unlock', style:'',->
        span class:'cell',->
          img  class:'verifyPageImg', src:'/img/icon-lock.png'
        span class:'cell',style:'font-size:0.8em;line-height: 1.2em;  padding-left: 5px;',->
          h5 style:'margin: 0px',->
            text _ "Unlock More Features"

          span style:'color:#666;',->
            text _ "Inviting a realtor to use RealMaster App, unlock more fabulous features." #
          #0.8em
        span class:'cell',->
          aparam = class:'pull-right id-action',href:"/1.5/user/unlock?d=identity",dataIgnore:'push'
          iparam = class:'fa fa-plus-square-o ',style:'padding-right: 3px;'
          if @req.isAllowed 'shareWeCard'
            aparam = class:'pull-right id-action',href:"#",dataIgnore:'push'
            iparam = class:'fa fa-check-square-o'
          a aparam ,->
            i iparam

      # follow realtor features
      # unless @user?.roles?.indexOf('realtor') >= 0
      #   li class:'table-view-cell identity-li-item',id:'id-unlock', style:'',->
      #     span class:'cell',->
      #       img  class:'verifyPageImg', src:'/img/icon-lock.png'
      #     span class:'cell',style:'font-size:0.8em;line-height: 1.2em;  padding-left: 5px; width: 100%;',->
      #       h5 style:'margin: 0px',->
      #         text _ "Follow A Realtor"

      #       span style:'color:#666;',->
      #         text _ "Follow A Realtor To Access More Details" #
      #       #0.8em
      #     span class:'cell',->
      #       aparam = class:'pull-right id-action',href:"/1.5/user/follow?d=identity",dataIgnore:'push'
      #       iparam = class:'fa fa-plus-square-o ',style:'padding-right: 3px;'
      #       if Array.isArray(@user?.flwng) and (@user.flwng.length >= 0)
      #         aparam = class:'pull-right id-action',href:"#",dataIgnore:'push'
      #         iparam = class:'fa fa-check-square-o'
      #       a aparam ,->
      #         i iparam
#             <span class="fa-stack fa-lg">
#   <i class="fa fa-square-o fa-stack-2x"></i>
#   <i class="fa fa-twitter fa-stack-1x"></i>
# </span>
STYLUS 'identity','''
.content-padded
  margin 10px
#id-id, #id-unlock
  display:table;
  width 100%

.id-action
  margin-left 5px
  font-size 1.7em
  color #5cb85c
.cell
  display table-cell
  vertical-align middle
.verifyPageImg
  margin-right 5px
  height 40px
.table-view-cell.identity-li-item
  padding:0 10px 5px 10px
  margin: 10px 0 10px 0;
'''


VIEW 'verifyNew', ->
  div id:'vueBody',->
    text '''<app-verify></app-verify>'''
  js '/js/entry/commons.js'
  js '/js/entry/appVerify.js'

VIEW 'unlock',->
  header class: 'bar bar-nav', ->
    a class: 'icon icon-left-nav pull-left',
    href: '/1.5/user/identity',
    dataIgnore:'push' #dataTransition: 'slide-out'
    h1 class: 'title',-> _ 'Unlock Features'
  div class: 'content input-align-right', ->
    div ngApp:'appProfile',ngController:'ctrlEditProfile',->
      div class: 'content-padded', style:'padding:10px 10px 10px 10px; background-color:#eee', ->
        div ->
          text _ 'Some features can be only accessed after being unlocked.'
        div style:'margin-top:15px',->
          text _ 'Step 1:'
        div style:'font-size:14px',->
          text _ 'Invite a Realtor to install RealMaster App and complete Realtor Verification successfully. '
          text _ 'You can easily share RealMaster App Free Download Page to invite your sponsor. '
        div style:'margin-top:15px',->
          text _ 'Step 2:'
        div style:'font-size:14px',->
          text _ 'Have a Realtor sponsor already?'
          text _ 'Just input his/her RealMaster App login email to unlock more features right now. '
      div class: 'content-padded', style:'margin-top:10px;', ->
        div id:'message',
        class:'error-block',
        style:'text-align:center;margin-bottom:10px;line-height: 1em;',
        ngShow:'message',->
          text '{{message}}'
        form class: 'input-group',name:'userForm', ->
          div class: 'input-row auto-height',->
            input type: 'email',
            name:'speml',
            style:'width: 86.6%; margin-right: 6.7%; text-align:left; ',
            ngModel:'formData.speml',
            placeholder: _("Sponsor's Login Email")
            p class:'error-block inline-block',
            style:'margin-left:6.7%',
            ngShow:'userForm.speml.$invalid && !userForm.speml.$pristine',->
              text _ 'Please Enter a Valid Email.'
          #hr style: 'width:90%'
          button class: 'btn btn-positive btn-mar-top btn-long btn-block',
          ngClick:'unlock();',
          ngDisabled:'userForm.$invalid',->
            _ 'Unlock'
          a href:'/app-download',
          class: 'btn btn-positive btn-mar-top btn-long btn-block',->
            _ 'Invite a Realtor Sponsor'

          # div style: 'width:auto;',->
          #   pre -> "{{formData}}"
  coffeejs { vars:{d:@d} },->
    null
  js '/js/unlock.min.js'

STYLUS 'unlock','''
.input-row.auto-height
  height auto
'''

###
VIEW 'realtorContact', ->
  text '''
  <style>
  nav#realtorContact.smb-md { height:250px;}
  nav#realtorContact li{
    padding-right:15px;
    text-align: left;
    color: #666;
    border-bottom: none;
  }
  nav#realtorContact li.cancel{
    text-align: center;
    border-top: 1px solid #ECE7E7;
  }
  nav#realtorContact .table-view{
    padding-top:0;
  }
  nav#realtorContact li i.fa{
    padding-right: 10px;
  }
  nav#realtorContact li i.fa-phone{
    padding-left: 3px;
  }
  </style>
  '''
  div class:"backdrop", style:'display:none; z-index:13', ngClick:"toggleSMB()"
  classParam = "menu slide-menu-bottom smb-auto"
  nav class:classParam, id:"realtorContact",->
    ul class:'table-view', ->
      li class:'table-view-cell', ->
        a ngHref:"tel:{{curRealtor.mbl}}", ->
          i class:'fa fa-fw fa-phone'
          text _ 'Call'
      li class:'table-view-cell', ->
        a ngHref:"mailto:{{curRealtor.eml}}", ->
          i class:'fa fa-fw fa-envelope'
          text _ 'Email'
      li class:'table-view-cell', ngClick:"chat(curRealtor)", ->
        i class:'fa fa-fw fa-comments-o', style:'color:#e03131'
        text _ 'Send a Message'
      li class:'table-view-cell', ngClick:"followRealtor(curRealtor)", ngShow:"!rltr.length",->
        i class:'fa fa-fw fa-link'
        text _ 'Follow'
      li class:'cancel table-view-cell', ngClick:"toggleSMB('close')", ->
        text _ 'Cancel'
###
VIEW 'followRealtor', ->
  coffeejs { vars:{rltr:@rltr}
    },->
    null
  div id:'vueBody',->
    text '''<app-roles-search></app-roles-search>'''
  js '/js/entry/commons.js'
  js '/js/entry/appRolesSearch.js'
