helpers = INCLUDE 'lib.helpers'
{respError} = INCLUDE 'libapp.responseHelper'
getTagsTypes = DEF 'getTagsTypes'
upsertCRM = DEF 'upsertCRM'
handleRefId = DEF 'handleRefId'
crmAuth = DEF 'crmAuth'
getUserGroupQuery = DEF 'getUserGroupQuery'
# addCRMID2User = DEF 'addCRMID2User'
GroupModel = MODEL 'Group'
getCRMFilters = DEF 'getCRMFilters'
ObjectId = INCLUDE('lib.mongo4').ObjectId
UserModel = MODEL 'User'

ListingAgent = COLLECTION 'chome', 'listing_agent'
UserCRM = COLLECTION 'chome', 'rm_crm'

LIMIT = 50

APP '1.5'
APP 'listingagent',true

getRefids = (las=[])->
  refids = []
  for la in las
    if la.aid
      refids.push la.aid
    else
      refids.push la._id
  refids

matchLaWithPhone = (las,areaCodes)->
  matchLas = []
  for la in las
    match = false
    if la.aid
      for tel in la.tels
        for c in areaCodes
          if (tel.indexOf(c.toString()) > -1) and (match is false)
            matchLas.push la
            match = true
            break
    else
      for c in areaCodes
        if (la.mbl.toString().indexOf(c.toString()) > -1) and (match is false)
          matchLas.push la
          match = true
          break

  matchLas

mergeCRM2LA = (las,crms,cb)->
  las ?= []
  cb null,las unless crms
  kvCrm = {}
  GroupModel.mergeGroups2CRM crms,(err,crms)->
    return cb err,null if err
    for crm in crms
      kvCrm[crm.refid] = crm
    for la in las
      la.props = la.props.slice(0,20) if la.props.length > 20
      if la.aid
        #handle agent has no user matched
        la.crm = kvCrm[la.aid]
      else
        la.crm = kvCrm[la._id]
    cb null,las

getCRMs = (q,opt,cb)->
  UserCRM.findToArray q,opt,(err,crms)->
    return cb err,null if err
    cb null,crms
getIdsFromCRMs = (crms=[])->
  uids = []
  aids = []
  for c in crms
    refid = c.refid
    if helpers.isObjectIDString(refid?.toString())
      uids.push refid
    else
      aids.push refid
  {uids,aids}
getLAsWithCRMs = (user,filterQuery,opt,cb)->
  ListingAgent.findToArray filterQuery,opt,(err,las)->
    return cb err,null if err
    refids = getRefids las
    getUserGroupQuery user,(err,q)->
      return cb err,null if err
      q.refid = {$in:refids}
      UserCRM.findToArray q,(err,crms)->
        return cb err,null if err
        mergeCRM2LA las,crms,(err,las)->
          return cb err,null if err
          cb null,las

POST 'list',(req,resp)->
  crmAuth req,resp,(user)->
    unless body = req.body
      return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
    filterQuery = {}
    filterQuery.roles = body.role if body.role
    if tag = body.tag
      if tag is 'unmatched'
        filterQuery.inDB = {$exists:0}
      if tag is 'matched'
        filterQuery.inDB = {$ne:null}
      if tag is 'topped'
        filterQuery.topped = {$ne:null}
    crmOpt = body.crmOpt
    if body.tags or body.tps or body.groups or (crmOpt is 1)
      hasCRM = true
    if areaCodes = body.areaCodes and Array.isArray areaCodes
      pNums = []
      for c in areaCodes
        pNums.push ///#{c}[-.]?([0-9]{3})[-.]?([0-9]{4})///
    skip = if body.page then (parseInt(body.page) - 1)*LIMIT else 0
    if hasCRM
      getUserGroupQuery user,(err,q)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        q = getCRMFilters q,body
        opt = {limit:LIMIT,skip,sort:{nl:-1}}
        getCRMs q,opt,(err,crms)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          refids = getIdsFromCRMs crms
          lQuery = {$or:[{_id:{$in:refids.uids}},{aid:{$in:refids.aids}}]}
          lQuery = Object.assign {},lQuery,filterQuery
          ListingAgent.findToArray lQuery,{limit:LIMIT,skip,sort:{nl:-1}},(err,las)->
            return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
            las ?= []
            return resp.send {ok:1,result:las} unless las
            if pNums
              las = matchLaWithPhone las,areaCodes
            mergeCRM2LA las,crms,(err,las)->
              return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
              return resp.send {ok:1,result:las}
    else
      filterQuery.$or = []
      if body.search
        searchReg = helpers.str2reg(body.search,true)
        filterQuery.$or.push {_id:searchReg},{nm:searchReg},{'mbl':searchReg},{tels:searchReg}
      filterQuery.$or.push {tels:{$in:pNums}},{'mbl':{$in:pNums}} if pNums
      delete filterQuery.$or if filterQuery.$or.length is 0
      opt = {limit:LIMIT,skip,sort:{nl:-1}}
      if crmOpt is 0
        getCRMs {},{},(err,crms)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          refids = getIdsFromCRMs crms
          filterQuery._id = {$nin:refids.uids} if refids.uids.length > 0
          filterQuery.aid = {$nin:refids.aids} if refids.aids.length > 0
          getLAsWithCRMs user,filterQuery,opt,(err,las)->
            return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
            return resp.send {ok:1,result:las}
      else
        getLAsWithCRMs user,filterQuery,opt,(err,las)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          resp.send {ok:1,result:las}

POST 'submit',(req,resp)->
  crmAuth req,resp,(user)->
    unless body = req.body
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
    upsertCRM body,user,(err,result)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      getTagsTypes req,user,true,(err,tagsTps)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        return resp.send {ok:1, result:result, tagsTps:tagsTps} unless helpers.isObjectIDString(result.refid?.toString())
        UserModel.addCRMID {req,uid:result.refid,crmid:result._id},(err,user)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          resp.send {ok:1, result:result, tagsTps:tagsTps}

# POST 'follow',(req,resp)->
#   crmAuth req,resp,(user)->
#     unless body = req.body
#       return respError MSG_STRINGS.BAD_PARAMETER,resp,"", err if err
#     refid = body.refid
#     aid = body.aid
#     q = {uid:user._id}
#     set =
#       mt: new Date()
#       uid: user._id
#       unm: user.nm
#       uavt: user.avt
#     if refid = new ObjectId refid
#       set.refid = refid
#       q.refid = refid
#     if aid
#       set.refid = aid
#       q.refid = aid
#       set.src = 'listingAgent'
#     update =
#       $set:set
#     UserCRM.findOneAndUpdate q,update,{upsert:true,returnDocument:'after'},(err,crm)->
#       return respError MSG_STRINGS.DB_ERROR,resp, err if err
#       if crm and crm.ok is 1
#         result = crm.value
#         resp.send {ok:1,result:result}

GET 'index',(req, resp)->
  crmAuth req,resp,(user)->
    resp.ckup 'index',{dGids:user.dGids},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

GET 'detail',(req,resp)->
  crmAuth req,resp,(user)->
    crmid = req.param('crmid')
    refid = req.param('refid')
    aid = req.param('aid')
    unless crmid or refid or aid
      return resp.ckup 'generalError',{err:"No ID specified"}
    dispVar = {}
    dispVar.isAdmin = req.isAllowed 'admin',user
    getUserGroupQuery user,(err,groupQ)->
      col1 = ListingAgent
      col2 = UserCRM
      if refid
        refid = new ObjectId refid
        q1 = {_id:refid}
        groupQ.refid = new ObjectId(refid)
        q2 = groupQ
      if aid
        q1 = {aid:aid}
        groupQ.refid = aid
        q2 = groupQ
      if crmid
        groupQ._id = new ObjectId crmid
        # q1 = {_id:new ObjectId crmid}
        q1 = groupQ
        col1 = UserCRM
        col2 = ListingAgent
      col1.findOne q1, (err, ret1) ->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        if crmid
          refid = ret1.refid
          if helpers.isObjectIDString(refid?.toString())
            q2 = {_id:new ObjectId(refid)}
          else
            q2 = {aid:refid}
        col2.findOne q2, (err, ret2) ->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          if crmid
            crm = ret1
            crmu = ret2
          else
            crm = ret2
            crmu = ret1
          getTagsTypes req,user,true,(err,tagsTps)->
            return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
            las = [crmu]
            crms = if crm then [crm] else []
            mergeCRM2LA las,crms,(err,las)->
              resp.ckup 'detail',{user:las[0],tagsTps,dispVar},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

VIEW 'index',->
  div id:'vueBody',style:'height:100%;overflow:hidden;',->
    text '''<app-listing-agent></app-listing-agent>'''
  coffeejs {vars:{dGids:@dGids}},->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appListingAgent.js'

VIEW 'detail',->
  div id:'vueBody',->
    text '''<app-crm-detail></app-crm-detail>'''
  coffeejs {vars:{user:@user,tags:@tagsTps.tags,types:@tagsTps.tps,page:'la',dispVar:@dispVar}},->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appCrmDetail.js'
