libProperties = INCLUDE 'libapp.properties'
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
libPropertyImage = INCLUDE 'libapp.propertyImage'

{respError} = INCLUDE 'libapp.responseHelper'
UserModel = MODEL 'User'
ProvAndCity = MODEL 'ProvAndCity'

# User = COLLECTION 'chome', 'user'
TopupAgents = COLLECTION 'chome', 'topup_agents'
SysData = COLLECTION 'chome', 'sysdata'
UserStats = COLLECTION 'chome', 'user_stats'


TopAgentsObj = {}
TopAgentsByIDs = {}
TopAgentsList = []
ENABLE_VIEW_COUNTER = false


#get value form object
getTopAgentByCityAndProv = (req, opt, cb) ->
  city = opt.city
  prov = ProvAndCity.getProvAbbrName opt.prov
  subCity = opt.subCity
  ret = {}
  cityName = libProperties.formatCityName(prov + '-' + city)
  subCityName = libProperties.formatCityName(prov + '-' + city + '-' + subCity) if subCity
  TopAgentsObj[cityName] ?= { ts: new Date() }
  now = new Date()
  if (now - TopAgentsObj[cityName].ts) > 60*10*1000
    reloadAgents()
  ta05 = TopAgentsObj[subCityName]?.ta1 or []
  ta1 = TopAgentsObj[cityName].ta1 or []
  ta15 = TopAgentsObj[subCityName]?.ta2 or []
  ta2 = TopAgentsObj[cityName].ta2 or []
  countList = {
    ta05: []
    ta1: []
    ta15: []
    ta2: []
  }
  nameList = ['ta05', 'ta1', 'ta15', 'ta2']
  for ag, i in [ta05, ta1, ta15, ta2]
    if ag?.length > 0
      count = []
      for ta in ag
        ta.vinc++ if ENABLE_VIEW_COUNTER
        avt = libPropertyImage.replaceRM2REImagePath ta.avt if ta.avt
        count.push { avt, eml: ta.eml, vip: ta.vip, \
          fnm: libUser.fullNameOrNickname(req.locale(), ta) }
      name = nameList[i]
      countList[name] = count
  # if ta1.length
  #   for ta in ta1
  #     ta.vinc++ if ENABLE_VIEW_COUNTER
  #     ta1c.push {avt:ta.avt,eml:ta.eml,vip:ta.vip, fnm:libUser.fullNameOrNickname(req.locale(),ta)}
  # if ta2.length
  #   for ta in ta2
  #     ta.vinc++ if ENABLE_VIEW_COUNTER
  #     ta2c.push {avt:ta.avt,eml:ta.eml,vip:ta.vip, fnm:libUser.fullNameOrNickname(req.locale(),ta)}
  # if ta15.length
  #   for ta in ta15
  #     ta.vinc++ if ENABLE_VIEW_COUNTER
  #     ta15c.push {avt:ta.avt,eml:ta.eml,vip:ta.vip, fnm:libUser.fullNameOrNickname(req.locale(),ta)}
  if req.isAllowed('devGroup') and req.param 'manage'
    return cb null, { ta05: ta05, ta1: ta1, ta15: ta15, ta2: ta2 }
  cb null, { ta05: countList.ta05, ta1: countList.ta1, ta15: countList.ta15, ta2: countList.ta2 }
  #   return cb null,{ta1:ta1, ta2:ta2}
  # cb null,{ta1:ta1c,ta2:ta2c}

updateAgStatus = (cb) ->
  q = { status: 'A' }
  now = helpers.dateFormat('YYYY-MM-DD')
  q.end_dt = { $lte: now }
  # console.log '+++++++'
  TopupAgents.updateMany q, { $set: { status: 'U' } }, (err, ret) ->
    return cb err if err
    cb null

reloadAgents = (cb) ->
  now = new Date()
  updateAgStatus (err, ret) ->
    return console.error err if err
    q = {}#status:'A'
    now = new Date()
    q.start_ts = { $lte: now }
    q.end_ts = { $gt: now }
    TopupAgents.findToArray q, { sort: { end_ts: 1 } }, (err, ret) ->
      return console.error err if err
      TopAgentsObj = {}
      TopAgentsByIDs = {}
      # agentEmls = []
      # TopAgentsByEmls = {}
      agentIds = []
      TopAgentsByUids = {}
      # console.log ret
      for ad in ret
        # NOTE: 2020-12-11 eml in db was array
        # _id:ObjectId("5fb1051d73acd91149da52e9"), "eml" : [ "<EMAIL>" ]
        # NoTE: 2021-6-7 get agent by uids
        # agentEmls.push UserModel.getEmail ad
        agentIds.push ad.uid
        TopAgentsByIDs[ad._id?.toString()] = ad
        # TopAgentsByEmls[ad.eml] = ag
      # TODO: look up by uid?
      UserModel.getListByIds agentIds, { projection: UserModel.AGENT_FIELDS }, (err, list) ->
        list ?= []
        for rl in list
          # rl.eml = if Array.isArray rl.eml then rl.eml[0] else rl.eml
          rl.vip = 1 if rl.roles?.indexOf('vip_plus') > -1
          TopAgentsByUids[rl._id] = rl
        for k, ad of TopAgentsByIDs
          rl = TopAgentsByUids[ad.uid] or {}
          ad.top_id = ad._id
          TopAgentsByIDs[k] = Object.assign ad, rl
        for k, ag of TopAgentsByIDs
          ag.vinc = 0
          ag.cinc = 0
          cityName = libProperties.formatCityName(ag.prov + '-' + ag.city)
          TopAgentsObj[cityName] ?= { ts: new Date() }
          TopAgentsObj[cityName].ta1 ?= []
          TopAgentsObj[cityName].ta2 ?= []
          if ag.subCity
            subCityName = libProperties.formatCityName(ag.prov + '-' + ag.city + '-' + ag.subCity)
            TopAgentsObj[subCityName] ?= { ts: new Date() }
            TopAgentsObj[subCityName].ta1 ?= []
            TopAgentsObj[subCityName].ta2 ?= []
            if ag.ta1
              TopAgentsObj[subCityName].ta1.push(ag)
            else if ag.ta2
              TopAgentsObj[subCityName].ta2.push(ag)
          else if ag.ta1
            TopAgentsObj[cityName].ta1.push(ag)
          else if ag.ta2
            TopAgentsObj[cityName].ta2.push(ag)
          TopAgentsList.push ag# = ret
        # console.log TopAgentsByIDs
        # console.log TopAgentsList
        # console.log TopAgentsObj
        cb() if cb

updateAgCounter = (cb)->
  date = helpers.dateFormat new Date(), 'YYYY_MM_DD'
  done = 0
  toDo = TopAgentsList.length
  for ag in TopAgentsList
    if ag.vinc > 0 or ag.cinc > 0
      vinc = ag.vinc
      cinc = ag.cinc
      id = ag._id
      ag.vinc = 0
      ag.cinc = 0
      do (id,vinc)->
        inc = {vc:vinc,"stat.#{date}vc":vinc,cc:cinc,"stat.#{date}":cinc}
        TopupAgents.updateOne {_id:id},{$inc:inc},(err)->
          if err then console.error err
          cb() if ++done >= toDo
    else
      cb() if ++done >= toDo

reloadAgents()
countDown = 10

helpers.repeat 60*1000*5, ->
  updateAgCounter ->
    if countDown-- < 0
      countDown = 10
      reloadAgents()

# bannerClicked = (id)->
#   if url = (ad = bannerByIDs[id])?.tgt
#     ad.cinc++
#     return ad
#   null

DEF 'getTopAgentByCityAndProv',getTopAgentByCityAndProv

APP '1.5'
GET 'realtor', (req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login?d=#index'},(user)->
    resp.ckup 'realtor',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

VIEW 'realtor',->
  div id:'vueBody',->
    text """<index-realtor-middle-page :disp-var="dispVar"></index-realtor-middle-page>"""
  css '/css/apps/appIndex.css'
  js '/js/entry/commons.js'
  js '/js/entry/appRealtorPage.js'


APP '1.5'
APP 'topagent',true
GET 'all',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'productSales'
      return resp.redirect '/1.5/index'
    resp.ckup 'all-topagents',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}


POST 'all',(req, resp)->
  unless req.isAllowed 'productSales'
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
  p = req.body
  query = {}
  query.status = p.status if p.status
  if p.city
    query.city = p.city
    query.status = 'A'
  if p.subCity
    query.subCity = p.subCity
  TopupAgents.findToArray query, {sort:{eml:1, city:1, status:1, end_ts:1}},(err, ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    agentArray = {}
    for agent in ret
      agentArray[agent.eml]?=[]
      agentArray[agent.eml].push agent
    return resp.send {ok:1,agentArray:agentArray}

#filter user stats by ta start and end date,and then group by month.
  # "stat" : {
  #   "2019_01_21_CONTACT" : 1,
  #   "2019_02_05_CONTACT" : 2,
  #   "2019_02_05_MBL" : 1,
  #   "2019_02_07_CONTACT" : 2,
filterAndGroupStatByMonth = (v,from, to)->
  d = {}
  for day,value of v
    # if i <= 0
    date = new Date(day.substr(0,10).replace(/_/g,'-'))

    key = day.replace('_vc','').replace(/_/g,'')
    monthKey = key.substr(0,6)
    if (date >= from) and (date <= to)
      typeKey = key.substr(8,10) || 'other'
      d[monthKey] ?= {}
      d[monthKey][typeKey] ?= 0
      d[monthKey][typeKey] += value
  return d

POST 'stats',(req, resp)->
  unless req.isAllowed('productSales')
    return resp.send {ok:0}
  uid = req.param 'uid'
  from = req.param 'from'
  to = req.param 'to'
  UserStats.findOne {_id:uid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} if not ret or not ret.stat
    ret = filterAndGroupStatByMonth(ret.stat, new Date(from), new Date(to))
    resp.send {ok:1, stat:ret}

GET 'manage',(req, resp)->
  unless req.isAllowed 'devGroup'
    return resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'manage',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

POST 'baseprice',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  unless req.isAllowed 'productSales'
    return error('not authed')
  city = req.param 'city'
  #should get full prov, saved full in sysdata
  prov =  ProvAndCity.getProvFullName req.param 'prov'
  return error('Bad Param city') unless city and prov
  cityName = libProperties.formatCityName(prov+'-'+city)
  cmd = req.param 'cmd'
  if cmd is 'get'
    SysData.findOne {_id:'topAgentBasePrice'},(err,ret)->
      ta1 = ret?[cityName]?.ta1BasePrice
      ta2 = ret?[cityName]?.ta2BasePrice
      if ta1 or ta2
        return resp.send {ok:1,ta1:ta1,ta2:ta2}
      resp.send {ok:1}
    return
  price = parseInt(req.param('price'))
  return error('Bad Param price') unless price
  set = {}
  # set[cityName] = {}
  field = if req.param('ta1') then 'ta1BasePrice' else 'ta2BasePrice'
  set[cityName+'.'+field] = price
  SysData.updateOne {_id:'topAgentBasePrice'},{$set:set},{upsert:true},(err,ret)->
    if err
      console.error err
      return error(err)
    return resp.send {ok:1}

#1.5/topagent/topagents
POST 'topagents',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  cmd = req.param 'cmd'
  if cmd is 'del'
    unless req.isAllowed 'productSales'
      return error('not authed')
    _id = req.param '_id'
    set = {status:'U'}
    set.end_ts = new Date()
    TopupAgents.updateOne {_id:_id},{$set:set},(err,ret)->
      if err
        console.error err
        return error(err)
      reloadAgents ()->
        resp.send {ok:1}
    return
  if cmd is 'user'
    unless req.isAllowed 'productSales'
      return error('not authed')
    uid = req.param 'uid'
    TopupAgents.findToArray {uid:uid},(err,list)->
      if err
        console.error err
        return error(err)
      for i in list
        i.top_id = i._id.toString()
      resp.send {ok:1, list:list}
    return
  city = req.param 'city'
  prov = req.param 'prov'
  subCity = req.param 'subCity'
  return error('Bad Param city') unless (city and prov)
  UserModel.appAuth {req,resp},(user)->
    getTopAgentByCityAndProv req,{city,prov,subCity},(err,ret)->
      if err
        console.error err
        return error(err)
      return resp.send { ok: 1, ta05: ret.ta05, ta1: ret.ta1, ta2: ret.ta2, ta15: ret.ta15 }

# make sure there is only one ta1 for give city
# status will be updateed auto matically every 5 mins
ensureOnlyOneTa1 = (doc,cb)->
  if not doc.ta1
    return cb null
  q = {
    # status:'A',
    city:doc.city,
    prov:doc.prov,
    ta1:true
  }
  now = new Date()
  q.start_ts = {$lte:now}
  q.end_ts = {$gt:now}
  if doc.subCity
    q.subCity = doc.subCity
  else
    q.subCity = { $exists:false }
  TopupAgents.findToArray q, (err,list)->
    if err
      return cb err
    if list?.length
      return cb 'Aleady Have TA1',list
    cb null

# add topup to db and update cache
#1.5/topagent/addtop
POST 'addtop',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  unless req.isAllowed 'productSales'
    return error('not authed')
  uid = req.param 'uid'
  eml = req.param 'eml'
  return error('Bad Param uid') unless uid
  return error('Bad Param eml') unless eml
  city = req.param 'city'
  prov = ProvAndCity.getProvAbbrName req.param 'prov'
  subCity = req.param 'subCity'
  unless city and prov
    return error 'no city or prov'
  start_dt = req.param 'start_dt'
  end_dt = req.param 'end_dt'
  unless start_dt and end_dt
    return error 'no date range'
  ta1 = if req.param('ta1') then true else false
  ta2 = if req.param('ta2') then true else false
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    doc = {
      city,
      prov,
      uid,
      eml,
      ta1,
      ta2,
      start_dt,
      end_dt,
      status:'A'
    }
    doc.start_ts = new Date(start_dt)
    doc.end_ts = new Date(end_dt)
    doc.subCity = subCity if subCity
    if doc.start_ts > doc.end_ts
      return error 'date range incorrect!'
    ensureOnlyOneTa1 doc,(err,ret)->
      if err
        console.error err
        return error(err)
      TopupAgents.insertOne doc,(err,ret)->
        if err
          console.error
          return error(err)
        reloadAgents ()->
          resp.send {ok:1, msg:'Setted add top agents'}


VIEW 'manage',->
  div id:'vueBody',->
    text """<app-top-agents></app-top-agents>"""
  js '/js/entry/commons.js'
  js '/js/entry/appTopAgents.js'

VIEW 'all-topagents',->
  div id:'vueBody',->
    text """<app-all-top-agents></app-all-top-agents>"""
  js '/js/entry/commons.js'
  js '/js/entry/appAllTopAgents.js'

APP '1.5'
GET 'userfollow', (req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    eml = req.param('eml') or ''
    JS_ARRAYS = [
      '/js/vue3.min.js',
      '/js/admin/userfollowPage.js',
    ]
    resp.ckup 'admin/manageFollow/userFollow',{jsPaths:JS_ARRAYS,eml},'_',{}

POST 'getUserRolesFlwng',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    eml = req.body?.eml
    UserModel.findUserAndFollowersByEml eml,(err,user)->
      return respError {clientMsg:req.l10n(err), resp} if err
      resp.send {ok:1,user}

POST 'addRMRealtor2User',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    {eml,realtorEml} = req.body or {}
    UserModel.addRMRealtor2User {eml,realtorEml},(err)->
      return respError {clientMsg:req.l10n(err), resp} if err
      resp.send {ok:1}

POST 'removeUserFlwngRm',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    {eml,realtorEml} = req.body or {}
    UserModel.removeUserFlwngRm {eml,realtorEml},(err)->
      return respError {clientMsg:req.l10n(err), resp} if err
      resp.send {ok:1}

GET 'realtorfollow', (req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    eml = req.param('eml') or ''
    JS_ARRAYS = [
      '/js/vue3.min.js',
      '/js/admin/userfollowPage.js',
    ]
    resp.ckup 'admin/manageFollow/realtorFollow',{jsPaths:JS_ARRAYS,eml},'_',{}

POST 'removeRealtorFollowers',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    {eml,realtorEml} = req.body or {}
    UserModel.removeRealtorFollowers {eml},(err)->
      return respError {clientMsg:req.l10n(err), resp} if err
      resp.send {ok:1}

POST 'assignFollowers2NewRealtor',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    {eml,realtorEml} = req.body or {}
    UserModel.assignFollowers2NewRealtor {eml,realtorEml},(err)->
      return respError {clientMsg:req.l10n(err), resp} if err
      resp.send {ok:1}