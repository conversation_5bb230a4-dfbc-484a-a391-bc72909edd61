# User = COLLECTION 'chome', 'user'
UserModel = MODEL 'User'
libUser = INCLUDE 'libapp.user'

debug = console.log


VIEW 'becameVip', ->
  text """
  <style>
  nav#becameVip.smb-md { height:250px;}
  nav#becameVip li.tip{text-align:center;
    padding:0px 15px 20px 15px;
  }
  nav#becameVip li{padding-right:15px;}
  nav#becameVip .avt {height:70px;}
  nav#becameVip .table-view .table-view-cell .avt img{
    width: 80px;
    height: 80px;
  }
  nav#becameVip .desc p{margin-bottom:0;color:black}
  .table-view-cell.learn-more{color:#ff0000}
  </style>
  """
  div class:"backdrop", style:'display:none; z-index:1', ngClick:"toggleSMB()"
  classParam = "menu slide-menu-bottom"
  if @auto
    classParam += " smb-auto"
  else
    classParam += " smb-md"
  click = ngClick:"learnMore()"
  if @url
    click = ngClick:"learnMore('#{@url}')"
  nav class:classParam, id:"becameVip",->
    ul class:'table-view', ->
      li class:'tip table-view-cell', click, ->
        div class:'avt',->
          img src:'/img/icon_vip_only.png'
        div class:'desc',->
          p ->
            text @line1 or _ 'VIP Realtors only!'
          p ->
            text @line2 or _ "Become a VIP today?"
      li class:'learn-more table-view-cell', click, ->
        text _ 'Learn More'
      li class:'cancel table-view-cell', ngClick:"toggleSMB('close')", ->
        text _ 'Cancel'

APP '1.5'
APP 'crm',true
APP 'leads',true

GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'c-list',{title:req.l10n('Leads','crm'), user:user}

POST (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    #error = (e)-> resp.send {err:e.toString()}
    return resp.send {needLogin:true,err:"Need login"} unless user
    UserModel.getFollowingList user._id?.toString(),(err,leads)->
      if err
        console.error err
      # debug leads
      if leads?.length
        for l in leads
          l.fnm = libUser.fullNameOrNickname(req.locale(),l)
      resp.send {list:leads}
###
POST 'chist.json', (req, resp) ->
  cid = req.param 'cid'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    #find user profile in user profile
    #find listings in mls_treb
    resp.send {ok:1, l:[]}

VIEW 'c-hist-pane', ->
  div id:'cHistModal', class:'modal',->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right ', ngClick:"toggleModal('cHistModal')"
      h1 class: 'title',-> 'Records'
    div class: 'content', style:'padding:60px 5px 0 5px;',->
      text ckup 'index-search-list-pane'
###

VIEW 'c-list',->
  div ngApp:'crmApp', ngController:'crmCtrl', id:'clientListPage', class:'ng-cloak',->
    text ckup 'busy-icon'
    header class: 'bar bar-nav', ->
      # text ckup 'headerbarUserModalBtn'
      h1 class: 'title',-> text @title
      a class:'icon fa fa-back pull-left',ngClick: "goBack()", dataIgnore: 'push'#javascript:void(0); ,onclick:'goBack(event);'
    div class: 'bar bar-standard bar-footer', ->
      a class:"fa fa-plus",href:"/app-download?action=showSMB"#&uid=#{@user.id}

    text ckup 'becameVip'
    div class: 'content', ->
      text """
      <style>
      #clientListPage .card {border-radius:0}
      #clientListPage .img-wrapper, #clientListPage .contact{display:inline-block;}
      #clientListPage .img-wrapper {width: 50px; overflow:hidden; vertical-align: top;}
      #clientListPage .contact {width:calc(100% - 60px);font-size: 13px;  color: #666;}
      #clientListPage .thumbnail{width: 40px; height:40px;}
      #clientListPage .detail {padding: 10px;}
      #clientListPage .actions {border-top: 1px solid #efefef; padding: 7px;font-size:14px;}
      #clientListPage .actions > a{display:inline-block; width:50%;     text-align: center; color:#666}
      #clientListPage .actions > a i {padding-right:7px}
      #clientListPage .actions > a:last-child {border-left:1px solid #efefef; height: 100%;}
      #clientListPage .name {font-size: 17px; color:black}
      #clientListPage .contact label {margin-bottom:0;font-weight:normal;min-width:51px;}
      #clientListPage .contact label:after{content:':'; padding-right:5px;}
      </style>
      """
      div class: 'card leads-card', ngRepeat:'c in list', ->
        div class:'detail', ->
          div class:'img-wrapper',->
            img class:'thumbnail pull-left',ngSrc:"{{c.avt || '/img/logo.png' }}"
          div class:'contact', ->
            div class:'name',->
              '{{(c.fnm) ? (c.fnm) : c.nm}}'
              # span class:'pull-right fa fa-list-alt',style:"margin-top: -27px; color:#007AFF", ngClick:"showInBrowser(\"http://#{@req.hostname}/1.5/wecard/card/\", c.id || c._id)"
            div class:'normal-label',->
              label -> _ 'Email'
              a ngHref:'mailto:{{isArray(c.eml) ? c.eml[0] : c.eml}}',->
                text '{{isArray(c.eml) ? c.eml[0] : c.eml}}'
              # {{isArray(owner.eml) ? owner.eml[0] : owner.eml}}
            div class:'normal-label', ngShow:'c.mbl',->
              label -> _ 'Phone'
              a ngHref:'tel:{{c.mbl}}',-> text '{{c.mbl}}'
            div class:'comment',->
              label -> _ 'Source','crm'
              span -> 'RealMaster APP'
              span class:'pull-right',-> "{{c.flwng[0].ts | date : 'yyyy.M.d' }}"
        div class:'actions', ->
          a ngHref:'/1.5/iframe?u=/1.5/wesite/{{c.id || c._id}}', ->
            i class:'fa fa-list-alt'
            text _ 'WeCard'
          a href:'#', ngClick:'chat(c)',->
            i class:'fa fa-comments-o'
            text _ 'Send Msg'
  coffeejs {vars:{lang:@req.locale()}}, ->
    null
  js '/js/c_list.min.js'
