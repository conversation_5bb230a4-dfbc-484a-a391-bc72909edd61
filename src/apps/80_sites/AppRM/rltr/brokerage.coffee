###
save/get listing info for special fields
m_zh,salesperson,phone,company phone...

Brokerage Call List Pane
/1.5/brkg/
###
debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
# conf = CONFIG()
# BrkgCpny = COLLECTION 'vow','brkg_cpny'
# Offices = COLLECTION 'vow','offices'
# Offices.createIndex {'nm':1,'mt':-1},{background:true}

UserModel = MODEL 'User'
Brokerage = MODEL 'Brokerage'
# isAllowed = DEF '_access_allowed'

###
VIEW 'brkgPhoneList',->
  header class:'bar bar-nav',->
    h2 class:'title', style:'font-size: 13px; padding: 0 30px 0 10px; text-overflow: ellipsis;overflow: hidden;',-> '{{prop.rltr}}'
    span class:'icon icon-close  pull-right detailModal',style:'color:white;',ngClick:"close('#{@id or 'brkgPhoneList'}')"
  div class:'content',->
    ul class: 'table-view brkg-list', style:'margin-top:0; padding-bottom: 25px;',->
      li class: 'list-item table-view-cell', ngRepeat:'b in brkgs',style:'padding:11px 114px 11px 10px', ->
        a ngHref:"tel:+1{{b.tel}}",class:'btn', style:'border: 1px none;  color: #007Aff; margin-top: -12px;', -> "{{b.tel}}"
        span style:'font-size:13px;',-> "{{b.addr}}"
###
APP '1.5'
APP 'brkg',true
###
  param:
    nm: brokerage name, without ",BROKERAGE"
  return:
    ok: 0 or 1
    e: error
    brkgs:[] brokerage branch(s) list
      {addr:, tel:, fax:, nm:}
###
# POST /1.5/brkg/phones
POST 'phones',(req,resp)->
  findError = (msg)->
    return resp.send {ok:0,e:req.l10n(msg)}
  ERROR_MSG = 'Can not find brokerage'
  UserModel.appAuth {req,resp},(user)->
    return findError req.l10n('Please login') unless user
    type = req.param 'tp'
    if (not UserModel.accessAllowed('brokerage',user)) and (type isnt 'name')
      return findError req.l10n('Unauthorized')
    return findError req.l10n('Unknown Brokerage') unless brkgNm = req.param 'nm'
    if m = brkgNm.match /^(.*?)\,\s?BROKERAGE$/i # non greedy, some has double BROKERAGE at end
      brkgNm = m[1]
    # brkgNm = brkgNm.replace "`",'.?' # some company has ` in name
    propId = req.param 'id'
    try
      brkgs = await Brokerage.getPhones {propId,name:brkgNm}
    catch err
      debug.error err," propId:#{propId},brkgNm:#{brkgNm}"
      return findError ERROR_MSG
    if not brkgs.length
      debug.warn MSG_STRINGS.NOT_FOUND," propId:#{propId},brkgNm:#{brkgNm}"
    resp.send {ok:1,brkgs}
