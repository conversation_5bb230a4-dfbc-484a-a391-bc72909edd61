ForumModel = MODEL 'Forum'
config = CONFIG(['serverBase'])
puts = console.log
SysData = COLLECTION 'chome','sysdata'
async = require 'async'
updateConfig = {
  updateInterval :5 # minutes
}
FORUM_UPDATE_CONFIG_KEY = 'forum_vc_update_config'

processPost = (post, cb)->
  ForumModel.updateVcc post,(err,ret)->
    puts err if err
    cb()

processPosts = () ->
  setInterval(->
    #Forum.findToArray { del:{$ne:true}, $or : [{ts :{$gt: new Date(Date.now() - 40 * 3600 *1000) }}, {mt: {$gt: new Date(Date.now() - 10 * 3600 *1000) }}]} ,{fields:{vc:1,vcc:1,sticky:1,ts:1,mt:1,src:1}},(err, posts)->
    ForumModel.findRecentForums (err,posts)->
      async.eachSeries posts, ((post, next) ->
        processPost post, next
      ), (err) ->
        if err
          console.error err
          throw err
   , updateConfig.updateInterval * 60000)


if config.serverBase?.masterMode and not config.serverBase?.satelliteMode
  # only run on master server
  SysData.findOne {_id:FORUM_UPDATE_CONFIG_KEY}, (err, ret) ->
    if err
      puts 'db error'
      console.error err
      throw err
    if not ret # just for first time
      SysData.insertOne {_id:FORUM_UPDATE_CONFIG_KEY, config: updateConfig }, (err,ret2) ->
        processPosts()
    else
      updateConfig = ret.config
      processPosts()
