config = CONFIG('contact')
helpers = INCLUDE 'lib.helpers'
wordpressHelper = INCLUDE 'libapp.wordpressHelper'

# accessAllowed = DEF '_access_allowed'

jwt = require 'jsonwebtoken'
async = require 'async'
URL = require('url')
# User = COLLECTION 'chome', 'user'
SysData = COLLECTION 'chome','sysdata'
UserProfile = COLLECTION 'chome', 'user_profile'
Forum = COLLECTION  'chome','forum'

ForumModel = MODEL 'Forum'
UserModel = MODEL 'User'

ACCESS_DENIED = 'Access Denied'
BAD_PARAMETER = 'Bad Parameter'
DB_ERROR = 'DB Error'


WP_HOSTS=null
adminUid = null
wordpressHelper.setUpCollection {UserProfile:UserProfile, Forum: Forum, SysData: SysData}

findError = (msg, resp, type, err)->
  if type == 'db'
    console.error err
  resp.send {e: msg?.toString()}

# find all hosts from userprofile, saved in cache,
# used for refresh category and delete and comments. also used for update post if no username specified
# TODO: should be server specific, config.isChinaMode
wordpressHelper.fetchWpCategories null, ()->


# DEF 'addWpComment', addWpComment
# DEF 'deleteWpComment', deleteWpComment
# DEF 'deleteFromWp', deleteFromWp

APP '1.5'
APP 'forum', true

POST 'refreshWpCategory', (req, resp) ->
  wordpressHelper.fetchWpCategories null, (err, result)->
    return resp.send {ok:0, err: err} if err
    resp.send {ok:1, ret: result}

# /1.5/forum/pushToWp
# push forum content to wordpress
POST 'pushToWp', (req, resp) ->
  _id = req.body._id
  return findError req.l10n(BAD_PARAMETER), resp unless _id
  UserModel.appAuth {req,resp}, (user)->
    return findError req.l10n(ACCESS_DENIED), resp unless user and (UserModel.accessAllowed 'forumAdmin', user)
    UserModel.findProfileById user._id,{},(err, profile)->
      return findError req.l10n(ACCESS_DENIED), resp unless (profile.wpHost and profile.wpUsername and profile.wpPwd)
      # find post
      wordpressHelper.findForumAndPushToWp  _id, profile, (ret)->
        resp.send ret

PUT 'wordPressEcho', (req, resp)->
  return resp.send {ok:0} unless p = req.body
  host = p.site_url
  wordpressHelper.isValidToken req, host, (token)->
    return resp.send {ok:0, err: 'token validation failed'} unless token
    # host = req.headers['x-real-ip']
    doUpdate = (p) ->
      ForumModel.updateFromWordpress p,(err,ret)->
        return findError req.l10n(DB_ERROR), resp,'db', err if err
        resp.send {ok:1}
    #set <NAME_EMAIL>
    if not adminUid
      UserModel.findByEml config.contact?.defaultRTEmail,{}, (err, user)->
        return findError req.l10n(DB_ERROR), resp,'db', err if err
        p.adminUid = user._id
        doUpdate(p)
    else
      doUpdate(p)

PUT 'wordPressCmntEcho', (req, resp)->
  headers = req.headers
  return resp.send {ok:0} unless p = req.body
  p = req.body
  host = p.site_url

  wordpressHelper.isValidToken req, host, (token)->
    return resp.send {ok:0} unless token
    ForumModel.updateCommentFromWordpress p, (err,ret)->
      return findError req.l10n(DB_ERROR), resp,'db', err if err
      resp.send {ok:1}


DEL 'wordPressEcho',(req, resp) ->
  # console.log 'delPostFromWp'
  # console.log req.body
  return resp.send {ok:0} unless p = req.body
  host = p.site_url
  wordpressHelper.isValidToken req, host, (token)->
    return resp.send {ok:0} unless token
    id = p.ID
    # console.log host
    return resp.send {ok:0} unless id
    ForumModel.deleteFromWordpress {id,host},(err,ret)->
      return findError req.l10n(DB_ERROR), resp,'db', err if err
      resp.send {ok:1}

DEL 'wordPressCmntEcho',(req, resp) ->
  # console.log 'delCmntFromWp'
  # console.log req.body
  return resp.send {ok:0} unless p = req.body
  host = p.site_url
  wordpressHelper.isValidToken req, host, (token)->
    return resp.send {ok:0} unless token
    id = p.comment_id?.toString()+"@"+host
    wpid = p.comment_post_ID
    return resp.send {ok:0} unless id
    # console.log host
    #{'wpHosts.wpid': wpid, 'cmnts.wpHosts': {$regex : id }} not woking in d3
    ForumModel.deleteCommentFromWordpress id,(err,ret)->
      return findError req.l10n(DB_ERROR), resp,'db', err if err
      resp.send {ok:1}
