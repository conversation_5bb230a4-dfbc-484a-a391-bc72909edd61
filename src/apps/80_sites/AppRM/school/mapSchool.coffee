###
Native MapSchool support
###

helpers = INCLUDE 'lib.helpers'
School = MODEL 'School'
async = require 'async'
debug = DEBUG()

ADDSTDFN 'findMapSchools',{needReq:true},(opt,cb)->
  limit = if opt.nm then 10 else 20
  limit = parseInt(opt.limit) if opt.limit?
  limitOrig = parseInt limit
  req = opt.req
  ret = {total:0,items:[],schCnt:0}
  isOnlyCnt = false
  types = [
    ['university','findUniversities',''],
    ['college','findUniversities',''],
    ['privateSchool','findPrivateSchools',''],
    ['school','findSchools',''],
  ]
  # console.log('++++limit=',limitOrig)
  # for i in types
    # debug.info 'await findMapSchools',opt[i]
  hasEnough = ()->
    ret.total >= limitOrig
  adjustLimit = (cnt)->
    limit = Math.max(limit-cnt,0)
  try
    list = []
    cnt = 0
    if opt.university
      param = opt.university.q
      {schList:univList, cnt:univCnt} = await School.getUniversitiesWithCnt {
        nm:param.nm,
        bbox:param.bbox,
        locale:req.locale(),
        showOnMap:true,
        tp:param.tp,
        limit,
      }
      # console.log('+++++',univ)
      cnt = univList.length
      ret.total += cnt
      ret.schCnt += univCnt
      # debug.info 'univ count =>',cnt
      ret.university = univList
      adjustLimit(cnt)
    isOnlyCnt = hasEnough()
    if opt.college
      param = opt.college.q
      {schList:collegelist, cnt:collCnt} = await School.getUniversitiesWithCnt {
        nm:param.nm,
        bbox:param.bbox,
        locale:req.locale(),
        showOnMap:true,
        tp:param.tp,
        limit,
        isOnlyCnt
      }
      unless isOnlyCnt
        cnt = collegelist.length
        ret.total += cnt
        # debug.info 'college count =>',cnt
        ret.college = collegelist
        adjustLimit(cnt)
      ret.schCnt += collCnt
    isOnlyCnt = hasEnough()
    if opt.school
      param = opt.school.q
      {schList:publicList, cnt:publiCnt} = await School.getPublicSchoolsWithCnt {
        nm:param.nm,
        city:param.city,
        bbox:param.bbox,
        filter:param.filter,
        sort: param.sort,
        limit:limit,
        locale:req.locale(),
        isOnlyCnt
      }
      unless isOnlyCnt
        cnt = publicList.length
        ret.total += cnt
        # debug.info 'public count =>',cnt
        adjustLimit(cnt)
        ret.school = publicList
      ret.schCnt += publiCnt
    isOnlyCnt = hasEnough()
    if opt.privateSchool
      param = opt.privateSchool.q
      {schList:privateList, cnt:privCnt} = await School.getPrivateSchoolsWithCnt {
        nm:param.nm,
        bbox:param.bbox,
        filter:param.filter,
        locale:req.locale(),
        limit,
        isOnlyCnt
        }
      unless isOnlyCnt
        cnt = privateList.length
        ret.total += cnt
        # debug.info 'private count =>',cnt
        ret.privateSchool = privateList
      ret.schCnt += privCnt
    return cb(null,ret)
  catch err
    # debug.info 'async findMapSchools done.',err,ret
    if err
      return cb err
    return cb null,ret

ADDSTDFN 'findSchools',{needReq:true},(opt,cb)->
  limit = if opt.nm then 10 else 50
  req = opt.req
  try
    returnSchools = await School.getPublicSchools {
      nm:opt.nm,
      city:opt.city,
      bbox:opt.bbox,
      filter:opt.filter,
      sort: opt.sort,
      limit:limit,
      locale:req.locale()
      }
  catch err
    return cb err
  return cb null,returnSchools

ADDSTDFN 'getSchBnd',{needReq:true},(opt,cb)->
  req = opt.req
  try
    school = await School.getPublicSchoolById {
      id:opt.schId,
      schBn: opt.bn,
      locale:req.locale(),
      showCurBnTxt: opt.showCurBnTxt
      }
  catch err
    return cb err
  return cb null,school

ADDSTDFN 'getHomeSchools',{needReq:true},(opt,cb)->
  req = opt.req
  try
    schools = await School.getHomeSchools {
      loc:opt.loc,
      locale:req.locale()
      }
  catch err
    return cb err
  return cb null, schools


ADDSTDFN 'findPrivateSchools',{needReq:true},(opt,cb)->
  req = opt.req
  try
    {schList:list, cnt} = await School.getPrivateSchoolsWithCnt {
      nm:opt.nm,
      bbox:opt.bbox,
      locale:req.locale()
    }
  catch err
    return cb err
  return cb null,{cnt:cnt,items:list}

ADDSTDFN 'getPrivateSchool',{needReq:true},(opt,cb)->
  req = opt.req
  try
    sch = await School.getPrivateSchoolById {id:opt.schId,locale:req.locale()}
  catch err
    return cb err
  if not sch
    return cb null,{}
  return cb null,sch

ADDSTDFN 'findUniversities',{needReq:true},(opt,cb)->
  req = opt.req
  try
    {schList:list, cnt} = await School.getUniversitiesWithCnt {
      nm:opt.nm,
      bbox:opt.bbox,
      locale:req.locale(),
      showOnMap:true,
      tp:opt.tp,
      }
  catch err
    return cb err
  return cb null,{cnt:cnt,items:list}

ADDSTDFN 'getUniversity',{needReq:true},(opt,cb)->
  req = opt.req
  try
    sch = await School.getUniversityById {id:opt.schId,locale:req.locale()}
  catch err
    return cb err
  return cb null,sch