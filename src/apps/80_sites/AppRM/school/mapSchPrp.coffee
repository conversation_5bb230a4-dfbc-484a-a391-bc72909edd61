
helpers = INCLUDE 'lib.helpers'
Schools = COLLECTION 'vow','school'



UserModel = MODEL 'User'
getCityList = DEF 'getCityList'
getPropertyTypes = DEF 'getPropertyTypeArray'
getMPropertyTypes = DEF 'getMPropertyTypeArray'
# countIDX = DEF 'countIDX'
getWXConfig = DEF 'getWXConfig'


APP '1.5'
APP 'srh',true
# GET (req,resp)-> show_map req,resp
# used in rm1Market and rm2PropDetail
# NOTE: rm2PropDetail/proplibAfter not used
###
#1.5/srh/findSchools,
POST 'findSchools',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    d = req.body or {}
    if d.mode is 'hotSch'
      q = {$and:[{'lat':{$lt:d.lat1}},{'lat':{$gt:d.lat2}},{'lng':{$lt:d.lng1}},{'lng':{$gt:d.lng2}}],IsActive:1,hot:1}
      #db.school.updateMany({catholic:0,$or:[{'fraser.0.firank':{$lt:100}},{'eqao.0.g6rank':{$lt:100}},{'eqao.0.g3rank':{$lt:100}},{'eqao.0.g9rank':{$lt:50}}]},{$set:{hot:1}},{multi:1})
    else if d.mode is 'mapBnds'
      q = {$and:[{'lat':{$lt:d.lat1}},{'lat':{$gt:d.lat2}},{'lng':{$lt:d.lng1}},{'lng':{$gt:d.lng2}}],IsActive:1}
    else
      q = {'bns.sw.0':{$lte:d.loc[0]},'bns.sw.1':{$lte:d.loc[1]},'bns.ne.0':{$gte:d.loc[0]},'bns.ne.1':{$gte:d.loc[1]},IsActive:1}
    Schools.findToArray q,
      {fields:{_id:1,addr:1,bns:1,catholic:1,city:1,nm:1,loc:1,url:1,addr:1,tel:1,gf:1,gt:1,fi:1,ef:1,fraser:1,eqao:1,ele:1,mid:1,hgh:1,hot:1},sort:{catholic:1,ef:1,fi:1,gf:1}},
      (err,results)->
        if err then console.error err
        if not results? then return resp.send {e:err.toString()}
        bnds = []
        schs = {}
        for sch in results
          if (frs = sch.fraser?[0])?
            sch.firate = frs.firank + '/' + frs.fitotal
          if (eqao = sch.eqao?[0])?
            if eqao.g6rank and eqao.g6total
              sch.eqaorate = eqao.g6rank + '/' + eqao.g6total + " G6"
            else if eqao.g3rank and eqao.g3total
              sch.eqaorate = eqao.g3rank + '/' + eqao.g3total + " G3"
            else if eqao.g9rank and eqao.g9total
              sch.eqaorate = eqao.g9rank + '/' + eqao.g9total + " G9"
          sch.c = sch.catholic
          delete sch.catholic
          for bn in sch.bns
            if (d.mode in ['hotSch','mapBnds']) or helpers.isInBoundary bn.bn,d.loc
              bn._id = sch._id
              for k in ['bns','bn','ref','catholic','c','fraser','Comments','exbnid','exclude']
                delete bn[k]
              if bn.useSch
                for k in ['gf','gt','ef','fi','c']
                  bn[k] = sch[k]
              else
                for k in ['gf','gt','ef','fi','c']
                  bn[k] ?= sch[k]
              bnds.push bn
              schs[sch._id] = sch # only school in boundary
          delete sch.bns
          delete sch.fraser
          delete sch.eqao
        bnds.sort (a,b)->
          r = a.c - b.c
          if r is 0 then r = a.ef - b.ef
          if r is 0 then r = a.fi - b.fi
          if r is 0 then r = a.gf - b.gf
          r
        resp.send {bnds:bnds,schs:schs}
###