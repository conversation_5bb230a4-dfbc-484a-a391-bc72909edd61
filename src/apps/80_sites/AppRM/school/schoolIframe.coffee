#for outside use
mapServer =  INCLUDE 'lib.mapServer'
UserModel = MODEL 'User'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

VIEW 'embededSchoolList',->
  coffeejs {vars:{
    share:@share,
    bar:@bar
    }}, ->
      null
  div id:'vueBody',->
    text '''<embeded-school-list></embeded-school-list>'''
  text ckup 'mapboxJs'
  js '/js/map/mapbox.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/embededSchoolList.js'

APP 'school'

# GET 'test-embed',(req,resp)->
#   resp.ckup 'testEmbed',{},'_',{}

# VIEW "testEmbed",->
#   div -> text "#{new Date()}"
#   iframe style:'width:100%;height:100vh;', src:'http://d7ww.realmaster.com:8082/school/embed?apts=1&lat=43.8151791&lng=-79.4577973&lang=en&tour_type=brand'

#/school/embed
GET 'embed',(req, resp)->
  if (/houssmax/.test req.headers?.referer) or req.param('apts')

    if (lang = req.param 'lang') and (lang in LANGUAGE_LIST)
      req.setLocale lang
    mapboxkey = mapServer.getMapboxAPIKey {isHousemax:1}
    # resp.ckup 'embededSchoolList', {googleapiJs:googleapiJs, share:1, bar:1},'_',{noAngular:true}
    resp.ckup 'embededSchoolList', {share:1},'_',{noAngular:true,mapboxkey}
  else
    resp.send 'Invalid Request 404!'

APP '1.5'
#1.5/schoolSearch
GET 'schoolSearch',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    isApp = req.getDevType() is 'app'
    mapboxkey = mapServer.getMapboxAPIKey {isApp}
    resp.ckup 'embededSchoolList', {},'_',{noAngular:true,mapboxkey}
