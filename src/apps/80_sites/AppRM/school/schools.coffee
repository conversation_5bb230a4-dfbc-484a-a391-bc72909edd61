# conf = CONFIG()
helpersStr = INCLUDE 'lib.helpers_string'
libUser = INCLUDE 'libapp.user'
School = MODEL 'School'
UserModel = MODEL 'User'
limit_per_page = 10
school_city_list = []
_school_city_list_resource = 'school_city_list_resource'
mapServer =  INCLUDE 'lib.mapServer'
dataMethods = DEF 'dataMethods'
getWXConfig = DEF 'getWXConfig'
GroupModel = MODEL 'Group'
TokenModel = MODEL 'Token'
gSchoolTokenKey = 'schoolReport'
gPrivateSchoolTokenKey = 'privateSchoolReport'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'


getDispVar = DEF 'getDispVar'
setCmntThumbUpAndRating = DEF 'setCmntThumbUpAndRating'
getMyRating = DEF 'getMyRating'
processCmnts = DEF 'processCmnts'

limiter = INCLUDE 'lib.limiter'
SysNotifyModel = MODEL 'SysNotify'
SchoolCensusModel = MODEL 'SchoolCensus'
# accessAllowed = DEF '_access_allowed'

HARD_LIMIT = 60
SOFT_LIMIT = 12

###*
 * 检查用户访问限制并获取学校信息
 * @param {Object} req - 请求对象
 * @param {Object} user - 用户对象
 * @param {String} id - 学校ID
 * @param {Function} cb - 回调函数
 * @description 
 * - 检查用户是否达到每日访问限制
 * - 根据不同用户角色设置不同的访问限制
 * - 获取私立学校详细信息
###
checklimitAndGetSchool = (req, user, id, cb)->
  # ids = id.split(',') only support single id
  if req.param 'share'
    try
      school = await getPrivateSchoolById(req, id)
    catch err
      return cb err
    cb null, school
  else
    limit = SOFT_LIMIT
    UserModel.findByTskDnAndId user._id,'pSch',(err,found)->
      if err then cb err
      limit = HARD_LIMIT if found
      if req.isAllowed 'psch100'
        limit = 100
      limiter.check user,'pSch',limit,(u,evt,cnt,notify)->
        if u
          if cnt >= HARD_LIMIT # reach HARD_LIMIT
            return cb req.l10n 'Reached Daily Usage Limit'
          if notify
            return cb null,null, \
              "#{evt} reached limit(#{cnt}). #{req.remoteIP()} User:#{u._id}:#{u.eml} #{u.nm_zh} #{u.nm_en} #{u.nm} #{u.roles}"
          # reached soft limit
          return cb {
            url:'/event?tpl=privateSchools&u=/1.5/school/private/shared&inframe=1',
            se:req.l10n('Reached Daily Usage Limit')
            }
        try
          school = await getPrivateSchoolById(req, id)
        catch err
          return cb err
        cb null, school


###*
 * 获取私立学校详情页面
 * @param {Object} req - 请求对象
 * @param {Object} resp - 响应对象
 * @description
 * - 验证用户权限和访问限制
 * - 获取学校详细信息
 * - 渲染学校详情页面
###
getPrivateSchoolDetail = (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  id = req.param 'id'
  isApp = req.getDevType() is 'app'
  isNative = req.param('src') is 'nativeMap'

  if not id
    return resp.ckup 'generalMessage',{nobar:1, msg: req.l10n 'Not found'}

  UserModel.appAuth {req,resp},(user)->
    #share do not need login
    if (not user) and (not req.param 'share')
      if shouldLogin(req,user)
        return resp.redirect '/1.5/user/login'
    
    isAdmin = req.isAllowed('admin')
    try
      isInGroup = await GroupModel.isInGroup {uid:user._id, groupName:':inReal'}
    catch err
      debug.error 'GroupModel.isInGroup',err
    # 判断权限是否能查看rmrank
    if isAdmin or isInGroup
      req.canExchange = 1
    checklimitAndGetSchool req, user, id, (err,sch, notify)->
      if err
        if err.url
          return resp.redirect err.url
        if 'string' is typeof err
          return resp.ckup 'generalMessage', {nobar:1, msg:err}
        else
          return resp.ckup 'generalMessage', {nobar:1, msg:req.l10n 'Error'}
      if not sch
        return resp.ckup 'generalMessage', {nobar:1, msg:req.l10n 'Not found'}
      if notify
        SysNotifyModel.notifyAdmin notify
        return resp.ckup 'generalMessage',
          {nobar:1,msg:req.l10n 'Reached Daily Usage Limit'}
      fields =
        #eml:'Email'
        #tel:'Phone'
        sex:'Gender'
        grd:'Grade'
        fndd:'Founded'
        religion:'Religious Affiliation'
        boarding:'Boarding'
        tuitnPreYear:'Yearly Tuition'
        enrlBoarding:'Number of Boarding Students'
        enrlBoardingGrd:'Boarding Grades'
        tuitnBoarding:'Boarding School Tuition'
        enrlDay:'Number of Day Students'
        enrlDayGrd:'Day Grades'
        tuitn:'Day School Tuition'
        admRt:'Acceptance Rates'
        admIntvw:'Request Interview'
        admSSAT:'Request SSAT'
        # hot:'Hot School'
      hasDetail = false
      if req.canExchange
        try
          sch = await checkSchoolToken(sch, user._id)
        catch err #ignore this error for inc vc
          debug.error err
    
      # getMyRating sch, user
      # processCnmts sch, req

      # if sch.cmnts
      #   for cmnt,idx in sch.cmnts
      #     cmnt.del = cmnt.del or false
      #     setCmntThumbUpAndRating sch.rating, user, cmnt, idx
      # console.log sch
      for fl,v of fields
        if sch?[fl]
          sch.hasDetail = true
      inFrame = req.param('inFrame')
      if sch.tuitnPreYear
        delete sch.tuitnBoarding
        delete sch.tuitn
      # iswebAdmin = req.param('iswebAdmin')
      # isForumAdmin = accessAllowed 'forumAdmin',user
      sch.loc = [sch.lat,sch.lng]
      getDispVar req, user, (err, ret)->
        #TODO:err and ret check？
        # 返回不会存在err情况，均返回null，ret可能会有err
        dispVar = ret?.datas or {}
        opt =
          dispVar:dispVar,
          title:"#{req.l10n('RealMaster Private School:','school')}#{sch.nm}",
          post:sch,
          inFrame:inFrame
          bar:req.param('bar')

        opt.isApp = isApp
        opt.curPSch = sch
        opt.fields = fields
        opt.shareHost = req.shareHost()
        opt.zoom = if req.isChinaIP() then 14 else 11
        opt.shareImage = req.shareHost()+'/img/school/school_forum_p.png'
        opt.isNative = isNative
        cfg =
          shareImage:opt.shareImage,
          noAngular:1,
          noUserModal:1,
          noref:1,
          desc:"#{sch.nm},#{sch.addr},#{sch.city},#{sch.prov}"

        # if user
        #   UserModel.logForumHistory user._id, {postid:sch._id.toString()}, () ->
        if req.getDevType() is 'app'
          return resp.ckup 'private-detail',opt, '_', cfg
        getWXConfig req,req.fullUrl(),(err,wxcfg)->
          cfg.wxcfg = wxcfg
          resp.ckup 'private-detail',opt,'_',cfg


VIEW 'private-detail',->
  css '/css/apps/privateSchool.css'
  # css '/css/apps/forum.css'
  # css '/css/apps/forumDetail.css'
  # css '/css/apps/rating.css'
  div id:'event-detail',->
    curPSch = @curPSch
    dispVar = @dispVar
    if @bar
      header class: 'bar bar-nav', ->
        h1 class: 'title', -> text _('RealMaster')
    div class:'WSBridge', style:'display:none',->
      span id: 'share-title-en', ->
        text 'RealMaster Private School: '
        text @curPSch.nm
      span id: 'share-desc-en',->
        text @curPSch.addr+','
        text (@curPSch.city_en or @curPSch.city)+','
        text (@curPSch.prov_en or @curPSch.prov)
      span id: 'share-title', ->
        text _('RealMaster Private School: ','pschool')
        text @curPSch.nm
      span id: 'share-desc',->
        text @curPSch.addr+','
        text @curPSch.city+','
        text @curPSch.prov
      # span id: 'share-data',->
      #   text 'id='+@curPSch._id
      span id: 'share-image',->
        text @shareImage
    div class: 'content',->
      div id:'vueBody',->
        text """<app-school-detail-page></app-school-detail-page>"""

      div class:'detail',->
        if curPSch.hasDetail
          div class:'head',->
            _('School Information')
        ul class:'table-view private',->
          for fl,v of @fields
            if curPSch[fl]
              li class:'list-item table-view-cell',->
                label ->
                  _(v)
                span ->
                  curPSch[fl]
      if curPSch.vc
        div class:'admin',style:'padding: 10px 15px;', ->
          text curPSch.vcd
          text '/'
          text curPSch.vc

      # div id:"comment", style:"background-color:white;margin-top:17px;",->
      #   text """<common-comments></common-comments>"""
      text ckup  'schooldisclaimer'
    div id:'footer',->
      text """<school-detail-footer></school-detail-footer>"""

  text ckup 'clickDirect'
  coffeejs {vars: {post: Object.assign({}, @curPSch),dispVar: @dispVar,col:'psch'}}, ->
    null
  js '/libs/htmx-1.7.0.min.js'
  js '/js/Chart-3.6.2.min.js'
  js '/js/chartjs-plugin-datalabels.js'
  js '/js/entry/commons.js'
  js '/js/entry/appSchoolDetailPage.js'

VIEW 'schoolDetailNew',->
  # css '/css/apps/forumDetail.css'
  # css '/css/apps/rating.css'
  if @bar
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', href: 'javascript:;', onclick:'goBack()'
  div class:'WSBridge', style:'display:none',->
    span id: 'share-title-en', ->
      text 'RealMaster Public School:'
      text @sch.nm
    span id: 'share-desc-en',->
      # text @sch.addr+','+ @sch.city+','+@sch.prov
      text "#{@sch.addr}, #{@sch.city_en or @sch.city}, #{@sch.prov_en or @sch.prov}"
    span id: 'share-title', ->
      text _('RealMaster Public School:','pschool')
      text @sch.nm
    span id: 'share-desc',->
      # text @sch.addr+','+@sch.city+','+@sch.prov
      text "#{@sch.addr},#{@sch.city},#{@sch.prov}" #TODO
    span id: 'share-image',->
      text @shareImage
    # TODO: track school share data stats
    # span id: 'share-data',->
    #   text 'id='+@sch._id
  div class:'content',style:'background-color:#f1f1f1;',->
    div id:'vueBody',->
      text """<app-school-detail-page></app-school-detail-page>"""
    # div id:"comment", style:"background-color:white;",->
    #   text """<common-comments></common-comments>"""
    text ckup 'schooldisclaimer'
  div id:"footer",->
    text """<school-detail-footer></school-detail-footer>"""
  # coffeejs {vars:{id:@id}},->
  #   null
  # js '/js/lz-string.min.js'
  # js '/js/calculate.min.js'
  text ckup 'clickDirect'
  coffeejs {vars:{
    sch:Object.assign {}, @sch
    board:Object.assign {}, @board
    dispVar: @dispVar,
    col:'sch',
    bar:@bar,
    }
  }, ->
    goBack = ->
      window.history.back()
    null
  js '/libs/htmx-1.7.0.min.js'
  js '/js/Chart-3.6.2.min.js'
  js '/js/chartjs-plugin-datalabels.js'
  js '/js/entry/commons.js'
  js '/js/entry/appSchoolDetailPage.js'



VIEW 'show-school-eqao',->
  titlemap = {yearmark:"Year",rank:'Rank',total:'Total',aw:"Writing",ar:"Reading",am:"Math",stu:"Students",acm:"Academic Math",apm:"Applied Math",acstu:"Academic Students",apstu:'Applied Students',acrank:'Academic Rank',aprank:'Applied Rank'}
  titlesg3 = ['rank','total','ar','aw','am','stu']
  titlesg9 = ['rank','total','acm','acstu','acrank','apm','apstu','aprank']
  titles =
    g3: titlesg3
    g6: titlesg3
    g9: titlesg9
  show_it = (g,eqao)->
    div class:'table-wrapper', style:'font-size:14px;', ngShow:'sch.eqao[0].' + g + 'total',->
      table class:'table table-striped',->
        caption ->
          text "EQAO Scores #{g.toUpperCase()}"
        text "<col width='50px'>"
        thead style:'font-size:0.8em;font-weight:normal;',->
          tr ->
            th -> text ''
            th ngRepeat:"r in sch.eqao" ,->
              text "{{r.yearmark}}"
        tbody ->
          tmp = 'N/A'

          for r in titles[g]
            tr ->
              td ->
                text titlemap[r]
              td ngRepeat:"t in sch.eqao" ,->
                if r.slice(-4) is 'rank'
                  tmp = g + r.slice(0,-4) + 'total'
                  text   "{{t." + g + r + "}}"
                else if r is 'total'
                  text "{{t." + tmp + "}}"
                else if r is 'yearmark'
                  text "{{t." + r + "}}"
                else
                  text "{{t." + g + r + "}}"

  show_it 'g3'
  show_it 'g6'
  show_it 'g9'


VIEW 'show-school-fraser',->
  titlemap =
    total:'Total',
    firank:'Rank',
    fitotal:'Total',
    fiincome:'Parents\' Income',
    firate:'Rating',
    filast5rank:'Last 5 Year Rank',
    filast5total:'Last 5 Year Total',
    fiesl:'ESL %',
    fispecial:'Special Edu %',
    yearmark:'Year'
  rows = ['firank','total','firate','fiincome','fiesl','fispecial'] #filast5rank
  div class:'table-wrapper',style:'font-size:14px;',ngShow:'sch.fraser',->
    table class:'table table-striped',->
      text "<col width='50px'>"
      thead style:'font-size:0.8em;font-weight:normal;',->
        tr ->
          th ->
            text ' '
          th ngRepeat:'r in sch.fraser',->
            text '{{r.yearmark}}'
        tbody ->
        for r in rows
          tr ->
            td ->
              text titlemap[r]

              td ngRepeat:'t in sch.fraser' ,->
                #text t[r]
                if r.slice(-4) is 'rank'
                  text '{{t.' + r + '}}'
                else if r is 'total'
                  text '{{t.fitotal}}'
                else if r is 'fiincome'
                  text '{{t.' + r + '/1000  }} k'
                else
                  text '{{t.' + r + '}}'



VIEW 'schoolListPage',->
  div id:'vueBody',->
    text '<app-school-list></app-school-list>'
  css '/css/sprite.min.css'
  js '/js/entry/commons.js'
  js '/js/entry/appSchoolList.js'

VIEW 'clickDirect',->
  coffeejs {}, ->
    clickDirect = (isapp, url,isNative)->
      # console.log isNative
      if (isapp)
        callBackStr = ":ctx:cmd-redirect:"+url;
        # // console.log('window.rmCall: '+callBackStr);
        if isNative
          # console.log url.split('?')?[1]
          callBackStr = ':ctx:'+url.split('?')?[1]
        if window.rmCall
          window.rmCall(callBackStr)
      else
        window.location = url
    null

VIEW 'schooldisclaimer',->
  div class:'claim',style:'padding-bottom: 60px; background-color:#f1f1f1;', ->
    p style:'padding: 0 15px;line-height: 1.1em;font-size: 10px;color: #ADACAC',->
     _('All the content here is from internet public sources. No guarantee is given that the information is correct, complete, and/or up-to-date. Any reliance you place on the information here is strictly at your own risk. RealMaster is not liable for the information provided here.')

VIEW 'university-detail',->
  css '/css/apps/privateSchool.css'
  div id:'event-detail',->
    sch = @sch
    if @bar
      header class: 'bar bar-nav', ->
        h1 class: 'title', -> text _('RealMaster')

    div class:'WSBridge', style:'display:none',->
      span id: 'share-title-en', ->
        text 'RealMaster University: '
        text @sch.nm
      span id: 'share-image',->
        text @sch.logo

    div class: 'content',->
      div class: 'info university-header',->
        div class: 'name',->
          h4 class:'name',->
            sch.nm
        div class:'school',->
          for tag in sch.tags
            span style:"color:#{tag.textColor};background:#{tag.color}",->
              text tag.nm
      div class: 'detail',->
        if sch.tuit or sch.stdnt
          div class:'head',->
            _('School Information')
        ul class:'table-view',->
          if sch.tuit
            if sch.tuit.dmstc
              div class:'head', -> text _ 'Domestic Tuition', 'school'
              if sch.tuit.dmstc.grd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Graduate Students', 'school'
                  span ->
                    sch.tuit.dmstc.grd
              if sch.tuit.dmstc.ugrd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Undergraduate Students', 'school'
                  span ->
                    sch.tuit.dmstc.ugrd
            if sch.tuit.int
              div class:'head', -> text _ 'International Tuition', 'school'
              if sch.tuit.int.grd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Graduate Students', 'school'
                  span ->
                    sch.tuit.int.grd
              if sch.tuit.int.ugrd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Undergraduate Students', 'school'
                  span ->
                    sch.tuit.int.ugrd
          if sch.stdnt
            if sch.stdnt.fltm
              div class:'head', -> text _ 'Full Time Students', 'school'
              if sch.stdnt.fltm.grd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Graduate', 'school'
                  span ->
                    sch.stdnt.fltm.grd
              if sch.stdnt.fltm.ugrd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Undergraduate', 'school'
                  span ->
                    sch.stdnt.fltm.ugrd
            if sch.stdnt and sch.stdnt.prtm
              if sch.stdnt.prtm
                div class:'head', -> text _ 'Part Time Students', 'school'
              if sch.stdnt.prtm.grd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Graduate', 'school'
                  span ->
                    sch.stdnt.prtm.grd
              if sch.stdnt.prtm.ugrd
                li class:'list-item table-view-cell',->
                  text '- '
                  label ->
                    _ 'Undergraduate', 'school'
                  span ->
                    sch.stdnt.prtm.ugrd


      div class: 'detail',->
        div class:'head',->
          _('Campus', 'school')
        for campus in sch.campus
          div class: 'info',->
            if campus.nm
              div class: 'name',->
                campus.nm
            div class:'addr',->
              text campus.addr+', '
              text campus.city+', '
              text campus.prov
            if campus.tel or campus.eml or campus.url
              ul class:'table-view',->
                if campus.tel
                  li class:'list-item table-view-cell',->
                    label ->
                      _('Tel')
                    a class:'link', href:"tel:#{campus.tel}",->
                      campus.tel
                if campus.eml
                  li class:'list-item table-view-cell',->
                    label ->
                      _('Email')
                    a class: 'link', href:"mailto:#{campus.eml}",->
                      campus.eml
                if campus.website
                  li class:'list-item table-view-cell',->
                    label ->
                      _('Website')
                    a class: 'link', onclick:"RMSrv.showInBrowser('#{campus.website}')",->
                      campus.website
          if campus.lat and campus.lng
            div class:'actions university-action',->
              div ->
                a  onclick:"clickDirect(#{@isApp},'/1.5/mapSearch?zoom=15&saletp=sale&loc=#{campus.lat},#{campus.lng}',#{@isNative})", href: 'javascript:void(0);',->
                  # i class: 'fa fa-list-ul'
                  text _('SALE','nearby used')
              div class:'rental',->
                a class:'rental', onclick:"clickDirect(#{@isApp},'/1.5/mapSearch?zoom=15&saletp=lease&loc=#{campus.lat},#{campus.lng}',#{@isNative})",href: 'javascript:void(0);',->
                  # i class: 'fa fa-list-ul'
                  text _('RENT','nearby rent')

      if sch.vc
        div class:'admin', ->
          text sch.vcd
          text '/'
          text sch.vc
      text ckup  'schooldisclaimer'
    div id: 'footer',->
      text '<school-detail-footer></school-detail-footer>'
  text ckup 'clickDirect'
  coffeejs {vars: {sch: Object.assign({}, @sch),dispVar: @dispVar,col:'university'}}, ->
    null
  js '/js/entry/universityDetail.js'


APP '1.5/school'

###*
 * 地图搜索学校API
 * @path POST /1.5/school/mapSearch/findSchools
 * @param {Object} req.body
 * @param {Array} req.body.bbox - 地图边界框坐标
 * @param {String} req.body.schid - 学校ID
 * @param {Array} req.body.loc - 位置坐标
 * @param {String} req.body.mode - 搜索模式(list/bnd)
 * @returns {Object} 学校列表结果
###
POST 'mapSearch/findSchools',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    d = req.body or {}
    d.limit = 50
    d.locale = req.locale()
    if (d.bbox and d.bbox?.length < 3)
      return respError {clientMsg:req.l10n('Not valid bnds data'),resp}
    if (not d.bbox) and (not d.schid) and (not d.loc) and (!d.schs and !d.bnds)
      return respError {clientMsg:req.l10n('No required data'),resp}
    try
      result = {schs: []}
      if d.schid
        d.id = d.schid
        school = await School.getPublicSchoolById d
        result.schs = [school]
      #used in old maplogic，self.getSchoolsInfo(null,{bbox:bbox, mode:'list'});
      # when web map zoomed>13,call findSchools
      # bbox: [-79.63378763726347, 43.58687193370449, -79.62065217045097, 43.59969967525916]
      # mode: "list"
      else if d.mode is 'list'
        d.needExtendFields = true # need rate
        d.showOnMap = true
        result.schs = await School.getSchools d
      else if d.mode is 'bnd' # for home school
        d.locale = req.locale()
        result.schs = await School.getHomeSchools d
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    return resp.send result

###*
 * 获取学校列表API
 * @path POST /1.5/school/getSchools
 * @param {Object} req.body - 查询参数
 * @returns {Object} 包含学校列表和总数的结果
###
POST 'getSchools', (req, resp)->
  UserModel.appAuth {req,resp},(user)->
    params = req.body
    params.slice = 1
    params.locale = req.locale()
    params.needExtendFields = 1
    try
      {schList:schs, cnt} = await School.getSchoolsWithCnt params
      resp.send {ok:1, schs, cnt}
    catch err
      debug.error err
      resp.send {ok:0 ,e:err}

###*
 * 获取学校列表页面
 * @path GET /1.5/school/schoolList
 * @returns {View} 学校列表页面视图
###
GET 'schoolList',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    resp.ckup 'schoolListPage',{}

###*
 * 检查学校token
 * @param {Object} school - 学校对象
 * @param {String} uid - 用户ID
 * @returns {Object} 处理后的学校对象
 * @description 检查用户是否已兑换学校数据的token
 * - 如果未兑换,则删除敏感数据(studentsMap/rankMap等)
 * - 如果已兑换,则保留完整数据
###
checkSchoolToken = (school,uid)->
  return school unless ((school.rmRankKeyFacts and Object.keys(school.rmRankKeyFacts).length > 0) or (school.censusChartData?.length > 1) or school.rankMap?.adjSummary or school.rankMap?.basic)#school.censusChartData 有一个默认的key: 'summary'，所以判断length>1
  school.canExchange = 1
  # token兑换学校通用模块，public school使用sourceId，private school使用_id
  tokenId = (school.sourceId or school._id) + (school.rmRankLastYear or 'Census')
  key = gSchoolTokenKey
  if school.private
    key = gPrivateSchoolTokenKey
    tokenId = school._id + (school.rmRankLastYear or 'Adjustment')
  isExchanged = await TokenModel.isExchanged {uid:uid, key:key, id:tokenId}
  unless isExchanged.result
    delete school.studentsMap
    delete school.rankMap
    delete school.chart
    delete school.summary
    delete school.censusChartData
    school.tokenId = tokenId
    school.tokenKey = key
  return school

###*
 * 获取公立学校详情
 * @param {Object} req - 请求对象
 * @param {Object} resp - 响应对象
 * @param {Object} user - 用户对象
 * @returns {Promise<Object>} 包含学校和教育局信息的对象 or null when not found
 * @description
 * - 获取学校基本信息
 * - 获取学校统计数据
 * - 处理用户权限和数据访问限制
###
getPublicSchoolDetail = (req,resp, user)->
  id = req.param('id')
  locale = req.locale()
  cfg = {id,locale,allFraser:1,noBounds:1}
  if user
    isAdmin = req.isAllowed('admin')
    try
      isInGroup = await GroupModel.isInGroup {uid:user._id, groupName:':inReal'}
    catch err
      debug.error 'GroupModel.isInGroup',err
    # 判断权限是否能查看rmrank
    if isAdmin or isInGroup
      cfg.canExchange = 1
  try
    school = await School.getPublicSchoolById cfg
  catch err #ignore this error for inc vc
    debug.error err
  if not school
    return null

  try
    id = school.sourceId or school._id
    census = await SchoolCensusModel.getChartData(id)
    devType = if (req.getDevType() is 'app') then 'app' else 'web'
    await School.incPublicSchoolStats {ids:id,user,isView:true,devType}
  catch err #ignore this error for inc vc
    debug.error err

  if cfg.canExchange
    if census?.summary?.length
      school.summary = census.summary
    # 传回来的censusChartData，没有数据也会有一个默认值存在([{key:'summary',txt:'summary'}])，所以判断length>1
    if census?.censusChartData?.length > 1
      school.censusChartData = census.censusChartData
    try
      school = await checkSchoolToken(school, user._id)
    catch err #ignore this error for inc vc
      debug.error err
  try
    board = await School.getSchoolBoardById school.sbid
    #有的数据没有board，不能返回err
  catch err
    debug.debug err
    return {sch:school}
  if req.param 'embed'
    debug.debug 'access school detail from embed'
  else if (not user) and (not req.param('share'))
    delete school.eqao
    delete school.fraser
  return {sch:school,board:board}

#old:1.5/school/detail
##1.5/school/public/detail
#get public school Detail
#need Login 需要在前台处理。
POST 'public/detail/:id.json',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    #TODO: POST里不能redirect。
    if req.getDevType() is 'app' and not user
      return respError {clientMsg:req.l10n('Please Login or Register For More Detail'),resp}
    try
      ret = await getPublicSchoolDetail req,resp, user
    catch error
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    if not ret
      return respError {clientMsg:req.l10n('School Not Found'), resp}
    return resp.send ret

# NOTE: referrer is https://d2.realmaster.com/school/embed?lat=43.6670237&lng=-79.4474107&tour_type=brand&lang=zh-cn
###*
 * 检查用户是否需要登录
 * @param {Object} req - 请求对象 
 * @param {Object} user - 用户对象
 * @returns {Boolean} 是否需要登录
 * @description
 * - 检查请求来源是否为特定域名
 * - 检查用户是否已登录
 * - 检查是否为分享链接
###
shouldLogin = (req,user)->
  if /houssmax/.test req.headers?.referer
    return false
  if user
    return false
  if req.param 'share'
    return false
  true

###*
 * 获取公立学校详情
 * @param {Object} req - 请求对象
 * @param {Object} resp - 响应对象
 * @param {Object} user - 用户对象
 * @returns {Promise<Object>} 包含学校和教育局信息的对象
 * @description
 * - 获取学校基本信息
 * - 获取学校统计数据
 * - 处理用户权限和数据访问限制
###
getPublicSchoolDetails=(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp},(user)->
    isApp = req.getDevType() is 'app'
    # not show school when not in app, except houssmax
    # BUG: referer not working
    # TODO: find a way to work around
    # if (not isApp) and not /houssmax/.test req.headers?.referer
    #   return resp.redirect '/app-download'
    if shouldLogin(req,user)
      #TODO:should not redirect. get调用
      return resp.redirect '/1.5/user/login'
      # return resp.ckup 'generalMessage',
      #   {
      #     nobar:1, msg:req.l10n('Need Login'),
      #     link:{
      #       nm:req.l10n('Login'),
      #       onclick:"clickDirect(#{isApp},
      #       '/1.5/user/login')"
      #       }
      #   }
    try
      school = await getPublicSchoolDetail req,resp,user
    catch err
      return resp.ckup 'generalError',{err:err}
    if not school?.sch
      return resp.ckup 'generalError',{err:req.l10n('School Not Found')}
    getDispVar req, user, (err, ret)->
      dispVar = ret.datas or {}
      sch=school.sch
      # 2020-08-31, julie,no rating and comments for school for now.
      # getMyRating sch, user
      # processCnmts sch, req
      # if sch.cmnts
      #   for cmnt,idx in sch.cmnts
      #     cmnt.del = cmnt.del or false
      #     setCmntThumbUpAndRating sch.rating, user, cmnt, idx
      inFrame = req.param('inFrame')
      opt ={
        dispVar:dispVar,
        title:"#{req.l10n('RealMaster Public School:','pschool')}#{sch.nm}",
        inFrame:inFrame,
        sch:sch,
        bar:req.param('bar')
        board:school.board}
      opt.shareHost = req.shareHost()
      opt.shareImage = req.shareHost()+'/img/school/school_forum.png'
      # shareImage:opt.shareImage
      cfg =
        noUserModal:1,
        wxcfg:{},
        noRatchetJs:1,
        noAngular:1,
        desc:"#{sch.nm},#{sch.addr},#{sch.city},#{sch.prov}"

      # if user
      #   UserModel.logForumHistory user._id, {postid:sch._id.toString()}, () ->
      if req.getDevType() is 'app'
        return resp.ckup 'schoolDetailNew',opt, '_', cfg
      getWXConfig req,req.fullUrl(),(err,wxcfg)->
        cfg.wxcfg = wxcfg
        return resp.ckup 'schoolDetailNew',opt,'_',cfg

#old: /1.5/school/inapp/detail?id=0192332
GET 'inapp/detail',(req,resp)->
  getPublicSchoolDetails(req,resp)

#new: /1.5/school/public/detail? id=0192332
GET 'public/detail',(req,resp)->
  getPublicSchoolDetails(req,resp)


###*
 * 获取私立学校信息
 * @param {Object} req - 请求对象
 * @param {String} id - 学校ID
 * @returns {Promise<Object>} 学校信息对象
 * @description
 * - 增加学校访问计数
 * - 获取学校详细信息
 * - 处理学校热门标记
###
getPrivateSchoolById = (req, id)->
  isAdmin = req.isAllowed('marketAdmin')
  if req.param 'share'
    isAdmin = false
  try
    await School.increasePrivateSchoolVc id
    sch = await School.getPrivateSchoolById {id,locale:req.locale(),isAdmin,canExchange:req.canExchange}
  catch err
    debug.error err
    return req.l10n(MSG_STRINGS.DB_ERROR)
  sch.hot = 'Y' if sch?.hot
  return sch


#/1.5/school/private
#房源附近学校列表或者地图范围内学校列表。
# { 'loc[]': [ '43.72199', '-79.45175' ] }
# { 'sw[]': [ '43.721990000000005', '-79.45175' ],
# 'ne[]': [ '43.721990000000005', '43.721990000000005' ] }
POST 'private', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    limit = 50
    q = req.body
    loc = q.loc or q['loc[]']
    sw = q.sw or q['sw[]']
    ne = q.ne or q['ne[]']
    if not (loc or (sw and ne))
      return resp.send {e:MSG_STRINGS.BAD_PARAMETER}

    if loc = q.loc or q['loc[]']
      dlat = 0.1
      dlng = 0.1
      q.bbox = [loc[1] - dlng,loc[0] - dlat,loc[1] + dlng,loc[0] + dlat]

    else if (sw = q.sw or q['sw[]']) and (ne = q.ne or q['ne[]'])
      q.bbox = [sw[1],sw[0],ne[1],ne[0]]

    q.needExtendFields = 1
    q.locale = req.locale()
    try
      {schList:list, cnt} = await School.getPrivateSchoolsWithCnt q
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    resp.send {items:list,cnt:cnt,}

# 分享后用户增加limit
# 1.5/school/private/shared
POST 'private/shared',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0} unless user
    UserModel.setTskDn user._id, {tskDn:'pSch'},(err)->
      if err
        console.error err
      # notifyAdmin "Shared Private Schools. #{req.remoteIP()} User:#{user._id}:#{user.eml} #{libUser.fullNameOrNickname(req.locale(),user)} #{user.nm} #{user.roles}"
      resp.send {ok:1, r:':ctx::cancel'}

#1.5/school/private/detail/:id
# only one school id is supported
GET 'private/detail/:id', (req,resp)->
  getPrivateSchoolDetail req, resp


#1.5/school/private
GET 'private',(req,resp)->
  loc = req.param 'loc'
  lat = req.param 'lat'
  lng = req.param 'lng'
  cityName = req.param 'cityName'
  # return if req.redirectHTTPWhenCnDitu()
  unless req.getDevType() is 'app'
    return resp.redirect '/adPage/needAPP'
  url = '/1.5/map/webMap?ss=1&zoom=15&tab=private'
  if loc?
    loc.split(',')
    lat = loc[0]
    lng = loc[1]
  # NOTE:https://github.com/jashkenas/coffeescript/issues/966
  lng = helpersStr.formatLng lng
  if (lat and (not helpersStr.isLatitude lat)) or \
  (lng and (not helpersStr.isLongitude lng))
    return resp.ckup 'generalError', {err:req.l10n('Location information is wrong')}
  url = url+ "&lat=#{lat}&lng=#{lng}"
  return resp.redirect url

#1.5/school/university/detail/:id
GET 'university/detail/:id',(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp},(user)->
    isApp = req.getDevType() is 'app'
    if shouldLogin(req,user)
      return resp.redirect '/1.5/user/login'
      # return resp.ckup 'generalMessage',
      #   {
      #     nobar:1, msg:req.l10n('Need Login'),
      #     link:{
      #       nm:req.l10n('Login'),
      #       onclick:"clickDirect(#{isApp},
      #       '/1.5/user/login')"
      #       }
      #   }
    id = req.param('id')
    locale = req.locale()
    try
      school = await School.getUniversityById {id,locale}
    catch err
      return resp.ckup 'generalError',{err:err}
    if not school
      return resp.ckup 'generalMessage', {nobar:1, msg:req.l10n 'Not found'}
    getDispVar req, user, (err, ret)->
      dispVar = ret.datas or {}
      inFrame = req.param('inFrame')
      opt =
        dispVar:dispVar,
        title:"#{req.l10n('RealMaster Universities:','school')}#{school.nm}",
        inFrame:inFrame,
        sch:school,
        bar:req.param('bar')
      opt.shareHost = req.shareHost()
      opt.shareImage = school.logo if school.logo
      opt.isApp = isApp
      # shareImage:opt.shareImage
      cfg =
        noUserModal:1,
        wxcfg:{},
        noRatchetJs:1,
        noAngular:1,
        desc:"#{school.nm}"

      # if user
      #   updateForumHist user._id, sch._id.toString(), () ->
      if req.getDevType() is 'app'
        return resp.ckup 'university-detail',opt, '_', cfg
      getWXConfig req,req.fullUrl(),(err,wxcfg)->
        cfg.wxcfg = wxcfg
        return resp.ckup 'university-detail',opt,'_',cfg

#/1.5/school/university
#房源附近学校列表或者地图范围内学校列表。
# { 'loc[]': [ '43.72199', '-79.45175' ] }
# { 'sw[]': [ '43.721990000000005', '-79.45175' ],
# 'ne[]': [ '43.721990000000005', '43.721990000000005' ] }
POST 'university', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN} unless user
    limit = 50
    # NOTE: potential security risk
    q = req.body
    loc = q.loc or q['loc[]']
    sw = q.sw or q['sw[]']
    ne = q.ne or q['ne[]']
    if not (loc or (sw and ne) or q.bbox)
      return resp.send {e:MSG_STRINGS.BAD_PARAMETER}

    if loc = q.loc or q['loc[]']
      dlat = 0.1
      dlng = 0.1
      q.bbox = [loc[1] - dlng,loc[0] - dlat,loc[1] + dlng,loc[0] + dlat]
    else if (sw = q.sw or q['sw[]']) and (ne = q.ne or q['ne[]'])
      q.bbox = [sw[1],sw[0],ne[1],ne[0]]

    q.needExtendFields = 1
    q.locale = req.locale()
    try
      {schList: list, cnt }= await School.getUniversitiesWithCnt q
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    resp.send {items:list, cnt}

APP 'privateSchools'
GET 'detail/:id', (req,resp)->
  getPrivateSchoolDetail req, resp

