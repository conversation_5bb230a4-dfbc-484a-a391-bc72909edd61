config = CONFIG(['serverBase','share'])
{unifyAddress,formatLng} = INCLUDE 'lib.helpers_string'
propAddress = INCLUDE 'lib.propAddress'
similarEngine = INCLUDE 'libapp.similarPropEngine'
libP = INCLUDE 'libapp.properties'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
debug = DEBUG()
UserModel = MODEL 'User'
Properties = MODEL 'Properties'
geolib = INCLUDE 'geolib'
GroupModel = MODEL 'Group'

LIST_PROP_FIELDS = libP.LIST_PROP_FIELDS

rateLimiter = INCLUDE 'lib.rateLimiterV2'
similarPropsLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:30,source:'similarProps'}
similarPropsLimiterFilter = rateLimiter.createFilter similarPropsLimiterInstance

sameBuildingInstance = rateLimiter.createLimiter {AccessPerSessionLimit:60,source:'sameBuilding'}
sameBuildingFilter = rateLimiter.createFilter sameBuildingInstance

APP '1.5'
APP 'similar',true

findError = (msg, resp, err)->
  debug.error err if err
  # ret = genErrRet(msgObj,resp)
  resp.req?.logger?.error err if err
  resp.send {ok:0,err:msg?.toString()}

###
# @description 根据房源loc,lat,lng为查询条件生成坐标信息
# @pram {object} params - conditions for similar search
# @param {object} prop - property detail
###
genLocationByProp = (params,prop)->
  if prop.lat and prop.lng
    params.lat = parseFloat prop.lat
    params.lng = formatLng prop.lng
  else if prop.loc and prop.loc.coordinates?.length
    params.lng = formatLng prop.loc.coordinates[0]
    params.lat = parseFloat prop.loc.coordinates[1]
  return


processProp = ({req, prop, user, params, dists, weights, isCip, isPad,isRealGroup}) ->
  if params or dists or weights
    unless prop.dist
      prop.dist = geolib.getDistance(
        {latitude: params.lat, longitude: params.lng},
        {latitude: prop.lat, longitude: prop.lng}
      )
    dists.push {id: prop._id,d: prop.dist}
    weights.push {id: prop._id,weight: prop.weight}
  prop.showSoldPrice = true
  libP.setUpPropRMPlusFields prop
  libP.genListingPicReplaceUrls(prop, isCip, {num:1,useThumb:true,isPad,use3rdPic:config.serverBase?.use3rdPic,shareHostNameCn:config.share?.hostNameCn})

  {isAllowedPropAdmin,isAllowedVipUser,l10n,_ab,apisrc,simplfyPtp} = libP.getBasePropDetailOpt req,user
  Properties.addPropAdditionalFields({
    user,prop,
    isAllowedPropAdmin,
    isAllowedVipUser,
    l10n,
    _ab,
    apisrc,
    isRealGroup,
    simplfyPtp})


GET '', (req, resp) ->
  # rateLimiterFilter req,resp,()->
  req.setLocale lang if lang = req.param 'lang'
  opt =
    d: req.param('d') or '/1.5/index'
    js: ['/js/vue3.min.js', '/js/similar/propsPage.js']
    css: '/css/apps/similarProps.css'
  resp.ckup 'similar/props', opt, '_', { noAngular: true, noUserModal: true, noRatchetJs: true }

SIMILAR_REQUIRED_FIELDS = ['type','lat','lng','lp','bdrms','bthrms','sqft','gr']

checkParams = (params) ->
  if params.id and params.type
    return true
  else
    for field in SIMILAR_REQUIRED_FIELDS
      return false unless params[field]?
  return true

PROP_TYPE = ['Detached','Semi-Detached','Townhouse','Apartment','Bungalow']
FIELDS = {lp:1,gr:1,bdrms:1,bthrms:1,br_plus:1,loc:1,lat:1,lng:1,uaddr:1,sqft1:1,sqft2:1}

# /1.5/similar
# body:{id,type,month} / {type,lat,lng,lp,bdrms,bthrms,sqft,gr}
POST '',(req,resp)->
  similarPropsLimiterFilter req,resp,()->
    return findError MSG_STRINGS.BAD_PARAMETER,resp unless params = req.body
    UserModel.appAuth {req,resp},(user)->
      return findError MSG_STRINGS.NEED_LOGIN,resp unless user
      return findError MSG_STRINGS.BAD_PARAMETER,resp unless (checkParams params)
      propdetail = null
      if params.id
        projection = Object.assign {}, LIST_PROP_FIELDS, FIELDS
        try
          propdetail = await Properties.findOneByIDAsync params.id, projection
        catch err
          debug.error 'find prop error:',err,'propId:',params.id
          return findError MSG_STRINGS.DB_ERROR,resp
        return findError MSG_STRINGS.NOT_FOUND,resp unless propdetail
        params.mlsid = params.id
        genLocationByProp params,propdetail
        params.lp = propdetail.lp
        params.gr = propdetail.gr
        params.bdrms = propdetail.bdrms
        params.bthrms = propdetail.bthrms
        params.br_plus = propdetail.br_plus
        if propdetail.sqft
          propSqft = similarEngine.getPropSqft(propdetail)
          if propSqft
            params.sqft = propSqft
      else
        params.lat = parseFloat params.lat
        params.lng = formatLng params.lng
      
      if not (params.lat and params.lng)
        # 缺少位置坐标
        debug.error 'Error: no location info,params:',params
        return findError MSG_STRINGS.BAD_PARAMETER, resp

      # 原房源ptype2如果有Link类型，将ptype2=Link作为权重计算
      if params.type.includes 'Link'
        params.isLink = true
      for type in params.type.split(',')
        if type in PROP_TYPE
          params.type = type
          break
      # check params.type is valid，similarEngine的MAX_RANGE里面是否有该类型
      if not similarEngine.distRange[params.type]
        if params.isLink
          # 房源type除Link以外没有其他类型满足相似房源查找,按照Detached查找相似房源
          params.type = 'Detached'
        else
          debug.error 'Error: invalid evalution type',params.type
          return findError MSG_STRINGS.BAD_PARAMETER, resp

      max_distance = similarEngine.distRange[params.type].distance
      max_month = params.month or similarEngine.distRange[params.type].month
      if (params.prov is 'BC')
        max_distance = max_distance * 2
      
      if params.saletp is 'Sale'
        rental = false
      else
        rental = true
      
      if params.saleDesc in ['Sold','Leased']
        isSold = true
      else
        isSold = false

      try
        result = await Properties.getSimilarSaleProps {params, rental, isSold, user}
      catch err
        debug.error 'similarEngine.getSimilarProps err:'+err,' body: ',params,'user:',user
        return resp.send {ok:0, err:MSG_STRINGS.INTERNAL_ERROR}
      
      response = {
        range:result?.range or params.range or max_distance
        last:result?.last or params.last or 12
      }

      response.items =  result?.usedProps or []
      response.max_dist_range = max_distance

      dists = []
      weights = []

      isChinaIP = req.isChinaIP()
      isPad = req.isPad()

      GroupModel.isInGroup {uid:user?._id,groupName:':inReal'},(err,isRealGroup)->
        debug.error 'GroupModel.isInGroup',err if err
        for prop in response.items
          processProp {req, prop, user, params, dists, weights, isCip:isChinaIP, isPad,isRealGroup}

        # NOTE:当传入数据没有id,而经纬度时不存在propdetail
        if propdetail
          processProp {req, prop:propdetail, user, isCip:isChinaIP, isPad,isRealGroup}

        response.ok = 1
        response.cnt = result?.cnt or 0
        req.setupL10n() unless req._ab
        response.items = propTranslate.translate_rmprop_list(req, response.items)
        if propdetail
          propdetail = propTranslate.translate_rmprop_detail({locale:req.locale(),transDDF:req.getConfig('transDDF')},propdetail)
        response.origProp = propdetail or {}

        return resp.send response

# body:{type,saletp,saleDesc,page,limit,id,bdrms}
POST 'building',(req,resp)->
  sameBuildingFilter req,resp,()->
    return findError MSG_STRINGS.BAD_PARAMETER,resp unless params = req.body
    return findError MSG_STRINGS.BAD_PARAMETER,resp unless params.id
    UserModel.appAuth {req,resp},(user)->
      return findError MSG_STRINGS.NEED_LOGIN,resp unless user
      projection = Object.assign {}, LIST_PROP_FIELDS, FIELDS
      try
        propdetail = await Properties.findOneByIDAsync params.id, projection
      catch err
        debug.error 'find prop error:',err,'propId:',params.id
        return findError MSG_STRINGS.DB_ERROR,resp
      return findError MSG_STRINGS.NOT_FOUND,resp unless propdetail
      params.uaddr = propdetail.uaddr
      genLocationByProp params,propdetail

      if not (params.lat and params.lng) and (not params.uaddr)
        # 缺少uaddr与位置坐标
        debug.error 'no location info and uaddr, params:',params,'user:',user
        return resp.send {ok:0, err:MSG_STRINGS.BAD_PARAMETER}
      if 'string' is typeof params.type
        params.type = params.type.split(',')
      try
        result = await Properties.getBuildingProps params
      catch err
        debug.error err,'getBuildingProps params:',params,'user:',user
        return resp.send {ok:0, err:MSG_STRINGS.INTERNAL_ERROR}

      try
        props = await Properties.addPropFavFields {props:result,uid:user?._id}
        isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
      catch err
        debug.error 'building',err
      isCip = req.isChinaIP()
      isPad = req.isPad()
      for prop in props
        processProp {req, prop, user, isCip, isPad,isRealGroup}
      processProp {req, prop:propdetail, user, isCip, isPad,isRealGroup}
      props = propTranslate.translate_rmprop_list(req, props)
      propdetail = propTranslate.translate_rmprop_detail({locale:req.locale(),transDDF:req.getConfig('transDDF')},propdetail)
      return resp.send {ok:1, items:props,origProp:propdetail}

