APP 'student_rental'

UserModel = MODEL 'User'
debug = DEBUG()
libUser = INCLUDE 'libapp.user'
libAppVer = INCLUDE 'libapp.appVer'
common = INCLUDE 'libapp.common'
CONTACT_REALTOR_VALUES = common.CONTACT_REALTOR_VALUES
js =[
  '/vue3.min.js',
  '/saves/savesIndexPage.js'
]
css = [
  '/apps/saves.css',
  '/apps/drawers.css'
  '/sprite.min.css'
]
getOption = (req)->
  opt =
    d:req.param('d') or '/1.5/index'
    isPopup:req.param 'isPopup'
    js:js
    css:css
    formPage: CONTACT_REALTOR_VALUES.STUDENT_RENTAL
  return opt

GET '', (req, resp,next)->
  UserModel.appAuth {req,resp,url:"/1.5/user/login?d=/studentRental"}, (user)->
    opt = getOption req
    addJs = ["/saves/propertiesPage.js"]
    opt.js = opt.js.concat(addJs)
    opt.page = CONTACT_REALTOR_VALUES.STUDENT_RENTAL
    opt.isChinaIP=req.isChinaIP()
    opt.appmode=req.cookies[libUser.APPMODE]
    opt.lang=req.locale()
    opt.os= if req.isIOSDevice() then 'ios' else 'android'
    libAppVer.getAppUpgradeSetting req, {os:opt.os}, (setting) ->
      opt.appUpgrade = setting
      resp.ckup 'studentRental/list',opt,'_',{noAngular:true,noUserModal:true,noRatchetJs:true}
