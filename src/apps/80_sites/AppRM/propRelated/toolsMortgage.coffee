
getExchangeRates = DEF 'getExchangeRates'
getExchangeNames = DEF 'getExchangeNames'
getBanners = DEF 'getBanners'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

APP '1.5'
APP 'tools',true

# '/1.5/tools/mortgage'
GET 'mortgage',(req,resp)->
  if (locale = req.param('lang')) and (locale in LANGUAGE_LIST)
    req.setLocale locale
  adHtml = null
  if banner = (banners = getBanners req.locale(),'mortgage')?[0]
    if (req.getDevType() is 'app') #and (not req.param 'inframe')
      js = "RMSrv.showInBrowser('/adJump/" + banner._id + "');"
    else
      js = "window.location='#{'/adJump/' + banner._id }'"
    adHtml = """<img src="#{banner.src}" onclick="#{js};" style="width:100%;"/>"""
    if banner.impImgUrl
      url = banner.impImgUrl.replace('[timestamp]',(''+Date.now())).replace('[ts]',(''+Date.now()))
      adHtml += """<img alt="Advertisement" style="width:1px;height:1px;display:none;" src="#{url}" height="1" width="1"/>"""
  #set title console.dir(req.headers)
  resp.ckup 'mortgage',{
    title:"Mortgage"
    mode:"mortgage"
    returnPath: req.param('d')
    exrates:getExchangeRates()
    exnames:getExchangeNames()
    adHtml: adHtml
  }
# '/1.5/tools/rentorbuy'
GET 'rentorbuy',(req,resp)->
  if (locale = req.param('lang')) and (locale in LANGUAGE_LIST)
    req.setLocale locale
  resp.ckup 'mortgage',{
    title:"Rent Or Buy"
    mode:"rentOrBuy"
    returnPath: req.param('d')
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }
# '/1.5/tools/transtax'
GET 'transtax',(req,resp)->
  if (locale = req.param('lang')) and (locale in LANGUAGE_LIST)
    req.setLocale locale
  resp.ckup 'mortgage',{
    title:"Transfer Tax"
    mode:"transfertax"
    returnPath: req.param('d')
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }
# '/1.5/tools/currency'
GET 'currency',(req,resp)->
  resp.ckup 'mortgage',{
    title:"Currencies"
    mode:"exchange"
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }

# '/1.5/tools/length'
GET 'length',(req,resp)->
  resp.ckup 'mortgage',{
    title:"Length"
    mode:"length"
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }

# '/1.5/tools/area'
GET 'area',(req,resp)->
  resp.ckup 'mortgage',{
    title:"Area"
    mode:"area"
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }

# '/1.5/tools/tools'
GET 'temperature',(req,resp)->
  resp.ckup 'mortgage',{
    title:"Temperature"
    mode:"temp"
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  }






VIEW 'mortgage',->
  css '/css/ratchet.popover.css'
  text """
  <style>
    .row{
      display: flex;
      margin-top: 10px;
    }
    .col {
      width: 33.3%;
    }
    .text-right {
      text-align: right;
    }
    table {
      width: 100%;
      border: none;
    }
    th {
      font-weight: normal;
      font-size: 10px;
      text-align: center;
    }
    .rentOrBuy {
      padding: 10px;
    }
    #chart {
      padding: 10px;
    }
  </style>
<body ng-app="App" ng-controller="MCtrl" class="ng-cloak ng-scope">

  <script src="/js/angular/1_3/angular-touch.min.js"></script>
  <script src="/js/calculate.min.js"></script>
  <script src="/js/Chart-2.5.0.min.js"></script>

  <link rel="stylesheet" type="text/css" href="/css/tools.css#1_5">
    <header class="bar bar-nav">
      <a href="#{@returnPath or 'javascript:goBack();'}" id="goback-link" dataIgnore="push" class="icon fa fa-back pull-left"
        style="#{if @req.param('nn') then 'display:none' else ''}"></a>
      <h1 class="title">
        {{val.mode.name}}
        <a href="#toolsPop"> <span class="icon icon-caret"></span> </a>
      </h1>
    </header>
    <div class="bar bar-standard bar-footer" >
      <div id="keyboard" class="keyboard" ng-show="val.showkeyboard">
        <div ng-repeat="btns in btnss" style="overflow:hidden;">
          <div ng-repeat="btn in btns" class="btn" ng-click="pressed($event,btn.act,btn.nm)"><i class="fa {{btn.fa}}" ng-show='btn.fa'></i>{{btn.nm}}</div>
        </div>
      </div>
    </div>
    <div id="toolWrapper" style="width: 100%; height: 100%; overflow: auto; -webkit-overflow-scrolling: touch;" class="has-sidebar-right content">

      <div id="toolsPop" class="popover">
        <header class="bar bar-nav">
          <h1 class="title">#{_('Select Tool')}</h1>
        </header>
        <ul class="table-view">
        <li ng-repeat="m in modes" class="table-view-cell" ng-click="mode(m);toggle('toolsPop','none');">{{m.name}}
        </li>
      </div>

      <div class="app">
        <div class="well well-sm alert-danger ng-hide" ng-show="error">
          <p class="ng-binding"></p>
        </div>
        <div class="" group="mortgage" ng-show="'mortgage'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label for="mortgage-price" >#{_('Price')}</label>
                  <div class=" " r4-num-input="price">60,000.00</div>
                </div>
                <div class="input-row">
                  <label for="mortgage-percentage" class=" ">#{_('Downpayment %')}</label>
                  <div class="" filter="none" r4-num-input="downpayPercentage">0</div>
                </div>
                <div class="input-row">
                  <label for="id-downpayment" class="control-label">#{_('Downpayment')}</label>
                  <div class=" " r4-num-input="downpayAmount">0.00</div>
                </div>
                <div class="input-row">
                  <label for="id-interest-rate" class=" ">#{_('Interest Rate')}</label>
                  <div class="" filter="none" r4-num-input="interestRate">3</div>
                </div>
                <div class="input-row">
                  <label for="id-amortization" class=" ">#{_('Amortization')}(#{_('Years')})</label>
                  <select class="form-control ng-pristine ng-untouched ng-valid needsclick" ng-model="amortization" ng-change="calc()">
                    <option value="20">20</option>
                    <option value="25">25</option>
                    <option value="30">30</option>
                    <option value="35">35</option>
                    <option value="40">40</option>
                  </select>
                </div>
              </form>
            </div>
            <div class="mortgage  ">
              <ul class="table-view">
                <li class="table-view-divider">#{_('Mortgage')}</li>
                <li class="table-view-cell">#{_('Loan Amount')}
                  <span class="badge">{{mortgage.amount | currency:\'$\':0}}</span>
                </li>
                <li class="table-view-cell">#{_('Monthly Payment')}
                  <span class="badge">{{mortgage.monthlyPayment | currency:\'$\':0}}</span>
                </li>
                <li class="table-view-cell">#{_('1st Month Interest')}
                  <span class="badge">{{mortgage.interest1 | currency:\'$\':0}}</span>
                </li>
                <li class="table-view-cell">#{_('1st Month Principal')}
                  <span class="badge">{{mortgage.principal1 | currency:\'$\':0}}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class=" ng-hide" group="rentOrBuy" ng-show="'rentOrBuy'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label for="mortgage-price" >#{_('Price')}</label>
                  <div class=" " r4-num-input="price">60,000.00</div>
                </div>
                <div class="input-row">
                  <label for="rent-price">#{_('Monthly Rent')}</label>
                  <div class=" " r4-num-input="monthlyRent">1,200</div>
                </div>
                <div class="input-row">
                  <label for="rent-increase-rate">#{_('Annual Rent Increase Rate %')}</label>
                  <div class=" " r4-num-input="annualRentIncrease">1</div>
                </div>
                <div class="input-row">
                  <label for="mortgage-percentage" class=" ">#{_('Downpayment %')}</label>
                  <div class="" filter="none" r4-num-input="downpayPercentage">20</div>
                </div>
                <div class="input-row">
                  <label for="id-downpayment" class="control-label">#{_('Downpayment')}</label>
                  <div class=" " r4-num-input="downpayAmount">0.00</div>
                </div>
                <div class="input-row">
                  <label for="home-value-increase-rate">#{_('Annual Value Increase Rate %')}</label>
                  <div class=" " r4-num-input="annualIncreaseVal">1</div>
                </div>
                <div class="input-row">
                  <label for="id-interest-rate" class=" ">#{_('Interest Rate')} %</label>
                  <div class="" filter="none" r4-num-input="interestRate">3</div>
                </div>
                <div class="input-row">
                  <label for="id-amortization" class=" ">#{_('Amortization')}(#{_('Years')})</label>
                  <select class="form-control ng-pristine ng-untouched ng-valid needsclick" ng-model="amortization" ng-change="calc()">
                    <option value="20">20</option>
                    <option value="25">25</option>
                    <option value="30">30</option>
                    <option value="35">35</option>
                    <option value="40">40</option>
                  </select>
                </div>
                <div class="input-row">
                  <label for="annual-property-tax" class=" ">#{_('Annual Property Tax')}</label>
                  <div class="" filter="none" r4-num-input="annualPropertyTax">1600</div>
                </div>
                <div class="input-row">
                  <label for="montly-management-fee" class=" ">#{_('Other Annual Fee')}</label>
                  <div class="" filter="none" r4-num-input="otherAnnualFee"></div>
                </div>
                <div class="input-row">
                  <label for="years-before-selling" class=" ">#{_('Years Before Selling')}</label>
                  <div class="" filter="none" r4-num-input="yearsBeforeSelling">10</div>
                </div>
              </form>
            </div>
            <div class="rentOrBuy">
              <div class="row">
                <div class="col"></div>
                <div class="col text-right">#{_('If Renting')}</div>
                <div class="col text-right">#{_('If Buying')}</div>
              </div>
              <div class="row">
                <div class="col">#{_('Loan Amount')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge">{{mortgage.amount | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Monthly Payment')}</div>
                <div class="col text-right">
                  <span class="badge">{{monthlyRent | currency:\'$\':0}}</span>
                </div>
                <div class="col text-right">
                  <span class="badge">{{mortgage.monthlyPayment | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Other Annual Fee')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge">{{otherTotalAnnualFee | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Total Payment')}</div>
                <div class="col text-right">
                  <span class="badge">{{totalMonthlyRent | currency:\'$\':0}}</span>
                </div>
                <div class="col text-right">
                  <span class="badge">{{totalPaymentsBuy | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Increase in value of home')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge">{{totalIncreaseVal | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Portion of mortgage paid down')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge">{{mortgagePaidDown | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Additional payment cost of buying')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge" ng-if="additionalCostOfBuy < 0">({{Math.abs(additionalCostOfBuy) | currency:\'$\':0}})</span>
                  <span class="badge" ng-if="additionalCostOfBuy >= 0">{{additionalCostOfBuy | currency:\'$\':0}}</span>
                </div>
              </div>
              <div class="row">
                <div class="col">#{_('Benefit from buying vs. renting')}</div>
                <div class="col text-right"></div>
                <div class="col text-right">
                  <span class="badge">{{benefitBuying | currency:\'$\':0}}</span>
                </div>
              </div>
            </div>
            <canvas id="chart" width="400" height="400"></canvas>
            <table>
              <tr>
                <th>#{_('Year')}</th>
                <th>#{_('Rent', 'Compare')}</th>
                <th>#{_('Buy', 'Compare')}</th>
                <th>#{_('Home Equity Increase')}</th>
                <th>#{_('Mortgage Balance')}</th>
              </tr>
              <tr ng-repeat="year in yearArray">
                <th>{{year}}</th>
                <th>{{yearlyRentList[year - 1] | currency:\'$\':0}}</th>
                <th>{{yearlyBuyList[year - 1] | currency:\'$\':0}}</th>
                <th>{{homeEquityIncrease[year - 1] | currency:\'$\':0}}</th>
                <th>{{yearlyMortgageBalance[year - 1] | currency:\'$\':0}}</th>
              </tr>
            </table>
          </div>
        </div>
        <div class=" ng-hide" group="transfertax" ng-show="'transfertax'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label  >#{_('Price')}</label>
                  <div   r4-num-input="price">60,000.00</div>
                </div>
                <div class="input-row">
                  <label  >#{_('Province')}</label>
                  <select ng-model="prov" name="prov" required="true" ng-change="calc()" style="
                    display: inline-block;
                    width: 50%;
                    margin: 0;
                    padding: 0;
                    height: 38px;
                    background: white;
                    -webkit-appearance: menulist;
                    text-align-last: right;">
                    <option value="ON">#{_('Ontario')}</option>
                    <option value="BC">#{_('British Columbia')}</option>
                    <option value="AB">#{_('Alberta')}</option>
                    <option value="Other">#{_('Other')}</option>
                  </select>
                </div>
                <div class="input-row" ng-show="prov == 'ON'">
                  <label for="toronto-property"  >#{_('Toronto Property')}</label>
                  <input type="checkbox" id="toronto-property" class="needsclick" ng-change="calc()" ng-model="torontoProperty">
                </div>
                <div class="input-row" ng-show="prov == 'BC'">
                  <label for="toronto-property"  >#{_('Foreign Buyer')}</label>
                  <input type="checkbox" id="foreign-buyer" class="needsclick" ng-change="calc()" ng-model="foreignBuyer">
                </div>
              </form>
            </div>
            <div class="mortgage  ">
              <ul class="table-view" ng-show="prov !== 'Other'">
                <li class="table-view-divider">#{_('Transfer-Tax')}</li>
                <li class="table-view-cell" ng-show="transfertax.tax !== false">#{_('Property Transfer Tax')}
                  <span class="badge">{{transfertax.tax | currency:\'$\':0}}</span>
                </li>
                <li class="table-view-cell" ng-show="transfertax.tax1 !== false">#{_('Property Transfer Tax')} <br> (#{_('First Time Buyer')})
                  <span class="badge">{{transfertax.tax1 | currency:\'$\':0}}</span>
                </li>
                <li class="table-view-cell" ng-show="transfertax.tax == false">#{_('Title Registration Fee')}
                  <span class="badge">{{transfertax.tax_t | currency:\'$\':0}}</span>
                </li>
              </ul>
              <ul class="table-view" ng-show="prov == 'Other'">
                <li class="table-view-divider">#{_('Transfer-Tax')}</li>
                <li class="table-view-cell">
                  #{_('Calculate Transfer Tax')}
                  <button class="btn btn-positive" ng-click="openTBrowser('https://www.ratehub.ca/land-transfer-tax')">Go</button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class=" ng-hide" group="exchange" ng-show="'exchange'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label  >#{_('Canadian Dollar')}</label>
                  <div  r4-num-input="price">{{price}}</div>
                </div>
                #{
                tmp = ''
                for k,v of @exnames
                  tmp += '<div class="input-row"><label >' + _(v) + '</label><div  r4-num-input="' + k + '"></div></div>'
                tmp
                }
              </form>
            </div>
            <ul class="table-view">
              <li class="table-view-divider">#{_('Update Time')}</li>
              <li class="table-view-cell" style="font-size: 0.9em;">#{new Date(@exrates.timestamp * 1000).toUTCString()}
              </li>
            </ul>
          </div>
        </div>
        <div class=" ng-hide" group="length" ng-show="'length'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group  ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label >#{_('Feet')}</label>
                  <div  filter="none" r4-num-input="Feet">1</div>
                </div>
                <div class="input-row">
                  <label >#{_('Meter')}</label>
                  <div  filter="none" r4-num-input="Meter"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Yard')}</label>
                  <div  filter="none" r4-num-input="Yard"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Mile')}</label>
                  <div  filter="none" r4-num-input="Mile"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Kilometer')}</label>
                  <div  filter="none" r4-num-input="Kilometer"></div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class=" ng-hide" group="area" ng-show="'area'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label >#{_('Feet')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Feet">1</div>
                </div>
                <div class="input-row">
                  <label >#{_('Meter')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Meter"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Yard')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Yard"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Acre')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Acre"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Hectare')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Hectare"></div>
                </div>
                <div class="input-row">
                  <label >#{_('Kilometer')}<sup>2</sup></label>
                  <div  filter="none" r4-num-input="Square_Km"></div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class=" ng-hide" group="temp" ng-show="'temp'==val.mode.mode">
          <div class="contents">
            <div class="inputs">
              <form class="input-group  ng-pristine ng-valid" role="form">
                <div class="input-row">
                  <label >#{_('Celsius')}</label>
                  <div  filter="none" r4-num-input="celsius">20</div>
                </div>
                <div class="input-row">
                  <label >#{_('Fahrenheit')}</label>
                  <div  filter="none" r4-num-input="fahrenheit"></div>
                </div>
              </form>
            </div>
          </div>
        </div>
        #{@adHtml or ''}
        <div ng-show="val.showkeyboard" class="keyboard-spacer ng-hide" style="height:40px;">
        </div>
      </div>
      <script  >
var exRates = {#{ ((k+":"+@exrates[k]) for k,v of @exnames).join(',')}};
var lengthUnits = {Feet:3.28084,Meter:1,Yard:1.093613,Mile:0.0006213712,Kilometer:0.001}
var areaUnits = {Square_Feet:10.76391,Square_Meter:1,Square_Yard:1.195991,Square_Acre:0.0002471054,Square_Hectare:0.0001,Square_Km:0.000001}
var modes = [{mode:"mortgage",name:"#{_('Mortgage Calculator')}"},
  {mode:"rentOrBuy",name:"#{_('Rent or Buy')}"},
  {mode:"transfertax",name:"#{_('Transfer Tax Calculator')}"},
  {mode:"exchange",name:"#{_('Exchange Calculator')}"},
  {mode:"length",name:"#{_('Length Convertor')}"},
  {mode:"area",name:"#{_('Area Convertor')}"},
  {mode:"temp",name:"#{_('Temperature Convertor')}"}
  ];
var btnss = [
  [{nm:'1'},{nm:'2'},{nm:'3'},{nm:'4'},{nm:'5'},{fa:'fa-long-arrow-left',act:'backone'}],
  [{nm:'6'},{nm:'7'},{nm:'8'},{nm:'9'},{nm:'0'},{nm:'AC',act:'clear'}],
  [{fa:'fa-angle-left',act:'previous'},{fa:'fa-angle-right',act:'next'},{nm:'.'},{nm:'00'},{nm:'000'},{fa:'fa-angle-down',act:'close'}],
  ];
var val = {mode:modes[0],showkeyboard:true};
var app = angular.module('App', [
  "ngTouch"
]);
app.directive('r4NumInput',function($filter){
  return {
    restrict: 'A',
    transclude: true,
    scope: false,
    link: function ($scope, ele,attrs){
      var filter = attrs.filter || 'currency';
      $scope.addNumInput(ele);
      $scope.$watch(
        function(scope){
          return $scope.$eval(attrs.r4NumInput);
        },
        function(value){
          if (filter == 'currency'){
            ele.html($filter(filter)(value,''));
          }else if (filter !== 'none'){
            ele.html($filter(filter)(value));
          }else if (value){
            var s = value.toString(),d=12;
            if (s.indexOf('e')){
              d=8;
            }
            if (value.toPrecision){
              ele.html(+value.toPrecision(d));
            }else{
              ele.html(value);
            }
          }else{
            ele.html(value);
          }
        }
      );
      ele.bind('click',function($event){ $scope.selNumInput($event,ele); });
    }
  };
});
app.controller('MCtrl', function($rootScope, $scope,$timeout){
  $scope.Math = window.Math;
  var numInputs = {},curInput;
  $scope.addNumInput = function(ele){
    var p = ele, group;
    while (p = p.parent()){
      if (group = p.attr('group')){
        break;
      };
    }
    if (!(p=numInputs[group])){p=numInputs[group]=[];}
    ele.r4Group = group;
    ele.r4Index = p.length;
    ele.r4NumInput = ele.attr('r4-num-input');
    p.push(ele);
  };
  $scope.selNumInput = function($event,ele,field){
    angular.forEach(numInputs[ele.r4Group], function(e){e.removeClass('current');});
    ele.addClass('current');
    curInput = ele;
    $scope.currentField = ele.r4NumInput;
    $scope.val.showkeyboard = true;
    $timeout(function(){$scope.$apply();},0);
    $event.preventDefault();
  }
  $scope.openTBrowser = function(url){
    var cfg, ref;
    cfg = {
      toolbar: {
        height: 44,
        color: '#E03131'
      },
      closeButton: {
        image: 'close',
        imagePressed: 'close_pressed',
        align: 'right',
        event: 'closePressed'
      },
      fullscreen: false
    };
    ref = RMSrv.openTBrowser(url, cfg);
    return ref.addEventListener(cordova.ThemeableBrowser.EVT_ERR, function(e) {
      return console.log(e);
    });
  }
  $scope.val = val;
  $scope.mortgage = {};
  $scope.transfertax = {};
  $scope.modes = modes;
  $scope.btnss = btnss;

  if (!window.localStorage){window.localStorage = {};}
  function urlParams() {
    var qs = location.search;
    qs = qs.split("+").join(" ");
    var params = {}, tokens,
        re = /[?&]?([^=]+)=([^&]*)/g;
    while (tokens = re.exec(qs)) {
      try{
        params[decodeURIComponent(tokens[1])]
            = decodeURIComponent(tokens[2]);
      }
      catch(e){}
    }
    return params;
  }
  var params = urlParams();

  if ($scope.price = parseInt( (params.p || 'null') , 10)){
    //console.log ("data is integer");
  }else {
    $scope.price =  ( 600000 || localStorage['calc-price']);
    //console.log ("data is not an integer");
  }
  if (!params.prov){
    $scope.prov = "ON";
  } else if (params.prov && ['ON','BC','AB'].indexOf(params.prov) > -1){
    $scope.prov = params.prov;
  } else  {
    $scope.prov = 'Other';
  }

  $scope.downpayPercentage = params.dppercent || localStorage['calc-downpay-percentage'] || 20;
  //$scope.downpayAmount = 0;
  $scope.interestRate = params.irate || localStorage['calc-interest-rate'] || 3.5;
  $scope.amortization = params.ayear || localStorage['calc-amortization'] || '25';

  $scope.monthlyRent = 1800;
  $scope.annualRentIncrease = localStorage['calc-annualRentIncrease'] || 1.5;
  $scope.annualIncreaseVal = localStorage['calc-annualIncreaseVal'] || 6.5;
  //$scope.annualPropertyTax = $scope.price * 0.008;
  $scope.annualPropertyTax = 0;
  $scope.yearsBeforeSelling = localStorage['calc-yearsBeforeSelling'] || 10;
  $scope.otherAnnualFee = 700;

  if (params.pt) {
    $scope.annualPropertyTax = parseFloat(params.pt);
  }
  if (params.mfee) {
    $scope.otherAnnualFee = parseFloat(params.mfee) * 12 ;
  }
  if (params.avgR) {
    $scope.monthlyRent = parseFloat(params.avgR);
  }

  $scope.initialBuyRent = function() {
    $scope.totalMonthlyRent = 0;
    $scope.yearlyRentList = [];
    $scope.totalPaymentsBuy = 0;
    $scope.yearlyBuyList = [];
    $scope.totalIncreaseVal = 0;
    $scope.mortgagePaidDown = 0;
    $scope.homeEquityIncrease = [];
    $scope.yearlyMortgageBalance = [];
    $scope.yearArray = [];
    $scope.additionalCostOfBuy = 0;
    $scope.benefitBuying = 0;
    $scope.otherTotalAnnualFee = 0;
  }
  $scope.ctx = document.getElementById("chart").getContext('2d');
  var chart = null;

  $scope.Feet = localStorage['calc-feet'] || 1;
  $scope.Square_Feet = localStorage['calc-sq-feet'] || 1;
  $scope.celsius = localStorage['calc-celsius'] || 20;
  $scope.mode = function (m){
    angular.forEach(numInputs[val.mode.mode], function(e){e.removeClass('current');});
    if ('string' == typeof m){
      val.mode = modes[0];
      for (k = 0; k < modes.length; k++){
        if (modes[k].mode == m){
          val.mode = modes[k];
          break;
        }
      }
    }else{
      val.mode = m;
    }
    $scope.currentField = null;
    $scope.val.showkeyboard = false;
    calc();
  };
  $scope.toggle = function(ele,mode){
    var togEle=document.getElementById(ele).style.display = mode;
    var mask = document.getElementsByClassName("backdrop")[0];
    var container = document.querySelector('#toolWrapper');
    container.removeChild(mask);
  };
  $scope.pressed = function ($event,act,name){
    switch (act){
      case 'close':
        $scope.val.showkeyboard = false;
        curInput.removeClass('current');
        break;
      case 'backone':
        var s = '' + $scope[$scope.currentField];
        console.log(s);
        $scope[$scope.currentField] = s.substr(0,Math.max(0,s.length - 1));
        calc();
        break;
      case 'next':
        group = numInputs[curInput.r4Group];
        idx = curInput.r4Index + 1
        if (idx >= group.length){idx = 0;}
        $scope.selNumInput($event,group[idx]);
        break;
      case 'previous':
        group = numInputs[curInput.r4Group];
        idx = curInput.r4Index - 1
        if (idx < 0 ){idx = group.length - 1;}
        $scope.selNumInput($event,group[idx]);
        break;
      case 'clear':
        $scope[$scope.currentField] = '';
        calc();
        break;
      default:
        input(name);
    }
  };
  var input = function (char){
    var s;
    if (s = $scope[$scope.currentField]){
      $scope[$scope.currentField] += char;
    }else{
      $scope[$scope.currentField] = char;
    }
    calc();
  };
  var calc = $scope.calc = function(){
    var k;
    $scope.error = null;
    switch ($scope.val.mode.mode){
      case 'mortgage':
        if ($scope.currentField == 'downpayAmount'){
          $scope.downpayPercentage = Math.round($scope.downpayAmount / $scope.price * 1000) / 10;
        }else{
          $scope.downpayAmount = Math.round($scope.price * $scope.downpayPercentage ) / 100;
        }
        if (! $scope.downpayAmount){$scope.downpayAmount = 0; $scope.downpayPercentage = 0;}
        if ($scope.interestRate > 30){
          $scope.error = "Error: Intereset Rate";
          break;
        }
        if ($scope.downpayPercentage > 100){
          $scope.error = "Error: Downpayment";
          break;
        }
        $scope.mortgage.amount = $scope.price - $scope.downpayAmount;
        $scope.mortgage.monthlyPayment = mortgage_payment_canada($scope.mortgage.amount,$scope.interestRate,$scope.amortization);
        $scope.mortgage.interest1 = mortgage_interest($scope.mortgage.amount,$scope.interestRate);
        $scope.mortgage.principal1 = $scope.mortgage.monthlyPayment - $scope.mortgage.interest1;
        if ($scope.mortgage.principal1 < 0){
          $scope.error = "Error: Too High Interest Rate ";
        }
        localStorage['calc-price'] = $scope.price;
        localStorage['calc-downpay-percentage'] = $scope.downpayPercentage;
        localStorage['calc-interest-rate'] = $scope.interestRate;
        localStorage['calc-amortization'] = $scope.amortization;
        break;
      case 'rentOrBuy':
        $scope.initialBuyRent();
        if ($scope.currentField == 'downpayAmount'){
          $scope.downpayPercentage = Math.round($scope.downpayAmount / $scope.price * 1000) / 10;
        }else{
          $scope.downpayAmount = Math.round($scope.price * $scope.downpayPercentage ) / 100;
        }
        if (! $scope.downpayAmount){$scope.downpayAmount = 0; $scope.downpayPercentage = 0;}
        if ($scope.interestRate > 30){
          $scope.error = "Error: Intereset Rate";
          break;
        }
        if ($scope.downpayPercentage > 100){
          RMSrv.dialogAlert('#{_('Downpayment % can not be more than 100')}');
          break;
        }
        if ($scope.annualPropertyTax == '') {
          $scope.annualPropertyTax = 0;
        }
        if ($scope.otherAnnualFee == '') {
          $scope.otherAnnualFee = 0;
        }
        if (parseInt($scope.yearsBeforeSelling) > parseInt($scope.amortization)) {
          RMSrv.dialogAlert('#{_('Years before selling can not be more than amortization years')}');
          $scope.yearsBeforeSelling = parseInt($scope.amortization);
          break;
        }
        $scope.mortgage.amount = $scope.price - $scope.downpayAmount;
        $scope.mortgage.monthlyPayment = mortgage_payment_canada($scope.mortgage.amount,$scope.interestRate,$scope.amortization);

        $scope.otherTotalAnnualFee = parseFloat($scope.annualPropertyTax) + parseFloat($scope.otherAnnualFee)

        let currentMonthlyRent = $scope.monthlyRent
        for (var i = 1; i <= $scope.yearsBeforeSelling; i++) {
          $scope.totalMonthlyRent += currentMonthlyRent * 12;
          $scope.yearlyRentList.push($scope.totalMonthlyRent);
          currentMonthlyRent = currentMonthlyRent * (1 + $scope.annualRentIncrease / 100);

          $scope.totalPaymentsBuy += $scope.mortgage.monthlyPayment * 12 + $scope.otherTotalAnnualFee;
          $scope.yearlyBuyList.push($scope.totalPaymentsBuy)

          $scope.yearArray.push(i);
        }

        $scope.totalIncreaseVal = $scope.price * Math.pow((1+$scope.annualIncreaseVal/100), $scope.yearsBeforeSelling) - $scope.price;

        //home equity
        let periodicInterestRate = Math.pow(Math.pow((1 + ($scope.interestRate / 100) / 2), 2),(1/12)) - 1
        let effectiveAnnualRate = Math.pow((1 + periodicInterestRate), 12) - 1
        let months = $scope.yearsBeforeSelling * 12;
        let monthsInYear = 1;

        let houseValue = $scope.price * (1 + $scope.annualIncreaseVal/100)
        let lastHouseValue = $scope.price;
        let houseIncreaseVal = houseValue - lastHouseValue;

        var interest;
        var p
        var mortgagePayment = $scope.mortgage.amount;
        for (var i = 1; i <= months; i++) {
          interest = mortgagePayment * periodicInterestRate;
          principal = $scope.mortgage.monthlyPayment - interest;

          mortgagePayment -= principal;
          $scope.mortgagePaidDown += principal

          if (monthsInYear == 12) {
            monthsInYear = 1;
            //$scope.homeEquityIncrease.push($scope.mortgagePaidDown + houseIncreaseVal);
            $scope.homeEquityIncrease.push(houseIncreaseVal);

            //mortgage balance
            $scope.yearlyMortgageBalance.push($scope.mortgage.amount - $scope.mortgagePaidDown);

            // house value increase
            lastHouseValue = houseValue
            houseValue = lastHouseValue * (1 + $scope.annualIncreaseVal/100);
            houseIncreaseVal += houseValue - lastHouseValue;
          } else {
            monthsInYear += 1;
          }
        }

        $scope.additionalCostOfBuy = $scope.totalMonthlyRent - $scope.totalPaymentsBuy;
        $scope.benefitBuying = $scope.mortgagePaidDown + $scope.additionalCostOfBuy + $scope.totalIncreaseVal;

        if (chart == null) {
          // fix Cannot read property 'childNodes' of undefined issue (chartjs in angularjs)
          setTimeout(function() {
            chart = new Chart($scope.ctx, {
              type: 'line',
              data: {
                labels: $scope.yearArray,
                datasets: [{
                  id: 'rent',
                  label: '#{_('Rent', 'Compare')}',
                  fill: false,
                  data: $scope.yearlyRentList,
                  borderColor: 'rgb(255, 99, 132)',
                  borderWidth: 3
                },
                {
                  id: 'buy',
                  label: '#{_('Buy', 'Compare')}',
                  fill: false,
                  data: $scope.yearlyBuyList,
                  borderColor: 'rgb(255, 159, 64)',
                  borderWidth: 3
                },
                {
                  id: 'homeEquityIncrease',
                  label: '#{_('Home Equity Increase')}',
                  fill: false,
                  data: $scope.homeEquityIncrease,
                  borderColor: 'rgb(255, 205, 86)',
                  borderWidth: 3
                },
                {
                  id: 'mortgageBalance',
                  label: '#{_('Mortgage Balance')}',
                  fill: false,
                  data: $scope.yearlyMortgageBalance,
                  borderColor: 'rgb(75, 192, 192)',
                  borderWidth: 3
                }],
                options: {
                  responsive: true,
                  title: {
                    display: true,
                    text: 'Chart with Multiline Labels'
                  },
                }
              }
            });
          }, 0);
        } else {
          chart.data.labels = $scope.yearArray;
          chart.data.datasets.forEach((dataset) => {
            var id = dataset.id;
            if (id == 'rent') {
              dataset.data = $scope.yearlyRentList
            }
            if (id == 'buy') {
              dataset.data = $scope.yearlyBuyList;
            }
            if (id == 'homeEquityIncrease') {
              dataset.data = $scope.homeEquityIncrease;
            }
            if (id == 'mortgageBalance') {
              dataset.data = $scope.yearlyMortgageBalance;
            }
          });
          chart.update();
        }

        localStorage['calc-price'] = $scope.price;
        localStorage['calc-downpay-percentage'] = $scope.downpayPercentage;
        localStorage['calc-interest-rate'] = $scope.interestRate;
        localStorage['calc-amortization'] = $scope.amortization;
        localStorage['calc-yearsBeforeSelling'] = $scope.yearsBeforeSelling;
        localStorage['calc-annualRentIncrease'] = $scope.annualRentIncrease;
        localStorage['calc-annualIncreaseVal'] = $scope.annualIncreaseVal;
        break;
      case 'transfertax':
        $scope.transfertax.tax = false;
        $scope.transfertax.tax1 = false;
        $scope.transfertax.tax_t = false;
        $scope.transfertax.tax_m = false;
        if($scope.prov == 'ON'){
          $scope.transfertax.tax = ontario_transfer_tax($scope.price);
          $scope.transfertax.tax1 = ontario_transfer_tax($scope.price,true);
          if ($scope.torontoProperty && $scope.torontoProperty !== 'false'){
            $scope.transfertax.tax += toronto_transfer_tax($scope.price);
            $scope.transfertax.tax1 += toronto_transfer_tax($scope.price,true);
          }
        } else if ($scope.prov == 'BC') {
          $scope.transfertax.tax = british_columbia_transfer_tax($scope.price, false, $scope.foreignBuyer);
          $scope.transfertax.tax1 = british_columbia_transfer_tax($scope.price, true, $scope.foreignBuyer);
        } else if ($scope.prov == 'AB') {
         $scope.transfertax.tax_t = alberta_title_fee($scope.price);
        }
        localStorage['calc-price'] = $scope.price;
        localStorage['calc-toronto-property'] = $scope.torontoProperty;
        break;
      case 'exchange':
        if ($scope.currentField && ($scope.currentField !== 'price')){
          k = $scope.currentField;
          $scope.price = $scope[k] / exRates[k];
        }
        for (k in exRates){
          if (exRates.hasOwnProperty(k) && (k != $scope.currentField)){
            $scope[k] = $scope.price * exRates[k];
          }
        }
        localStorage['calc-price'] = $scope.price;
        break;
      case 'length':
        k = $scope.currentField;
        if (! lengthUnits[k]){k = 'Feet';}
        var l = $scope[k] / lengthUnits[k];
        for (var key in lengthUnits){
          if (lengthUnits.hasOwnProperty(key) && (key != k)){
            $scope[key] = l * lengthUnits[key];
          }
        }
        localStorage['calc-feet'] = $scope.Feet;
        break;
      case 'area':
        k = $scope.currentField;
        if (! areaUnits[k]){k = 'Square_Feet';}
        var l = $scope[k] / areaUnits[k];
        for (var key in areaUnits){
          if (areaUnits.hasOwnProperty(key) && (key != k)){
            $scope[key] = l * areaUnits[key];
          }
        }
        localStorage['calc-sq-feet'] = $scope.Square_Feet;
        break;
      case 'temp':
        if ('fahrenheit' == $scope.currentField){
          $scope.celsius = ($scope.fahrenheit - 32) * 5 / 9;
        }else{
          $scope.fahrenheit = ($scope.celsius * 9 / 5) + 32;
        }
        localStorage['calc-celsius'] = $scope.celsius;
        break;
    }
  };
  $scope.torontoProperty = false;
  if (k = params.trnt){
    k = k.toLowerCase();
    if ((k == '0') || (k == 'false') || (k == 'f')){
      k = false;
    }else{
      k = true;
    }
    $scope.torontoProperty = k;
  }else if ('undefined' != typeof localStorage['calc-toronto-property']){
    $scope.torontoProperty = (localStorage['calc-toronto-property'] && (localStorage['calc-toronto-property'] != 'false'));
  }
  $scope.mode('#{@mode}'||'mortgage');
});
      </script>
    </div>

  </body>

"""
