# fs = require 'fs'
# path = require 'path'
libproperties = INCLUDE 'libapp.properties'
{hasChinese} = INCLUDE 'lib.helpers_string'
{dotdate,dateFormat} = INCLUDE 'lib.helpers_date'
libi18n = INCLUDE 'lib.i18n'
# dots = INCLUDE 'lib.dot'
{fetch} = INCLUDE 'lib.fetch'
LZString = INCLUDE 'lib.lz-string'
libUser = INCLUDE 'libapp.user'
geolib = INCLUDE 'geolib'
setLang = DEF 'setLang'
{respError} = INCLUDE 'libapp.responseHelper'

config = CONFIG(['share','contact'])
debug = DEBUG()

# UserListings = COLLECTION 'chome','user_listing'

Properties = MODEL 'Properties'

{setPropWebUrl} = INCLUDE 'libapp.propWeb'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
# getProcessUserInfoOpt = DEF 'getProcessUserInfoOpt'
loadDotTemplates = DEF 'loadDotTemplates'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
SaveCMAModel = MODEL 'SaveCMA'
# cors = require('cors');
APP '1.5'
APP 'htmltoimg', true
# validUrl = (req, res, next) ->
#   if !req.query.url
#       next(new Error('No url specified'));
#   else if (typeof req.query.url !== 'string' || url.parse(req.query.url).host === null)
#       next(new Error(`Invalid url specified: ${req.query.url}`));
#   else
#       next();
templateList = []
nameIndexMapping = {}
templateFolderPath = config.share?.templatePath
listingPicUrls = libproperties.listingPicUrls
rebuildMapping = DEF 'rebuildMapping'
reloadTemplates = DEF 'reloadTemplates'
CMA_XINCLUDED = {
  'cac_inc': 'Centre Air Condition',
  'heat_inc': 'Heat',
  'hydro_inc': 'Hydro',
  'prkg_inc': 'Parking',
  'water_inc': 'Water',
  'comel_inc': 'Common Elements',
  'tv': 'TV',
  'wifi': 'Wifi'
}

# doReload()
reloadTemplates templateFolderPath, (err, ret)->
  if err
    console.error err
  templateList = ret
  nameIndexMapping=rebuildMapping templateList
  # console.log ret

POST 'reload', (req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    return resp.send {ok:1,l:[]} unless user
    reloadTemplates templateFolderPath, (err, ret)->
      if err
        console.error err
        return resp.send {ok:0, err:err}
      # setTimeout ()->
      templateList = ret
      nameIndexMapping = rebuildMapping templateList
      GroupModel.getUserGroups user, (err,groups)->
        return resp.send {ok:0,err:err.toString()} if err
        groups = groups.map (g)-> return g.nm
        templateList2 = filterTempList req,user,templateList,groups
        resp.send {ok:1, l:templateList2}
    # console.log ret
    # , 500
    # helpers.wait 500,{},()->
isWechatPic = (url)->
  return /qpic\.cn\/mmbiz/.test url

# return true if url is valid prop url
isPropPicUrlDomain = (url)->
  url ?= ''
  if not /^http/.test url
    return false
  part1 = url.split('?')[0]
  if /\.(js|css|html)$/.test part1
    return false
  return true if isWechatPic(url)
  # DDF prop: //cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262822684/0/640/480/664fd7c71fb0958de0d24fd1421ebb31/16/5f4d7e97335337192889569d24f77a85/262822684.JPG
  if /paragonrels\.com\/ParagonImages/.test url
    return true
  if /^http(s)?:\/\/img\.realmaster\.com\/.*\.jpg(\?.*)?/.test url
    return true
  if /^http(s)?:\/\/f\.realmaster\.(com|cn)\//.test url
    return true
  if /^http(s)?:\/\/f\.realexpert\.(com|cn)\//.test url
    return true
  if /^http(s)?:\/\/trreb-image\.ampre\.ca\//.test url
    return true
  if /^http(s)?:\/\/.*\.cloudfront.net\//.test url
    return true
  return false

GET 'imgflyer', (req, resp)->
  return resp.send {ok:0, e:MSG_STRINGS.ACCESS_DENIED} unless req.getDevType() is 'app'
  data = req.param 'data'
  url = req.param 'url'
  return resp.send {ok:0, e:MSG_STRINGS.ACCESS_DENIED} if not (data or url)
  UserModel.appAuth {req,resp}, (user) ->
    # TODO: may check referer?
    #and (not /schimgs/.test(req.query.url))#and not(req.param('src') is 'forum')
    try
      # url = req.query.url
      if data
        data = JSON.parse(LZString.decompressFromEncodedURIComponent data)
        uri = decodeURIComponent data.url
      else
        uri = url
      return resp.send {ok:0, e:MSG_STRINGS.ACCESS_DENIED} unless isPropPicUrlDomain(uri)
      # NOTE: plguin access for wechat-rn-lib
      if not isWechatPic(uri)
        if (not UserModel.accessAllowed('vipUser',user))
          return resp.send {ok:0, e:MSG_STRINGS.ACCESS_DENIED}
      if hasChinese(uri)
        urlParts = uri.split('/')
        urlParts[urlParts.length - 1] = encodeURIComponent(urlParts[urlParts.length - 1])
        uri = urlParts.join '/'
      #helpers.http_get url, {binary: true, timeout:10000}, (err,data) ->
      try
        response = await fetch uri,{method:'GET',timeout:9000,responseType:'arraybuffer'}
      catch err
        return resp.send {ok:0, e:err}
      if response.ok
        return resp.send(response.data)
      else
        return resp.send {ok:0, e:response.data}
      # request {method:'GET',uri,encoding:null,timeout:9000},(err, response, data)->
      #   if err
      #     # console.log '------------',response,err
      #     return resp.send {ok:0, e:err}
      #   return resp.send(data)
    catch error
      console.log error
      return resp.send {ok:0, e:MSG_STRINGS.ACCESS_DENIED}

# TODO: need show but not clickable/hide to other user/
filterTempList = (req, user, list, groups)->
  # console.log '++++',groups
  groups ?= []
  ret = []
  lang = req.locale()
  for t in list
    t2 = Object.assign {},t
    if (t.owner is 'all') or (req.isAllowed(t.owner))
      t2.isUseable = true
    else
      t2.isUseable = false
    if t.group?
      if t.group not in groups
        continue
      else
        t2.isUseable = true
    if lang isnt 'en'
      # for t in templateList2
      t2.name = libi18n.JTFTAUTO(t.name_zh,lang)
      t2.description = libi18n.JTFTAUTO(t.description_zh,lang)
    ret.push t2
  ret.sort (a,b)->
    return 1 if a.sort > b.sort
    return -1 if a.sort < b.sort
    0
  ret


GET 'templatelist', (req,resp)->
  id = req.param 'id'
  unless id
    return resp.ckup 'generalError', {err:'No id specified!'}
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->
    isAdmin = req.isAllowed('devGroup')
    isVipRealtor = req.isAllowed('isVipRealtor')
    GroupModel.getUserGroups user, (err,groups)->
      return resp.ckup 'generalError', {err:err.toString()} if err
      groups = groups.map (g)-> return g.nm
      tempList = filterTempList req,user,templateList,groups
      resp.ckup 'templatelist', {config,templateList:tempList,id:id,isAdmin:isAdmin,isVipRealtor:isVipRealtor}, '_', {noAngular:1, noUserModal:1}


VIEW 'templatelist', ->
  text '''<style>
    #tempList .table-view-cell{
      padding: 10px 10px;
    }
    #tempList li img{
      width: 90px;
      height: 80px;
      vertical-align: top;
    }
    #tempList .wrapper{
      display:inline-block;
      width:calc(100% - 100px);
      font-size:15px;
      vertical-align: top;
      padding: 0 0 0 10px;
    }
    #tempList .desc{
      color:#777;
    }
    .contact{
      padding:12px 10px 10px 10px;
      color:#777;
      border-top: 1px solid #f1f1f1;
      text-align: center;
      font-size: 14px;
    }
    .contact  a{
      display:inline-block;
      width:50%;
      text-align:center;
    }
    .contact .pe{
      padding-top:12px;
    }
    </style>'''
  div class:'content',->
    if @isAdmin
      div id:'reloadTemplates', class:'btn btn-block btn-primary btn-sharp', -> _ 'Reload'
    ul id:'tempList', class: 'table-view temp-list', ->
    div class:'contact',->
      text _ 'For company specialized template, please contact us.'
      div class:'pe',->
        a class:'phone', href:'tel:************', -> '************'
        a class:'email', href:"mailto:#{@config.contact?.defaultReciveEmail or '<EMAIL>'}", -> @config.contact?.defaultReciveEmail or '<EMAIL>'

  js '/js/jquery-1.10.1.min.js'
  coffeejs {vars:{
    templateList:@templateList,
    id:@id,
    isVipRealtor:@isVipRealtor,
    strOptTip:@req.l10n('Available only for Premium VIP user! Upgrade and get more advanced features.'),
    strLater:@req.l10n('Later')
    strSeemore:@req.l10n('See More')
    lang:@req.locale()
    }}, ->
      null
  js '/js/templatelist.min.js'

# verGTE = DEF 'verGTE'

findPropDataByIdOrIdsAndRender = ({req,resp,id,ids,folder,showPrint}) ->
  render = (data)->
    try
      # html = dots[entry](data)
      html = tempFn(data, req)
    catch error
      console.error  error
      return resp.ckup 'generalError', {err:'Failed to gen html'}
    # TODO: older version rg. 6.2.0 fix this
    showSign = req.param 'showSign'
    locLang = req.param 'lang'
    selModel = req.param 'selModel'
    opt = {
      html:html,
      shareFriend:(not req.verGTE('6.1.2')) or (req.verGTE('6.1.3')),
      shareTimeline:(not req.verGTE('6.1.2')) or (req.verGTE('6.1.3')),
      supportPng:(req.verGTE('6.2.8'))
    }
    opt = Object.assign opt,{showSign,locLang,props:data.props,showPrint,selModel} if showSign
    resp.ckup 'dotViewHolder',opt,'_',{
      noAngular:1,
      noRatchetJs:1,
      # noRatchet:1,
      noUserModal:1}
      # noAppCss:1}
  unless folder
    return resp.ckup 'generalError', {err:'Need template name'}
  idx = nameIndexMapping[folder]
  if not idx?
    return resp.ckup 'generalError', {err:'Template not found'}
  doc = templateList[idx]
  if not doc.name
    return resp.ckup 'generalError', {err:'Template not found'}
  # path= templateFolderPath+'/'+doc.folder+'/nobranded.html'
  # fileText = fs.readFileSync(path,'utf8')
  fileText = doc.fileText
  if not fileText
    return resp.ckup 'generalError', {err:'Template no file text'}
  # imgPath = doc.imgPath
  # fileText = fileText.replace(/src=\"\.\//ig,'src="'+imgPath)
  # fileText = fileText.replace(/url\(\"\.\//ig,'url("'+imgPath)
  tempFn = doc.tempFn
  # tempFn = dots.template(fileText)
  if not tempFn
    return resp.ckup 'generalError', {err:'Template no tempFn'}
  UserModel.appAuth {req,resp}, (user) ->
    if not user
      return resp.ckup 'generalError', {err:'Need Login'}
    setLang req if ids
    selModel = req.param 'selModel'
    cusContent = req.param 'cusContent'
    getProperties {id,ids,req,user,selModel,cusContent},(err,ret)->
      msg = {}
      doc.msg ?= {}
      if Object.keys doc.msg
        for _id,v of doc.msg
          if 'object' is typeof v
            msg[_id] = req.l10n(v.k,v.ctx)
          else
            msg[_id] = req.l10n v
      locale = req.locale()
      tmpUser = UserModel.getProcessedInfo {lang:locale, user}
      data = { prop: ret, msg:msg , user:tmpUser, lang:locale, selModel}
      if ids
        delete data.prop
        data.props = ret
        data.showBsmt = not libproperties.maskFieldAllOrNotAll(data.props,'propType','Condo',true)
        data.showStoreys = not libproperties.maskFieldAllOrNotAll(data.props,'propType','Condo',false)
        data.showMaint = not libproperties.maskFieldAllOrNotAll(data.props,'stp_en','Lease',true)
      render(data)

# /1.5/htmltoimg/cmaWebDottemplate
# web端渲染CMA
GET 'cmaWebDottemplate',(req,resp)->
  req.setupL10n() unless req._
  unless (_id = req.param 'id')
    return resp.ckup 'generalError', {err:'Template not found'}
  try
    result = await SaveCMAModel.getIdsById _id
  catch err
    debug.error err
    return respError {category:MSG_STRINGS.DB_ERROR, resp}
  ids = result.ids.join(',')
  folder = 'comparetable'
  showPrint = true
  findPropDataByIdOrIdsAndRender({req,resp,ids,folder,showPrint})

# /1.5/htmltoimg/idsData
# 根据ids查找房源具体信息
POST 'idsData',(req,resp)->
  UserModel.appAuth {req,resp}, (user) ->
    unless (ids = req.body.ids)
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or (req.isAllowed 'devGroup'))
      debug.error {msg:"htmltoimg/idsData:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await getPropByIds {ids,req,user}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    for item in result
      if libproperties.isMlsPrefix(item.id)
        item.id = item.id.slice(3)
    return resp.send {ok:1,result}

# /1.5/htmltoimg/dottemplate
GET 'dottemplate',(req,resp)->
  req.setupL10n() unless req._
  folder = req.param 'nm'
  id = req.param 'id'
  ids = req.param 'ids'
  if not (id or ids)
    return resp.ckup 'generalError', {err:'Prop Id not found'}
  findPropDataByIdOrIdsAndRender({req,resp,id,ids,folder,showPrint:false})

getProperties=({id,ids,req,user,selModel,cusContent},cb)->
  if ids
    try
      newList = await getPropByIds {ids,req,user,selModel,cusContent}
      return cb null,newList
    catch err
      console.error err
      return cb err
  return getPropById {id,req,user},cb

getPropById=({id,req,user},cb)->
  if not id
    return cb 'Prop Id not found'
  Properties.findOneByFuzzID id,{projection:{editHis:0}},(err,prop)->
    if err
      console.error err
      return cb err.toString()
    if not prop
      return cb 'Prop Not found!'
    # TODO: translate
    # translateRmPropDetailOpt = {_:req._,_ab:req._ab,l10n:req.l10n,locale:req.locale,headers:req.headers,cookies:req.cookies}
    locale = req.locale()
    transDDF =  req.getConfig('transDDF')
    prop = propTranslate.translate_rmprop_detail({locale,transDDF}, prop)
    if /^RM/.test id
      scope = {ml_num:prop.sid}
      prop.picUrls = libproperties.convert_rm_imgs(scope,prop.pic,'reset')
      unless prop.ptype2
        prop.ptype2 = (prop.ptp or '')+(prop.pstyl or '')
      unless prop.saletp
        prop.saletp = prop.stp or ''
      unless prop.sid
        prop.sid = prop.id
    else
      # genListingPicReplaceUrls
      prop.picUrls = listingPicUrls(prop, {isCip:req.isChinaIP(), isPad:req.isPad()})
    l10n = (a,b)->req.l10n a,b
    _ab = (a,b)->req._ab(a,b)
    libproperties.setUpPropRMPlusFields prop,l10n,_ab
    propPrice = prop.sp or prop.lp or prop.lpr
    prop.FormatedLp =libproperties.currencyFormat(propPrice,'$',0)
    length = Math.max(2, prop.picUrls.length)
    for i in [0..length]
      prop['img'+i] = prop.picUrls[i] or '/img/noPic.png'
    return cb null,prop

# compare table获取数据列表
getPropByIds=({ids,req,user,selModel,cusContent})->
  if not ids
    throw new Error 'Prop Id not found'
  ids = ids.split(',') if 'string' is typeof ids
  isAllowedPropAdmin = UserModel.accessAllowed('propAdmin',user)
  isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
  list = await Properties.getAndSortPropListByIdsInProperties ids,libproperties.COMPARE_TABLE_FIELDS
  l10n = (a,b)->req.l10n(a,b)
  for item in list
    isCondo = libproperties.isCondo(item)
    if bedSize = joinBedroomSize item.rms,isCondo
      item.bedSize = bedSize
    if xInclude = joinXIncluded item,l10n
      item.xInclude = xInclude
    item.sldd = dotdate item.sldd if item.sldd
    item.onD = dotdate item.onD if item.onD
    item.lpValStrRed = libproperties.currencyFormat(item.lp,'CAD$',0) if item.lp
    item.lpValStrRed = libproperties.currencyFormat(item.lpr,'CAD$',0) if item.lpr
    item.spValStrRed = libproperties.currencyFormat(item.sp,'CAD$',0) if item.sp
  list = propTranslate.translate_rmprop_list(req, list)
  libproperties.processProps list,{isCip:req.isChinaIP()}
  titleTaxyr = 0
  isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
  for p in list
    Properties.addPropAdditionalFields({
      user,prop:p,
      isAllowedPropAdmin,
      isAllowedVipUser,
      l10n,
      _ab:(a,b)->req._ab(a,b),
      apisrc:req.param('apisrc')
      isRealGroup,
      simplfyPtp:false})
    setPropWebUrl(req,p)
    if p.poss_date and p.psn
      p.occupancy = "#{dotdate p.poss_date} (#{p.psn})"
    else if p.poss_date and (not p.psn)
      p.occupancy = dotdate p.poss_date
    else if (not p.poss_date) and p.psn
      p.occupancy = p.psn
    if p.market?.rmProp # 真楼花不显示dom
      delete p.dom if p.dom
    if titleTaxyr < p.taxyr
      titleTaxyr = p.taxyr
    if p.sqft
      p.size = "#{p.sqft} #{l10n('ft&sup2;')}"
    else if p.rmSqft
      p.size = "#{p.rmSqft}(#{l10n('Estimated')}) #{l10n('ft&sup2;')}"
    Properties.deletePropPriviteFields({user,prop:p})
  list = libproperties.dealBuiltYearField({list,l10n:(a,b)->req.l10n(a,b)})
  newList = await Properties.getRmStoreyInfoList list
  if selModel is 'diff'
    addContrastDifference newList
  if cusContent
    customContent = await SaveCMAModel.getCustomContent(user?._id)
    newList = dealCustomContent(newList,customContent.content)
  newList.titleTaxyr = titleTaxyr
  return newList

# TODO: move to prop lib
addContrastDifference = (list)->
  compareProp = list.shift()
  for prop in list
    for f in ['bthrms','bdrms','lp','sp','kch']
      if compareProp[f] and prop[f]
        prop['diff'+f] = prop[f] - compareProp[f]
        if f in ['lp','sp']
          absDiff = Math.abs prop['diff'+f]
          absDiff = libproperties.currencyFormat(absDiff,'$',0)
          prop['diff'+f+'Str'] = absDiff
    dist = geolib.getDistance(
      {latitude: compareProp.lat, longitude: compareProp.lng},
      {latitude: prop.lat, longitude: prop.lng}
    )
    prop.dist = "#{dist}m"
  list.unshift(compareProp)

# 将房子的bedroom的area 拼接成一个 eg:(19.22 + 18.52)
joinBedroomSize = (rms,isCondo) ->
  unless rms
    return false
  rms = libproperties.getRooms({rms})
  size = []
  for item in rms
    if isNaN(parseInt(item.area))
      continue
    if (item.t is 'Primary B') or /Bedroom/i.test(item.t) or /(Br|Bed|Bdrm|Bedro)$/i.test(item.t)
      size.push item.area
    else if (/Den/.test(item.t) and isCondo)
      size.push item.area
  if size.length > 0
    return "(#{size.join(' + ')})"
  return false

# mapping CMA_XINCLUDED里面有数据的值
getIncludeFromCMAXINCLUDED = (prop,l10n) ->
  xInclude = []
  for k,v of CMA_XINCLUDED
    if ((prop[k] is 'Y') or (prop[k] is 1))
      xInclude.push(l10n(v))
  return xInclude

# 将管理费包含的内容展示在页面，优先mfeeInc字段里的数据，如果没有在考虑CMA_XINCLUDED里面包含的字段
joinXIncluded = (prop,l10n) ->
  unless prop
    return false
  xInclude = []
  if (Array.isArray(prop.mfeeInc) and prop.mfeeInc.length)
    xInclude = prop.mfeeInc
  else if (Array.isArray(prop.RentIncludes) and prop.RentIncludes.length)
    xInclude = prop.RentIncludes
  else
    xInclude = getIncludeFromCMAXINCLUDED(prop,l10n)
  if xInclude.length > 0
    return xInclude.join(', ')
  return false

# 将CMA排序前编写的自定义内容融合在当前列表中
dealCustomContent = (newList,content) ->
  for item in newList
    index = content.findIndex((el)-> return el.id.toString() is item._id.toString())
    if index >= 0
      item.bltYrField = content[index].bltYr if content[index].bltYr
      item.remark = content[index].remark if content[index].remark
      item.size = content[index].size if content[index].size
  return newList

VIEW 'dotViewHolder',->
  css '/css/sprite.min.css'
  text """
  <style>
  .bar-footer.footer-tab a span.tab-label{
    padding-top: 0px;
  }
  #shareModal{
    z-index:22;
  }
  #sortModal{
    z-index:22;
  }
  #shareModal .row .share{
    display:inline-block;
    width: 25%;
    text-align: center;
  }
  #shareModal .share span{
    display:block;
  }
  #shareModal .row{
    display: flex;
    padding: 10px;
  }
  #shareModal .share .desc{
    color:#666;
    font-size:12px;
    line-height: initial;
  }
  #cancelBtn {
    text-align: center;
    padding-top: 11px;
  }
  .backdrop{
    display:none;
  }
  body{
    height: auto !important;
    top: auto !important;
    bottom: auto !important;
    position: inherit !important;
  }
  .sprite50-52{
    margin: 7px auto;
  }
  #id_with_sign input:checked{
    outline: 0;
    background-color: white;
    border: 1px solid #5cb85c;
    color: white;
  }
  #id_with_sign input:checked:after{
    content: '\f00c';
    font: normal normal normal 14px/1 fa;
    font-family: fa;
    font-size: 9px;
    position: absolute;
    top: 1px;
    left: 2px;
    color: #5cb85c;
  }
  .footerDisplay{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .langSelect{
    display: table;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 12px;
  }
  .langLabel{
    display: table-cell;
    padding: 7px 7px;
    overflow: hidden;
    line-height: 1;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-left: 1px solid #ccc;
  }
  .propPart {
    height: calc(100% - 128px);
    overflow: auto;
    z-index: 21;
    position: fixed;
    background: #f1f1f1;
    top: 128px;
    width: 100%;
  }
  .propPart .propTable{
    display: flex;
    flex-wrap: wrap;
    font-size: 12px;
    padding-bottom: 50px;
  }
  .propTable .propInfo {
    background: #fff;
    border: 3px solid #f1f1f1;
    position: relative;
  }
  .propTable > div {
    width: 33.3333%;
    padding: 5px;
  }
  .propTable p {
    white-space: nowrap;
    margin: 0;
    font-size: 12px;
    overflow: hidden;
    width: 100%;
    line-height: 16px;
    text-overflow: ellipsis;
    padding-left: 3px;
  }
  .sortConfirm{
    position: relative;
    border: 0px;
    background: #e03131;
    color: #fff;
    border: 0px solid #fff;
    height: 44px;
    width: 50%;
  }
  .sortCancel{
    position: relative;
    border: 0px solid #fff;
    background: #fff;
    color: #8c8c8c;
    height: 44px;
    width: 50%;
  }
  .sortPosition{
    position: fixed;
    right: 0;
    left: 0;
    bottom: 0;
  }
  </style>
  """
  js '/js/lz-string.min.js'
  js '/js/sortable.min.js'
  div id:'dotRenderHTMLWrapperRM', class:'',->
    text @html
  div class:'bar bar-footer bar-standard footer-tab',style:'display:none',->
    div class:'footerDisplay',->
      div id:'printBtn', style:'display: flex;display:none',->
        a href:'javascript:;',onclick:'openPrint()',->
          span class:'icon fa fa-print'
          span class:'tab-label',-> _ 'Print'
      label id:'id_with_sign',style:'display:none;padding: 0 0 0 10px;width: 120px;',->
        input type:'checkbox', name:'sign',id:'sign',checked:'checked',onclick:'showSignature()'
        span style:'margin-left: 5px;',-> _ 'Signature'
      div id:'selModel',class:'langSelect',style:'display:none;margin-left:-30px;',->
        a id:'id_model_diff',href:'javascript:;',onclick:'saveSelfContent("diff")',->
          span class:'langLabel model_diff',-> _ 'Diff'
        a id:'id_model_list',href:'javascript:;',onclick:'saveSelfContent("list")',->
          span class:'langLabel model_list',-> _ 'List'
      div id:'selLang',class:'langSelect',style:'display:none',->
        a id:'id_share_lang_zh',href:'javascript:;',onclick:'changelanguage("zh-cn")',->
          span class:'langLabel lang_zh',-> '中'
        a id:'id_share_lang_en',href:'javascript:;',onclick:'changelanguage("en")',->
          span class:'langLabel lang_en',-> 'En'
      div style:'display: flex;',->
        a id:'sortBtn',href:'javascript:;',style:'margin-right:20px;display:none;',onclick:'sortClicked()',->
          span class:'icon fa fa-sort-amount-desc'
          span class:'tab-label',-> _ 'Sort'
        a id:'shareBtn',href:'javascript:;',->
          span class:'icon fa fa-rmshare'
          span class:'tab-label',-> _ 'Share'
 
  div class:'backdrop'
  div style:'display:none',->
    div id:'share-image'
  div id:'shareModal',class:'share-dialog modal modal-75pc',style:'display:none',->
    div class:'content', ->
      # wechat friend/wechat timeline/facebook/download
      div class:'row',->
        if @shareFriend
          div class:'share', onclick:'shareFriend()',->
            div class:'img', ->
              span class:'sprite50-52 sprite50-5-2'
            div class:'desc',-> _ 'Wechat Friend'
        if @shareTimeline
          div class:'share',->
            div class:'img', onclick:'shareTimeline()',->
              span class:'sprite50-52 sprite50-5-3'
            div class:'desc',-> _ 'Wechat Moment'
        # div class:'share',->
        #   div class:'img', onclick:'shareFacebook()',->
        #     img src:'/img/share_facebook.png'
        #   div class:'desc',-> _ 'Facebook'
        div class:'share', onclick:'downloadPic()',->
          div class:'img', ->
            span class:'sprite50-52 sprite50-6-2'
          div class:'desc',-> _ 'Download'
    div id:'cancelBtn',class:'bar bar-footer bar-standard',->
      text _ 'Cancel'
  div id:'sortModal',style:'display:none',->
    div class:'propPart', ->
      div id:'sortPreview',class:'propTable',->
      div class:'sortPosition',->
        button class:'sortConfirm',onclick:'saveSelfContent()',->
          text _ 'Confirm'
        button class:'sortCancel',onclick:'sortCancel()',->
          text _ 'Cancel'

  coffeejs {vars:{
    supportPng:@supportPng,
    showSign:@showSign,
    locLang: @locLang,
    selModel:@selModel,
    props: @props,
    showPrint: @showPrint
    }}, ->
      null
  js '/js/html2canvas.min.js'
  js '/js/dotViewHolder.min.js'


###
GET 'templateview', (req,resp, next) ->
  resp.ckup 'templateview',{}, '_' ,{}

VIEW 'templateview',->
  # css '/web/css/common.css'
  # css '/css/apps/forum.css'
  # js '/js/lz-string.min.js'
  js '/web/packs/commons.js'
  div id: "index", style:'height: 100%;',->
    text """<htmltoimg></htmltoimg>"""
  js '/web/packs/imgtohtml.js'
  # js '/js/jquery-2.1.1.min.js'
  # js '/web/packs/bootstrap.min.js'
###
