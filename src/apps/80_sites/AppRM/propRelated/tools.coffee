# accessAllowed = DEF '_access_allowed'
UserModel = MODEL 'User'


APP '1.5'
APP 'tools',true

# '/1.5/tools'
# @returns list of tools
GET (req,resp)->
  #UserModel.appAuth {req,resp},(user)-> # continue session
    #enabled = UserModel.accessAllowed 'AreaMeasure',user
    # enabled = true
  resp.ckup 'tools',{title:"RealMaster Tools",amEnabled:true}

VIEW 'tools',->
  text """
  <style>
  .table-view .table-view-cell{
    border-bottom: 1px solid #f1f1f1;
  }
  .table-view .table-view-cell .icon{
    min-width: 25px;
    color: #007aff;
    line-height: 21px;
  }
  </style>
  """
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: 'javascript:void(0);',onclick:'javascript:window.history.back();'
    # text ckup 'headerbarUserModalBtn'
    h1 class: 'title',-> _ @title

  # text ckup 'bottomNav',{actPos:30}

  div class: 'content', ->
    ul class: 'table-view', ->
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/mortgage', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-calculator fa-fw'
          div class: 'media-body',-> _ 'Mortgage'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/rentorbuy', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-calculator fa-fw'
          div class: 'media-body',-> _ 'Rent Or Buy'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/transtax', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-calculator fa-fw'
          div class: 'media-body',-> _ 'Transfer Tax'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/currency', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-dollar fa-fw'
          div class: 'media-body',-> _ 'Currency Exchange'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/length', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-arrows-h fa-fw'
          div class: 'media-body',-> _ 'Length'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/area', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-square-o fa-fw'
          div class: 'media-body/area',-> _ 'Area','measure'
      li class: 'table-view-cell media', ->
        a class: '', href: '/1.5/tools/temperature', dataIgnore: 'push', ->
          span class: 'media-object pull-left icon fa fa-sun-o fa-fw'
          div class: 'media-body',-> _ 'Temperature'
      if @amEnabled
        li class: 'table-view-cell media', ->
          a class: '', href: '/1.5/tools/measure', dataIgnore: 'push', ->
            span class: 'media-object pull-left icon fa fa-arrows fa-fw'
            div class: 'media-body',-> _ 'Measure'
      li class: 'table-view-cell media', ->
        a class: '', href: 'javascript:void(0)', onclick:"RMSrv.scanQR('/1.5/iframe?u=');",->
          span class: 'media-object pull-left icon fa fa-qrcode fa-fw'
          div class: 'media-body',-> _ 'Scan QR Code'
      # li class: 'table-view-cell media', style:'padding-right:40px;',->
      #   a dataIgnore: 'push', href: 'javascript:void(0)', onclick:'RMSrv.showInBrowser("https://my.matterport.com/show/?m=cXXCDg6L3GD");', ->
      #     span class: 'media-object pull-left icon fa fa-camera-retro fa-fw'
      #     div class: 'media-body',->
      #       text _ 'Virtual Tour'
      #       span class:'pull-right',style:'font-size:0.7em;color:white;background-color:#D9522C;border-radius:10px;padding:1px 10px;',->
      #         text _ 'Promote'
