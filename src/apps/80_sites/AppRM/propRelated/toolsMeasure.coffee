# accessModule = DEF '_access_module'
UserModel = MODEL 'User'
UserModel.accessModule 'AreaMeasure'
mapServer =  INCLUDE 'lib.mapServer'

APP '1.5'
APP 'tools',true

# '/1.5/tools/measure'
GET 'measure',(req,resp)->
  # return if req.redirectHTTPWhenCnDitu()
  isApp = req.getDevType() is 'app'
  unless isApp
    return resp.redirect "/adPage/needAPP"
  apikey = mapServer.getMapboxAPIKey {isApp}
  resp.ckup 'measure',{title:"Measure",apikey,loc:req.param('loc'),returnPath:req.param('d')}

VIEW 'measure',->
  div ngApp:'',->
    css 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css'
    script src: '/js/jquery-1.10.1.min.js'
    script src: '/js/jquery.mobile-1.3.1.min.js'
    script src: '/js/map/mapboxProjection.min.js'
    if @req.isChinaIP()
      input id:'china-ip',type:'hidden',name:'chinaIP',value:'1'
    # js @googleapiJs
    js 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js'
    js '/js/map/turf.min.js'
    className = 'bar bar-nav'
    unless @req.param 'inframe'
      className = 'bar bar-standard bar-header-secondary'
      header class: 'bar bar-nav', dataRole: 'header', ->
        a class: 'icon fa fa-back pull-left', href: (@returnPath or 'javascript:goBack();'), dataIgnore: 'push', rel: 'external', dataAjax: 'false'
        h1 id: 'id-header', class: 'title',-> _ 'Measure Tool'
    div class: className, ->
      button class: 'btn pull-left', title: 'Back', onclick: 'removeLast();', ->
        i class: 'fa fa-share fa-fw fa-flip-horizontal'
      button class: 'btn pull-right', title: 'Reset', onclick: 'reset();', ->
        i class: 'fa fa-undo fa-fw'
      h1 id: 'id_info_bar', class: 'title', style:'left: 0;',-> _ 'Touch Map to start'
    div class: 'bar bar-standard bar-footer', ->
      a class: 'btn pull-left', href: 'javascript:void(0);',id:'locateMe',->
        i class: 'fa fa-map-marker'
      div class: 'btn-group pull-left', role: 'group', ariaLabel: '...', style: 'height: 30px;', ->
        button id: 'id-left', class: 'btn btn-default mbutton', type: 'button', ->
          i class: 'fa fa-long-arrow-left fa-fw'
        button id: 'id-up', class: 'btn btn-default  mbutton', type: 'button', ->
          i class: 'fa fa-long-arrow-up fa-fw'
        button id: 'id-down', class: 'btn btn-default mbutton', type: 'button', ->
          i class: 'fa fa-long-arrow-down fa-fw'
        button id: 'id-right', class: 'btn btn-default mbutton', type: 'button', ->
          i class: 'fa fa-long-arrow-right fa-fw'
        button id: 'id-confirm', class: 'btn btn-default mbutton ', type: 'button', ->
          i class: 'fa fa-check fa-fw'
      a class: 'btn pull-right help-btn', href: '#helpModal',->
        i class: 'fa fa-question fa-fw'
    div id: 'measureview', class: 'content fullHeight', dataRole: 'page', ngmSharedController: 'ctrl:schCtrl', ->
      div class: 'container', ->
        div class: 'mapViewContainer',->
          div id:'street', class:'mapViewBtn',-> text _'Map'
          div id:'satellite', class:'mapViewBtn active',-> text _'Satellite'
        div id: 'id_map'
      div id: 'detail'
    div id: 'helpModal', class: 'modal modal-short', ->
      header class: 'bar bar-nav', ->
        a class: 'icon icon-close pull-right help-btn', href: '#helpModal'
        h1 class: 'title',-> _ 'Help'
      div id: 'help', class: 'content',style:'margin-left:10px', ->
        p class: 'content-padded', _ 'Click on up-right corner to restart.'
        p class: 'content-padded', _ 'Click on up-left corner to remove last point.'
        # p class: 'content-padded', 'Click on header bar title to change units.'
        if @loc
          span id:'loc',style:'display:none;',-> text @loc
  coffeejs {vars:{
    apikey:@apikey
    }
  }, ->
    null

STYLUS 'measure','''
.ui-content
  padding 0
  margin 0

div
  &.container
    position relative
  &.mbutton
    z-index 100
    position absolute
    cursor pointer
.mapViewContainer
  margin 10px
  z-index 1
  position absolute
  cursor pointer
  left 0
  top 0
.mapViewBtn
  direction ltr
  overflow hidden
  text-align center
  height 40px
  display table-cell
  vertical-align middle
  position relative
  color rgb(86, 86, 86)
  font-family Roboto, Arial, sans-serif
  user-select none
  font-size 18px
  background-color rgb(255, 255, 255)
  padding 0px 23px
  border-bottom-left-radius 2px
  border-top-left-radius 2px
  background-clip padding-box
  box-shadow rgba(0, 0, 0, 0.3) 0px 1px 4px -1px
  min-width 36px
.mapViewBtn.active
  color rgb(0,0,0)
  font-weight bold
  box-shadow rgba(0,0,0,0.3) 0px 1px 7px 2px
  font-size larger
div#id-left
  top 40%
  left 0px

div#id-right
  top 40%
  right 0px

div#id-up
  top 0px
  left 40%

div#id-down
  bottom 0px
  left 40%

div#id-confirm,
         div#id-help
  bottom 0px
  right 0px

#id-footer
  vertical-align middle
  line-height 31px

.bar-footer
  background-color white
  font-size 17px
  padding-left 3px
  a
    color #000

.btn-group
  margin 0
  .btn
    line-height 1.8
    top 3px
'''


###
1 click: latlng & elevation
2: length & last latlng
3+: length & area
###
COFFEE 'measure',->
  mapboxgl.accessToken = vars.apikey;
  debug = (e)-> console?.log e
  views =
    street:'mapbox://styles/mapbox/streets-v11'
    satellite:'mapbox://styles/mapbox/satellite-v9'
  gmap = null
  shape = null
  locs = []
  curP = -1
  _msg = null
  activeMarker = null
  markerEvent = null
  calc = null
  clearLayer = ->
    if gmap.getLayer('polyLayer')
        gmap.removeLayer('polyLayer')
    if gmap.getSource('polySource')
      gmap.removeSource('polySource')
  msg = (m)->
    if not _msg? then _msg = $('#id_info_bar').html()
    if not m then m = _msg
    $('#id_info_bar').html m
  reset = ->
    for m in locs
      # m.removeEventListener 'dragend',markerEvent
      m.getElement().removeEventListener 'click',markerEvent
      m.remove()
    # shape?.setMap null
    # shape = null
    clearLayer()
    locs = []
    curP = -1
    activeMarker -1
    msg()
  removeLast = ->
    if (m = locs?.pop())
      # google.maps.event.clearListeners m,'dragend'
      # google.maps.event.clearListeners m,'click'
      # m.setMap null
      m.getElement().removeEventListener 'click',markerEvent
      m.remove()
      if curP >= locs.length
        curP = locs.length - 1
        activeMarker locs.length - 1
      calc()
  do ->
    projection = null
    feet = false
    disM = 0
    areaM2 = 0
    unitsL = ['m','km','kkm','mkm']
    unitsLF = ['ft','mi','kmi']
    unitsA = ['m2','km2','mkm2']
    unitsAL = ['ft2','acre','hectare']
    round = (n,d = 1)-> Math.round(n * Math.pow(10,d)) / Math.pow(10,d)
    roundL = (l,d = 1)->
      i = 0
      if feet
        ft = l * 3.28084
        mi = l * 0.0006213712
        if mi < 1
          return round(ft,d) + unitsLF[i]
        else
          i = 1
          while mi >= 1000 and i < 2
            mi /= 1000
            i++
          return round(mi,d) + unitsLF[i]
      else
        while l >= 1000 and i < 3
          l /= 1000
          i++
        return round(l,d) + unitsL[i]
    roundA = (a,d = 1)->
      i = 0
      if feet
        ft2 = a * 10.76391
        acre = a * 0.0002471054
        km2 = a * 0.000001
        if acre < 1
          return round(ft2,d) + unitsAL[0]
        if km2 < 1
          return round(acre ,d) + unitsAL[1]
        return round(km2,d) + unitsAL[2]
      else
        while a >= 1000000 and i < 2
          a /= 1000000
          i++
        return round(a,d) + unitsA[i]
    initSlider = ->
      oldP = null
      moveP = (dx,dy)->
        debug "Slide Move"
        if curP >= 0
          m = locs[curP]
          # p = m.getPosition()
          p = m.getLngLat()
          pp = latlngToPoint gmap,p
          pp.x += dy
          pp.y += dx
          p = pointToLatlng gmap,pp
          pos = new mapboxgl.LngLat(p.lng,p.lat)
          m.setLngLat pos
      $('div button.mbutton').on 'vmousedown',(e)->
        e.stopPropagation()
        e.preventDefault()
        self = $(@)
        dx = dy = 0
        switch self.attr('id')
          when 'id-left'
            dx = -1
          when 'id-right'
            dx = 1
          when 'id-up'
            dy = -1
          when 'id-down'
            dy = 1
          when 'id-confirm'
            activeMarker -1
            calc()
          when 'id-help'
            $('#help').popup 'open'
        if dx isnt 0 or dy isnt 0
          moveP dx,dy
        false
    draw = (ps)->
      # shape?.setMap null
      if ps.length is 2
        # shape = new google.maps.Polyline {
        #   path: ps
        #   map: gmap}
        source = {
          'type': 'geojson',
          'data': {
            'type': 'Feature',
            'properties': {},
            'geometry': {
              'type': 'LineString',
              'coordinates': [
                [ps[0].lng,ps[0].lat],
                [ps[1].lng,ps[1].lat],
              ]
            }
          }
        }
        gmap.addSource('polySource',source)
        gmap.addLayer({
          'id': 'polyLayer',
          'type': 'line',
          'source': 'polySource',
          'layout': {
            'line-join': 'round',
            'line-cap': 'round'
          },
          'paint': {
            'line-color': '#FF0000',
            'line-width': 5
          }
        })
      else if ps.length > 2
        # shape = new google.maps.Polygon {
        #   paths: ps,
        #   strokeColor: "#FF0000"
        #   strokeOpacity: 0.8
        #   strokeWeight: 2
        #   fillColor: "#FF0000"
        #   fillOpacity: 0.1
        #   clickable: false
        #   map: gmap}
        coordinates = []
        for p in ps
          coordinates.push [p.lng,p.lat]
        coordinates.push [ps[0].lng,ps[0].lat]
        source = {
          'type': 'geojson',
          'data': {
            'type': 'Feature',
            'geometry': {
              'type': 'Polygon',
              'coordinates': [coordinates]
            }
          }
        }
        gmap.addSource('polySource',source)
        gmap.addLayer({
          'id': 'polyLayer',
          'type': 'fill',
          'source': 'polySource',
          'layout': {},
          'paint': {
            'fill-color': '#FF0000',
            'fill-opacity': 0.4
          }
        });
    calc = ->
      clearLayer()
      if locs.length <= 0 then return msg()
      if locs.length is 1
        # p = locs[0].getPosition()
        p = locs[0].getLngLat()
        return msg "#{round(p.lat,5)},#{round(p.lng,5)}"
      ps = (l.getLngLat() for l in locs)
      draw ps
      if ps.length is 2
        # disM = google.maps.geometry.spherical.computeDistanceBetween(ps[0],ps[1])
        line = turf.lineString([[ps[0].lng,ps[1].lat],[ps[1].lng,ps[0].lat]])
        disM = turf.length(line, {units: 'kilometers'}) * 1000
        disMV = roundL(disM,1)
        feet = true
        disFV = roundL(disM,1)
        feet = false
        return msg disMV + ' : ' + disFV
      # more than 3, only show area
      # disM = google.maps.geometry.spherical.computeLength ps
      # areaM2 = google.maps.geometry.spherical.computeArea ps
      psAreaArray = []
      for p in ps
        psAreaArray.push [p.lng,p.lat]
      psAreaArray.push [ps[0].lng,ps[0].lat]
      polygon = turf.polygon([psAreaArray])
      areaM2 = turf.area(polygon);
      areaM2V = roundA(areaM2,1)
      feet = true
      areaF2V = roundA(areaM2,1)
      feet = false
      msg  areaM2V + ' : ' + areaF2V #roundL(disM,1) + " : " +
    markerEvent = (idx)->
      activeMarker idx
      calc()
    activeMarker = (idx)->
      if (curP >= 0) and (m = locs[curP])
        # m.setIcon null
        m.getElement().setAttribute 'src','/img/mapmarkers/red_pin.png'
        m.getElement().setAttribute 'width','43px'
      if (idx >= 0) and (m = locs[idx])
        # image =
        #   url:'/img/mapmarkers/red_pin.png'
        #   size:new google.maps.Size(60,100)
        #   origin:new google.maps.Point(0,0)
        #   ancher:new google.maps.Point(30,100)
        #   scaledSize:new google.maps.Size(60,100)
        # m.setIcon image
        m.getElement().setAttribute 'src','/img/mapmarkers/red_pin.png'
        m.getElement().setAttribute 'width','76px'
        curP = idx
        $('div.mbutton').show()
        $('div#id-help').hide()
      else
        $('div.mbutton').hide()
        $('div#id-help').show()
    # $('#id-header').on 'click',->
      # feet = !feet
      # calc()
    # $('#id_info_bar').on 'click',->
      # feet = !feet
      # calc()
    $('#id-footer').on 'click',removeLast
    clicked = (e)->
      p = e.lngLat
      # m = new google.maps.Marker {
      #   position: p
      #   draggable: true
      #   optimized: false
      #   map: gmap
      #   }
      el = document.createElement 'img'
      el.setAttribute 'src','https://maps.gstatic.com/mapfiles/api-3/images/spotlight-poi2_hdpi.png'
      el.setAttribute 'width','76px'
      m = new mapboxgl.Marker({element:el,draggable: true,anchor:'bottom'}).setLngLat(p).addTo(gmap)
      idx = locs.length
      do (idx)->
        m.on 'dragend', (e) ->
          markerEvent idx
        el.addEventListener 'click', (e) ->
          markerEvent idx
          e.stopPropagation()
      locs.push m
      markerEvent idx
    loc = [-79.374097,43.618865]
    if locTxt = $('#loc').text()
      loc = (parseFloat v for v in locTxt.split(','))
    Umarker = null
    showUmarker = (loc1,icon = '/img/bluedot.png')->
      # Upos = new google.maps.LatLng loc1[0],loc1[1]
      Upos = new mapboxgl.LngLat loc1[1],loc1[0]
      if Umarker
        Umarker.getElement().setAttribute('src',icon);
        Umarker.setLngLat Upos
      else
        # Umarker = new google.maps.Marker {
        #               position: Upos
        #               icon: icon
        #               optimized: false
        #               map: gmap}
        el = document.createElement('img');
        el.setAttribute('src', icon);
        Umarker = new mapboxgl.Marker(el).setLngLat(Upos).addTo(gmap)
    initMap = ->
      opts =
        container:'id_map',
        style: views.satellite
        center: [loc[1],loc[0]]
        zoom: if locTxt then 19 else 18
        # mapTypeId: google.maps.MapTypeId.SATELLITE #HYBRID #
        #draggableCursor:'crosshair'
        doubleClickZoom:false
      # gmap = new google.maps.Map document.getElementById('id_map'),opts
      gmap = new mapboxgl.Map(opts);
      gmap.addControl(new mapboxgl.NavigationControl());
      # gmap.setTilt 0
      # google.maps.event.addListener gmap,'click',clicked
      gmap.on 'click', clicked
      $('div.mbutton').hide()
      $('div#id-help').show()
      if locTxt and loc
        showUmarker loc,'/img/reddot.png'
    locateMe = ->
      # if window.LocationServices
      #   locationService = window.LocationServices
      # else if RMSrv?.geolocation
      #   locationService = RMSrv.geolocation
      # else
      #   locationService = navigator.geolocation
      # locationService.getCurrentPosition (l)->
      #   loc[0] = l.coords.latitude
      #   loc[1] = l.coords.longitude
      #   gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
      #   showUmarker loc
      RMSrv.getGeoPosition (l)->
        if l.err
          return console.error l.err
        loc[0] = l.coords.latitude
        loc[1] = l.coords.longitude
        # gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
        gmap?.setCenter new mapboxgl.LngLat loc[1],loc[0]
        showUmarker loc
    $('#locateMe').on 'click',locateMe
    ua = navigator.userAgent
    iphone = ~ua.indexOf('iPhone') || ~ua.indexOf('iPod')
    ipad = ~ua.indexOf('iPad')
    ios = iphone or ipad
    android = ~ua.indexOf 'Android'
    fullHeight = (self)->
      self.width window.innerWidth
      portrait = window.orientation is 0 or window.orientation is 180
      tHeight = if portrait then screen.height else if ipad then screen.width else window.innerHeight
      hfHeight = 0
      self.find('[data-role="header"], [data-role="footer"]').each ->
        hfHeight += $(@).height()
      theHeight = tHeight - hfHeight # - (if ipad then 100 else if portrait then 64 else 0)
      theHeight -= 43*2; #2 top nav bars

      self.find('div.fullHeight').height theHeight
      mapdiv = self.find('#id_map')
      mapdiv.height theHeight - 20 #longer bottom block google
      mapdiv.width window.innerWidth
    cur_page = null
    $(document,window).on 'resize orientationchange webkitfullscreenchange mozfullscreenchange fullscreenchange', ->
      if cur_page
        cur_page.width window.innerWidth
        cur_page.trigger 'pageshow'
    $('#measureview').on 'pageshow',(e)->
      cur_page = $(@)
      fullHeight cur_page
      if gmap?
        google.maps.event.trigger gmap,'resize'
    $('.help-btn').click (e)->
      #alert 'click'
      #have to do that cause we modified ratchet.js
      e.preventDefault()
    $ ->
      unless locTxt and loc
        locateMe()
      initMap()
      initSlider()
      $('.mapViewBtn').on 'click',->
        view = $(this).attr('id')
        $('.mapViewBtn').removeClass 'active'
        $(this).addClass 'active'
        gmap.setStyle views[view]
  null
