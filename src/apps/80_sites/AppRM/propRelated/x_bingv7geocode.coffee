
GeoCache    = COLLECTION 'vow','geo_cache'
geoCacheOrig =  COLLECTION 'vow','geoCacheOrig'
{formatLng} = INCLUDE 'lib.helpers_string'
GeoCoder = MODEL 'GeoCoder'
propAddress = INCLUDE 'lib.propAddress'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
helpers_number =  INCLUDE 'lib.helpers_number'
APP '1.5'
APP 'geocoding',true
{respError} = INCLUDE 'libapp.responseHelper'


# get lat lng from address got from google autocomplete(google does not provide lat,lng so cant locate on map, have to call geocoder either by google or by us).
# NOTE: req.body = {addr:val.addr+', '+val.city}  "addr": "53 Samor Road", "city": "North York, ON, Canada"
# isapp 才能使用。

# /1.5/geocoding
POST (req,resp) ->
  if not (req.getDevType() is 'app')
    return respError {clientMsg:MSG_STRINGS.ACCESS_DENIED,resp}
  addr = req.param('addr') or req.body.addr
  #NOTE: api support reverse geocoding，but not used now，remove for safty
  lat = req.param 'lat'
  lng = req.param 'lng'
  if not addr and (not(lat and lng))
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
    
  
  params = {engineType: 'publicApi'} # geocoding 使用mapbox的。

  if addr
    addrArray = addr.split(',')
    if addrArray.length < 4
      return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}
    params.cnty = addrArray[addrArray.length-1]
    params.prov = addrArray[addrArray.length-2]
    params.city = addrArray[addrArray.length-3]
    addrArray.splice(addrArray.length-3)
    params.addr = addrArray.join(', ')
    GeoCoder.geocoding params,(err,result)->
      dealWithResult({err,result,req,resp})
  else if (lat and lng)
    params.lat = lat
    params.lng = formatLng lng
    GeoCoder.reverseGeocoding params,(err,result)->
      dealWithResult({err,result,req,resp})

dealWithResult=({err,result,req,resp})->
  if err
    return respError {
      clientMsg:MSG_STRINGS.GEO_CODING_ERROR,
      resp,
      sysErr:err
    }
  return respError {clientMsg:MSG_STRINGS.NOT_FOUND,resp} unless result
  return resp.send {ok:1, result: result}

#1.5/geocoding/getGeocodingByEngine
POST 'getGeocodingByEngine',(req,resp)->
  unless req.isAllowed('admin')
    return resp.send {ok:0, err:MSG_STRINGS.ACCESS_DENIED}
  param = {ts: new Date()}
  allowedflds = [
    'addr',
    'city',
    'prov',
    'cnty',
    'type',
    'lat',
    'lng',
    'geoq',
    'upsert',
    'ptype',
    'ptype2',
    'st',
    'st_num',
    'ts',
    'status',
    'lst',
    'zip'
  ]
  for fld in allowedflds
    param[fld] = req.body[fld] if req.body?[fld]

  if not param.addr and (not(param.lat and param.lng))
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp}

  param.useCache = req.body.useCache  if req.body?.useCache?

  if param.addr
    for fld in ['city','cnty','prov','addr']
      if (typeof param[fld] is 'number') or helpers_number.isNumber param[fld]
        return resp.send {ok:0, e:"#{fld} Expect String"}
    
    propAddress.formatProvAndCity param

    param.engineType = param.type or ''
    # unless addr
    #   return resp.send {ok:0, err:MSG_STRINGS.BAD_PARAMETER}
    GeoCoder.geocoding param,(err,result)->
      dealWithResult({err,result,req,resp})
  else
    param.engineType = 'publicApi'
    param.useCache = false
    param.saveResult = false
    param.lng = formatLng param.lng
    debug.info param
    GeoCoder.reverseGeocoding param,(err,result)->
      dealWithResult({err,result,req,resp})
