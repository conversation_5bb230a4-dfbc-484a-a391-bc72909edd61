UserModel = MODEL 'User'

APP '1.5'
APP 'map',true

VIEW "appMapMixView",->
  div id:'vueBody',->
    text """<app-map-mix-view></app-map-mix-view>"""
  js '/js/entry/commons.js'
  js '/js/entry/appMapMixView.js'

GET 'mixview',(req, resp)->
  # return if req.redirectHTTPWhenCnDitu()
  UserModel.appAuth {req,resp},(user)->
    # resp.ckup 'getvip', {}, '_', {noAngular:1, noRatchetJs:1, noCordova:1}
    resp.ckup 'appMapMixView',{},'_',{noAngular:1}
