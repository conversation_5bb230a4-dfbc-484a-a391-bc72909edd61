# useage:http://app.test:8080/1.5/direction?fromlat=43&fromlng=-79
# conf = CONFIG()
Places = COLLECTION 'vow','places'
SchoolModel = MODEL 'School'

UserModel = MODEL 'User'
mapServer =  INCLUDE 'lib.mapServer'
{respError} = INCLUDE 'libapp.responseHelper'

APP '1.5'
APP 'direction', true
GET 'places', (req,resp) ->
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"

  req.setLocale lang if lang = req.param 'lang'
  resp.ckup "places",{},'_',{noAngular:true}

POST 'places', (req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  query = {prov: params.prov}
  # Places.findToArray query, {fields: {name:1, campusName:1, prov:1, city: 1,addr: 1}}, (err, places) ->
  try
    schoolList = await SchoolModel.getSchools {curSchoolType:'university',prov:params.prov}
  catch err
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
  places = []
  for school in schoolList
    places = places.concat school.campus if school?.campus?.length
  return resp.send {ok:1, places: places}

GET (req,resp)->
  # return if req.redirectHTTPWhenCnDitu()
  unless req.getDevType() is 'app'
    return resp.redirect '/adPage/needAPP'
  googleapiJs = mapServer.getGoogleAPILink({
    tp:'googleapi',
    lib:'places'
  })
  req.setLocale lang if lang = req.param 'lang'
  resp.ckup 'direction',{googleapiJs:googleapiJs},'_',{noAngular:true}

POST 'savedaddr', (req, resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0, url:'/1.5/user/login#index'} unless user
    resp.noCache()
    UserModel.findProfileById user._id,{projection:{savedAddr:1}}, (err, profile)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp} if err
      savedAddr = profile?.savedAddr || []
      return resp.send {ok:1, savedAddr: savedAddr}

POST 'managePlace', (req, resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0, url:'/1.5/user/login#index'} unless user
    resp.noCache()

    unless (params = req.body) and ('string' is typeof params.addr) and ((params.action is 'save') or (params.action is 'delete'))
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}

    UserModel.pullSavedAddress user._id, {params},(err)-> # no need upsert when pull
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp} if err
      if params.action is 'save'
        UserModel.addSavedAddress user._id, {params},(err)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp} if err
          return resp.send {ok:1}
      else
        return resp.send {ok:1}

VIEW "direction",->
  js @googleapiJs
  div id:'vueBody',->
    text """<direction-search></direction-search>"""
  js '/js/entry/commons.js'
  js '/js/entry/directionSearch.js'

#没使用mapbox的direction,mapbox没有客户端的widget，需要自己画界面。
VIEW "direction-mapbox",->
  js @googleapiJs
  div id:'vueBody',->
    text """<direction-search></direction-search>"""
  js '/js/entry/commons.js'
  js 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js'
  css 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css'
  css 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.4.2/mapbox-gl-geocoder.css'
  js 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.4.2/mapbox-gl-geocoder.min.js'
  js '/js/map/mapbox.min.js'
  js '/js/map/mapboxTransit.min.js'
  js '/js/entry/directionSearch.js'
  css '/css/mapbox.css'

VIEW "places",->
  div id:'vueBody',->
    text """<place-select-modal></place-select-modal>"""
  js '/js/entry/commons.js'
  js '/js/entry/placeSelect.js'
