UserModel = MODEL 'User'
CoopModel = MODEL 'CoopHouse'
# Coop = COLLECTION 'vow','coophouses'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
APP 'coophouse'

#/1.5/coophouse?id=
# GET (req,resp)->
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
#     id = req.param 'id'
#     Coop.findOne {_id:id},(err,coop)->
#       return resp.ckup 'generalError',{err:err} if err
#       if not coop
#         return resp.ckup 'generalMessage', {nobar:1, msg:req.l10n 'Not found'}
#       resp.ckup 'coophouse/detail',{coop}

GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    id = req.param 'id'
    CoopModel.findById {_id:id},(err,coop)->
      return resp.ckup 'generalError',{err:err} if err
      if not coop
        return resp.ckup 'generalMessage', {nobar:1, msg:req.l10n 'Not found'}
      return resp.ckup 'coophouse/detail',{coop}


getShortCoopTitle = (title)->
  title.replace(/homes|housing|co\-operative/ig,'')


ADDSTDFN 'getCoop',{needLogin:true},(options,cb)->
  findError = (err)->
    debug.error err
    e = if 'string' is typeof err then err else 'Error'
    return {e:e,cnt:0,items:[]}
  # unless user = options.user
  #   return findError 'Login to see coophouses'
  q = options
  southWest = q.sw
  northEast = q.ne
  return cb null,{cnt:0, items:[]} if not (southWest or northEast)
  return cb null,{cnt:0, items:[]} if (not Array.isArray(southWest)) or southWest.length isnt 2
  return cb null,{cnt:0, items:[]} if (not Array.isArray(northEast)) or northEast.length isnt 2
  query = {
    page: q.p or 0,
    southWest,
    northEast
  }
  CoopModel.findByGeoCodeAndCount query, (err,coops)->
    if err
      debug.error 'getCoop', err
      return findError 'System Error. Please try again.'
    {counts, result} = coops
    result ?= []
    for c in result
      c.sTitle = getShortCoopTitle(c.title)
      c.displayContents1 = c.primary_sponsor or c.provider_name or ''
      c.type = c.type.toString() if Array.isArray(c.type)
      c.displayContents2 = c.type.trim() if c.type
      c.displayContents3 = c.phone.trim() if c.phone
      c.url = c.source_link or c.source or ''
    return cb null, {cnt:counts,items:result}
