
TransitStop = COLLECTION  "vow",'transit_stop'
TransitRoute = COLLECTION  "vow",'transit_route'
mapServer = INCLUDE 'lib.mapServer'
helpersStr = INCLUDE 'lib.helpers_string'
getGoogleAPILink = mapServer.getGoogleAPILink
UserModel = MODEL 'User'
{respError} = INCLUDE 'libapp.responseHelper'

TransitRoute.createIndex {'geometry':'2dsphere'}
TransitRoute.createIndex {'shapes.dup':1}

TransitStop.createIndex {'prov':1,'city':1, 'tp':1,'route_id':1}
TransitStop.createIndex {'geometry':'2dsphere', 'ids':1}

APP '1.5'
APP 'transit',true


GET '', (req,resp)->
  return resp.ckup 'transit-home',{},'_',{noAngular:true, noref: true}

POST 'stop/all', (req,resp)->
  params = req.body
  page = parseInt params.page or 1
  limit = 20
  query ={}
  for k in ['city','prov','type']
    query[k] = params[k] if params[k]
  for k in ['stop_id','nm']
    if params[k]
      regStr = helpersStr.str2reg(params[k],true)
      query[k] = regStr
  TransitStop.findToArray query, {skip: (page-1) * limit, limit: limit + 1}, (err, ret) ->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    resp.send {ok:1, stopList:ret}

GET 'stop', (req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    unless accessAllowed 'manageTransit',user
      return resp.redirect '/1.5/index'
    # return if req.redirectHTTPWhenCnDitu()
    unless req.getDevType() is 'app'
      return resp.redirect "/adPage/needAPP"
    googleapiJs = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),lib:'geometry,places',src:'drctn'})

    req.setLocale lang if lang = req.param 'lang'
    resp.ckup 'transit-stop-edit',{googleapiJs:googleapiJs},'_',{noAngular:true}

POST 'stop/detail', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body._id
  query = {_id:id}
  TransitStop.findOne query, (err, ret) ->
    resp.send {ok:1, stop:stop}

delStop = (id, req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
  TransitStop.deleteOne {_id: id}, (err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n('Delete stop failed'), resp} unless ret.result.n
    return resp.send {ok:1}

updateStop = (set, req, resp)->
  updateValsList  = [
    '_id',
    'city',
    'prov',
    'stop_id',
    'type',
    'lat',
    'lng',
    'loc_type',
    'nm',
    'url',
    'wc',
    'zone',
    'entrances'
  ]
  stop = {}
  for i in updateValsList
    stop[i] = v if (v = set[i])?
  stop.mt = new Date()
  TransitStop.updateOne {_id: set._id}, update, {upsert:true}, (err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1}

POST 'stop/manage', (req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless accessAllowed 'manageTransit',user
    set = req.body
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless set
    if type is 'edit'
      updateStop set, req, resp
    else if type is 'del'
      delStop set._id,req, resp

GET 'route', (req,resp)->
  googleapiJs = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),lib:'geometry,places',src:'drctn'})

  return resp.ckup 'transit-route',{googleapiJs:googleapiJs},'_',{noAngular:true, noref: true}

GET 'route/one', (req,resp)->
  # return if req.redirectHTTPWhenCnDitu()
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  googleapiJs = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),lib:'geometry,places',src:'drctn'})

  req.setLocale lang if lang = req.param 'lang'
  resp.ckup 'transit-route-edit',{googleapiJs:googleapiJs},'_',{noAngular:true}

GET 'route/shape', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless (id = req.param 'id') and (shapeid = req.param 'shapeid')
  TransitRoute.findOne {_id:id}, (err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless ret?.shapes
    geoJson = {}
    for shape in ret.shapes
      if shape.id == shapeid
        geoJson = shape
    resp.setHeader 'Content-Type','application/json'
    return resp.send geoJson

# add api check if there is available route within the bound. (return gobus, go train,ttc, up)
POST 'route/availables',(req, resp) ->
  query = {}
  opt = req.body
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless opt.bbox
  swLng = helpersStr.formatLng opt.bbox[0]
  swLat = opt.bbox[1]
  neLng = helpersStr.formatLng opt.bbox[2]
  neLat = opt.bbox[3]
  geoIntersects = {
    $geoIntersects: {
      $geometry: {
         type: "Polygon" ,
         coordinates: [[[ swLng,  swLat],[ swLng,  neLat],[neLng,neLat],[ neLng,  swLat],[ swLng,  swLat]]]
      }
   }
  }
  query['shapes.geometry']= geoIntersects
  aggregate = [
    {$match: query},
    {$group:{_id:{tp:'$tp',route_tp:'$route_tp'},total : { $sum : 1 }}}
  ]
  TransitRoute.aggregate aggregate, (err,routes)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1,routes:routes}

POST 'route/all', (req,resp)->
  query = {}
  opt = req.body
  query.city = opt.city if opt.city
  query.prov = opt.prov if opt.prov
  query.route_tp = {$in:opt.routeTp} if opt.routeTp
  query.tp = opt.tp if opt.tp
  query.fnm = opt.fnm if opt.fnm

  if opt.bbox
    swLng = helpersStr.formatLng opt.bbox[0]
    swLat = opt.bbox[1]
    neLng =helpersStr.formatLng  opt.bbox[2]
    neLat = opt.bbox[3]
    geoWithin = {
      $box: [
        [ swLng,  swLat],
        [neLng,neLat]
      ]
    }

    geoIntersects = {
      $geoIntersects: {
        $geometry: {
           type: "Polygon" ,
           coordinates: [[[ swLng,  swLat],[ swLng,  neLat],[neLng,neLat],[ neLng,  swLat],[ swLng,  swLat]]]
        }
     }
    }
  if opt.routeTp.indexOf('Bus') >=0 or opt.routeTp.indexOf('Tram') >=0
    query['shapes.geometry']= geoIntersects if geoIntersects

  query = {_id: new TransitRoute.ObjectId(opt._id) } if opt._id
  # console.log JSON.stringify query
  aggregate = [
    {$match: query},
    {$project: {
        shapes: {$filter: {
            input: '$shapes',
            as: 'shape',
            cond: {$ne: ['$$shape.dup', true]}
        }},
        fnm:1,
        color:1
    }},
    { $sort:{fnm:1}},
  ]

  TransitRoute.aggregate aggregate, (err,routes)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    routeIds = []
    # console.log routes
    for route in routes
      routeIds.push new TransitRoute.ObjectId(route._id)
      if route.shapes
        for shape in route.shapes
          shape.properties = {color:route.color}
      if route.shapes.length>1
        console.log route.fnm
    stops = []
    return resp.send {ok:1,routes:routes, stops:stops} unless routeIds.length
    query = {'routes._id':{$in:routeIds}}
    if opt.routeTp.indexOf('Bus') >=0 or opt.routeTp.indexOf('Tram') >=0
      query['geometry']={$geoWithin:geoWithin} if geoWithin
    TransitStop.findToArray query, (err,stops)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1,routes:routes, stops:stops}

POST 'route/one', (req,resp)->
  return respError req.l10n(MSG_STRINGS.BAD_PARAMETER), resp unless id = req.param 'id'
  query = {_id:id}
  TransitRoute.findOne query, (err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless ret
    return resp.send {ok:1,ret:ret}

VIEW "transit-home",->
  div id: "vueBody",style:'height: 100%;overflow: hidden;',->
    text """<app-transit-home></app-transit-home>"""
  js '/js/entry/commons.js'
  js '/js/entry/appTransitHome.js'


VIEW "transit-stop-edit",->
  js @googleapiJs
  div id: "vueBody",style:'height: 100%;overflow: hidden;',->
    text """<app-transit-stop></app-transit-stop>"""
  js '/js/entry/commons.js'
  js '/js/entry/appTransitStop.js'

VIEW "transit-route",->
  js @googleapiJs

  div id: "vueBody",style:'height: 100%;overflow: hidden;',->
    text """<app-transit-route></app-transit-route>"""
  js '/js/entry/commons.js'
  js '/js/entry/appTransitRoute.js'

VIEW "transit-route-edit",->
  js @googleapiJs
  div id: "vueBody",style:'height: 100%;overflow: hidden;',->
    text """<app-transit-route-edit></app-transit-route-edit>"""
  js '/js/entry/commons.js'
  js '/js/entry/appTransitRouteEdit.js'
