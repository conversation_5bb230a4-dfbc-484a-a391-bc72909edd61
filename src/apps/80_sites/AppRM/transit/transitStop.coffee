TransitStop = COLLECTION  'vow','transit_stop'
TransitRoute = COLLECTION  'vow','transit_route'
TransitRouteShapeCol = COLLECTION 'vow', 'transit_route_shape'
APP 'stop'
GET '',(req,resp)->
  return resp.ckup 'generalError',{err:'No ID specified'} unless id = req.param 'id'
  TransitStop.findOne {_id:id},(err, stop)->
    return resp.ckup 'generalError',{err:'Stop not found'} if err
    routeIds = (route._id for route in stop.routes)
    TransitRouteShapeCol.findToArray {_id:{$in:routeIds}},(err,routes)->
      resp.ckup 'transit/stopDetail',{stop,routes}