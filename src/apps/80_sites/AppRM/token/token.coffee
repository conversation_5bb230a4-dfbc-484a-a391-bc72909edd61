debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'
{fullNameOrNickname} = INCLUDE 'libapp.user'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
{encodeObj2JsonString} = INCLUDE 'lib.helpers_object'
{isMlsPrefix} = INCLUDE 'libapp.properties'
objectCache = INCLUDE 'libapp.objectCache'
{dateFormat} = INCLUDE 'lib.helpers_date'
GroupModel = MODEL 'Group'
UserModel = MODEL 'User'
TokenModel = MODEL 'Token'
SysDataModel = MODEL 'SysData'

SystemKey = 'systemTokenKeys'
UserProjection = {nm:1,nm_zh:1,nm_en:1,eml:1,avt:1,_id:1}
TOKEN_TYPES = [{type: 'Token'},{type: 'Credit Report',canBeNegative: true}]
TK_CREDIT_REPORT = 'tkCreditReport'
TK_Token = 'tkToken'
CREDIT_REPORT = 'Credit Report'
DEFAULT = 'Default'

APP 'token'

# token/list
GET 'list', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    if req.isAllowed 'tokenAdmin'
      return resp.ckup 'token/tokenList', {TOKEN_TYPES:encodeObj2JsonString(TOKEN_TYPES)}
    return resp.redirect '/1.5/index'

getUserInfoByUid = (uid,lang) ->
  userOrig = await UserModel.findByIdAsync(uid, {projection: UserProjection})
  userInfo = dealUserInfo(userOrig, lang)

# token/edit
GET 'edit', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    uid = req.param('uid')
    unless req.isAllowed 'tokenAdmin'
      return resp.redirect '/1.5/index'
    unless uid
      return resp.redirect '/token/list'
    try
      userInfo = await getUserInfoByUid(uid, req.locale())
    catch err
      debug.error err
      return resp.redirect '/token/list'
    return resp.ckup 'token/tokenEdit', {userInfo:encodeObj2JsonString(userInfo),TOKEN_TYPES:encodeObj2JsonString(TOKEN_TYPES)}

# token/history
GET 'history', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    type = req.param('type')
    uid = req.param('uid')
    if type is 'admin'
      try
        userInfo = await getUserInfoByUid(uid, req.locale())
      catch err
        debug.error err
        return resp.redirect '/1.5/index'
    else
      userInfo = dealUserInfo(user, req.locale())
    return resp.ckup 'token/tokenHistory', {userInfo:encodeObj2JsonString(userInfo),TOKEN_TYPES:encodeObj2JsonString(TOKEN_TYPES)}

# token/systemKey
GET 'systemKey', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    if req.isAllowed 'tokenAdmin'
      return resp.ckup 'token/tokenSystemKey', {TOKEN_TYPES:encodeObj2JsonString(TOKEN_TYPES)}
    return resp.redirect '/1.5/index'

# token/exchange
GET 'exchangeTemplate', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    key = req.param('key')
    id = req.param('id')
    name = req.param('name')
    memo = req.param('memo')
    ctx =
      key: key
      id: id
      memo: memo
      name: name
    try
      isExchanged = await TokenModel.isExchanged {uid:user._id, key, id}
    catch err #ignore this error for inc vc
      debug.error err
    unless isExchanged.result
      ctx.balance = isExchanged.balance
      ctx.token = isExchanged.token
    return resp.ckup 'token/exchange',ctx,'_',{}

# token/getHistoryDetail 有token记录的用户和管理员可以看到具体Token加减历史记录。
POST 'getHistoryDetail',(req,resp)->
  uids = req.body?.uids
  page = req.body?.page
  date = req.body?.date
  type = req.body?.type
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      {tokenList, limit} = await TokenModel.getHistory({uids,type,date,page})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    result = { history: [], limit }
    unless tokenList.length
      date = date or dateFormat(new Date(), 'YYYY/MM')
      result.history.push({date: date,his:[], sumIn: '-', sumOut: '-'})
      return resp.send {ok:1, result}
    dateRange = []
    dateRange.push(dateFormat(tokenList[tokenList.length - 1].ts, 'YYYY/MM'), dateFormat(tokenList[0].ts, 'YYYY/MM'))
    try
      tokenSumInfo = await TokenModel.getSumTokenByMonth({dateRange,type,uids})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    {uidsList, history} = dealHistoryDetailFrontEnd(tokenList, tokenSumInfo)
    result = { history, limit }
    unless req.isAllowed 'tokenAdmin'
      return resp.send {ok:1, result}
    try
      usersInfo = await UserModel.getListByIds(uidsList, {projection: UserProjection})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    for item in history
      for info in item.his
        index = usersInfo.findIndex((el) -> return el._id.toString() is info.uid.toString())
        if index >= 0
          userName = dealUserInfo(usersInfo[index], req.locale())
          info = Object.assign(info, userName)
    return resp.send {ok:1, result}

# token/getUserList，管理员权限可用查询用户列表。
POST 'getUserList',(req,resp)->
  eml = req.body?.eml
  page = req.body?.page
  type = req.body?.type
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"getUserList:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      {userList, limit, userInfo} = await TokenModel.getUserList(eml,type,page)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    result = dealUserListFrontEnd(userList, userInfo, req.locale())
    return resp.send {ok:1, result:{userList: result, limit } }

# token/adminModify 管理员修改用户Token记录,t: 加减的数据，model里会计算最终数据
POST 'adminModify',(req,resp)->
  uid = req.body?.uid
  reason = req.body?.reason
  token = req.body?.token
  type = req.body?.type
  unless uid and reason and token
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  regex = /^(\-)?\d+(\.\d{1,2})?$/
  unless regex.test(token)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"adminModify:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      {err,token} = await TokenModel.adminModify {uid, reason, token, uidAdmin: user._id, type}
    catch error
      debug.error error
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    if err
      return resp.send {ok:0, e: req.l10n(err)}
    return resp.send {ok:1, result:{token}}

# token/getKeyList 查询系统录入的token项目，并分组返回。tokenAdmin 权限
POST 'getKeyList',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"getKeyList:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      stystemTokens = await SysDataModel.findById(SystemKey)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (stystemTokens?.item and Array.isArray(stystemTokens.item))
      return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
    resultGroup = {}
    for item in TOKEN_TYPES
      resultGroup[item.type] = []
    systemTokenKeys = {}
    stystemTokens.item.forEach (token) ->
      systemTokenKeys[token.key] = Object.assign({},token)
      token.isEdit = false
      token.t = (token.t / 100).toFixed(2)
      if token.tp
        resultGroup[token.tp].push(token)
      else
        resultGroup.Token.push(token)
    objectCache.setObjectCacheList {type:'systemTokenKeys',value:systemTokenKeys}
    for k,v of resultGroup
      v.sort (a, b) ->
        return a.key.localeCompare(b.key)
    return resp.send {ok:1, result:resultGroup}

# token/updateSystemKey 更新系统token规定的分数，名字，tokenAdmin 权限
POST 'updateSystemKey', (req, resp) ->
  key = req.body?.key
  token = req.body?.token
  name = req.body?.name
  unless key and token and name
    return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
  regex = /^(\-)?\d+(\.\d{1,2})?$/
  unless regex.test(token)
    return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
  parseIntT = Number((token * 100).toFixed(0)) # 2.55 * 100 = 2.549999999
  if Number.isNaN(parseIntT)
    return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"updateSystemKey:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      systemTokenOrig = await SysDataModel.findById(SystemKey)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless systemTokenOrig
      return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
    tokenItem = systemTokenOrig.item
    index = tokenItem.findIndex((el)-> return el.key is key)
    if index < 0
      return respError { clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp }
    tokenItem[index].t = parseIntT
    tokenItem[index].nm = name
    try
      result = await SysDataModel.updateById(SystemKey,{item: tokenItem})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys',tokenItem[index].key
    systemTokenCache.t = parseIntT
    systemTokenCache.nm = name
    objectCache.setObjectCacheList {type:'systemTokenKeys',key: systemTokenCache,value:systemTokenCache}
    return resp.send {ok:1}

# token/getTokenBalance 判断当前用户是否有tokne记录，并获取当前用户token余额
POST 'getTokenBalance', (req, resp) ->
  uids = req.body?.uids
  isHistory = req.body?.isHistory
  UserModel.appAuth {req,resp}, (user)->
    result = {hasRecord: false, isShow: false}
    unless user
      return resp.send {ok:1, result}
    unless isHistory
      uids = [user._id]
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or req.isAllowed 'tokenAdmin')
      # debug.error {msg:"getTokenBalance:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    result.isShow = true
    try
      record = await TokenModel.getDiffTypeTokenBalance(uids)
    catch err
      debug.error err
    unless record?.length
      return resp.send {ok:1, result}
    recordObj = {}
    for item in record
      if item[TK_Token]?
        recordObj = sumTokenByTokenType({recordObj, key: DEFAULT, tp:'', token: item[TK_Token]})
      if item[TK_CREDIT_REPORT]?
        recordObj = sumTokenByTokenType({recordObj, key: CREDIT_REPORT, tp:CREDIT_REPORT, token: item[TK_CREDIT_REPORT]})
    recordArr = Object.values(recordObj)
    for item in recordArr
      item.token = (item.token / 100).toFixed(2)
    if recordArr.length
      result = {hasRecord: true, tokens:recordArr, isShow: true}
    return resp.send {ok:1, result}

# token/getInrealMember 获取Inreal Group成员信息,token管理员权限
POST 'getInrealMember', (req, resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"getInrealGroupMember:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    result = []
    try
      membersDB = await GroupModel.findGroupsByName({nm:[':inReal'],flds:{members:1}})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless membersDB[0]?.members?.length
      return resp.send {ok:1, result}
    uids = []
    for memb in membersDB[0].members
      uids.push(memb.uid)
    try
      userInfo = await UserModel.getListByIds(uids, {projection: UserProjection})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    result = dealMemberInfoFrontEnd(userInfo, req.locale())
    return resp.send {ok:1, result}

# token/getSummaryDetail 获取token按月份统计信息
POST 'getSummaryDetail',(req,resp)->
  uids = req.body?.uids
  date = req.body?.date
  type = req.body?.type
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless date
      date = dateFormat(new Date(), 'YYYY/MM')
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or req.isAllowed 'tokenAdmin')
      debug.error {msg:"getSummaryDetail:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      history = await TokenModel.getSummaryTokenByKeyAndUid({uids,type,date})
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    result = { history: [] }
    unless history.length
      return resp.send {ok:1, result}
    historyFormat = dealSummaryDetailFrontEnd(history, date, req.locale())
    if ((uids?.length isnt 1) and (req.isAllowed 'tokenAdmin'))
      try
        summaryAllInfo = await TokenModel.getSummaryTokenByKey({uids,type,date})
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp}
      hisArry = []
      for item in summaryAllInfo
        hisTemp = dealSumInSumOutInfo(item)
        hisArry = hisArry.concat(hisTemp)
      historyFormat.unshift({his:hisArry})
    return resp.send {ok:1, result:{history:historyFormat}}

# token/getReasonTags 查询管理员填写的原因
POST 'getReasonTags',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'tokenAdmin'
      debug.error {msg:"getKeyList:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      reason = await TokenModel.getEditReason()
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    result = []
    if reason and Array.isArray(reason)
      reason.forEach((el) -> result.push({isChoose: false, reason: el}))
    return resp.send {ok:1, result: result}

# token/applyToken 判断是否可以兑换，之后根据系统指定key来加减用户积分。
POST 'applyToken',(req,resp)->
  body = req?.body
  key = body?.key
  unless key
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  validKey = objectCache.getObjectCacheList 'systemTokenKeys', key
  unless validKey
   return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      isGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    unless (isGroup or req.isAllowed 'tokenAdmin')
      debug.error {msg:"applyToken:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    param = Object.assign({uid: user._id}, body)
    try
      result = await TokenModel.canPerform(user._id, key)
      unless result
        return respError {clientMsg:req.l10n('Insufficient balance'), resp}
      {err} = await TokenModel.applyToken(param)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    if err
      return respError {clientMsg:req.l10n(err), resp}
    return resp.send {ok:1}

# 处理user信息，获取需要的部分
dealUserInfo = (user,lang) ->
  unless user
    return {}
  userInfo = {}
  userInfo.fnm = fullNameOrNickname lang, user
  userInfo.eml = UserModel.getEmail(user) if user.eml
  userInfo.avt = replaceRM2REImagePath(user.avt) if user.avt
  userInfo.uid = user._id or ''
  return userInfo

dealMemberInfoFrontEnd = (userList,lang) ->
  frontEndReault = []
  unless userList?.length
    return frontEndReault
  for user in userList
    userInfo = dealUserInfo(user, lang)
    frontEndReault.push(Object.assign({}, userInfo, {isSel: false}))
  frontEndReault.sort (a, b) -> return a.fnm.localeCompare(b.fnm)
  return frontEndReault

###
# @description 将从数据库获取的用户信息处理成前端需要的格式
# @param {array} userList - 从token表获取到的用户最新积分情况
# @param {array} userInfoOrgi - 从user表查询的用户基本信息
# @return {array} frontEndReault - 前端需要的格式，包含用户邮箱，姓名和token情况等信息
###
dealUserListFrontEnd = (userList, userInfoOrgi, lang) ->
  if userInfoOrgi.length
    for item in userInfoOrgi
      index = userList.findIndex((el)-> return el?._id?.toString() is item._id.toString())
      if index > -1
        userList[index] = Object.assign(userList[index], item)
      else
        item.token = '-'
        userList.push(item)
  frontEndReault = []
  for item in userList
    obj = {
      uid: item._id,
      token: item.token or '-',
    }
    if (item.token? and (item.token isnt '-'))
      obj.token = (item.token / 100).toFixed(2)
    userInfo = dealUserInfo(item, lang)
    frontEndReault.push(Object.assign({}, obj, userInfo))
  return frontEndReault

addTokenPrefixSymbolAndDecimal = (token) ->
  token2Dec = (token / 100).toFixed(2)
  if token <= 0
    return token2Dec
  if token > 0
    return "+#{token2Dec}"
  return '-'

dealSumInSumOutInfo = (info) ->
  hisTemp = []
  reason = 'Admin Edit'
  systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys',info._id.key
  if info._id.key
    reason = systemTokenCache?.nm
  if info.sumIn
    sumInInfo = Object.assign({}, {t:addTokenPrefixSymbolAndDecimal(info.sumIn), reason, count:info.countIn})
    hisTemp.push(sumInInfo)
  if info.sumOut
    sumOutInfo = Object.assign({}, {t:addTokenPrefixSymbolAndDecimal(info.sumOut),reason, count:info.countOut})
    hisTemp.push(sumOutInfo)
  return hisTemp

# 将从数据库获取的统计信息处理成前端需要的格式
dealSummaryDetailFrontEnd = (history, date, lang) ->
  aggregateUid = {}
  for his in history
    user = Object.assign({}, his, {_id: his._id.uid})
    userInfo = dealUserInfo(user, lang)
    hisTemp = dealSumInSumOutInfo(his)
    if aggregateUid[his._id.uid.toString()]
      aggregateUid[his._id.uid.toString()].his = aggregateUid[his._id.uid.toString()].his.concat(hisTemp)
    else
      aggregateUid[his._id.uid.toString()] = Object.assign({}, userInfo, {his:hisTemp})
  return Object.values(aggregateUid)

# 将从数据库获取的历史详情处理成前端需要的格式
dealHistoryDetailFrontEnd = (tokenList, tokenSumInfo) ->
  historyObj = {}
  tokenSumInfoObj = {}
  userObj = {}
  for sum in tokenSumInfo
    tokenSumInfoObj[sum._id] = sum
  systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys'
  tokenList.forEach((list) ->
    userObj[list.uid] = 1
    date = dateFormat(list.ts, 'YYYY/MM')
    list.formatDate = dateFormat(list.ts, 'MM/DD HH24:MI')
    list.t = addTokenPrefixSymbolAndDecimal(list.t)
    if list.m?.length and isMlsPrefix(list.m)
      list.openDetail = 1
    if list.key
      list.reason = systemTokenCache[list.key]?.nm
    if historyObj[date]
      historyObj[date].his.push(list)
    else
      historyObj[date] = {
        date: date,
        his: [list],
        sumIn: addTokenPrefixSymbolAndDecimal(tokenSumInfoObj[date].sumIn)
        sumOut: addTokenPrefixSymbolAndDecimal(tokenSumInfoObj[date].sumOut)
      }
  )
  history = Object.values(historyObj)
  uidsList = Object.keys(userObj)
  return {history,uidsList}

# 根据token key求和token总值
sumTokenByTokenType = ({recordObj, tp, key, token}) ->
  if recordObj[key]
    recordObj[key].token += token
  else
    recordObj[key] = {token: token, tp: tp}
  return recordObj