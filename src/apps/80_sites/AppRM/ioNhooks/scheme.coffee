###
/scheme/
  features for APP direct link from other APP or webpage

Outside url:
  /open-app?p1=1&p2=2
  will automatically recognize if app installed, and jump to realmaster://?p1=1&p2=2, which in turn open up /scheme?p1=1&p2=2.
  Or it will jump to iOS or android download page, or the internal APP download page
###

config = CONFIG(['wechatApp','serverBase','mailEngine','contact'])
UserModel = MODEL 'User'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{ getFBUser, verifyAppleIdToken,handleOauthUser } = INCLUDE 'libapp.oauth'
sendMailService = SERVICE 'sendMail'
getDotHtml = DEF 'getDotHtml'
jwt = require 'jsonwebtoken'

MSG_STRINGS.def {
  PARSE_ERROR: 'Can not parse oauth info',
}
USER_LOGIN_URL = '/1.5/user/login'

genLoginUrl = (err)->
  "#{USER_LOGIN_URL}?err=#{err.toString()}"

# go home page
goHome = (resp) -> resp.redirect '/1.5/index'

APP 'scheme'
GET (req,resp)->
  UserModel.appAuth {req,resp},(user)-> # sign in first
    # single listing view
    if (ml_num = req.param('m_ids'))?.length > 5
      return resp.redirect "/1.5/prop/detail/inapp?id=#{ml_num}"
    # wechat sign in
    if (code = req.param('code'))?.length > 10
      return UserModel.getInfoFromWechat {code:code,appId:config.wechatApp?.AppID,secret:config.wechatApp?.AppSecret},(err,userinfo)->
        if err
          console.error 'scheme getInfoFromWechat error:' + err.toString()
          console.error err
          return goHome resp
        return goHome resp unless userinfo
        UserModel.getWechatAuth {req,wxuser:userinfo},(err,ret)->
          if err
            console.error 'scheme getWechatAuth error:' + err.toString()
            console.error err
            return goHome resp
          if ret?.extraInfo is 'EMAIL_VERIFY'
            req.session.set 'verifyEml',new Date().getTime(),->
            return resp.redirect '/1.5/user/success?eml='+ret.eml+'&wxUnionid='+userinfo.unionid
          # if need to go login/register page
          if ret?.needUser and ret?.wxuser
            url = "/1.5/user/bind/#{ret.wxuser.unionid}?d=/1.5/user/login"
            if ret.wxuser.flwng
              url += '&uid='+ret.wxuser.flwng
            return resp.redirect url
          goHome resp
    goHome resp

# facebook/google login used in native app
# see: facebookAuth
# /scheme/facebook?code=xxxxfacebookAuthToken
GET 'facebook',(req,resp)->
  unless (req.getDevType() is 'app')
    return resp.redirect genLoginUrl(req.l10n(MSG_STRINGS.ACCESS_DENIED))
  code = req.param 'code'
  return goHome resp unless code
  getFBUser code, (err, uData) ->
    if err
      debug.error 'getFBuser in scheme.coffee'
      return resp.redirect genLoginUrl(err)
    uData.uTp = 'facebook'
    handleOauthUser {isThirdPartyAuth:1,sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
      return resp.redirect genLoginUrl(err) if err
      return resp.redirect redirectUrl if redirectUrl
      goHome resp

GET 'google',(req,resp)->
  unless (req.getDevType() is 'app')
    return resp.redirect genLoginUrl(req.l10n(MSG_STRINGS.ACCESS_DENIED))
  idToken = req.param 'idToken'
  return resp.redirect(genLoginUrl(req.l10n(MSG_STRINGS.ACCESS_DENIED))) unless idToken
  try
    uData = jwt.decode(idToken)
  catch e
    return resp.redirect genLoginUrl(e)
  uData.id = uData.sub
  uData.uTp = 'google'
  handleOauthUser {isThirdPartyAuth:1,sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
    return resp.redirect genLoginUrl(err) if err
    return resp.redirect redirectUrl if redirectUrl
    goHome resp

updateAppleOauthUser = (uData,cb)->
  if uData.email
    return cb(null,uData)
  UserModel.findEmlByAppleId uData.id,(err, existUser)->
    if err
      debug.error 'Get user with apple id ', err
      return cb(err,null)
    if eml = existUser?.eml
      uData.email = eml[0] or eml
    cb(null,uData)

GET 'apple',(req,resp)->
  unless (req.getDevType() is 'app')
    return resp.redirect genLoginUrl(req.l10n(MSG_STRINGS.ACCESS_DENIED))
  token = req.param 'token'
  return resp.redirect(genLoginUrl(req.l10n(MSG_STRINGS.ACCESS_DENIED))) unless token
  try
    tokenResponse = JSON.parse token
  catch e
    debug.error MSG_STRINGS.PARSE_ERROR, 'apple'
    return resp.redirect "#{USER_LOGIN_URL}?err="+MSG_STRINGS.PARSE_ERROR
  verifyAppleIdToken tokenResponse,(err,verifiedUser)->
    return resp.redirect(genLoginUrl(err)) if err
    debug.debug verifiedUser, 'verifiedUser'
    appleUserObj = {id:verifiedUser.sub,uTp:'apple'}
    appleUserObj.email = verifiedUser.email
    appleUserObj.firstName = tokenResponse.fullName?.givenName
    appleUserObj.lastName = tokenResponse.fullName?.familyName
    debug.debug appleUserObj, 'appleUserObj'
    updateAppleOauthUser appleUserObj,(err,uData)->
      return resp.redirect genLoginUrl(err) if err
      handleOauthUser {isThirdPartyAuth:1,sendMailService,req,uData,UserModel,getDotHtml,MSG_STRINGS,config},(err,redirectUrl)->
        return resp.redirect genLoginUrl(err) if err
        return resp.redirect redirectUrl if redirectUrl
        goHome resp

# from push notification, u: url parameter needs to be encoded
GET 'jump',(req,resp)->
  UserModel.appAuth {req,resp},(user)-> # sign in first
    if url = req.param 'u'
      url = decodeURIComponent url
    else
      url = '/1.5/index'
    url = encodeURI url
    #console.log url
    resp.redirect url

APP 'open-app'
GET (req,resp)->
  url = 'realmaster://' + req.search
  dlUrl = req.shareHost()+'/app-download'
  if uid = req.param('uid')
    dlUrl += "?uid=#{uid}"
  if req.isIOSDevice()
    dlUrl = 'https://itunes.apple.com/us/app/realmaster/id978214719?ls=1&mt=8'
  else if req.isAndroid() and not req.isChinaIP()
    dlUrl = 'https://market.android.com/details?id=com.realmaster'
  resp.ckup 'open-app',{url: url,query:req.search, dlUrl:dlUrl}
  #.replace(/'/g,"%27").replace(/"/g,"%22")

VIEW 'open-app',->
  if @req.isWeChat()
    div id:'needBrowser', ->
      div class: 'content content-padded content-cover',\
      style: 'display:block; position:absolute;z-index: 20;top: 0; left: 0; width: 100%; height: 100%;',->
        div class:'backdrop', style:'background-color: rgba(0, 0, 0, .6);'
        #, onclick:"document.querySelector('#content-cover').style.display = 'none'"
        div class: 'arrow', style:'position:absolute; z-index:20; right:0; top:0;',->
          img style:'', src: '/img/arrow.png'
        div class: 'layer', style:'position: absolute; z-index:20; left: 20px; top: 224px;',->
          style -> '''p {color:white;font-size: 15px;margin-right: 20px;}'''
          div class: 'tip', href: '#', style:'color:white;',->
            p ->
              text _ '1. Click top right corner'
              img src: '/img/share-icon.png', alt: '',\
              style:'margin-left: 10px;height: 17px;vertical-align: middle;'
            p ->
              text _ "2. Select 'Open In Browser' "
              # img src: '/img/p.png', alt: ''
            br()
            h4 -> text _ 'Trying to Open RealMaster APP'
            p ->
              text _ 'You are in WeChat, please follow above instruction to start RealMaster APP.'
  else
    div id:'needBrowser', class:'',->
      header class: 'bar bar-nav',->
        h1 class: 'title', -> _ 'Try to start RealMaster APP'
      div class: 'content content-padded', style:'',->
        div style:'text-align:left; padding:15px; padding-top:30px;',->
          p -> _ "If RealMaster APP doesn't start automatically, please try load this page in your mobile browser."

  coffeejs {vars:{url:@url,dlurl:@dlUrl,q:@query,wechat:@req.isWeChat()}},->
    null
  js '/js/open_app.min.js'
