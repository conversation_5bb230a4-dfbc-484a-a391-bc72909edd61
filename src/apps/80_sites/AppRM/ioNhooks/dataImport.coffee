config = CONFIG(['serverBase','trebManualImport','mailEngine','importNotify'])
trebConfig = config.trebManualImport
chinaMode = config.serverBase?.chinaMode
masterMode = config.serverBase?.masterMode
createRNIIndex = config.serverBase?.createRNIIndex
# can only connect database.rni when masterMode is true(satelliteMode is false)
if not chinaMode
  TrebRecords  = COLLECTION 'rni','mls_treb_master_records'
  BcreManualRecords  = COLLECTION 'rni','mls_bcre_manual_records'
  BcreRaws  = COLLECTION 'rni','mls_bcre_manual_raws'
  TrebManualRecords = COLLECTION 'rni','mls_treb_manual_records'
  TrebManualRecordsLog = COLLECTION 'rni','mls_treb_manual_records_log'
  BcreManualRecordsLog  = COLLECTION 'rni','mls_bcre_manual_records_log'
BcreAgents  = COLLECTION 'vow','mls_bcre_agents'
Agents    = COLLECTION 'vow','agents'
Offices   = COLLECTION 'vow','offices'
Properties = COLLECTION 'vow','properties'
GeoCache  = COLLECTION 'vow','geo_cache'
ImporterLog = COLLECTION 'vow','importer_log'
Boundary = COLLECTION 'vow', 'boundary'
if createRNIIndex and (not config.serverBase?.developer_mode)
  ONE_YEAR_SECONDS = 365*24*3600
  BcreManualRecordsLog.createIndex {id:1,_mt:-1},{background:true}
  BcreManualRecordsLog.createIndex {_mt:-1},\
    {expireAfterSeconds:ONE_YEAR_SECONDS,background:true}
  TrebManualRecordsLog.createIndex {id:1,_mt:-1},{background:true}
  TrebManualRecordsLog.createIndex {_mt:-1},\
    {expireAfterSeconds:ONE_YEAR_SECONDS,background:true}

treb = INCLUDE 'libapp.trebImportForWatch'
bcre = INCLUDE 'libapp.bcreImportForWatch'

stringHelper = INCLUDE 'lib.helpers_string'
dateHelper = INCLUDE 'lib.helpers_date'
debugHelper = INCLUDE 'lib.debug'
GeoCacheOrig    = COLLECTION 'vow','geoCacheOrig'
sendMail = SERVICE 'sendMail'
debug = debugHelper.getDebugger()
# debug.info 'trebConfig',trebConfig
GeocodingStat    = COLLECTION 'vow','geo_coding_stat'
axios = require('axios')
XORCipher = INCLUDE('lib.XORCipher').XORCipher

{wait,clearWait} = INCLUDE('lib.helpers')
os = require('os')


colls =
  TrebRecords: TrebRecords
  TrebManualRecords: TrebManualRecords
  TrebManualRecordsLog: TrebManualRecordsLog
  BcreManualRecords: BcreManualRecords
  BcreManualRecordsLog: BcreManualRecordsLog
  BcreAgents: BcreAgents
  BcreRaws: BcreRaws
  Agents: Agents
  Offices: Offices
  Properties: Properties
  GeoCache: GeoCache
  Boundary: Boundary
key = '8ab6bf4833d97aff41045844d12fbab508221e4369c5c63491b66a3473ad5d80'





APP '_imp_'
challengeBit = null
challengeCode = null
timeStart = null
timeEnd = null
clientIP = null

EMAIL_ENGINE = config.mailEngine?.mailEngine2 or config.mailEngine?.mailEngine or 'SES'
EMAIL_FROM = sendMail.getFromEmailByEngine(EMAIL_ENGINE)
EMAIL_TO = ['<EMAIL>']
IMPORT_NOTIFY = config.importNotify?.defaultTo or EMAIL_TO
BCRE_NOTIFY = config.importNotify?.bcreTo or EMAIL_TO
TIMEOUT = 600000 #600000 in production use 2000 for nuitest
INACTIVE = 'INACTIVE'
ACTIVE = 'ACTIVE'

SRC_MAP ={
  BCRE:{
    status: INACTIVE
    startTime: 0
    endTime:0
    counts: 0
    timeout:null
    update: bcre.importMany
  },
  BCREAGNT:{
    status: INACTIVE
    startTime:0
    endTime:0
    counts: 0
    timeout:null
    update: bcre.importAgentMany
  },
  TREB:{
    status: INACTIVE
    startTime:0
    endTime:0
    counts: 0
    timeout:null
    update: treb.importMany
  },
  REALMMLP:{
    # TODO
  }
}


isGoodIp = (ip,cb)->
  return cb(true) if trebConfig?.skipManualImportAuth
  #if ip is clientIP then return cb true
  ImporterLog.findToArray {mt:{$gt:new Date(Date.now() - 90*60000)}},(err,results)->
    if err or not results then return cb false
    for r in results
      if r.ip is ip
        return ImporterLog.updateOne {_id:r._id},{$set:{mt:new Date()}},(err)->
          if err then debug.error err
          cb true
    return cb false

startTimer = ({src,type,ip},cb)->
  # clear timeout if it has it
  if (src.timeout)
    clearWait(src.timeout)
    src.timeout = null
  # let active activity pass
  # update status to active, add start time and send email for the new activity
  if src.status is ACTIVE
    return cb()
  else
    src.startTime = new Date().getTime()
    src.status = ACTIVE
    startTime = dateHelper.dateFormat(src.startTime)
    firstMail = {
      engine: EMAIL_ENGINE
      from: EMAIL_FROM
      to: getMailTo type
      subject: "Notification: #{type} importing started"
      text:
        "Start Time: #{startTime},\n
        Client ip: #{ip}"
      eventType: 'DataImportStarted'
    }
    sendMail.sendMail firstMail,(err,data)->
      if err
        debug.error err
      else
        debug.info data
      return cb()

updateTimer = ({src,type,ip})->
  # set end time and setTimeout
  src.endTime = new Date().getTime()
  # clear timeout if it has it
  if (src.timeout)
  # for async nature, must clear timeout right before set it
    clearWait(src.timeout)
  src.timeout = wait(TIMEOUT,{src,type,ip},sendEmail)


getMailTo = (type)->
  return if type is 'BCRE' then BCRE_NOTIFY else IMPORT_NOTIFY

sendEmail = ({src,type,ip})->
  spendTime = (src.endTime - src.startTime) / 1000
  startTime = dateHelper.dateFormat(src.startTime)
  counts = src.counts
  mail =
    engine: EMAIL_ENGINE
    from: EMAIL_FROM
    to: getMailTo type
    subject: "Notification: #{type} imported"
    text:
      "Start Time: #{startTime},\n
      Spend: #{spendTime} secs,\n
      Imported counts: #{counts},\n
      Client ip: #{ip},\n
      Hostname: #{os.hostname()}"
    eventType: 'DataImportFinished'
  sendMail.sendMail mail,(err,data)->
    return debug.error err if err
    src.status = INACTIVE
    src.timeout = null
    return debug.info data

# APP '_imp_/trbv3'
# chrome CORS is using options to get the header
OPTIONS 'trbv3',(req,resp,next)->
  # console.log req.method
  resp.setHeader 'Access-Control-Allow-Origin','*'
  resp.setHeader 'Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS'
  # headers["Access-Control-Allow-Credentials"] = false;
  resp.setHeader 'Access-Control-Max-Age', '86400' # 24 hours
  resp.setHeader 'Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization'
  # console.log resp.getHeader('Access-Control-Allow-Origin')
  return resp.send {ok:1}

POST 'trb/auth',(req,resp)->
  now = Date.now()
  debug.info "import auth #{req.remoteIP()}"
  return resp.send {err:'Who are you?'} if timeStart and now < (timeStart + 30000)
  if time = req.body?.time
    challengeBit = time
  debug.info req.body
  newCode = stringHelper.md5('' + now + 'AnyTHing' + time)
  challengeCode = XORCipher.encode key,newCode
  debug.info newCode
  clientIP = req.remoteIP()
  ImporterLog.insertOne {ts:new Date(),ip:clientIP,rts:new Date(time),mt:new Date()}
  resp.send {c:newCode,t:(Date.now() + 123)}


POST 'trb',(req,resp)->
  json = req.body
  clientIP = req.remoteIP()
  if trebConfig?.relayDomain
    debug.info "relay data to #{trebConfig.relayDomain}"
    axios.post(trebConfig.relayDomain+'/_imp_/trb', json)
    .then( (response)->
    )
    .catch((error)->
      debug.error('Error: relayDomain post error')
      # TODO: connect ECONNREFUSED ************:80
      # NOTE: error contains axios obj, noneed
      # if /502/.test error.toString()
      debug.error error.toString() if error
      # else
        # debug.error error.toString()
    )

  if 'string' is typeof json.data
    try
      json.data = JSON.parse json.data
    catch error
      findError('JSON.parse error')

  findError = (err, n)->
    debug.error err
    debug.error clientIP
    debug.error json.c
    req.logger?.error err
    resp.send {err:err, n}
  # return findError "Are you ready?" unless timeEnd and timeStart and challengeCode
  # return findError "Realy?" if now < timeStart
  # return findError "Too Late?" if now > timeEnd
  # return findError "WTF!" unless (json.c is challengeCode)
  # can not operate database.rni when chinaMode is true
  return findError MSG_STRINGS.CONNECT_RNI_ERROR if chinaMode
  isGoodIp clientIP, (good)->
    return findError 'Really!' unless good
    if (json.data?.srcTp in ['BCRE','BCREAGNT'])
      type = json.data.srcTp
    else if Array.isArray(json.data)
      type = 'TREB'
    else
      return findError "WTF!!#{typeof json.data}"
    src = SRC_MAP[type]
    startTimer {src,type:type,ip: clientIP},->
      #NOTE: src.update is import many, not mongo.update
      src.update json.data,colls,(err,n)->
        return findError err, n if err
        src.endTime = new Date().getTime()
        src.counts += n
        updateTimer({src,type: type,ip: clientIP})
        debug.info "imported #{type}: #{n}"
        return resp.send {ok:1,n:n}

###
format:
src:REALMMLP
###
POST 'trbv3',(req,resp)->
  body = req.body
  json = {}
  clientIP = req.remoteIP()
  resp.setHeader 'Access-Control-Allow-Origin','*'
  resp.setHeader 'Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS'
  # headers["Access-Control-Allow-Credentials"] = false;
  resp.setHeader 'Access-Control-Max-Age', '86400' # 24 hours
  resp.setHeader 'Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization'

  findError = (err, n)->
    debug.error err
    debug.error clientIP
    debug.error json.c
    req.logger?.error err
    resp.send {err:err, n}

  if trebConfig?.relayDomain
    debug.info "relay data to #{trebConfig.relayDomain}"
    axios.post(trebConfig.relayDomain+'/_imp_/trb', json)
    .then( (response)->
    )
    .catch((error)->
      debug.error('Error: relayDomain post error')
      # TODO: connect ECONNREFUSED ************:80
      # NOTE: error contains axios obj, noneed
      # if /502/.test error.toString()
      debug.error error.toString() if error
      # else
        # debug.error error.toString()
    )

  if 'string' is typeof body.data
    try
      json.data = JSON.parse body.data
    catch error
      findError('JSON.parse error')
  else
    json.data = body.data

  
  # return findError "Are you ready?" unless timeEnd and timeStart and challengeCode
  # return findError "Realy?" if now < timeStart
  # return findError "Too Late?" if now > timeEnd
  # return findError "WTF!" unless (json.c is challengeCode)
  # can not operate database.rni when chinaMode is true
  return findError MSG_STRINGS.CONNECT_RNI_ERROR if chinaMode
  # isGoodIp clientIP, (good)->
  #   return findError 'Really!' unless good
  if (body?.src in ['REALMMLP'])
    type = body.src
  else
    return findError "UNSUPPORTED!!#{typeof json.data}"
  src = SRC_MAP[type]
  console.log json.data
  # startTimer {src,type:type,ip: clientIP},->
    #NOTE: src.update is import many, not mongo.update
    # src.update json.data,colls,(err,n)->
      # return findError err, n if err
      # src.endTime = new Date().getTime()
      # src.counts += n
      # updateTimer({src,type: type,ip: clientIP})
      # debug.info "imported #{type}: #{n}"
  return resp.send {ok:1,n:0}