UserModel = MODEL 'User'
debug = DEBUG 'dumpHeader'
stringHelper = INCLUDE 'lib.helpers_string'

APP '_dumpheader'
GET (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    headers = req.headers
    ip = req.remoteIP()
    json = stringHelper.stringify {headers,ip}
    debug.info 'request',json
    resp.ckup 'dumpHeader', { json }

VIEW 'dumpHeader', ->
  h2 -> 'Headers'
  pre ->
    text @json