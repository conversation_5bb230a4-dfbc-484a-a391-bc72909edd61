# https://developer.apple.com/documentation/safariservices/supporting_associated_domains
#  OLD: 3ZJZ7RQ3Q9 -> 8A2DLASB37
#  new: 8A2DLASB37
# {
# "appID": "8P7343TG54.com.tencent.xin.SDKSample",
# "paths": ["/sdksample/*"]
# }
# {
#    "applinks":{
#       "apps":[
#
#       ],
#       "details":[
#          {
#             "appID":"532LCLCWL8.com.tencent.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"88L2Q4487U.com.tencent.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"8P7343TG54.com.tencent.wc.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"SG5PVJM4JW.com.tencent.wx",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"2CJ5YJ6LY5.com.tencent.wechat.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"8H3NQF6GGQ.com.tencent.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"8P7343TG54.com.tencent.wc.xin.SDKSample",
#             "paths":[
#                "/sdksample/*"
#             ]
#          },
#          {
#             "appID":"SG5PVJM4JW.com.tencent.wx.xin.WeAppSDKSample",
#             "paths":[
#                "/weappsdksample/*"
#             ]
#          },
#          {
#             "appID":"HKB8WPFBR9.com.tencent.qy.xin",
#             "paths":[
#                "/cgi-bin/newreadtemplate",
#                "/app/*"
#             ]
#          },
#          {
#             "appID":"HKB8WPFBR9.com.tencent.xin.WeAppSDKSample.db",
#             "paths":[
#                "/weappsdksample/*"
#             ]
#          },
#          {
#             "appID":"V6JU9G4QMB.com.tencent.mm.xin.WeAppSDKSample",
#             "paths":[
#                "/weappsdksample/*"
#             ]
#          }
#       ]
#    }
# }
#             "/1.5/prop/*"
# remove *
appleFile = """
{
  "applinks":{
    "apps":[],
    "details":[
      {
        "appID":"8A2DLASB37.com.realmaster",
        "paths":[
          "/app/*",
          "/sdksample/*",
          "*"
        ],
        "components":[
          {
            "#":"no_universal_links",
            "exclude":true,
            "comment":"Matches any URL whose fragment equals no_universal_links and instructs the system not to open it as a universal link"
          },
          {
            "/":"/getapp/*",
            "comment":"Matches any URL whose path starts with /buy/"
          },
          {
            "/":"/1.5/prop/detail",
            "comment":"open prop detail page in app"
          },
          {
            "/":"/s/*detail*",
            "comment":"open prop detail page in app using short url"
          },
          {
            "/":"/zh-cn/*",
            "exclude":true,
            "comment":"Matches any URL whose path starts with /help/website/ and instructs the system not to open it as a universal link"
          },
          {
            "/":"/1.5/topics/*",
            "?":{
              "id":"????????????????????????"
            },
            "comment":"Matches any URL whose path starts with /help/ and which has a query item with name 'articleNumber' and a value of exactly 4 characters"
          }
        ]
      }
    ]
  }
}
"""
# NOTE: 0 prod release
# 1 debug / test
# 2 debug with local key
googleFile = """
[{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target": {
    "namespace": "android_app",
    "package_name": "com.realmaster",
    "sha256_cert_fingerprints":
    ["75:92:E6:6C:BE:1D:12:60:89:65:A9:CC:DB:BC:E0:B9:4F:9C:FC:58:D9:73:5D:1B:B2:F0:D3:5D:F5:7C:11:9B",
    "fa:c6:17:45:dc:09:03:78:6f:b9:ed:e6:2a:96:2b:39:9f:73:48:f0:bb:6f:89:9b:83:32:66:75:91:03:3b:9c",
    "3e:cf:18:9e:b4:e5:60:44:3e:9a:79:72:53:cd:c1:3d:19:99:dd:e1:ea:5c:20:1e:2d:25:49:65:c0:36:c4:38"]
  }
}]
"""

# GET 'apple-app-site-association',(req,resp)->
#   # /openapp/
#   resp.contentMime('application/json')
#   resp.send appleFile

# GET '.well-known/apple-app-site-association',(req,resp)->
#   # /openapp/ charset=utf-8
#   resp.contentMime('application/json')
#   resp.send appleFile

# # https://stackoverflow.com/questions/28802115/i-am-trying-to-test-android-deep-link-urls-through-adb-to-launch-my-app
# GET '.well-known/assetlinks.json',(req,resp)->
#   # /openapp/ charset=utf-8
#   resp.contentMime('application/json')
#   resp.send googleFile

# empty = """
# <html><head></head><body></body></html>
# """
# GET 'sdksample',(req,resp)->
#   resp.send empty
#
# GET 'sdksample/:id',(req,resp)->
#   resp.send empty