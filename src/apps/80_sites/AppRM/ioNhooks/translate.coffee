conf = CONFIG(['azure','i18n','deepseek','deepL','openAI','gemini','claude'])
UserModel = MODEL 'User'
debug = DEBUG()

rateLimiter = INCLUDE 'lib.rateLimiterV2'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
translatorManager = translatorManagerLib.createTranslatorManager(conf)

propTransLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:30,source:'translate'}
propTransLimiterFilter = rateLimiter.createFilter propTransLimiterInstance


# TODO: security issues here. User can translate unlimied arbitrary strings.
# TODO: Shall only accept property id and translate it if not translated yet.

APP 'translate'
POST (req,resp)->
  _error = (e)->
    return resp.send {ok:0,err:e}
  toBeTranslatedStr = req.param 'm'
  return _error('No content!') unless toBeTranslatedStr
  return _error(MSG_STRINGS.UNKNOWN_APPID) unless (req.getDevType() is 'app')
  propTransLimiterFilter req,resp,()->
    toBeTranslatedStr = decodeURIComponent toBeTranslatedStr
    # in log data=: `[{}]`  '[{}]'
    UserModel.auth {req,resp},(user)->
      # m is encodeURIComponent(prop.m)
      return _error(MSG_STRINGS.NEED_LOGIN) unless user
      debug.debug 'translate call:',toBeTranslatedStr
      try
        [m,src] = await translatorManager.translate(toBeTranslatedStr)
        return resp.send {ok:1,m,src}
      catch err
        debug.error 'translate error:',err.toString(),'\nuser:',user.eml,'\ntoTranslate:', toBeTranslatedStr
        return resp.send {ok:0,error:'Translate failed'}
