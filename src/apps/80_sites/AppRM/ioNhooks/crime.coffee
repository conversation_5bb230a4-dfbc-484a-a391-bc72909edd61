config = CONFIG(['serverBase','importNotify','crime','mailEngine'])
chinaMode = config.serverBase.chinaMode

CrimeModel = MODEL 'Crime'


crimeLib = INCLUDE 'libapp.crimeImport'
stringHelper = INCLUDE 'lib.helpers_string'
dateHelper = INCLUDE 'lib.helpers_date'
debugHelper = INCLUDE 'lib.debug'
XORCipher = INCLUDE('lib.XORCipher').XORCipher
{wait,clearWait} = INCLUDE('lib.helpers')
rateLimiter = INCLUDE 'lib.rateLimiterV2'
helpers = INCLUDE 'lib.helpers'
{str2reg,genNanoId} = INCLUDE 'lib.helpers'
# sendMailLib = INCLUDE 'lib.sendMail'

# sendMail = sendMailLib.getSendMail config

sendMail = SERVICE 'sendMail'

# debug.info 'trebConfig',trebConfig
arcgisPbfDecode = require('arcgis-pbf-parser')
axios = require('axios')
os = require('os')
crypto = require('crypto')
util = require 'util'

debug = debugHelper.getDebugger()
# sendMailAsync = helpers.addAsyncSupport sendMail.sendMail
# key = '8ab6bf4833d97aff41045844d12fbab508221e4369c5c63491b66a3473ad5d80'

# APP ''
challengeBit = null
challengeCode = null
timeStart = null
timeEnd = null
clientIP = null
# NOTE: same token for 3 imports
gImportToken = null
gAuthValues = {}

EMAIL_ENGINE = config.mailEngine?.mailEngine2 or config.mailEngine?.mailEngine or 'SES'
EMAIL_FROM = sendMail.getFromEmailByEngine(EMAIL_ENGINE)
EMAIL_TO = ['<EMAIL>']
# EMAIL_TO = ['<EMAIL>']
IMPORT_NOTIFY = config.importNotify?.defaultTo or EMAIL_TO
BCRE_NOTIFY = config.importNotify?.bcreTo or EMAIL_TO
CRIME_NOTIFY = config.importNotify?.defaultTo or EMAIL_TO
V3_AUTH_TOKEN = config.crime?.v3ImpAuth or 'auth_admin_token'

SENDEMAIL_TIMEOUT = 600000 #600000 in production use 2000 for test
INACTIVE = 'INACTIVE'
ACTIVE = 'ACTIVE'

# src mapping for tracing import states
SRC_MAP ={
  # york region police
  YRP:{
    status: INACTIVE
    startTime: 0
    endTime:0
    counts: 0
    timeout:null
    update: crimeLib.importYRP
  },
  # toronto police service
  TPS:{
    status: INACTIVE
    startTime:0
    endTime:0
    counts: 0
    timeout:null
    update: crimeLib.importTPS
  },
  # peel regional police
  PRP:{
    status: INACTIVE
    startTime:0
    endTime:0
    counts: 0
    timeout:null
    update: crimeLib.importPRP
  },
  # halton regional police
  HRP:{
    status: INACTIVE
    startTime:0
    endTime:0
    counts: 0
    timeout:null
    update: crimeLib.importHRP
  },
}

# NOTE: no longer used, since using digest auth+token
# isGoodIp = (ip)->
#   if trebConfig?.skipManualImportAuth
#     return true
#   #if ip is clientIP then return cb true
#   results = await ImporterLog.findToArray {mt:{$gt:new Date(Date.now() - 90*60000)}}
#   if not results then return false
#   for r in results
#     if r.ip is ip
#       await ImporterLog.updateOne {_id:r._id},{$set:{mt:new Date()}}
#       return true
#   return false

# start timer for each type import, send email to indicate start
startTimer = ({src,type,ip})->
  # clear timeout if it has it
  if (src.timeout)
    clearWait(src.timeout)
    src.timeout = null
  # let active activity pass
  # update status to active, add start time and send email for the new activity
  if src.status is ACTIVE
    return true
  src.startTime = new Date().getTime()
  src.status = ACTIVE
  startTime = dateHelper.dateFormat(src.startTime)
  firstMail = {
    engine: EMAIL_ENGINE
    from: EMAIL_FROM
    to: getMailTo type
    subject: "Notification: #{type} importing started"
    text:
      "Start Time: #{startTime},\n
      Client ip: #{ip}"
    eventType: 'CrimeImportStarted'
  }
  sendMail.sendMail firstMail,(err,data)->
    console.log err,data
  return true

# update running time for each import
# @params {
#   src: object, # SRC_MAP.[src]对应的obj
#   type: string, # enum:['YRP','TPS','PRP']
#   ip: string, # user.ip
# }
updateTimer = ({src,type,ip})->
  # set end time and setTimeout
  src.endTime = new Date().getTime()
  # clear timeout if it has it
  if (src.timeout)
  # for async nature, must clear timeout right before set it
    clearWait(src.timeout)
  src.timeout = wait(SENDEMAIL_TIMEOUT,{src,type,ip},sendEmail)

# send diff mail to diff person
getMailTo = (type)->
  return if type is 'REALMMLP' then IMPORT_NOTIFY else CRIME_NOTIFY

# sendmail after import timeout done, notify for time and n
sendEmail = ({src,type,ip})->
  spendTime = (src.endTime - src.startTime) / 1000
  startTime = dateHelper.dateFormat(src.startTime)
  counts = src.counts
  mail =
    engine: EMAIL_ENGINE
    from: EMAIL_FROM
    to: getMailTo type
    subject: "Notification: #{type} imported"
    text:
      "Start Time: #{startTime},\n
      Spend: #{spendTime} secs,\n
      Imported counts: #{counts},\n
      Client ip: #{ip},\n
      Hostname: #{os.hostname()}"
    eventType: 'CrimeImportFinished'
  crimeLib.clearCache()
  gImportToken = null
  sendMail.sendMail mail,(err,data)->
    return debug.error err if err
    src.status = INACTIVE
    src.timeout = null
    return debug.info data

# old auth method
# POST 'auth',(req,resp)->
#   now = Date.now()
#   debug.info "import auth #{req.remoteIP()}"
#   return resp.send {err:'Who are you?'} if timeStart and now < (timeStart + 30000)
#   if time = req.body?.time
#     challengeBit = time
#   debug.info req.body
#   newCode = stringHelper.md5('' + now + 'AnyTHing' + time)
#   challengeCode = XORCipher.encode key,newCode
#   debug.info newCode
#   clientIP = req.remoteIP()
#   ImporterLog.insertOne {ts:new Date(),ip:clientIP,rts:new Date(time),mt:new Date()}
#   resp.send {c:newCode,t:(Date.now() + 123)}

# chrome CORS is using options to get the header
OPTIONS '_imp_/crime',(req,resp,next)->
  # console.log req.method
  resp.setHeader 'Access-Control-Allow-Origin','*'
  resp.setHeader 'Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS'
  # headers["Access-Control-Allow-Credentials"] = false;
  resp.setHeader 'Access-Control-Max-Age', '86400' # 24 hours
  resp.setHeader 'Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization'
  # console.log resp.getHeader('Access-Control-Allow-Origin')
  return resp.send {ok:1}

# +2024-05-17T17:47:17.098 127.0.0.1 OPTIONS localhost:8080/_imp_/crime 1715982437098 curl/8.4.0
# curl -i -X OPTIONS http://localhost:8080/_imp_/crime

# GET '_imp_/crime',(req,resp,next)->
#   console.log req.method
#   resp.setHeader 'Access-Control-Allow-Origin','*'
#   resp.setHeader 'Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS'
#   # headers["Access-Control-Allow-Credentials"] = false;
#   resp.setHeader 'Access-Control-Max-Age', '86400' # 24 hours
#   resp.setHeader 'Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization'
#   return resp.send {ok:1}

POST '_imp_/crime',(req,resp)->
  # console.log '++++',req.headers
  # console.log req.method
  resp.setHeader 'Access-Control-Allow-Origin','*'
  resp.setHeader 'Access-Control-Allow-Methods', 'POST, GET, PUT, DELETE, OPTIONS'
  # headers["Access-Control-Allow-Credentials"] = false;
  resp.setHeader 'Access-Control-Max-Age', '86400' # 24 hours
  resp.setHeader 'Access-Control-Allow-Headers', 'X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization'
  findError = (err, n)->
    debug.error err
    debug.error clientIP
    # debug.error json.c
    req.logger?.error err
    resp.send {err:err, n, ok:0}

  # checks header for token
  authHeader = req.headers.authorization or ''
  if (not authHeader) or (not authHeader.startsWith('Bearer '))
    # console.log '+++++authHeader no bearer unmatch!'
    return findError '403'
  authParams = getParamsFromHeader authHeader
  {token} = authParams
  if (not token) or (token isnt gImportToken)
    # console.log '+++++token unmatch!'
    return findError '403'

  # console.log req.method
  # return findError 'Really!' unless checkIsAuthed req
  return findError MSG_STRINGS.CONNECT_RNI_ERROR if chinaMode
  if not req?.body?.file
    return resp.send {err:'No file'}
  file = req.body.file
  src = req.body.src
  if src not in ['YRP','TPS','PRP','HRP']
    return resp.send {err:'No src'}
  clientIP = req.remoteIP()
  # console.log req.body.src if req.body
  # return resp.send {ok:1}
  try
    contentBuffer = Buffer.from(file, 'base64')
    # console.log contentBuffer
    featureCollection = arcgisPbfDecode(new Uint8Array(contentBuffer)).featureCollection
  catch error
    debug.error error
    return findError('JSON.parse error',0)
  # featureCollection = {type:'featureCollection',features:[]}
  try
    await CrimeModel.saveRawData featureCollection
  catch error
    debug.error error
    # return findError('JSON.parse error',0)
  featureCollectionList = []
  for item in featureCollection.features
    # console.log '---raw',item
    if mergedItem = crimeLib.isValidRawRecord(src,item)
      featureCollectionList.push mergedItem
  # console.log 'featureCollectionList:',featureCollectionList if featureCollectionList.length > 0
  if featureCollectionList.length <= 0
    return resp.send {ok:1,n:0,msg:'No valid record'}
  
  # return resp.send {ok:1}
  # return findError "Are you ready?" unless timeEnd and timeStart and challengeCode
  # return findError "Realy?" if now < timeStart
  # return findError "Too Late?" if now > timeEnd
  # can not operate database.rni when chinaMode is true
  
  srcImporter = SRC_MAP[src]
  return findError 'No src' unless srcImporter
  startTimer {src:srcImporter,type:src,ip: clientIP}
  #NOTE: src.update is import many, not mongo.update
  try
    {err,n} = await srcImporter.update featureCollectionList,CrimeModel
  catch err
    debug.error err
    return findError err,0
  # console.log 'xxxxx',err,n
  return findError err, n if err?.length
  srcImporter.endTime = new Date().getTime()
  srcImporter.counts += n
  updateTimer({src:srcImporter,type: src,ip: clientIP})
  debug.info "crime imported #{src}: #{n}"
  return resp.send {ok:1,n:n}



APP '_imp_/v3/login'

# salt for md5, use ramdomUUID
generateNonce = (type)->
  tmp =  crypto.randomUUID()
  gAuthValues[type] = tmp
  return tmp

# /**
#  * Extracts parameters from a Authorization header string.
#  *
#  * @param {string} [header=''] - The header string to extract parameters from.
#  * @returns {Object} - An object containing the extracted parameters.
#  */
# header = `Bearer token="${authResponse.token || ''}"`,
# TODO: use buildin lib
getParamsFromHeader = (header='')->
  # only support Bearer/Digest
  ret = header.substring(7)
  .split(',')
  .map((param) -> param.trim().split('='))
  .reduce((params, [key, value])->
    params[key] = value.replace(/"/g, '')
    return params
  , {})
  ret

# generate md5 hased response
generateResponse = (authToken, nonce)->
  HA1 = stringHelper.md5(authToken + ':' + nonce)
  HA2 = stringHelper.md5('POST:/_imp_/v3/login')
  response = stringHelper.md5(HA1 + ':' + nonce + ':' + HA2)
  return response

# send 401 unauth to resp
respUnAuthorized = (resp)->
  resp.setHeader(
    'WWW-Authenticate',
    "Digest, nonce=\"#{generateNonce('nonce')}\", opaque=\"#{generateNonce('opaque')}\""
  )
  resp.statusCode = 401
  resp.send('Unauthorized')

# send forbidden to resp
respForbidden = (resp)->
  resp.statusCode = 403
  resp.send('Forbidden')

# digest auth middle ware, verifies user response and hashed token
digestAuth = (req,resp,next)->
  # console.log '++++',req.headers
  authHeader = req.headers.authorization or ''
  if (not authHeader) or (not authHeader.startsWith('Digest '))
    # TODO: opaque is req uniq id
    return respUnAuthorized(resp)
    
  authParams = getParamsFromHeader authHeader
  {authToken, nonce, opaque, response} = authParams
  if (response isnt generateResponse(authToken, nonce))
    return respUnAuthorized(resp)
  if (authToken isnt stringHelper.md5(V3_AUTH_TOKEN))
    if nonce in [undefined,'undefined'] or opaque in [undefined,'undefined']
      return respUnAuthorized(resp)
    else
      # diff token
      return respForbidden(resp)
  return next()

generateAndStoreToken = ()->
  gImportToken = genNanoId()
  return gImportToken

# /**
#  * Handles a POST request to the '_imp_/v3/login' endpoint.
#  *
#  * This function first adds the client's IP address to a rate limiter whitelist,
#  * then calls the `digestAuth` function to handle the authentication process.
#  * If the authentication is successful, it generates and stores a new token,
#  * and returns a response with a 'Login successful' message and the token.
#  *
#  * @param {Object} req - The HTTP request object.
#  * @param {Object} resp - The HTTP response object.
#  * @returns {void}
#  */
# POST '_imp_/v3/login'
POST (req, resp)->
  # resp.setHeader 'Access-Control-Allow-Origin','*'
  next = ()->
    token = generateAndStoreToken()
    return resp.send {message: 'Login successful', token}
  rateLimiter.addWhiteListIP req.remoteIP()
  return digestAuth(req,resp,next)