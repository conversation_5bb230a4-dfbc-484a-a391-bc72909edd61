config = CONFIG(['serverBase'])
developer_mode = config.serverBase?.developer_mode




APP ''
productionRobots = '
User-agent: *\nDisallow: /1.5/user/\nDisallow: /wpnews/\nDisallow: /forum/
'
# User-agent: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\nDisallow: /\n

# for dev, dont allow all
devRobots = '
User-agent: *\nDisallow: /
'
# TODO: 生成更好更SEO的txt https://www.w3cschool.cn/tools/index?name=createrobots
# /robots.txt
GET 'robots.txt',(req,resp)->
  resp.contentMime('text/plain')
  if developer_mode
    return resp.send devRobots
  resp.send productionRobots
