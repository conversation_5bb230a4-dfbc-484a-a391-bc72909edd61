Ads = COLLECTION 'chome', 'ads'
# statHelper = INCLUDE 'libapp.stat_helper'
helpers = INCLUDE 'lib.helpers'

APP 'splashScreen'

splashStat = {
  imp:0
  click:0
  skip:0
  unknown:0
}

updateSplashCounter = (cb)->
  unless splashStat.imp or splashStat.click
    return
  date = helpers.dateFormat new Date(), 'YYYY_MM_DD'
  tmp = {}
  for k,v of splashStat
    tmp[k] = v
  tmp["stat.#{date}vc"] = splashStat.imp
  tmp["stat.#{date}c"] = splashStat.click
  tmp["stat.#{date}skip"] = splashStat.skip
  for k,v of splashStat
    splashStat[k] = 0
  id = 'splashStat'#TODO: ad provide + ad name
  Ads.findOneAndUpdate {_id:id}, {$inc:tmp}, {upsert:true},(err,ret)->
    if err
      console.error err
      cb(err) if cb

helpers.repeat 60000, ->
  updateSplashCounter()

GET '', (req, resp)->
  t = req.param 't'
  ts = req.param 'ts'
  unless t and ts
    return resp.send 'need t and ts'
  if t not in ['imp','click','skip']
    t = 'unknown'
  #host name ignores port
  src = (req.hostname+'').replace(/\W/ig,'_').toLowerCase()
  splashStat[t]+=1
  splashStat[src] ?= 0
  splashStat[src] += 1
  resp.send 'Ok'

APP 'webhook'
#gmail, sendGrid, SES
