UserModel = MODEL 'User'
conf = CONFIG(['wechat','wechatApp','wechatWeb'])
{respError} = INCLUDE 'libapp.responseHelper'
crypto = require 'crypto'
debug = DEBUG()

# 生成签名
generateSign = (nonce,timestamp,token)->
  arr = [nonce,timestamp,token]
  arr.sort()
  str = arr.join('')

  return crypto.createHash('sha1').update(str).digest('hex')


APP 'wechat'

# /wechat/webhook
# 用户发给移动应用的消息以及开发者需要的事件推送，都将被微信转发至该服务器地址中。
# URL(服务器地址)https://www.realmaster.com/wechat/webhook
# Token(令牌) conf.wechatToken = 71b1
# EncodingAESKey
# (消息加密密钥) conf.wechatAESKey = fUKNE
# 消息加密方式兼容模式
# 数据格式JSON
GET 'webhook', (req,resp)->
  signature = req.param 'signature'
  timestamp = req.param 'timestamp'
  nonce = req.param 'nonce'
  echostr = req.param 'echostr'
  if not (signature and timestamp and nonce and echostr)
    debug.error 'webhook: Bad parameter'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}

  hashres = generateSign nonce,timestamp,conf.wechat?.token
  if hashres is signature
    return resp.send echostr
  else
    debug.error 'webhook: Invalid signature'
    return respError {clientMsg:req.l10n(MSG_STRINGS.INVALID_SIGNATURE),resp}


POST 'webhook', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}  unless req.body
  MsgType= req.body?.MsgType
  Event = req.body?.Event
  OpenID = req.body?.OpenID
  AppID = req.body?.AppID
  RevokeInfo = req.body?.RevokeInfo
  IsMob = false
  if (AppID isnt conf.wechatApp?.AppID) and (AppID isnt conf.wechatWeb?.AppID) and (AppID isnt conf.wechat?.appId)
    debug.error 'webhook: Unknown AppID'+AppID
    return respError {clientMsg:req.l10n(MSG_STRINGS.UNKNOWN_APPID),resp}
  if AppID is conf.wechatApp?.AppID
    IsMob = true
  # TODO:用户信息修改对应操作数据库中用户信息

  # 微信用户撤回授权
  if Event is 'user_authorization_revoke' and RevokeInfo is '301'
    debug.info 'webhook: Revoke Wechat:'+OpenID+':'+RevokeInfo
    UserModel.revokeWechat {OpenID,IsMob},(err,userinfo)->
      if err
        debug.error 'webhook: Revoke Wechat Failed'+err.toString()
        return respError {clientMsg:req.l10n(MSG_STRINGS.REVOKE_FAILED),resp}
      # 微信规定的返回格式
      resp.send MSG_STRINGS.SUCCESS
  else
    debug.info 'webhook: Unknown Event'+Event
    resp.send MSG_STRINGS.SUCCESS