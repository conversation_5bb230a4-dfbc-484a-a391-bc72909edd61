UserModel = MODEL 'User'
CrmModel = MODEL 'Crm'
{respError} = INCLUDE 'libapp.responseHelper'
libproperties = INCLUDE 'libapp.properties'
{fullNameOrNickname} = INCLUDE 'libapp.user'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
debug = DEBUG()
ActionCodeModel = MODEL 'ActionCode'
ShowingModel = MODEL 'Showing'
GroupModel = MODEL 'Group'
# listingPicUrls = libproperties.listingPicUrls
errorPage = (err, resp) ->
  resp.ckup 'generalError', {err:err}

APP '1.5'
APP 'crm', true

# get all crm list page
#1.5/crm
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    resp.ckup 'contactCrm',{out:true},'_',{noAngular:true}

POST '',(req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless body = req.body
  UserModel.appAuth {req,resp}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.hasRole 'realtor'
    id = body.id
    owner = body.owner or false
    if body.viewedUid and (user._id.toString() isnt body.viewedUid)
      try
        {err,viewedUser} = await ShowingModel.checkShowingAdmin {req,resp,user,viewedUid:body.viewedUid}
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
      return respError {clientMsg:req.l10n(err),resp} if err
      user = viewedUser if viewedUser
    try
      list = await CrmModel.getList {id,uid:user._id,lang:user.locale,owner}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    resp.send {ok:1, list:list}

POST 'edit', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless body = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.hasRole 'realtor'
    if body.viewedUid and (user._id.toString() isnt body.viewedUid)
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp}
    try
      emailError = await UserModel.checkEmailAddress body.eml
    catch err
      debug.error err,' checkEmailAddress:',body.eml
      return resp.send {ec:0,err:MSG_STRINGS.BAD_PARAMETER}
    if emailError
      return resp.send {ok:0,emlErr:1,err:req.l10n(emailError)}
    try
      isRealGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    if (not req.isAllowed 'noteAdmin') and (not isRealGroup)
      delete body.source
    try
      await CrmModel.update {uid:user._id,body}
    catch err
      # 没有找到邮箱对应经纪，返回提示,添加标记以识别不是数据库问题，coErr:1
      return resp.send {ok:0,err:req.l10n(MSG_STRINGS.NO_COOP_AGENT),coErr:1} if err is 0
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    return resp.send {ok:1}

POST 'updateLst', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}  unless req.body
  body= req.body
  id = body._id
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.hasRole 'realtor'
    try
      await CrmModel.updateLstById id
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    return resp.send {ok:1}

###
  根据crm coll的uid和cid判断用户是否存在关联的经济，跳转用户同意关联页面,自己不能绑定自己
###
GET 'linkagent/:id',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    id = req.param('id')
    cid = req.param('cid')#当前用户绑定的crm._id
    try
      agent = await UserModel.findByIdAsync id,{projection:UserModel.AGENT_FIELDS}
      ret = await CrmModel.checkAgentLinked {uid:user._id,aid:id}
    catch err
      debug.error err
    avt = agent?.avt or agent?.wxavt or agent?.pic
    cfg =
      agentId:id
      cid:cid
      agentAvt : replaceRM2REImagePath avt
      agentNm : fullNameOrNickname req.locale(),agent
      isPopup : req.param 'isPopup'
      d : '/home/<USER>'
      code : req.param('code')
    if ret
      cfg.linked = ret
    if id is user._id.toString()
      cfg.self = true
    resp.ckup 'showing/linkAgentResult',cfg,'_',{noAngular:true}

# 用户同意关联经纪，crm中添加cid并且在对应的showing中添加cid
POST 'linkagent', (req,resp)->
  unless ((aid = req.body?.aid) and (cid = req.body?.cid) and (code = req.body?.code))
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    user.lang = user.locale
    user.eml = UserModel.getEmail user
    try
      crmUser = await CrmModel.linkAgent {user,aid,cid}
      await ActionCodeModel.removeCode code
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    if crmUser is MSG_STRINGS.USER_NOT_FOUND
      return resp.send {ok:0, e: req.l10n(MSG_STRINGS.USER_NOT_FOUND)}
    return resp.send {ok:1}

POST 'unlinkagent', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}  unless body = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    unless (req.hasRole('realtor') or ((position = body.position) is 'clnt'))
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp}
    try
      ret = await CrmModel.unlinkAgent {uid:user._id,id:body.id,position:body.position}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    return resp.send {ok:1,msg:req.l10n('Unlinked')}

POST 'linkedAgents', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}  unless body = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    {limit,page} = body
    try
      users = await CrmModel.getLinkedAgents {user,limit,page}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    return resp.send {ok:1,list:[]} unless users?.length
    for _user in users
      _user.fnm = fullNameOrNickname(user.locale,_user)
      _user.avt = replaceRM2REImagePath _user.avt if _user.avt
    return resp.send {ok:1,list:users}

VIEW "contactCrm",->
  div id:'vueBody',->
    text """<contact-crm></contact-crm>"""
  coffeejs {vars:{
    fromUrl:@out
    }
  }, ->
    null
  js '/js/entry/commons.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/emailMisspelled.min.js'
  js '/js/entry/contactCrm.js'

###
注册生成绑定用户的qrcode
###
ActionCodeModel.regAction 'linkUser',(params,cb)->
  return cb MSG_STRINGS.NO_PARAM unless params
  return cb 'No request object' unless params.req
  UserModel.appAuth {req:params.req,resp:params.req.resp}, (user)->
    return cb MSG_STRINGS.NEED_LOGIN unless user
    user.lang = user.locale
    user.eml = UserModel.getEmail user
    try
      await CrmModel.linkAgent {user,aid:params.data?.aid,cid:params.data?.cid}
    catch err
      ddebug.error err
      return cb MSG_STRINGS.SYSTEM_ERROR
    cb null,result

###
  删除协作经纪
###
POST 'deleteCoop', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}  unless body = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.hasRole 'realtor'
    try
      ret = await CrmModel.deleteCoopAgent {uid:user._id,id:body._id}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    if ret is MSG_STRINGS.CANT_DELETE
      return resp.send {ok:1,msg:req.l10n(MSG_STRINGS.CANT_DELETE)}
    return resp.send {ok:1,msg:req.l10n(MSG_STRINGS.DELETED_COOP_AGENT)}
