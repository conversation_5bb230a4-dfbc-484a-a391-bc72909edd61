# User = COLLECTION 'chome', 'user'
UserCRM = COLLECTION 'chome', 'rm_crm'
UserCRM.createIndex {refid:1},{background:true,sparse:true}
UserCRM.createIndex {uid:1},{background:true}
GroupModel = MODEL 'Group'
UserModel = MODEL 'User'
helpers = INCLUDE 'lib.helpers'
{respError} = INCLUDE 'libapp.responseHelper'
ObjectId = INCLUDE('lib.mongo4').ObjectId

USER_FIELDS = {_id:1,nm:1,eml:1,tel:1,mbl:1,wx:1,avt:1,lts:1,roles:1,mt:1,crmID:1}
CRM_FIELDS = {_id:1,refid:1,memos:1,lmemo:1,tags:1,gids:1,uid:1,ts:1,mt:1}
USER_LIMIT = 50
CRM_TS =
  crmts: '_id'
  crmmt: 'mt'

isCRMTs = (ts)->
  CRM_TS[ts]

objectIdFromDate = (date)->
	new ObjectId(Math.floor(date.getTime() / 1000).toString(16) + "0000000000000000")

getUsers = (query,option,cb)->
  sort = {}
  # sort crm create date
  if not (isCRMTs option.sort)
    sort[option.sort] = -1
  if option.page
    skip = (option.page - 1) * USER_LIMIT
  UserModel.getList query,{projection:UserModel.CRM_FIELDS,sort,limit:USER_LIMIT,skip},(err,users)->
    return cb err,null,null if err
    refids = []
    for u in users
      u.ts = u._id.getTimestamp()
      refids.push u._id
    cb err,users,refids

getUserCRMs = (q,opt,user,cb)->
  sort = {}
  if isCRMTs opt.sort
    sort[CRM_TS[opt.sort]] = -1
  else
    sort[opt.sort] = -1
  if opt.page
    skip = (opt.page - 1) * USER_LIMIT
  UserCRM.findToArray q,{projection:CRM_FIELDS,limit:USER_LIMIT,sort,skip},(err,crms)->
    return cb err,null if err
    cb null,crms

getUserGroupQuery = (user,cb)->
  GroupModel.getUserGroupIDs user,(err,myGids)->
    cb err,{$or:[{uid:user._id},{gids:{$in:myGids}}]}
DEF 'getUserGroupQuery',getUserGroupQuery

getTagsTypes = (req,user,force,cb)->
  if (not force) and (tagsTps = req.session?.get 'crmTagsTps')
    return cb null, tagsTps
  getUserGroupQuery user,(err,q)->
    UserCRM.distinct 'tags',q,(err,tags)->
      return cb err,null if err
      UserCRM.distinct 'memos.tps',q,(err,tps)->
        return cb err,null if err
        ret = {tags,tps}
        req.session?.set 'crmTagsTps', ret, ->
          cb null, ret
DEF 'getTagsTypes',getTagsTypes

handleRefId = (refid)->
  if helpers.isObjectIDString(refid?.toString())
    return new ObjectId(refid)
  ## handle listing agent id DDF1234567
  return refid

DEF 'handleRefId',handleRefId

arrayEqual = (a, b) ->
  a.length is b.length and a.every (elem, i) -> elem is b[i]

upsertCRM = (set,user,cb)->
  fields = ['_id','refid','uid','tags','lmemo','memos','gids','memo','oldTags']
  refid = handleRefId set.refid
  for key,value of set
    if fields.indexOf(key) is -1
      delete set[key]
  delete set.refid
  delete set.memos unless set.memos and set.memos.length > 0
  set.refCol = 'listing_agent' unless helpers.isObjectIDString(refid?.toString())
  uid = new ObjectId(user._id)
  set.mt = new Date()

  #handle gids
  if set.gids.length is 0
    gids = user.dGids || []
  else
    gids = set.gids
  if gids
    objectGids = []
    for g in gids
      objectGids.push(new ObjectId g)
    set.gids = objectGids
  unless set.gids
    delete set.gids
  #end of handle gids
  if set._id
    q = {_id:new ObjectId(set._id)}
    delete set._id
    delete set.uid
  else
    q = {refid:refid,uid:uid}
  tempM = set.memo
  delete set.memo
  delete set.memos
  #handle different tags create memo for tags change log
  oldTags = set.oldTags
  delete set.oldTags
  if (not (arrayEqual set.tags,oldTags))
    diffTags = true
    tempM =
      m: "#{oldTags.join(',')} => #{set.tags.join(',')}"

  if tempM?.m or diffTags
    memo =
      uid: uid,
      nm: user.nm,
      ts: new Date(),
      m: tempM.m
    if tempM.tps
      memo.tps = tempM.tps
    set.lmemo = memo
    update =
      $push: memos: memo
      $set:set
  else
    update =
      $set:set
  UserCRM.findOneAndUpdate q,update, {upsert:true,returnDocument:'after'},(err,crm)->
    return cb err,null if err
    if crm and crm.ok is 1
      result = crm.value
      GroupModel.getCRMGroups result,(err,result)->
        return cb err,null if err
        result.oldTags = result.tags
        cb null, result
DEF 'upsertCRM', upsertCRM


getCRMFilters = (q,body)->
  tags = body.tags
  tps = body.tps
  includeTag = body.includeTag
  groups = body.groups
  if tags
    if includeTag is false
      q.tags = {$nin:tags}
    else
      q.tags = {$in:tags} if tags
  q['memos.tps'] = {$in:tps} if tps
  if groups
    gids = []
    for gid in groups
      gids.push new ObjectId(gid)
    q.gids = {$in:gids}
  q
DEF 'getCRMFilters',getCRMFilters

crmAuth = (req,resp,cb)->
  UserModel.appAuth {req,resp},(user)->
    return resp.ckup 'generalError',{err:"Not Authorized"} unless (user and (req.isAllowed 'productSales'))
    cb user
DEF 'crmAuth',crmAuth


mergeCRM2User = (users,crms,opt,cb)->
  crmUsers = {}
  if crms.length is 0
    return cb null,users
  if crms?.length > 0
    GroupModel.mergeGroups2CRM crms,(err,crms)->
      cb err,null if err
      for c in crms
        if crmUsers[c.refid]
          crmUsers[c.refid].push c
        else
          crmUsers[c.refid] = [c]
      sort = opt.sort
      isCRMSort = isCRMTs sort
      for u in users
        u.crm = crmUsers[u._id]
        u[sort] = getSortDateCRMs crmUsers[u._id],CRM_TS[sort] if isCRMSort
      sortByKey users,sort if isCRMSort
      cb null,users

getSortDateCRMs = (crms,key)->
  date = null
  for crm in crms
    date = crm[key] if crm[key] > date
  date

sortByKey = (array, key) ->
  array.sort (a,b) ->
    if a[key] < b[key]
      1
    else if a[key] > b[key]
      -1
    else
      0

#get users then get conresponding crms
getUserWithCRM = (query,option,user,cb)->
  getUsers query,option, (err,users,refids)->
    return cb 'Error when get realtors',null if err
    getUserGroupQuery user,(err,q)->
      return cb 'Error when get groups',null if err
      cb null,users if option.crmOpt is 0
      q.refid ={$in:refids}
      getUserCRMs q,option,user,(err,crms)->
        return cb 'Error when get CRM',null if err
        mergeCRM2User users,crms,option,(err,result)->
          return cb 'Error when get CRM groups',null if err
          cb null,result

getCRMWithUser = (query,option,crmFilterQuery,user,cb)->
  getUserGroupQuery user,(err,qgroup)->
    crmFilterQuery.$or = qgroup.$or
    getUserCRMs crmFilterQuery,option,user,(err,crms)->
      return cb 'Error when get UseCRM',null if err
      return cb null,[] if crms.length is 0
      uids = []
      for crm in crms
        uids.push crm.refid
      query._id = {$in:uids}
      getUsers query,option, (err,users,refids)->
        return cb 'Error when get users',null if err
        mergeCRM2User users,crms,option,(err,result)->
          return cb 'Error when get groups',null if err
          cb null,result

APP '1.5'
APP 'crm',true

GET 'index',(req, resp)->
  crmAuth req,resp,(user)->
    getTagsTypes req,user,false,(err,tagsTps)->
      resp.ckup 'index',{tagsTps:tagsTps,owner:user},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

#/1.5/crm/detail/?id=87asd98f7as98df7
GET 'detail',(req,resp)->
  crmAuth req,resp,(user)->
    dispVar = {}
    dispVar.isAdmin = req.isAllowed 'admin',user
    crmid = req.param('crmid')
    refid = req.param('refid')
    unless crmid or refid
      return resp.ckup 'generalError',{err:"No CRMID specified"}
    # TODO: usually we keep the object(this), caller side doesn't know if the function
    #             depends on 'this' pointer. use UserModel['findById' or 'findByCrmId']() instead
    if refid
      # fn = UserModel.findById
      fn = 'findById'
      q = refid
    if crmid
      # fn = UserModel.findByCrmId
      fn = 'findByCrmId'
      q = crmid
    UserModel[fn] q,{projection:UserModel.CRM_FIELDS},(err, crmu) ->
      getUserGroupQuery user,(err,q2)->
        if crmid
          q2._id = new ObjectId(crmid)
        if refid
          q2.refid = new ObjectId(refid)
        UserCRM.findOne q2, (err, crm) ->
          getTagsTypes req,user,false,(err,tagsTps)->
            unless crm and crm.gids
              crmu.crm = crm
              return resp.ckup 'detail',{user:crmu,types:tagsTps.tps,tags:tagsTps.tags,dispVar},"_",{noAngular:true,noUserModal:true,noRatchetJs:true}
            try
              groups = await GroupModel.findGroupsByIds crm.gids,{nm:1,_id:1}
            catch err
              debug.error '/crm/detail',err
            if groups
              crm.groups = groups
            crmu.crm = crm
            resp.ckup 'detail',{user:crmu,types:tagsTps.tps,tags:tagsTps.tags,dispVar},"_",{noAngular:true,noUserModal:true,noRatchetJs:true}

POST 'list',(req,resp)->
  crmAuth req,resp,(user)->
    unless body = req.body
      return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp} if err
    option = page:(body.page or 1)
    option.sort = if body.sort then body.sort else '_id'
    dateSort = if isCRMTs(option.sort) then CRM_TS[option.sort] else option.sort
    query = roles:$exists:true
    query.roles = body.role if body.role
    crmFilterQuery = {}
    if search = body.search
      searchReg = helpers.str2reg(search,true)
      query.$or = [
        {eml:searchReg},
        {oEml:searchReg},
        {nm_en:searchReg},
        {nm_zh:searchReg},
        {nm:searchReg},
        {mbl:searchReg},
        {tel:searchReg}
      ]
    if start_date = body.start_date
      startDate = if dateSort is '_id' then objectIdFromDate(new Date(start_date)) else new Date(start_date)
      startDateFilter = {$gte:startDate}
      if isCRMTs(option.sort)
        crmFilterQuery[dateSort] = startDateFilter
      else
        query[dateSort] = startDateFilter
    if end_date = body.end_date
      endDate = if dateSort is '_id' then objectIdFromDate(new Date(end_date)) else new Date(end_date)
      endDateFilter = {$lte:endDate}
      if isCRMTs(option.sort)
        crmFilterQuery[dateSort] = endDateFilter
      else
        query[dateSort] = endDateFilter
      if startDateFilter
        if isCRMTs(option.sort)
          crmFilterQuery[dateSort] = {$gte:startDate,$lte:endDate}
        else
          query[dateSort] = {$gte:startDate,$lte:endDate}
    tags = body.tags
    groups = body.groups
    tps = body.tps
    crmOpt = body.crmOpt
    if (tags?.length > 0) or (groups?.length > 0) or (isCRMTs(option.sort)) or (tps?.length > 0) or (crmOpt is 1)
      hasCRM = true
    if hasCRM
      crmFilterQuery = getCRMFilters crmFilterQuery,body
      getCRMWithUser query,option,crmFilterQuery,user,(err,result)->
         return respError {clientMsg:MSG_STRINGS.DB_ERROR,resp, sysErr:err} if err
         resp.send {ok:1,result:result}
    else
      option.crmOpt = body.crmOpt
      query.crmID = {$exists:0} if crmOpt is 0
      getUserWithCRM query,option,user,(err,result)->
        return respError {clientMsg:MSG_STRINGS.DB_ERROR,resp, sysErr:err} if err
        resp.send {ok:1,result:result}
POST 'user_groups',(req,resp)->
  crmAuth req,resp,(user)->
    GroupModel.getUserGroups user, (err,groups)->
      return respError {clientMsg:MSG_STRINGS.DB_ERROR,resp, sysErr:err} if err
      resp.send {ok:1,ret:groups}

POST 'submit', (req,resp)->
  crmAuth req,resp,(user)->
    return respError {clientMsg:MSG_STRINGS.BAD_PARAMETER,resp} unless (set = req.body)
    upsertCRM set,user,(err,result)->
      return respError {clientMsg:MSG_STRINGS.DB_ERROR,resp, sysErr:err} if err
      getTagsTypes req,user,true,(err,tagsTps)->
        UserModel.addCRMID {req,uid:result.refid,crmid:result._id},(err,user)->
          return respError {clientMsg:MSG_STRINGS.DB_ERROR,resp, sysErr:err} if err
          return resp.send {ok:1,result:result,types:tagsTps.tps,tags:tagsTps.tags}

POST 'submitDgroup',(req,resp)->
  crmAuth req,resp,(user)->
    dGids = req.body.dGids
    UserModel.updateDGids {req,dGids},(err)->
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp, sysErr:err} if err
      resp.send {ok:1,message:req.l10n('Saved')}

POST 'get_tagstps',(req,resp)->
  crmAuth req,resp,(user)->
    getTagsTypes req,user,false,(err,tagsTps)->
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp, sysErr:err} if err
      resp.send {ok:1,tagsTps:tagsTps}

VIEW 'index',->
  div id:'vueBody',style:'height:100%;overflow:hidden;',->
    text """<app-crm></app-crm>"""
  coffeejs {vars:{tagsTps:@tagsTps,owner:@owner}},->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appCrm.js'

VIEW 'detail',->
  div id:'vueBody',->
    text """<app-crm-detail></app-crm-detail>"""
  coffeejs {vars:{user:@user,tags:@tags,types:@types,dispVar:@dispVar}},->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appCrmDetail.js'
