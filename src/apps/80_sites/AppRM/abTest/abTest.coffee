ABTestModel = MODEL 'ABTest'
{respError} = INCLUDE 'libapp.responseHelper'
{isAlphaNumeric} = INCLUDE 'lib.helpers_string'
debug = DEBUG()

APP 'abtest'
# 根据传入数据，创建或更新test结果
# /abtest?groupName=&uuid=&seed=&action=&group=
PUT '',(req,resp)->
  groupName = req.param 'groupName'
  seed = req.cookies[groupName+'seed']
  uuid = req.cookies.uuid
  action = req.param 'action'
  unless isAlphaNumeric(uuid,12,'nanoid') and isAlphaNumeric(groupName)
    return respError {category:MSG_STRINGS.BAD_PARAMETER, resp}
  try
    {error,group} = await ABTestModel.update {uuid,groupName,seed,action}
  catch err
    debug.error err
    return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
  if error
    return respError {category:MSG_STRINGS.BAD_PARAMETER, resp}
  resp.send {ok:1,group}
