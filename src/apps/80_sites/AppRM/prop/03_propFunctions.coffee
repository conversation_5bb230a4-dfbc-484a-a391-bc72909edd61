helpers = INCLUDE 'lib.helpers'
libproperties = INCLUDE 'libapp.properties'
libPropertyImage = INCLUDE 'libapp.propertyImage'
propWeb = INCLUDE 'libapp.propWeb'
propSearch = INCLUDE 'libapp.propSearch'
statHelper = INCLUDE 'libapp.stat_helper'
limiter = INCLUDE 'lib.limiter'
libUser = INCLUDE 'libapp.user'

Properties = MODEL 'Properties'
ProvAndCity = MODEL 'ProvAndCity'
PropStatsModel = MODEL 'PropStats'
Projects = COLLECTION 'chome','pre_constrct'
# UserListings = COLLECTION 'chome', 'user_listing'
SysData = COLLECTION 'chome', 'sysdata'
UserWatchModel =MODEL 'UserWatch'

# checkAllowed = DEF '_access_allowed'
getConfig = DEF 'getConfig'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
config = CONFIG(['serverBase','share'])
debug = DEBUG()

get_shared_to_from_share = DEF 'get_shared_to_from_share'
isOwnCtrlContact = DEF 'isOwnCtrlContact'
# fnIsRealtor = DEF 'isRealtor'

UserModel = MODEL 'User'
propTranslate = INCLUDE 'libapp.propertiesTranslate'

# Building,Utilities
# isDDFField = (nm)->
#   if 'string' isnt typeof nm
#     return false
#   if not /^[A-Z]/.test nm
#     return false
#   if nm in DDF_EXT_FIELDS
#     return true
#   false

# isSimpleString = (v)->
#   return 'string' is typeof v and not /\d/.test v

# isSimpleArray = (l)->
#   unless Array.isArray l
#     return false
#   for i in l
#     if 'string' isnt typeof i or not isSimpleString(i)
#       return false
#   return true
USER_LIMIT = 50
VIP_LIMIT  = 150

# TODO: deprecated, remove after not called,
checkPredVisCnt = (prop, user, isVip, cb)->
  limit = if isVip then VIP_LIMIT else USER_LIMIT
  # BUG: this cb wont invoke if not logged in
  limiter.check user,'pred',limit,(u,evt,cnt,notify)->
    if notify
      console.log "#{evt} reached limit(#{cnt}). User:#{u._id}:#{u.eml}"
    prop.predOutrange = cnt > limit
    cb prop.predOutrange
DEF 'checkPredVisCnt', checkPredVisCnt

getTranslateRmpropDetailOpt = (opt)->
  translateRmPropDetailOpt =
    setupL10n:(a,b)->opt.setupL10n a,b
    _ab:(a,b)->opt._ab a,b
    l10n:(a,b)->opt.l10n a,b
    _:(a,b)->opt._ a,b
    # locale:opt.locale
    # headers:opt.headers
    # cookies:opt.cookies

# policy = getConfig('DDF_DISPLAY_POLICY')

# setUpPropFav = (user,prop)->
#   #TODO: favUsr may cause performance issue if has a lot favUser
#   prop.fav = false
#   prop.favGrp = []
#   prop.favcnt = prop.favU?.length or 0
#   if user and Array.isArray(prop.favU)
#     for tmp in prop.favU
#       #use user._id is tmp.u.toString() -> false
#       if (''+user._id).localeCompare(tmp.u.toString()) is 0
#         prop.fav = true
#         prop.favGrp.push tmp?.g
#   delete prop.favUsr
#   delete prop.favU
#   delete prop.rmlog
# DEF 'setUpPropFav',setUpPropFav

# Unique UserID shall across all platform
createUniqueListingID = (cb)->
  extendLength = (v)->
    if v < 10000
      v = "" + v
      fill = 5 - v.length
      for i in [1..fill]
        v = '0' + v
    return v + ""
  SysData.findOneAndUpdate {_id:'ListingUniqueID'},{$inc:{lastID:1}},{upsert:true,returnDocument:'after'},(err,r)->
    if err then return cb err,null
    if not r?.value?.lastID then return cb new Error "Can't get UserUniqueID record"
    cb null,"RM1-" + extendLength(r.value.lastID)
DEF 'createUniqueListingID',createUniqueListingID


#TODO: still using for projects
#TODO: remove this
# updateListingStatus = (d, cb)->
#   suffix = d.suffix
#   community = null
#   #faddr -> formated address, key in db
#   #suffix -> vc + translate suffix
#   q = {_id:d._id}
#   update = {
#     $inc:{
#       vc : 1
#       vcd : 1 # vc daily
#     }
#   }
#   update.$inc['vcweb'] = 1 if d.fromWeb
#   update.$inc['vc'+get_shared_to_from_share(suffix)] = 1 if suffix
#   if d.role in ['r','c']
#     update.$inc['vcapp'+d.role] = 1
#   props = Projects
#   if d.rmid
#     q = {id:d.rmid}
#     # TODO: after projects refactor
#     props = UserListings
#   if d.db
#     props = d.db
#   props.updateOne q, update, (err, ret)->
#     console.log err if err
#     cb err, ret if cb
#   null
# DEF 'updateListingStatus', updateListingStatus

checkIsValidQuery = (q)->
  return false unless q?.id
  true

isOwner = (prop,user)->
  uid = prop?.uid?.toString() or ''
  _id = user?._id?.toString() or 'no user'
  uid is _id

handleRMPropRequest = ({user,
  locale,id,isShare,
  isAllowedPropAdmin,
  isAllowedVipUser,
  simplfyPtp,
  l10n,
  _ab,
  devType,
  apisrc,
  transDDF,
  isRealGroup,
src},cb)->
  opt = arguments[0]
  rmid = id
  #hanle new website url
  error = {}
  unless rmid?.length
    return cb MSG_STRINGS.BAD_PARAMETER#'No id specified'
  # idArray = rmid.split('-')
  # rmid = idArray.slice(idArray.length-2).join('-')
  # rmid = rmid.split('.')[0]
  # opt.setupL10n() unless opt._ab
  try
    prop = await Properties.findRMOneByID rmid
  catch err
    debug.error err,'handleRMPropRequest'
    error.msg = MSG_STRINGS.DB_ERROR
    error.code = 0
    return cb(error)
  if (not prop)
    error.msg = 'Error: No listing found'
    error.code = 2
    error.url = '/1.5/prop/error?id='+rmid
    return cb(error)
  unless ((prop.market?.st is 'Promoted') or isShare)
    error.msg = 'Listing is no longer promoted'
    error.code = 2
    error.url = '/1.5/prop/error?id='+rmid
    return cb(error)
  unless 'app' is devType
    prop.noSEOIndex = prop.market?.st isnt 'Promoted'
  if prop.market?.cmstn?
    prop.cmstn = prop.market.cmstn
  if prop.market?.agrmntImg?
    prop.agrmntImg = prop.market.agrmntImg
  prop.isV = prop.market?.isV or false
    # prop.adok = 1
  unless (user?.id is prop.uid) #if user getting prop info
    delete prop['58']
    delete prop['market']
    delete prop['uhouzz']
  if (devType is 'app') and user
    UserModel.logRMListingHistory(user._id,rmid)
  Properties.incListingStats {ids:[rmid],isView:true,user,devType} #, faddr, suffix, prop.cmty
  prop = propSearch.convertRMListingsDispToTreb([prop])[0]
  prop.ptype = libproperties.getPtypeFull prop.ptype if prop.ptype
  prop.dom = libproperties.computePropDom(prop)
  # translateRmPropDetailOpt = getTranslateRmpropDetailOpt opt
  prop = propTranslate.translate_rmprop_detail({locale,transDDF}, prop) #simple_translate_rmprop(req,  prop)
  prop.canEditOhs = libUser.canEditOpenHouse({isAllowedPropAdmin,prop,user})
  prop.isOwner = isOwner(prop,user)
  Properties.addPropAdditionalFields({
    user,prop,
    isAllowedPropAdmin,
    isAllowedVipUser,
    # configShowSoldPrice,
    l10n,
    _ab,
    apisrc,
    isRealGroup,
    simplfyPtp})
  if imgUrls = prop.pic?.l
    prop.pic.l = libPropertyImage.getRMListingsImages {sid:prop.sid,imgUrls}
  try
    prop = await Properties.addPropFavFields {props:prop,detail:true,uid:user?._id}
    prop = await injectPropCityCmtyStats prop
  catch err
    debug.error err,'handleRMPropRequest'
  cb(null, prop)

genListingPicReplaceUrls = libproperties.genListingPicReplaceUrls

# NOTE:deprecated, use await Properties.getTranslatedPropDetailByID {
getPropDetails = ({
  suffix,
  isAllowedShowSoldPrice,
  isVipRealtor,
  isChinaIP,
  apisrc,
  locale,
  user,id,sid,nt,slp,isShare,
  devType,
  isPad,
  l10n,
  _ab,
  transDDF,
  fubSrc,
  isAllowedPropAdmin,
  isDevGroup,
  isRealGroup,
  },
cb)->
  opt = arguments[0]
  isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
  opt.isAllowedVipUser = isAllowedVipUser
  simplfyPtp = slp or false
  opt.simplfyPtp = simplfyPtp
  opt.devType = devType
  # console.log '+++++',opt
  error = {}
  
  unless (id or sid)
    error.msg = MSG_STRINGS.BAD_PARAMETER#'Not valid prop id'
    error.code = 2
    error.url = '/1.5/prop/error?id='+id
    return cb(error)
  # TODO: trans should be outside of this func
  # locale = opt.lang
  # console.log 'xxxxxxxxx locale:',locale
  # if locale and (locale in LANGUAGE_LIST)
  #   req.setLocale locale
  # req.setupL10n() unless req._ab and req._
  if /^RM/.test id
    return handleRMPropRequest(opt, cb)
  noTranslate = nt

  # UserModel.appAuth {req,resp}, (user) ->
  # or (params.fav is 1)
  # if getConfig('showSoldPrice') or req.isAllowed('vipUser')
  #   delete fields.sp
  excludes = Properties.genExcludes {isVipRealtor,isAllowedPropAdmin,isDevGroup,isRealGroup}
  Properties.findPropertyDetail {id,sid,excludes},(err,prop)->
    # Properties.findOne q, {fields:fields}, (err, prop)->
    if err
      debug.error err,'getPropDetails findPropertyDetail'
      error.msg = err
      error.code = 0
      return cb(error)

    # prop not found or prop.del = 1 and not admin
    if not prop or ((not UserModel.accessAllowed('admin',user)) and prop.del)
      error.code = 2
      error.msg = 'Not found'
      error.url = '/1.5/prop/error?id='+id
      return cb(error)

    if (devType is 'app') and user
      UserModel.logPropertiesHistory(user._id,prop._id or prop.sid )
    libproperties.setUpPropLogin(isShare, user, prop)
    # if (not isShare) and (not user) and needLoginToViewProp(req, DDF_DISPLAY_POLICY, prop)
    if (not fubSrc) and prop.login
      error.code = 1
      error.msg = 'According to Real Estate Board rules, you have to login to see this listing!'
      error.id = id
      delete prop.sp if prop.sp #hide sold price if not login
      # NOTE webDetail shows partial detail, blured, in app not showing at all
      if not opt.isWebDetail
        return cb(error)
      #   prop.login = true
      # else
      #   return cb(error)
    # setUpPropFav(user,prop)
    isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
    # configShowSoldPrice = getConfig('showSoldPrice')
    Properties.addPropAdditionalFields({
      user,prop,
      isAllowedPropAdmin,
      isAllowedVipUser,
      # configShowSoldPrice,
      l10n,
      _ab,
      apisrc,
      isRealGroup,
      simplfyPtp})
    prop.rms = libproperties.getRooms({rms,devType}) if rms = prop.rms
    # NOTE: dup, done in addPropAdditionalFields
    # prop.canEditOhs = libUser.canEditOpenHouse({isAllowedPropAdmin,prop,user})
    # TODO: isOwnCtrlContact deprecated
    prop.rmcontact = isOwnCtrlContact(prop)
    prop.p_ab = ProvAndCity.getProvAbbrName(prop.prov or prop.prov_en)
    Properties.incListingStats {
      ids:[prop._id],
      isView:true,
      user,
      suffix:get_shared_to_from_share(suffix),
      devType
    }
    # TODO: trans should be outside of this func
    # rmPropDetailOpt = getTranslateRmpropDetailOpt opt
    prop = propTranslate.translate_rmprop_detail({locale,transDDF}, prop) unless noTranslate
    genListingPicReplaceUrls(prop, isChinaIP, {use3rdPic:config.serverBase?.use3rdPic,isPad,shareHostNameCn:config.share?.hostNameCn})
    try
      prop = await Properties.addPropFavFields {props:prop,detail:true,uid:user?._id}
      prop = await injectPropCityCmtyStats prop
    catch err
      debug.error err,'getPropDetails'
    filterPrediction isAllowedShowSoldPrice,user,prop,->
      UserWatchModel.getWatchingStautsByProperty {uid:user?._id,prop},(err,watchingUsers)->
        debug.error err,'getPropDetails getWatchingStautsByProperty' if err
        prop.isWatching = false
        prop.isCmtyFav = false
        prop.cmtyWatch = {}
        prop.watching = {}
        if watchingUsers
          for watchingUser in watchingUsers
            watching = UserWatchModel.formatWatching watchingUser
            if watchingUser.tp is 'cmty'
              prop.isCmtyFav = true
              prop.cmtyWatch = watching
            else if watchingUser.uaddr is prop.uaddr
              prop.isWatching = true
              prop.watching = watching
        Properties.getRmStoreyInfo prop.uaddr, (err,rmStoreyInfo)->
          if err
            debug.error err
            return cb MSG_STRINGS.DB_ERROR
          if rmStoreyInfo
            for info in ['rmStoreys']
              prop[info] = rmStoreyInfo[info] if rmStoreyInfo[info]
          if prop.hideInfo
            return cb(null, prop)
          if length = prop.topuids?.length
            # ? TODO: may need bcc email
            propUid = prop.topuids[length-1]
            # userPublicInfoOpt = getUserPublicInfoOpt opt
            # TODO: fix getUserPublicInfo
            UserModel.findPublicInfo {lang:locale, id:propUid, noSas:1},(err, retu)->
              # debug.error err if err
              prop.tlAgent = retu
              cb(null, prop)
          else
            cb(null, prop)
DEF 'getPropDetails', getPropDetails

# TODO: deprecated, remove after not called,
getHiddenPrice = (pred_sp)->
  hiddenPrice = ''
  predPrice = pred_sp
  for i in [0..(predPrice.length-1)]
    c = predPrice[i]
    if c is '$' or c is ',' or i is 1
      hiddenPrice += c
    else
      hiddenPrice += '*'
  return hiddenPrice
DEF 'getHiddenPrice',getHiddenPrice

# TODO: deprecated, remove after not called, use libproperties.filterPredictionValues
filterPrediction = (isAllowedShowSoldPrice,user,prop,cb)->
  return cb() unless prop.pred_sp
  # prop.pred_accuracy = calcPredAccuracy prop
  prop.pred_spl = libproperties.currencyFormat((prop.pred_sp-prop.pred_spsd),'$',0)
  prop.pred_sph = libproperties.currencyFormat((prop.pred_sp+prop.pred_spsd),'$',0)
  prop.pred_sp = libproperties.currencyFormat(prop.pred_sp,'$',0)
  done = (mask)->
    if mask
      prop.pred_sp = getHiddenPrice prop.pred_sp
      prop.pred_spl = getHiddenPrice prop.pred_spl
      prop.pred_sph= getHiddenPrice prop.pred_sph
      # prop.pred_accuracy = '******'
      delete prop.pred_his
    cb()
  if user
    prop.showPredict = isAllowedShowSoldPrice
    return checkPredVisCnt prop,user,prop.showPredict,done
  done true
DEF 'filterPrediction',filterPrediction

#already have prop, find city stats for last month
injectPropCityCmtyStats = (prop)->
  throw new Error 'No city and Prov' unless prop.city and prop.prov
  ret = await PropStatsModel.findPropCityCmty prop
  prop = statHelper.injectCmtyStatResultToProp({ret,prop})
  return prop


# logUserPropHist = (user, ml_num)->
#   update = {
#     $push:{},
#     $set:{pnEdmMt:new Date()}
#   }
#   histObj =
#     id: ml_num
#     ts: new Date()
#   update.$push.prophist =
#     { $each: [histObj], $slice: -150 }
#   # TODO: $pull? prophist: {$elemMatch: {ml_num:ml_num}}
#   UserProfile.updateOne {_id:user._id }, update, {upsert: true},(err)->
#     console.log err.toString() if err
#   return
# DEF 'logUserPropHist',logUserPropHist
