###
ALTER TABLE `vow`.`properties`
CHANGE COLUMN `sqft` `sqft1` INT(11) NULL DEFAULT NULL COMMENT 'Square Footage: 1-499:1 soft:2\n500-599(599.5)...,3500-5000(5000.5),5000+(sqft2: null), or sqft = sqf2\n' ,
CHANGE COLUMN `age` `age1` INT(11) NULL DEFAULT NULL COMMENT '0:new, 0-5: age2 = 5, 100+: age2 = NULL or exact building year age = age2' ;
ALTER TABLE `vow`.`properties`
ADD COLUMN `sqft` VARCHAR(32) NULL AFTER `fce`,
ADD COLUMN `age` VARCHAR(32) NULL AFTER `sqft2`;

###


VIEW 'listing-share-desc',->
  isntApp = @req.getDevType() isnt 'app'
  span id:'share-title-en', ->
    span ngIf:"prop.tl",->
      text "{{prop.tl}}"
    span ngIf:"!prop.tl",->
      text "RealMaster " if isntApp
      text "{{prop.lp | currency:\'$\':0}}, {{prop.addr}} {{prop.unt}}, {{prop.city_en || prop.city}} {{prop.prov_en || prop.prov}} "
  span id:'share-desc-en',  ->
    text "{{ isArray(prop.ptp) ? prop.ptp[0] : prop.ptp_en }} {{prop.pstyl_en}} " #Type:
    text "{{prop.sqft?', '+prop.sqft+' Sqft, ':''}} "
    span ngIf:"prop.bcf != 'b'", ->
      text "Bedroom: {{prop.rmbdrm || prop.tbdrms || prop.bdrms}}, Kitchen: {{prop.kch}}, Bathroom: {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}, "
    text "{{prop.m?(prop.m | limitTo:70):''}}{{prop.m.length > 70 ? '...' : ''}}"

  span id:'share-title',->
    span ngIf:"prop.tl",->
      text "{{prop.tl}}"
    span ngIf:"!prop.tl",->
      text _("RealMaster") + ' ' if isntApp
      text "{{ prop.lp | currency:\'$\':0}}, {{prop.addr}} {{prop.unt}}, {{prop.city_zh || prop.city}} {{prop.prov_zh || prop.prov}}"
  span id:'share-desc',     ->
    text "{{ isArray(prop.ptp) ? prop.ptp[3] : prop.ptp + ' ' + prop.pstyl }} " ##{_('Type','property')}:
    text "{{prop.sqft?', '+prop.sqft+' '+'#{_('Sqf','property')}, ':''}} "
    span ngIf:"prop.bcf != 'b'", ->
      text "#{_('Bedroom')}: {{prop.rmbdrm || prop.tbdrms || prop.bdrms}}, #{_('Kitchen')}: {{prop.kch}}, #{_('Bathroom')}: {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}, "
    text "{{prop.m?(prop.m | limitTo:70):''}}{{prop.m.length > 70 ? '...' : ''}}"
