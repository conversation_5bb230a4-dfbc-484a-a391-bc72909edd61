config = CONFIG(['share'])
debug = DEBUG()
Properties = MODEL 'Properties'
UserModel = MODEL 'User'
PropFavouriteModel = MODEL 'PropFavourite'
GroupModel = MODEL 'Group'
{respError} = INCLUDE 'libapp.responseHelper'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
{setPropWebUrl} = INCLUDE 'libapp.propWeb'
libP = INCLUDE 'libapp.properties'
APP '1.5'
APP 'propFav',true

# 查找包含指定房源的最新更新的收藏夹名字
# @param {Object} favMap - 用户收藏夹
# @param {Array} group - 当前用户包含指定房源的收藏夹
# @return {Object} {currentGrp,grp,grps} - {包含指定房源的最新更新收藏夹; 收藏夹的编号; 用户收藏夹信息}
findLatestFolder = (favMap,group) ->
  # 收藏的文件夹按照更新时间倒序 [ '0', '2', '3' ]
  favMapArr = Object.keys(favMap).sort((a, b)->
    return Date.parse(favMap[b].mt) - Date.parse(favMap[a].mt)
  )
  currentGrp = {}
  grp = 0
  grps = []
  # 查找包含当前房源的最新更新的文件夹
  for item in favMapArr
    index = group.findIndex((el)->return el.g.toString() is item)
    if index >=0
      currentGrp = {idx:item,mt:favMap[item].mt,val:favMap[item]}
      grp = parseInt(item)
      break
  unless Object.keys(currentGrp).length
    return false
  for item in favMapArr
    grps.push({idx:item,mt:favMap[item]['mt'],val:favMap[item]})
  return {currentGrp,grp,grps}

# /1.5/propFav/getGrpAndFavProps
# 查找指定房源被客户收藏的最新的收藏文件夹及文件夹里的其余房源信息
# @param {String} id - 房源的_id
# @return {Object} {items,grps,currentGrp} - {包含指定房源最新更新的收藏夹收藏的房源信息; 用户收藏夹信息; 当前收藏夹信息}
POST 'getGrpAndFavProps', (req, resp)->
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    try
      isRealGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    if (not req.isAllowed 'devGroup') and (not isRealGroup)
      debug.error {msg:"propFav/getGrpAndFavProps:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      group = await PropFavouriteModel.findGroupByIdAndUid(user._id,req.body.id)
      unless group.length # 用户没有收藏当前房源
        resp.send {ok:0,msg:MSG_STRINGS.NOT_FOUND}
      favMapObj = await PropFavouriteModel.findFavoriteMap(user._id)
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    unless favMapObj?.favMap
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),category:MSG_STRINGS.BAD_PARAMETER,resp}
    favMap = favMapObj.favMap
    delete favMap.cntr if favMap.cntr
    folderInfo = findLatestFolder(favMap,group)
    unless folderInfo
      return resp.send {ok:0,msg:MSG_STRINGS.NOT_FOUND}
    {currentGrp,grp,grps} = folderInfo
    isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
    isAllowedPropAdmin = UserModel.accessAllowed('propAdmin',user)
    try
      result = await Properties.findUserFavProps {user,limit:50,grp,isAllowedVipUser,isAllowedPropAdmin}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    unless result
      resp.send {ok:1, items:[], grps,currentGrp}
    {ret,count} = result
    ret = propTranslate.translate_rmprop_list(req, ret)
    libP.thumbUrlListReplace(ret, req.isChinaIP(), config.share?.hostNameCn)
    for p in ret
      Properties.addPropAdditionalFields({
        user,prop:p,
        isAllowedPropAdmin,
        isAllowedVipUser,
        l10n:(a,b)->req.l10n(a,b),
        _ab:(a,b)->req._ab(a,b),
        apisrc:req.param('apisrc')
        isRealGroup,
        simplfyPtp:false})
      setPropWebUrl(req,p)
      Properties.deletePropPriviteFields({user,prop:p})
    resp.send {ok:1, items:ret, grps,currentGrp}

# /1.5/propFav/isFavedProp
# 判断该房源是否被客户收藏
POST 'isFavedProp', (req, resp)->
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    try
      group = await PropFavouriteModel.findGroupByIdAndUid(user._id,req.body.id)
    catch err
      debug.error err
      return resp.send {ok:1, fav:false}
    unless group.length # 用户没有收藏当前房源
      return resp.send {ok:1, fav:false}
    favGrp = []
    for item in group
      favGrp.push(item.g)
    return resp.send {ok:1, fav:true,favGrp}

POST 'archive', (req, resp)->
  unless grp = req.body?.grp
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    flag = req.body?.flag
    try
      msg = await PropFavouriteModel.archiveFolder {uid:user._id,grp,flag}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    return resp.send {ok:1, msg:req.l10n(msg)}
