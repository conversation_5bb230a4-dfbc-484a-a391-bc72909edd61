config = CONFIG(['share'])
helpersStr = INCLUDE 'lib.helpers_string'
checksum = INCLUDE 'lib.checksum'
statHelper = INCLUDE 'libapp.stat_helper'
libproperties = INCLUDE 'libapp.properties'
libUser = INCLUDE 'libapp.user'
objectCache = INCLUDE 'libapp.objectCache'
debug = DEBUG()
Properties = MODEL 'Properties'
Bookkeeping = COLLECTION 'chome', 'bookkeeping'

{ replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
{listingPicUrlReplace} = INCLUDE 'libapp.common'
{saletpIsSale} = INCLUDE 'libapp.propertiesTranslate'
GroupModel = MODEL 'Group'
ShowingModel = MODEL 'Showing'
getWXConfig = DEF 'getWXConfig'
UserModel = MODEL 'User'
dataMethods = DEF 'dataMethods'
removePopCityCacheByProvAndCity = DEF 'removePopCityCacheByProvAndCity'
MAP_PROP_FIELDS = DEF 'MAP_PROP_FIELDS'
getPropDetails = DEF 'getPropDetails'
process_listingList_req = DEF 'process_listingList_req'
{respError} = INCLUDE 'libapp.responseHelper'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
# get_ad_user_from_prop = DEF 'get_ad_user_from_prop'
# User = COLLECTION 'chome', 'user'
# conf = CONFIG()
# getExchangeRates = DEF 'getExchangeRates'
# getExchangeNames = DEF 'getExchangeNames'
# l10nDetail = DEF 'l10nDetail'
# setLang = DEF 'setLang'
# geo_coder = INCLUDE 'lib.geo_coder'
UserOpLogs = MODEL 'UserOpLogs'
IN_REAL_GROUP_NAME = ':inReal'
GeoCoder = MODEL 'GeoCoder'
EstimatedModel = MODEL 'Estimated'
###
getPropFav = (user,ml_num,cb)->
  if not user?._id
    return cb null
  UserProfile.findOne {_id:user._id,"favPrps.#{ml_num}":$exists:true},(err,uProfile)->
    cb err,uProfile?
DEF 'getPropFav',getPropFav


process_listing_jdoc = (req, prop)->
  ret = {}
  try
    for k,v of prop
      if k not in ['jdoc','jdoc2']
        ret[k] = v
    jdoc  = JSON.parse prop.jdoc
    jdoc2 = JSON.parse prop.jdoc2
    for k,v of jdoc
      unless ret.hasOwnProperty k
        ret[k] = v
    for i in ['slddt', 'm_zh'] # 'sp','lst' not show at first, click to show
      ret[i] = jdoc2?[i]
  catch err
    console.error err.toString()
  return ret
DEF 'process_listing_jdoc',process_listing_jdoc
###
sendError = (req,resp,e)->
  console.error req.body
  console.error e
  resp.send  {err:e.toString(), success:false}

dataMethods.showedTerm = (req, user, cb) ->
  showedTerm = req.body?.showedTerm or false
  return cb(null, showedTerm) unless user
  devType = req.getDevType()
  UserModel.updateUserShowedTerm user._id,{showedTerm,devType},(ret)->
    return cb(null,ret)


dataMethods.ownerData = (req, user, cb) ->
  ownerId = req.body.ownerId
  # return cb('No oid specified!',null) unless ownerId
  return cb(null, {}) unless ownerId
  UserModel.findPublicInfo {lang:req.locale(), id:ownerId},(err, owner)->
    cb err,owner

dataMethods.wxConfig = (req, user, cb)->
  unless req.isWeChat()
    return cb null, {}
  unless reqUrl = req.body.url
    return cb 'No Url!'
  getWXConfig req,reqUrl,(err,wxcfg)->
    if /d\d.realmaster/.test req.hostname
      wxcfg?.debug = true
    return cb err, wxcfg

dataMethods.sessionUser = (req, user, cb)->
  UserModel.findPublicInfo {lang:req.locale(),user}, cb

decEncrypedCode = (ec)->
  secret = config.share?.secret
  [pass, message] = checksum.shareDecode(ec,secret)
  return false unless pass
  try
    message = JSON.parse message
  catch e
    console.log 'Invalid Data'
    return false
  return message

genShareTitle = (opt,prop={})->
  str = "#{opt.l10n('RealMaster')} "
  unless prop._id
    return str
  if prop.ltp is 'assignment'
    str += " #{opt.l10n('Assignment')} • "
  else if prop.ltp is 'exlisting'
    str += " #{opt.l10n('Exclusive','realtor sale')} • "
  else if prop.ltp is 'rent' and not prop.cmstn
    str += " #{opt.l10n('Landlord Rent')} • "
  else if prop.ltp is 'rent' and prop.cmstn
    str += " #{opt.l10n('Rent','share-title rent')} • "
  str += ' '+helpersStr.currency (prop.lp or prop.lpr),'$',0
  str += ' • '
  if prop.addr
    str += " #{prop.addr} #{prop.unt || ''}"
  str += " #{prop.city} #{prop.prov}"
  return str

# @redirectInapp: redirect to /inapp if open this link in app
process_listing_req = ({req,resp,message={},user,redirectInapp})->
  # user = req.user
  opt = libproperties.getBasePropDetailOpt req,user
  opt.id = req.param('id')
  opt.ec = req.param('ec')
  if sid = req.param('sid')
    opt.sid = sid
  # error = (e)-> sendError req,opt.resp,e
  #ec is encrypted datasting
  if id = message?.r
    opt.id = message.r
  lang = (req.param('lang') or req.param('locale'))
  opt.useThumb = true # for share image
  # if lang and (lang in LANGUAGE_LIST)
  #   req.setLocale lang
  # console.log '++++++lang: ',lang,req.param('lang'),req.param('locale'),req.headers
  # TODO: decData, pass propDetial
  if opt.ec
    msg = decEncrypedCode(opt.ec)
    if msg is false
      return resp.ckup 'generalError',{err:'Not valide code'}
    # if msg.r.indexOf(',') > 0
    #   console.error 'id is array of ids, should not be here'
    opt.id = msg.r
  # console.log '+++++++',opt.ec,opt.id
  if not (opt.id)?
    return resp.ckup 'generalError',{err:'No ID specified'}
  if opt.id.indexOf(',') > 0
    message.r ?= opt.id
    return process_listingList_req {req,resp,message,user}
  if suffix = message?.h or msg?.h
    if (req.getDevType() isnt 'app') and suffix is 'A'
      suffix = req.getDevType()
    # console.log 'xxxxxxxx',suffix,message,msg
    opt.suffix = suffix
  opt.transDDF = req.getConfig('transDDF')
  # console.log '+++++',req.getDevType(),opt,redirectInapp
  # open shared link inapp, from universal link
  if (req.getDevType() is 'app') and  redirectInapp
    # TODO: redirect to app server
    # url:"/1.5/prop/detail/inapp?lang=#{locale}&d=/1.5/index&id=#{prop._id}&src=pn"
    return resp.redirect req.appHostUrl()+"/1.5/prop/detail/inapp?id=#{opt.id}&lang=#{lang}&src=pn&mode=list"
    # return resp.ckup 'propDetailPageInapp',{id:opt.id},'_',{
    #   noUserModal:1,wxcfg:{},hasPageCss:1,noAppCss:1,noRatchet:1,
    #   nosmbcss:1,noFontAwesome:1,noRatchetJs:1,noAngular:1
    # }
  projection = Properties.genExcludes opt

  try
    opt.isRealGroup = await GroupModel.isInGroup {uid:user?._id, groupName:IN_REAL_GROUP_NAME}
  catch err
    debug.error 'GroupModel.isInGroup',err,' uid:',user?._id

  try
    result = await Properties.getTranslatedPropDetailByID {
      id:opt.id,
      fromStr:req.getDevType(),
      trans:req.locale(), #req.param local or lang?
      disKeyOnly:true,
      additionalConfig:opt,
      projection,
      user
    }
  catch err
    debug.error err
    return resp.ckup 'generalError',{err_tran:req.l10n(MSG_STRINGS.DB_ERROR)}
  # getPropDetails opt,(err,prop)->
  #   if err
  #     return resp.ckup 'generalError',{err_tran:req.l10n(err.msg)}
  # NOTE: no prop handleed in getPropDetails
  prop = result.detail
  unless prop
    return resp.ckup 'generalError',{err:req.l10n('Property not found')}
  title = genShareTitle(req,prop)
  shareImage = prop.thumbUrl or '/img/noPic.png'
  # prop 匹配disclaimer信息
  libproperties.setupDisclaimer prop,result.dis
  # 获取估价数据，但是因为是分享出去的不展示，显示View
  if libproperties.isMlsPrefix(prop._id) and saletpIsSale(prop.saletp_en or prop.saletp)
    try
      estimateData = await EstimatedModel.getAllEstimateInfo(prop._id, {rent_mn1: 1})
    catch err
      debug.error 'Error: getAllEstimateInfo',err,' propId:',prop._id
    prop.estRent = Number(estimateData.rent_mn1) if estimateData?.rent_mn1?
  aid = req.param('aid')
  wDl = req.param('wDl')
  cfg1 = {title, prop, shareImage, aid, lang, wDl}
  # console.log '+++++',cfg1
  cfg2 = {noUserModal:1,wxcfg:{},noRatchetJs:1,noAngular:1,noCordova:1,shareImage,noref:1}
  return resp.ckup 'propDetailPageNew',cfg1,'_',cfg2

DEF 'process_listing_req',process_listing_req

VIEW 'propDetailPageNew',->
  # js '/js/ratchet/segmented-controllers.js'
  text """<div id='vueBody'>
  <prop-detail-page></prop-detail-page>
  </div>
  """
  coffeejs {vars:{prop:@prop,aid:@aid,wDl:@wDl}},->
    null
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  css '/css/swiper.min.css'
  js '/js/calculate.min.js'
  # js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  # js '/js/Chart-2.5.0.min.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/propDetailPage.js'

VIEW 'propDetailPageInapp',->
  # js '/js/ratchet/segmented-controllers.js'
  if @id
    coffeejs {vars:{id:@id}},->
      null
  div id:'vueBody',->
    text """
    <app-prop-detail-page></app-prop-detail-page>
    """
  # coffeejs {vars:{id:@id}},->
  #   null
  # css '/css/sprite.min.css'
  # css '/css/svgsprite.min.css'
  css '/css/swiper.min.css'
  js '/js/calculate.min.js'
  # js '/js/lz-string.min.js'
  js '/js/entry/commons.js'
  js '/js/stdfn.min.js'
  # js '/js/Chart-2.5.0.min.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/appPropDetailPage.js'
  # js '/js/html2canvas.min.js'

# '/1.5/prop/detail/:id'
# this page is accessed in side of App, will desplay property detail in app, need auth_auth
APP '1.5'

APP 'prop',true

# /1.5/prop/manage
# TODO: refactor control
# TODO: add test
POST 'manage', (req, resp)->
  error = (err) ->
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp}, (user)->
    return error('Need login') unless user
    return error('Need id') unless id = req.param '_id'
    yturl = req.param('yturl')
    ytvid = req.param('ytvid')
    vurlcn = req.param('vurlcn')
    vimgcn = req.param('vimgcn')
    clearVid = req.param('clearVid')
    removeTop = req.param('removeTop')
    deleteProp = req.param 'delete'
    sid = req.param 'sid'
    hide = req.param 'hide'
    src = req.param 'src'
    isAllowedPropAdmin = req.isAllowed('propAdmin')
    isAdmin = req.isAllowed('admin')
    try
      isInGroup = await GroupModel.isInGroup {uid:user._id, groupName:IN_REAL_GROUP_NAME}
    catch err
      debug.error 'GroupModel.isInGroup',err
    # Remove topup listing's topup
    if deleteProp
      unless isAllowedPropAdmin
        return error(MSG_STRINGS.NO_AUTHED)
      # admin can del from db, propAmin can only hide
      if not isAdmin
        hide ?= true
      return Properties.removeListing {id,user,hide},(err,ret)->
        if err
          return error(err)
        return resp.send {ok:1,id:id,msg:ret}
    if removeTop
      unless isAllowedPropAdmin
        return error(MSG_STRINGS.NO_AUTHED)
      return Properties.removeTopListing {id,user},(err,ret)->
        if err
          return error(err)
        return resp.send {ok:1,removed:id}
    # add video link to property, disp a video icon
    if ytvid or vurlcn or clearVid
      unless isAllowedPropAdmin or isInGroup
        return error(MSG_STRINGS.NO_AUTHED)
      return Properties.updatePropVideoLink {id,vurlcn,ytvid,vimgcn,yturl,clearVid},(err,ret)->
        if err
          debug.error err
          return resp.send {ok:0, err: MSG_STRINGS.DB_ERROR}
        return resp.send ret
    ohs = req.param('ohs')
    ohvs = req.param('ohvs')
    if ohs or ohvs
      # NOTE: could be [], remove all ohs
      unless Array.isArray ohs
        return error('oh needs to be array')
      act = req.param('act') or 'add'
      act = act.toUpperCase() + '_OH'
      # add ohs to prop
      Properties.updateOhs {isInGroup,id,ohs,ohvs,isAllowedPropAdmin,user},(err,prop)->
        if err
          debug.error err
          return resp.send {ok:0,err:MSG_STRINGS.DB_ERROR}
        removePopCityCacheByProvAndCity(prop.prov,prop.city)
        UserOpLogs.log {user,act:UserOpLogs.ACTION[act],op:{ohs,ohvs},target:id}
        resp.send {ok:1}
      return
    useTrebPhoto = req.param('useTrebPhoto')
    rmPho = req.param('rmPho')
    # set prop to force use treb prop
    if useTrebPhoto?
      Properties.setUseTrebPhoto {id,useTrebPhoto},(err,ret)->
        if err
          console.error err
          return resp.send {ok:0, err:err}
        resp.send {ok:1,ret:ret}
      return
    # set flag refreshPic
    if refreshPic= req.param('refreshPic')?
      unless req.isAllowed('brokerage') or isAdmin
        return error(MSG_STRINGS.NO_AUTHED)
      return Properties.refreshPic {id,src,sid},(err,ret,updateQuery)->
        if err
          console.error err
          return error(err.toString())
        resp.send {ok:1, updateQuery, n:ret}
    if rmPho? # update photo number
      return error(MSG_STRINGS.NO_AUTHED) unless isAdmin
      return Properties.updatePictureNum {id,rmPho,src},(err,ret,updateQuery)->
        if err
          console.error err
          return error(err.toString())
        resp.send {ok:1, updateQuery, n:ret}
    if maxPhoDownload = req.param('maxPhoDownload')
      return error(MSG_STRINGS.NO_AUTHED) unless isAdmin
      sid = req.param('sid')
      return Properties.setMaxPhotoDownload {sid,maxPhoDownload,src},(err,ret,updateQuery)->
        if err
          console.error err
          return error(err.toString())
        resp.send {ok:1, updateQuery, n:ret}

    if isAllowedPropAdmin
      keys = ['priceChange','status','daddr','rmdpho','dpred','useMlatlng','tlBcc','tgr','park_spcs','hideSqft','hideBuiltYr','rmSqft','rmBltYr','hideEstVal']
    else if isInGroup
      keys = ['rmSqft','rmBltYr','hideEstVal']
    else
      return error(MSG_STRINGS.NO_AUTHED)
    cfg = {keys,user,id,editData:{}}
    log = {user,target:id,op:{}}
    for i in keys
      continue if (req.param i) is undefined
      cfg.editData[i] = req.param i
      if i in ['tgr','park_spcs'] and cfg.editData[i]?
        cfg.editData[i] = parseInt(cfg.editData[i])
      if i in ['hideSqft','hideBuiltYr','hideEstVal']
        log.op[i] = cfg.editData[i]
        if cfg.editData[i] is true
          act = 'HIDE_'
        else
          act = 'SHOW_'
        log.act = UserOpLogs.ACTION[act+i.slice(4).toUpperCase()]
    return error('Need status') unless Object.keys(cfg.editData).length
    if log.act
      UserOpLogs.log log
    Properties.updatePropStatus cfg,(err,ret)->
      if err
        console.error err
        return error(err)
      resp.send {ok:1,set:ret}

# 1.5/prop/manage
GET 'manage',(req,resp)->
  id = req.param 'id'
  error = (err)->
    resp.ckup 'generalError', {err_tran:req.l10n(err)}
  UserModel.appAuth {req,resp}, (user)->
    return error('Not loggedIn') unless user
    Properties.findOneByID id, {}, (err,prop)->
      return error('Prop Not Found') unless prop
      isAllowedPropAdmin = req.isAllowed('propAdmin')
      GroupModel.isInGroup {uid:user._id,groupName:IN_REAL_GROUP_NAME},(err,isInGroup)->
        debug.error 'GroupModel.isInGroup',err if err
        unless libUser.canEditOpenHouse({isAllowedPropAdmin,isInGroup,prop,user})
          return error('Not Authed')
        resp.ckup 'propManagePage',{id:id}

VIEW 'propManagePage',->
  # js '/js/ratchet/segmented-controllers.js'
  div id:'vueBody',->
    text """<app-prop-manage></app-prop-manage>"""
  # coffeejs {},->
  #   null
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  js '/js/entry/commons.js'
  js '/js/entry/appPropManage.js'

GET 'error', (req, resp)->
  ml_num = req.param('id') or ''
  cfg = {}
  cfg.hasU = 1
  cfg.avt  = "/img/icon_Alert.png"
  cfg.nm   = 'RealMaster'
  cfg.cardUrl  = req.fullUrl('/getapp') #app-download')
  cfg.noWeCard = 1
  cfg.err = 1
  cfg.noLoading = 1
  if (req.getDevType() is 'app')
    cfg.noGetApp = 1
  cfg.extraInfo = ml_num
  cfg.shareInfoText = req.l10n "Can't find property. It could be sold, leased, expired or terminated. Please ask your realtor for detail."
  return resp.ckup 'NewsMiddleAdPageEn', cfg, 'adPage', {jump:0}

# setFav = (user,obj,fav,cb)->
#   if not cb?
#     cb = fav
#     fav = true
#   if not user?._id
#     return cb "Need login"
#   if fav
#     fn = UserModel.addFavorite
#   else
#     fn = UserModel.deleteFavorite
#   fn user._id,{obj,tp:'props'},(err)->
#     cb err

isRMlisting = (id) ->
  return id.substr(0,2) is 'RM'

GET 'editremarks',(req,resp)->
  UserModel.appAuth {req,resp}, (user) ->
    try
      isInGroup = await GroupModel.isInGroup {uid:user._id, groupName:IN_REAL_GROUP_NAME}
    catch err
      debug.error 'GroupModel.isInGroup',err
    unless req.isAllowed('vipRealtor') or isInGroup
      return resp.ckup 'generalError',{err:"Not Authorized"}
    unless id = req.param 'id'
      return resp.ckup 'generalError',{err:"No Id"}
    Properties.findOneByID id, {projection:{_id:1,m:1,m_zh:1}}, (err,prop)->
      if err or (not prop)
        return resp.ckup 'generalError',{err:err or 'Not found'}
      # console.log '+++++here'
      return resp.ckup 'editremarksView',{m:prop.m,m_zh:prop.m_zh,id:id},'_',{noUserModal:1,noAppCss:0,noRatchetJs:1,noAngular:1}

GET 'editloc',(req,resp)->
  UserModel.appAuth {req,resp}, (user) ->
    unless id = req.param 'id'
      return resp.ckup 'generalError',{err:'No Id'}
    if isRMlisting(id)
      return resp.redirect '/1.5/index'
    unless (req.isAllowed('devGroup') or req.isAllowed('geoAdmin'))
      return resp.ckup 'generalError',{err:MSG_STRINGS.NOT_AUTHORIZED}
    Properties.findOneByID id, {projection:MAP_PROP_FIELDS}, (err,prop)->
      if err or (not prop)
        return resp.ckup 'generalError',{err:err or MSG_STRINGS.NOT_FOUND}
      return resp.ckup 'editLocView',{prop:prop,id:id},'_',{noUserModal:1,noAppCss:0,noRatchetJs:1,noAngular:1}

# 1, update prop_parts/properties/geo_cache
# 2. log user operation
POST 'manageLoc',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp}, (user) ->
    unless (req.isAllowed('propManage') or req.isAllowed('geoAdmin'))
      return error MSG_STRINGS.NOT_AUTHORIZED
    unless id = req.param 'id'
      return error 'No Id'
    unless (lat = req.param('lat')) and (lng = req.param('lng'))
      return error 'No lat lng'
    lat = parseFloat lat
    lng = helpersStr.formatLng lng
    opt = {lat,lng,user,id}
    for i in ['cnty','prov','city','zip','st','st_num','st_sfx']
      opt[i] = req.param i
    Properties.updateLocation opt,(err,ret)->
      if err
        console.error err
        return error err
      return resp.send ret

VIEW 'editLocView',->
  div id:'vueBody',->
    text """<app-prop-loc></app-prop-loc>"""
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  js '/js/entry/commons.js'
  js '/js/entry/appPropLoc.js'


VIEW 'editremarksView',->
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  js '/js/jquery-1.10.1.min.js'
  text '''<style>
  .content{
    background-color: rgb(230, 230, 230);
  }
  .remarksWrapper{
    color:#666;
    font-size:14px;
    padding:10px;
    margin-bottom:5px;
  }
  #textareaVals{
    padding: 10px;
  }
  </style>'''
  # header class: 'bar bar-nav', style:"background: #e03131; padding-right:0px;", ->
  #   a class: 'icon fa fa-back pull-left', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
  #   h1 class: 'title', -> text _("RealMaster")
  div class: 'content', ->
    div class:'remarksWrapper',->
      text @m
    div class:'editor',->
      textarea id:'textareaVals',name:'message', rows:'10',->
        text @m_zh
    div id:'saveBtn', class:'btn btn-block btn-long btn-positive',->
      text _ 'Save'
  coffeejs {vars:{id:@id}},->
    $ ->
      $('#saveBtn').on 'click',(e)->
        $elem = $(e.target)
        data = {id:vars.id,m_zh:$('#textareaVals').val()}
        #alert JSON.stringify data
        # console.log data
        $.post '/1.5/prop/update',data,(ret)->
          RMSrv.dialogAlert(ret.msg or ret.err)
    null

#/1.5/prop/update
POST 'update', (req, resp) ->
  UserModel.appAuth {req,resp}, (user) ->
    error = (err) ->
      resp.send {ok:0, err:err}
    return error('Need login') unless user
    id = req.param 'id'
    return error('No id') unless id
    # NOTE: used to get sp from 3rd party website <- getPageContent
    log = {user,target:id,op:{},act:UserOpLogs.ACTION.EDIT_MEMO}
    flds = ['m_zh','src']
    vals = {propId:id}
    for i in flds
      if v = req.param i
        vals[i] = v
        log.op[i]=v
    try
      ret = await Properties.updateMZh vals
    catch err
      console.error err
      return resp.send {ok:0, err:err}
    UserOpLogs.log log
    resp.send {ok:1, msg:req.l10n(ret)}

# NOTE: [propdetail.vue] /1.5/prop/price, not used
# try get price, and send according msg
# POST 'price', (req, resp) ->
#   UserModel.appAuth {req,resp}, (user) ->
#     error = (err, text, href) ->
#       resp.send {ok:0, err:err, text:text, href:href}
#     return error(req.l10n('Please Login')) unless user
#     isRealtor = req.hasRole 'realtor'
#     isSpUser = (not isRealtor) and user?.flwng
#     isFavMode = req.param 'fav'
#     #NOTE: backdoor for faved users
#     if (not isFavMode) and (not req.isAllowed('showSoldPrice'))
#       #return error(req.l10n('Please contact your realtor'), req.l10n('Contact'),'/1.5/index') if isSpUser
#       #return error(req.l10n('Please follow a realtor'), req.l10n('Follow'),'/1.5/user/follow') if not isRealtor
#       return error(req.l10n('Vip Realtors only, Become a VIP today?'), req.l10n('Contact Us'),'#userModal') if isRealtor and not req.isAllowed('vipRealtor')
#       #return error(req.l10n('Restricted Access.'),req.l10n('Contact Us'),'#userModal')
#     id = req.param 'id'
#     return error('No id') unless id
#     flds = ['sp', 'slddt', 'lst', 'm_zh']
#     ret = {ok:1}
#     updateRet = (prop)->
#       return unless prop
#       for i in flds
#         ret[i] = prop[i] if prop[i]
#     if isRMlisting(id)
#       UserListings.findOne {id:id}, (err,prop)->
#         return error(err.toString()) if err
#         return resp.send ret unless prop #not found
#         updateRet(prop)
#         return resp.send ret
#     else
#       Properties.findOne {_id:id}, (err,prop)->
#         if err then return error err
#         updateRet(prop) if prop
#         return resp.send ret

POST 'pretranslate',(req,resp)->
  error = (err) ->
    return resp.send {ok:0, err:err}
  return error('no id') unless id = req.param 'id'
  return error('no m') unless m = req.param 'm'
  Properties.getPropMbyID {id:id},(err,ret)->
    if err
      console.error err
      return resp.send {ok:0,err:err}
    resp.send ret


#/1.5/prop/agreementVerify
POST 'agreementVerify', (req, resp) ->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'agreementAdmin'
    id = req.param 'id'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
    verify = req.param 'verify'
    try
      {msg,err} = await Properties.agreementVerify {id,verify}
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1, msg:req.l10n(msg)}
    catch err
      return respError {clientMsg:err, resp}

#/1.5/prop/adminRevoke
POST 'adminRevoke', (req, resp) ->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'agreementAdmin'
    id = req.param 'id'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
    reason = req.param 'reason'
    Properties.revokeListingMarket {to:'market',cmstn:'',id:id},(err,ret)->
      #TODO: should return err as sysErr, do not ignore.
      return respError {clientMsg:err, resp} if err
      Properties.addAdminRevokeReason {id,reason},(err,msg)->
        return respError {clientMsg:err, resp} if err
        resp.send {ok:1, msg}

#/1.5/prop/hideListing 隐藏/显示楼花转让房源
POST 'hideListing', (req, resp) ->
  if (not req.body?.id) and (not req.body?.hide)
    return respError {category:MSG_STRINGS.BAD_PARAMETER,clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'assignAdmin'
      debug.error {msg:"hideListing:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await Properties.hideAssignListing req.body.id,req.body.hide
    catch err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    if req.body?.marketRmProp
      objectCache.clearObjectCacheList 'trustedAssigm'
    return resp.send {ok:result.ok, msg:req.l10n(result.msg)}

# POST 'fav',(req,resp)->
#   UserModel.appAuth {req,resp},(user)->
#     error = (e)-> sendError req,resp,e
#     return resp.send {needLogin:true,err:req.l10n("Need login")} if not user?
#     return error "Bad Parameter" unless ml_num = req.param('id')
#     fav = not (req.param('act') is 'rm')
#     msg = if fav then req.l10n('Favoured') else req.l10n('Unfavoured')
#     obj = {id:ml_num,addr:req.param('addr'),lp:req.param('lp')}
#     setFav user,obj,fav,(err)->
#       if err
#         return resp.send {needLogin:true,err:req.l10n("Need login")} if err is "Need login"
#         return error err
#       resp.send {ok:1, msg:msg}

#/1.5/prop/getUserSavedLog
POST 'getUserSavedLog',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    id = req.param 'id'
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
    try
      logs = await UserModel.getDetailSavedLog {uid:user._id,id:id,l10n:(a,b)->req.l10n a,b}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
    resp.send {ok:1,logs}

ADDSTDFN 'getGroupAddShowingLog',{needReq:true,userObject:1},(opt,cb)->
  getGroupAddShowingLog opt,(err,users)->
    cb(err,users)

###
获取inreal group成员曾经对相同地址或id的房源添加到showing的情况
@param id prop id
uaddr和id必须至少存在一个
###
getGroupAddShowingLog = (opt,cb)->
  {id,uaddr,unit,user} = opt
  unless user
    debug.error 'getGroupAddShowingLog not login'
    return cb(MSG_STRINGS.NEED_LOGIN)
  if not (id or uaddr or unit)
    debug.error 'getGroupAddShowingLog BAD_PARAMETER'
    return cb(MSG_STRINGS.BAD_PARAMETER)
  try
    gid = await GroupModel.isInGroup {uid:user._id, groupName:IN_REAL_GROUP_NAME}
  catch err
    debug.error err
    return cb MSG_STRINGS.DB_ERROR
  unless gid
    debug.error "getGroupAddShowingLog:#{MSG_STRINGS.NO_AUTHORIZED} uid #{user._id}, #{typeof(user._id)}"
    return cb MSG_STRINGS.NO_AUTHORIZED
  try
    memberIds = await GroupModel.getMembersuidById gid
    # TODO: if prop sold or stautus is u dont show to non-admin
    uids = await ShowingModel.getInrealGroupAddShowingLog {uid:user._id,\
      propId:id,uaddr,unit,uids:memberIds}
    userInfo = await UserModel.getListByIds uids,{projection:{nm_zh:1,nm_en:1,nm:1,avt:1}}
  catch err
    debug.error err
    return cb MSG_STRINGS.DB_ERROR
  users = []
  for u in userInfo
    if avt = u?.avt
      u.avt = replaceRM2REImagePath avt
    u.nm = libUser.fullNameOrNickname(user.locale, u)
    users.push u
  cb(null,users)

# /1.5/prop/showGeoCache
GET 'showGeoCache', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    unless req.isAllowed('geoAdmin')
      return resp.ckup 'generalError',{err:MSG_STRINGS.NO_AUTHORIZED}
    uaddr = req.param('uaddr')
    return resp.ckup 'admin/showGeocache',{uaddr}

#/1.5/prop/showGeoCache
POST 'showGeoCache', (req, resp) ->
  unless uaddr = req.body?.uaddr
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed('geoAdmin')
      debug.error {msg:"prop/showGeoCache:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    uaddr = decodeURIComponent(uaddr)
    try
      geocache = await GeoCoder.getGeoCacheByUaddr uaddr
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
    geocacheStr = MSG_STRINGS.NOT_FOUND
    if geocache
      geocacheStr = helpersStr.stringify(geocache)
    resp.send {ok:1,geocache: geocacheStr}

APP 'detail',true


# get view from outside app(in-app uses modal)
# params: .uid              # will append user brief info at bottom
#         ~~?fromAgent=true~~   #shared from agent (not used any more)
#         &dl           #show download bottom hover, overides from agent and not from app

#/1.5/prop/detail?id=TRBN11111
GET '',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    # req.user = user
    lang = (req.param('lang') or req.param('locale'))
    if lang and (lang in LANGUAGE_LIST)
      req.setLocale lang
    return process_listing_req {req,resp,user,redirectInapp:true}

#/1.5/prop/detail/inapp?id=TRBN11111
GET 'inapp',(req,resp)->
  if req.getDevType() isnt 'app'
    return resp.ckup 'generalError',{err:'Error Loading Page'}
  if not id = req.param('id')
    return resp.ckup 'generalError',{err:'No ID specified'}
  if lang = req.param('lang') and lang in LANGUAGE_LIST
    req.setLocale lang
  return resp.ckup 'propDetailPageInapp',{},"_",{noUserModal:1,wxcfg:{},hasPageCss:1,noAppCss:1,noRatchet:1,nosmbcss:1,noFontAwesome:1,noRatchetJs:1,noAngular:1}
