config = CONFIG(['serverBase'])
debug = DEBUG()
PropManualImportModel = MODEL 'PropManualImport'
UserModel = MODEL 'User'
{respError} = INCLUDE 'libapp.responseHelper'
APP '1.5'
APP 'import',true

# /1.5/import/manual
# propadmin有权限打开手动导入房源页面
GET 'manual', (req, resp)->
  return resp.redirect '/1.5/index' unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
  UserModel.appAuth {req,resp}, (user)->
    return resp.redirect '/1.5/user/login' unless user
    return resp.redirect '/1.5/index' unless req.isAllowed('propAdmin')
    resp.ckup 'import/manualImport',{}

# /1.5/import/edit
# 添加数据
POST 'edit', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      {msg,ret} = await PropManualImportModel.addProp(req.body)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    if msg is MSG_STRINGS.ALREADY_EXISTS
      return resp.send {ok:0, err:req.l10n(msg),ret}
    return resp.send {ok:1, msg:req.l10n(msg)}

# /1.5/import/sync
# 添加数据
POST 'sync', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      msg = await PropManualImportModel.syncProp(req.body.id)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    if msg is MSG_STRINGS.NOT_FOUND
      return resp.send {ok:0, err:req.l10n(msg)}
    return resp.send {ok:1, msg:msg}

# /1.5/import/reset
# 添加数据
POST 'reset', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      msg = await PropManualImportModel.resetProp(req.body.id)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    # if msg is MSG_STRINGS.NOT_FOUND
    #   return resp.send {ok:0, err:req.l10n(msg)}
    return resp.send {ok:1, msg:msg}

# /1.5/import/dump
# 添加数据
POST 'dump', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  unless req.body?
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      {msg,ids} = await PropManualImportModel.export(req.body)
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    # if msg is MSG_STRINGS.NOT_FOUND
    #   return resp.send {ok:0, err:req.l10n(msg)}
    return resp.send {ok:1, msg:msg, ids}

# /1.5/import/history
# 获取历史数据
POST 'history', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      list = await PropManualImportModel.getList()
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    return resp.send {ok:1, list}

# /1.5/import/delete
# 删除数据
POST 'delete', (req, resp)->
  unless (config.serverBase?.masterMode and (not config.serverBase?.chinaMode))
    return respError {clientMsg:'Mode error',resp}
  unless req.body?.id
    return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHED),resp} unless req.isAllowed('propAdmin')
    try
      msg = await PropManualImportModel.delete req.body?.id
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR,resp}
    return resp.send {ok:1, msg:req.l10n(msg)}
