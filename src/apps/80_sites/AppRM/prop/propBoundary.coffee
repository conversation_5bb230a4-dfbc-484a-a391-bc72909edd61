
PropertyBoundaryModel = MODEL 'PropertyBoundary'
UserModel = MODEL 'User'
{respError} = INCLUDE 'libapp.responseHelper'

APP 'prop'
APP 'boundary', true

POST 'bbox', (req, resp) ->
  UserModel.appAuth { req, resp }, (user) ->
    return respError { category: MSG_STRINGS.NEED_LOGIN, errorCode: 0, resp } unless user
    # TODO: remove this when have native support in 6.2.7
    return resp.send {ok:0, err:'Unavailable'}
    { bbox } = req.body
    coordinates = await PropertyBoundaryModel.findByBBox bbox
    resp.send { ok: 1, coordinates }
