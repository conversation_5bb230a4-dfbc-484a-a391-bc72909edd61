helpers = INCLUDE 'lib.helpers'
propSearch = INCLUDE 'libapp.propSearch'
mapServer =  INCLUDE 'lib.mapServer'
limiter = INCLUDE 'lib.limiter'
libUser = INCLUDE 'libapp.user'
UserModel = MODEL 'User'
School = MODEL 'School'
CommunityModel = MODEL 'Community'
{deepCopyObject} = INCLUDE 'lib.helpers_object'
# getWXConfig = DEF 'getWXConfig'
dataMethods = DEF 'dataMethods'

Properties = MODEL 'Properties'
ptpValuesList = Properties.ptpValuesList
domFilterValsArr = DEF 'domFilterValsArr'
domFilterValsShortArr = DEF 'domFilterValsShortArr'
checkVersionNShow = DEF 'checkVersionNShow'

getConfig = DEF 'getConfig'
appVersion = DEF 'appVersion'

countProperties = propSearch.countProperties

translatePtps = (req)->
  ret = [{ptp_en: "", pstyl_en: "", ptp: req.l10n("No restrictions")}]
  for p in ptpValuesList
    tmp = {
      ptp_en:p[0]
      pstyl_en:p[1]
      ptp:req.l10n(p[0])
    }
    if p[1]
      tmp.pstyl = req.l10n(p[1])
    ret.push tmp
  ret
DEF 'translatePtps', translatePtps

APP '1.5'


# verGTE = DEF 'verGTE'

ADDPLUGIN 'useWebMap',(data)-> # return true/false
  # use webmap setting in session, if has
  if @getAppVer() is 'appDebug'
    return true
  if (useWebMap = @session.get('useWebMap'))?
    # NOTE: do not remove log
    # console.log('useWebMap ++++',useWebMap,typeof(useWebMap))
    # console.log 'useWebMap api',useWebMap
    useWebMap = if useWebMap is 'true' then true else false
    return useWebMap
  # use webmap when in china
  # console.log '+++++++verGTE',verGTE(@, '6.0.1')
  unless @verGTE('6.0.1')
    return @isRealChinaIP()
  # console.log '+++++isIos',@headers
  # NOTE: native fetch user-agent = {<PROJECT_NAME>/<VERSION>}
  if @isIOSDevice()
    return false
  # NOTE: Android map crashes, use webmap for now
  # if @isAndroid()
  #   return true
  # null == depends
  null

ADDPLUGIN 'useTileOverlay',(data)-> # return true/false
  # use webmap setting in session, if has
  if (useTileOverlay = @session.get('useTileOverlay'))?
    # NOTE: do not remove log
    # console.log('useTileOverlay ++++',useTileOverlay,typeof(useTileOverlay))
    # console.log 'useWebMap api',useWebMap
    useTileOverlay = if useTileOverlay is 'true' then true else false
    return useTileOverlay
  # use webmap when in china
  unless @isRealChinaIP()
    return false
  # lower version no support
  unless @verGTE '6.0.1'
    return false
  # if @isIOS()
  return true
  # false

GET_MAP_TILE_URL = DEF 'GET_MAP_TILE_URL'
# MAP_TILE_URL = DEF 'MAP_TILE_URL'
# TODO: use appConfig for this
dataMethods.tileUrl = (req, user, cb) ->
  tileUrl = req.session.get('tileUrl')
  unless tileUrl
    tileUrl = GET_MAP_TILE_URL(req.isRealChinaIP())
  return cb null, tileUrl

dataMethods.useWebMap = (req, user, cb) ->
  return cb null, req.useWebMap()

# TODO:?
# dataMethods.mapShowStigmatized = (req, user, cb) ->
#   return cb null, true
# dataMethods.mapShowCoop = (req, user, cb) ->
#   return cb null, true

dataMethods.useTileOverlay = (req, user, cb) ->
  return cb null, req.useTileOverlay()

# TODO: do not use displayVal in if statement/selectTag
dataMethods.propFeatureTags = (req, user, cb) ->
  values = [
    ['Open House','oh',true]
    ['Best School','sch','best'],
    ['Near MTR','neartype','mtr'],
    ['Price Off','lpChg','off'],
    ['Sold Fast','sold','fast'],
    ['Sold At Loss','soldLoss','loss'],
  ]
  # NOTE: this is load sequence dependent
  # TODO: use GroupModel.isInGroup {uid:user._id,groupName:':inReal'},(err,ret)->
  dataMethods.isRealGroup req,user,(err,inGroup)->
    if inGroup and (appVersion(req) >= '6.2.6')
      values = values.concat [
        ['POS','isPOS',1],
        ['Estate','isEstate',1],
      ]
    ret = []
    for i in values
      ret.push {field:i[1],value:i[2],displayVal:i[0],k:i[0]}
    return cb null,ret

# NOTE:setSaleTp ignored this value
dataMethods.propSaleTps = (req, user, cb) ->
  return cb null,{
    'Sale':{saletp:'sale',dom:''},
    'Sold':{saletp:'sale',dom:'-90',v:'3 month'},
    'Rent':{saletp:'lease',dom:''},
    'Leased':{saletp:'lease',dom:'-90',v:'3 month'},
  }

dataMethods.hotCommercialPtypes = (req, user, cb) ->
  return cb null, [
    {k:'Farm',v:req.l10n('Farm')},
    {k:'Office',v:req.l10n('Office')},
    {k:'School',v:req.l10n('Schools')}
  ]

dataMethods.propPtypes = (req, user, cb) ->
  return cb null, Properties.getAllTranslatedPtypes(req.locale())

dataMethods.ptpTypes = (req, user, cb) ->
  return cb null, translatePtps(req)

dataMethods.propExposures = (req, user, cb)->
  return cb null, [
    {k:'N', v:req.l10n('N','exposure')},
    {k:'E', v:req.l10n('E','exposure')},
    {k:'S', v:req.l10n('S','exposure')},
    {k:'W', v:req.l10n('W','exposure')},
    {k:'NW', v:req.l10n('NW','exposure')},
    {k:'NE', v:req.l10n('NE','exposure')},
    {k:'SW', v:req.l10n('SW','exposure')},
    {k:'SE', v:req.l10n('SE','exposure')},
  ]
dataMethods.propSortMethods = (req, user, cb)->
  return cb null, [
    {k:'auto-ts', v:req.l10n('Auto','sort')},
    {k:'ts-desc', v:req.l10n('Listing date new to old')},
    {k:'ts-asc', v:req.l10n('Listing date old to new')},
    {k:'lp-desc', v:req.l10n('Price high to low')},
    {k:'lp-asc', v:req.l10n('Price low to high')},
    {k:'spcts-desc', v:req.l10n('Updated new to old')},
    {k:'spcts-asc', v:req.l10n('Updated old to new')}
  ]

dataMethods.propYearValues = (req, user, cb)->
  return cb null, [
    {k:'', v:req.l10n('Any')},
    # {k:'new', v:req.l10n('New')},
    {k:0,v:req.l10n('New')},
    {k:2,v:2},
    {k:5,v:5},
    {k:7,v:7},
    {k:10,v:10},
    {k:15,v:15},
    {k:30,v:30},
    {k:50,v:50},
    {k:100,v:100},
  ]
dataMethods.propFrontFtValues = (req, user, cb)->
  return cb null, [
    {k:'', v:req.l10n('Any')},
    # {k:'new', v:req.l10n('New')},
    # {k:0,v:req.l10n('New')},
    {k:10,v:10},
    {k:20,v:20},
    {k:30,v:30},
    {k:40,v:40},
    {k:50,v:50},
    {k:60,v:60},
    {k:70,v:70},
    {k:80,v:80},
    {k:90,v:90},
    {k:100,v:100},
  ]
dataMethods.propDepthValues = (req, user, cb)->
  return cb null, [
    {k:'', v:req.l10n('Any')},
    # {k:'new', v:req.l10n('New')},
    # {k:0,v:req.l10n('New')},
    {k:50,v:50},
    {k:60,v:60},
    {k:70,v:70},
    {k:80,v:80},
    {k:90,v:90},
    {k:100,v:100},
    {k:150,v:150},
    {k:200,v:200},
    {k:250,v:250},
    {k:300,v:300},
  ]
dataMethods.propSqftValues = (req, user, cb)->
  return cb null, [
    {k:'', v:req.l10n('Any')},
    {k:400,v:400},
    {k:500,v:500},
    {k:600,v:600},
    {k:700,v:700},
    {k:800,v:800},
    {k:900,v:900},
    {k:1000,v:1000},
    {k:1200,v:1200},
    {k:1400,v:1400},
    {k:1600,v:1600},
    {k:1800,v:1800},
    {k:2000,v:2000},
    {k:2500,v:2500},
    {k:3000,v:3000},
    {k:3500,v:3500},
    {k:4000,v:4000},
  ]


dataMethods.projSortMethods = (req, user, cb)->
  return cb null, [
    {k:'soon',v:req.l10n('Complete Soon','Pre-Construction')},
    {k:'new',v:req.l10n('New Release','Pre-Construction')},
    {k:'auto-ts', v:req.l10n('Auto','sort')},
    {k:'ts-desc', v:req.l10n('Listing date new to old')},
    {k:'ts-asc', v:req.l10n('Listing date old to new')},
    # {k:'lp-desc', v:req.l10n('Price high to low')},
    # {k:'lp-asc', v:req.l10n('Price low to high')},
    {k:'mt-desc', v:req.l10n('Updated new to old')},
    {k:'mt-asc', v:req.l10n('Updated old to new')}
  ]
# dataMethods.propCounts = (req, user, cb)->
#   countProperties Properties, (err, ctx)->
#     return cb err, ctx

dataMethods.domFilterVals = (req, user, cb)->
  orig = domFilterValsArr
  ret = []
  for i in orig
    ret.push {k:i.k, v:req.l10n(i.v)}
  return cb null, ret

dataMethods.domFilterValsShort = (req, user, cb)->
  orig = domFilterValsShortArr
  ret = []
  for i in orig
    sv = i.sv
    if i.k in ['']
      sv = req.l10n(i.sv)
    ret.push {k:i.k, v:req.l10n(i.vv), sv:sv, vv:i.v}
  return cb null, ret

domYearFilterValsArr = []
curYear = new Date().getFullYear()
for i in [0..(curYear-2003)] #support domYear from curYear to 2003
  domYearFilterValsArr.push {k:(curYear-i)*(-1),sv:curYear-i}

dataMethods.domYearFilterVals = (req,user,cb)->
  ret = []
  for domYear in domYearFilterValsArr
    {k,sv} = domYear
    ret.push {k:k.toString(),sv,v:"#{sv} #{req.l10n('year')}",tp:'year'}
  return cb null, ret

dataMethods.bsmtFilterVals = (req, user, cb)->
  req.setupL10n() unless req._ab
  ret = [] #{k:'',v:req.l10n('Any')}
  for k in ['Fin','Unfin','NON','Prt','Apt','Sep','W/O','Crw','W/U','Slab']
    ret.push({
      k:k,
      v:req._ab(k, 'bsmt')
    })
  return cb null, ret

dataMethods.savedSearches = (req,user,cb)->
  UserModel.appAuth {req,resp:null},(user)->
    return cb null,[] unless user
    UserModel.findSavedSearch user._id,(err,savedSearch)->
      return cb MSG_STRINGS.DB_ERROR,null if err
      savedSearchMap = {}
      for s in savedSearch
        key = s.nm or s.ts?.toISOString().slice(0,10)
        savedSearchMap[key] = s.k
      return cb null,savedSearchMap


dataMethods.psnValues = (req,user,cb)->
  return cb null, [
    {k:'',v:req.l10n('Any')},
    {k:'immed', v:'IMMED'},
    {k:'tba',v:'TBA/TBD'},
    {k:'flex',v:'Flexible'}
  ]
studentRentalLib = INCLUDE 'libapp.studentRental'
dataMethods.distances = (req,user,cb)->
  return cb null,studentRentalLib.getDistances()

dataMethods.rentalSorts = (req,user,cb)->
  return cb null,studentRentalLib.getSortOptions(req)

dataMethods.propTags = (req,user,cb)->
  PROP_TAGS = [
    {k:'ts',v:'new',desc:'new'},
    {k:'lpChg',v:'off',desc:'price off'},
    {k:'soldLoss',v:'loss',desc:'sold at loss'},
    {k:'recent',v:'sold',desc:'recently sold'},
    {k:'sold',v:'fast',desc:'sold fast'}
  ]
  propTags = deepCopyObject PROP_TAGS,1
  lang = req.locale()
  if lang isnt 'en'
    for tag in propTags
      tag.desc = req.l10n tag.desc,'tag'
  return cb null,propTags

#TODO: remove this after all got pn
dataMethods.updatePN = (req, user)->
  if user #and not user?.pn
    return true
  false

show_map = (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    ctx = {}
    ctx.title ?= req.l10n("RealMaster") + " " + req.l10n("Map Search")
    # ctx.googleapiJs = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),src:'map'})
    checkVersionNShow req,resp,'5.3.1',->
      propsLimit = getConfig('showingPropsLimit')
      isApp = req.getDevType() is 'app'
      isRealtor = req.hasRole 'realtor',user
      apikey = mapServer.getMapboxAPIKey {isApp}
      paramAppmode = req.param 'appmode'
      # 优先param，可能是从地图来的，地图切换了模式，但cookie没有
      appmode = paramAppmode or req.cookies[libUser.APPMODE]
      cfg = {ctx,isRealtor,propsLimit}
      unless paramAppmode
        cfg.appmode = appmode
      # NOTE param.appmode is in vars already
      resp.ckup 'mapSearchPageNew',cfg,'_', {noAngular:1,mapboxkey:apikey}

APP 'mapSearch', true

GET 'advFilter',(req,resp)->
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  resp.ckup 'advFilterView'

VIEW 'advFilterView',->
  # TODO: should not need this css
  css '/css/apps/appMapSearch.css'
  css '/css/pikaday.css'
  div id:'vueBody',->
    text """<map-adv-filter-modal></map-adv-filter-modal>"""
  js '/js/entry/commons.js'
  js '/js/moment.min.js'
  js '/js/pikaday.js'
  js '/js/entry/appAdvFilterModalPage.js'

#1.5/mapSearch
GET '',(req,resp)->
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  # return if req.redirectHTTPWhenCnDitu()
  show_map req,resp

###
#288

<i @click="toggleTitleTypeSelect()" class="fa fa-angle-down"></i>
{{items.length+'/'+cntTotal}}{{cntTotal>=99?'+':''}} {{_('results')}}
<loading-square :loading.sync="loading"></loading-square>
<a href="/1.5/user/login" v-show="mapSearchTipLogin" style="width: 28px;">{{_('LOGIN','login now')}}</a>
<h1 class="title v-cloak--block" v-cloak>住宅<i class="fa fa-angle-down"></i></h1>
<h1 class="title" v-cloak>{{propTmpFilterVals.saletp}}</h1>
<a v-show="viewMode == 'map'" id="searchStrToggle" @click="showSearchInput()" class="pull-right">
  <span class="icon fa fa-rmsearch" v-show="!showSearchStr"></span>
  <span class="icon icon-close" v-show="showSearchStr"></span></a>
<a v-show="propTmpFilter.src=='mls' && !focused && !showItems" @click="showAdvFilter()" class="pull-right icon"><i class="fa fa-rmfilter"></i></a>
<div @click="showQuickFilter('sort')" class="sort">{{_('Sort')}}<span :class="[quickFilterMode == 'sort' ? 'fa-angle-up':'fa-angle-down']" class="fa"></span></div>
<input v-model="searchStr" id="addrInput" @focus="inputFocus()" :placeholder="_('Input Address/Listing ID')" :class="{focused:focused || showItems}" class="search"/>
toggleSaletpSelect('open') toggleSaletpSelect('open')
###
VIEW 'MapMainWindowViewText', ->
  text """
<flash-message></flash-message>
<div class="hidedrop backdrop" :class="{showdrop:showSaleTypeSelect}" @click="toggleSaletpSelect()"></div>
<header v-cloak id="header-bar" v-show="appMapMode !== 'fav'" class="bar bar-nav">
  <a href="javascript:;" data-sub="close" onClick="goBack2({isPopup:vars.isPopup,d:vars.d})" class="icon fa-back pull-left"></a>
  <div href="javascript:;" data-sub="header buy" @click="setSaleTp('sale',1);" class="toggleTitle" :class="{'active':propTmpFilter.ltp!=='projects' && propTmpFilter.saletp == 'sale'}">
    <div> {{_('Buy','menu')}}</div>
    <div class="red-border white-border" :class="calcDirection('sale',0)"></div>
  </div>
  <div href="javascript:;" data-sub="header rent" @click="setSaleTp('lease',1);" class="toggleTitle" :class="{'active':propTmpFilter.ltp!=='projects' && propTmpFilter.saletp !== 'sale'}">
    <div> {{_('Rent','menu')}}</div>
    <div class="red-border white-border" :class="calcDirection('lease',1)"></div>
  </div>
  <div href="javascript:;" data-sub="header precon" @click="setSearchMode({k:'PreCons',clear:true})" class="toggleTitle" :class="{'active':propTmpFilter.src=='rm' && propTmpFilter.ltp == 'projects'}">
    <div> {{_('PreCon','menu')}}</div>
    <div class="red-border white-border" :class="calcDirection('PreCons',2)"></div>
  </div>
  <div class="nativeSearchEntry" data-sub="search" v-show="isNativeSearch" @click="onClickSearchBar"></div>
  <a href="javascript:;" data-sub="show list" @click="showListView()" v-show="!dispVar.listShareMode && viewMode=='map' && !showItems" class="pull-right mode-switch">
    <span class="">{{_('LIST', 'view')}}</span>
  </a>
  <a href="javascript:;" data-sub="show map" @click="showMapView()" v-show="!dispVar.listShareMode && viewMode=='list' && !showItems" class="pull-right mode-switch">
    <span class="">{{_('MAP','view')}}</span>
  </a>
  <a id="searchBtn" data-sub="search" @click="onClickSearchBar()" class="fa fa-rmsearch pull-right"></a>
</header>

<header v-cloak id="favGrpSelect" v-show="appMapMode == 'fav'" class="bar bar-nav">
  <a href="javascript:;" data-sub="cloase" v-show="!noBack" class="icon fa fa-back pull-left" @click="goBack()"></a>
  <a href="javascript:;" data-sub="show list" @click="showListView()" v-show="!dispVar.listShareMode && viewMode=='map'" class="pull-right mode-switch">
    <span class="">{{_('LIST', 'view')}}</span>
  </a>
  <a href="javascript:;" data-sub="show map" @click="showMapView()" v-show="!dispVar.listShareMode && viewMode=='list'" class="pull-right mode-switch">
    <span class="">{{_('MAP','view')}}</span>
  </a>
  <!-- <a @click="showFavOptions()" v-show="!((mapmode == 'projects')&& (appMapMode=='fav'))" class="pull-right"><span style="" class="icon icon-compose"></span></a> -->
  <h1 class="title" data-sub="change folder" @click="toggleTitleTypeSelect()" v-show="!((mapmode == 'projects')&& (appMapMode=='fav'))" ><span>{{grpName}}</span><i class="fa fa-caret-down"></i></h1>
</header>
<!-- <div id="saleTypeSelect" class="modal modal-55pc">
  <div class="content">
    <div class="inline-wrapper">
      <div class="inline" :class="{active:searchModeSaletp == 'sale'}" @click="setSearchModeSaletp('sale')"><span>{{_('For Sale')}}</span></div>
      <div class="inline" :class="{active:searchModeSaletp == 'lease'}" @click="setSearchModeSaletp('lease')"><span>{{_('For Rent')}}</span></div>
    </div>
    <div class="types-wrapper">
      <div v-for="s in searchModes" @click="setSearchMode(s)" class="type">
        <img :src="s.img"/>
        <div class="tp-name">{{_(s.v||s.k, s.ctx)}}</div>
      </div>
    </div>
  </div>
  <div class="bar bar-standard bar-footer">
    <button @click="toggleSaletpSelect()" class="btn btn-block btn-long btn-nooutline">{{_('Close')}}</button>
  </div>
</div>
-->
<div class="content" v-show="focused" id="autocompleteWrapper" v-cloak>
  <autocomplete-list-wrapper :show-autocompletes="showAutocompletes" :hist="hist" :glist="gautocompletes" :alist="autocompletes" :cnt="cnt" :acloading.sync="acloading"></autocomplete-list-wrapper>
</div>
<div id="map-view" :class="{hidden2:!(viewMode == 'map' && !showItems),precon:isPtypeProject()}" v-cloak>
  <div v-show="appMapMode !== 'fav'" class="bar bar-standard bar-header-secondary">
    <a data-sub="filter city" @click="getCityList()" class="city"><span>{{_('City')}}</span><span :class="[quickFilterMode == 'city'?'fa-caret-up':'fa-caret-down']" class="fa"></span>
    </a>
    <div class="split" v-show="!isPtypeProject()"></div>
    <a v-show="!isPtypeProject()" data-sub="filter sale" class="sale subTag" :class="{'active':!isSoldCondition()}" @click="switchToSaleMode()">
      <span v-show="this.propTmpFilter.saletp=='sale'">{{_('For Sale','saleSoldSelect')}}</span>
      <span v-show="this.propTmpFilter.saletp!=='sale'">{{_('For Rent','saleSoldSelect')}}</span>
    </a>
    <a v-show="!isPtypeProject()" data-sub="filter sold" class="sold subTag" :class="{'active':isSoldCondition()}" @click="toggleSoldDomSelect()">
      {{this.propTmpFilter.saletp=='sale' ? _('Sold','saleSoldSelect') : _('Leased','saleSoldSelect')}} {{computedOffMarketDays()}}
    </a>
<!--
    <div @click="getCityList()" class="sale" :class="[propTmpFilter.saletp == 'sale' ? 'active':'']"><span>{{_('For Sale')}}</span>
    </div>
    <div @click="getCityList()" class="sold" :class="[propTmpFilter.saletp !== 'sale' ? 'active':'']"><span>{{_('Sold')}}</span>
    </div>
      <div @click="showQuickFilter('ptype2')" class="ptype">{{_('Style')}}<span :class="[quickFilterMode == 'ptype2' ? 'fa-angle-up':'fa-angle-down']" class="fa"></span></div>
      <div @click="showQuickFilter('price')" class="price">{{_('Price')}}<span :class="[quickFilterMode == 'price' ? 'fa-angle-up':'fa-angle-down']" class="fa"></span></div>
  -->
    <!--div v-show="propTmpFilter.src!=='mls'" @click="showQuickFilter('sort')" class="pull-right sort">{{_('Sort')}}<span :class="[quickFilterMode == 'sort' ? 'fa-angle-up':'fa-angle-down']" class="fa"></span></div-->
    <a v-show="!isPtypeProject()" data-sub="advanced search" data-query="place:2" @click="showAdvFilter()" class="sort">{{_('FILTER')}}<span class="fa fa-rmfilter" :class="hasExtraFilter()"></span></a>
  </div>
  <!--
  <div v-show="showSearchStr" class="search-input bar bar-standard bar-header-secondary short">
    <div><input id="inputAddr" type="text" v-model="searchStr" placeholder="Input Address" onclick="this.setSelectionRange(0, this.value.length)"></div>
    <span class="fa fa-rmsearch"></span>
  </div>
  -->
  <div id="saleSoldSelect" class="side-pane" v-if="dispVar.showSoldPriceBtn && this.propTmpFilter.ptype !== 'Commercial' && this.propTmpFilter.src !== 'rm'" v-show="! appMapMode=='fav'">
    <span class="sale" :class="{'active':!isSoldCondition()}" @click="switchToSaleMode()">
      <span v-show="this.propTmpFilter.saletp=='sale'">{{_('Sale','saleSoldSelect')}}</span>
      <span v-show="this.propTmpFilter.saletp!=='sale'">{{_('Rent','saleSoldSelect')}}</span>
    </span>
    <span class="sold" :class="{'active':isSoldCondition()}" @click="toggleSoldDomSelect()">
      <span v-show="this.propTmpFilter.saletp=='sale'">{{_('Sold','saleSoldSelect')}}</span>
      <span v-show="this.propTmpFilter.saletp!=='sale'">{{_('Leased','saleSoldSelect')}}</span>
      <span class="days">{{computedOffMarketDays}}</span>
      <span class="fa" :class="[showSoldList?'fa-angle-up':'fa-angle-down']"></span>
    </span>
  </div>
  <div id="domFilterVals" class="side-pane" v-show="showSoldList">
    <div class="soldOnly" @click="setSoldOnly()">{{_('Sold Only')}}<span class="pull-right fa" :class="[propTmpFilter.soldOnly?'fa-rmcheck':'fa-rmuncheck']"></span></div>
    <div v-for="t in domFilterVals" @click="selectSoldFilterVal(t)">{{t.v}}</div>
  </div>
  <div id="mapDispMode" data-sub="mapDispMode" @click="setMapTypeId()" class="side-pane">
    <span class="fa" :class="mapDispMode == 'ROADMAP'?'fa-globe':'fa-map-o'"></span>
  </div>
  <div id="locateMe" data-sub="locateMe" @click="locateMe()" class="side-pane"><span class="fa fa-locate"></span></div>
  <div id="mapZoomIn" data-sub="mapZoomIn" @click="zoomIn" class="side-pane icon icon-plus"></div>
  <div id="mapZoomOut" data-sub="mapZoomOut" @click="zoomOut" class="side-pane fa fa-rmminus"></div>
  <div v-show="loading || showMapSearchTip" class="mapSearchTip bar bar-standard bar-footer-secondary" :class="{loading:loading}">
    <div v-html="computedMapSearchTip"></div><a data-sub="close map search tip" @click="closeMapSearchTip()" class="icon fa fa-rmclose" v-show="!loading"></a>
  </div>
  <div id="progressWrapper" v-show="loading" :class="{fav:appMapMode=='fav'}">
    <loading-bar></loading-bar>
  </div>
  <!--
  <div v-show="loading" class="mapSearchTip bar bar-standard bar-footer-secondary">
    <div>{{_('Loading...')}}</div>
  </div>
  -->
  <nav class="bar bar-tab" v-show="appMapMode !== 'fav' && !isPtypeProject()">
    <a v-for="ptype in bottomNavPtypes" :data-sub="'filter '+ptype.txt" class="tab-item" v-show="ptype.saletp?propTmpFilter.saletp == ptype.saletp:true" href="javascript:;" :class="{active:computedMapSearchMode == ptype.k}" @click="setSearchMode({k:ptype.k,clear:true})">
      <span :class="'icon fa fa-'+ptype.icon"></span>
      <span class="tab-label">{{_(ptype.txt,ptype.ctx)}}</span>
    </a>
    <!--
      <a class="tab-item" href="javascript:;" @click="toggleSaletpSelect('open')" :class="{active: isOtherFooterActive}">
        <span class="icon fa fa-option"></span>
        <span class="tab-label">{{_('More','footer more prop types')}}</span>
      </a>
    <div v-show="!dispVar.listShareMode" class="total pull-left" @click="toggleSaletpSelect()">
      <span class="fa fa-option highlight-red" :class="{flash:loading}"></span>
      <div class="inline">
        <span class="highlight">{{propTmpFilterVals.saletp}} - {{computedMapSearchMode}}</span>
        <span class="fa fa-angle-down"></span>
      </div>
    </div>
    -->
  </nav>
  <div id="map-container" class="content" :class="{'full-height':appMapMode == 'fav'}">
    <div id="map-holder"></div>
  </div>
</div>
<div v-cloak id="ptype2Select" v-if="propTmpFilter.src=='mls'" :class="{visible:quickFilterMode == 'ptype2'}" class="popover">
  <ul class="table-view">
    <li v-for="t in ptype2s" data-sub="filtered ptype2s" :data-query="'type:'+t.k" @click="ptype2Select(t.k, t.v)" class="table-view-cell">{{t.v}}<span :class="ptype2CheckMark(t.k)" class="icon-pull-right fa"></span></li>
  </ul>
  <div class="bar bar-standard bar-footer">
    <button data-sub="search filtered ptype2s" @click="isSelectedTag = 1;doSearch({clear:true})" class="btn btn-sharp btn-nooutline btn-block btn-long">{{_('OK')}}</button>
  </div>
</div>
<div v-cloak id="ptpSelect" v-if="propTmpFilter.src=='rm' && this.propTmpFilter.ltp != 'projects'" :class="{visible:quickFilterMode == 'ptype2'}" class="popover">
  <ul class="table-view">
    <li v-for="t in ptpTypes" data-sub="filtered ptype2s" :data-query="'pType:'+t.ptp+',pStyle:'+t.pstyl" @click="ptpSelect(t)" class="table-view-cell">{{t.ptp}} {{t.pstyl}}
      <span :class="[(propTmpFilter.ptp == t.ptp_en && propTmpFilter.pstyl == t.pstyl_en) ? 'fa-check':'']" class="icon-pull-right fa"></span>
    </li>
  </ul>
</div>
<div v-cloak id="ptpSelect" v-if="propTmpFilter.src=='rm' && this.propTmpFilter.ltp == 'projects'" :class="{visible:quickFilterMode == 'ptype2'}" class="popover">
  <ul class="table-view">
    <li v-for="t in projPropTypes" data-sub="filtered ptype2s" :data-query="'pType:'+t.ptp" @click="ptpSelect(t)" class="table-view-cell precons" :class="[(propTmpFilter.ptp == t.ptp_en) ? 'selected':'']">{{t.ptp}}
    </li>
  </ul>
</div>
<div v-cloak id="priceRange" :class="{visible:quickFilterMode == 'price'}" class="popover">
  <div id="priceInputWrapper">
    <div>
      <input type="number" v-model="propTmpFilter.min_lp" number="number" :placeholder="_('Min')"/>
    </div>
    <div class="dash"></div>
    <div>
      <input type="number" v-model="propTmpFilter.max_lp" number="number" :placeholder="_('Max')"/>
    </div>
  </div>
  <div class="bar bar-standard bar-footer">
    <button data-sub="search filtered price" @click="isSelectedTag = 1;doSearch({clear:true})" class="btn btn-sharp btn-nooutline btn-block btn-long">{{_('OK')}}</button>
  </div>
</div>
<div v-cloak id="sortSelect" :class="{visible:quickFilterMode == 'sort'}" class="popover">
  <ul class="table-view">
    <li v-show="propTmpFilter.ltp == 'projects'" data-sub="sort" :data-query="'type:'+s.k" v-for="s in dispVar.projSortMethods" @click="sortSelect(s.k, s.v)" class="table-view-cell">{{s.v}}<span :class="[propTmpFilter.sort == s.k ? 'fa-check':'']" class="icon-pull-right fa"></span></li>
    <li v-show="propTmpFilter.ltp !== 'projects'" data-sub="sort" :data-query="'type:'+s.k" v-for="s in dispVar.propSortMethods" @click="sortSelect(s.k, s.v)" class="table-view-cell">{{s.v}}<span :class="[propTmpFilter.sort == s.k ? 'fa-check':'']" class="icon-pull-right fa"></span></li>
  </ul>
</div>
<div id="str-list-view" v-show="showItems && !focused" v-cloak>
  <div class="content">
    <prop-list :list.sync="propItems" :disp-var="dispVar" :show-fav="false" :has-wechat="hasWechat"></prop-list>
    <button id="showMoreBtn" data-sub="load more" @click="showMoreProps()" class="btn btn-block btn-positive" v-show="cnt > propItems.length">{{_('Show More')}}</button>
  </div>
</div>
<div id="list-view" v-show="viewMode == 'list' && !showItems && !focused" v-cloak >
  <!--div v-show="appMapMode !== 'fav' && filterOnTop==1" class="bar bar-standard bar-header-secondary short">
    <a @click="getCityList({doSearch:true})" class="city filter"><span v-show="propTmpFilterVals.city" class="cityName">{{propTmpFilterVals.city}}</span><span v-show="!propTmpFilterVals.city">{{_('City')}}</span><span :class="[quickFilterMode == 'city' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span></a>
    <a @click="filterOnTop = 1;showQuickFilter('ptype2')" class="ptype filter">{{_('Style')}}<span :class="[quickFilterMode == 'ptype2' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span></a>
    <a v-show="!isPtypeProject()" @click="filterOnTop = 1; showQuickFilter('price')" class="price filter">{{_('Price')}}<span :class="[quickFilterMode == 'price' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span></a>
    <!--a v-show="propTmpFilter.src!=='mls'" @click="filterOnTop = 1; showQuickFilter('sort')" class="sort filter">{{_('Sort')}}<span :class="[quickFilterMode == 'sort' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span></a-->
    <a v-show="!isPtypeProject()" @click="showAdvFilter()" class="sort filter" :class="{'active':hasExtraFilter()}">
      <span>{{_('More')}}</span>
      <span class="filterCounts" v-if="hasExtraFilter() > 0">{{hasExtraFilter()}}</span>
      <span :class="[quickFilterMode == 'sort' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span>
    </a>
  </div-->
  <nav v-if="!isPtypeProject()" id="listViewFooter" class="bar bar-tab" v-show="appMapMode !== 'fav' && !showPropTable" v-cloak>
    <!--
    <a @click="multiShare()" v-show="dispVar.listShareMode" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{_('Share')}}
      <span class="badge" style="color:white">{{toShare.length}}</span>
    </a>
    <a @click="toggleShareMode()" v-show="dispVar.listShareMode" class="btn btn-tab btn-half btn-sharp btn-fill length">{{_('Cancel')}}</a>
    
    <a @click="multiShowing()" v-show="dispVar.listShowingMode" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{_('+ Showing')}}
      <span class="badge" style="color:white">{{toShowing.length}}</span>
    </a>
    <a @click="toggleShowingMode()" v-show="dispVar.listShowingMode" class="btn btn-tab btn-half btn-sharp btn-fill length">{{_('Cancel')}}</a>
    -->
    <div v-show="!dispVar.listShareMode">
      <a v-for="ptype in bottomNavPtypes" :data-sub="'filter '+ptype.txt" class="tab-item" v-show="ptype.saletp?propTmpFilter.saletp == ptype.saletp:true" href="javascript:;" :class="{active:computedMapSearchMode == ptype.k}" @click="setSearchMode({k:ptype.k,clear:true})">
        <span :class="'icon fa fa-'+ptype.icon"></span>
        <span class="tab-label">{{_(ptype.txt,ptype.ctx)}}</span>
      </a>
    </div>
    <!--
    <div v-show="!dispVar.listShareMode" class="total pull-left" @click="toggleSaletpSelect()">
      <span class="fa fa-option highlight-red" :class="{flash:loading}"></span>
      <div class="inline">
        <span class="highlight">{{propTmpFilterVals.saletp}} - {{computedMapSearchMode}}</span>
        <span class="fa fa-angle-down"></span>
      </div>
    </div>
    <a href="javascript:;" @click="toggleShareMode()" v-show="!dispVar.listShareMode && propTmpFilter.src=='mls'" class="tab-item-cust pull-right">
      <span class="icon fa fa-rmshare"></span>
      <span class="tab-label">{{_('Share')}}</span>
    </a>
    -->
  </nav>
  <div id="list-container" :class="{'relative':oldVerBrowser}" v-on:scroll="listScrolled" class="content full-height">
    <!--
    <double-go-link :links.sync="links" v-show="appMapMode !== 'fav'")></double-go-link>
    -->
    <div class="list-view-links" v-show="!isPtypeProject()">
      <a href="javascript:;" data-sub="nearby" @click="searchAutocomplete({tp:'gps',saletp:propTmpFilter.saletp, ptype:propTmpFilter.ptype})">
        <div class="icon sprite listLinksIcon nearby"></div>
        <div>{{_('Nearby','indexSearch')}}</div>
      </a>
      <!--
      <a href="javascript:;" data-sub="open house" @click="setSearchMode({k:'Open House',clear:true})">
        <div class="icon sprite listLinksIcon openHouse"></div>
        <div>{{_('Open House','indexSearch')}}</div>
      </a>
      <a href="/1.5/forum">
        <div class="icon"><img src="/img/list-view-forum.png"></div>
        <div>{{_('Forum')}}</div>
      </a>
      <a href="javascript:;" data-sub="sold" v-show="propTmpFilter.saletp == 'sale'" @click="setSearchMode({k:'Sold',clear:true})">
        <div class="icon sprite listLinksIcon sold"></div>
        <div>{{_('Sold')}}</div>
      </a>
      <a href="javascript:;" data-sub="leased" v-show="propTmpFilter.saletp == 'lease'" @click="setSearchMode({k:'Leased',clear:true})">
        <div class="icon sprite listLinksIcon sold"></div>
        <div>{{_('Leased')}}</div>
      </a>!dispVar.isRealtor
      -->
      <a @click="goToStat()" data-sub="trends">
        <div class="icon sprite listLinksIcon stat"></div>
        <div>{{_('Trends')}}</div>
      </a>
      <a href="/1.5/community/filter?d=back" data-sub="community">
        <div class="icon sprite listLinksIcon cmty"></div>
        <div>{{_('Community')}}</div>
      </a>
      <a href="/1.5/promote/mylisting" data-sub="post">
        <div class="icon sprite listLinksIcon postListings"></div>
        <div>{{_('Post')}}</div>
      </a>
    </div>
    <div class="filter-tags" :class="{'filter-on-top': propTmpFilter.ltp == 'projects' || isFilterOnTop()}">
    <div v-show="appMapMode !== 'fav'" id="filter-middle" class="filter-middle bar bar-standard bar-header-secondary short">
      <a @click="getCityList({doSearch:true})" data-sub="filter city" class="city filter">
        <span v-show="propTmpFilterVals.city" class="cityName">{{propTmpFilterVals.city}}</span>
        <span v-show="!propTmpFilterVals.city">{{_('City')}}</span>
        <span :class="[quickFilterMode == 'city' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span>
      </a>
      <a @click="showQuickFilter('ptype2')" data-sub="filter style" class="ptype filter">
        {{_('Style')}}
        <span :class="[quickFilterMode == 'ptype2' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span>
      </a>
      <a @click="showQuickFilter('price')" data-sub="filter price" class="price filter" :class="isPtypeProject()?'hidden-filter':''">
        {{_('Price')}}
        <span :class="[quickFilterMode == 'price' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span>
      </a>
      <!--a v-show="propTmpFilter.src!=='mls'" @click="filterOnTop = 1; showQuickFilter('sort')" class="sort filter">{{_('Sort')}}<span :class="[quickFilterMode == 'sort' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span></a-->
      <a @click="showAdvFilter()" data-sub="advanced search" data-query="place:1" class="sort filter" :class="{'active':hasExtraFilter()}" :class="isPtypeProject()?'hidden-filter':''">
        {{_('More')}}
        <span class="filterCounts" v-if="hasExtraFilter() > 0">{{hasExtraFilter()}}</span>
        <span :class="[quickFilterMode == 'sort' ? 'fa-caret-up':'fa-caret-down']" class="fa"></span>
      </a>
      <div class="project-ad" v-show="isPtypeProject()">
        <span class="project-ad-container" v-show="dispVar.isApp && dispVar.isRealtor">
          <a @click="inquery()" data-sub="precon cooperation">{{_('Project Cooperation')}}</a>
          <span class="icon icon-compose"></span>
        </span>
      </div>
    </div>
    <div id="filter-tag-container" v-show="!isPtypeProject()">
      <div v-show="appMapMode !== 'fav'" id="filter-tag" class="filter-tag short">
        <a class="tag" data-sub="filter tab" data-query="tab:today" @click="selectTag('today')" :class="{'active':propTmpFilter.dom=='0'}">{{_('Today','tag')}}</a>
        <a class="tag" data-sub="filter tab" data-query="tab:sold" @click="selectTag('sold')" :class="{'active':isSoldCondition()}">
          <span v-show="propTmpFilter.saletp=='sale'">{{_('Sold','tag')}}</span>
          <span v-show="propTmpFilter.saletp!=='sale'">{{_('Leased','tag')}}</span>
        </a>
        <a class="tag" data-sub="filter tab" :data-query="'tab:'+featureTag.k" v-for="featureTag in propFeatureTags" v-if="showPropTag(featureTag.k)" @click="selectTag(featureTag.k)" :class="{'active':propTmpFilter[featureTag.field]}">{{_(featureTag.displayVal)}}</a>
      </div>
    </div>
    </div>
    <a id="proplist" class="anchor"></a>
    <div v-if="((propTmpFilter.city || propTmpFilter.cmty) && (stat.avgs && propTmpFilter.saletp=='sale') || (stat.avgr && propTmpFilter.saletp=='lease')) && !isPtypeProject()" class="daily-feeds" data-sub="go trends" :data-query="'city:'+propTmpFilterVals.city" @click="goToStat()">
      <div class="cmty-stats">
        <div class="data">
          <div class="tl">{{propTmpFilterVals.cmty||propTmpFilterVals.city}}</div>
          <div class="nm">{{stat.tsf | monthNameAndDate}} - {{stat.tst | monthNameAndDate}}</div>
        </div>
        <template v-if="stat.avgs && propTmpFilter.saletp=='sale'">
          <div class='data'>
            <div class="val price">${{stat.avgs | propPrice}}</div>
            <div class="nm">{{_('Avg. Price','indexSearch')}}</div>
          </div>
          <div class="data" v-show="stat.cmpSy">
            <span class="caret fa fa-caret-up" v-show="stat.cmpSy > 0"></span>
            <span class="caret fa fa-caret-down" v-show="!(stat.cmpSy > 0)"></span>
            <div class='val price'>{{stat.cmpSy | percentage}}%</div>
            <div class="nm">{{_('YoY')}}</div>
          </div>
        </template>
        <template v-if="stat.avgr && propTmpFilter.saletp=='lease'">
          <div class='data'>
            <div class='val price'>${{stat.avgr | propPrice}}</div>
            <div class="nm">{{_('Avg. Price','indexSearch')}}</div>
          </div>
          <div class="data" v-show="stat.cmpSy">
            <span class="caret fa fa-caret-up" v-show="stat.cmpRy > 0"></span>
            <span class="caret fa fa-caret-down" v-show="!(stat.cmpRy > 0)"></span>
            <div class='val price'>{{stat.cmpRy | percentage}}%</div>
            <div class="nm">{{_('YoY')}}</div>
          </div>
        </template>
      </div>
      <div class="monthly-stats">
        <span>{{_('Trends')}}<span class="icon icon-right-nav" ></span></span>
      </div>
    </div>
    <div class="top-listing-wrapper" v-show="(items.length > 0) && !isPtypeProject()">
      <a class="top-listing" data-sub="charge" @click="showAd($event)">
       <!-- <span class="top-my-listing">{{_('Top Listing','indexSearch')}}</span>
        <span class="icon icon-right-nav" ></span>
        <div class="txt">{{_('20x greater exposure')}}</div> -->
      </a>
      <div class="share-showing-wrapper">
        <a class="showing" href="javascript:;" data-sub="showing" @click="toggleTableMode('showing')" v-show="dispVar.isRealtor">
          <div class="icon sprite16-21 sprite16-6-4"></div>
          <div class="txt">{{_('Showing','showing')}}</div>
        </a>
        <a class="share" href="javascript:;" data-sub="share" @click="toggleTableMode('share')">
          <div class="icon sprite16-21 sprite16-6-1"></div>
          <div class="txt">{{_('Share')}}</div>
        </a>
      </div>
    </div>
    <div class="hidedrop backdrop" :class="{showdrop:showPropTable}"></div>
    <prop-table class='limited-height' v-if="!isPtypeProject()" :list="items" v-show='showPropTable' :action='action' :disp-var="dispVar" :from='"list"'></prop-table>
    <prop-list :has-wechat="hasWechat" :show-top.sync="propTmpFilterVals.cmty||propTmpFilterVals.city" :list="items" :checked-list.sync="checkedList" :disp-var="dispVar" :class="{'paddingTop32':propTmpFilter.ltp == 'projects'}"></prop-list>
    <div v-show="items.length &gt;= 99" style="color:#666; padding: 10px;">{{_('Too many results, input more accurate search criteria.')}}</div>
  </div>
</div>
<div id="propHalfDetail" :class="{active:propHalfDetail}" class="modal">
  <prop-preview-element :prop="curProp" :prop-half-detail="propHalfDetail" :disp-var="dispVar"></prop-preview-element>
</div>
<div :class="{active:halfDrop, high:halfDropHigh}" @click="clearModals()" class="halfDrop"></div>
<div v-cloak id="titleTypeSelect" :class="{active:showTitleTypeSelect}" class="modal modal-60pc">
  <ul class="table-view">
    <li v-for="t in dispVar.propPtypes" :class="{selected:t.v == grpName}" @click="ptypeSelect([t.k, t.v])" class="table-view-cell">
      <span class="icon sprite16-18 sprite16-6-2"></span>
      <span class="group-name">{{t.v}}</span>
      <!-- <span v-show="t.k == propTmpFilter.ptype" class="icon-pull-right fa fa-check"></span> -->
    </li>
  </ul>
</div>
<div id="homeSchoolList" :class="{active:showHomeSchoolList}" class="modal">
  <header class="bar bar-nav"><a @click="showHomeSchoolList = false" class="icon pull-right icon-close"></a>
    <h1 class="title">{{_('Home Schools')}}</h1>
  </header>
  <div class="content">
    <school-list :disp-var="dispVar" :schs.sync="homeSchoolSchs" channel="home-school-retrieved"></school-list>
  </div>
</div>
<contact-realtor v-show="smbMode == 'contact'" :cur-backdrop-id="'propListContactRealtorBackdrop'" :realtor="curRealtor" :show-follow="false"></contact-realtor>
<div id="aptListModal" :class="{active:aptListModal}" class="modal">
  <header class="bar bar-nav"><a @click="closeAptList()" class="icon pull-right icon-close"></a>
    <h1 class="title">{{aptItems.length}} {{_('Properties')}}</h1>
  </header>
  <div class="content">
    <prop-list :has-wechat="hasWechat" :list.sync="aptItems" :disp-var="dispVar"></prop-list>
  </div>
</div>
<div id="bndList" class="modal modal-60pc">
  <header class="bar bar-nav">
    <h1 class="title">{{_('Select Boundary')}}</h1>
  </header>
  <div class="content">
    <div class="table-view">
      <div v-for="(s,$index) in curSch.bns" @click="selBnd(s, true, $index)" class="table-view-cell">
        <span class="grade pull-right"> {{s.gf}}-{{s.gt}} {{_('Grades Only','school-grade')}} </span>
        <span class="name">{{curSch.nm}} {{s.extStr?s.extStr:''}}</span>
        <img src="/img/sch_catholic.png" v-show="s.c"/>
        <img src="/img/sch_ef.png" v-show="s.ef"/>
        <img src="/img/sch_fi.png" v-show="s.fi"/>
      </div>
    </div>
  </div>
  <div class="bar bar-standard bar-footer">
    <button @click="toggleModal('bndList','close')" class="btn btn-block btn-long">{{_('Cancel')}}</button>
  </div>
</div>
<div id="schPreview" :class="{active:schoolHalfDetail}" class="modal">
  <!--school-preview-element :school="curSch" :school-half-detail.sync="schoolHalfDetail" :bnd="curBnd"></school-preview-element-->
  <school-list-element :disp-var="dispVar" :bnd="curSch" :show-sch-info="true"></school-list-element>
</div>
<!--
<prop-need-login :message.sync="message"></prop-need-login>
-->
<share-dialog :w-dl.sync="wDl" :w-sign.sync="wSign" :disp-var="dispVar" :prop="prop"></share-dialog>
<prop-showing-actions :disp-var="dispVar"></prop-showing-actions>
<div id="obj" style='display:none'>list</div>
<div id="user-roles" style='display:none'>{{dispVar.userRoles}}</div>
<div style="display:none">
  <span v-for="(v,k) in strings">{{_(v)}}</span>
</div>
  """

#The one with out modals for native map embed
VIEW 'mapSearchPageNew',->
  js '/js/entry/commons.js'
  css '/css/apps/appMapSearch.css'
  css '/css/apps/cpmBanner.css'
  div id:'vueBody',->
    text """
  <div style="display:none" id="rmPageBarColor">#e03131</div>
  <div style="display:none" class="WSBridge">
    <listing-share-desc :prop.sync='prop' :is-app='dispVar.isApp'></listing-share-desc>
    <div id="wx-title">{{_('RealMaster')}} {{cntTotal}}{{cntTotal>=99?'+':''}} {{_('Results')}}</div>
    <div id="wx-desc"><span v-if="dispVar.sessionUser._id">{{_('Shared by')}} {{dispVar.sessionUser.fnm?dispVar.sessionUser.fnm:dispVar.sessionUser.nm}},{{dispVar.sessionUser.mbl}},{{dispVar.sessionUser.eml}}</span><span v-else="v-else">{{_('RealMaster Multiple Properties Sharing')}}</span></div>
    <div id="wx-image"><span v-if="dispVar.sessionUser">{{dispVar.sessionUser.avt || '/img/logo.png'}}</span><span v-else="v-else">/img/logo.png</span></div>
    <div id="share-image">{{shareImage}}</div>
    <div id="share-data">{{shareData}}</div>
    <div id="m-share-title-en">RealMaster {{shareList.length}} Properties</div>
    <div id="m-share-desc-en">{{sessionUserContact?'Shared by':''}} {{sessionUserContact}}</div>
    <div id="m-share-title">{{_('RealMaster')}} {{shareList.length}} {{_('Properties','property')}}</div>
    <div id="m-share-desc">{{sessionUserContact?_('Shared by'):''}} {{sessionUserContact}}</div>
    <div id="m-share-data">{{mShareData}}</div>
    <div id="m-share-image">{{mShareImage}}</div>
    <div id="m-share-url">http://{{dispVar.reqHost}}/1.5/search/prop?{{mShareData}}</div>
  </div>
  <!--
  <div id="propDetailModal" class="modal">
    <prop-detail :bnds.sync="bnds" :schs.sync="schs" :loading.sync="loading"></prop-detail>
  </div>
  <div id="schoolDetailModal" :class="" class="modal">
    <school-detail :sch="curSch" :bnd="curBnd"></school-detail>
  </div>
  <city-select-modal :show-subscribe="true" :need-loc="true" :cur-city.sync="dispVar.userCity"></city-select-modal>
  -->
  <prop-fav-actions :loading.sync="loading" :disp-var="dispVar"></prop-fav-actions>
  #{@ckup('MapMainWindowViewText',{})}
    """
  # js @googleapiJs
  # js 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js'
  # css 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css'
  # text ckup 'mapboxJs'
  coffeejs {vars:{isRealtor:@isRealtor,propsLimit:@propsLimit,appMode:@appmode}}, ->
    null

  # js '/js/map/mapbox.min.js'
  js '/js/localCache.min.js'
  # js '/js/indexHistCtrl.min.js'
  js '/js/lz-string.min.js'
  # js '/js/google/markerclusterer.js'
  # js '/js/calculate.min.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/appMapSearchNew.js'
