# TODO: this file no longer needed
helpers = INCLUDE 'lib.helpers'
statHelper = INCLUDE 'libapp.stat_helper'
libP = INCLUDE 'libapp.properties'
libUser = INCLUDE 'libapp.user'

PredStat = COLLECTION 'vow','pred_stat'
Properties = MODEL 'Properties'
Evaluation = COLLECTION 'chome','evaluation'

UserModel = MODEL 'User'
getWXConfig = DEF 'getWXConfig'
# userIDQuery = DEF 'userIDQuery'
getConfig = DEF 'getConfig'
filterPrediction = DEF 'filterPrediction'
debug = DEBUG()
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
GroupModel = MODEL 'Group'

propTranslate = INCLUDE 'libapp.propertiesTranslate'


PROP_FIELDS = {_id:1,unt:1,addr:1,uaddr:1,dom:1,lp:1,sp:1,onD:1,offD:1,\
  sldd:1,pred_his:1,lpr:1,bdrms:1,tbdrms:1,tbthrms:1,bthrms:1,\
  tgr:1,tax:1,gr:1,taxyr:1,apt_num:1,ptype:1,ptype2:1,sid:1,pred_mt:1,\
  pred_sp:1,pred_spsd:1,pred_err:1,pred_ok:1,pic:1,pho:1,phosrc:1,phomt:1,\
  ddfID:1,picUrl:1,city:1,lst:1,sqft1:1,sqft2:1,depth:1,front_ft:1,\
  lotsz_code:1,irreg:1,tp:1,br_plus:1,st:1,cnty:1,prov:1,cmty:1,lat:1,\
  lng:1,dpred:1}

predictCities = {}
predictCitiesList = []
initPredictStat = (cb)->
  statFields = {_id:1,stdp:1,diffp:1}
  now = new Date()
  date = helpers.dateFormat(statHelper.getLastMonth(now),'YYYY-MM-DD')
  q = {'_id.date':{$gt:date}}
  lastDate = null
  PredStat.findToArray q,{fields:statFields,sort:{'_id.date':-1,count:-1}},(err,ret)->
    ret ?= []
    for r in ret
      return cb() if lastDate and (lastDate isnt r._id.date)
      lastDate = r._id.date
      predictCities[r._id.prov+':'+ r._id.city] = {stdp:r.stdp,diffp:r.diffp}
      predictCitiesList.push r
    cb()

calcPredAccuracy = (prop) ->
  return 'UNKNOWN' unless cityPredData = getPredictCity prop
  cityD = cityPredData.stdp * prop.pred_sp
  if prop.pred_spsd < (0.67 * cityD)
    pred_accuracy = 'HIGH'
  else if prop.pred_spsd > (1.37 * cityD)
    pred_accuracy = 'LOW'
  else
    pred_accuracy = 'MEDIUM'
  pred_accuracy
DEF 'calcPredAccuracy', calcPredAccuracy



getPredictCity = (prop)->
  return Object.assign {},predictCities[prop.prov+':'+prop.city]
DEF 'getPredictCity',getPredictCity

getPredictCitiesList = ->
  return predictCitiesList
DEF 'getPredictCityList',getPredictCitiesList


PLSWAIT '_predict_cities'
initPredictStat ->
  PROVIDE '_predict_cities'

APP '1.5'
APP 'prop',true

#/1.5/prop/prediction?id=TRBN11111
GET 'prediction',(req,resp)->
  if req.getDevType() isnt 'app'
    return resp.ckup 'generalError',{err:'Error Loading Page'}
  if not id = req.param('id')
    return resp.ckup 'generalError',{err:'No ID specified'}
  req.setupL10n() unless req._ab and req._
  if lang = req.param('lang') and lang in LANGUAGE_LIST
    req.setLocale lang
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->
    # Properties.findOne {_id: id}, {fields:PROP_FIELDS}, (err, prop)->
    Properties.findOneByID id, {projection:PROP_FIELDS}, (err, prop)->
      return resp.ckup 'generalError',{err:'Error Loading Page'} if err
      # translateRmPropDetailOpt = {
      #   _:req._,_ab:req._ab,l10n:req.l10n,
      #   locale:req.locale,headers:req.headers,cookies:req.cookies}
      prop = propTranslate.translate_rmprop_detail({locale:lang,transDDF:req.getConfig('transDDF')}, prop)
      opt =
        hasRoleAdmin:req.hasRole('_admin')
        isAllowedVipUser:req.isAllowed('vipUser')
        apisrc:req.param('apisrc')
        # configShowSoldPrice:getConfig('showSoldPrice')
      # libP.filterPropSpLstField(opt, prop) #get showsoldprice feild etc
      if prop.dpred is 'N'
        return resp.ckup 'generalError',{err:'We do not show estimation for this property'}
      statFields = {_id:1,count:1,stdp:1,diffp:1,sldaskp:1}
      pageField = {noUserModal:1,wxcfg:{},noAppCss:0,noRatchetJs:1,noAngular:1}
      GroupModel.isInGroup {uid:user?._id,groupName:':inReal'},(err,isRealGroup)->
        debug.error 'GroupModel.isInGroup',err if err
        Properties.addPropAdditionalFields({
          user,prop,
          isAllowedPropAdmin:opt.hasRoleAdmin,
          isAllowedVipUser:opt.isAllowedVipUser,
          # configShowSoldPrice,
          l10n:(a,b)->req.l10n(a,b),
          _ab:(a,b)->req._ab(a,b),
          apisrc:opt.apisrc,
          isRealGroup,
          simplfyPtp:false})
        try
          prop = await Properties.addPropFavFields {props:prop,detail:true,uid:user?._id}
        catch err
          debug.error err,'addPropFavFields'
        unless req.isAllowed 'predict', user
          isAllowedShowSoldPrice = req.isAllowed 'showSoldPrice',user
          filterPrediction isAllowedShowSoldPrice,user,prop,->
            # return resp.send ok:1,result:predictCitiesList,prop:prop
            return resp.ckup 'propPredictionPage',{result:predictCitiesList,prop:prop},'_',pageField
          return null
        dates = []
        now = new Date()
        date = helpers.dateFormat(statHelper.getLastMonth(now),'YYYY-MM-DD')
        q = {'_id.date':{$gt:date}}
        map = {}
        dates = []
        PredStat.findToArray q,{fields:statFields,sort:{'_id.date':-1}},(err,ret)->
          ret ?= []
          for r in ret
            dates.push(r._id.date) if dates.indexOf(r._id.date) < 0
            key=(''+r._id.prov+r._id.city).replace(/\W/g,'').toLowerCase()
            map[key] ?= {c:r._id.city,p:r._id.prov,l:[],count:r.count}
            map[key].l.push {dt:r._id.date,count:r.count,stdp:r.stdp,diffp:r.diffp,avgsp:r.avgsp,avglp:r.avglp,sldaskp:r.sldaskp}
          list = []
          for k,v of map
            list.push v
          list.sort (a,b)->
            return b.count - a.count
          isAllowedShowSoldPrice = req.isAllowed 'showSoldPrice',user
          filterPrediction isAllowedShowSoldPrice,user,prop,->
            # resp.send {ok:1, result: {list:list,dates:dates},prop:prop}
            return resp.ckup 'propPredictionPage',{result:{list:list,dates:dates},prop:prop},'_',pageField
          return null

# GET 'services',(req, resp)->
#   resp.ckup 'services',{},'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

VIEW 'propPredictionPage',->
  div id:'vueBody',->
    text '''
    <app-prop-prediction-page></app-prop-prediction-page>
    '''
  coffeejs {vars:{result:@result,prop:@prop}},->
    null

  js '/js/entry/commons.js'
  js '/js/Chart-2.8.0.min.js'
  js '/js/Chart.BoxPlot.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/appPropPredictionPage.js'

