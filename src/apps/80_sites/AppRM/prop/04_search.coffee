i18n = INCLUDE 'lib.i18n'
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'

# debug = console.log

# maskListings = DEF 'maskListings'
# maskListing = DEF 'maskListing'
#l10nDetail = DEF 'l10nDetail'
# l10nList = DEF 'l10nList'
# accessAllowed = DEF '_access_allowed'
setLang = DEF 'setLang'
UserModel = MODEL 'User'
# findListingByAddrOrId = DEF 'findListingByAddrOrId'
LIST_PROP_FIELDS = DEF 'LIST_PROP_FIELDS'
getConfig = DEF 'getConfig'
config = CONFIG(['share','serverBase'])
debug = DEBUG()
gShareHostNameCn = config.share?.hostNameCn or 'realmaster.cn'
gShareHost = config.share?.host
# getWXConfig = DEF 'getWXConfig'
# MAP_PROP_FIELDS = DEF 'MAP_PROP_FIELDS'
# User = COLLECTION 'chome', 'user'
# UserProfile = COLLECTION 'chome', 'user_profile'
# UserListings = COLLECTION 'chome', 'user_listing'
libP = INCLUDE 'libapp.properties'
Properties = MODEL 'Properties'
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
GroupModel = MODEL 'Group'

VIEW 'listSearchPage',->
  coffeejs {vars:{id:@id,share:@share}}, ->
    null
  div id:'vueBody',->
    text """<app-list-search></app-list-search>"""
  css '/css/sprite.min.css'
  css '/css/svgsprite.min.css'
  css '/css/swiper.min.css'
  js '/js/Chart-2.5.0.min.js'
  js '/js/lz-string.min.js'
  # js '/js/localCache.min.js'
  # js '/js/indexHistCtrl.min.js'
  js '/js/stdfn.min.js'
  js '/js/calculate.min.js'
  js '/js/entry/commons.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/appListSearch.js'



sendError = (req,resp,e)->
  debug.error req.body,e
  resp.ckup 'generalError', {title:req.l10n('Error'),err:e.toString()}


checkIndexSearchInputRedirects = (req,resp,fnError, message, cb)->
  #validate query param
  return fnError req.l10n "No ID specified" unless (ml_num = message?.r or req.param 'id')?
  ml_num = ml_num.toString().trim() # req.param may return an object instead of string
  # special features
  if (ml_num is '))*')
    unless req.isAllowed 'devGroup'
      return cb '/1.5/index'
    return cb '/1.5/admin/toolsList'
  if (ml_num is '**1') and (req.session?.get('user')) # remove flwng
    return UserModel.deleteFollowing req,->
      return cb '/1.5/index'
  if (ml_num is '**2')
    return UserModel.deleteRoles req,->
      cb '/1.5/index'
  if (ml_num is '**3') and (req.session?.get('user')) # remove flwng
    return UserModel.deleteWXuid req,->
      return cb '/1.5/index'
  if (ml_num is '**4')
    return cb '/1.5/user/wecardManage'
  # reload i18n table
  if (ml_num is '))1')
    return i18n.reload ->
      return cb '/1.5/index'
  # TODO: ))2 load i18n page
  if (ml_num is '))2')
    return cb '/sys/l10n'
  if (ml_num is '))3')
    return cb '/service_check'
  #  accessAllowed 'sysConfig'
  # try payment
  if (ml_num is '))5')
    return cb '/bill/pay/stripe'

  if (ml_num is '))edm')
    return cb '/sys/edm'
  if (ml_num is '))route')
    return cb '/1.5/transit/route'

  if (ml_num is '))ep')
    return cb '/sys/edm/report'
  if (ml_num is '))crm')
    unless req.isAllowed 'productSales'
      return cb '/1.5/index'
    return cb '/1.5/crm/index'

  # try coverPage
  ###
  if (ml_num is '))6')
    return req.session.set 'coverPage',3,->
      return resp.redirect '/1.5/index'
  if (ml_num is '))7')
    return req.session.set 'coverPage',null,->
      return resp.redirect '/1.5/index'
  ###
  if (ml_num is '))8cn')
    return cb '/sitejump?r=cn'
  if (ml_num is '))8ca')
    return cb '/sitejump?r=ca'
  if (ml_num is '))8')
    return cb '/sitejump?r=null'
  # analytics page
  if (ml_num is '))9')
    return cb '/analytics'
  if (ml_num is '))10')
    return cb '/analytics_spevent'
  # admin
  if (ml_num is '))a')
    return cb '/sys/user'
  # user switch
  if (ml_num is '))s')
    return cb '/sys/switch'
  # admin
  if (ml_num is '))b')
    return cb '/1.5/banner/manage'
  # admin
  if (ml_num is '))e')
    return cb '/evaluation'
  # test page
  if (ml_num is ')))')
    return cb '/wechatauth'
  # diagnosePage
  if (ml_num is ':;(')
    return cb '/diagnosePage'

  if (ml_num is '***')
    return cb '/1.5/test'

  if (ml_num is '*https')
    url = req.fullUrl '/1.5/index'
    if /^https/.test url
      url = 'http' + url.slice 5
    else if /^http\:/.test url
      url = 'https' + url.slice 4
    else
      debug.error "checkIndexSearchInputRedirects Unknown url protocol #{url}"
      req.logger?.error "Unknown protocol #{url}"
    return cb url

  # if (ml_num is '**p')
  #   unless req.isAllowed 'predict'
  #     return cb '/1.5/index'
  #   return cb '/1.5/prop/predlist'
  # NOTE: old api no loger supported, use new
  if /^\)\)?\d\(?\($/.test(ml_num)
    # unless req.isAllowed 'devGroup'
    return cb '/1.5/index'
    # if m = ml_num.match(/^\)(\d)\($/)
    #   if m[1] is '0'
    #     url = 'realmaster.com/app/apptest'
    #   else
    #     url = "d#{m[1]}.realmaster.com/app/apptest"
    # else
    #   url = 'realmaster.com/app/apptest'
    #   switch req.host?.toLowerCase()
    #     when "ch.#{gShareHostNameCn}"
    #       url = "c1.#{gShareHostNameCn}/app/apptest"
    #     when "c1.#{gShareHostNameCn}"
    #       url = "ch.#{gShareHostNameCn}/app"
    #     else
    #       console.log "unsupported debug source host:#{req.host.toLowerCase()}"
    # return cb("http://#{url}?sv=" + req.cookies['apsv'])
  cb null, ml_num

checkIndexSearchInput = (req,resp,fnError, message, cb)->
  #validate query param
  return fnError req.l10n "No ID specified" unless (ml_num = message?.r or req.param 'id')?
  ml_num = ml_num.toString().trim() # req.param may return an object instead of string
  # check if ID list
  if (list = ml_num.split(',')).length > 1
    # multiple ID
    for l,i in list
      l = l.trim()
      unless /^[a-zA-Z0-9-]+$/i.test l #allow search rmid
        return fnError req.l10n "Alphabeta and numbers only please"
      list[i] = l.toUpperCase()
    # if not req.param('share')
    #   # 没有share，查询的是inputSearch，此时输入需要是string
    #   list = list.join(',')
    cb list
  else
    #if input is chinese
    if /[\u4e00-\u9fa5]+/.test(ml_num)
      # console.log ml_num
      return fnError "", ml_num
    unless /^\@?[a-z0-9\-\s\'\#\&]+$/i.test(ml_num)
      return fnError req.l10n "Alphabeta and numbers only please"
    cb ml_num

process_listingList_req = ({req, resp, message}, cb)->
  # getWXConfig req,req.fullUrl(),(err,cfg)->
  setLang req
  error = (e, ml_num)-> #sendError req,resp,e
    resp.ckup 'generalError', {err:e}
  done = (p)->
    resp.ckup "listSearchPage",p,"_",{noAngular:1,noUserModal:1,noRatchetJs:1}
  checkIndexSearchInputRedirects req,resp,error,message, (redirectUrl, ml_num)->
    if redirectUrl
      return resp.redirect redirectUrl
    UserModel.appAuth {req,resp},(user)->
      title = if Array.isArray(ml_num) then "#{ml_num.length} #{req.l10n('Properties')}" else req.l10n('Property Share')
      done {title:title,id:message?.r,share:req.param('share')}
DEF 'process_listingList_req', process_listingList_req

# getFavIDs = (user,cb)->
#   UserModel.findProfileById user._id,{},(err,uProfile)->
#     cb err,uProfile?.favPrps

# rmFavExp = (user,ids)->
#   toRmIDs = {}
#   for id in ids
#     toRmIDs["favPrps.#{id}"] = 1
#   update = $unset:toRmIDs
#   UserProfile.updateOne {_id:user._id},update,(err)->
#     if err then debug.error err


APP '1.5'
APP 'search',true

# GET '/search/prop'
#@return list view of results
APP 'prop',true

MINIAPP_PROP_FIELDS = null
# TODO: to be optimized  Fred
#'/1.5/search/prop/list?id='''

# TODO: depreciate this api, very rare used
#input search
POST 'list', (req,resp)->
  isMLS = (srhKeys)->
    ('string' is typeof srhKeys) and (/^\w{1}\d+$/.test srhKeys)
  error = (e)-> resp.send {e:e.toString(),ok:0,ec:3}
  locale = req.param('locale') or req.param('lang')
  if locale and (locale in LANGUAGE_LIST)
    req.setLocale locale
  checkIndexSearchInputRedirects req,resp,error,null, (redirectUrl, ml_num)->
    if redirectUrl
      return resp.send {redirect:redirectUrl}
    checkIndexSearchInput req,resp,error, null, (srhKeys)->
      UserModel.appAuth {req,resp}, (user)->
        try
          isRealGroup = await GroupModel.isInGroup {uid:user?._id, groupName:':inReal'}
        catch err
          debug.error 'GroupModel.isInGroup',err,' uid:',user?._id

        pageNum = req.param('p')
        if pageNum and isNaN(parseInt(pageNum))
          return error req.l10n 'Incorrect Page Number'
        pageNum = parseInt(pageNum) or 0
        limit = parseInt(req.param('limit')) or 20
        pageNum = pageNum
        offSet = limit * pageNum
        opts = libP.getBaseForPropList req,user
        opts = Object.assign {
          input: srhKeys
          limit: limit
          pageNum: pageNum
          skip: offSet
          fields:LIST_PROP_FIELDS #NOTE: causes bug in sql, non-exist fields
          # DDF_DISPLAY_POLICY:getConfig('DDF_DISPLAY_POLICY')
          user
          isRealGroup
        }, opts
        # NOTE: some user shared merged prop, so allow search for now, called in searchByIds
        # TODO: remove this line after all shared prop merged
        # libP.genSearchMergeOrDelProps opts
        # console.log 'opts++++',opts
        if opts.apisrc is 'miniapp.sold'
          unless MINIAPP_PROP_FIELDS
            MINIAPP_PROP_FIELDS = Object.assign({}, LIST_PROP_FIELDS,{sp:1,sldd:1})
          opts.fields = MINIAPP_PROP_FIELDS
        fn = if req.param('share') then 'searchByIds' else 'inputSearch'
        Properties[fn] opts,(err, ret)->
          #ret:{list:[],cnt:0}
          return error err if err # never ignore error
          ret.list ?= []
          # configShowSoldPrice = getConfig('showSoldPrice')
            # NOTE: setted in addPropAdditionalFields
            # libP.setUpUserFav(user._id, ret.list) if user?._id and ret.list.length
          isAllowedPropManage = req.isAllowed('propManage')
          Properties.inputRMListingsSearch {isAllowedPropManage,limit,uid:user?._id,input:srhKeys,skip: offSet,props:ret.list},(err,rmProps)->
            return cb(err) if err

            ret.list = rmProps
            ret.cnt = ret.cnt or rmProps.length

            req.setupL10n() unless req._ab
            for p in ret.list
              Properties.addPropAdditionalFields({
                user,prop:p,
                isAllowedPropAdmin:opts.isPropAdmin,
                isAllowedVipUser:opts.isVipUser,
                # configShowSoldPrice,
                l10n:(a,b)->req.l10n(a,b),
                _ab:(a,b)->req._ab(a,b),
                apisrc:opts.apisrc,
                isRealGroup,
                simplfyPtp:false})
              libP.setUpPropLogin(opts.isShare, user, p)
              libP.genListingPicReplaceUrls(p, req.isChinaIP(), {use3rdPic:config.serverBase?.use3rdPic,isPad:req.isPad(),shareHostNameCn:config.share?.hostNameCn,useThumb:1})
            try
              props = await Properties.addPropFavFields {props:ret.list,uid:user?._id}
            catch err
              debug.error err,'checkIndexSearchInputRedirects addPropFavFields'
            listRet = {
              ok:1,
              resultList:props,
              nextP:pageNum+1,
              offSet:offSet,
              cnt:(ret?.cnt or 0),
              cntRemain:(ret?.cntRemain or 0),
              pSize:limit,
              title:srhKeys,
              searchAfter:ret?.searchAfter,
            }
            listRet.resultList = propTranslate.translate_rmprop_list(req, listRet.resultList)
            if Array.isArray srhKeys and srhKeys[0].substr(0,3) is 'RM1'
              listRet.barTitle = listRet.title = req.l10n('RealMaster') + ' ' + req.l10n('Exclusive-Listing') + ' ' +  req.l10n('Market') + ' ' + req.l10n('Report')
              timeNow = new Date()
              formatDate = timeNow.getFullYear() + '.' + (timeNow.getMonth() + 1) + '.' + timeNow.getDate()
              listRet.desc = formatDate + ' ' + req.l10n('Total') + ' ' + (ret?.list?.length or 0) + ' ' + req.l10n('Exclusive Listings, Assignments, Rental Listings and MLS Promoted Listings.')
            return resp.send listRet


# [view] return index-search-list-result view
# /1.5/search/prop
# -------- paramters --------
#         ?id=E1,E2,E3    #property ml_num
#         &lang=zh-cn     #language
#         &uid=aaaaa      #share user id || null
#         &share=1        #from share || false, if true can access idx = x
#         &dl=1           #show get app (share must be set)

GET (req,resp)->
  return process_listingList_req({req,resp})