mapServer =  INCLUDE 'lib.mapServer'
UserModel = MODEL 'User'
PropertiesModel = MODEL 'Properties'
GroupModel = MODEL 'Group'
helpers = INCLUDE 'lib.helpers'
{respError} = INCLUDE 'libapp.responseHelper'
debug = DEBUG()
APP '1.5'
APP 'map',true

MAX_LIMIT = 150

GET 'searchLocation', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  isApp = req.getDevType() is 'app'
  apikey = mapServer.getMapboxAPIKey {isApp,isChina:req.isChinaIP()}
  strings = {}
  strings.searchPlaceHolder = req.l10n('Search','map')
  # strings.address=req.l10n('Address')
  # strings.city=req.l10n('City')
  # strings.prov=req.l10n('Province')
  # strings.country=req.l10n('Country')
  # strings.confirmMessage=req.l10n('You entered an address manually, please make sure the marker is already moved to the correct location.','map')
  # strings.addressFormatError=req.l10n('Wrong address format. Eg: 123 street,Toronto,Ontario,Canada','map')
  # debug.log JSON.stringify(strings)
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.ckup "map/mapboxSearchLocation",strings,'_',{mapboxkey:apikey}

GET 'showLocOnMap', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  isApp = req.getDevType() is 'app'
  apikey = mapServer.getMapboxAPIKey {isApp,isChina:req.isChinaIP()}

  return resp.ckup "map/mapboxShowLocOnMap",{},'_',{mapboxkey:apikey}
# /1.5/map/notesMap
GET 'notesMap',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    isApp = req.getDevType() is 'app'
    apikey = mapServer.getMapboxAPIKey {isApp,isChina:req.isChinaIP()}
    ctx = {}
    if req.param('from') is 'claim'
      ctx.title = 'Deals'
    return resp.ckup 'propNotes/notesMap', ctx,'_',{mapboxkey:apikey,hasPageCss:1}

VIEW 'webMap',->
  coffeejs {vars:{
    share:@share,
    bar:@bar,
    tab:@tab,
    isOldVersion:@isOldVersion,
    }}, ->
      null
  div id:'vueBody',->
    text '<web-map></web-map>'
  text ckup 'mapboxJs'
  js '/js/map/mapbox.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/webMap.js'

# /1.5/map/webMap
GET 'webMap',(req, resp)->
  loc = req.param 'loc'
  lat = req.param 'lat'
  lng = req.param 'lng'
  tab = req.param 'tab'
  if lng and (url = helpers.needRedirectUrl lng,req.url)
    return resp.redirect url
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    isApp = req.getDevType() is 'app'
    apikey = mapServer.getMapboxAPIKey {isApp,isChina:req.isChinaIP()}
    if loc
      lat = loc[0]
      lng = loc[1]
    if (lat and (not helpers.isLatitude lat)) or (lng and (not helpers.isLongitude lng))
      return resp.ckup 'generalError', {err_tran:req.l10n('Location information is wrong')}
    isOldVersion = not req.verGTE('6.2.0')
    return resp.ckup 'webMap', {isOldVersion},'_',{noAngular:true,mapboxkey:apikey} if tab isnt 'stigma'
    UserModel.findByTskDnAndId user._id,'creepyHouse',(err,found)->
      if err then debug.error err
      if found
        req.session.set "rhLimit",MAX_LIMIT,->
      resp.ckup 'webMap', {isOldVersion},'_',{noAngular:true,mapboxkey:apikey}

# /1.5/map/selectMap
GET 'selectMap',(req, resp)->
  id = req.param 'id'
  lat = req.param 'lat'
  lng = req.param 'lng'
  isPopup = req.param 'isPopup'
  d = req.param 'd'
  if lng and (url = helpers.needRedirectUrl lng,req.url)
    return resp.redirect url
  if id
    prop = await PropertiesModel.findOneByIDAsync id,{useMlatlng:1,daddr:1,lat:1,lng:1,cmty:1,city:1,prov:1,cnty:1,addr:1,zip:1}
  prop ?= {lat, lng}
  return resp.ckup 'generalError', {err_tran:req.l10n('Address not found')} unless prop?.lat and prop?.lng
  for k,v in prop
    prop[k] = encodeURIComponent(v)
  isNewVersion = not req.verGTE('6.2.8')
  emurl = req.exMapURL()
  return resp.ckup 'map/selectMap', {jsonProp:helpers.encodeObj2JsonString(prop),prop,\
    isNewVersion,emurl,isPopup,d},'_',{noAngular:true}


