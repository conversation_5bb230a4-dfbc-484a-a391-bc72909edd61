###
Hidden page for internal test/diagnose purpose
###

News = COLLECTION 'chome', 'news'
# verifier = require 'email-domain-verifier' #很慢，准确,适合批量记录log
# legit = require('legit') #很快，不准确，适合发送及时邮件
# vld = require '@devmehq/email-validator-js' #很快，一般准确,可以使用

APP '1.5'
GET 'test',(req,resp)->
  resp.ckup 'test'

# POST 'test',(req,resp)->
#   # response = await verifier.verifyEmailDomain req.body.email
#   # response = await legit(req.body.email)
#   response = await vld.verifyEmail({
#     emailAddress: req.body.email, verifyMx: true,
#     verifySmtp: true, timeout: 3000
#   })
#   console.log 'ret => ',response
#   resp.send response

GET 'videotest',(req,resp)->
  resp.ckup 'videotest'

VIEW 'videotest',->
  text """
  <link href="/css/video-js.min.css" rel="stylesheet">
  <script src="/js/video/video.min.js"></script>
  <script src="/js/video/youtube.min.js"></script>
  """
  header class: 'bar bar-nav', ->
    a class: 'icon icon-left pull-left', href: '/1.5/index'
    h1 class: 'title',-> "video test"
  div class:'content', ->
    div class:'video-wrapper', style:"width:400px; height:300px; position:fixed; top: 44px;overflow: hidden;z-index: 10;",->
      # text """
      # <video
      #   id="vid1"
      #   class="video-js vjs-default-skin"
      #   controls
      #   autoplay
      #   data-setup='{ "fluid": true, "techOrder": ["youtube"], "sources": [{ "type": "video/youtube", "src": "https://www.youtube.com/watch?v=xjS6SftYQaQ"}] }'
      # >
      # </video>
      # """
      text """
      <iframe class="video-wrapper" width="560" height="315" src="https://www.youtube.com/embed/5qap5aO4i9A?playsinline=1&rel=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; " sandbox="allow-same-origin allow-presentation allow-scripts allow-forms allow-modals" allowfullscreen></iframe>
      """
      # text """
      # <video
      # data-setup='{"fluid": true}'
      # id="my-player"
      # class="video-js"
      # controls
      # preload="auto"
      # poster="//vjs.zencdn.net/v/oceans.png"
      # data-setup='{}'>
      #   <source src="//vjs.zencdn.net/v/oceans.mp4" type="video/mp4"></source>
      #   <source src="//vjs.zencdn.net/v/oceans.webm" type="video/webm"></source>
      #   <source src="//vjs.zencdn.net/v/oceans.ogv" type="video/ogg"></source>
      #   <p class="vjs-no-js">
      #     To view this video please enable JavaScript, and consider upgrading to a
      #     web browser that
      #     <a href="https://videojs.com/html5-video-support/" target="_blank">
      #       supports HTML5 video
      #     </a>
      #   </p>
      # </video>
      # """
    div id:'page-content',style:"margin-top:400px; padding: 10px 15px 10px 15px;",->
      text """
      <script>
      var wrapper = document.querySelectorAll('.video-wrapper');
      var index = 0, length = wrapper.length;
      var width = window.innerWidth+'px';
      var height = window.innerWidth*0.5625120773+'px';
      for ( ; index < length; index++) {
          wrapper[index].style.width = width;
          wrapper[index].style.height = height;
      }
      var wrapper2 = document.querySelector('#page-content');
      wrapper2.style.marginTop = window.innerWidth*0.5625120773+'px';
      </script>
      <p>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. In iaculis nunc sed augue lacus viverra vitae congue eu. Nisl nunc mi ipsum faucibus. Facilisis magna etiam tempor orci eu lobortis elementum nibh. Nisl condimentum id venenatis a. Volutpat ac tincidunt vitae semper quis lectus nulla at. Integer vitae justo eget magna fermentum iaculis eu non. Consectetur adipiscing elit pellentesque habitant morbi. A pellentesque sit amet porttitor. Massa massa ultricies mi quis hendrerit. Elementum eu facilisis sed odio morbi quis. Tincidunt ornare massa eget egestas purus viverra accumsan in.
      </p>
      <p>
      Massa sed elementum tempus egestas sed sed. Est lorem ipsum dolor sit amet consectetur adipiscing. Sit amet facilisis magna etiam tempor orci. Leo a diam sollicitudin tempor id eu nisl nunc. A pellentesque sit amet porttitor eget. Consequat ac felis donec et odio pellentesque diam volutpat commodo. Commodo odio aenean sed adipiscing. Ut aliquam purus sit amet luctus venenatis lectus. In vitae turpis massa sed elementum tempus egestas sed sed. Enim ut tellus elementum sagittis vitae et leo duis ut. Duis convallis convallis tellus id interdum. Nulla aliquet porttitor lacus luctus accumsan tortor posuere ac ut. At ultrices mi tempus imperdiet nulla malesuada. Orci eu lobortis elementum nibh tellus molestie nunc. Mi in nulla posuere sollicitudin aliquam ultrices sagittis. Sit amet mauris commodo quis imperdiet massa tincidunt. Venenatis cras sed felis eget velit aliquet sagittis id consectetur. At erat pellentesque adipiscing commodo elit at imperdiet dui. Quisque id diam vel quam elementum.
      </p>
      <p>
      Nunc lobortis mattis aliquam faucibus purus in massa tempor nec. Cras fermentum odio eu feugiat pretium. Pellentesque adipiscing commodo elit at imperdiet. Feugiat vivamus at augue eget arcu dictum varius. Non odio euismod lacinia at quis risus sed vulputate odio. Tempus imperdiet nulla malesuada pellentesque elit eget gravida cum. Cum sociis natoque penatibus et magnis dis parturient montes. Mauris in aliquam sem fringilla ut morbi tincidunt. Eleifend quam adipiscing vitae proin. Donec massa sapien faucibus et. Eu nisl nunc mi ipsum faucibus vitae aliquet nec ullamcorper. Sit amet tellus cras adipiscing enim eu turpis. Pellentesque pulvinar pellentesque habitant morbi. Malesuada nunc vel risus commodo viverra maecenas. Habitant morbi tristique senectus et. Non pulvinar neque laoreet suspendisse interdum consectetur libero. Interdum consectetur libero id faucibus nisl tincidunt eget. Fames ac turpis egestas integer eget aliquet nibh praesent.
      </p><p>
      Etiam dignissim diam quis enim lobortis scelerisque fermentum. Urna id volutpat lacus laoreet non curabitur gravida arcu ac. Mattis vulputate enim nulla aliquet porttitor lacus. Venenatis cras sed felis eget velit aliquet sagittis id. Ullamcorper dignissim cras tincidunt lobortis feugiat vivamus at augue eget. Nunc sed blandit libero volutpat sed. Magna fringilla urna porttitor rhoncus dolor purus. Ultricies tristique nulla aliquet enim tortor at auctor urna nunc. Faucibus interdum posuere lorem ipsum dolor sit. Metus dictum at tempor commodo ullamcorper a lacus vestibulum.
      </p><p>
      Et ultrices neque ornare aenean euismod elementum nisi quis. Id cursus metus aliquam eleifend mi in nulla. Pretium aenean pharetra magna ac placerat vestibulum lectus mauris ultrices. Pretium lectus quam id leo in vitae. Habitasse platea dictumst vestibulum rhoncus est pellentesque elit ullamcorper. Risus commodo viverra maecenas accumsan lacus vel facilisis volutpat. Purus sit amet luctus venenatis lectus. A cras semper auctor neque vitae. Tincidunt eget nullam non nisi est sit amet facilisis magna. Laoreet sit amet cursus sit amet dictum. Mauris pellentesque pulvinar pellentesque habitant morbi tristique senectus. Sit amet consectetur adipiscing elit.
      </p>
      """

VIEW 'test', ->
  js '/js/jquery-1.10.1.min.js'
  header class: 'bar bar-nav', ->
    a class: 'icon icon-left pull-left', href: '/1.5/index'
    h1 class: 'title',-> 'Diagnose'
  div class:'content', ->
    div ->
      div id:'share-title', -> 'share title with id'
      div id:'share-desc', -> 'share desc without special chars'
      div id:'share-desc-back', -> 'share desc with special chars (*^&%^&*)'
      div id:'share-url', -> 'https://realmaster.com/getapp'
    input id:'urlInput', type:'text',placeholder:'url to go'
    span id:'hiddenValue', -> text 'blahblahblah'
    a id:'click', onclick:'myFunction()', -> text 'tbrowser'
    br()
    a id:'click2', onclick:'myFunction2()', -> text 'closeAndRedirect parent'
    br()
    a id:'click3', onclick:'myFunction3()', -> text 'closeAndRedirectRoot parent'
    br()
    a id:'click4', onclick:'myFunction4()', -> text 'rmCall'
    br()
    a id:'click5', onclick:'myFunction5()', -> text 'getpagecontent /1.5/test'
    br()
    a id:'click6', onclick:'myFunction6()', -> text 'rmsrv.showInMap'
    br()
    # input id:'calendarInput', value:'saveEvent', type:'text',placeholder:'calendar cmd name'
    a id:'click7', onclick:'myFunction7()', -> text 'calendar check->auth->addEvent'
    br()
    a id:'click8', onclick:'myFunction8()', -> text 'appInstalledChecker:google maps'
    br()
    a id:'click12', onclick:'myFunction12()', -> text 'test video.js in youtube'
    br()
    div ->
      a href:'https://www.youtube.com/watch?v=qiHCLi_lR9k&list=PLku534PwIYW0p5dSpyEVyjxcch3RX961X&index=2',->
        'youtube link'
    a id:'click11', class:'btn', onclick:'myFunction11()',-> text 'get lastMapPosition getItemObj'
    br()
    a id:'click9', class:'btn', onclick:'myFunction9()',-> text 'set lastMapPosition'
    br()
    a id:'click10', class:'btn', onclick:'myFunction10()',-> text 'remove lastMapPosition'
    br()
    a id:'click13', onclick:"myFunction13()",-> text 'isCommandAvailable:popup'
    hr()
    a id:'click13', onclick:"myFunction27()",-> text 'scan QR Code'
    hr()
    a id:'click14', onclick:"myFunction14()",-> text 'getMemoryDetail'
    hr()
    a id:'click15', onclick:"RMSrv.share('linking-share',null,{dest:'sms'})",-> text 'linking sms'
    div ->
    a id:'click16', onclick:"RMSrv.share('linking-share',null,{dest:'mailto'})",-> text 'linking mailto'
    div ->
    a id:'click18', onclick:"RMSrv.share('single-share',null,{})",-> text 'whatsapp single share'
    hr()
    a id:'click17', onclick:"RMSrv.getRoutesCounts((ret)=>{alert(JSON.stringify(ret))})",-> text 'getRoutesCounts'
    hr()
    a id:'click19',class:'btn btn-positive', onclick:"myFunction19()",-> text 'copy to clipboard'
    hr()
    a id:'click20', onclick:"RMSrv.hasWechat((ret)=>{alert(ret+typeof(ret))})",-> text 'hasWechat'
    hr()
    a id:'click22', onclick:"RMSrv.copyToClipboard('copy this text')",-> text 'copy to clipboard'
    hr()
    a id:'click23', onclick:"myFunction23()",-> text 'check permission default timeout'
    hr()
    a id:'click24', onclick:"myFunction24()",-> text 'check permission -1 timeout, no default return val'
    hr()
    a id:'click25', onclick:"myFunction25(0)",-> text 'setApplicationIconBadgeNumber 0'
    hr()
    a id:'click26', onclick:"myFunction25(2)",-> text 'setApplicationIconBadgeNumber 2'
    hr()
    a id:'click27', onclick:"myFunction26()",-> text 'getApplicationIconBadgeNumber'
    input id:'lat',placeholder:'lat'
    input id:'lng',placeholder:'lng'
    input id:'addr',placeholder:'addr'
    input id:'city',placeholder:'city'
    input id:'prov',placeholder:'prov'
    input id:'type',placeholder:'google|bing|mapbox|here|mapquest'
    br()
    a id:'click11', onclick:'geocodingtest()',-> text 'do geo Coding test'
    hr()
    hr()
    br()
    input id:'email',type:'email',placeholder:'email'
    a id:'click112', onclick:'checkemailValid()',-> text 'check Email'
    br()
  js '/js/diagnose_test.min.js?v=1122'

APP 'diagnosePage'
GET (req,resp)->
  resp.ckup 'diagnose'

GET 'api',(req,resp)->
  q = {del:{$ne:1},lang:req.locale()}
  q['dt'] = {'$gt':new Date("2015-08-27T20:23:41.601Z")}
  skip =  0
  News.findToArray q,{sort:[['pin','descending'],['dt','descending']],limit:10,skip:skip},(err,list)->
    if err
      console.error err
      return resp.send {e:err.toString()}
    resp.send {list:list or []}

# VIEW 'diagnoseMap', ->
#   div ngApp:'detailApp', ngController:'detailCtrl', class:'ng-cloak', ->
#     header class: 'bar bar-nav', ->
#       a class: 'icon icon-left pull-left',
#       href: '/1.5/index',
#       style:'color:white;',
#       dataTransition: 'slide-out'
#       h1 class: 'title',-> 'Diagnose'
#     div id:'inputAddressModal', class:'modal ', style:'z-index:20;', ->
#       header class:'bar bar-nav', ->
#         a class: 'icon icon-close pull-right',
#         href: '#',
#         ngClick:"toggleModal('inputAddressModal')",
#         dataIgnore: 'push'
#         h1 class: 'title',-> _ 'Set Address'

#       div class:'bar bar-standard bar-footer' , ->
#         div class:'err-block', ngHide:'fullAddr', ->
#           i class:'fa fa-exclamation-circle'
#           text _ 'Incomplete address, missing Postcode'

#         div class:'positive-block', ngShow:'fullAddr', ->
#           i class:'fa fa-check-circle'
#           text '{{inputAddrRet}}'

#         div class:'zip-revise',->
#           input type:'text',
#           ngModel:'inputZipRet',
#           placeholder:'Zip Code',
#           maxlength:'6'
#           p ->
#             text _ 'Revise postcode if necessary.'

#       div class:'bar bar-standard bar-header-secondary', style:'padding:0',->
#         form id:'addr-search', class:'',->
#           input type:'text', placeholder:'Input Address', ngModel:'inputAddr'
#           button class:'btn btn-default btn-nooutline fa fa-search', ngClick:'searchAddr();'

#       div class:'content', style:'padding-bottom:200px', ->
#         div class:'content-padded',
#         style:'margin:5px 0px 0px 0px;  display:block; height:210px;',-> #height:100%;
#           div id:'id_d_map', style:'height:205px;',-> #height:100%

#     div class: 'content',->#style:'margin:10px;',->
#       button class:'btn btn-block', id:'loadMap', ngClick:'loadMap()',->
#         text 'Load Map'
#       # div class:'content-padded' ,  style:'margin:5px 0px 0px 0px;  display:block; height:210px;',-> #height:100%;
#       #   div id:'id_d_map', style:'height:205px;',-> #height:100%


#   # js '/js/angular.min.js'
#   script src: '/libs/jquery2.js'
#   if @req.isChinaIP()
#   #input id:'china-ip',type:'hidden',name:'chinaIP',value:'1'
#     gmap_url = 'https://ditu.google.cn/maps/api/js?sensor=false'
#   else
#     gmap_url = 'https://maps.googleapis.com/maps/api/js?sensor=flase'

#   coffeejs { vars:{gurl:gmap_url} } ,->
#     null
#   js '/js/diagnoseMap.min.js'

VIEW 'diagnose',->
  # js '/js/shake.js'
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left',
    href: '/1.5/index',
    dataTransition: 'slide-out'
    h1 class: 'title',-> 'Diagnose'
  div class: 'content',->
    div class: '', style:'margin:3px;',->
      button id:'getSystemVersion', class:'btn',-> 'getSystemVersion'
      hr()
      button id:'getDeviceId', class:'btn',-> 'getDeviceId'
      hr()
      button id:'getUniqueId', class:'btn',-> 'getUniqueId'
      hr()
      button id:'hasGoogleService', class:'btn',-> 'hasGoogleService'
      hr()
      button id:'hasPlayServices', class:'btn',-> 'hasPlayServices'
      hr()
      button id:'exitApp', class:'btn',-> 'exitApp'
      hr()
      pre id:'appStatChange',-> 'you can invoke check again here!(alert will block execute for check alive, avoid use, otherwise cause refresh)'
      button id:'permissionsCheckNotification', class:'btn',-> 'permissionsCheckNotification'
      hr()
      button id:'permissionsCheckLocation', class:'btn',-> 'permissionsCheckLocation'
      button id:'permissionsCheckLocationaccuracy', class:'btn',-> 'permissionsCheckLocationAccuracy'
      hr()
      button id:'permissionsRequestLocation', class:'btn',-> 'permissionsRequestLocation'
      button id:'permissionsRequestLocationaccuracy', class:'btn',-> 'permissionsRequestLocationAccuracy'
      hr()
      button id:'permissionsRequestNotification', class:'btn',-> 'permissionsRequestNotification'
      hr()
      button id:'openSettings', class:'btn',-> 'openSettings'
      button id:'openSettingsNotification', class:'btn',-> 'openSettingsNotification'
      button id:'openSettingsLocation', class:'btn',-> 'openSettingsLocation'
  coffeejs ->

    RMSrv.onAppStateChange (opt)->
      # write content to div appStatChange
      document.getElementById('appStatChange').innerHTML = JSON.stringify(opt)
    document.getElementById('permissionsCheckNotification').addEventListener 'click',->
      RMSrv.permissions('notification','check',-1,(ret)->
        alert(ret)
      )
    document.getElementById('openSettingsNotification').addEventListener 'click',->
      RMSrv.openSettingsNotification()
    document.getElementById('openSettingsLocation').addEventListener 'click',->
      RMSrv.openSettingsLocation()
    document.getElementById('openSettings').addEventListener 'click',->
      RMSrv.openSettings()
    document.getElementById('permissionsCheckLocation').addEventListener 'click',->
      RMSrv.permissions('location','check',-1,(ret)->
        alert(ret)
      )
    document.getElementById('permissionsRequestLocation').addEventListener 'click',->
      RMSrv.permissions('location','request',-1,(ret)->
        alert(ret)
      )
    document.getElementById('permissionsCheckLocationaccuracy').addEventListener 'click',->
      RMSrv.permissions('locationaccuracy','check',-1,(ret)->
        alert(ret)
      )
    document.getElementById('permissionsRequestNotification').addEventListener 'click',->
      RMSrv.permissions('notification','request',-1,(ret)->
        alert(ret)
      )
    document.getElementById('permissionsRequestLocationaccuracy').addEventListener 'click',->
      RMSrv.permissions('locationaccuracy','request',-1,(ret)->
        alert(ret)
      )
    document.getElementById('getSystemVersion').addEventListener 'click',->
      RMSrv.getSystemVersion((ret)->
        alert(ret)
      )
    document.getElementById('getDeviceId').addEventListener 'click',->
      RMSrv.getDeviceId((ret)->
        alert(JSON.stringify(ret))
      )
    document.getElementById('getUniqueId').addEventListener 'click',->
      RMSrv.getUniqueId((ret)->
        alert(JSON.stringify(ret))
      )
    document.getElementById('hasGoogleService').addEventListener 'click',->
      RMSrv.hasGoogleService((ret)->
        alert(ret)
      )
    document.getElementById('hasPlayServices').addEventListener 'click',->
      RMSrv.hasPlayServices((ret)->
        alert(ret)
      )
    document.getElementById('exitApp').addEventListener 'click',->
      RMSrv.exitApp()
    null
