helpers = INCLUDE 'lib.helpers'
statHelper = INCLUDE 'libapp.stat_helper'
libproperties = INCLUDE 'libapp.properties'
# propCommons = INCLUDE 'libapp.propCommons'
limiter = INCLUDE 'lib.limiter'
libUser = INCLUDE 'libapp.user'
conf = CONFIG('share')
gShareHostNameCn = conf.share?.hostNameCn or 'realmaster.cn'


dataMethods = DEF 'dataMethods'
dataMethods.statHelpDocUrl = (req, user)->
  statHelpDocUrl = "https://www.#{gShareHostNameCn}/mp/35a1b2bb1984ae8b3d6f5dc1c2d14052a712b48337b8de3113410b519757c861cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228d8a5034?lang=zh-cn"
  return statHelpDocUrl
# dataMethods.realmasterHelpUrl = (req, user)->
#   realmasterHelpUrl = "https://www.#{gShareHostNameCn}/mp/5f67b2bb1984ae8b3d605bc3c18a1704a142b18263b18e311f115e51c3049964cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac17e4c7cb7dd7477c?lang=zh-cn"
#   return realmasterHelpUrl
dataMethods.shareHostNameCn = (req,user)->
  return conf.share?.hostNameCn or 'realmaster.cn'
dataMethods.stagingHelpUrl = (req,user)->
  return "http://www.#{gShareHostNameCn}/mp/bba7b2bb1984ae8b3d365e92c6801c54a219b18060e8de354e460c0491529932cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228df1f23c?lang=zh-cn&uid=RM_1031"

UserModel = MODEL 'User'
propStatsLastUpdateRecord = DEF 'propStatsLastUpdateRecord'
ProvAndCity = MODEL 'ProvAndCity'
propStatsModel = MODEL 'PropStats'

APP '1.5'
APP 'prop', true
APP 'stats', true

# given prov and city find stat for recent year
# sort: price(decending,avgS/avgR);   priceGrow(cmpSm/cmpSr);   activeProp(nS/nR); view(vc)
# type: sale; lease
# style: h/b/s/sm/th/cd
# - get popular cities
# - get stats for popular cities for this month, and current city
# - insert selected into popular, sort
# - translate result
# - cb null. val

#translate ranking result, need original value for change city use
translateRankingResult = (req, l, isCmty)->
  totalVc = 0
  for c in l
    c.pn = if isCmty then c.p else req.l10n(c.p)
    c.n =  if isCmty then c.o else req.l10n(c.o)
    totalVc += (parseInt(c.vc) or 0)
  for c in l
    if req.isAllowed 'devGroup'
      c.vcv = c.vc
    c.vc = (parseInt(c.vc) or 0)/totalVc
  null

#   {n:'万锦',o:'Markham',p:'ON',pn:'安大略省',avgr:2300,avgs:1500000,cmpsm:-0.043,cmpsy:0.178, cmprm:0.033,cmpry:0.22},
#write single stat result into corresponding record
statFormatIntoRet = (opt, retrecord, statobj)->
  tmp = statobj
  # if opt.cmty
  #   cmtyName = statHelper.sepCmtyName(opt.cmty).fmtd
  #   tmp = tmp.cmty[cmtyName] or {}
  if opt.style isnt 'all'
    tmp = tmp.tps?[opt.style] or {}
  if opt.mode is 'cmty'
    retrecord.avgs = if (tmp.avgSld isnt 0) then tmp.avgSld else tmp.avgS
  else
    retrecord.avgs = tmp.avgS
  retrecord.avgr = tmp.avgR
  retrecord.ns = tmp.nS
  retrecord.nr = tmp.nR
  # retrecord.vc = tmp.vcR + tmp.vcS
  retrecord.vc = parseInt(tmp.vc) or 0
  retrecord.vcrp = tmp.vcRApp/tmp.nR
  retrecord.vcsp = tmp.vcSApp/tmp.nS
  retrecord.cmpsm = v if (v = statHelper.calcCmpSm(tmp))?
  retrecord.cmpsy = v if (v = statHelper.calcCmpSy(tmp))?

  retrecord.cmprm = v if (v = statHelper.calcCmpRm(tmp))?
  retrecord.cmpry = v if (v = statHelper.calcCmpRy(tmp))?

  retrecord.cmpnsm = tmp.cmpNSm/(tmp.nS - tmp.cmpNSm) if tmp.cmpNSm
  retrecord.cmpnsy = tmp.cmpNSy/(tmp.nS - tmp.cmpNSy) if tmp.cmpNSy
  retrecord.cmpnrm = tmp.cmpNRm/(tmp.nR - tmp.cmpNRm) if tmp.cmpNRm
  retrecord.cmpnry = tmp.cmpNRy/(tmp.nR - tmp.cmpNRy) if tmp.cmpNRy
  retrecord.cmpnactvsm = tmp.cmpNActvSm/(tmp.nActvS - tmp.cmpNActvSm) if tmp.cmpNActvSm
  retrecord.cmpnactvsy = tmp.cmpNActvSy/(tmp.nActvS - tmp.cmpNActvSy) if tmp.cmpNActvSy
  retrecord.cmpnactvrm = tmp.cmpNActvRm/(tmp.nActvR - tmp.cmpNActvRm) if tmp.cmpNActvRm
  retrecord.cmpnactvry = tmp.cmpNActvRy/(tmp.nActvR - tmp.cmpNActvRy) if tmp.cmpNActvRy


#write stat list result into retMapping
matchCityWithStatusList = (opt, retMapping, list)->
  ret = []
  for statRecord in list
    if v = retMapping[statRecord._id]
      statFormatIntoRet(opt, v, statRecord)
      ret.push v
  ret

#get popular city list and find datas
updateRecordProv = (_id)->
  prov = _id.split('-')[1]
  prov = ProvAndCity.getProvFullName(prov)
  prov

popularCitiesM = null
popularCitiesW = null
helpers.repeat 60000 * 60 * 24,(->
  popularCitiesM = null
  popularCitiesW = null
)
getTranslatedPopularCityListVc = (req, opt, cb)->
  limit = opt.limit or 30
  itvl = opt.itvl or 'M'
  # d = propStatsLastUpdateRecord.propStatsMonthTs
  if itvl is 'M'
    val = popularCitiesM
  else
    val = popularCitiesW
    # d = propStatsLastUpdateRecord.propStatsWeekTs
  if val
    return cb null, val
  ret = []
  id = statHelper.getIdHeadByStatMode(opt.ts, itvl)
  _id = helpers.str2reg(id)
  propStatsModel.findPopularCityList {_id,limit},(err,ret)->
    return cb err if err
    return cb 'No Records' unless ret
    for r in ret
      r.p = updateRecordProv(r._id)
      r.o = r.city
    if itvl is 'M'
      popularCitiesM = ret
    else
      popularCitiesW = ret
    cb null, ret

getPopularCityStatRanking = (req, opt, cb)->
  ts = opt.ts
  curid = opt.curid
  ids = []
  retMapping = {}
  opt.limit = 50
  getTranslatedPopularCityListVc req, opt, (err,popularCities)->
    contained = false
    for c in popularCities
      id = statHelper.generateDBId({prov:c.p, city:c.o, ts:opt.ts, prefix:opt.itvl or 'M'})
      retMapping[id] = {p:c.p, o:c.o, id:id}
      if id is curid
        retMapping[id].cur = true
        contained = true
      #NOTE: if c.p or c.o is null, id is null
      ids.push(id) if id
    fields =
      cmty:0
    propStatsModel.findListByIds {ids,fields},(err, list)->
      return cb err if err
      ret = matchCityWithStatusList(opt, retMapping, list)
      cb null, contained, ret

#given city stat record for this month, generate communities ranking list
generateCmtyRankingFromCity = (opt={}, curCity={})->
  l = []
  cmtyName = libproperties.sepCmtyName(opt.cmty).fmtd
  for k,v of curCity.cmty
    tmp = {o:v.nm, p:opt.prov, onm:v.onm}
    tmp.cur = true if k is cmtyName
    statFormatIntoRet(opt, tmp, v)
    l.push tmp
  l

# helpers.repeat 1000*3600*24*7,(-> updatePopularCityStatRanking())
getCityStatRanking = (req, opt={type:'sale'}, cb)->
  unless opt.city and opt.prov
    return cb 'Need City and prov'
  # insert current city into RankingList from popular cities
  # ret = [{n:'万锦',o:'Markham',p:'ON',pn:'安大略省',avgr:2300,avgs:1500000,cmpsm:-0.043,cmpsy:0.178, cmprm:0.033,cmpry:0.22}..]
  prefix = opt.itvl or 'M'
  isAllowedVipPlus = req.isAllowed 'vipPlus'
  opt.ts = statHelper.genSearchRecordMonth({isAllowedVipPlus, prefix, propStatsLastUpdateRecord})
  # if not vip show last last week
  # if prefix is 'W' and not req.isAllowed 'vipPlus'
  #   opt.ts = propStatsLastUpdateRecord.propStatsWeekTsVisitor
  # opt.mode = req.param 'mode'
  opt.curid = statHelper.generateDBId({prov:opt.prov, city:opt.city, ts:opt.ts, prefix:prefix})
  if opt.mode is 'cmty'
    propStatsModel.findOneById {id:opt.curid}, (err, curCity)->
      return cb err if err
      #fond curcity statobj change stat format into ret format
      ret = []
      ret = generateCmtyRankingFromCity(opt, curCity) if curCity
      # ret.sort(sortingMethod)
      translateRankingResult(req, ret, true)
      cb null,ret
  else
    getPopularCityStatRanking req, opt, (err, contained, ret)->
      return cb err if err
      if contained
        # ret.sort(sortingMethod)
        translateRankingResult(req, ret)
        return cb null, ret
      propStatsModel.findOneById {id:opt.curid}, (err, curCity)->
        return cb err if err
        #fond curcity statobj change stat format into ret format
        if curCity
          tmp = {p:opt.prov, o:opt.city, cur:true}
          statFormatIntoRet(opt, tmp, curCity)
          ret.push tmp
        # ret.sort(sortingMethod)
        translateRankingResult(req, ret)
        cb null,ret

num2month = helpers.num2month

#mapping have all 12 ids, list may not have that
parseDBResult = (mapping, list)->
  labels = []
  for i in list
    if v = mapping[i._id]
      labels.push v.label
  labels

#given prov and ciity (and cmty) return cmpSm and cmp
getAvgPriceList = (req, opt={}, cb)->
  opt.lang = req.locale()
  mret = statHelper.generateStatDBIds(opt)
  ids = mret.ids
  propStatsModel.findListByIds {ids,opt}, (err, list)->
    return cb err if err
    # console.log 'DB took: '+(Date.now()-s)
    cb null, {l:list, idsMapping:mret.idsMapping, ids:ids}

getLastYear = statHelper.getLastYear

calcCmpVals = (l1Obj, l2Obj, opt)->
  ids1 = l1Obj.ids.slice().reverse()
  idvMapping = l1Obj.idvMapping
  id = null
  v1 = {}
  for i in ids1
    if idvMapping[i]#most recent record exists
      id = i
      break
  for i in l1Obj.l
    if i._id is id
      cmty = null
      cmty = libproperties.sepCmtyName(opt.cmty).fmtd if opt.cmty
      v1 = i
      if i.cmty?[cmty]
        v1 = i.cmty[cmty]
      style = opt.style
      if (opt.style isnt 'all') and v1.tps?[style]
        v1 = v1.tps[style]
      break
  ret = {}
  if v = v1.cmpSm
    ret.cmpsm = v1.cmpSm/(v1.avgS - v1.cmpSm)
    # ret.cmppidxm =
  # else
  #   ret.cmpsm = v2.avgS - v1.avgS
  if v = v1.cmpMSld
    ret.cmpsm = v/(v1.avgSld - v1.cmpMSld)
  if v = v1.cmpSy
    ret.cmpsy = v1.cmpSy/(v1.avgS - v1.cmpSy)
    # ret.cmppidxy = (v1.avgS/v1.avgTax) - ((v1.avgS-v1.cmpSy)/v1.avgTax)
  #over write cmpsm
  if v = v1.cmpYSld
    ret.cmpsy = v/(v1.avgSld - v1.cmpYSld)
  if v = v1.cmpRm
    ret.cmprm = v1.cmpRm/(v1.avgRm - v1.cmpRm)
  if v = v1.cmpRy
    ret.cmpry = v1.cmpRy/(v1.avgRm - v1.cmpRy)
  if v = v1.cmpNRy
    ret.cmpnry = v1.cmpNRy
  if v = v1.cmpNRm
    ret.cmpnrm = v1.cmpNRm
  if v = v1.cmpNSy
    ret.cmpnsy = v1.cmpNSy
  if v = v1.cmpNSm
    ret.cmpnsm = v1.cmpNSm
  if v = v1.cmpNActvRy
    ret.cmpnactvry = v1.cmpNActvRy
  if v = v1.cmpNActvRm
    ret.cmpnactvrm = v1.cmpNActvRm
  if v = v1.cmpNActvSy
    ret.cmpnactvsy = v1.cmpNActvSy
  if v = v1.cmpNActvSm
    ret.cmpnactvsm = v1.cmpNActvSm
  
  if v = v1.cmpMDom and v isnt 'N/A'
    ret.cmpmdom = Math.floor(v1.cmpMDom*10)/10
  if v = v1.cmpYDom and v isnt 'N/A'
    ret.cmpydom =  Math.floor(v1.cmpYDom*10)/10
  # if (v1.avgSld isnt 0) and (v1.avgS isnt 0)
  #   thisYear = v1.avgSld/v1.avgS
  #   lastYear = (v1.avgSld-v2.cmpMSld)/v1.avgS-v1.cmpSm
  #   ret.cmpmsplp = (thisYear-lastYear)/lastYear
  # if (v1.avgSld isnt 0) and (v1.avgS isnt 0)
  #   thisYear = v1.avgSld/v1.avgS
  #   lastYear = (v1.avgSld-v2.cmpMSld)/v1.avgS-v1.cmpSm
  #   ret.cmpmsplp = (thisYear-lastYear)/lastYear
  if (v1.avgR isnt 0) and (v1.avgS isnt 0) and (v1.cmpRm?) and (v1.cmpSm?)
    ret.cmpmrplp = getCmpRplp(v1, false)
  if (v1.avgR isnt 0) and (v1.avgS isnt 0) and (v1.cmpRy?) and (v1.cmpSy?)
    ret.cmpyrplp = getCmpRplp(v1, true)
  ret

getCmpRplp = (v1, isYear)->
  f1 = 'cmpSm'
  f2 = 'cmpRm'
  if isYear
    f1 = 'cmpSy'
    f2 = 'cmpRy'
  thisYear = v1.avgS/(v1.avgR*12)
  lastYear = (v1.avgS-v1[f1])/((v1.avgR-v1[f2])*12)      #637033/1,949
  v = (thisYear - lastYear)/lastYear
  Math.floor(v*100)/100

#extrect the data we care about
genDataObjFromOpt = (lobj, opt, isSold)->
  l = lobj?.l or []
  idsMapping = lobj?.idsMapping or {}
  ret = {}
  for i in l
    record = i
    _id = record._id
    ts = idsMapping[_id].ts
    # label = idsMapping[_id].label
    if opt.cmty
      cmtyName = libproperties.sepCmtyName(opt.cmty).fmtd
      record = record.cmty?[cmtyName] or {}
    if opt.style isnt 'all'
      record = record.tps?[opt.style] or {}
    tmp = {ts:ts}
    saleprice = if isSold then (record.avgSld or null) else (record.avgS or null)
    if opt.mode is 'price'
      rentprice = statHelper.getRentPrice(record)
      tmp.v = if opt.saletp is 'Sale' then (Math.round(saleprice/100)/10) else rentprice
    else if opt.mode is 'nprop'
      tmp.v = if opt.saletp is 'Sale' then record.nS else record.nR
    else if opt.mode is 'pidx'
      tmp.v = if (opt.saletp is 'Sale') and (saleprice isnt null) then Math.round(saleprice / record.avgTax / 1.99) / 100 else null
    else if opt.mode is 'dom'
      dom = if opt.saletp is 'Sale' then record.avgDomS else record.avgDomR
      tmp.v = if dom in ['N/A',null] then null else Math.floor(dom*10)/10
    else if opt.mode is 'splp'
      val = null
      if opt.saletp is 'Sale' and (record.avgSld isnt 0) and (record.avgSldS isnt 0)
        val = record.avgSld/record.avgSldS
      tmp.v = if val? then Math.floor(val*1000)/10 else val
    else if opt.mode is 'rplp'
      val = null
      if (record.avgR isnt 0) and (record.avgS isnt 0)
        val = record.avgS/(record.avgR*12)
      # if (record.avgRtd isnt 0) and (record.avgSld isnt 0)
      #   val = dbret.avgSld / (dbret.avgRtd * 12)
      tmp.v = if val? then Math.floor(val*10)/10 else val
    else if opt.mode is 'nsldnrtd'
      val = null
      fld = 'nRtd'
      if opt.saletp is 'Sale'
        fld = 'nSld'
      if (record[fld] isnt 0)
        val = record[fld]
      tmp.v = val
    else if opt.mode is 'nactv'
      val = null
      fld = 'nActvR'
      if opt.saletp is 'Sale'
        fld = 'nActvS'
      if (record[fld] isnt 0)
        val = record[fld]
      tmp.v = val
    else
      break
    ret[_id] = tmp
  ret

setDefaultFromDate = (d, itvl='M',l)->
  to = new Date(+d)
  from = new Date(+to)
  for i in l
    from = statHelper.getLastTs(from, itvl)
  [from,to]

#use the shorter one of the 2
simpleMerge = (req, l1Obj, l2Obj, opt)->
  l1 = []#this year
  l2 = []#last year
  #l3 = []#this year sold price
  labels = []
  ol1 = l1Obj.idvMapping
  # ol1Sld = l1Obj.idvMappingSld or {}
  ol2 = l2Obj.idvMapping
  idsMapping = l1Obj.idsMapping
  l2ids = l2Obj.ids#.reverse()
  legend1 = ''
  legend2 = ''
  now = propStatsLastUpdateRecord.propStatsMonthTs
  [r1from, r1to] = setDefaultFromDate(now, opt.itvl, l1Obj.ids)
  [r2from, r2to] = setDefaultFromDate(statHelper.getLastYear(now), opt.itvl, l1Obj.ids)
  hasl1from = false
  hasl2from = false
  for _id,idx in l1Obj.ids#.reverse()
    l1.push if v = ol1[_id] then v.v else null
    # if v = ol1Sld[_id]
    #   l3.push if v.v is 0 then null else v.v
    labels.push idsMapping[_id].label
    _id2 = l2ids[idx]
    l2.push if v = ol2[_id2] then v.v else null
    if ol1[_id] and not hasl1from
      hasl1from = true
      r1from = ol1[_id].ts
    if ol2[_id2] and not hasl2from
      hasl2from = true
      r2from = ol2[_id2].ts
    if (idx is l1Obj.ids.length-1) and ol1[_id]
      r1to = ol1[_id].ts
      r2to = ol2[_id2].ts if ol2[_id2]
  legend1 = generateLegend(req, r1from, r1to)
  legend2 = generateLegend(req, r2from, r2to)
  {l1,l2,labels,legend1,legend2}

isValidSoldRecord = (lastId, r, opt)->
  isBCRecord = /-BC-/.test r._id
  if (not isBCRecord) and (r.area not in ['Peel','York','Toronto','Halton','Durham','Simcoe'])
    return false
  if (r._id isnt lastId)
    return false
  record = r
  if opt.cmty
    cmtyName = libproperties.sepCmtyName(opt.cmty).fmtd
    record = r.cmty?[cmtyName] or {}
  (record.avgSld?) and (record.avgSld isnt 0)

generateLegend = (req, f,t)->
  _helper = (ts)->
    ts.getFullYear()+'.'+req.l10n(num2month(ts.getMonth()),'Month')
  _helper(f)+'-'+_helper(t)

genDataListByMode = (req, opt, l1Obj, l2Obj, isSold)->
  l1Obj.idvMapping = genDataObjFromOpt(l1Obj, opt, isSold)
  # l1Obj.idvMappingSld = genDataObjFromOpt(l1Obj, opt, true)
  l2Obj.idvMapping = genDataObjFromOpt(l2Obj, opt, isSold)
  mergeRet = simpleMerge(req, l1Obj, l2Obj, opt)
  l1 = mergeRet.l1
  l2 = mergeRet.l2
  labels = mergeRet.labels
  # l2 = [] unless req.isAllowed 'vipRealtor'
  list = [
    {nm:mergeRet.legend1,l:l1}, #now.getFullYear()
    {nm:mergeRet.legend2,l:l2} #opt.ts.getFullYear()
  ]
  # if req.isAllowed('devGroup') and l3.length
  #   list.push {nm:req.l10n('Sold Price'), l:l3}
  ret = {list:list, labels:labels}
  tmp = calcCmpVals(l1Obj, l2Obj, opt) unless opt.mode is 'pidx'
  Object.assign(ret, tmp)

#propDetail chart
#opt: {city,prov,itvl}
getCityStatHistory = (req,opt,cb)->
  # now = new Date()#'2017-08-31 19:36'
  # opt.ts = now
  opt.limit = 6
  getAvgPriceList req, opt, (err, l1Obj)->
    # console.log JSON.stringify(l1Obj.l[0].cmty)
    # console.log l1Obj
    return cb err if err
    labels = []
    records = []
    recordName = req.l10n('Avg Price(k)')
    lastRecords = []
    lastRecordName = req.l10n('Avg Rent')
    idsMapping = l1Obj?.idsMapping or {}
    cmty = ''
    hasSoldPrice = false
    hasRentPrice = false
    if opt.cmty
      cmty = libproperties.sepCmtyName(opt.cmty).fmtd if opt.cmty
    for r in l1Obj.l
      idsMapping[r._id].v = r
      # if r.cmty?[cmty]
      #   r = r.cmty[cmty]
      # if r.avgSld
      #   hasSoldPrice = true
    ids = l1Obj.ids.reverse()
    hasCmty = false
    for id in ids
      labels.push idsMapping[id].label
      r = idsMapping[id].v || {}
      if r.cmty?[cmty]
        hasCmty = true
        r = r.cmty[cmty]
        # delete r.tps
      # console.log r
      saleprice = r.avgS
      rentprice = r.avgR
      records.push (Math.round(saleprice/100)/10)
      lastRecords.push rentprice#(Math.round(rentprice/100)/10)
    # console.log l1Obj
    cb null, {records,lastRecords,recordName,lastRecordName,labels, cmty:hasCmty}

#opt has saletp/city/prov/cmty/style
getCityStatRankingHistory = (req, opt, cb)->
  if opt.itvl is 'M'
    now = new Date()#'2017-08-31 19:36'
    if req.isAllowed('vipRealtor') and statHelper.useCurrentMonth(propStatsLastUpdateRecord.propStatsMonthTs, now)
      now = statHelper.getNextMonth(now)
  else
    #will for loop so set to last + 7 = current, might already be current, then this weeks record is used, which is incorrect
    # now = new Date(+propStatsLastUpdateRecord.propStatsWeekTs)#'2016-05-22'
    isAllowedVipPlus = req.isAllowed 'vipPlus'
    now = statHelper.genSearchRecordMonth({isAllowedVipPlus, prefix:'W',propStatsLastUpdateRecord})
    now.setDate(now.getDate() + 7) #if now.getDay() >= 6
    # if not req.isAllowed 'vipPlus'
    #   now = propStatsLastUpdateRecord.propStatsWeekTs
  opt.ts = now
  getAvgPriceList req, opt, (err, l1Obj)->
    opt.ts = getLastYear(now)
    return cb err if err
    getAvgPriceList req, opt, (err, l2Obj)->
      #data from 201506M, TODO: add hist for 2 years ago
      # opt.ts = getLastYear(opt.ts)
      # return cb err if err
      # getAvgPriceList req, opt, (err, l3Obj)->
      #l2Obj is last year
      return cb err if err
      isSold = false
      # if opt.saletp is 'Sale'
      lastId = l1Obj.ids[0]
      for r in l1Obj.l
        # console.log isValidSoldRecord(lastId, r, opt)
        if isValidSoldRecord(lastId, r, opt)
          isSold = true
          break
      # console.log '#####'+isSold
      ret = {}
      # ret.l3Obj = l3Obj
      l1Obj.ids = l1Obj.ids.reverse()
      l2Obj.ids = l2Obj.ids.reverse()
      # console.log '+++'
      # console.log l1Obj.ids
      # console.log l2Obj.ids
      for mode in ['price','nprop','dom','splp','rplp','nsldnrtd','nactv']
        opt.mode = mode
        ret[mode] = genDataListByMode(req, opt, l1Obj, l2Obj, isSold)
      cb null, ret


DEFAULT_LIMIT = 25
VIP_LIMIT = 75
MAX_LIMIT = 50
DEV_LIMIT = 150
SysNotifyModel = MODEL 'SysNotify'

getLimitNumber = (req)->
  if req.isAllowed 'vipUser'
    return VIP_LIMIT
  if req.isAllowed 'devGroup'
    return DEV_LIMIT
  if limit = req.session.get('statLimit')
    return MAX_LIMIT
  DEFAULT_LIMIT

GET '',(req,resp)->
  unless req.getDevType() is 'app'
    url = "/adPage/needAPP"
    if uid = req.param 'uid'
      url += '?uid='+uid
    return resp.redirect url
  done = ()->
    resp.ckup 'statPage',{},'_',{noUserModal:1, noAngular:1, noAppCss:0, noRatchetJs:1}
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    UserModel.findByTskDnAndId user._id,'statShare',(err,found)->
      if err then console.error err
      #unless found
      #  return resp.redirect "/event?tpl=creepyHouse&u=/stigma/house/shared"
      if found
        return req.session.set "statLimit",MAX_LIMIT,->
          return done()
      done()

POST 'shared',(req,resp)->
  if req.param 'pre'
    return resp.send {ok:0, preshare:1}
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0} unless user?._id
    update = {$addToSet:{tskDn:'statShare'}}
    UserModel.setTskDn user._id, {tskDn:'statShare'},(err)->
      if err
        console.error err
      SysNotifyModel.notifyAdmin "Shared statShare. #{req.remoteIP()} User:#{user._id}:#{user.eml} #{libUser.fullNameOrNickname(req.locale(),user)} #{user.nm} #{user.roles}"
      resp.send {ok:1,j:'/1.5/prop/stats?d=/1.5/index&city=Toronto&prov=Ontario&cityName=Toronto'}

#count new listings
# getAvailableListingCount = (opt={}, cb)->
#   q = {status:'A', saletp:'Sale'}
#   q.city = opt.city if opt.city
#   q.prov = opt.prov if opt.prov
#   q.cmty = opt.cmty if opt.cmty
#   if opt.style isnt 'all'
#     q = Object.assign(q,statHelper.getHousePtype(opt.style))
#   Properties.countDocuments q, (err, nS)->
#     return cb err if err
#     q.saletp = 'Lease'
#     Properties.countDocuments q, (err, nR)->
#       return cb err if err
#       cb null, {nS:nS, nR:nR}

getCityBriefInfo = DEF 'getCityBriefInfo'

# /1.5/prop/stats/priceLineChart
POST 'priceLineChart',(req,resp)->
  error = (err,silent)->
    ret = {ok:0, err:err, silent}
    resp.send ret
  # unless req.getDevType() is 'app'
  #   return error "Only available in RealMaster APP"
  UserModel.appAuth {req,resp},(user)->
    opt =
      city:   req.param 'city'
      prov:   req.param 'prov'
      # style:  req.param 'style'
      cmty:   req.param 'cmty'
      ts:     propStatsLastUpdateRecord.propStatsMonthTs #this month may not have stats yet
      itvl:   req.param('itvl') or 'M'
      # now:    now
      # saletp: req.param('saletp') or 'Sale'
    unless opt.city and opt.prov
      return error('No City and Prov',true)
    getCityStatHistory req, opt, (err,ret)->
      return error(err.toString()) if err
      ret.ok = 1
      resp.send ret

# /1.5/prop/stats/briefCityInfo
POST 'briefCityInfo',(req,resp)->
  now = new Date()#'2017-08-31 19:36'
  opt =
    city:   req.param 'city'
    prov:   req.param 'prov'
    style:  req.param 'style'
    cmty:   req.param 'cmty'
    ts:     propStatsLastUpdateRecord.propStatsMonthTs #this month may not have stats yet
    itvl:   'M'
    now:    now
    saletp: if req.param('saletp') is 'sale' then 'Sale' else 'Lease'
  return resp.send {ok:0, err:'no city and prov'} unless opt.city and opt.prov
  getCityBriefInfo req, opt, (err, cityinfo)->
    ret =
      ok:1
      stat:cityinfo
    resp.send ret
# /1.5/prop/stats/cityInfo
POST 'cityInfo', (req, resp)->
  error = (err, vip)->
    ret = {ok:0, err:err}
    ret.vip = vip if vip
    resp.send ret
  unless req.getDevType() is 'app'
    return error "Only available in RealMaster APP"
  UserModel.appAuth {req,resp},(user)->
    return error(req.l10n('Please login')) unless user
    statLimit = getLimitNumber(req)
    limiter.check user,'statShare',statLimit,(u,evt,cnt,notify)->
      if u
        #if notify # when 70,140,210...
        #  notifyAdmin "#{evt} reached limit(#{cnt}). #{req.remoteIP()} User:#{u._id}:#{u.eml} #{u.fn} #{u.ln} #{u.nm} #{u.roles}"
        # return findError req.l10n "Reached Daily Usage Limit"
        if req.isAllowed('brokerage', u) and not req.isAllowed('vipUser', u)
          vip = {
            msg:'Reached daily limit. Upgrade to VIP to get more access quota'
          }
          return error(null, vip)
        if cnt < MAX_LIMIT
          return resp.send {eurl:"/event?tpl=statShare&u=/1.5/prop/stats/shared"}
        return error(req.l10n("Reached Daily Usage Limit"))
      now = new Date()#'2017-08-31 19:36'
      opt =
        city:   req.param 'city'
        prov:   req.param 'prov'
        style:  req.param 'style'
        cmty:   req.param 'cmty'
        ts:     propStatsLastUpdateRecord.propStatsMonthTs #this month may not have stats yet
        itvl:   req.param('itvl') or 'M'
        now:    now
        saletp: req.param('saletp') or 'Sale'
      return error('no city and prov') unless opt.city and opt.prov
      getCityBriefInfo req, opt, (err, cityinfo)->
        return error(err.toString()) if err
        getCityStatRankingHistory req, opt, (err, result)->
          return error(err.toString()) if err
          opt.mode = 'cmty'
          getCityStatRanking req, opt, (err, cmtylist)->
            return error(err.toString()) if err
            opt.mode = 'city'
            getCityStatRanking req, opt, (err, citylist)->
              return error(err.toString()) if err
              ret =
                ok:1
                history:result
                cityinfo:cityinfo
                cmtylist:cmtylist
                citylist:citylist
              resp.send ret

VIEW 'statPage', ->
  div id:'vueBody',->
    text """<app-prop-stats></app-prop-stats>"""
  js '/js/Chart-2.5.0.min.js'
  js '/js/html2canvas.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appPropStats.js'
  css '/css/sprite.min.css'
