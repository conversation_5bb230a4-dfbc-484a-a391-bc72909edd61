propStatsModel = MODEL 'PropStats'
statHelper = INCLUDE 'libapp.stat_helper'
helpers = INCLUDE 'lib.helpers'
libproperties = INCLUDE 'libapp.properties'

propStatsLastUpdateRecord = {}
propStatsLastUpdateRecord.propStatsWeekTs = statHelper.getLastWeek(new Date())
propStatsLastUpdateRecord.propStatsMonthTs = statHelper.getLastMonth(new Date())
# propStatsLastUpdateRecord.propStatsWeekTsVisitor = statHelper.getLastWeek(propStatsLastUpdateRecord.propStatsWeekTs)
DEF 'propStatsLastUpdateRecord', propStatsLastUpdateRecord

findStatTs = ()->
  propStatsModel.findLastRecord (err,record)->
    if err # Never ignore errors
      console.error err
    return unless record
    propStatsLastUpdateRecord.propStatsMonthTs = new Date(record.propStatsMonthTs.ts) if record.propStatsMonthTs
    propStatsLastUpdateRecord.propStatsWeekTs = new Date(record.propStatsWeekTs.ts) if record.propStatsWeekTs
    # propStatsLastUpdateRecord.propStatsWeekTsVisitor = statHelper.getLastWeek(propStatsLastUpdateRecord.propStatsWeekTs) if record.propStatsWeekTs
    null
findStatTs()
helpers.repeat 600000, findStatTs

#query status db to find city status object and return
getCityBriefInfo = (req, opt={}, cb)->
  city = opt.city
  prov = opt.prov
  return cb 'city and prov required' unless city and prov
  opt.propStatsLastUpdateRecord = propStatsLastUpdateRecord
  opt.isAllowedVipPlus = req.isAllowed 'vipPlus'
  propStatsModel.findOne opt,(err, dbret)->
    return cb err if err
    style = opt.style or 'all'
    cmty = opt.cmty
    prefix = opt.itvl or 'M'
    dbret ?= {}
    ts = dbret.statTs
    #ret.ns = getActiveListingCountList {city:city, prov:prov}
    if cmty
      cmtyName = libproperties.sepCmtyName(cmty).fmtd
      dbret = dbret.cmty?[cmtyName] or {}
    if style isnt 'all'
      dbret = dbret.tps?[style] or {}
    # getAvailableListingCount opt, (err, ret)->
    #   return cb err if err
    lprp = null
    if (dbret.avgR isnt 0) and (dbret.avgS isnt 0)
      lprp = dbret.avgS / (dbret.avgR * 12)
    # if (dbret.avgRtd isnt 0) and (dbret.avgSld isnt 0)
    #   lprp = dbret.avgSld / (dbret.avgRtd * 12)
    splp = null
    if opt.saletp is 'Sale' and (dbret.avgSld isnt 0) and (dbret.avgSldS isnt 0)
      splp = dbret.avgSld/dbret.avgSldS
    ret =
      ns:dbret.nS#depreciated
      nr:dbret.nR
      nSld:dbret.nSld
      nActv:dbret.nActv
      nActvS:dbret.nActvS
      nActvR:dbret.nActvR
      # avgSld:dbret.avgSld
      avgs:if (sld = dbret.avgSld) and (sld isnt 0) then dbret.avgSld else dbret.avgS
      avgr:statHelper.getRentPrice(dbret)
      noffmkts:dbret.nOffMktS
      noffmktr:dbret.nOffMktR
      avgtax:dbret.avgTax
      hasSld:if dbret.avgSld? and (dbret.avgSld isnt 0) then true else false
      crm:0
      ts:ts
      avgdomr: if dbret.avgDomR? and (dbret.avgDomR isnt 0) then Math.floor(dbret.avgDomR*10)/10 else null
      lprp:if lprp? then Math.floor(lprp*10)/10 else lprp
      splp:if splp? then Math.floor(splp*1000)/10 else splp

    ret.cmpRy = statHelper.calcCmpRy(dbret)
    ret.cmpSy = statHelper.calcCmpSy(dbret)

      # console.log ret
    if prefix is 'W'
      l = statHelper.getWeekDates(ts)
    else if prefix is 'M'
      l = statHelper.getMonthDates(ts)
    if l
      ret.tsf = l[0]
      ret.tst = new Date(l[1].setDate(l[1].getDate() - 1))
    cb null, ret

DEF 'getCityBriefInfo',getCityBriefInfo
