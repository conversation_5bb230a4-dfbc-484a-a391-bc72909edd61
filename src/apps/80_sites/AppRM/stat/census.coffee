helpers = INCLUDE 'lib.helpers'
path = require 'path'
fs = require 'fs'
translator = SERVICE 'translator'
censusHelper = INCLUDE 'libapp.censusHelper'

# conf = CONFIG()
debug = DEBUG()
# Wecard = COLLECTION 'chome', 'wecard'
Census = COLLECTION 'vow','census'
Census_keys = COLLECTION 'vow','census_keys'

Census2016ReversKeys = COLLECTION 'vow','census2016ReversKeys'
Census2016Notes = COLLECTION 'vow','census2016Notes'
Census2016 = COLLECTION 'vow','census2016'
Census2021ReversKeys = COLLECTION 'vow','census2021ReversKeys'
Census2021Notes = COLLECTION 'vow','census2021Notes'
Census2021 = COLLECTION 'vow','census2021'
UserModel = MODEL 'User'

_census2021_resource  = '_census2021_resource'
_census2021_translation_file = 'census2021'
Census2021IdMap = {}
Census2021NotesMap = {}

PLSWAIT _census2021_resource

translator.onReady ->
  putToTranslateFile = (prefix,keyValue)->
    transEngine = translator.getTranslator 'census2021','en'
  Census2021ReversKeys.findToArray {},(err, ret)->
    if err or not ret
      console.log 'cannot get census mapping keys'
    else
      for key in ret
        Census2021IdMap[key._id.toString()] = key.val
      putToTranslateFile '',Census2021IdMap
    Census2021Notes.findToArray {}, (err, ret)->
      if err or not ret
        console.log 'cannot get census mapping notes'
      else
        for key in ret
          Census2021NotesMap[key._id.toString()] = key.val
        putToTranslateFile NOTE_PREFIX,Census2021NotesMap
      PROVIDE _census2021_resource,_census2021_resource

_census2016_resource  = '_census2016_resource'
_census2016_translation_file = 'census2016'
Census2016IdMap = {}
Census2016NotesMap = {}

PLSWAIT _census2016_resource

translator.onReady ->
  putToTranslateFile = (prefix,keyValue)->
    transEngine = translator.getTranslator 'census2016','en'
  Census2016ReversKeys.findToArray {},(err, ret)->
    if err or not ret
      console.log 'cannot get census mapping keys'
    else
      for key in ret
        Census2016IdMap[key._id.toString()] = key.val
      putToTranslateFile '',Census2016IdMap
    Census2016Notes.findToArray {}, (err, ret)->
      if err or not ret
        console.log 'cannot get census mapping notes'
      else
        for key in ret
          Census2016NotesMap[key._id.toString()] = key.val
        putToTranslateFile NOTE_PREFIX,Census2016NotesMap
      PROVIDE _census2016_resource,_census2016_resource

NOTE_PREFIX = 'NT'

populateCommu = (cfg={}) ->
  {year='2021',locale='en',commu} = cfg
  return commu unless (commu and commu.census)
  mapping = {}
  notes = {}
  # year = req.param 'year'
  if year is '2021'
    trans = translator.getTranslator 'census2021',locale
    IdMap = Census2021IdMap
    noteMap = Census2021NotesMap
  else if year is '2016'
    trans = translator.getTranslator 'census2016',locale
    IdMap = Census2016IdMap
    noteMap = Census2016NotesMap

  for name, value of commu.census
    # mapping[name] = trans(name,(IdMap[name] or ''),true)
    mapping[name] = trans(name)
    for subName,v of value
      # mapping[subName] = trans(subName,(IdMap[subName] or ''),true)
      mapping[subName] = trans(subName)
      if v[3] #has Notes
        #TODO: use _ CensusNotesMap[v[3]]
        note = noteMap[v[3]]
        notes[v[3]] = trans((NOTE_PREFIX + v[3]),(note or ''),true)
  [commu.census, mapping, notes]


APP '1.5'
APP 'census', true
#1.5/census/commu
GET 'commu', (req, resp) ->
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  UserModel.appAuth {req,resp},(user) ->
    loc = req.param 'loc'
    resp.ckup 'commu',{}, '_', {noAngular:1, noRatchetJs:1, noRMApp:1, noCordova:1, noAppCss:1, noUserModal:1}

#1.5/census/commu
POST 'commu', (req, resp) ->
  UserModel.appAuth {req,resp},(user) ->
    error = (err)->
      resp.send {ok:0, err:req.l10n(err)}
    # unless user
    #   return error('Please Login')
    d = req.body
    year = req.param 'year'
    # default use census2021
    year ?= '2021'
    col = Census2021
    if year is '2021'
      col = Census2021
    else if year is '2016'
      col = Census2016
    censusHelper.getCommunityByLocOrId col, d, (err, community) ->
      if err
        debug.error err
        return resp.send {ok:0, err:req.l10n(err.toString())}
      if not community
        return resp.send {ok:0, err:req.l10n(MSG_STRINGS.NOT_FOUND)}
      ret = {ok:1, commu:{}}
      [ret.commu, ret.mapping, ret.notes] = populateCommu {year,locale:req.locale(),commu:community}
      if d.type is 'demographic'
        lang = req.locale()
        commuName = d.commuId?[0] or community._id
        fileName = commuName.replace('.','_')
        censusImage = "/img/imgstocks/census/#{fileName}.jpg"
        summary = censusHelper.getDemographicSummary year, ret.commu,lang
        censusChartData = censusHelper.getCensusChartData year, ret.commu, ret.mapping, lang
        resp.send {
          ok: 1,
          _id:community._id
          bbox: community.bbox,
          picBbox: community.picBbox,
          summary,
          censusChartData,
          censusImage
        }
      else
        resp.send ret
    # unless d.loc and d.loc[0] and d.loc[1]
    #   return error('No Location Data!')
    # Census.findToArray {
    #   census:{$exists:true}
    #   'bns.sw.0':{$lte:d.loc[0]},
    #   'bns.sw.1':{$lte:d.loc[1]},
    #   'bns.ne.0':{$gte:d.loc[0]},
    #   'bns.ne.1':{$gte:d.loc[1]}}
    #   ,{sort:{}}, (err,results)->
    #     if err
    #       console.error err
    #       return resp.send {ok:0, err:err.toString()}
    #     if (not results?) or (!results.length) then return resp.send {ok:0, err:req.l10n('No available data for this location.')}
    #     ret = {ok:0, commu:{}}
    #     done = ()->
    #       resp.send ret
    #     for commu in results
    #       for bn in commu.bns
    #         if helpers.isInBoundary bn.bn,d.loc
    #           ret.ok = 1
    #           # console.log '+++'
    #           # console.log bn.bn.length
    #           # if bn.bn.length < 85
    #           #   ret.bns = []
    #           #   ret.center = [((bn.sw[0] + bn.ne[0])/2).toFixed(6), ((bn.sw[1] + bn.ne[1])/2).toFixed(6)]
    #           #   for tmp in bn.bn
    #           #     ret.bns.push tmp.toFixed(6) #google use 6 digit
    #           # send mapping and value 1. reduce repeat, 2. let front-end handle display
    #           [ret.commu, ret.mapping, ret.notes] = populateCommu req,commu
    #           return done()
    #     done()

VIEW 'commu', ->
  js '/js/Chart-1.1.1.min.js'
  div id:'vueBody',->
    text """<commu></commu>"""
  js '/js/entry/commons.js'
  js '/js/entry/commu.js'
