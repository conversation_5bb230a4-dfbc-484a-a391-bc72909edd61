libUser = INCLUDE 'libapp.user'
mapServer =  INCLUDE 'lib.mapServer'
helpers = INCLUDE 'lib.helpers'
statCollector = INCLUDE 'lib.statCollector'
libPropertyImage = INCLUDE 'libapp.propertyImage'
User  = COLLECTION 'chome', 'user'
Properties = MODEL 'Properties'
UserModel = MODEL 'User'
CrmModel = MODEL 'Crm'
Showing = MODEL 'Showing'
{respError} = INCLUDE 'libapp.responseHelper'
getConfig = DEF 'getConfig'
# accessAllowed = DEF '_access_allowed'
getWXConfig = DEF 'getWXConfig'
config = CONFIG(['serverBase','share'])
{deepCopyObject} = INCLUDE 'lib.helpers_object'
ObjectId = INCLUDE('lib.mongo4').ObjectId


libproperties = INCLUDE 'libapp.properties'
{genListingPicReplaceUrls,setUpPropRMPlusFields} = libproperties
PROP_FIELD_FILTER = Object.assign {rltr:1,cnty:1,uaddr:1}, libproperties.MAP_PROP_FIELDS
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger() #3,3
{listingPicUrlReplace} = INCLUDE 'libapp.common'

statCollector.addItem 'Showing','Created',Showing.getStat
statCollector.addItem 'Showing','Showing Date',Showing.getDate

errorPage = (err, resp) ->
  resp.ckup 'generalError', {err:err}

APP '1.5'
APP 'showing', true


# get all showing list page
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    title = 'Showing'
    resp.ckup 'showingList',{title},'_',{noAngular:true}

#only display showing.dt > now
# showShowingList = (lists)->
#   list = []
#   now = new Date().toLocaleDateString()
#   for showing in lists
#     date=new Date(showing.dt)
#     if (Date.parse(date)- Date.parse(now)) >= 86400000
#       list.push(showing)
#   return list

###*
 * 根据showing的时间和日期对showing列表进行分组
 * @param {Object} params - 输入参数对象
 * @param {number} params.today - 当前日期,数字格式
 * @param {string} params.now - 当前时间,字符串格式
 * @param {Array} params.list - showing列表
 * @returns {Object} 分组后的showing列表,包含past和upcoming两个数组
###
groupByTime = ({today, now, list})->
  return {past: [], list: []} unless list?.length
  # 获取showing列表的属性状态
  list = await getListPropStatus list
  # 获取当前日期和时间，today返回值为数字，now返回值为字符串
  upcomingList = []
  pastList = []
  
  # 遍历showing列表进行分组
  for s in list
    # 根据日期判断showing状态
    if s.dtNum < today
      s.lbl = 'Completed'
      pastList.unshift s
    else if s.dtNum > today
      s.lbl = 'Upcoming'
      upcomingList.push s
    else
      # 当天的showing根据具体时间判断状态
      s.lbl = 'Today'
      
      if s.stT
        # 处理跨天的情况,如果结束时间小于开始时间,在小时上加24
        endTime = if s.endT < s.stT then Number(s.endT.slice(0,2))+24+s.endT.slice(2) else s.endT
        
        # 根据当前时间判断showing状态
        if s.stT > now
          s.lbl = 'Upcoming'
          upcomingList.push s
        else if endTime < now
          s.lbl = 'Completed'
          pastList.unshift s
        else if endTime > now and s.stT < now
          s.lbl = 'Current'
          upcomingList.push s
      else
        upcomingList.push s

  # 返回分组结果  
  return {
    past: pastList
    list: upcomingList
  }

###*
 * 获取showing的创建者信息并添加到showing列表中
 * @param {string} lang - 语言设置
 * @param {Array} list - showing列表
 * @returns {Array} 添加了创建者信息的showing列表
###
getShowingOwner = (lang, list) ->
  return [] unless list?.length

  # 提取所有showing创建者ID
  ids = list.map((l) -> l.uid[0]).filter(Boolean)
  return list unless ids.length

  # 获取所有创建者信息
  agents = await UserModel.getListByIdsAsync ids, {
    projection: UserModel.SHOWING_AGENT_FIELDS
  }
  
  # 构建agent信息映射
  agentMap = agents.reduce((map, agent) -> 
    map[agent._id] = {
      avt: if agent.avt then libPropertyImage.replaceRM2REImagePath(agent.avt)
      fnm: libUser.fullNameOrNickname(lang, agent)
    }
    map
  , {})

  # 将创建者信息添加到showing中
  for showing in list
    if agent = agentMap[showing.uid[0]]
      showing.avt = agent.avt if agent.avt
      showing.fnm = agent.fnm if agent.fnm

  return list

#找到所有 showing list 里的 prop id
getListPropStatus = (list)->
  ids=[]
  for l in list
    for p in l.props
      ids.push p._id
  _props = await Properties.findListingsByIDsAsync ids,{projection:{_id: 1,status: 1,lst: 1}}
  unless _props?.length
    debug.error 'showing can not found props in',ids
    return []
  # propMap = {}
  # #prop details from prop part
  propResults={}
  for _prop in _props
    propResults[_prop._id] = _prop
  for l in list
    for prop in l.props
      if tmp = propResults[prop._id]
        prop.status = tmp.status
        prop.saletp = tmp.saletp
        prop.lst = tmp.lst
  #inject prop detail to showing
  return list

# get all showing list of login user
POST '',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp} unless req.hasRole('realtor')
    unless (b = req.body).afterDate and b.today and b.now and b.filterDate
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    if b.uid and (b.uid.toString() isnt user._id.toString())
      try
        {err,viewedUser} = await Showing.checkShowingAdmin {req,resp,user,viewedUid:b.uid}
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
      return respError {clientMsg:req.l10n(err),resp} if err
      user = viewedUser if viewedUser
    clnt = b.clnt
    afterDate = parseInt b.afterDate
    today = parseInt b.today
    now = b.now
    filterDate = b.filterDate
    try
      ret = await Showing.getList user._id,{clnt,afterDate,projection:Showing.LIST_PAGE_FIELDS}
      if ret?.length > 0
        # 判断owner，给查找出来的list增加coop agent信息
        ret = await CrmModel.addCoopAgentInfo {list:ret,lang:req.locale(),uid:user._id}
        list = await groupByTime {today,now,list:ret}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    unless ret?.length > 0
      return resp.send {ok:1,list:[]}
    resp.send {ok:1, list:list}

# 查看经纪能否创建新showing,获取当前时间之后的showing，当天的已经完成的不算
POST 'upcoming',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp} unless req.hasRole('realtor')
    isVip = req.isAllowed 'vipUser'
    isRealtor = req.hasRole('realtor')
    listLimit = getConfig('showingListLimit')
    propsLimit = getConfig('showingPropsLimit')
    vipUpcomingLimit = getConfig('vipShowingUpcoimgLimit')
    isntVipUpcomingLimit = getConfig('isntVipShowingUpcoimgLimit')
    afterDate = helpers.date2num(new Date())
    afterTime = helpers.getTheCurrentTime(new Date())
    try
      list = await Showing.getList user._id,{afterDate,afterTime,projection:Showing.LIST_PAGE_FIELDS}
      if not isVip
        count = await Showing.countTotal user._id
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    return resp.send {ok:1,list:[]} unless list?.length > 0
    isReachedLimit = (roles,list)->
      upcomingLimit = if roles then vipUpcomingLimit else isntVipUpcomingLimit
      return if list.length >= upcomingLimit then true else false
    if isVip
      vipReachedLimit = isReachedLimit(isVip, list)
      if vipReachedLimit
        list.length = vipUpcomingLimit
      return resp.send {ok:1, list:list, isvip:isVip, vipReachedLimit,propsLimit}
    else
      isntVipReachedLimit = isReachedLimit(isVip, list)
      if isntVipReachedLimit
        list.length = isntVipReachedLimit
      isntVipReachedTotal = if count >= listLimit then true else false
      resp.send {ok:1, list:list, isvip:isVip, isntVipReachedLimit,isntVipReachedTotal,propsLimit}

# 客户查看半年内绑定的经纪给自己创建的showing
POST 'client',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    unless (b = req.body).today and b.now
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    today = parseInt b.today
    now = b.now
    try
      ret = await Showing.getListByCid user._id,{projection:Showing.LIST_PAGE_FIELDS}
      if ret?.length
        list = await getShowingOwner req.locale(),ret
        list = await groupByTime {today,now,list}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    return resp.send {ok:1,list:[]} unless ret?.length > 0
    resp.send {ok:1, list}

#detail是否需要登陆？ redirect
GET 'detail', (req,resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    #getprops and get showings
    propIds = req.param 'propIds'
    showingId = req.param 'showingId'
    today = req.param('today') or false
    #如果有showing id，需要先找到showing，然后把props注入。没有时直接返回props detail
    showing = {}
    props = []
    try
      if showingId
        showing = await getShowingDetail req, resp, showingId
        if today and (showing?.dtNum < today)
          showing.lbl = true
      if propIds
        props = await getProps {req, ids:propIds}
    catch err
      debug.error err
      return errorPage err, resp
    ckupDetail req,resp,props,showing

ckupDetail = (req, resp, props, showing) ->
  inFrame = req.param('inFrame') or true
  isVipUser = req.isAllowed 'vipUser'
  isApp = req.getDevType() is 'app'
  apikey = mapServer.getMapboxAPIKey {isApp}
  propsLimit = getConfig('showingPropsLimit')
  resp.ckup 'showingDetail',{props:props, showing:showing, isvip:isVipUser, propsLimit:propsLimit, inFrame:inFrame, title:'Showing Detail'},'_',{mapboxkey:apikey,noAngular:true}

# TODO: DRY with searchByIds(/1.5/search/prop), see: docs/Dev_property/propertiy_apis_table.md
getProps = ({req, ids,user}) ->
  return [] unless ids?.length
  ids = ids?.split(',') unless Array.isArray ids
  ids = Array.from(new Set(ids))
  l10n=(a,b)->req.l10n(a,b)
  _ab=(a,b)->req._ab(a,b)
  if user
    isAllowedPropAdmin = UserModel.accessAllowed('propAdmin',user)
    isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
  else
    isAllowedPropAdmin = false
    isAllowedVipUser = false
  props = await Properties.findListingsByIDsAsync ids,{projection:PROP_FIELD_FILTER}
  uids = []
  propMap = {}
  ret = await Properties.addPropFavFields {props:props,uid:user?._id}
  for prop in ret
    Properties.addPropAdditionalFields({
      user,prop,
      isAllowedPropAdmin,
      isAllowedVipUser,
      l10n,
      _ab,
      apisrc:req.param('apisrc')
      simplfyPtp:false})
    genListingPicReplaceUrls(prop, req.isChinaIP(), {use3rdPic:config.serverBase?.use3rdPic,isPad:req.isPad(),shareHostNameCn:config.share?.hostNameCn,useThumb:1})
    # NOTE: this will overwrite prop.tags in ret.props
    prop.durtn ?= 60
    prop.tags ?= []
    prop.index = 0
    prop.failed ?= false
    prop.inBuylist ?= false
    setUpPropRMPlusFields prop,l10n,_ab
    if /^RM/.test(prop.id) and prop.uid
      uids.push prop.uid
    propMap[prop._id] = prop
  # sortProps排序，根据传入的id顺序返回结果列表
  sortProps = []
  for id in ids
    sortProps.push p if p = propMap[id]
  return sortProps unless uids?.length

  #append rm listing owner
  users = await UserModel.getListByIdsAsync uids,{projection:UserModel.LISTING_OWNER_FIELDS}
  return sortProps unless users?.length
  userkeyValue = {}
  for u in users
    userkeyValue[u._id] = u
  for p in sortProps
    p.adrltr = userkeyValue[p.uid] if userkeyValue[p.uid]
  return sortProps

# /1.5/showing/props
# NOTE: +prop in showing edit page
POST 'props', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp} unless req.hasRole('realtor')
    unless (b = req.body).ids
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    ids = b.ids
    try
      props = await getProps {req, ids}
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    return resp.send {ok:1, props: props}

#get detail of specified showing
getShowingDetail = (req, resp, id)->
  ret = await Showing.findById id,{ownerUid:true}
  return null unless ret?.props?.length
  ids = []
  for prop in ret.props
    ids.push prop._id
  #get showing customer info
  _props = await getProps {req, ids}
  propMap = {}
  #prop details from prop part
  for _prop in _props
    propMap[_prop._id] = _prop
  #inject prop detail to showing
  for prop,i in ret.props
    if p = propMap[prop._id]
      if Array.isArray(p.tags) and p.tags.length is 0
        delete p.tags
        prop.tags ?= []
      for f in ['durtn','tags','failed','inBuylist']
        if prop[f]
          delete p[f]
      prop = Object.assign prop, p
    if prop.failed
      delete prop.stT
      delete prop.endT
    prop.oriIdx = i
  return ret

POST 'edit', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp} unless req.hasRole('realtor')
    unless b = req.body
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    if b.viewedUid and (user._id.toString() isnt b.viewedUid)
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp}
    del = b.del
    id = b._id
    isAdmin = req.isAllowed 'admin'
    if del
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp} unless id
      update = {isAdmin,uid:user._id}
      fn = 'delete'
    else
      if b.props?.length > getConfig('showingPropsLimit')
        return resp.send {ok:0,e:req.l10n(MSG_STRINGS.TOO_MANY_PROPERTIES)}
      update = {uid:user._id,body:b}
      fn = 'updateOne'
    try
      ret = await Showing[fn] id,update
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    resp.send {ok:1,showingId:ret.showingId, clsId: ret.clsId}

GET 'detail\/share',(req,resp)->
  # 和detail页面不同吗？share的时间没有传进来吗？  get不能传入时间，使用后台时间
  today = helpers.date2num new Date()
  showingId = req.param 'showingId'
  req.setLocale lang if lang = req.param 'lang'
  isPopup = if req.param 'isPopup' then true else false
  d = req.param('d') or false
  isApp = req.getDevType() is 'app'
  cfg =
    hasU:       1
    avt:        '/img/icon_Alert.png'
    nm:       req.l10n 'RealMaster'
    cardUrl:  req.fullUrl('/getapp')
    noWeCard: 1
    err:      1
    noLoading:  1
    shareInfoText:  req.l10n 'Can not find the page. Please check the URL and try again.'
  errorPage = (config)->
    return resp.ckup 'NewsMiddleAdPageEn', config, 'adPage', {jump:0}

  projection = UserModel.SHOWING_AGENT_FIELDS
  projection.roles = 1
  try
    showing = await getShowingDetail req, resp, showingId
    agent = await UserModel.findByIdAsync showing.uid,{projection}
  catch err
    debug.error err
  unless showing
    # 如果没有找到showing，提示showing可能被删除
    return errorPage(cfg)

  # 在share页面判断得是agent是否存在来判断是否显示agent部分
  if agent
    agent.fnm = libUser.fullNameOrNickname req.locale(),agent
    agent.cpny = libUser.userCpny req.locale(),agent
    agent.vip = UserModel.accessAllowed 'vipUser',agent
  if showing.exp
    #如果showing被删除，提示showing被经纪删除
    cfg.avt = libPropertyImage.replaceRM2REImagePath agent.avt if agent?.avt
    cfg.shareInfoText = req.l10n('This Showing has been removed') + '<br>' + agent?.fnm
    return errorPage(cfg)
  if showing.dtNum < today
    # 需要lbl字段判断是否能添加到calendar
    showing.lbl = true
  for prop in showing.props
    prop.mapLink = getPropMapLink req,prop
  showing.isWeChat = req.isWeChat()
  showing.isChinaIP = req.isChinaIP()
  if showing.stPt
    showing.startPointMapLink = getPropMapLink req, showing.stPt
  # only safari browser can add the showing to the apple calendar
  isSafari = req.isSafari()
  calendarFields = getCalendarFields(showing)
  gUrl = "https://calendar.google.com/calendar/r/eventedit?text=#{calendarFields.SUMMARY}&location=#{calendarFields.location}&dates=#{calendarFields.DTSTART}/#{calendarFields.DTEND}&pli=1"
  getWXConfig req,req.fullUrl(),(err,wxcfg)->
    layoutConfig = {wxcfg:wxcfg}
    layoutConfig.shareImage = "#{req.shareHost()}/img/showing/shareImg.png"
    layoutConfig.desc = "#{showing.props.length} #{req.l10n 'Properties'}"
    title = "RealMaster Showing #{showing.dt} #{showing.stT or ''}"
    showing.reqHost = req.shareHost()
    return resp.ckup 'showing/showingShare',{title,lang,gUrl,isSafari,showing,\
      agent,isPopup,d,isApp},'_',layoutConfig

getPropMapLink = (req,prop) ->
  return '' unless prop.addr
  if prop.addr
    if (not prop.addr) and prop.lat and prop.lng
      addr = prop.lat + ',' + prop.lng
    else
      addr = (prop.city_en or prop.city) + ',' + (prop.prov_en or prop.prov)
      cnty = prop.cnty_en or prop.cnty or ''
      if /^CA$/i.test(cnty)
        addr += ' Canada'
      else
        addr += ' ' + cnty
      if prop.addr
        addr = prop.addr + ',' + addr
      else if prop.zip
        addr = addr + ', ' + prop.zip
    url = req.exMapURL() + encodeURIComponent(addr)

getCalendarFields = (showing)->
  DTSTAMP = helpers.formatDateTime new Date()
  DTSTART = helpers.formatDateTime new Date(showing.dt+' '+ showing.stT)
  DTEND = helpers.formatDateTime new Date(showing.dt+' '+ showing.endT)
  location = ''
  SUMMARY = 'Showing'
  if showing.stPt and showing.stPt.faddr
    location=showing.stPt.faddr
  else
    scheProps = showing.props.filter (a)->
      return a.stT
    if scheProps.length>0
      scheProps.sort (a,b)->
        if a.stT>b.stT
          return 1
        else
          return -1
      location = scheProps[0].addr + '\,'+scheProps[0].city+'\,'+scheProps[0].prov

  return {location,SUMMARY,DTSTAMP,DTSTART,DTEND}

#calendar有用到吗？ share页面添加到calendar
GET 'calendar\_:id.ics', (req,resp)->
  id = req.param 'id',
  TRIGGER = req.param 'trigger' or 0

  try
    showing = await Showing.findById id,{projection:Showing.CALENDAR_FIELDS}
  catch err
    debug.error err
  calendarFields = getCalendarFields(showing)
  if TRIGGER
    alertDate = new Date(new Date(showing.dt+' '+ showing.stT).getTime() - TRIGGER*60*1000)
    alertStr = ';VALUE=DATE-TIME:#{helpers.formatDateTime(alertDate)}'

  str=
    """
    BEGIN:VCALENDAR
    VERSION:2.0
    PRODID:-//RealMaster v1.0//EN
    BEGIN:VEVENT
    UID:#{showing.clsId}
    SEQUENCE:1
    DTSTAMP:#{calendarFields.DTSTAMP}
    DTSTART:#{calendarFields.DTSTART}
    DTEND:#{calendarFields.DTEND}
    SUMMARY:#{calendarFields.SUMMARY}
    LOCATION:#{calendarFields.location}
    BEGIN:VALARM
    TRIGGER#{alertStr}
    DESCRIPTION:#{calendarFields.SUMMARY} start at #{calendarFields.DTSTART}
    ACTION:DISPLAY
    END:VALARM
    END:VEVENT
    END:VCALENDAR
    """
  resp.contentMime 'text/calendar'
  return resp.send str

VIEW 'showingDetail',->
  div id:'vueBody',->
    text """<showing-detail></showing-detail>"""
  coffeejs {vars:{
    props:@props,
    showing:@showing,
    inFrame:@inFrame,
    isvip:@isvip,
    title:@title,
    propsLimit:@propsLimit
    }
  }, ->
    null

  css '/css/apps/showing.min.css'
  css '/css/apps/common.min.css'
  css '/css/sprite.min.css'
  js '/js/objectAssignDeep.min.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/entry/commons.js'
  js '/js/html2canvas.min.js'
  js '/js/emailMisspelled.min.js'
  js '/js/entry/showingDetail.js'
  # text ckup 'mapboxJs'
  # text """<script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-directions/v4.0.2/mapbox-gl-directions.js" async defer>
  #  </script>
  #  <link rel="stylesheet" type="text/css" href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-directions/v4.0.2/mapbox-gl-directions.css" async defer>
  #  """
  # js '/js/map/mapbox.min.js'
  # css '/css/mapbox.min.css'

VIEW 'showingList',->
  div id:'vueBody',->
    text """<showing-list></showing-list>"""
  coffeejs {vars:{
    title:@title,
    }
  }, ->
    null
  css '/css/sprite.min.css'
  js '/js/entry/commons.js'
  js '/js/language/changeLanguage.min.js'
  js '/js/emailMisspelled.min.js'
  js '/js/entry/showingList.js'

GET 'buylist/:feature',(req,resp)->
  feature = req.param('feature')
  if feature not in ['owner','client']
    return resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    opt =
      d:req.param('d') or '/1.5/index',
      isPopup:req.param('isPopup'),
      lang:req.locale(),
      role:feature
    resp.ckup 'showing/buylist',opt,'_',{noAngular:true,noUserModal:true,noRatchetJs:true}

# 查找uid中匹配的数据，能看到客户信息，可以添加过滤客户进行过滤
POST 'buylist/owner',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp}  unless req.hasRole('realtor')
    body = req.body ?= {}
    query = {uid:user._id}
    userIds = []
    if clnt = body.clnt
      userIds.push new ObjectId(clnt)
      query.clnt = clnt#经纪可以过滤用户
    if cid = body.cid
      userIds.push new ObjectId(cid)
    try
      showings = await Showing.getBuylist query
    catch err
      debug.error err
      return resp.send {ok:0,err}
    return resp.send {ok:1,list:[]} unless showings?.length
    getBuylistPropInfo {req,resp,user,showings,isOwner:true,userIds,cid,clnt}


# 查找cid匹配的数据，能看到经纪信息，不能看到经纪备注的名字等内容
POST 'buylist/client',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    try
      showings = await Showing.getBuylistByCid user._id
    catch err
      debug.error err
      return resp.send {ok:0,err}
    return resp.send {ok:1,list:[]} unless showings?.length
    getBuylistPropInfo {req,resp,user,showings,isOwner:false}

getBuylistPropInfo = ({req,resp,user,showings,isOwner,userIds,cid,clnt})->
  return resp.send {ok:1,list:[]} unless showings?.length
  isOwner ?= false
  userIds ?= []
  limit = parseInt(req.param 'limit') or 20
  page = parseInt(req.param 'page') or 0
  lang = req.locale()
  returnPropList = []
  proplist = [] #保存了房源顺序，showing信息
  propShowingMap = {} #存有showing信息的，
  propMap = {} #处理过数据的房源列表
  propIds = []
  userResults={}
  clntIds = []
  for showing in showings
    # 需要获取经济及客户信息，客户可以是app用户也可以是自己添加的用户
    # 经济不需要获取其他经纪信息，客户不需要获取其他客户信息
    if isOwner
      #没有过滤客户的情况下需要查找客户信息，客户信息需要找user和crm表
      clntIds.push showing.clnt if showing.clnt and not clnt
    else
      # 客户获取其他经济信息(showing owner)
      showing.ownerUid = showing.uid[0]
      userIds.push showing.ownerUid
    # 重构数据结构 showing:[{_id:showingId,prop:[]}]-> prop:[{_id:propId,showing:{}}]
    props = showing.props
    delete showing.props
    for prop in props
      if prop.inBuylist
        propIds.push prop._id
        prop.showing = showing
        proplist.push prop #可能会有重复房源，不同showing，不同客户，或者不同经纪
  try
    _props = await Properties.findListingsByIDsAsync propIds,{projection:PROP_FIELD_FILTER}
    if isOwner # 获取crm中对应客户的信息
      _clnts = await CrmModel.findListByShowingCids {ids:clntIds,uid:user._id}
    else #获取经纪信息
      _users = await UserModel.getListByIds userIds,{projection:UserModel.SHOWING_AGENT_FIELDS}
  catch err
    debug.error err
    return resp.send {ok:0,err}
  if _users?.length
    for _user in _users
      userResults[_user._id] ?= {}
      _user.avt = libPropertyImage.replaceRM2REImagePath _user.avt if _user.avt
      _user.fnm = libUser.fullNameOrNickname lang,_user
      userResults[_user._id] = Object.assign userResults[_user._id],_user
  if _clnts?.length
    for _clnt in _clnts
      userResults[_clnt._id] ?= {}
      userResults[_clnt._id] = Object.assign userResults[_clnt._id],_clnt
  l10n=(a,b)->req.l10n(a,b)
  _ab=(a,b)->req._ab(a,b)
  if _props?.length
    for _prop in _props
      genListingPicReplaceUrls(_prop, req.isChinaIP(), {use3rdPic:config.serverBase?.use3rdPic,isPad:req.isPad(),shareHostNameCn:config.share?.hostNameCn,useThumb:1})
      setUpPropRMPlusFields _prop,l10n,_ab
      _prop.del = false
      propMap[_prop._id] = _prop
  for prop in proplist
    _prop = deepCopyObject(propMap[prop._id])
    prop = Object.assign _prop,prop
    # TODO: when showing.uid = [id]
    prop.realtor = userResults[prop.showing.ownerUid] if userResults[prop.showing.ownerUid]
    if isOwner and (clntInfo = userResults[prop.showing.clnt])
      prop.clnt = clntInfo
    propShowingMap[prop._id+prop.showing._id] = prop
  returnPropList = Object.values propShowingMap
  # 根据加入buylist的时间进行排序
  returnPropList.sort (a,b)->
    return new Date(b.bTs).getTime() - new Date(a.bTs).getTime()
  more = false
  if limit and (returnPropList.length > (limit*(page+1)))
    more = true
  returnPropList = returnPropList.slice(limit*page,limit*(page+1))
  return resp.send {ok:1,list:returnPropList,more}

POST 'toggleBuylist',(req, resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp}  unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED),resp} unless req.hasRole('realtor')
    unless (b = req.body).id and b.pid
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    b.uid = user._id
    try
      await Showing.toggleBuylist b
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp, sysErr:err}
    return resp.send {ok:1}



