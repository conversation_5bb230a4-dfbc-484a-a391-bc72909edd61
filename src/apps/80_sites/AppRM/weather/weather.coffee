weatherLib = INCLUDE 'libapp.weather'
WeatherModel = MODEL 'Weather'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
BoundaryModel = MODEL 'Boundary'
# appConfig = CONFIG()
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'

APP '1.5'
APP 'weather', true

# /1.5/weather/weatherInfo
POST 'weatherInfo', (req,resp)->
  city = req.body?.city
  prov = req.body?.prov
  depaTime = req.body?.time
  unless (city and prov and depaTime)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),category:MSG_STRINGS.BAD_PARAMETER,resp}
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    try
      origDBData = await WeatherModel.weatherInforFor48Hours({city,time:depaTime,projection:{hourly:1,alerts:1,daily:1},prov})
    catch err
      debug.error "weatherInforFor48Hours:#{city},#{depaTime}",err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR, resp}
    projection =
      hourly:
        temp:1
        hmdty:1
        uv:1
        weather:1
      daily:
        tempmin:1
        tempmax:1

    if origDBData
      frontEndFormat = weatherLib.convertDBDataToFrontEndFormat({
        time: depaTime,
        projection,
        hourlyData: origDBData.hourly,
        dailyData: origDBData.daily
        alertsData: origDBData.alerts
      })
      return resp.send {ok:1,result:{weather:frontEndFormat}}
    try
      centerPoint = await BoundaryModel.getCenterPointBasedOnCity(city)
    catch err
      debug.error "getCenterPointBasedOnCity:#{req.body.city}",err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR, resp}
    
    if ((not centerPoint) or (not centerPoint.center) or (not centerPoint.center.coordinates) or (centerPoint.center.coordinates.length < 2))
      debug.error "get center point based on city failed: #{city} ",centerPoint
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),category:MSG_STRINGS.BAD_PARAMETER,resp}
    try
      databaseFormat = await weatherLib.getWeatherInforFromThirdParties({
        lng:centerPoint.center.coordinates[0],
        lat:centerPoint.center.coordinates[1],
        city: city
        prov: prov
      })
    catch err
      debug.error 'getWeatherInforFromThirdParties:',err.response?.status,err.response?.data
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND),category:MSG_STRINGS.NOT_FOUND, resp}
    
    unless databaseFormat
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND),category:MSG_STRINGS.NOT_FOUND, resp}
    try
      saveResult = await WeatherModel.saveWeatherDate(databaseFormat)
    catch err
      debug.error 'saveWeatherDate:',databaseFormat,err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR, resp}

    frontEndFormat = weatherLib.convertDBDataToFrontEndFormat({
      time: depaTime,
      projection,
      hourlyData: databaseFormat.hourly
      dailyData: databaseFormat.daily
      alertsData: databaseFormat.alerts
    })
    return resp.send {ok:1,result:{weather:frontEndFormat}}
