# # auth_auth = DEF '_auth_auth'
# # auth_login = DEF '_auth_login'
# auth_rlogin = DEF '_auth_rtoken_login'
# # auth_logout = DEF '_auth_logout'
# auth_by_uk = DEF '_auth_by_unikey'
# auth_gen_uk = DEF '_auth_gen_unikey'
# auth_del_uk = DEF '_auth_del_unikey'
helpers = INCLUDE 'lib.helpers'
debug = DEBUG()
# UserModel = MODEL 'User'

# app_auth = (req,resp,url,cb)->
#   done = (user)->
#     # if user, set req.user return
#     if user
#       req.user = user
#       # setup language
#       req.setLocale user.locale if user.locale and (user.locale isnt req.locale())
#       if user.ck and user.cn
#         req.session.set 'ck',user.ck
#         req.session.set 'cn',user.cn,->
#           cb user
#         return null
#       else
#         return cb user
#     # still no user, redirect to url
#     if url then return resp.redirect url
#     cb null
#   # auth_auth, with null
#   UserModel.auth {req,resp},(user)->
#     return done user if user
#     # check cookie 'k', auto sign-in auth_by_unikey
#     #console.log req.cookies
#     if k = req.cookies['k']
#       auth_by_uk req,k,(err,user)->
#         if err then req.logger?.error err
#         done user
#     else
#       done()

# app_after_login = (req,user,lid,cb)->
#   # after auth: setup 'k'; add req.user
#   # web = req.param 'web'
#   isApp = req.getDevType() is 'app'
#   auth_gen_uk lid,isApp,(err,uk)->
#     if err or not uk then return cb err or new Error("Can't create key")
#     req.resp.cookie 'k',uk,{maxAge:365*24*3600*1000,path:'/'}
#     req.user = user
#     # setup language
#     req.setLocale user.locale if user.locale
#     cb null,user

# app_login = (req,user_pass,cb)->
#   # _auth_login
#   UserModel.auth_login req,user_pass,(err,user,extraInfo)->
    # if err or not user then return cb(err,null,extraInfo) or new Error "No User"
#     app_after_login req,user,user_pass.u,cb

# app_rlogin = (req,token,cb)->
#   console.log 'app_rlogin'
#   UserModel.auth_rlogin req,token,(err,user)->
#     console.log "app_rlogin #{err} #{user}"
#     if err or not user then return cb err or new Error('Auth Error')
#     if lid = (user.login_id or (if Array.isArray(user.eml) then user.eml[0] else user.eml))
#       return app_after_login req,user,lid,cb
#     else
#       return cb new Error('Can not find user')

# app_logout = (req,cb)->
#   # remove 'k';req.user
#   isApp = req.getDevType() is 'app'
#   if k = req.cookies['k']
#     req.resp?.clearCookie 'k'
#     return auth_del_uk k, isApp, (err)->
#       if err
#         req.logger?.warn err
#         console.error err
#       UserModel.auth_logout req,cb
#   # _auth_logout
#   UserModel.auth_logout req,cb

config = CONFIG(['geetest','recapcha'])
error_validate = 'Validation Error!'
geetest = require('geetest/gt-sdk')
pc_geetest = new geetest geetest_id: config.geetest.id, geetest_key: config.geetest.key

app_register_pcgeetest = (req, cb) ->
  pc_geetest.register (err, data) ->
    cb err,data

# @input body #input from client
# type body {
#   web: boolean # not assigned by client, should be only true if dev mode
# }
# type isCip boolean; #true if client is cip
app_verify_robot = (body, isCip, cb)->
  return cb null unless body.web
  if isCip
    verify_geetest body, cb
  else
    verify_recapcha body, cb

verify_geetest = (body, cb)->
  if not (body.geetest_challenge and body.geetest_validate and body.geetest_seccode)
    return cb error_validate
  pc_geetest.validate {
    challenge:body.geetest_challenge,
    validate:body.geetest_validate,
    seccode:body.geetest_seccode
  }, (err, success) ->
    ret = error_validate
    if err
      console.error err
    ret = null if success
    cb ret

verify_recapcha = (body, cb)->
  recapchaResponse = body.recaptcha
  if (recapchaResponse is undefined) or (recapchaResponse is '') or (recapchaResponse is null)
    return cb 'No recaptcha response in body'
  secretKey = config.recapcha?.secretKey
  verificationUrl = "https://www.google.com/recaptcha/api/siteverify?secret=#{secretKey}&response=#{recapchaResponse}"
  helpers.http_get verificationUrl,{protocol:'https',timeout:10000},(err,data) ->
    if err
      debug.error err
      return cb error_validate
    try
      parsedData = JSON.parse(data)
    catch e
      debug.error e
      return cb error_validate
    if parsedData.success
      return cb()
    else
      debug.error parsedData
      return cb error_validate

# DEF 'app_auth',app_auth
# DEF 'app_login',app_login
# DEF 'app_logout',app_logout
# DEF 'app_rlogin',app_rlogin
# DEF 'app_after_login',app_after_login
DEF 'app_verify_robot', app_verify_robot
DEF 'app_register_pcgeetest', app_register_pcgeetest
