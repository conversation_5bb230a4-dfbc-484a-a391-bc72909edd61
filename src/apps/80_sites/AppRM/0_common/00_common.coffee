# debug = console.log
# warn = console.log
# helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
TrebKeys =  COLLECTION 'vow','mls_treb_keys'
conf = CONFIG(['wechat','share','serverBase'])
#mysql = INCLUDE 'lib.mysql'
# mysql_client = mysql.getDb conf.sqldbs.vow
SysData = COLLECTION 'chome','sysdata'
wechat = INCLUDE 'lib.wechat'
debug = DEBUG 'common'
getConfig = DEF 'getConfig'
mapServer = INCLUDE 'lib.mapServer'
###
CityTree
CityList
CityInclusion
###
city_inclusion = # refer wikipedia
  '01._NY':['01.W04.0262','01.W04.0280','01.W04.0290','01.W04.0310','01.W04.0320','01.W05','01.C04.0620','01.C04.0630','01.C05','01.C06','01.C07','01.C11.0800','01.C12','01.C13','01.C14','01.C15','01._NY']
  '01._SC':['01.E04','01.E05','01.E06','01.E07','01.E08','01.E09','01.E10','01.E11','01._SC']
  '01._ET':['01.W06','01.W07','01.W08','01.W09','01.W10','01._ET']
  '01._DT':['01.C01.0890','01.C01.0960','01.C01.1001','01.C01.0900','01.C08','01._DT'] #'01.W01','01.W02','01.W03','01.C01','01.C02','01.C03','01.C08','01.C09','01.C10','01.C11','01.E01','01.E02','01.E03','01._DT']
  '01._OT':['01.W03.0400','01.W03.0390','01.W02.0410','01.W02.0420','01.W02.0440','01.W02.0460','01.W01','01.C02','01.C03.0740','01.C03.0750','01.C04.0710','01.C04.0700','01.C04.0690','01.C10','01.C09','01.C08','01.C01','01.E01','01.E02','01._OT']
  '01._EY':['01.E03','01.C11','01._EY']
  '01._YK':['01.W04.0300','01.W04.0350','01.W04.0330','01.W04.0340','01.W03.0360','01.W03.0370','01.W03.0380','01.W02.0430','01._YK']
city_list = [
  {k:'01._DT',n:"Toronto Downtown"} # value:name
  {k:'01._OT',n:"Toronto Old Toronto"} # value:name
  {k:'01._NY',n:"Toronto North York"}
  {k:'01._SC',n:"Toronto Scarborough"}
  {k:'01._ET',n:"Toronto Etobicoke"}
  {k:'01._EY',n:"Toronto East York"}
  {k:'01._YK',n:"Toronto York"}
  {k:'42.08',n:'London',noconsume:true}
  {k:'50.01',n:'Windsor',noconsume:true}
  # {k:'32',n:'Ottawa',noconsume:true}
  # {k:'X99.12',n:'Montreal',noconsume:true}
  # {k:'X99._CG',n:'Calgary',noconsume:true}
  '05.03' # municipality code only
  '05.02'
  '05.01'
  '06.04'
  '06.01'
  '06.02'
  '06.03'
  '09.'  # area code + . to get municipalities
  '07.'
  '10.'
  '40' # area code only for area name only
  '46'
  '01' # list toronto as a single city
  ]
city_list_fav = []
city_list_extra = []
city_list_object = {} # {01._DT:'Downtown Toronto',05.03:'Mississauga'}
city_tree = null # {05:{nm:'Peel',tp:'area',05.03:{nm:'Mississauga',tp:'municipality',05.03.01:{nm:'airport',tp:'coomunity'}}}}
community_list = {} # {01._DT:[{k:01.C01.0001,n:'Toronto C01'}]}

_city_list_resource = 'city_list_resource'


getCityInclusion = (city)->
  if city then return city_inclusion[city] or city
  return city_inclusion

DEF 'getCityTree',-> city_tree
DEF 'getCityList',-> city_list
DEF 'getCityListObject',-> city_list_object
DEF 'getCityInclusion',getCityInclusion
DEF 'getCommuniyList',(city)-> community_list[city]
DEF 'getCitySql',(k)-> city_list_object[k].sql
DEF 'getCityFavList',-> city_list_fav
DEF 'getCityExtraList',-> city_list_extra

# INREG 'rmGetCitySql',(k,cb)->
  # cb null,city_list_object[k].sql

###
PropertyTypes
PropertyTypeList
PropertyTypeInclusion
###
fc_property_type_inclusion =
  'Detached':['Detached','Det W/Com Elements','Duplex','Triplex','Fourplex','Rural Resid','Rural Residential','Cottage','Link','Det Condo']
  'Semi-Detached':['Semi-Detached','Semi-Det Condo']
  'Freehold Townhouse':['Att/Row/Twnhouse','Freehold Townhouse'] # last for self
  'Condo Townhouse':['Condo Townhouse']
  'Condo Apartment':['Condo Apt','Co-Op Apt','Comm Element Condo','Co-Ownership Apt','Leasehold Condo','Condo Apartment','Phased Condo']
  'Room':['Upper Level','Lower Level','Room','Shared Room','Time Share']
  'Parking Space':['Parking Space']
  'Land':['Vacant Land','Vacant Land Condo','Land'] # last for self
  'Other':['Other','Store W/Apt/Offc','Multiplex','Mobile/Trailer','Mobil/Trailer','Locker','Farm','Attached','0']
property_types =
  fc: (k for k of fc_property_type_inclusion)

ptypes = (TrebKeys,cb)->
  #TrebKeys.get_coll (err,coll)->
  TrebKeys.findToArray {_id:$in:['f191','c191','b191']},(err,list)->
    if err then return cb err
    #console.log list
    for l in list
      oo = []
      for o in l.opts
        if o?.length > 0
          oo.push o
      property_types[l._id.charAt(0)] = oo
    pt =
      fc_property_type_inclusion: fc_property_type_inclusion
      property_types: property_types
    cb null,pt
DEF 'ptypes',ptypes

property_type_objects = {} #[locale][bcf]{code:name}

_property_type_list_resource = 'property_type_list_resource'
PLSWAIT _property_type_list_resource
ptypes TrebKeys,(err,pt)->
  if err then throw err
  PROVIDE _property_type_list_resource

getPropertyTypes = (bcf,locale)->
  locale ?= 'en'
  return obj if (localeObjs = (property_type_objects[locale] ?= {})) and (obj = localeObjs[bcf])?
  return null if not (typeArray = property_types[bcf])?
  fn = i18n.getFun locale
  types = {}
  for t in typeArray
    types[t] = fn t
  return localeObjs[bcf] = types
getPropertyTypeInclusion = (type)->
  if type then return fc_property_type_inclusion[type] or type
  fc_property_type_inclusion
typeOut2In = (tp)->
  ret = []
  if Array.isArray tp
    for t in tp
      if t.length > 1
        if Array.isArray(tps = getPropertyTypeInclusion(t))
          ret = ret.concat tps
        else
          ret.push tps
  else if tp.length > 1
    if Array.isArray(tps = getPropertyTypeInclusion(tp))
      ret = ret.concat tps
    else
      ret.push tps
  ret

DEF 'getPropertyTypeInclusion',getPropertyTypeInclusion
DEF 'getPropertyTypeArray',(bcf)-> property_types[bcf] # b/c/f/fc
DEF 'getMPropertyTypeArray',(bcfs)->
  allTypes = {}
  for k in bcfs
    allTypes[k] = property_types[k]
  allTypes
DEF 'getPropertyTypes', getPropertyTypes
DEF 'getMPropertyTypes',(bcfs,locale = 'en')->
  allTypes = {}
  for k in bcfs
    allTypes[k] = getPropertyTypes k,locale
  allTypes

###
  Excluding Brokerages
###
excluded_brokerage = [
#  'COMFREE COMMONSENSE'
#  'COMMONSENSE NETWORK, BROKERAGE'
#  'COMFREE COMMONSENSE NETWORK, BROKERAGE'
#  'BUY DIRECT REALTY CORPORATION, BROKERAGE'
#  'ONE PERCENT REALTY LTD., BROKERAGE'
#  'POINT ZERO ONE REALTY LTD., BROKERAGE'
]

DEF 'getEXBrokerage',-> excluded_brokerage

maskListing = (l,remark)->
  for k,v of l
    switch k
      when 'ml_num','bcf','s_r'
        continue
      when 'lat','lng'
        delete l[k]
      when 'ad_text','addr','st','cross_st' #,'extras'
        l[k] = remark
      when 'lp_dol','pic_num'
        l[k] =  0
      else
        l[k] = '-'

DEF 'maskListing',maskListing

DEF 'maskListings',(listings,remark = "Please Login")->
  idx = 0
  for l in listings
    if l.idx isnt 'x' # not idx
      idx++
      maskListing l,remark


DEF 'setLang',(req)->
  if lang = req.param 'lang'
    lang = lang[0] if Array.isArray lang
    lang = i18n.locale lang
  else
    lang = req.locale()
  req.setLocale lang
  lang

WXObj = wechat.init SysData,conf.wechat?.appName,conf.wechat?.appId #,conf.wechatSecret
DEF 'getWXConfig',(req,url,cb)->
  unless req.isWeChat()
    return cb null
  try
    wxcfg = WXObj.getJsConfig url
  catch e
    debug.error e
    wxcfg = null
  cb null,wxcfg

DEF 'getWechatConfig',(req,url)->
  if not req.isWeChat()
    return null
  try
    wxcfg = WXObj.getJsConfig url
  catch error
    debug.error error,'url:',url
    wxcfg = null
  return wxcfg

DEF 'robotPreventionSetup',(req,cb)->
  ts = Date.now()
  hideKey = "_rp_hk_"  + Math.floor(Math.random() * 1000)
  hideVal = "v" + Math.ceil(Math.random() * 1000)
  obj = {ts:ts,hk:hideKey,hv:hideVal}
  # console.log '+++'
  # console.log obj
  req.session?.set 'robotPreven',obj,->
    if cb then cb null,obj

# return true means "is Robot"
DEF 'robotPreventionCheck',(req,cb)->
  obj = req.session?.get 'robotPreven'
  ret = true
  done = ->
    if obj? and (req.param('isApi') or req.body?.isApi)
      obj.ts = Date.now()
    else
      obj = null
    req.session?.set 'robotPreven',obj,->
      cb null,ret if cb
    ret
  # console.log '###'
  # console.log obj
  return done() if (not obj?) or (Date.now() - obj.ts < 3000)
  if (req.param(obj.hk) isnt obj.hv) and (req.body?[obj.hk] isnt obj.hv)
    return done()
  ret = false
  done()
  ret

# ADDPLUGIN 'getAppVerNumber',->
#   normalizeVer @cookies['apsv']

DEF 'appVersion',(req)->
  req.cookies['apsv'] or 'NOT-APP'

# used when share out side of RM
# promote.coffee (to 58 or yxhj)
# x_apiShare.coffee (RMSrv.share)
ADDPLUGIN 'hasViewed',(id)->
  req = @
  unless pvhist = req.session.get('pvhist')
    pvhist = {}
  ret = pvhist[id] is 1
  pvhist[id] = 1
  req.session.set 'pvhist', pvhist, ()->
  return ret

ADDPLUGIN 'isPad',()->
  if ua = @headers?['user-agent']
    return /iPad/i.test ua
  return not (/Mobile/i.test ua)

# TODO:?
# ADDPLUGIN 'isTestServer',()->
#   /d\d.realmaster.com/.test @.hostname

# ADDPLUGIN 'getGoogleAPILink',(cfg)->
  # return mapServer.getGoogleAPILink(cfg)

ADDPLUGIN 'exMapURL',(data)->
  ###
  can not open apple map in china, apple map will redirect to google map...
  ###
  # if @.isChinaIP()
  #   return "https://ditu.google.cn/maps/?q="
  # else
  #TODO: deal with chinaIP
  if @isIOSDevice()
    return 'https://maps.apple.com/?q='
  else
    return "https://maps.google.com/?q="

#do ->
ADDPLUGIN 'shareHost', () ->
  req = @
  #handle test server url update
  val = ''
  if conf.share?.host
    val = conf.share.host
  else if /d\d\.realmaster/.test(@.host)
    val = @.host
  else if /app.test/.test @.host
    val = 'www.realmaster.com'#@.host
  else
    val = 'www.realmaster.com'
  unless /^http/.test val
    val = 'https://'+val
  return val

ADDPLUGIN 'appHostUrl', () ->
  req = @
  #handle test server url update
  val = ''
  if conf.serverBase?.appHostUrl
    val = conf.serverBase.appHostUrl
  else if /d\d\.realmaster/.test(@.host)
    val = @.host
  else if /app.test/.test @.host
    val = 'app.test'#@.host
  else
    val = 'realmaster.com'
  unless /^http/.test val
    val = 'https://'+val
  return val

canonicalHost = conf.serverBase?.canonicalHost
ADDPLUGIN 'getCanonicalUrl', (url) ->
  curUrl = url or @url
  if canonicalHost
    return "#{canonicalHost}#{curUrl}"
  else
    return @fullUrl curUrl

ADDPLUGIN 'getConfig',(name)-> getConfig name

GroupModel = MODEL 'Group'

sysGroup = []

DEF 'sysGroupNameIdMapping',->
  map = {}
  for grp in sysGroup
    map[grp.nm] = grp._id
  map

getSysGroup = ()->
  nm = [':RmSales',':RmProp',':RmProject',':RmFeedback',':RmTopListing']
  try
    group = await GroupModel.findGroupsByName {flds:{_id:1,nm:1},nm}
  catch err
    debug.error err
  sysGroup = group if group
getSysGroup()
