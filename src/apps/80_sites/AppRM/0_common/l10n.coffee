# TODO: AUTO translate
#http://mymemory.translated.net/doc/spec.php

# accessAllowed = DEF '_access_allowed'
UserModel = MODEL 'User'
helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
I18n = COLLECTION 'chome', 'i18n'
debug = console.log
LANGUAGE_LIST = DEF 'LANGUAGE_LIST'

JT2FT = i18n.JT2FT
I18n.findToArray {'zh':{$exists:false},'zh-cn':{$exists:true}},{'zh-cn':1},(err,toTrans)->
  if (not err) and toTrans
    console.log "Translating zh-cn to zh (#{toTrans.length})[#{toTrans[0]}]"
    I18n.deleteOne {_id:'  '},{safe:true},(err)->
      doone = ->
        if c = toTrans.pop()
          zh = JT2FT c['zh-cn']
          I18n.updateOne {_id:c._id},{$set:{zh:zh}},{safe:true},(err)-> process.nextTick doone
      doone()

APP 'sys'
APP 'l10n',true
GET (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless UserModel.accessAllowed 'sysConfig',user
      return resp.redirect '/1.5/index'
    list = LANGUAGE_LIST.slice(1)
    resp.ckup 'list',{list:list,languages: i18n.language_list}
    ###
    I18nTodo.findToArray {},{_id:1},(err,l)->
      list = []
      for i in l
        list.push i._id
      resp.ckup 'list',{list:list,languages: i18n.language_list}
    ###

# POST: /sys/l10n
POST (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless UserModel.accessAllowed 'sysConfig',user
      return resp.redirect '/1.5/index'
    b = req.body
    if lang = b.lang # show edit page
      fields = {_id:1,orig:1}
      fields[lang] = 1
      q = {}
      console.log b
      return I18n.findToArray q,{sort:{_id:-1},projection:fields},(err,list)->
        if err then return resp.E500 err
        if b.ajax
          resp.send {list:list,thelang:lang,thename: i18n.language_list[lang]}
        else
          resp.ckup 'i18n',{thelang:lang,thename: i18n.language_list[lang]} # list:list
      # I18n.get_coll (err,coll)->
      #   if err then return resp.E500 err
      #   debug "Query:#{JSON.stringify(q)};fields:#{JSON.stringify(fields)}"
      #   coll.find(q,fields).sort({_id:1}).toArray (err,list)->
    else if lang = b.thelang # submit from edit page
      total = b.total_line
      rs = {}
      tt = {}
      if b.t
        t = b.t.split /\n\r|\r\n/g
        debug t
        for i in t
          if i.length > 2
            m = i.match /^\s*(\d+)[\.|\。|\s]\s*(.*)$/
            debug m
            if not m?
              debug "BAD: #{i}"
            else
              tt[m[1]] = m[2]
        debug tt
      debug b
      for i in [0..total]
        if b["x#{i}"]
          I18n.deleteOne {_id:b["l#{i}"]}
        else
          s = tt[i] or b["t#{i}"]
          if helpers.trim(s).length > 0
            r = {}
            r[lang] = s
            rs[b["l#{i}"]] = r
      done = (err,all)->
        if err then return resp.ckup 'error',{err:err}
        debug all
        i18n.reload ->
          resp.redirect req.app.url()
      doone = (r,k,cb)->
        I18n.updateOne {_id:k},{$set:r},{safe:true},cb
      helpers.map rs,doone,done
    else
      resp.redirect req.app.url()

POST 'ajax',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless UserModel.accessAllowed 'sysConfig',user
      return resp.redirect '/1.5/index'

    b = req.body
    if b.refresh
      i18n.reload ->
        resp.send {ok:1}
    else if b.d
      I18n.deleteOne {_id:b.d},{safe:true},(err)->
        if err then return resp.send err
        resp.send {d:true}
    else
      set = {ts:new Date()}
      set[b.l] = b.s
      set['zh'] = JT2FT b.s if (b.l is 'zh-cn') and b.s
      I18n.updateOne {_id:b.k},{$set:set},{safe:true},(err)->
        if err then return resp.send err
        i18n.reload b.k,(err)->
          if err then return resp.send err
          resp.send {u:b.s}

LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset:'utf-8'
      meta name:"viewport",content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      #meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'
      meta name:"apple-mobile-web-app-capable",content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"
      meta name:"format-detection",content:"telephone=no"
      meta name:'googlebot',content:'noindex,nofollow' if @noIndex
      g4tags js:new Date()
      title -> _ "RealMaster"
      js '/js/jquery-1.10.1.min.js'
      js '/js/angular.min.js'
    body ->
      g4body()
      div -> a href:'/1.5/index',-> _ "Back"
      div class:'rootbox',-> text @content()

VIEW 'i18n',->
  div class:'input_box',ngApp:'appL10n',ngController:'ctrlL10n',->
    div class:'input_box_title',-> text _ @thename
    form action:@req.app.url(),method:'POST',->
      style -> """
      ul {list-style: none;    padding: 0;    margin: 0;}
      li:nth-child(even) {background: #FFF}
      li:nth-child(odd) {background: #EFE}
      li {padding: 5px 5px; border-top: solid 1px #ddd;}
      span.key {text-align:left;}
      input.remove {float:right;}
      input.val {width:98%;}
      """
      div ->
        input id:'filter',style:'width:65%;',ngModel:'strFilter',ngBlur:'filter()'
        input id:'filter-btn',type:'button',style:'float:right;',value:_("Filter"),ngClick:'filter()'
        input id:'filter-btn',type:'button',style:'float:right;margin-right:2%',value:_("Search"),ngClick:'search()'
      input type:'hidden',name:'thelang',id:'id_thelang',value:@thelang
      div ngShow:"err",ngClick:"err = null",-> "{{err}}"
      div -> "{{list.length}}{{overflow}}/{{total}} {{thename}}({{thelang}})"
      ul ->
        li id:"row-{{$index}}",ngRepeat:"l in list track by $index",->
          div ->
            span class:'key',->
              #text "{{$index}}."
              span class:'kv',-> "{{l.o}}({{l.id}})"
            input type:'button',id:"remove-{{$index}}",class:'remove',ngClick:'remove($index)',value:_("Del")
          div ->
            input type:'hidden',name:"l{{$index}}",id:i,ngModel:"l.id"
            input name:"t{{$index}}",id:"new-{{$index}}",class:'new val',ngBlur:"changed($index)",ngModel:"l.v"
      hr()
      input type:'button',value:_("Reload"),ngClick:"reload()"
      input type:'button',value:_("Refresh APP"),ngClick:"refresh()",style:"float:right;"
COFFEE 'i18n',->
  app = angular.module 'appL10n',[]
  app.controller 'ctrlL10n',['$http','$timeout','$scope',($http,$timeout,$scope)->
    MAX = 90
    lang = $("#id_thelang").val()
    all = {}
    findError = (err)->
      $scope.err = err
      $timeout ->
        $scope.$apply()
    $scope.reload = ->
      $http.post('/sys/l10n',{ajax:1,lang:lang}).error(findError).success (ret)->
        console.log ret
        console.log lang
        for n in ret.list
          all[n._id] = {o:n.orig,v:n[lang]}
        $scope.thelang = ret.thelang
        $scope.thename = ret.thename
        $scope.total = Object.keys(all).length
        $scope.filter()
        #$scope.$apply()
    $scope.refresh = ()->
      $http.post( "/sys/l10n/ajax",{refresh:1}).error(findError).success (r)->
        if r.ok
          window.locaction = window.location
    $scope.remove = (idx)->
      console.log idx
      n = $scope.list[idx]
      console.log n
      $http.post( "/sys/l10n/ajax",{d:n.id}).error(findError).success (r)->
        if r.d
          $scope.list.splice(idx,1)
          delete all[n._id]
        else
          console?.log r
        $scope.total = Object.keys(all).length
        #$scope.$apply()
    $scope.changed = (idx)->
      n = $scope.list[idx]
      $http.post("/sys/l10n/ajax",{k:n.id,s:n.v,l:lang}).error(findError).success (r)->
        if r.u
          all[n.id].v = r.u
          item = $("#new-#{idx}")
          item.fadeOut().fadeIn()
        else
          console?.log r
    $scope.filter = ->
      list = []
      m = $scope.strFilter?.toLowerCase()
      #reg = new RegExp $scope.strFilter,'i'
      l = 0
      $scope.overflow = ""
      #console.log "filter:#{m}"
      for k,n of all
        #console.log "#{k}=>#{k.indexOf(m)}"
        if k?.indexOf(m) >= 0
          list.push {id:k,v:n.v,o:n.o}
          if l++ > MAX
            $scope.overflow = "++"
            break
      $scope.list = list
      $timeout -> $scope.$apply()
    $scope.search = ->
      list = []
      m = $scope.strFilter?.toLowerCase()
      #reg = new RegExp $scope.strFilter,'i'
      l = 0
      $scope.overflow = ""
      reg = new RegExp("^\(#{m}|#{m}\:[a-z0-9]*\)$")
      for k,n of all
        if reg.test k
          list.push {id:k,v:n.v,o:n.o}
          if l++ > MAX
            $scope.overflow = "++"
            break
      $scope.list = list
      $timeout -> $scope.$apply()

    $scope.reload()
  ]
  null

VIEW 'list',->
  div class:'input_box_group',->
    div class:'input_box',->
      div class:'input_box_title',->
        text _ 'Localization & Internationalization'
      form id:'id_form',action:@req.app.url(),method:'POST',->
        select name:'lang',->
          for lang in @list
            option value:lang,-> text _(@languages[lang] or lang)
        input type:'hidden',name:'mode',id:'id_input_mode',value:'all'#'batch'
        input type:'button',
        id:'id_submit_mode_all',
        value: _ 'GO'
        #input type:'submit',value: _ "Batch Edit"
  # coffeejs -----
  coffeejs ->
    form = $('#id_form')
    $('#id_submit_mode_all').on 'click',->
      $('#id_input_mode').val 'all'
      form.submit()
    null



VIEW 'i18n_batch',->
  div class:'input_box',->
    div class:'input_box_title',-> text _ @thename
    form action:@req.app.url(),method:'POST',->
      max = 0
      input type:'submit',value:_ "SAVE"
      input type:'button',value:_("Cancel"),onclick:"javascript:location.href='#{@req.app.url()}'"
      br()
      input type:'hidden',name:'batchmode',value:'1'
      input type:'hidden',name:'thelang',id:'id_thelang',value:@thelang
      table class:'i18n',->
        tr ->
          trans = []
          td ->
            orig = []
            for l,i in @list
              input type:'hidden',name:"l#{i}",id:"id_l#{i}",value:l._id
              orig.push "#{i}. #{l._id}"
              trans.push "#{i}. #{l[@thelang] or ''}"
              max = i
            div ->
              text orig.join "<br />"
          td style:'vertical-align: top;',->
            textarea name:"t",id:'id_t',style:'min-height:100%;',-> text trans.join '\n'
      span class:'input_box_help',-> #text _ "To remove the line from database, ."
      br()
      input type:'hidden',name:'total_line',id:'id_total_line',value:max
      #input type:'button',value:_("Auto Translate"),id:'id_btn_autotrans'
      input type:'submit',value:_ "SAVE"
      input type:'button',value:_("Cancel"),onclick:"javascript:location.href='#{@req.app.url()}'"

STYLUS 'i18n_batch','''
table.i18n
  width 100%
  td
    width 50%
textarea
  height 100%
  width 100%
'''
