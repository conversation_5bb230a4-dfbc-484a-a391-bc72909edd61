checksum = INCLUDE 'lib.checksum'
ProvAndCity = MODEL 'ProvAndCity'
libProperties = INCLUDE 'libapp.properties'

# verGTE = DEF 'verGTE'
getConfig = DEF 'getConfig'

SysData = COLLECTION 'chome', 'sysdata'
GroupModel = MODEL 'Group'
config = CONFIG()
pushNotifyModel = MODEL 'PushNotify'

checkVersionNShow = (req, resp, need, cb) ->
  if req.verGTE need
    return cb true if cb
    return true
  ctx =
    title: req.l10n 'Upgrade Core Version'
    msg: req.l10n 'A new version of APP Core is available. You need to upgrade APP Core to the latest version before using this new features.'
    link:
      nm: req.l10n 'Upgrade Now'
      url: '/getapp'
  resp.ckup 'generalMessage', ctx, "_", {noUserModal: true}
  return false
DEF 'checkVersionNShow', checkVersionNShow

# give W/Y/I return suffix
get_shared_to_from_share = (src)->
  # vcwb / vcdz / vcyx
  dataTo =
    'W' : 'wb'
    'Y' : 'yx'
    'I' : 'dz'
    'A' : 'app'
  dataTo[src] or src or ''
DEF 'get_shared_to_from_share', get_shared_to_from_share

msgIdCntr = 1
sendOneMessage = ({to, msg, url, title})->
  unless msg and to.pn
    return
  pn =
    to: to.pn
    from: 'RealMaster'
    msg: msg
    id: msgIdCntr++
  pn.url = url if url
  pn.title = title if title
  # pn.launchImage = launchImage if launchImage
  pushNotifyModel.send pn
DEF 'sendOneMessage',sendOneMessage


# TODO: Deprecated, remove after new prop detail/list
popularCities = {list:[], mapping:{}}
# load from hard-coded cities, read own ctrl from sysdata
loadPopularCities = ()->
  cities = ProvAndCity.getPopularCitiesList().list.slice()
  # for c in cities
  #   path = c.prov+'.'+c.city
  #   c.own = if Math.random()*10 > 5 then true else false
  # popularCities = cities
  # return
  SysData.findOne {_id:'ownCtrlCity'},(err, ret)->
    if err or not ret
      popularCities.list =  cities
      return
    for c in cities
      city = libProperties.formatCityName(c.city)
      # path = c.prov+'.'+city
      tmp = ret[c.prov]?[city]
      unless 'object' is typeof tmp
        tmp ?= 0
        tmp = {sale:tmp, rent:0} # compitable with previous data
      c.own = tmp
    popularCities.list = cities
    popularCities.mapping = ret
loadPopularCities()

# TODO: Deprecated, see provAndCity.isOwnCtrlContactCity
isOwnCtrlContact = (prop = {})->
  prov = prop.prov
  city = prop.city
  cmty = prop.cmty
  unless prov and city
    return false
  city = libProperties.formatCityName(city)
  ret = popularCities.mapping or {}
  unless tmp = ret[prov]?[city]
    return false
  unless 'object' is typeof tmp
    tmp = {sale:0, rent:0}
  isSale = /Sale/.test prop.saletp?.toString()
  return (tmp.sale and isSale) or (tmp.rent and !isSale)


DEF 'isOwnCtrlContact', isOwnCtrlContact
DEF 'popularCitiesWithOwnControlData', popularCities
DEF 'loadPopularCities', loadPopularCities

ADDPLUGIN 'userGroups',GroupModel.getUserGroups
ADDPLUGIN 'userGroupIDs',GroupModel.getUserGroupIDs

topPropertiesByProvCache = {}
newPropsByCityCache = {}
DEF 'topPropertiesByProvCache',topPropertiesByProvCache
DEF 'newPropsByCityCache',newPropsByCityCache
removePopCityCacheByProvAndCity = (prov,city)->
  pc = prov+':'+city
  # delete newPropsByCityCache[pc]
  delete topPropertiesByProvCache[prov]
DEF 'removePopCityCacheByProvAndCity',removePopCityCacheByProvAndCity

#true -> online
customerServicesStatus = {
  list:{
   'info':true,
   'payment':false,
  }
}
DEF 'customerServicesStatus',customerServicesStatus
