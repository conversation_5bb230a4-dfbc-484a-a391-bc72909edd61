Transactions = COLLECTION 'chome','transactions'
sendMail = SERVICE 'sendMail'
UserModel = MODEL 'User'
sendMailLib = INCLUDE 'lib.sendMail'
libUser = INCLUDE 'libapp.user'

emailDateFormat = (now)->
  return '' unless now
  monthNames = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
  ]
  return monthNames[now.getMonth()]+' '+now.getDate()+', '+now.getFullYear()

config = CONFIG(['share','contact'])
#https://stackoverflow.com/questions/149055/how-can-i-format-numbers-as-money-in-javascript
formatMoney = (n, c, d, t) ->
  # n = this
  c = if isNaN(c = Math.abs(c)) then 2 else c
  d = if d == undefined then '.' else d
  t = if t == undefined then ',' else t
  s = if n < 0 then '-' else ''
  i = String(parseInt(n = Math.abs(Number(n) or 0).toFixed(c)))
  j = if (j = i.length) > 3 then j % 3 else 0
  s + (if j then i.substr(0, j) + t else '') + i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + t) + (if c then d + Math.abs(n - i).toFixed(c).slice(2) else '')

getHstAndRealPrice = (v)->
  realPrice = v/1.13
  tax = v - realPrice
  return [realPrice, tax]

generateEmailM = (req, bill, charge, user, startTs, newTs, cfg)->
  PURCHASE_TYPE = bill.PURCHASE_TYPE or 'Listing Promotion'
  INVOICE_NUMBER = bill._id?.toString() or charge.id
  now = if startTs then new Date startTs else new Date()
  PAID_DATE = emailDateFormat(new Date())
  FORMATED_DATE = emailDateFormat(now)
  amount = parseFloat(charge.amount_total/100)
  endDateTime = null #if newTs then newTs else new Date(+now+amount/PRICE_PER_DAY*24*3600)
  if newTs
    endDateTime = newTs
  FORMATED_DATE_RANGE = 'Effective date: '+FORMATED_DATE+' - '+emailDateFormat(endDateTime)
  AMOUNT_STR = '$'+formatMoney(amount, 2, '.', ',')
  [REAL_PRICE,HST] = getHstAndRealPrice(amount)
  REAL_PRICE = '$'+formatMoney(REAL_PRICE, 2, '.', ',')
  HST = '$'+formatMoney(HST, 2, '.', ',')
  USER_EML = if Array.isArray user.eml then user.eml[0] else user.eml
  USER_FNM = libUser.fullNameOrNickname(user.locale, user)
  ADDRESS = (req.param('addr') or '')+' '+(req.param('city') or '')
  if unt = req.param('unt')
    ADDRESS = unt+' '+ADDRESS
  id = req.param('id')

  # console.log '++++++'+id
  if /^TRB/.test(id)
    ADDRESS += ' ('+id.substr(3)+')'
  if cfg.sendNotice
    GSTHST = ""
    invoiceNum = ""
    mainTitle = "Your Top Listing Effective #{FORMATED_DATE}"
    title = "Notice"
    shareHost = config.share.host or 'https://www.realmaster.com'
    bottom = """
    <tr><td style="font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3">Customer</td></tr>
    <tr><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 18px" class="p"><a href="mailto:<EMAIL>" style="color: #5f6a7d;text-decoration: none;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" target="_blank">Username: #{user.eml}</a></td></tr>
    <tr><td style="border-top-color: #e0e0e0;padding: 18px 0;border-top-style: solid;border-top-width: 1px;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p">Thank you for your support! If you have any questions, feel free to contact us.<br>
        <h4>How to track your listing performance?</h4>
		1. Download RealMaster App. <a href="#{shareHost}/app-download?lang=en">Download Now</a><br>
		2. Use above account to sign in.<br>
		3. Search your listing by MLS# #{cfg.prop.sid}.<br>
    <h4>Learn more about RealMaster Top Listing</h4>
    <a href="https://www.realmaster.ca/top-listing">Top Listing</a>
</td></tr>
    """
  else
    GSTHST = """<tr><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 16px" class="p">GST/HST: <span style="border-bottom:1px dashed #ccc;z-index:1" t="7" onclick="return false;" data="*********">********* RT 0001</span><br></td></tr>"""
    invoiceNum = """Invoice #<span style="border-bottom:1px dashed #ccc;z-index:1" t="7" onclick="return false;" data="000001">#{INVOICE_NUMBER}</span><br>"""
    mainTitle = "#{AMOUNT_STR} paid on #{PAID_DATE}"
    title = "Invoice"
    bottom = """
    <tr><td style="font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3">Customer</td></tr>
    <tr><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 18px" class="p">#{USER_FNM}<br><span data-href="mailto:#{USER_EML}" style="color: #5f6a7d;text-decoration: none;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" target="_blank">#{USER_EML}</a></td></tr>
    <tr><td style="border-top-color: #e0e0e0;padding: 18px 0;border-top-style: solid;border-top-width: 1px;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p">Thank you for your support!</td></tr>
<tr><td style="border-top-width: 1px;border-top-color: #e0e0e0;border-top-style: solid;padding: 6px 0 12px"><table class="tabDat" style="text-align: left" cellspacing="0" cellpadding="0">
<tbody><tr style="vertical-align: top"><td colspan="2" style="padding-bottom: 12px" valign="top"><table style="text-align: left" cellspacing="0" cellpadding="0">
    <tbody><tr style="vertical-align: top">
      <td style="color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold;font-size: 14px;line-height: 22px;padding: 16px 0 0px" class="p key" width="450" valign="top"><div style="padding-right: 20px"><table style="table-layout: fixed;text-align: left" cellspacing="0" cellpadding="0" width="100%"><tbody><tr style="vertical-align: top"><td style="font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3" valign="top">#{PURCHASE_TYPE}</td></tr></tbody></table></div></td>
      <td style="color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold;font-size: 14px;line-height: 22px;padding: 16px 0 0px" class="p value" align="right" width="70" valign="top">#{AMOUNT_STR}</td>
    </tr>
</tbody></table></td></tr>
<!--
<tr style="vertical-align: top">
    <td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p key" align="left" width="450" valign="top"><div style="padding-right: 20px"><table style="table-layout: fixed;text-align: left" cellspacing="0" cellpadding="0" width="100%"><tbody><tr style="vertical-align: top"><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 18px" class="p" valign="top">Discount</td></tr></tbody></table></div></td>
    <td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p value" align="right" width="70" valign="top">0</td>
  </tr> -->
<tr style="vertical-align: top">
    <td style="border-top-width: 1px;padding: 18px 0 0px;border-top-color: #e0e0e0;border-top-style: solid;color: #2d394e;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal" class="p key" width="450" valign="top"><div style="padding-right: 20px"><table style="table-layout: fixed;text-align: left" cellspacing="0" cellpadding="0" width="100%"><tbody><tr style="vertical-align: top"><td style="color: #2d394e;font-size: 14px;line-height: 22px;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3" valign="top">Sub-Total</td></tr></tbody></table></div></td>
    <td style="border-top-style: solid;padding: 18px 0 0px;border-top-color: #e0e0e0;border-top-width: 1px;color: #2d394e;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal" class="p value" align="right" width="70" valign="top">#{REAL_PRICE}</td>
  </tr>
<tr style="vertical-align: top">
    <td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p key" align="left" width="450" valign="top"><div style="padding-right: 20px"><table style="table-layout: fixed;text-align: left" cellspacing="0" cellpadding="0" width="100%"><tbody><tr style="vertical-align: top"><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p" valign="top">HST (13%)</td></tr></tbody></table></div></td>
    <td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d" class="p value" align="right" width="70" valign="top">#{HST}</td>
  </tr>
<tr style="vertical-align: top"><td colspan="2" style="font-size: 0px;line-height: 0px" height="18" valign="top">&nbsp;</td></tr>
    <tr style="vertical-align: top">
      <td style="border-top-color: #e0e0e0;padding: 18px 0 0px;border-top-width: 1px;border-top-style: solid;font-size: 16px;line-height: 26px;font-weight: bold;font-family: sqmarket-medium, Helvetica, sans-serif;color: #2d394e" class="p key" width="450" valign="top"><div style="padding-right: 20px"><table style="table-layout: fixed;text-align: left" cellspacing="0" cellpadding="0" width="100%"><tbody><tr style="vertical-align: top"><td style="font-size: 16px;line-height: 26px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3" valign="top">Total Paid</td></tr></tbody></table></div></td>
      <td style="border-top-width: 1px;padding: 18px 0 0px;border-top-color: #e0e0e0;border-top-style: solid;font-size: 16px;line-height: 26px;font-weight: bold;font-family: sqmarket-medium, Helvetica, sans-serif;color: #2d394e" class="p value" align="right" width="70" valign="top">#{AMOUNT_STR}</td>
    </tr>
    """
  INVOICE_STR = """
<table style="width: 100%;" cellspacing="0" cellpadding="0" bgcolor="#222222"><tbody><tr><td style="width: 100%;" bgcolor="#ffffff" align="center" width="500">
  <table class="single default invert" cellspacing="0" cellpadding="0" bgcolor="#e76c6d" width="100%"><tbody><tr><td style="padding: 0 20px" align="center"><table cellspacing="0" cellpadding="0"><tbody><tr><td style="padding: 32px 0 0px" class="singleCell" width="520"><table class="center" style="text-align: center" cellspacing="0" cellpadding="0" width="100%">
    <tbody><tr><td class="icon" align="center"><table style="text-align: center" cellspacing="0" cellpadding="0" width="inherit"><tbody><tr><td style="padding: 0px 0 5px"><img src="https://d2isyty7gbnm74.cloudfront.net/hKZP4D17iRCbo7Q9cSYeg4tGJ2I=/68x68/https://d1g145x70srn7h.cloudfront.net/files/241ea87c5ecee526b8b7a7ee4d63a16a2ff2130c/original.png" style="display: inline-block;-webkit-border-radius: 11px;-moz-border-radius: 11px;border-radius: 11px;border: 0px solid #ffffff" height="64" width="64"></td></tr></tbody></table></td></tr>
    <tr><td style="font-size: 16px;line-height: 19px;color: #ffffff;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold;padding: 0px 0 17px" class="h3">Realmaster Technology Inc.</td></tr>
    <tr><td align="center"><div style="width: 0;height: 0;border-left-width: 11px;border-left-color: transparent;border-left-style: solid;border-right-width: 11px;border-right-color: transparent;border-right-style: solid;border-bottom-width: 11px;border-bottom-color: #ffffff;border-bottom-style: solid"></div></td></tr>
  </tbody></table></td></tr></tbody></table></td></tr></tbody></table>
  <table class="single default" cellspacing="0" cellpadding="0" bgcolor="#ffffff" width="100%"><tbody><tr><td style="padding: 0 20px" align="center"><table cellspacing="0" cellpadding="0"><tbody><tr><td style="padding: 29px 0" class="singleCell" width="400"><table class="left" style="text-align: left" cellspacing="0" cellpadding="0" width="100%">
    <tbody><tr><td style="color: #2d394e;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 32px;padding: 0px 0 8px" class="h1" align="center">#{title}</td></tr>
    <tr><td align="center"><table style="text-align: left" cellspacing="0" cellpadding="0"><tbody><tr><td style="color: #5f6a7c;font-size: 16px;line-height: 24px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal" class="p" align="center">#{mainTitle}</td></tr></tbody></table></td></tr>
<tr><td style="font-size: 0px;line-height: 0px" height="28">&nbsp;</td></tr>
    <tr><td style="border-top-style: solid;padding: 19px 0 0px;border-top-width: 1px;border-top-color: #e0e0e0;font-size: 16px;line-height: 26px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3">#{PURCHASE_TYPE}</td></tr>
    <tr><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 18px" class="p">#{invoiceNum}#{FORMATED_DATE_RANGE}<br>Address: #{ADDRESS}</td></tr>
#{bottom}
</tbody></table></td></tr>
  </tbody></table></td></tr></tbody></table></td></tr></tbody></table>
  <table class="single default" cellspacing="0" cellpadding="0" bgcolor="#eeeeee" width="100%"><tbody><tr><td style="padding: 0 20px" align="center"><table cellspacing="0" cellpadding="0"><tbody><tr><td style="padding: 29px 0 38px" class="singleCell" width="400"><table class="center" style="text-align: center" cellspacing="0" cellpadding="0" width="100%">
    <tbody><tr><td style="font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" class="h3">Realmaster Technology Inc.</td></tr>
    <tr><td style="font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 16px" class="p">
<br><a href="mailto:#{config.contact?.defaultReciveEmail or '<EMAIL>'} style="color: #5f6a7d;text-decoration: none;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold" target="_blank">#{config.contact?.defaultRTEmail}</a><br><a href="tel:************"><span style="border-bottom:1px dashed #ccc;z-index:1" t="7" onclick="return false;" data="************">************</span></a><br>
</td></tr>
#{GSTHST}

      </tbody></table></td></tr></tbody></table></td></tr></tbody></table>
</td></tr></tbody></table>
  """
DEF 'generateEmailM',generateEmailM

#| operation | amount | trans-type | pay_tp | prod  | prod_desc |
#| Recharge  | +      | Deposit    | *      | /
#| Gift      | +      | Deposit    | Gift   | /
#| RefundProd| +      | Deposit    | Refund | TA/VIP|
#| Refund    | -      | Refund     | *      | /
#| Purchase  | -      | Subscripti | Balance| TA/VIP|
#one of Deposit/Refund/Subscription
getTransactionType = (pay_tp,amount,act)->
  if ('Balance' is pay_tp) and amount < 0
    return 'Subscription'
  if /Refund|Gift/.test(pay_tp)
    return 'Deposit'
  if amount < 0
    return 'Refund'
  else
    return 'Deposit'

getOperationType = (pay_tp,amount,act)->
  if pay_tp is 'Gift'
    return 'Gift'
  if (pay_tp is 'Refund')# and (act is 'refund')
    return 'RefundProd'
  if (amount < 0) and (pay_tp is 'Balance')
    return 'Purchase'
  if (amount > 0) and (pay_tp not in ['Gift', 'Refund'])
    return 'Recharge'
  if amount < 0
    return 'Refund'
  return 'Error'

generateTransaction = (req,user,cb)->
  t = {}
  eml = req.param 'eml'
  amount = parseFloat(req.param('amount'))
  act = req.param 'act'
  cpmnm = req.param 'cpmnm'
  cpmid = req.param 'cpmid'

  unless act in ['add-bal','purchase']
    return cb 'Not supported action'
  unless eml
    return cb 'Bad param eml'
  if isNaN amount
    return cb 'Bad param amount'
  amount = parseInt(amount*100)/100
  if act is 'add-bal'
    pay_tp = req.param 'pay_tp'
    t.pay_tp = pay_tp
    if (pay_tp is 'Refund') and (amount < 0)
      return cb 'Real Refund amount Should > 0'
  else if act is 'purchase'
    if amount > 0
      return cb 'Purchase amount has to < 0, eg. -600'
    city = req.param 'city'
    prov = req.param 'prov'
    # unless city and prov
    #   return cb 'No city and prov'
    start_dt = req.param 'start_dt'
    end_dt = req.param 'end_dt'
    # unless start_dt and end_dt
    unless start_dt
      return cb 'No start ts'
    #TA/VIP/TL
    prod = []
    if req.param 'ta1'
      t.ta1 = true
      prod.push 'TA1'
    if req.param 'ta2'
      t.ta2 = true
      prod.push 'TA2'
    if cpmid
      t.cpm = true
      prod.push "CPM"
    t.start_dt = start_dt
    t.end_dt = end_dt
    t.start_ts = new Date(start_dt)
    t.end_ts = new Date(end_dt) if end_dt
    if t.start_ts > t.end_ts
      return cb 'Incorrect date range'
    t.city = city
    t.prov = prov
    t.pay_tp = 'Balance'
    t.prod = prod
    if cpmid
      t.prod_desc = if cpmnm then 'CPM-'+cpmnm else "CPM-"+cpmid
    else
      t.prod_desc = 'TA-'+city+'-'+prod

  t.ts = new Date()
  t.cpmid = cpmid
  t.uid = req.param 'uid'
  t.ueml = eml
  t.operator = user._id
  t.opeml = if Array.isArray(user.eml) then user.eml[0] else user.eml
  t.operation = getOperationType(t.pay_tp,amount,act)
  if t.operation is 'Error'
    return cb 'Error getting op type'
  t.trans_type = getTransactionType(t.pay_tp,amount,act)
  if t.uid.toString() is t.operator.toString()
    console.error "**Error: Transaction #{t.trans_type} t.uid == t.operator #{t.uid} #{t.opeml}"
    return cb "Can not add/reduce balance for self."
  # if t.trans_type is 'Error'
  #   return cb 'Error getting trans type'
  t.amount = amount
  return cb null, t

#update user balance or prop ads board;
updateUserOrAdsBorard = (trans, cb)->
  # console.log trans
  amount = trans.amount
  trans_type = trans.trans_type
  if (trans_type is 'Deposit') and (amount < 0)
    return cb 'Deposit should be postive'
  UserModel.findByEml trans.ueml,{}, (err,retu)->
    if err or not retu?
      return cb err or MSG_STRINGS.USER_NOT_FOUND
    balance = retu.balance or 0
    if balance + amount < 0
      return cb 'Not enough balance'
    UserModel.incBalance trans.ueml, {balance,amount}, (err,ret)->
      if err
        console.log err
        return cb err
      cb null, retu

#use template form topup
sendReceiptToUser = (req,trans,user,cb)->
  # TODO: remove this
  return cb null,'done but didnt sent email receipt in test mode' unless trans.doSend
  amount = Math.abs(trans.amount)*100
  newTs = trans.end_ts
  if trans.operation is 'Recharge'
    trans.PURCHASE_TYPE = 'RM Account Balance'
  if trans.operation is 'Purchase'
    trans.PURCHASE_TYPE = 'RM '
    if trans.ta1
      trans.PURCHASE_TYPE += 'TA01'
    else if trans.ta2
      trans.PURCHASE_TYPE += 'TA02'
  if trans.operation is 'Refund'
    trans.PURCHASE_TYPE = 'RM Refund'
  body = generateEmailM(req,trans,{amount:amount},user,trans.start_ts, newTs)
  opt = {subject:'Your Receipt from RealMaster'}
  notifyArray = []#'<EMAIL>'
  eml = if Array.isArray user.eml then user.eml[0] else user.eml
  notifyArray.push eml
  errs = []
  jobs = []
  # TODO: handle ses not working
  engine = opt.engine or 'SES' # 'gmail'
  for to in notifyArray
    mail =
      _trans: 'smtp'
      engine: engine
      from: sendMail.getFromEmailByEngine(engine)
      replyTo: opt.replyTo or "RealMaster Technology Inc.<#{config.contact?.defaultRTEmail}>"
      to: to
      subject: opt.subject or 'New Message From RealMaster APP'
      text: body
      html: body
      isNeedValidate: true
      eventType: 'Transaction'
    # console.log mail
    jobs.push mail
  # return cb null,'sent'
  doOne = ()->
    job = jobs.pop()
    return cb null,'done' unless job
    sendMail.sendMail mail,(err,response)->
      if err
        console.error err
        return cb err
      doOne()
  doOne()

addTransaction = (req, resp, user, cb)->
  generateTransaction req,user,(err,trans)->
    if err
      console.error err
      return cb err
    updateUserOrAdsBorard trans, (err,retu)->
      if err
        console.error err
        return cb(err)
      Transactions.insertOne trans,(err,ret)->
        if err or (not ret)
          console.error err
          return cb(err)
        # console.log 'insertOne+++++'
        # console.log ret.ops[0]._id
        trans._id = ret.ops?[0]?._id or ret.insertedId or 'ErrNoInsertId'
        sendReceiptToUser req,trans,retu,(err,ret)->
          if err
            console.error err
            return cb(err)
          return cb(null, ret)

DEF 'getTransactionType',getTransactionType
DEF 'getOperationType',getOperationType
DEF 'sendReceiptToUser',sendReceiptToUser
DEF 'addTransaction',addTransaction


APP '1.5'
APP 'transaction',true

POST 'add',(req, resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp},(user)->
    return error('Need Login') unless user
    return error('not authed') unless req.isAllowed 'productSales'
    addTransaction req, resp, user,(err, ret)->
      if err
        return resp.send {ok:0, err:err}
      resp.send {ok:1,msg:ret}

POST 'all',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  uid = req.param 'uid'
  return error('Bad Param uid') unless uid
  UserModel.appAuth {req,resp},(user)->
    return error('Need Login') unless user
    return error('not authed') unless req.isAllowed 'productSales'
    Transactions.findToArray {uid:uid},(err,list)->
      if err
        console.error err
        return error err
      return resp.send {ok:1, list:list}

#resend invoice
POST 'invoice',(req,resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  unless req.isAllowed 'productSales'
    return error('not authed')
  _id = req.param '_id'
  return error('Bad Param _id') unless _id
  Transactions.findOne {_id:_id},(err,trans)->
    if err
      console.error err
      return error err
    # console.log trans
    UserModel.findOneById trans.uid,{},(err,retu)->
      if err
        console.error err
        return error(err)
      trans.doSend = true
      sendReceiptToUser req,trans,retu,(err,ret)->
        if err
          console.error err
          return error(err)
        return resp.send {ok:1,msg:'email sent'}
