fs = require 'fs'
path = require 'path'
dots = INCLUDE 'lib.dot'
GroupModel = MODEL 'Group'
conf = CONFIG('emailTemplate')

genTemplateFromInfo = (obj)->
  ret = {
    owner:obj.owner or 'all',
    name: obj.name,
    name_zh: obj.name_zh,
    entry: obj.entry,
    description:obj.description,
    description_zh:obj.description_zh,
    folder:obj.folder,
    fileText:obj.fileText,
    tempFn:obj.tempFn,
    msg:obj.msg,
    group:obj.group,
    imgPath:obj.imgPath,
    sort:obj.sort
  }
  if obj.demoimg
    ret.demoimg = '/img/shareimg/'+obj.folder+'/'+obj.demoimg
  else
    ret.demoimg = '/img/noPic.png'
  return ret


loadDotTemplates = (cfg,cb)->
  templateFolderPath = cfg.templateFolderPath
  unless templateFolderPath
    return cb 'No template folder path'
  #job is folder name under templateFolderPath
  fnImg = (job)->
    return '/img/shareimg/'+job+'/'
  if cfg.fnImg
    fnImg = cfg.fnImg
  templateList = []
  groups = []
  fs.readdir templateFolderPath,{},(err, items)->
    return cb err if err
    items ?= []
    doOneJob = ()->
      job = items.pop()
      unless job
        uniqueGroups = groups.filter((item, pos) ->
          groups.indexOf(item) is pos
        )
        return cb null,{templateList,groups:uniqueGroups}
      cpath = path.join(templateFolderPath, job)
      fs.stat cpath,(err,stats)->
        if stats.isDirectory()
          infoPath = path.join(cpath,'info.json')
          fs.readFile infoPath,'utf8',(err,data)->
            return cb err if err
            try
              obj = JSON.parse(data)
              obj.folder = job
              fs.readFile templateFolderPath+'/'+job+'/'+obj.entry,'utf8',(err,fileText)->
                if err
                  console.error 'Error: read entry error: '+job+'/'+obj.entry
                  console.error err
                  return doOneJob()
                imgPath = fnImg(job)
                obj.imgPath = imgPath
                fileText = fileText.replace(/src=\"\.\//ig,'src="'+imgPath)
                fileText = fileText.replace(/url\(\"\.\//ig,'url("'+imgPath)
                obj.fileText = fileText
                tempFn = dots.template(fileText)
                obj.tempFn = tempFn
                console.log 'loaded dot file template: ',templateFolderPath+'/'+job+'/'+obj.entry
                templateList.push genTemplateFromInfo(obj)
                if obj.group
                  groups.push obj.group
                doOneJob()
            catch err
              console.error 'Error: read info error: '+infoPath
              console.error err
              doOneJob()
        else
          console.log 'ignore non-folder',cpath
          doOneJob()
    doOneJob()

DEF 'loadDotTemplates',loadDotTemplates


#reload template
rebuildMapping = (templateList)->
  nameIndexMapping = {}
  for t,index in templateList
    # console.log '@@@',t.name
    nameIndexMapping[t.folder] = index
  return nameIndexMapping

reloadTemplates = (path, cb)->
  templateList = []

  cfg = {
    templateFolderPath: path,
    fnImg: (job)->
      return '/img/shareimg/'+job+'/'
  }
  loadDotTemplates cfg, (err,ret)->
    templateList = ret?.templateList
    # console.log '++++++'
    # console.log templateList.length
    # console.log ret.groups
    if ret?.groups?.length
      for g in ret.groups
        GroupModel.upsertSysGroup {name:g},(err,ret)->
          console.error err if err
    cb null,templateList

DEF 'rebuildMapping',rebuildMapping
DEF 'reloadTemplates',reloadTemplates


templateFolderPath = conf.emailTemplate?.path
nameIndexMapping = {}
templateList = []

unless templateFolderPath
  err = 'Error: emailTemplate.path is missing!'
  console.error err
  throw new Error(err)

# doReload()
reloadTemplates templateFolderPath, (err, ret)->
  if err
    console.error err
  templateList = ret
  nameIndexMapping = rebuildMapping templateList if templateList

getDotHtml = (req, folder, data)->
  idx = nameIndexMapping[folder]
  if not idx?
    return {err:'Template not found'}
  doc = templateList[idx]
  if not doc.name
    return {err:'Template name not found'}
  fileText = doc.fileText
  if not fileText
    return {err:'Template no file text'}
  tempFn = doc.tempFn
  if not tempFn
    return {err:'Template no tempFn'}
  try
    html = tempFn(data, req)
    return {html:html}
  catch error
    console.error 'template Fn error:',error
    return {err:'Failed to gen html'}

DEF 'getDotHtml',getDotHtml
