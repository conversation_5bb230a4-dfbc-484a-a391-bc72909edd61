
# accessAllowed = DEF '_access_allowed'
# appLog = DEF 'appLog'
# normalizeVer = DEF 'normalizeVer'
# verGTE = DEF 'verGTE'

cfg = CONFIG(['serverBase','share'])
helpers = INCLUDE 'lib.helpers'
# TODO: use debug module
debug = (m)-> console.log m
gShareHostNameCn = cfg.share?.HostNameCn or 'realmaster.cn'
UserModel = MODEL 'User'
# DUAL_HOMEPAGE_ENABLED = cfg.dualHomepage or false
DUAL_HOMEPAGE_ENABLED = true
libUser = INCLUDE 'libapp.user'

# '/' route
APP ''
GET (req,resp)->
  #if isbrowser goto www.rm.com use cfg
  #if req.getDevType() is 'browser'
  # always goto www.rm.com
  return resp.redirect "https://#{cfg.serverBase.wwwDomain}"
  #resp.redirect '/app-download'

POST 'alive',(req,resp)->
  other = req.param('me')
  ts = req.param('ts')
  debug "ALIVE Fm:#{other} Ts:#{ts} Me:#{Date.now()}"
  return resp.send "Who?" unless ts
  resp.send "OK" # {ts:Date.now()}

VIEW 'homepage',->
  h3 -> @a

_checkedSites = {}
_checkAlive = (host,me,cb)->
  # debug "Check Host Alive: #{host}"
  ts = Date.now()
  if site = _checkedSites[host]
    if site.ts > (ts - 10000) # within 10s
      cb() if cb
      return null
  _checkedSites[host] = {ts:ts,working:1}
  helpers.http_post {hostname:host,path:'/alive',protocol:'https',timeout:5000},{ts:ts,me:me},(err,ret)->
    if err or ret isnt 'OK'
      debug "Host Not Alive: #{host} #{err} #{ret?.substr(0,50)}"
      _checkedSites[host] = {ts:ts,err:err or "Bad Return"}
    else
      debug "Host Alive: #{host}"
      _checkedSites[host] = {ts:ts,ok:1,ret:ret}
    cb() if cb
# get jump to url. False if no need to jump
_jumpHost = (req)->
  host = false
  #TODO: 'realmaster.cn' is used in multiple places, should put it as constant
  if req.isRealChinaIP() # user in China
    host = "ch.#{gShareHostNameCn}"
    # if req.isChinaMode() # server in China
    if host is req.host # we are serving this host
      host = false
  else # user not in China
    host = 'realmaster.com'
    unless req.isChinaMode() # server not in China
      host = false # no need to jump
  if host and _checkedSites[host]?.err
    host = false # remote site is not available
  return host unless host # no need to jump
  _checkAlive host,req.hostname
  # console.log "jumpHost:" + host # debug
  host
_getJumpUrl = (req,resp,host,cb)->
  # debug "getJumpUrl:" + host
  UserModel.appAuth {req,resp},(user)-> # continue user login status here
    done = (rid)->
      url = "https://#{host}/rauth/#{rid}"
      if sv = (req.param('sv') or req.cookies?.apsv)
        url += "?sv=#{sv}"
      debug "getJumpUrl:#{url}"
      cb url
    if user
      return UserModel.genRtoken {user,host},(err,ret)->
        if err
          console.log err
          return done 'blank'
        return done ret.rtoken
    return done 'blank'
_setAppCookie = (req,resp)->
  if sv = req.param('sv')
    resp.cookie 'apsv',sv,{path:'/',maxAge:1000*3600*24*365}
  if appmode = req.param('appmode')
    resp.cookie libUser.APPMODE,appmode,{path:'/',maxAge:1000*3600*24*365}

extractIDFromUA = (ua)->
  ua ?= ''
  ret = /RMuserID\/([0-9a-zA-Z\-])+/.exec ua
  if ret
    return ret[0].split('/')[1]
  return null

POST 'matchuser',(req,resp)->
  UserModel.auth {req,resp},(user)->
    unless user
      return resp.send {ok:0, err:MSG_STRINGS.NEED_LOGIN}
    domain = _jumpHost req
    ua = req.UA() #headers?['user-agent']
    cookie = req.cookies #headers?.cookie
    ip = req.remoteIP()
    # console.log '+++++#',req.param 'id'
    done = (err,ret)->
      # console.log '++++++))))',err,ret?.result?.n
      resp.send {ok:1}
    if id = req.param 'id'
      return UserModel.matchUserDeviceId {req,id,ua,cookie,ip,user},done
    done()

POST 'loguserwechat',(req,resp)->
  UserModel.auth {req,resp},(user)->
    unless user
      return resp.send {ok:0, err:MSG_STRINGS.NEED_LOGIN}
    done = (err,ret)->
      # console.log '++++++))))',err,ret?.result?.n
      resp.send {ok:1}
    has = req.param 'hasWechat'
    if has?
      return UserModel.logUserHasWechat {req,has,user},done
    done()

#TODO: zone(isCip),apsv, server list
GET 'appdm',(req,resp)-> # get app domain. for appVer > 5.0
  UserModel.auth {req,resp},(user)->
    domain = _jumpHost req
    # domain = 'ch.realexpert.cn'
    # NOTE: user not login here, only cmate.sid
    # useWebMap:req.useWebMap()
    # TODO: get user agent , devid, send error if block
    ua = req.UA() #headers?['user-agent']
    cookie = req.cookies #headers?.cookie
    ip = req.remoteIP()
    # console.log '+++++++',ua,cookie,ip,user,extractIDFromUA ua
    # req.param?
    if id = extractIDFromUA ua
      UserModel.logUserDevUniqID {id,ua,cookie,ip,user}
    resp.send {dm:domain, useWebMap:req.isRealChinaIP()}

gAppModeHomeUrlMap = {
  'rm':'/home/<USER>'
  'mls':'/home/<USER>'
}

getHomepageUrl = (req,resp)->
  # if req.isChinaIP()
  #   return 'https://ch.realexpert.cn/1.5/index'
  proto = if cfg.serverBase?.useHTTPS then 'https' else 'http'
  # appmode = req.param('appmode')
  # if appmode
  #   resp.cookie libUser.APPMODE,appmode,{path:'/',maxAge:1000*3600*24*365}
  # (not req.verGTE('5.6.0')) not used, this stage no apsv
  if req.isAndroid() and cfg.serverBase?.androidNoHTTPS
    proto = 'http'
  if DUAL_HOMEPAGE_ENABLED
    appmode = req.cookies[libUser.APPMODE]
    unless appmode
      appmode = 'mls'
      resp.cookie libUser.APPMODE,appmode,{path:'/',maxAge:1000*3600*24*365}
    urlPath = gAppModeHomeUrlMap[appmode] or '/home/<USER>'
    return "#{proto}://#{req.host}#{urlPath}"
  else
    return '/1.5/index'
DEF 'getHomepageUrl',getHomepageUrl

# GET '/app' app route
APP 'app'
GET (req,resp)->
  sv = req.param('sv')
  appmode = req.param('appmode')
  done = (url)->
    # TODO: shall not return two different types of response.
    if (sv) #and verGTE('5.0.0')
      return resp.redirect url # we are serving Cn client. No need for 2nd check.
      # cnHost = "ch.#{gShareHostNameCn}"
      # if req.isRealChinaIP() and (not req.isChinaMode()) and (not _checkedSites[cnHost]?.err)
      #   resp.redirect "https://#{cnHost}/app?sv=#{sv}&appmode=#{appmode}"
      # else
      #   resp.redirect url
    else
      resp.send {url:url}
    # appLog req
  if host = _jumpHost req
    return _getJumpUrl req,resp,host,(url)->
      done url
  # local app
  _setAppCookie req,resp
  done getHomepageUrl req,resp

# for jump to apptest environment
GET 'apptest',(req,resp)->
  _setAppCookie req,resp
  resp.redirect getHomepageUrl req,resp

APP 'in_app_close'
GET (req,resp)->
  resp.send "<html><body>This page shall auto-close.</body></html>"

APP 'rauth'
GET ':token',(req,resp)->
  _setAppCookie req,resp
  unless token = req.param('token')
    console.error "No Token for rauth"
    return resp.redirect '/1.5/index'
  if token is 'blank' # not logged in at original location
    return resp.redirect '/1.5/index'
  debug "Try login token #{token}"
  UserModel.appRlogin {req,token},(err,user)->
    if err
      console.error "RAuth Failed : #{err}"
    return resp.redirect '/1.5/index'

# when testing jump
APP 'sitejump'
GET (req,resp)->
  UserModel.auth {req,resp,url:'/1.5/index'},(user)->
    unless UserModel.accessAllowed 'jumpSite',user
      return resp.redirect '/1.5/index'
    host = null
    switch req.param 'r'
      when 'ca'
        if cfg.serverBase.developer_mode
          host = 'd1.realmaster.com'
        else
          host = 'realmaster.com'
      when 'cn'
        if cfg.serverBase.developer_mode
          host = "shf2.#{gShareHostNameCn}"
        else
          host = "ch.#{gShareHostNameCn}"
      else
        host = _jumpHost req
    if host and _checkedSites[host]?.err
      console.log "target host is not available: #{host}"
      host = req.host # remote site is not available
    _checkAlive host,req.hostname
    _getJumpUrl req,resp,host,(url)->
      # debug "jump:#{url}"
      resp.redirect url

#TODO: upadate url
checkAliveAll = ->
  _checkAlive 'realmaster.com','B',->
    _checkAlive "ch.#{gShareHostNameCn}",'B',->

checkAliveAll()
setInterval checkAliveAll, 300000 # every 5 min
