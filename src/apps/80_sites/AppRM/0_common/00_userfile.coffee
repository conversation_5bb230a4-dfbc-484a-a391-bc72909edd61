# move to MODEL User

# appConfig = CONFIG()
# UserFile = COLLECTION 'chome','user_file'
# SysData = COLLECTION 'chome','sysdata'
# helpers = INCLUDE 'lib.helpers'
# fileHelper = INCLUDE 'libapp.fileHelper'

# FILE_WEB_BASE_DOMAIN = appConfig.user_file?.wwwbase
# unless FILE_WEB_BASE_DOMAIN
#   throw new Error "No user_file wwwbase defined"
# DEF 'FILE_WEB_BASE_DOMAIN',FILE_WEB_BASE_DOMAIN

# FILE_LOCAL_FOLDER_BASE = appConfig.user_file?.folder
# unless FILE_LOCAL_FOLDER_BASE
#   throw new Error "No user_file img folder defined"
# DEF 'FILE_LOCAL_FOLDER_BASE',FILE_LOCAL_FOLDER_BASE

# if FILE_LOCAL_FOLDER_BASE.slice(-1) is '/'
#   FILE_LOCAL_FOLDER_BASE = FILE_LOCAL_FOLDER_BASE.substr(0,FILE_LOCAL_FOLDER_BASE.length-1)
# FILE_WEB_PROTOCOL = appConfig.user_file?.protocol
# unless FILE_WEB_PROTOCOL in ['http','https']
#   throw new Error "user_file protocol incorectly defined"
# DEF 'FILE_WEB_PROTOCOL',FILE_WEB_PROTOCOL

# # fileHelper.configSetup(appConfig)
# ensureUserFolder = fileHelper.ensureUserFolder
# DEF 'ensureUserFolder',ensureUserFolder

# # callback(err, userfile, reserved-file-names)
# reserveUserFile = (user,fileNum,cb)->
#   if not cb?
#     cb = fileNum
#     fileNum = 1
#   update =
#     $set:{
#       ts:new Date(),
#       base:FILE_WEB_BASE_DOMAIN,
#       fldr:FILE_LOCAL_FOLDER_BASE
#     }
#   if fileNum > 0
#     update.$inc = cntr:fileNum
#   else
#     update.$setOnInsert = cntr:17
#   UserFile.findOneAndUpdate {_id:user._id},update,{upsert:true,returnDocument:'after'},(err,r)->
#     # user may not have userFile yet, so must get updated Object instead of original
#     if err then return cb err
#     if not (userFile = r.value) then return cb new Error "Can't create user file"
#     ensureUserFolder SysData,UserFile,userFile,(err,nUserFile)->
#       if err then return cb err
#       ret = []
#       if fileNum > 0
#         if userFile.cntr < 17
#           userFile.cntr = 17 + fileNum
#           UserFile.updateOne {_id:user._id},{$set:cntr:userFile.cntr},(err)->
#             if err then console.error err
#         index = userFile.cntr
#         index -= fileNum
#         for i in [index...(index + fileNum)]
#           ret.push helpers.number2chars i
#       cb null, nUserFile, ret

# DEF 'reserveUserFile',reserveUserFile
