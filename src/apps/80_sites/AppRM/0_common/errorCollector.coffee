ErrorLog = COLLECTION 'chome', 'error_log'
ErrorLog.createIndex {_mt:1},{ expireAfterSeconds: 60*60*24*30 } # keep log for one month only

removeDot = (obj)->
  ret = {}
  for k,v of obj
    if k.indexOf('.')
      k = k.replace /\./g,'-'
    ret[k] = v
  ret
APP 'cError'
POST (req,resp)->
  obj =
    ip: req.remoteIP()
    ts: new Date()
    appver: req.cookies['apsv']
    ua: req.headers?['user-agent']
    cookies: removeDot req.cookies
    user: removeDot req.session?.get('user')
    msg: req.param "m"
  # console.error JSON.stringify obj
  req.logger?.warn obj
  resp.send 'logged'
  ErrorLog.insertOne obj,(err)->
    if err then console.error err
