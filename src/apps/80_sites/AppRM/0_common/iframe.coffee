APP '1.5'

GET 'iframe',(req,resp)->
  url = decodeURIComponent(req.param('u'))
  if /(https|http):\/\/([a-zA-Z\d-]+\.)*realmaster\.(com|cn)\/([a-z0-9\.\-\_\/\?\=\*\&\#\$]+)/.test(url) or /^\/[^\/]+/.test(url)
    url += (if url.indexOf('?') > 0 then "&" else "?") + "inFrame=1"
  cfg = {url:url}
  if req.param 'nh'
    cfg.noHeader = true
  if req.param 'nf'
    cfg.noFooter = true
  resp.ckup 'iframe',cfg,'iframe'

LAYOUT ['iframe','_'],->
  block 'body',->
    body ->
      text @content()

VIEW 'iframe',->
  div ->
    unless @noHeader
      header class: 'bar bar-nav', ->
        a class: "icon fa fa-back pull-left",href:"#" ,onclick:"goBack(event);"
        # TODO: if come from other location, may need use goBack
        #a class: 'icon icon-close pull-right', href: '#propDetailModal',dataIgnore:'push'
        h1 class: 'title',-> _ 'Webpage'

    text ckup 'shareDialog2',{page:'iframe',noLang:true,noSign:true,doc:"RMSrv.fDoc()", req:@req}

    unless @noFooter
      div class: 'bar bar-standard bar-footer', ->
        #a class: 'icon icon-compose pull-left'
        a class: 'icon icon-share pull-right', style:"color:#E03131",onclick: "RMSrv.share('show');"

    div id: 'frame', class: 'content', style:"padding-bottom:0;", ->
      iframe id:'iframe',name:'iframe',sandbox:"allow-forms allow-same-origin allow-scripts",style:'border:0;width:100%;height:100%;overflow:hidden;',src:@url

    div id: 'backdrop', class: 'backdrop', style: 'display:none;'

    div class:'WSBridge',style:'display:none',->
      span id:'share-title',-> text _ "RealMaster APP Sharing"
      span id:'share-desc',-> text _ "Shared with RealMaster APP"
      span id:'share-url',-> @url
      span id:'share-image',-> text "http://#{@req.hostname}/img/logo.png"

