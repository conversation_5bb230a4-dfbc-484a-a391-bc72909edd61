moderation = INCLUDE 'libapp.contentModeration'
checkContent = moderation.checkContent
textModeration = moderation.textModeration
imageModeration = moderation.imageModeration
ModerationLog = COLLECTION 'chome', 'moderation_log'
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  ModerationLog.createIndex( { 'ts': 1 }, { expireAfterSeconds: 3600*24*182 } )

addLog = (ret,params,cb) ->
  insertData =
    ts: new Date()
    uid: params.user?._id
    col: params.collection
    labels: ret.labels
  insertData.id = params.id if params.id
  ModerationLog.insertOne insertData,(err)->
    cb err,ret

contentModeration = (params, cb) ->
  checkContent params,(err, ret) ->
    return cb err,ret unless ret?.block
    addLog ret,params,(err,ret)->
      cb err,ret
DEF 'contentModeration', contentModeration

###
params can be sting or array of string or object include 
###
checkTextContent = (params, cb) ->
  return cb(null,null) if (params.bypassAll or params.user?.noFltr)
  textModeration params,(err,ret)->
    return cb err,ret unless ret?.block
    addLog ret,params,(err,ret)->
      cb err,ret
DEF 'checkTextContent', checkTextContent

checkImageContent = (params, cb) ->
  imageModeration params,(err,ret)->
    return cb err,ret unless ret?.block
    cb err,ret
DEF 'checkImageContent', checkImageContent