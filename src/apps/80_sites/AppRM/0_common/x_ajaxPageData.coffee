UserModel = MODEL 'User'
# dataMethods = DEF 'dataMethods'

dataMethodCall = DEF 'dataMethodCall'

# err:''
# errCode:2
APP '1.5'
POST 'pageData', (req, resp) ->
  unless datas = req.body?.datas #['isLoggined']
    console.error('Potential Hacker:' + req.remoteIP())
    return resp.send {e:'Who are you?'}
  UserModel.appAuth {req,resp},(user)->
    dataMethodCall req, user, datas, (err, ret)->
      return resp.send ret
