libUser = INCLUDE 'libapp.user'
conf = CONFIG('share')

# TODO: use auto generated value?
DEF 'APP_VER','6.2.5' # change this number everytime release to production
DEF 'WEB_VER','2d07f'

libProperties = INCLUDE 'libapp.properties'
helpers = INCLUDE 'lib.helpers'
libAppVer = INCLUDE 'libapp.appVer'

getNewestCoreVer = (req)->
  libAppVer.getAppCoreVer(req)



phoneCntys = [
  {cd:1,iso2:'CA',iso3:'CAN',nm:'Canada'}
  {cd:1,iso2:'US',iso3:'USA',nm:'United States'}
  {cd:86,iso2:'CN',iso3:'CHN',nm:'China'}
  {cd:93,iso2:'AF',iso3:'AFG',nm:'Afghanistan'}
  {cd:355,iso2:'AL',iso3:'ALB',nm:'Albania'}
  {cd:213,iso2:'DZ',iso3:'DZA',nm:'Algeria'}
  {cd:1684,iso2:'AS',iso3:'ASM',nm:'American Samoa'}
  {cd:376,iso2:'AD',iso3:'AND',nm:'Andorra'}
  {cd:244,iso2:'AO',iso3:'AGO',nm:'Angola'}
  {cd:1264,iso2:'AI',iso3:'AIA',nm:'Anguilla'}
  {cd:672,iso2:'AQ',iso3:'ATA',nm:'Antarctica'}
  {cd:1268,iso2:'AG',iso3:'ATG',nm:'Antigua and Barbuda'}
  {cd:54,iso2:'AR',iso3:'ARG',nm:'Argentina'}
  {cd:374,iso2:'AM',iso3:'ARM',nm:'Armenia'}
  {cd:297,iso2:'AW',iso3:'ABW',nm:'Aruba'}
  {cd:61,iso2:'AU',iso3:'AUS',nm:'Australia'}
  {cd:43,iso2:'AT',iso3:'AUT',nm:'Austria'}
  {cd:994,iso2:'AZ',iso3:'AZE',nm:'Azerbaijan'}
  {cd:1242,iso2:'BS',iso3:'BHS',nm:'Bahamas'}
  {cd:973,iso2:'BH',iso3:'BHR',nm:'Bahrain'}
  {cd:880,iso2:'BD',iso3:'BGD',nm:'Bangladesh'}
  {cd:1246,iso2:'BB',iso3:'BRB',nm:'Barbados'}
  {cd:375,iso2:'BY',iso3:'BLR',nm:'Belarus'}
  {cd:32,iso2:'BE',iso3:'BEL',nm:'Belgium'}
  {cd:501,iso2:'BZ',iso3:'BLZ',nm:'Belize'}
  {cd:229,iso2:'BJ',iso3:'BEN',nm:'Benin'}
  {cd:1441,iso2:'BM',iso3:'BMU',nm:'Bermuda'}
  {cd:975,iso2:'BT',iso3:'BTN',nm:'Bhutan'}
  {cd:591,iso2:'BO',iso3:'BOL',nm:'Bolivia'}
  {cd:387,iso2:'BA',iso3:'BIH',nm:'Bosnia and Herzegovina'}
  {cd:267,iso2:'BW',iso3:'BWA',nm:'Botswana'}
  {cd:55,iso2:'BR',iso3:'BRA',nm:'Brazil'}
  {cd:1284,iso2:'VG',iso3:'VGB',nm:'British Virgin Islands'}
  {cd:673,iso2:'BN',iso3:'BRN',nm:'Brunei'}
  {cd:359,iso2:'BG',iso3:'BGR',nm:'Bulgaria'}
  {cd:226,iso2:'BF',iso3:'BFA',nm:'Burkina Faso'}
  {cd:95,iso2:'MM',iso3:'MMR',nm:'Burma (Myanmar)'}
  {cd:257,iso2:'BI',iso3:'BDI',nm:'Burundi'}
  {cd:855,iso2:'KH',iso3:'KHM',nm:'Cambodia'}
  {cd:237,iso2:'CM',iso3:'CMR',nm:'Cameroon'}
  {cd:238,iso2:'CV',iso3:'CPV',nm:'Cape Verde'}
  {cd:1345,iso2:'KY',iso3:'CYM',nm:'Cayman Islands'}
  {cd:236,iso2:'CF',iso3:'CAF',nm:'Central African Republic'}
  {cd:235,iso2:'TD',iso3:'TCD',nm:'Chad'}
  {cd:56,iso2:'CL',iso3:'CHL',nm:'Chile'}
  {cd:61,iso2:'CX',iso3:'CXR',nm:'Christmas Island'}
  {cd:61,iso2:'CC',iso3:'CCK',nm:'Cocos (Keeling) Islands'}
  {cd:57,iso2:'CO',iso3:'COL',nm:'Colombia'}
  {cd:269,iso2:'KM',iso3:'COM',nm:'Comoros'}
  {cd:682,iso2:'CK',iso3:'COK',nm:'Cook Islands'}
  {cd:506,iso2:'CR',iso3:'CRC',nm:'Costa Rica'}
  {cd:385,iso2:'HR',iso3:'HRV',nm:'Croatia'}
  {cd:53,iso2:'CU',iso3:'CUB',nm:'Cuba'}
  {cd:357,iso2:'CY',iso3:'CYP',nm:'Cyprus'}
  {cd:420,iso2:'CZ',iso3:'CZE',nm:'Czech Republic'}
  {cd:243,iso2:'CD',iso3:'COD',nm:'Democratic Republic of the Congo'}
  {cd:45,iso2:'DK',iso3:'DNK',nm:'Denmark'}
  {cd:253,iso2:'DJ',iso3:'DJI',nm:'Djibouti'}
  {cd:1767,iso2:'DM',iso3:'DMA',nm:'Dominica'}
  {cd:1809,iso2:'DO',iso3:'DOM',nm:'Dominican Republic'}
  {cd:593,iso2:'EC',iso3:'ECU',nm:'Ecuador'}
  {cd:20,iso2:'EG',iso3:'EGY',nm:'Egypt'}
  {cd:503,iso2:'SV',iso3:'SLV',nm:'El Salvador'}
  {cd:240,iso2:'GQ',iso3:'GNQ',nm:'Equatorial Guinea'}
  {cd:291,iso2:'ER',iso3:'ERI',nm:'Eritrea'}
  {cd:372,iso2:'EE',iso3:'EST',nm:'Estonia'}
  {cd:251,iso2:'ET',iso3:'ETH',nm:'Ethiopia'}
  {cd:500,iso2:'FK',iso3:'FLK',nm:'Falkland Islands'}
  {cd:298,iso2:'FO',iso3:'FRO',nm:'Faroe Islands'}
  {cd:679,iso2:'FJ',iso3:'FJI',nm:'Fiji'}
  {cd:358,iso2:'FI',iso3:'FIN',nm:'Finland'}
  {cd:33,iso2:'FR',iso3:'FRA',nm:'France'}
  {cd:689,iso2:'PF',iso3:'PYF',nm:'French Polynesia'}
  {cd:241,iso2:'GA',iso3:'GAB',nm:'Gabon'}
  {cd:220,iso2:'GM',iso3:'GMB',nm:'Gambia'}
  {cd:995,iso2:'GE',iso3:'GEO',nm:'Georgia'}
  {cd:49,iso2:'DE',iso3:'DEU',nm:'Germany'}
  {cd:233,iso2:'GH',iso3:'GHA',nm:'Ghana'}
  {cd:350,iso2:'GI',iso3:'GIB',nm:'Gibraltar'}
  {cd:30,iso2:'GR',iso3:'GRC',nm:'Greece'}
  {cd:299,iso2:'GL',iso3:'GRL',nm:'Greenland'}
  {cd:1473,iso2:'GD',iso3:'GRD',nm:'Grenada'}
  {cd:1671,iso2:'GU',iso3:'GUM',nm:'Guam'}
  {cd:502,iso2:'GT',iso3:'GTM',nm:'Guatemala'}
  {cd:245,iso2:'GW',iso3:'GNB',nm:'Guinea-Bissau'}
  {cd:224,iso2:'GN',iso3:'GIN',nm:'Guinea'}
  {cd:592,iso2:'GY',iso3:'GUY',nm:'Guyana'}
  {cd:509,iso2:'HT',iso3:'HTI',nm:'Haiti'}
  {cd:39,iso2:'VA',iso3:'VAT',nm:'Holy See (Vatican City)'}
  {cd:504,iso2:'HN',iso3:'HND',nm:'Honduras'}
  {cd:852,iso2:'HK',iso3:'HKG',nm:'Hong Kong'}
  {cd:36,iso2:'HU',iso3:'HUN',nm:'Hungary'}
  {cd:354,iso2:'IS',iso3:' IS',nm:'Iceland'}
  {cd:91,iso2:'IN',iso3:'IND',nm:'India'}
  {cd:62,iso2:'ID',iso3:'IDN',nm:'Indonesia'}
  {cd:98,iso2:'IR',iso3:'IRN',nm:'Iran'}
  {cd:964,iso2:'IQ',iso3:'IRQ',nm:'Iraq'}
  {cd:353,iso2:'IE',iso3:'IRL',nm:'Ireland'}
  {cd:44,iso2:'IM',iso3:'IMN',nm:'Isle of Man'}
  {cd:972,iso2:'IL',iso3:'ISR',nm:'Israel'}
  {cd:39,iso2:'IT',iso3:'ITA',nm:'Italy'}
  {cd:225,iso2:'CI',iso3:'CIV',nm:'Ivory Coast'}
  {cd:1876,iso2:'JM',iso3:'JAM',nm:'Jamaica'}
  {cd:81,iso2:'JP',iso3:'JPN',nm:'Japan'}
  {cd:962,iso2:'JO',iso3:'JOR',nm:'Jordan'}
  {cd:7,iso2:'KZ',iso3:'KAZ',nm:'Kazakhstan'}
  {cd:254,iso2:'KE',iso3:'KEN',nm:'Kenya'}
  {cd:686,iso2:'KI',iso3:'KIR',nm:'Kiribati'}
  {cd:965,iso2:'KW',iso3:'KWT',nm:'Kuwait'}
  {cd:996,iso2:'KG',iso3:'KGZ',nm:'Kyrgyzstan'}
  {cd:856,iso2:'LA',iso3:'LAO',nm:'Laos'}
  {cd:371,iso2:'LV',iso3:'LVA',nm:'Latvia'}
  {cd:961,iso2:'LB',iso3:'LBN',nm:'Lebanon'}
  {cd:266,iso2:'LS',iso3:'LSO',nm:'Lesotho'}
  {cd:231,iso2:'LR',iso3:'LBR',nm:'Liberia'}
  {cd:218,iso2:'LY',iso3:'LBY',nm:'Libya'}
  {cd:423,iso2:'LI',iso3:'LIE',nm:'Liechtenstein'}
  {cd:370,iso2:'LT',iso3:'LTU',nm:'Lithuania'}
  {cd:352,iso2:'LU',iso3:'LUX',nm:'Luxembourg'}
  {cd:853,iso2:'MO',iso3:'MAC',nm:'Macau'}
  {cd:389,iso2:'MK',iso3:'MKD',nm:'Macedonia'}
  {cd:261,iso2:'MG',iso3:'MDG',nm:'Madagascar'}
  {cd:265,iso2:'MW',iso3:'MWI',nm:'Malawi'}
  {cd:60,iso2:'MY',iso3:'MYS',nm:'Malaysia'}
  {cd:960,iso2:'MV',iso3:'MDV',nm:'Maldives'}
  {cd:223,iso2:'ML',iso3:'MLI',nm:'Mali'}
  {cd:356,iso2:'MT',iso3:'MLT',nm:'Malta'}
  {cd:692,iso2:'MH',iso3:'MHL',nm:'Marshall Islands'}
  {cd:222,iso2:'MR',iso3:'MRT',nm:'Mauritania'}
  {cd:230,iso2:'MU',iso3:'MUS',nm:'Mauritius'}
  {cd:262,iso2:'YT',iso3:'MYT',nm:'Mayotte'}
  {cd:52,iso2:'MX',iso3:'MEX',nm:'Mexico'}
  {cd:691,iso2:'FM',iso3:'FSM',nm:'Micronesia'}
  {cd:373,iso2:'MD',iso3:'MDA',nm:'Moldova'}
  {cd:377,iso2:'MC',iso3:'MCO',nm:'Monaco'}
  {cd:976,iso2:'MN',iso3:'MNG',nm:'Mongolia'}
  {cd:382,iso2:'ME',iso3:'MNE',nm:'Montenegro'}
  {cd:1664,iso2:'MS',iso3:'MSR',nm:'Montserrat'}
  {cd:212,iso2:'MA',iso3:'MAR',nm:'Morocco'}
  {cd:258,iso2:'MZ',iso3:'MOZ',nm:'Mozambique'}
  {cd:264,iso2:'NA',iso3:'NAM',nm:'Namibia'}
  {cd:674,iso2:'NR',iso3:'NRU',nm:'Nauru'}
  {cd:977,iso2:'NP',iso3:'NPL',nm:'Nepal'}
  {cd:599,iso2:'AN',iso3:'ANT',nm:'Netherlands Antilles'}
  {cd:31,iso2:'NL',iso3:'NLD',nm:'Netherlands'}
  {cd:687,iso2:'NC',iso3:'NCL',nm:'New Caledonia'}
  {cd:64,iso2:'NZ',iso3:'NZL',nm:'New Zealand'}
  {cd:505,iso2:'NI',iso3:'NIC',nm:'Nicaragua'}
  {cd:227,iso2:'NE',iso3:'NER',nm:'Niger'}
  {cd:234,iso2:'NG',iso3:'NGA',nm:'Nigeria'}
  {cd:683,iso2:'NU',iso3:'NIU',nm:'Niue'}
  {cd:672,iso2:'',iso3:'NFK',nm:'Norfolk Island'}
  {cd:850,iso2:'KP',iso3:'PRK',nm:'North Korea'}
  {cd:1670,iso2:'MP',iso3:'MNP',nm:'Northern Mariana Islands'}
  {cd:47,iso2:'NO',iso3:'NOR',nm:'Norway'}
  {cd:968,iso2:'OM',iso3:'OMN',nm:'Oman'}
  {cd:92,iso2:'PK',iso3:'PAK',nm:'Pakistan'}
  {cd:680,iso2:'PW',iso3:'PLW',nm:'Palau'}
  {cd:507,iso2:'PA',iso3:'PAN',nm:'Panama'}
  {cd:675,iso2:'PG',iso3:'PNG',nm:'Papua New Guinea'}
  {cd:595,iso2:'PY',iso3:'PRY',nm:'Paraguay'}
  {cd:51,iso2:'PE',iso3:'PER',nm:'Peru'}
  {cd:63,iso2:'PH',iso3:'PHL',nm:'Philippines'}
  {cd:870,iso2:'PN',iso3:'PCN',nm:'Pitcairn Islands'}
  {cd:48,iso2:'PL',iso3:'POL',nm:'Poland'}
  {cd:351,iso2:'PT',iso3:'PRT',nm:'Portugal'}
  {cd:1,iso2:'PR',iso3:'PRI',nm:'Puerto Rico'}
  {cd:974,iso2:'QA',iso3:'QAT',nm:'Qatar'}
  {cd:242,iso2:'CG',iso3:'COG',nm:'Republic of the Congo'}
  {cd:40,iso2:'RO',iso3:'ROU',nm:'Romania'}
  {cd:7,iso2:'RU',iso3:'RUS',nm:'Russia'}
  {cd:250,iso2:'RW',iso3:'RWA',nm:'Rwanda'}
  {cd:590,iso2:'BL',iso3:'BLM',nm:'Saint Barthelemy'}
  {cd:290,iso2:'SH',iso3:'SHN',nm:'Saint Helena'}
  {cd:1869,iso2:'KN',iso3:'KNA',nm:'Saint Kitts and Nevis'}
  {cd:1758,iso2:'LC',iso3:'LCA',nm:'Saint Lucia'}
  {cd:1599,iso2:'MF',iso3:'MAF',nm:'Saint Martin'}
  {cd:508,iso2:'PM',iso3:'SPM',nm:'Saint Pierre and Miquelon'}
  {cd:1784,iso2:'VC',iso3:'VCT',nm:'Saint Vincent and the Grenadines'}
  {cd:685,iso2:'WS',iso3:'WSM',nm:'Samoa'}
  {cd:378,iso2:'SM',iso3:'SMR',nm:'San Marino'}
  {cd:239,iso2:'ST',iso3:'STP',nm:'Sao Tome and Principe'}
  {cd:966,iso2:'SA',iso3:'SAU',nm:'Saudi Arabia'}
  {cd:221,iso2:'SN',iso3:'SEN',nm:'Senegal'}
  {cd:381,iso2:'RS',iso3:'SRB',nm:'Serbia'}
  {cd:248,iso2:'SC',iso3:'SYC',nm:'Seychelles'}
  {cd:232,iso2:'SL',iso3:'SLE',nm:'Sierra Leone'}
  {cd:65,iso2:'SG',iso3:'SGP',nm:'Singapore'}
  {cd:421,iso2:'SK',iso3:'SVK',nm:'Slovakia'}
  {cd:386,iso2:'SI',iso3:'SVN',nm:'Slovenia'}
  {cd:677,iso2:'SB',iso3:'SLB',nm:'Solomon Islands'}
  {cd:252,iso2:'SO',iso3:'SOM',nm:'Somalia'}
  {cd:27,iso2:'ZA',iso3:'ZAF',nm:'South Africa'}
  {cd:82,iso2:'KR',iso3:'KOR',nm:'South Korea'}
  {cd:34,iso2:'ES',iso3:'ESP',nm:'Spain'}
  {cd:94,iso2:'LK',iso3:'LKA',nm:'Sri Lanka'}
  {cd:249,iso2:'SD',iso3:'SDN',nm:'Sudan'}
  {cd:597,iso2:'SR',iso3:'SUR',nm:'Suriname'}
  {cd:268,iso2:'SZ',iso3:'SWZ',nm:'Swaziland'}
  {cd:46,iso2:'SE',iso3:'SWE',nm:'Sweden'}
  {cd:41,iso2:'CH',iso3:'CHE',nm:'Switzerland'}
  {cd:963,iso2:'SY',iso3:'SYR',nm:'Syria'}
  {cd:886,iso2:'TW',iso3:'TWN',nm:'Taiwan'}
  {cd:992,iso2:'TJ',iso3:'TJK',nm:'Tajikistan'}
  {cd:255,iso2:'TZ',iso3:'TZA',nm:'Tanzania'}
  {cd:66,iso2:'TH',iso3:'THA',nm:'Thailand'}
  {cd:670,iso2:'TL',iso3:'TLS',nm:'Timor-Leste'}
  {cd:228,iso2:'TG',iso3:'TGO',nm:'Togo'}
  {cd:690,iso2:'TK',iso3:'TKL',nm:'Tokelau'}
  {cd:676,iso2:'TO',iso3:'TON',nm:'Tonga'}
  {cd:1868,iso2:'TT',iso3:'TTO',nm:'Trinidad and Tobago'}
  {cd:216,iso2:'TN',iso3:'TUN',nm:'Tunisia'}
  {cd:90,iso2:'TR',iso3:'TUR',nm:'Turkey'}
  {cd:993,iso2:'TM',iso3:'TKM',nm:'Turkmenistan'}
  {cd:1649,iso2:'TC',iso3:'TCA',nm:'Turks and Caicos Islands'}
  {cd:688,iso2:'TV',iso3:'TUV',nm:'Tuvalu'}
  {cd:256,iso2:'UG',iso3:'UGA',nm:'Uganda'}
  {cd:380,iso2:'UA',iso3:'UKR',nm:'Ukraine'}
  {cd:971,iso2:'AE',iso3:'ARE',nm:'United Arab Emirates'}
  {cd:44,iso2:'GB',iso3:'GBR',nm:'United Kingdom'}
  {cd:598,iso2:'UY',iso3:'URY',nm:'Uruguay'}
  {cd:1340,iso2:'VI',iso3:'VIR',nm:'US Virgin Islands'}
  {cd:998,iso2:'UZ',iso3:'UZB',nm:'Uzbekistan'}
  {cd:678,iso2:'VU',iso3:'VUT',nm:'Vanuatu'}
  {cd:58,iso2:'VE',iso3:'VEN',nm:'Venezuela'}
  {cd:84,iso2:'VN',iso3:'VNM',nm:'Vietnam'}
  {cd:681,iso2:'WF',iso3:'WLF',nm:'Wallis and Futuna'}
  {cd:967,iso2:'YE',iso3:'YEM',nm:'Yemen'}
  {cd:260,iso2:'ZM',iso3:'ZMB',nm:'Zambia'}
  {cd:263,iso2:'ZW',iso3:'ZWE',nm:'Zimbabwe'}
]



# MINIAPP_POP_CITIES =
# [
#   {
#     n: '多伦多'
#     o: 'Toronto'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Toronto.jpg'
#   }
#   {
#     n: '万锦'
#     o: 'Markham'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Markham.jpg'
#   }
#   {
#     n: '列治文山'
#     o: 'Richmond Hill'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Richmond_Hill.jpg'
#   }
#   {
#     n: '密西沙加'
#     o: 'Mississauga'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Mississauga.jpg'
#   }
#   {
#     n: '奥克维尔'
#     o: 'Oakville'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Oakville.jpg'
#   }
#   {
#     n: '布兰普顿'
#     o: 'Brampton'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Brampton.jpg'
#   }
#   {
#     n: '旺市'
#     o: 'Vaughan'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Vaughan.jpg'
#   }
#   {
#     n: '奥洛拉'
#     o: 'Aurora'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Aurora.jpg'
#   }
#   {
#     n: '新市'
#     o: 'Newmarket'
#     p: 'Ontario'
#     p_ab: 'ON'
#     pn: '安大略省'
#     img: '/img/miniapp/Newmarket.jpg'
#   }
# ]
# defined in 00_msgString, removed here

DEF 'TYPE_STRINGS', { # for internal code
  VERIFY_EMAIL: 'verifyEmail'
  RESET_PASSWD: 'resetPasswd'
}
# verify_roles_list = ['realtor','mortgage', 'inspector', 'lawyer', 'insurance_agent', 'contractor', 'merchant']
verify_roles_list = ['realtor', 'merchant']


domFilterValsArr = [
  # 1,2,3,7,14,30,60
  {k:'',v:'No restrictions'}
  {k:'0',v:'Today'}
  {k:'1',v:'Less than 1 day'},
  {k:'2',v:'Less than 2 days'},
  {k:'3',v:'Less than 3 days'},
  {k:'7',v:'Less than 1 week'},
  {k:'14',v:'Less than 2 weeks'},
  {k:'30',v:'Less than a month'},
  {k:'60',v:'Less than 2 months'},
  {k:'-1',v:'Off Market 1 day', vv:'1 day'},
  {k:'-2',v:'Off Market 2 days', vv:'2 days'},
  {k:'-3',v:'Off Market 3 days', vv:'3 days'},
  {k:'-7',v:'Off Market 1 week', vv:'1 week'},
  {k:'-14',v:'Off Market 2 weeks', vv:'2 weeks'},
  {k:'-30',v:'Off Market 1 month', vv:'1 month'},
  {k:'-60',v:'Off Market 2 months', vv:'2 months'},
  {k:'-90',v:'Off Market 3 months', vv:'3 months'},
  {k:'-180',v:'Off Market 6 months', vv:'6 months'},
  {k:'-360',v:'Off Market 1 year', vv:'1 year'}
]
# sv: short value
domFilterValsShortArr = [
  {k:'',vv:'Any',sv:'Any'},
  {k:'0',vv:'Today',sv:'0d'},
  {k:'-1',v:'Off Market 1 day', vv:'Last 1 day', sv:'1d'},
  {k:'-2',v:'Off Market 2 days', vv:'Last 2 days', sv:'2d'},
  {k:'-7',v:'Off Market 1 week', vv:'Last 1 week', sv:'1w'},
  {k:'-14',v:'Off Market 2 weeks', vv:'Last 2 weeks', sv:'2w'},
  {k:'-30',v:'Off Market 1 month', vv:'Last 1 month', sv:'1m'},
  {k:'-60',v:'Off Market 2 months', vv:'Last 2 months', sv:'2m'},
  {k:'-90',v:'Off Market 3 months', vv:'Last 3 months', sv:'3m'},
  {k:'-180',v:'Off Market 6 months', vv:'Last 6 months', sv:'6m'},
  {k:'-360',v:'Off Market 1 year', vv:'Last 1 year',sv:'1y'}
]

# TODO: initialize i18n, even not run there yet
#defaultCityToronto = {city:'Toronto', prov:'Ontario', lat:43.705718, lng:-79.398810}
defaultCityToronto = {city:'Toronto', prov:'ON', lat:43.705718, lng:-79.398810}

LIST_PROP_FIELDS = libProperties.LIST_PROP_FIELDS
MAP_PROP_FIELDS = libProperties.MAP_PROP_FIELDS


HOME_DRAWS1 = [
  {
    k: 'resale'
    t: 'Resale'
    url: '/1.5/mapSearch?mode=list&d=/1.5/index'
    geo: true
    ctx: 'for sale used props'
  }
  {
    k: 'rent'
    t: 'For Rent'
    url: '/1.5/mapSearch?d=/1.5/index&mode=list&saletp=lease'
    geo: true
  }
  {
    k: 'projects'
    t: 'Projects'
    ctx: 'prop'
    url: '/1.5/mapSearch?d=/1.5/index&mode=list&mapmode=projects'
    projQuery: 1
  }
  {
    k: 'assignment'
    t: 'Assignment'
    ctx: 'Listing'
    url: '/1.5/mapSearch?d=/1.5/index&mode=list&mapmode=assignment'
  }
  {
    k: 'sold'
    t: 'Sold'
    ctx: 'indexDrawer'
    url: "/1.5/mapSearch?d=/1.5/index&mode=map&mapmode=sold"
    loc: true
  }
  # {
  #   k: 'agent'
  #   t: 'Agent'
  #   ctx: 'indexDrawer'
  #   url: '/1.5/user/follow?d=/1.5/index'
  #   subGeo:true
  # }
  {
    k: 'yellowpage'
    t: 'Yellow Page'
    ctx: 'indexDrawer'
    url: '/1.5/yellowpage?d=/1.5/index'
  }
  {
    k: 'landlord'
    t: 'Landlord'
    ctx: 'indexDrawer'
    url: '/1.5/landlord/owners?d=/1.5/index'
  }
  {
    k: 'estimate'
    t: 'Estimate'
    ctx: 'indexDrawer'
    url: '/1.5/evaluation?d=/1.5/index'
  }
  {
    k: 'school'
    t: 'School'
    ctx: 'indexDrawer'
    geo: true
    latLng: true
    url: '/1.5/school/schoolList?d=/1.5/index'
  }
  {
    k: 'stigmatized'
    t: 'Stigmatized'
    ctx: 'indexDrawer'
    url: '/1.5/map/webMap?d=/1.5/index&tab=stigma&nohouse=1'
    geo: true
    latLng: true
    needLogin:true
  }
  {
    k: 'trend'
    t: 'Trend'
    ctx: 'indexDrawer'
    geo: true
    latLng: true
    url: '/1.5/prop/stats?d=/1.5/index'
  }
  {
    k: 'sell'
    t: 'Sell'
    ctx: 'indexDrawer'
    url: '/1.5/landlord/sellhome?d=/1.5/index'
  }
  {
    k: 'renovation'
    t: 'Renovation'
    ctx: 'indexDrawer'
    browser:true
    url: 'https://www.realrenovation.ca?d=/1.5/index'
  }
  {
    k: 'more'
    t: 'More'
    ctx: 'indexDrawer'
    url: '/1.5/more?d=/1.5/index'
  }
]
HOME_DRAWS_MLS = [
  {
    k: 'resale'
    t: 'Resale'
    geo: false
    geocn: true
    ctx: 'for sale used props'
    url: '/1.5/mapSearch?mode=map&d=/home/<USER>' #'/1.5/mapSearch?mode=list&d=/1.5/index'
    urlcn: '/1.5/mapSearch?mode=list&d=/home/<USER>'
  }
  {
    k: 'rent'
    t: 'Rent'
    url: '/1.5/mapSearch?d=/home/<USER>' #&mode=list
    urlcn: '/1.5/mapSearch?d=/home/<USER>'
    geocn: true
    geo: false
  }
  {
    k: 'sold'
    t: 'Sold'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
    urlcn: '/1.5/mapSearch?d=/home/<USER>'
    geocn: true
    geo: false
  }
  {
    k: 'commercial'
    t: 'Commercial'
    geocn: true
    geo: false
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
    urlcn: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'projects'
    t: 'PreCon'
    ctx: 'prop'
    url: '/1.5/mapSearch?d=/home/<USER>'
    projQuery: 1
  }
  {
    k: 'commlease'
    t: 'Comm Lease'
    # geo: true
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'estimate'
    t: 'Estimate'
    ctx: 'indexDrawer'
    url: '/1.5/evaluation?d=/home/<USER>'
  }
  {
    k: 'trend'
    t: 'Trend'
    ctx: 'indexDrawer'
    geo: true
    latLng: true
    url: '/1.5/prop/stats?d=/home/<USER>'
  }
  {
    k: 'stigmatized'
    t: 'Stigmatized'
    ctx: 'indexDrawer'
    url: '/1.5/map/webMap?d=/home/<USER>'
    geo: true
    latLng: true
    needLogin:true
  }
  {
    k: 'school'
    t: 'School'
    ctx: 'indexDrawer'
    geo: true
    latLng: true
    url: '/1.5/school/schoolList?d=/home/<USER>'
  }
  {
    k: 'transit'
    t: 'Transit'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  # {
  #   k: 'co-ophouse'
  #   t: 'Co-op Housing'
  #   ctx: 'indexDrawer'
  #   url: '/1.5/mapSearch?d=/1.5/index&mode=map&coop=true&nohouse=1'
  # }
  {
    k: 'sell'
    t: 'Sell'
    ctx: 'indexDrawer'
    url: '/1.5/landlord/sellhome?d=/home/<USER>'
  }
  {
    k: 'openhouse'
    t: 'Open House'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
    urlcn: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'more'
    t: 'More'
    ctx: 'indexDrawer'
    url: '/1.5/more?d=/home/<USER>'
  }

]

HOME_DRAWS_MARKET = [
  {
    k: 'projects'
    t: 'PreCon'
    ctx: 'prop'
    url: '/1.5/mapSearch?d=/home/<USER>'
    projQuery: 1
  }
  {
    k: 'assignment'
    t: 'Assignment'
    ctx: 'Listing'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'exlisting'
    t: 'Exclusive'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'll-rent'
    t: 'Landlord Rental'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  {
    k: 'ex-rent'
    t: 'Exclusive Rental'
    ctx: 'indexDrawer'
    url: '/1.5/mapSearch?d=/home/<USER>'
  }
  # {
  #   k: 'agent'
  #   t: 'Agent'
  #   ctx: 'indexDrawer'
  #   url: '/1.5/user/follow?d=/home/<USER>'
  #   subGeo:true
  # }
  {
    k: 'yellowpage'
    t: 'Yellow Page'
    ctx: 'indexDrawer'
    url: '/1.5/yellowpage?d=/1.5/index'
  }
  {
    k: 'landlord'
    t: 'Landlord'
    ctx: 'indexDrawer'
    url: '/1.5/landlord/owners?d=/1.5/index'
  }
  {
    k: 'post'
    t: 'Post'
    ctx: 'indexDrawer'
    url: '/1.5/promote/mylisting?d=/1.5/index'
  }
  {
    k: 'renovation'
    t: 'Renovation'
    ctx: 'indexDrawer'
    browser:true
    url: 'https://www.realrenovation.ca?d=/home/<USER>'
  }
]

HOME_SERVICES = [
  [
    {tl:'Evaluation',stl:'Get your home value',url:'/1.5/staging',tp:'eval',action:'openEvaluation',img:'/img/landlord_est.png'},
    {tl:'Post Rental',stl:'Be your own landlord',url:'/1.5/yellowpage',tp:'rent',action:'openMyListing',img:'/img/landlord_post1.png'},
  ],
  [
    {tl:'Renovation',stl:'Worry-free Service',url:'/1.5/promote/mylisting',tp:'reno',action:'openRenovation',img:'/img/landlord_reno1.png'},
    {tl:'Yellow Pages',stl:'Best providers for you',url:'/1.5/evaluation',tp:'yellowpage',action:'openServices',img:'/img/landlord_yp.png'},
  ]
]
getHomeServices = ()->
  return helpers.deepCopyObject {
    draws1:HOME_DRAWS1,
    drawsMls:HOME_DRAWS_MLS,
    drawsMarket:HOME_DRAWS_MARKET,
    services:HOME_SERVICES
  }

# https://gist.github.com/xantiagoma/39145a3042eca53a57ac3290a1a34973?permalink_comment_id=3415377
# maptile url google: 'https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}'
# open street map: http://c.tile.openstreetmap.org/{z}/{x}/{y}.png
# conf.shareHost
# NOTE: mptl.realmaster.com在aliyun外部就是访问不到，没有购买独立外网ip。程序就改成都用.cn
MAP_TILE_URL_CN = "https://mptl.#{conf.share.hostNameCn or 'realmaster.cn'}/vt/lyrs=m&x={x}&y={y}&z={z}"
MAP_TILE_URL = 'https://mptl.realmaster.com/vt/lyrs=m&x={x}&y={y}&z={z}'
GET_MAP_TILE_URL = (isCip=false)->
  # if isCip then MAP_TILE_URL_CN else MAP_TILE_URL
  MAP_TILE_URL_CN

backgroundTs = 7200 #10mins=600, -1 = never, 1800 default
dataMethods = DEF 'dataMethods'
dataMethods.backgroundTs = (req, user)->
  return backgroundTs

DYNAMIC_ISLAND = 48
IPHONE_X = 32
IPHONE_14 = 44 #NOTE: ios may not block content visually, but actually does block press event
DEFAULT_BAR_HEIGHT = 20
FOOT_HEIGHT = 15
# https://gist.github.com/adamawolf/3048717
iosModelsAndNotch = {
  'iPhone7,1' : {nm:'iPhone 6 Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone7,2' : {nm:'iPhone 6',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone8,1' : {nm:'iPhone 6s',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone8,2' : {nm:'iPhone 6s Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone8,4' : {nm:'iPhone SE (GSM)',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone9,1' : {nm:'iPhone 7',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone9,2' : {nm:'iPhone 7 Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone9,3' : {nm:'iPhone 7',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone9,4' : {nm:'iPhone 7 Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone10,1' : {nm:'iPhone 8',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone10,2' : {nm:'iPhone 8 Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone10,3' : {nm:'iPhone X Global',notch:IPHONE_X},
  'iPhone10,4' : {nm:'iPhone 8',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone10,5' : {nm:'iPhone 8 Plus',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone10,6' : {nm:'iPhone X GSM',notch:IPHONE_X},
  'iPhone11,2' : {nm:'iPhone XS',notch:IPHONE_14},
  'iPhone11,4' : {nm:'iPhone XS Max',notch:IPHONE_14},
  'iPhone11,6' : {nm:'iPhone XS Max Global',notch:IPHONE_14},
  'iPhone11,8' : {nm:'iPhone XR',notch:IPHONE_14},
  'iPhone12,1' : {nm:'iPhone 11',notch:IPHONE_X,foot:FOOT_HEIGHT},
  'iPhone12,3' : {nm:'iPhone 11 Pro',notch:IPHONE_14},
  'iPhone12,5' : {nm:'iPhone 11 Pro Max',notch:IPHONE_14},
  'iPhone12,8' : {nm:'iPhone SE 2nd Gen',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone13,1' : {nm:'iPhone 12 Mini',notch:IPHONE_X},
  'iPhone13,2' : {nm:'iPhone 12',notch:IPHONE_14},
  'iPhone13,3' : {nm:'iPhone 12 Pro',notch:IPHONE_14},
  'iPhone13,4' : {nm:'iPhone 12 Pro Max',notch:IPHONE_14},
  'iPhone14,2' : {nm:'iPhone 13 Pro',notch:IPHONE_14},
  'iPhone14,3' : {nm:'iPhone 13 Pro Max',notch:IPHONE_14},
  'iPhone14,4' : {nm:'iPhone 13 Mini',notch:IPHONE_X},
  'iPhone14,5' : {nm:'iPhone 13',notch:IPHONE_14},
  'iPhone14,6' : {nm:'iPhone SE 3rd Gen',notch:DEFAULT_BAR_HEIGHT,foot:0},
  'iPhone14,7' : {nm:'iPhone 14',notch:IPHONE_14,foot:FOOT_HEIGHT},
  'iPhone14,8' : {nm:'iPhone 14 Plus',notch:IPHONE_14,foot:FOOT_HEIGHT},
  'iPhone15,2' : {nm:'iPhone 14 Pro',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
  'iPhone15,3' : {nm:'iPhone 14 Pro Max',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
  'iPhone15,4' : {nm:'iPhone 15',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
  'iPhone15,5' : {nm:'iPhone 15 Plus',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
  'iPhone16,1' : {nm:'iPhone 15 Pro',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
  'iPhone16,2' : {nm:'iPhone 15 Pro Max',notch:DYNAMIC_ISLAND,foot:FOOT_HEIGHT},
}
dataMethods.iosModelsAndNotch = (req, user)->
  return iosModelsAndNotch

# native是否使用默认地图
# TODO: user can set it and store it for ios only
dataMethods.useDefaultMap = (req, user)->
  return false
# native是否显示楼盘
dataMethods.showsBuildings = (req, user)->
  return true
# native是否显示交通路况
dataMethods.showsTraffic = (req, user)->
  return false

LANGUAGE_LIST = ['zh','zh-cn','en','kr']
LANGUAGE_OBJ = [
  {
    k:'en',
    v:'English'
  },
  {
    k:'zh-cn',
    v:'简体中文'
  },
  {
    k:'zh',
    v:'繁体中文'
  },
  {
    k:'kr',
    v:'한국어'
  },
]

LANGUAGE_ABBR_OBJ=[
  {k:'en',v:'En'},
  {k:'zh', v:'繁'},
  {k:'zh-cn',v:'简'},
  {k:'kr',v:'한'}]

dataMethods.languageList = ()->
  return LANGUAGE_LIST

dataMethods.languageObj = ()->
  return LANGUAGE_OBJ

dataMethods.languageAbbrObj = ()->
  return LANGUAGE_ABBR_OBJ

# streetSuffix = "dr,rd,cres,crt,court,crescent,road,st,streat,way,ave,avenue,BOULEVARD,blvd,COVE,creek,DRIVE,GARDEN,HILL,LANE,PASS,PARK,PLACE,ROW,RUN,STATION,TRAIL,VILLE".toLowerCase().split(',')
# DEF 'streetSuffix',streetSuffix

# DEF 'backgroundTs',backgroundTs
DEF 'GET_MAP_TILE_URL',GET_MAP_TILE_URL
DEF 'MAP_PROP_FIELDS', MAP_PROP_FIELDS
DEF 'LIST_PROP_FIELDS', LIST_PROP_FIELDS
DEF 'defaultCityToronto',defaultCityToronto
DEF 'domFilterValsShortArr',domFilterValsShortArr
DEF 'domFilterValsArr',domFilterValsArr
DEF 'verify_roles_list',verify_roles_list
# DEF 'MINIAPP_POP_CITIES',MINIAPP_POP_CITIES
# DEF 'propMReplaceList',propMReplaceList
DEF 'phoneCntys',phoneCntys
DEF 'getNewestCoreVer',getNewestCoreVer
# TODO: inconsistency
DEF 'allowed_category',libUser.getYellowpageMainCate
DEF 'allowed_sub',libUser.getYellowpageSub
# DEF 'getYellowpageCategory', libUser.getYellowpageCategory #TODO: should remove this
DEF 'getHomeServices', getHomeServices
DEF 'iosAppDownloadLink', 'https://apps.apple.com/us/app/realmaster/id978214719?ls=1'
DEF 'LANGUAGE_LIST',LANGUAGE_LIST
DEF 'LANGUAGE_OBJ',LANGUAGE_OBJ
DEF 'LANGUAGE_ABBR_OBJ',LANGUAGE_ABBR_OBJ
