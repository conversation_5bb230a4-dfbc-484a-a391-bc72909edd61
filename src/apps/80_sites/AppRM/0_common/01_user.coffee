# crypto = INCLUDE 'crypto'
# checksum = INCLUDE 'lib.checksum'
# helpers = INCLUDE 'lib.helpers'
# fileHelper = INCLUDE 'libapp.fileHelper'
libUser = INCLUDE 'libapp.user'
# libOauth = INCLUDE 'libapp.oauth'
debug = DEBUG 'common'
# debugHelper = INCLUDE 'lib.debug'
# debug = debugHelper.getDebugger()
config = CONFIG('contact')

User = COLLECTION 'chome', 'user'
# UserFile = COLLECTION 'chome', 'user_file'
# ErrColl    = COLLECTION 'chome', 'pn_error'
# WechatUser = COLLECTION 'chome','wechat_user'
# verbose = 3
# debug = (lvl,msg)-> console.log msg if lvl <= verbose

# auth_by_id = DEF '_auth_login_only_by_id'
# auth_has_login = DEF '_auth_has_login_id'
# app_after_login = DEF 'app_after_login'
# baseSendSMS = SERVICE 'sendSMS'
# checkAllowed = DEF '_access_allowed'
# SHARE_SECRET = appConfig.share?.secret
# # getUserBaseUrlWithPath = fileHelper.getUserBaseUrlWithPath
# postThirdPartyImg = fileHelper.postThirdPartyImg

ADDPLUGIN 'canShowVOWDetail',(user)->
  #(user? and @isChinaIP()) or (user?.roles?.indexOf('realtor') >= 0) or (user?.flwng?.length > 0)
  return true if user
  false
# ADDPLUGIN 'userCpny',(user)->
  # TODO: shall not use @session, it seems all use cases check other users, not current user.
  #             make error explicit, so we can catch it.
  # TODO: by using User model, there is User.userCpny, shall follow DRY. We may not need this PLUGIN any more.
  # return null unless user or (user = @session?.get 'user')
  # if @locale()?.toString().substr(0,2).toLowerCase() is 'zh'
  #   return user.cpny_zh or user.cpny_en or user.cpny
  # return user.cpny_en or user.cpny_zh or user.cpny

# TODO: may use User model DRY. and @session as above.
# ADDPLUGIN 'fullNameOrNickname',(user)->
#   return null unless user or (user = @session?.get 'user')
#   isZh = @locale()?.toString().substr(0,2).toLowerCase() is 'zh'
#   if isZh
#     # if user.nm_zh
#     return user.nm_zh or user.nm_en or user.nm or ''
#   return user.nm_en or user.nm_zh or user.nm or ''
  # if user.fn and user.ln
  #   if isZh
  #     ret = user.ln + user.fn
  #   else
  #     ret = user.fn + ' ' + user.ln
  #   # if user.nm
  #   #   ret += "(#{user.nm})"
  #   return ret
  # else if user.nm
  #   return user.nm
  # else
  #   return (user.fn or '') or (user.ln or "")
ADDPLUGIN 'getShareUID',(user)->
  #not makes sence when sharing rm listing
  # (user.roles?.indexOf('vip_alliance') >= 0)
  return null unless user or (user = @session?.get 'user')
  if (user.roles?.indexOf('realtor') >= 0) or user.realtor#getUserPublicInfo will not return roles, instead .realtor = 1, .vip =1
    return user._id or user.id
  if user.flwng?.length > 0
    return user.flwng[0].uid
  null
ADDPLUGIN 'isfasAdmin', (user, city) ->
  return false

infoUserId = null
ADDPLUGIN 'getProjShareUID',(user, cb)->
  return cb(null,infoUserId) if infoUserId
  User.findOne {eml:config.contact?.defaultRTEmail}, (err,ret)->
    if err
      debug.error err if err
      return cb err
    if not ret
      return cb null, ''
    ret ?= {}
    infoUserId = ret._id
    return cb null, ret._id

# userIDQuery = (id)->
#   if ('string' is typeof id) and /^[a-f0-9]{24}$/.test id
#     return {$or:[{_id:new User.ObjectId(id)},{id:id}]}
#   return {id:id}
# DEF 'userIDQuery',userIDQuery

# getUserEmail = (user)->
#   return null unless user?.eml
#   ret = if Array.isArray(user.eml) then user.eml[0] else user.eml
#   ret.toString().toLowerCase()
# DEF 'getUserEmail',getUserEmail

# # TODO if user updates id check mdfed <= 0, if not mdfd, mdfd = 2,  not allow to change if <= 0
# # updateOne wecard table where id = user.id -> newId,
# # updateUserSetID = (req,id,cb)->
# #
# updateUserSetRoles = (req,user,roles,cb)->
#   findError = (err)-> cb err
#   if not cb
#     cb = roles
#     roles = user
#     user = req.session.get('user')
#   return findError new Error "No User" unless user
#   user.roles ?= []
#   cb null,user unless roles?
#   # not work if roles is string
#   if (Array.isArray roles) or (not 'object' is typeof roles)
#     roles = {add:roles}
#   changed = false
#   update = $set:{}
#   updateVipmt = (r)-> update.$set.vip_mt = new Date() if /vip/i.test r

#   if roles.add
#     # console.log roles.add
#     if Array.isArray(roles.add)
#       for r in roles.add
#         if user.roles.indexOf(r.toString()) < 0
#           user.roles.push r.toString()
#           changed = true
#           updateVipmt r
#     else if 'string' is typeof roles.add
#       if user.roles.indexOf(roles.add) < 0
#         user.roles.push roles.add
#         changed = true
#         updateVipmt roles.add
#     else
#       return findError new Error "Bad Parameter"
#   if roles.del
#     if Array.isArray(roles.del)
#       for r in roles.del
#         if (p = user.roles.indexOf r.toString()) >= 0
#           user.roles.splice p,1
#           changed = true
#     else if 'string' is typeof roles.del
#       if (roles.del is 'all')
#         user.roles = []
#         changed = true
#       else if (p = user.roles.indexOf roles.del) >= 0
#         user.roles.splice p,1
#         changed = true
#     else
#       return findError new Error "Bad Parameter"
#   return cb null,user unless changed
#   update.$set.roles = user.roles
#   if user.flwng? and (user.roles.indexOf('realtor') >= 0) # when realtor, unset flwng
#     update.$unset = {flwng:1}
#   User.findOneAndUpdate {_id:user._id},update ,{upsert:false,returnDocument:'after'},(err,r)->
#     if err
#       return findError err
#     nuser = r.value
#     if user is req.session.get 'user'
#       req.session.set 'user',nuser,->
#         cb null,nuser
#     else
#       cb null,nuser

# DEF 'updateUserSetRoles',updateUserSetRoles

# # Update one or multiple field of a user
# updateUser = (req,user,data,cb)->
#   if not cb # When user not provided seperately
#     cb = data
#     data = user
#     user = req.session.get 'user'
#   findError = (err)-> cb err
#   return findError new Error "No User" unless user
#   _updateUser = ->
#     delete data._id
#     delete data.eml
#     delete data.src
#     delete data.site # reserved keywords
#     unset = {}
#     # for key of data
#     #   if data.hasOwnProperty(key)
#     #  data[key] = data[key].replace(/\'/g," ")   #for '
#     #  data[key] = data[key].replace(/\"/g," ")   #for "
#     #  data[key] = data[key].replace(/\{/g," ")   #for {
#     #  data[key] = data[key].replace(/\}/g," ")   #for }
#     update = {}
#     for key,val of data
#       if key in ['$inc', '$push','$addToSet','$pull']
#         update[key] = val
#         delete data[key]
#         delete val['roles'] if key is '$push'
#         continue
#       if val or val is 0 # has something to set
#         # id:'' will delete id
#         # if user changes id field,
#         # 1. check if user already changed, if changed ignore, and disable that in settings
#         # 2. check for conflicts, if conflicts, send error
#         # 3. modify all id field in wecard collection?
#         if key is 'id'
#           unless ('string' is typeof val) and (/^[a-zA-Z0-9\-]+$/.test val)
#             return findError new Error "Wecard ID: Only a-z A-Z and -"
#           if val.length  < 5
#             return findError new Error "ID too short, at leaset 5 chars!"
#         if 'string' is typeof val
#           data[key] = val.replace(/\</g,'&lt;').replace(/\>/g,'&gt;')
#         if /^\$/.test key
#           return findError new Error "Bad key #{key}"
#       else
#         if key is 'id'
#           return findError new Error "ID too short, at leaset 5 chars!"
#         delete data[key]
#         unset[key] = 1
#     if Object.keys(data).length > 0
#       update.$set = data
#     if Object.keys(unset).length > 0
#       update.$unset = unset
#     if Object.keys(update).length <= 0
#       return cb null,user
#     User.findOneAndUpdate {_id:user._id},update ,{upsert:false,returnDocument:'after'},(err,r)->
#       if err
#         if err.code is 11000
#           return findError new Error "This ID has already been used!"
#         else
#           return findError err
#       nuser = r.value
#       if user is req.session.get('user') # update current user
#         req.session.set 'user',nuser,->
#           cb null,nuser
#       else
#         cb null,nuser
#   if data.roles
#     # delete data.roles
#     roles = data.roles
#     delete data.roles
#     updateUserSetRoles req,user,roles,(err,u2)->
#       if err then return findError err
#       user = u2
#       _updateUser()
#   else
#     _updateUser()

# DEF 'updateUser',updateUser

# # need DBG
# # user = {roles:['realtor']}
# # var ref, ref1;
# # (typeof user !== "undefined" && user !== null ? (ref = user.roles) != null ? ref.indexOf('realtor') : void 0 : void 0) || ((typeof user !== "undefined" && user !== null ? (ref1 = user.flwng) != null ? ref1.length : void 0 : void 0) > 0);
# # returns false

# # for school rank
# ADDPLUGIN 'canShowVOWDetail',(user)->
#   #(user? and @isChinaIP()) or (user?.roles?.indexOf('realtor') >= 0) or (user?.flwng?.length > 0)
#   return true if user
#   false
# ADDPLUGIN 'userCpny',(user)->
#   return null unless user
#   if @locale()?.toString().substr(0,2).toLowerCase() is 'zh'
#     return user.cpny_zh or user.cpny_en or user.cpny
#   return user.cpny_en or user.cpny_zh or user.cpny
# ADDPLUGIN 'fullNameOrNickname',(user)->
#   return null unless user
#   isZh = @locale()?.toString().substr(0,2).toLowerCase() is 'zh'
#   if isZh
#     # if user.nm_zh
#     return user.nm_zh or user.nm_en or user.nm or ''
#   return user.nm_en or user.nm_zh or user.nm or ''
#   # if user.fn and user.ln
#   #   if isZh
#   #     ret = user.ln + user.fn
#   #   else
#   #     ret = user.fn + ' ' + user.ln
#   #   # if user.nm
#   #   #   ret += "(#{user.nm})"
#   #   return ret
#   # else if user.nm
#   #   return user.nm
#   # else
#   #   return (user.fn or '') or (user.ln or "")
# ADDPLUGIN 'getShareUID',(user)->
#   return null unless user
#   #not makes sence when sharing rm listing
#   # (user.roles?.indexOf('vip_alliance') >= 0)
#   if (user.roles?.indexOf('realtor') >= 0) or user.realtor#getUserPublicInfo will not return roles, instead .realtor = 1, .vip =1
#     return user._id or user.id
#   if user.flwng?.length > 0
#     return user.flwng[0].uid
#   null
# ADDPLUGIN 'isfasAdmin', (user, city) ->
#   # must be reginal admin
#   # no city specified, then cannot admin
#   # return false unless user?.fas?.length and city?
#   # for element in user.fas
#   #   if element.city is city.city or ((not element.city) and element.prov is city.prov)
#   #     return true
#   return false

# infoUserId = null
# ADDPLUGIN 'getProjShareUID',(user, cb)->
#   return cb(null,infoUserId) if infoUserId
#   User.findOne {eml:appConfig.defaultRTEmail}, (err,ret)->
#     if err
#       console.error err if err
#       return cb err
#     if not ret
#       return cb null, ''
#     ret ?= {}
#     infoUserId = ret._id
#     return cb null, ret._id

# sendSMS = (user,body,cb)->
#   unless user?.mbl?.length > 8
#     return cb "No Cell Phone Number"
#   sms =
#     To: '+1' + user.mbl
#     Body: body
#     From: '+19056142609'
#   # console.log sms
#   baseSendSMS sms,(err,response)->
#     if err then console.error err
#     cb err

# sendPN = (user,body,cb)->
#   unless user?.pn?.length > 8
#     return cb "No Push Notify ID"
#   pn =
#     to: user.pn
#     from: 'RealMaster'
#     msg: body
#   # console.log pn
#   pushNotify pn,ErrColl,(err,response)->
#     if err then console.error err
#     cb err

# sendPNorSMS = (buzU,body,cb)->
#   if buzU.pn
#     # send notification
#     sendPN buzU,body,cb
#   else if buzU.mbl
#     sendSMS buzU,body,cb
#   else
#     cb new Error("No method to send notification")

# DEF 'sendPN',sendPN
# DEF 'sendPNorSMS',sendPNorSMS

# # need to add support when no user in session
# followUser = (req,user,buzUserID,cb)->
#   findError = (e)-> if cb then cb e
#   # add to flwng
#   #unless user = req.session.get('user')
#   #  return findError new Error "No User"

#   unless buzUserID
#     return findError new Error "No Followed User"
#   q = {id:buzUserID}
#   if 'string' is typeof buzUserID and /^[a-f0-9]{24}$/i.test buzUserID
#     q = {_id:buzUserID}
#   User.findOneAndUpdate q,{$inc:{rmb:-30,wsc:-10}},(err,buzU)->
#     # change buz rmb
#     return findError new Error "user not found" unless buzU = buzU.value
#     tmp = {}
#     tmp.uid = buzU._id.toString()
#     tmp.ts = new Date()
#     if flwng = user.flwng
#       flwng.push tmp
#       update = flwng:flwng
#     else
#       update = flwng:[tmp]
#     sendPNorSMS buzU,"#{user.nm} followed you.",(err)->
#       if err then console.log err
#     updateUser req,user,update,(err,user)->
#       if err then return findError err
#       return cb null,user

# DEF 'followUser',followUser


# updateUserActivity = (req,act,cb)->
#   switch act
#     when 'share'
#       actpts = 1
#       wsc = 1
#       rmb = 5
#     when 'newBlog'
#       actpts = 1
#       wsc = 1
#       rmb = 0
#     when 'follow'
#       actpts = 1
#       rmb = 0
#     when 'favNews'
#       actpts = 1
#       rmb = 0
#     when 'favProp'
#       actpts = 1
#       rmb = 0
#     when 'sponsed'
#       actpts = 2
#       rmb = 0
#     else
#       return cb null
#   updateUser req,{$inc:{actpts:actpts, rmb:rmb, wsc:wsc}},cb

# isRealtor = (user)-> user?.roles?.indexOf('realtor') >= 0
# DEF 'isRealtor',isRealtor

# DEF 'updateUserActivity',updateUserActivity
# userPublicFields =
#   _id:1
#   id:1
#   fn:1
#   nm_zh:1
#   nm_en:1
#   cpny_zh:1
#   cpny_en:1
#   ln:1
#   nm:1
#   avt:1
#   mbl:1
#   eml:1
#   web:1
#   sgn:1
#   wx:1
#   itr:1
#   wxgrp:1
#   cpny:1
#   roles:1
#   qrcd:1
#   grpqrcd:1
#   wurl:1
#   fburl:1
#   twturl:1
#   cpny_pstn:1
#   addr:1
#   tel:1
#   fax:1
#   exlang:1
#   fas:1,
#   fornm:1
#   sas:1
#   category:1
#   cpmnm:1
  # forBlk:1
  # splang:1
# getProcessUserInfoOpt = (opt,user)->
# #   opt =
# #     isAllowedVipUser:opt.isAllowed('vipUser',user)
# #     isAllowedEventAdmin:opt.isAllowed('eventAdmin',user)
# #     hasRoleRealtor:libUser.hasRole('realtor',user)
# #     fullNameOrNickname:opt.fullNameOrNickname user
# #     userCpny:opt.userCpny user
# # DEF 'getProcessUserInfoOpt', getProcessUserInfoOpt

# #TODO:
# processUserInfo = (opt, user)->
#   return {} unless user
#   # always create an copy. Don't change original object, when we are not sure if we shall change it
#   r,user
#   rUser.vip = 1 if checkAllowed('vipUser',user)
#   rUser.realtor = 1 if libUser.hasRole('realtor',user)
#   rUser.evtAdmin = 1 if checkAllowed('eventAdmin',user)
#   delete rUser.roles
#   rUser.fnm = libUser.fullNameOrNickname(opt.locale,user)
#   rUser.cpny = libUser.userCpny(opt.locale,user)
#   rUser.eml = user.eml[0] if Array.isArray user.eml
#   for i in ['web', 'wurl', 'fburl', 'twturl']
#     if (url = user[i]) and (not /^http/.test url)
#       rUser[i] = 'http://' + user[i]
#   rUser
# DEF 'processUserInfo',processUserInfo

# getUserPublicInfoOpt = (opt)->
#   # opt =
#   #   # headers:opt.headers
#   #   # locale:opt.locale
#   #   isAllowed:(role,user)->opt.isAllowed(role,user)
#   #   hasRole:(role,user)->opt.hasRole(role,user)
#   #   fullNameOrNickname:(user)->opt.fullNameOrNickname user
#   #   userCpny:(user)->opt.userCpny user
#   # not able to get function as param in second level
#   opt
# DEF 'getUserPublicInfoOpt',getUserPublicInfoOpt

# getUserPublicInfo = (opt, q, cb)->
#   # if user
#   #   ret = {}
#   #   for k, v of fields
#   #     ret[k] = user[k]
#   #   return cb null, user
#   User.findOne q, {fields:userPublicFields}, (err,user)->
#     return cb(err.toString()) if err
#     return cb(null,{}) unless user
#     user ?= {}
#     # opt = getProcessUserInfoOpt opt,user
#     user = processUserInfo(opt, user)
#     return cb(null, user)
# DEF 'getUserPublicInfo', getUserPublicInfo

# getUserPrivateInfo = (req, user)->
#   if user?._id
#     ret = {}
#     for k, v of userPublicFields
#       ret[k] = user[k]
#     # opt = getProcessUserInfoOpt req
#     ret = processUserInfo({locale:req.locale()}, ret)
#     return ret
#   else
#     return {}
# DEF 'getUserPrivateInfo', getUserPrivateInfo

# # move to propdetail
#   # dataMethods = DEF 'dataMethods'
#   # dataMethods.sessionUser = (req, user)->
#   #   return getUserPrivateInfo(req, user)

# # TODO : 7.9
# wechatGetUserInfo = (param,cb)->
  # url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=#{param.appId}&secret=#{param.secret}&code=#{param.code}&grant_type=authorization_code"
  # opt =
  #   protocol: 'https'
  #   timeout: 20000
  # debug.verbose "wechatGetUserInfo: start #{new Date()}"
  # helpers.http_get url,opt,(err,ret)->
  #   debug.verbose "wechatGetUserInfo: got token #{new Date()}"
  #   if err
  #     return cb err
  #   try
  #     json = JSON.parse ret
  #   catch e
  #     console.log "Error WeChat Return:" + ret
  #     return cb e #resp.redirect '/wechatauth/web?err=2'
  #   if json.errcode
  #     console.log "Error: #{json.errmsg}"
  #     return cb json #resp.redirect '/wechatauth/web?err=3'
  #   url = "https://api.weixin.qq.com/sns/userinfo?access_token=#{json.access_token}&openid=#{json.openid}"
  #   debug.verbose "wechatGetUserInfo: to get userinfo #{new Date()}"
  #   helpers.http_get url,opt,(err,ret)->
  #     debug.verbose "wechatGetUserInfo: got userinfo #{new Date()}"
  #     if err
  #       return cb err #resp.redirect '/wechatauth/web?err=11'
  #     try
  #       json = JSON.parse ret
  #     catch e
  #       console.log "Error WeChat Return:" + ret
  #       return cb e #resp.redirect '/wechatauth/web?err=12'
  #     if json.errcode
  #       console.log "Error: #{json.errmsg}"
  #       return cb json #resp.redirect '/wechatauth/web?err=13'
  #     console.log json
  #     cb null,json
# DEF 'wechatGetUserInfo',wechatGetUserInfo

# ###
# { openid: 'oyQyduJxHQfAPvzAwpFdzloQyM1k',
# nickname: 'Nick Name ',
# sex: 1,
# language: 'zh_CN',
# city: '',
# province: '',
# country: 'CA',
# headimgurl: 'http://wx.qlogo.cn/mmopen/sTJptKvBQLIrGdo2tvPEWERgPay8NUicRa8mAniby58Mp0OILtRheK8nGPbKjrZweqxjzaNOf9UVhkvz9Eib8icMXlkuVB0Cmv3j/0',
# privilege: [],
# unionid: 'oSWWZt_Yua6F68JgIS5J9j8hL5hA---' }
# ###
# wechatEnsureWechatUser = (wxuser,cb)->
#   return cb new Error("No Wechat User ID") unless wxuser?.unionid
#   User.findOne {wxuid:wxuser.unionid},(err,user)->
#     if err then console.error err
#     if user # has user already
#       return sendPN user,"WeChat Auth Performed.",cb
#     # insert into wechat_user
#     WechatUser.updateOne {_id:wxuser.unionid},{$set:wxuser},{upsert:true},cb
# DEF 'wechatEnsureWechatUser',wechatEnsureWechatUser

# # can only bind current user
# wechatAuthUser = (req,wxuser,cb)->
#   findError = (err)->
#     console.log err
#     cb err
#   return cb new Error("No Wechat User ID") unless wxuser?.unionid
#   # user logged in, bind wechat account
#   if user = req.session.get('user')
#     # console.log '+++++session user, return bind'
#     return wechatBindUser req,user,wxuser,cb
#   # not logged in
#   User.findOne {wxuid:wxuser.unionid},(err,user)->
#     return findError err if err
#     # console.log '+++++dbuser',user
#     uploadUserAvt req,user,{uTp:'wx'},->
#       if eml = user?.eml
#         # has user, try login
#         eml = eml[0] if Array.isArray eml
#         return auth_by_id req,eml,(err,nuser)->
#           return findError err if err
#           app_after_login req,nuser,eml,(err)->
#             return findError err if err
#             cb null,nuser
#       # no user, ensureWechatUser
#       WechatUser.findOneAndUpdate {_id:wxuser.unionid},{$set:wxuser},{upsert:true,returnDocument:'after'},(err,ret)->
#         # ask login or input email/phone
#         if err then return cb err
#         nwxuser = ret?.value
#         cb null,{needUser:true,wxuser:nwxuser}

# reserveUserFile = DEF 'reserveUserFile'
# #download in img server
# uploadUserAvt = (req,user,opt,cb)->
#   unless user
#     console.log 'No user yet'
#     return cb()
#   if /realmaster/.test user.avt
#     return cb()
#   reserveUserFile user,0,(err,nUserFile,fileNames)->
#     if err
#       req.logger?.log err
#       return cb()
#     db = {Users:User,UserFile}
#     postThirdPartyImg user,nUserFile,db,opt,(err,nuser)->
#       if err
#         req.logger?.log err
#         return cb()
#       # NOTE: minor error, 404, 403 instead of db error
#       if 'string' is typeof nuser #and/Error/.test nuser
#         console.error nuser
#         return cb()
#       # updateUser req,user,{avt:nuser.avt},(err,nuser)->
#       # user.avt has updated, update session user avt
#       if (suser = req.session.get('user')) and (suser._id?.toString() is nuser?._id?.toString())
#         return req.session.set 'user',nuser,()->
#           return cb()
#         # console.log 'updated\t'+user.eml+'\t'+nuser.avt
#       cb()


# wechatBindUser = (req,user,wxuser,cb)->
#   data = {}
#   # avtor
#   if wxuser.headimgurl?.length > 10
#     data.wxavt = wxuser.headimgurl
#     if (not user?.avt)
#       data.avt = wxuser.headimgurl
#   # unionid
#   data.wxuid = wxuser.unionid
#   # nickname
#   if (not user?.fn) and (not user?.ln) # user probably not setup names yet
#     data.nm = wxuser.nickname
#   else
#     data.wxnm = wxuser.nickname
#   # sex
#   if not user?.sex?
#     switch wxuser.sex
#       when 1 then data.sex = 'Male'
#       when 2 then data.sex = 'Female'
#       else data.sex = 'None'
#   # lang
#   if (not user?.locale?) and wxuser.lang
#     data.locale = wxuser.lang.toLowerCase().replace('_','-')
#   User.updateMany {wxuid:wxuser.unionid},{$unset:{wxuid:1}},{multi:1},(err)->
#     if err then return cb err
#     updateUser req,user,data,(err,nuser)->
#       if err then return cb err
#       # flwng
#       uploadUserAvt req,nuser,{uTp:'wx'}, ->
#         if wxuser.flwng
#           followUser req,nuser,wxuser.flwng,(err,nnuser)->
#             cb err,nnuser
#         else
#           cb null,nuser
#         return if wxuser.isFromMiniApp
#         sendPNorSMS user,"Bound with Wechat Acnt #{wxuser.nickname}",->
#           WechatUser.deleteOne {_id:wxuser._id},(err)->
#             if err then console.error err

# DEF 'wechatAuthUser',wechatAuthUser

# # create/update a user
# wechatBindDBUser = (req,wxuid,cb)->
#   WechatUser.findOne {_id:wxuid},{readPreference:User.ReadPreferencePrimary},(err,wxuser)->
#     if err then return cb err
#     if not wxuser then return cb new Error "Unknown Wechat user id #{wxuid}"
#     if user = req.session.get('user')
#       console.log wxuser
#       return wechatBindUser req,user,wxuser,cb
#     else
#       return cb new Error "Need login"

# DEF 'wechatBindDBUser',wechatBindDBUser


# wbGetUserInfo =  (param,cb)->
#   #url = "http://openapi.58.com/oauth2/access_token"
#   unless param.code and param.redirect_uri and param.wbClientId and param.wbSecret
#     return cb new Error "No enough parameters"
#   opt =
#     protocol: 'http'
#     host: 'openapi.58.com'
#     path: '/oauth2/access_token'
#     timeout: 20000
#   data =
#     code: param.code
#     grant_type: 'authorization_code'
#     redirect_uri: param.redirect_uri
#     time_sign: param.time_sign or Date.now()
#     client_id: param.wbClientId
#   data.client_secret = crypto.createHash('md5').update(param.wbSecret + 'openapi.58.com' + data.time_sign).digest('hex')
#   console.log data
#   helpers.http_post opt,data,(err,ret)->
#     if err
#       return cb err
#     try
#       json = JSON.parse ret
#     catch e
#       console.log "Error 58 Return:" + ret
#       return cb e #resp.redirect '/wechatauth/web?err=2'
#     if json.errcode
#       console.log "Error: #{json.errmsg}"
#       return cb json #resp.redirect '/wechatauth/web?err=3'
#     cb null,json
# DEF 'wbGetUserInfo',wbGetUserInfo

# ###
# switch current user to another user account.
# Without newUser, account will switch back to original user
# callback: {ok:0/1,err:error message}
# ###
# switchUser = (req,newUser,cb)->
#   unless cb
#     cb = newUser
#     newUser = null
#   # check user/realUser
#   return cb {ok:0,err:"Unknown User"} unless user = (req.session.get('realUser') or req.session.get('user'))
#   return cb {ok:0,err:"No Auth"} unless checkAllowed 'userAdmin',user
#   # check if return to original user
#   if not newUser
#     if not (realUser = req.session.get('realUser'))
#       return cb {ok:0, err: "No Real User"}
#     req.session.set 'user',realUser,->
#       req.session.set 'realUser',null,->
#         cb {ok:1}
#     return null
#   # find newUser
#   q = {eml:newUser}
#   if ('string' isnt typeof newUser) or ((not helpers.isEmail(newUser)) and (newUser.length is 24))
#     q = {_id:newUser}
#   User.findOne q,(err,nUser)->
#     if err
#       console.error err
#       return cb {ok:0,err:err}
#     return cb {ok:0,err:"User not found"} unless nUser
#     if nUser.roles?.indexOf('_admin') >= 0
#       return cb {ok:1,err:"Not Authorized"}
#     # do not overwrite realUser
#     unless req.session.get('realUser')
#       req.session.set('realUser',user)
#     req.session.set 'user',nUser,->
#       cb {ok:1}

# DEF 'switchUser',switchUser

# _auth_login_n_ensure_login = DEF '_auth_login_n_ensure_login'

# getUserFromThirdPartyObj = (obj) ->
#   switch obj.uTp
#     when 'google'
#       uData =
#         googleId: obj.id
#         nm:obj.name
#         eml:obj.email
#         src:'website'
#         googleLocal:obj.locale
#         fn:obj.given_name
#         ln:obj.family_name
#         pic:obj.picture
#         uTp:obj.uTp
#     when 'facebook'
#       uData =
#         facebookId: obj.id
#         nm:obj.name
#         eml:obj.email
#         src:'website'
#         fn:obj.first_name
#         ln:obj.last_name
#         pic:obj.picture?.data?.url
#         uTp:obj.uTp
#   for k,v of uData
#     delete uData[k] unless v
#   uData

# authThirdPartyUser = (req, obj, cb) ->
#   email = obj.email
#   return cb "No #{obj.uTp} email", null unless email
#   auth_has_login email, (err, uExist) ->
#     data = getUserFromThirdPartyObj obj
#     if uExist
#       # if has user, sigin automataclly _auth_login_only_by_id
#       return auth_by_id req, email, (err, user) ->
#         return cb err,user,false if user.googleId or user.facebookId
#         # bind user with googleid/facebookid
#         if data.uTp is 'google' and (not user.googleId)
#           update = {googleId: data.googleId}
#         if data.uTp is 'facebook' and (not user.facebookId)
#           update = {facebookId: data.facebookId}
#         sendMail = true
#         updateUser req,user,update,(err,nuser) ->
#           # send notification email when user bind google/facebook
#           return cb err,nuser,sendMail
#     # if no user , create one; and create login, signin automatically
#     ensureUser req,data,(err,user)->
#       return cb err,null if err
#       uploadUserAvt req,user,{uTp:data.uTp},->
#         return _auth_login_n_ensure_login req,email,(err,user,{pass})->
#           return cb err,null if err
#           cb null,user,pass



# createUniqueUserID = DEF 'createUniqueUserID'
# ensureUser = (req,data,cb)->
#   User.findOne {eml:data.eml},(err,user)->
#     if err then return findError err
#     if user?._id then return cb null,user
#     #in download page user may pass nm in
#     unless data.nm
#       nickName = data.eml.substring 0, data.eml.indexOf('@')
#       nickName += ''+ Math.floor(Math.random() * (8999 + 1)) + 1
# #     else
#       nickName = data.nm
#     createUniqueUserID (err,uid)->
#       #todo Debug
#       #console.log '##############' + uid
#       if err then return findError err
#       src = req.getUA() or 'browser'
#       if apsv = req.cookies?.apsv
#         src += ":#{apsv}"
#       else
#         src += ':' + data.src if data.src #data.src = 'download' if from download page
#       data.eml = [data.eml]
#       delete data.uTp
#       u = Object.assign {nm:nickName,src:src,id:uid},data
#       u.wxuid = data.wxid if data.wxid
#       u.mbl = data.mbl if data.mbl
#       #set current weekday for edm group
#       u.edmGrp = (new Date()).getDay()
#       if locale = req.locale() # save locale for new user
#         u.locale = locale
#         switch locale
#           when 'zh'
#             u.splang = ['Cantonese']
#           when 'zh-cn'
#             u.splang = ['Mandarin']
#           when 'en'
#             u.splang = ['English']
#       User.insertOne u,{safe:true},(err,users)->
#         cb err,users?.ops?[0]

# new part
# authThirdPartyUser = (req, obj, cb) ->
#   unless obj
#     errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
#     debug.error 'authThirdPartyUser',errMsg
#     return cb errMsg,null,{}
#   email = obj.email
#   unless email and helpers.isEmail(email)
#     errMsg = 'Email is missing or invalid'
#     debug.error 'authThirdPartyUser', errMsg
#     return cb req.l10n(errMsg), null,{}
#   auth_has_login email, (err, existingUser) ->
#     data = libOauth.genUserFromThirdPartyObj obj
#     unless data
#       errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
#       debug.error 'auth_has_login',errMsg
#       return cb errMsg,null,{}
#     # console.log 'existingUser',existingUser
#     if not existingUser
#       # if no user , create one; and create login, signin automatically
#       return oauthCreateUser req, data,email, cb
#     oauthLoginUser req, data, cb
#     if not existingUser.emlV
#       authEmailVerified email,(err)->
#         debug.error err if err
# # DEF 'authThirdPartyUser', authThirdPartyUser

# _oauthLoginUser = (req, user, cb) ->
#   email = getUserEmail user
#   return auth_by_id req, email, (err, user) ->
#     app_after_login req,user,email,(err)->
#       return cb err,user
# # return {err, loginedUser, {sendMail:true/false}}
# oauthLoginUser = (req, uData, cb)->
#   unless uData
#     errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
#     debug.error 'oauthLoginUser',errMsg
#     return cb errMsg,null,{}
#   email = uData.eml or uData.email
#   User.findOne {eml:email},(err,existingUser)->
#     return cb err, null,{} if err
#     unless existingUser
#       errMsg = "#{email} #{req.l10n(MSG_STRINGS.USER_NOT_FOUND)}"
#       debug.error 'oauthLoginUser',errMsg
#       return cb errMsg, null,{}
#     update = null
#     if (uData.uTp is 'google') and (not existingUser.googleId)
#       update = googleId:uData.googleId
#     else if (uData.uTp is 'facebook') and (not existingUser.facebookId)
#       update = facebookId:uData.facebookId
#     # send notification email when user bind google/facebook
#     done = (err,updatedUser)->
#       _oauthLoginUser req,updatedUser, (err, loginedUser)->
#         ret = if (existingUser.googleId or existingUser.facebookId) then {} else {sendMail:true}
#         return cb err,loginedUser,ret
#     unless update
#       return done(null,existingUser)
#     # bind user with googleId/facebookId
#     return updateUser req,existingUser,update,done

# DEF 'oauthLoginUser', oauthLoginUser
# # NOTE: old code fixed by sam
# # ensureUser req,data,(err,user)->
# #   return cb err,null if err
# #   uploadUserAvt req,user,{uTp:data.uTp},->
# #     return _auth_login_n_ensure_login req,email,(err,user,ret)->
# #       if err
# #         debug.error '_auth_login_n_ensure_login', err
# #         return cb err,null
# #       unless ret
# #         debug.error '_auth_login_n_ensure_login', "Not able to find login with email: #{email}"
# #         return cb MSG_STRINGS.USER_NOT_FOUND,null
# #       cb null,user,ret.pass
# oauthCreateUser = (req, uData, email, cb) ->
#   ensureUser req,uData,(err,user)->
#     return cb err,null,{} if err
#     uploadUserAvt req,user,{uTp:uData.uTp},->
#       # email = getUserEmail user
#       return _auth_login_n_ensure_login req,email,(err,user,ret)->
#         if err
#           debug.error '_auth_login_n_ensure_login', err
#           return cb err,null,{}
#         unless ret
#           debug.error '_auth_login_n_ensure_login', "Not able to find login with email: #{email}"
#           return cb MSG_STRINGS.USER_NOT_FOUND,null,{}
#         {pass} = ret
#         app_after_login req,user,email,(err)->
#           return cb err,null,{} if err
#           cb null,user,{pass}
# DEF 'oauthCreateUser', oauthCreateUser
