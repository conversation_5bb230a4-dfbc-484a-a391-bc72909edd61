helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'

i18n_fields = "area,municipality,lp_code,type_own1_out,style,county,bldg_amen1_out,bldg_amen2_out,bldg_amen3_out,bldg_amen4_out,bldg_amen5_out,bldg_amen6_out,bsmt1_out,bsmt2_out,bus_type,a_c,gar_type,laundry_lev,lotsz_code,heating,fuel,drive,constr1_out,constr2_out,prop_feat1_out,prop_feat2_out,prop_feat3_out,prop_feat4_out,prop_feat5_out,prop_feat6_out,rm1_out,rm2_out,rm3_out,rm4_out,rm5_out,rm6_out,rm7_out,rm8_out,rm9_out,rm10_out,rm11_out,rm12_out,sewer
,rm1_dc1_out,rm1_dc2_out,rm1_dc3_out
,rm2_dc1_out,rm2_dc2_out,rm2_dc3_out
,rm3_dc1_out,rm3_dc2_out,rm3_dc3_out
,rm4_dc1_out,rm4_dc2_out,rm4_dc3_out
,rm5_dc1_out,rm5_dc2_out,rm5_dc3_out
,rm6_dc1_out,rm6_dc2_out,rm6_dc3_out
,rm7_dc1_out,rm7_dc2_out,rm7_dc3_out
,rm8_dc1_out,rm8_dc2_out,rm8_dc3_out
,rm9_dc1_out,rm9_dc2_out,rm9_dc3_out
,rm10_dc1_out,rm10_dc2_out,rm10_dc3_out
,rm11_dc1_out,rm11_dc2_out,rm11_dc3_out
,rm12_dc1_out,rm12_dc2_out,rm12_dc3_out
,level1,level2,level3,level4,level5,level6,level7,level8,level9,level10,level11,level12
,water,laundry_lev
,spec_des1_out,spec_des2_out,spec_des3_out,spec_des4_out,spec_des5_out,spec_des6_out
,pool,uffi,status
,wcloset_t1lvl,wcloset_t2lvl,wcloset_t3lvl,wcloset_t4lvl,wcloset_t5lvl
,partial,mate"
i18n_fields = (helpers.trim(n) for n in i18n_fields.split ',')

i18n_sfields = "area,municipality,lp_code,type_own1_out,style,bus_type,prop_type,county,status,mate,partial"
i18n_sfields = (helpers.trim(n) for n in i18n_sfields.split ',')

i18n_mfields = "exclusive,include,tenant,tenant_req,term,amenities,equipment,basement"
i18n_mfields = (helpers.trim(n) for n in i18n_mfields.split ',')

i18n_noinsert_fields = "occ,zoning"
i18n_noinsert_fields = (helpers.trim(n) for n in i18n_noinsert_fields.split ',')


ary2ctx = (ary)->
  ctxobj = {}
  for k in ary
    ctxobj[k] = k.replace /\d/g,''
  ctxobj
i18n_fields = ary2ctx i18n_fields
i18n_sfields = ary2ctx i18n_sfields
i18n_mfields = ary2ctx i18n_mfields
i18n_noinsert_fields = ary2ctx i18n_noinsert_fields

i18ns = {}

DEF "l10nDetail",(d,lang = 'en')->
  _ = (i18ns[lang] ?= i18n.getFun lang)
  for k,ctx of i18n_fields
    if (v = d[k])?.length > 0
      d[k] = _ v,ctx
  for k,ctx of i18n_mfields
    if (v = d[k])?.length > 0
      dda = []
      for vv in v.split ','
        dda.push _ vv,ctx
      d[k] = dda.join ';'
  for k,ctx of i18n_noinsert_fields
    if (v = d[k])?.length > 0
      d[k] = _ v,ctx,true
  d

DEF "l10nList",(list,lang = 'en')->
  _ = (i18ns[lang] ?= i18n.getFun lang)
  for d in list
    d.etype = d.type_own1_out
  for k,ctx of i18n_sfields
    for d in list
      if (v = d[k])?.length > 0
        d[k] = _ v,ctx
  for k,ctx of i18n_mfields
    for d in list
      if (v = d[k])?.length > 0
        dda = []
        for vv in v.split ','
          dda.push _ vv,ctx
        d[k] = dda.join ';'
  for k,ctx of i18n_noinsert_fields
    for d in list
      if (v = d[k])?.length > 0
        d[k] = _ v,ctx,true
  list
  