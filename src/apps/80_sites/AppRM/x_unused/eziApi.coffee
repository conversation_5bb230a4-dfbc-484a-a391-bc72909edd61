# 2018.03.28 not used.
# appConfig = CONFIG()
# ALLOWED_IPS = appConfig.eziIPs #'127.0.0.1'
#
# User = COLLECTION 'chome', 'user'
# json = INCLUDE 'lib.json'
#
# authCreateLogin = DEF '_auth_create_login'
# authHasLogin = DEF '_auth_has_login_id'
# createUniqueUserID = DEF 'createUniqueUserID'
#
#
# APP 'api'
#
# realtor_schema = new json.JsonSchema {
#   type: 'object'
#   properties:
#     eziUid:
#       type: 'string'
#       required: true
#     locale:
#       type: 'string'
#       required: true
#       enum: ['en','zh','zh-cn']
#     rid:
#       required: true
#     eml:
#       type: 'string'
#       format: 'email'
#       required: true
#     mbl:
#       required: true
#   }
# user_schema = new json.JsonSchema {
#   type: 'object'
#   properties:
#     rmUid:
#       type: 'string'
#       required: true
#     eml:
#       type: 'string'
#       format: 'email'
#       required: true
#     mbl:
#       required: true
#     nm:
#       required: true
#       type: 'string'
#   }
#
#
# returnError = (resp,err)-> resp.send {ok:0,e:err}
#
# isAllowedIP = (req,resp)->
#   unless ALLOWED_IPS.indexOf(req.remoteIP()) >= 0
#     returnError resp, "Unauthorized Access"
#     return false
#   true
#
# GET 'isUser',(req,resp)->
#   return unless isAllowedIP req,resp
#
#   dm = req.param('dm')
#   eml = req.param('e')
#   unless dm and eml
#     return returnError resp,"Bad Parameters"
#   eml = eml.toString().toLowerCase()
#   User.findOne {eml:eml},(e,user)->
#     if e then return returnError resp, e.toString()
#     st = 0
#     if user
#       st = 1
#       if user.roles?.indexOf('realtor') >= 0
#         st = 10
#       else if user.flwng?
#         st = 2
#     resp.send {ok:1,st:st}
#
#
# createUser = (req,resp,realtor)->
#   return unless isAllowedIP req,resp
#
#   findError = (err)->
#     console.error err
#     returnError resp,err
#
#   data = req.param 'data'
#   try
#     eziUser = JSON.parse data
#   catch e
#     console.error e
#     console.error data
#     return findError "Bad Parameter"
#   console.log eziUser
#
#   # check params
#   if realtor
#     fields = ['eziUid','eml','locale','mbl','rid','pass','fn','ln','nm','cpny','cpny_pstn']
#     schema = realtor_schema
#   else
#     fields = ['rmUid','eml','mbl','nm']
#     schema = user_schema
#
#   for k,v of eziUser
#     if fields.indexOf(k) < 0
#       return findError "Unknown parameter #{k}"
#
#   updateUser = (eziUser,user,cb)->
#     set = {}
#     for k in fields
#       set[k] = eziUser[k] if eziUser[k]
#     delete set.eml
#     update = $set: set
#
#     if eziUser.roles
#       if user.roles
#         update.$addToSet = roles: $each: eziUser.roles
#       else
#         update.$set.roles = eziUser.roles
#     if eziUser.flwng
#       if user.flwng
#         for v,i in user.flwng
#           if v.uid is eziUser.flwng[0].uid
#             user.flwng.splice i,1
#             break
#         user.flwng.unshift eziUser.flwng[0] # put as first
#         update.$set.flwng = user.flwng
#       else
#         update.$set.flwng = eziUser.flwng
#
#     User.findOneAndUpdate {_id:user._id},update,{returnDocument:'after'},(err,nUser)->
#       cb err,(nUser.value or nUser),1
#
#   ensureUser = (eziUser,cb)->
#     User.findOne {eml:eziUser.eml},(err,user)->
#       if err then return findError err
#       if not eziUser.nm?
#         nickName = eziUser.eml.substring 0, eziUser.eml.indexOf('@')
#         nickName += ''+ Math.floor(Math.random() * (8999 + 1)) + 1
#         eziUser.nm = nickName
#       if realtor
#         eziUser.roles = ['realtor','vip']
#       else
#         eziUser.flwng = [{uid:eziUser.rmUid,ts:new Date()}]
#
#       if user?._id # existing user, update info only
#         return updateUser eziUser,user,cb
#       eziUser.src = 'EZI'
#       createUniqueUserID (err,uid)->
#         if err then return findError err
#         eziUser.id = uid
#         User.insertOne eziUser,{safe:true},(err,users)->
#           cb err,users?.ops[0]
#   ensureLogin = (eziUser,user,cb)->
#     authHasLogin eziUser.eml,(err,has)->
#       if err then return findError err
#       if has then return cb null,has
#       authCreateLogin eziUser.eml,eziUser.pass,user._id,(err)->
#         cb err,false
#   done = (user,hadUser)->
#     resp.send {ok:1,rmUid:user._id,exists:hadUser}
#
#
#   schema.validate eziUser,(err,nb)->
#     return findError err if err
#     eziUser.eml = eziUser.eml.toLowerCase()
#     pass = eziUser.pass or eziUser.mbl
#     delete eziUser.pass
#     ensureUser eziUser,(err,user,hadUser)->
#       if err then return findError err
#       eziUser.pass = pass
#       ensureLogin eziUser,user,(err,hadLogin)-> # check if has login already
#         done user, hadUser
#
#
# ###
#     data: utf-8 encoded json string
#       eziUid: EZI user id
#       locale: en/zh-cn/zh  (zh-cn: simplified chinese, zh: taiwan chinese)
#       mbl: (cell phone number. Number only)
#       rid: TREB ID
#       eml: email
#       pass: login password
#
#       (following are optional)
#       fn: first name
#       ln: last name
#       nm: nickname (optional)
#       avt: aviator url (prefer 144x144px)
#       cpny: company name
#       cpny_pstn: Broker/Salesperson
#   return: utf-8 encoded json string
#     ok: 1 or 0 (1: success, 0: failed)
#     e: error message if has error
#     rmUid: RealMaster User ID (when success)
# ###
#
# POST 'ensureRealtor',(req,resp)->
#   createUser req,resp,true
# # for test
# #GET 'ensureRealtor',(req,resp)->  createUser req,resp,true
#
# ###
# parameters:
#   data: utf-8 encoded json string
#     rmUid: realtor’s RM UID
#     mbl: mobile phone number (number only)
#     eml: email address
#     nm: nickname (optional)
#     pass: login password (optional)
# return: utf-8 encoded json string
#   ok: 1 or 0 (1: success, 0: failed)
#   e: error message if has error
#   rmUid: RealMaster User ID (when success)
#   pass: generated password, if not provided
# ###
#
# POST 'bindC',(req,resp)->
#   createUser req,resp,false
# # for test
# #GET 'bindC',(req,resp)-> createUser req,resp,false
#
#
# ###
#   parameters:
#     data: utf-8 encoded json string
#       rmUid: realtor’s RM UID
#       enable: 1 or 0  (enable as paid realtor)
#   return: utf-8 encoded json string
#     ok: 1 or 0 (1: success, 0: failed)
#     e: error message if has error
# ###
# POST 'eziPaid',(req,resp)->
#   return unless isAllowedIP req,resp
#
#   findError = (err)-> returnError resp,err
#
#   data = req.param 'data'
#   try
#     eziCmd = JSON.parse data
#   catch e
#     console.error e
#     return findError "Bad Parameter"
#
#   unless uid = eziCmd.rmUid
#     return findError "No User Specified"
#   User.findOne {_id:uid},(err,user)->
#     if err then return findError err
#     unless user then return findError "Unknown User"
#     update = null
#     if eziCmd.enable
#       if user.roles?.indexOf('ezi') >= 0
#         return resp.send {ok:1}
#       update = {$addToSet:roles:'ezi'}
#     else
#       unless user.roles?.indexOf('ezi') >= 0
#         return resp.send {ok:1}
#       update = {$pull:roles:'ezi'}
#     User.findOneAndUpdate {_id:user._id},update,(err)->
#       if err then return findError err
#       resp.send {ok:1}
