# TODO: intit server list from db
# serversList = [
#   {nm:'d2',domain:'http://d2.realmaster.com',ip:null},
#   {nm:'apptest',domain:'http://app.test:8080',ip:null}
# ]
# currentServerName = 'apptest'

# notifyOtherServer = (opt)->
#   msg = {
#     tp:'insert',
#     ts: new Date(),
#     base:opt.base,
#     fldr:opt.fldr,
#     fnm:opt.fnm,
#     fext:opt.fext,
#     serverNm:currentServerName
#   }
#   for server in serversList
#     if server.nm is currentServerName
#       continue
#     tmp = {host:server.domain}
#     helpers.http_post tmp, msg, (err,ret)->
#       if err
#         console.error err
#         #TODO: log server sync fail?
#         return
#       console.log 'MSG: server: '+server.nm+'sync user Img success'

#verify if post is from one of the servers
# verifyReqSource = (req,cb)->
#   console.log req.host


# handleOtherServeSyncReq = (req,resp)->
#   error = (msg)->
#     resp.send {ok:0, msg:msg}
#   verifyReqSource req, (err)->
#     if err
#       console.error err
#       return error(err)
#     tp = req.params 'tp'
#     #TODO: download file from s3
#     if tp is 'insert'
#     else if tp is 'delete'

# recoredUserInsertDelAction = (opt,cb)->
#   record = {
#     tp:opt.tp #del/insert
#     ts:new Date()
#   }
#   FileActions.insertOne record,{},(err,ret)->


# POST 'signS3',(req,resp)->
#   findError = (e)->
#     console.error e
#     resp.send {e:e.toString()}
#   appAuth req,resp,null,(user)->
#     if not user then return findError "Unauthorized"
#     # get/replace
#     console.log b = req.body
#     switch b.tp
#       when 'avator'
#         fileName = 'A.jpg'
#         file = {i:'A',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
#       else
#         fileName = 'B.jpg'
#         file = {i:'B',e:'jpg',w:b.w,h:b.h,s:b.s,ts:new Date()}
#     # avator use 'A' as default, no need to reserve files
#     reserveUserFile user,0,(err,userFile,fileNames)->
#       if err then return findError err
#       # make sure folder
#       folderPath = FILE_LOCAL_FOLDER_BASE + userFile.fld + '/'
#       bucket = s3config.bucket or s3config.params.Bucket
#       s3_params =
#         Bucket: bucket
#         Key: folderPath + fileName
#         Expires: 60
#         ContentType: b.type
#         ACL: 'public-read'
#       console.log s3_params
#       s3.getSignedUrl 'putObject', s3_params, (err, data)->
#         if err then return findError err
#         ret =
#           sgnd: data
#           s3url: 'https://' + bucket + '.s3.amazonaws.com' + '/' + folderPath + fileName
#           url: FILE_WEB_BASE_DOMAIN + folderPath + fileName
#           key: s3_params.Key
#         upsertUserFile userFile,[file],(err)->
#           if err then return findError err
#           resp.send ret
# POST 'uploadImg',(req,resp)->
#   findError = (e)->
#     console.error e
#     resp.send {e:e.toString()}
#   appAuth req,resp,null,(user)->
#     if not user then return findError "Unauthorized"
#     # get/replace
#     try
#       console.log payload = JSON.parse req.body.payload
#     catch e
#       return findError e
#     if not (upfiles = req.files)
#       return findError "No File Found"
#     console.log '+++++MSG: start'
#     filesCount = 1#TODO: support multi upload
#     reserveUserFile user,filesCount,(err,userFile,fileNames)->
#       if err then return findError err
#       # make sure folder
#       folderPath = FILE_LOCAL_FOLDER_BASE + userFile.fld
#       bucket = s3config.bucket or s3config.params.Bucket
#       fileExt = payload.ext or 'jpg'
#       console.log fileNames
#       fileDriver.createFolder folderPath,(err)->
#         if err then return findError err
#         thumbKey = folderPath + '/' + fileNames[0]  + '_' + "." + fileExt
#         key      = folderPath + '/' + fileNames[0] + "." + fileExt
#         contentType = mime.lookup fileExt,'image/jpeg'
#         now   = new Date()
#         f = upfiles.file
#         f.name = fileNames[0] + '.' + fileExt
#         console.log key
#         fileDriver.uploadFiles [f],folderPath,(err,uploaded)->
#           if err then return findError err
#           console.log uploaded
#           console.log '###'
#           file = {}
#           file.w  = payload.w or 128
#           file.h  = payload.h or 128
#           file.s  = payload.s or 0
#           file.ts = now
#           file.i  = fileNames[0]
#           file.t  = payload.isThumb or false
#           file.e  = fileExt
#           # console.log isThumb is false
#           console.log file
#           upsertUserFile userFile,file,(err)->
#             if err then return findError err
#             avtUrl = userFile.base + userFile.fld + '/' + file.i + '.' + file.e + '?v=' + Date.now().toString().slice(-6)
#             resp.send {sUrl:avtUrl}


  # fileDriver.createFolder folderPath,(err)->
  #   if err then return findError err
  #   f = upfiles.file
  #   f.name = file.i + '.' + file.e
  #   fileDriver.uploadFiles f,folderPath,(err,uploaded)->
  #     if err then return findError err
  #     console.log uploaded


  HMAC_SHA256 = (key,str,encode)->
    crypto.createHmac('sha256', key).update(str).digest(encode)

  _savedSigningDate = null
  _savedSigningKey = null
  getS3SigningKey = (date)->
    return _savedSigningKey if date is _savedSigningDate
    DateKey = HMAC_SHA256 ("AWS4" + s3config.secretAccessKey),date
    DateRegionKey = HMAC_SHA256 DateKey, s3config.region
    DateRegionServiceKey = HMAC_SHA256 DateRegionKey, 's3'
    _savedSigningDate = date
    return _savedSigningKey = HMAC_SHA256 DateRegionServiceKey,'aws4_request'

  # GET 's3test',(req,resp)->
  #   #if (bucket = req.param('bucket')) and (key = req.param('key'))
  #     # TODO: confirm file existance, or remove entity?
  #   appAuth req,resp,'/1.5/user/login',(user)->
  #     ctx =
  #       s3key:s3config.accessKeyId
  #       s3region:s3config.region
  #       s3date: (new Date()).toISOString().replace(/\-|\:|(\.\d{3})/g,'')
  #       s3bucket: s3config.params.Bucket
  #       user:user
  #     resp.ckup 's3test',ctx

  #retired, upload to server directly and then server to s3 and sync
  # POST 's3sign',(req,resp)->
  #   findError = (e)->
  #     console.error e
  #     resp.send {e:e.toString()}
  #   appAuth req,resp,null,(user)->
  #     return findError 'This api no longer supported'
  #     # this feature require user object
  #     unless user then return findError "Unauthorized"
  #     # avator use 'A' as default, no need to reserve files
  #     getPolicy2 = (exp, key, contentType, bucket, credential, date)->
  #       return {
  #           expiration: exp
  #           conditions:[
  #             {acl:'public-read'}
  #             {key: key}
  #             {'Content-Type':contentType}
  #             {bucket: bucket}
  #             # {success_action_redirect: success_action_redirect_url}
  #             # {success_action_status: 200}
  #             {'x-amz-algorithm':'AWS4-HMAC-SHA256'}
  #             {'x-amz-credential':credential}
  #             {'x-amz-date':date}
  #           ]
  #         }
  #     getPolicy = (exp, key, contentType, bucket, credential, date)->
  #       return {
  #           expiration: exp
  #           conditions:[
  #             {bucket: bucket}
  #             ["starts-with", "$key", key],
  #             {acl:'public-read'}
  #             # {success_action_redirect: success_action_redirect_url}
  #             ["starts-with", "$Content-Type", contentType],
  #             {"x-amz-meta-uuid": "14365123651274"},
  #             {"x-amz-server-side-encryption": "AES256"},
  #             ["starts-with", "$x-amz-meta-tag", ""],
  #             # {success_action_status: 200}
  #             {'x-amz-credential':credential}
  #             {'x-amz-algorithm':'AWS4-HMAC-SHA256'}
  #             {'x-amz-date':date}
  #           ]
  #         }
  #     reserveUserFile user,1,(err,userFile,fileNames)->
  #       if err then return findError err
  #       # make sure folder
  #       folderPath = FILE_LOCAL_FOLDER_BASE + userFile.fld
  #       bucket = s3config.bucket or s3config.params.Bucket
  #       fileExt = req.param "ext",'jpg'
  #       rurl    = req.param 'rurl'
  #       s3fileDriver.createFolder folderPath,(err)->
  #         thumbKey = folderPath + '/' + fileNames[0]  + '_' + "." + fileExt
  #         key      = folderPath + '/' + fileNames[0] + "." + fileExt
  #         contentType = mime.lookup fileExt,'image/jpeg'
  #         now   = new Date()
  #         exp   = new Date(now.getTime() + 60 * 1000) # 1 min
  #         date  = now.toISOString().replace(/\-|\:|\.\d{3}/g,'')
  #         date8 = date.substr(0,8)
  #         credential = "#{s3config.accessKeyId}/#{date8}/#{s3config.region}/s3/aws4_request"
  #         success_action_redirect_url = rurl or req.fullUrl('/1.5/s3test')
  #         policy      = getPolicy(exp.toISOString(), key,      contentType, s3fileDriver.cfg.params.Bucket, credential, date)
  #         thumbPolicy = getPolicy(exp.toISOString(), thumbKey, contentType, s3fileDriver.cfg.params.Bucket, credential, date)
  #         if rurl
  #           policy.conditions.push({success_action_redirect: success_action_redirect_url})
  #         if process.version >= 'v6'
  #           stringToSignBuf = Buffer.from(JSON.stringify(policy))
  #         else
  #           stringToSignBuf = new Buffer(JSON.stringify(policy))
  #         stringToSign = stringToSignBuf.toString('base64')
  #         signature = HMAC_SHA256 getS3SigningKey(date8),stringToSign,'hex'
  #         thumbPolicySign = new Buffer(JSON.stringify(thumbPolicy)).toString('base64')
  #         thumbSignature = HMAC_SHA256 getS3SigningKey(date8),thumbPolicySign,'hex'
  #         ret =
  #           thumbPolicy:   thumbPolicySign
  #           thumbKey:      thumbKey
  #           thumbSignature:thumbSignature
  #           policy:  stringToSign
  #           key:     key
  #           date:    date
  #           credential: credential
  #           signature:  signature
  #           success_action_redirect : success_action_redirect_url
  #           success_action_status : 200
  #           contentType:contentType
  #           s3bucket: s3config.params.Bucket
  #         #TODO: get file w,h,s,i,ts,t(thumb)
  #         file = {}
  #         file.w  = req.param "w", 128
  #         file.h  = req.param "h", 128
  #         file.s  = req.param "s", 0
  #         file.ts = now
  #         file.i  = fileNames[0]
  #         file.t  = req.param "t", '0'
  #         file.e  = fileExt
  #         # console.log isThumb
  #         # console.log isThumb is false
  #         console.log file
  #         if file? #TODO: create file obj
  #           upsertUserFile userFile,[file],(err)->
  #             if err then return findError err
  #             resp.send ret
  #         else
  #           resp.send ret


  # VIEW 's3test',->
  #   text "#{@user?._id}"
  #   #form id:'s3form',action:"http://#{@s3bucket}.s3.amazonaws.com/",method:"POST",enctype:"multipart/form-data",-> #-website-us-west-2
  #   form id:'s3form',action:"#{@req.getProtocol()}://#{@s3bucket}.s3.amazonaws.com/",method:"POST",enctype:"multipart/form-data",-> #-website-us-west-2
  #     input type:'hidden',name:'acl',value:'public-read'
  #     input type:'hidden',name:'key',               id:'s3-key',value:'test.jpg'
  #     input type:'hidden',name:'policy',            id:"s3-policy",value:''
  #     input type:'hidden',name:'x-amz-credential',  id:'s3-credential',value:"#{@s3key}/#{@s3date.substr(0,8)}/#{@s3region}/s3/aws4_request"
  #     input type:'hidden',name:'x-amz-date',        id:'s3-date',value:@s3date
  #     input type:'hidden',name:'x-amz-signature',   id:'s3-signature',value:""
  #     input type:'hidden',name:'success_action_redirect',value:@req.fullUrl('/1.5/s3test')
  #     input type:'hidden',name:'x-amz-algorithm',value:'AWS4-HMAC-SHA256'
  #     input type:'file',name:'file'
  #   div onclick:"upload();",-> _ "Upload"
  #   coffeejs ->
  #     upload = ->
  #       debug = (e)-> console.log e
  #       # call get ajax, with ext
  #       xmlhttp = new XMLHttpRequest()
  #       xmlhttp.onreadystatechange = ->
  #         if (xmlhttp.readyState is XMLHttpRequest.DONE) and (xmlhttp.status is 200)
  #           resp = JSON.parse xmlhttp.responseText
  #           debug resp
  #           sendFile resp
  #         else
  #           debug xmlhttp.status
  #       xmlhttp.open("POST","/1.5/s3sign",true)
  #       xmlhttp.setRequestHeader("Content-type","application/x-www-form-urlencoded")
  #       xmlhttp.send("ext=jpg")
  #       sendFile = (r)->
  #         # set all fields
  #         for k,v of r
  #           document.getElementById("s3-#{k}").value = v
  #         # submit
  #         document.getElementById("s3form").submit()
  #     null