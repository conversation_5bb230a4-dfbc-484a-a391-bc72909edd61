
# #NOTE: this file is not used promote
# # config = CONFIG()
# UserListings = COLLECTION 'chome','user_listing'
# # SysData = COLLECTION 'chome','sysdata'
# # debug = (m)-> console.log m
# # helpers = INCLUDE 'lib.helpers'

# # prepSocket = DEF 'prepSocket'
# UserModel = MODEL 'User'
# # isAllowed = DEF '_access_allowed'
# getExchangeRates = DEF 'getExchangeRates'
# getExchangeNames = DEF 'getExchangeNames'
# getGoogleAPILink = DEF 'getGoogleAPILink'


# APP '1.5'
# # isRealtor = DEF 'isRealtor'
# getRMListings = DEF 'getRMListings'
# POST 'market/api', (req, resp)->
#   req.setupL10n()
#   verifyListings = (data, cb)->
#     #data is array of listing ids
#     update = {$set:{}}
#     data.to = "market"
#     update.$set['mt'] = new Date()
#     # update.$set[data.to + '.st'] = "Terminated"
#     update.$set['ver'] = true
#     msg = 'Verify success: '
#     if data.ver
#       update.$set['ver'] = false
#       msg = 'Unverify success: '
#     UserListings.updateMany { id: data.id }, update ,{w:1,multi:1}, (err,ret)->
#       return cb err.toString() if err
#       return cb "Error: no listing found" unless ret.result.ok
#       cb null, msg + data.id

#   terminateListings = (data, cb)->
#     #data is array of listing ids
#     update = {$set:{}}
#     data.to = "market"
#     update.$set['mt'] = new Date()
#     # update.$set[data.to + '.st'] = "Terminated"
#     update.$set['status'] = "Terminated"
#     UserListings.updateMany { id: {$in:data} }, update ,{w:1,multi:1}, (err,ret)->
#       return cb err.toString() if err
#       return cb "Error: no listing found" unless ret.result.ok
#       cb null, 'Ternimate success: ' + data.toString()

#   UserModel.appAuth {req,resp},(user)->
#     error = (err, url)->
#       resp.send {ok:0, err:err, url:url}

#     return error('Please Login', '/1.5/user/login') unless user
#     # return error('Not Realtor', '/1.5/verify') unless isAllowed 'wecardRltrMnu',user

#     # cmd:'string', data:{}
#     return error('No command') unless cmd = req.param 'cmd'
#     data = req.param 'data'

#     if cmd is 'pmt-p-lst'
#       data.marketListPage = true
#       getRMListings(req, user, data, (ret)->
#         return resp.send ret
#       )
#     else if cmd is 'pmt-p-exrt'
#       rates =
#         exrates:getExchangeRates()
#         exnames:getExchangeNames()
#       for k, v of rates.exnames
#         rates.exnames[k] = req._ v
#       return resp.send rates
#     else if cmd is 'pmt-p-verify'
#       return unless data.id
#       return unless req.isAllowed 'marketAdmin'
#       verifyListings data, (err, ret)->
#         if err
#           return resp.send {ok:0, cmd:cmd, err:err.toString()}
#         else
#           return resp.send {ok:1, msg:ret}
#       return
#     else if cmd is 'pmt-p-temint'
#       return unless data.id
#       return unless data.id.length
#       return unless req.isAllowed 'marketAdmin'
#       terminateListings data.id, (err, ret)->
#         if err
#           return resp.send {ok:0, cmd:cmd, err:err.toString()}
#         else
#           return resp.send {ok:1, msg:ret}
#       return
#     # else if cmd is 'pmt-p-pDtl'
#     #   findErr = (err)->
#     #     ret.err = err
#     #     return resp.send ret
#     #   ret =
#     #     ok:0
#     #   return findErr "Error: no id" unless data.id
#     #   UserListings.findOne {id:data.id}, (err,prop)->
#     #     return findErr err if err
#     #     return findErr "Error: no listing found" unless prop
#     #     ret.ok = 1
#     #     #translate fields to locale for display
#     #     ret.prop = translate_rmprop_detail(req, prop)

#     #     delete ret.prop.market
#     #     delete ret.prop['58']
#     #     delete ret.prop.uhouzz
#     #     ret.cmd = cmd
#     #     return resp.send ret
#     else
#       return error('Unkown Command')


# GET 'promote/market', (req,resp)->
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
#     # allow logged in user to see
#     # unless req.hasRole 'realtor'
#     #   return resp.ckup "generalError", {err:"This feature is available to realtors only"}
#     type = "market"#(req.param "type") or "mylisting"
#     ml_num = req.param 'ml_num'
#     promoteTo =  req.param 'to' if ml_num #only works if has ml_num
#     #TODO: support ltype
#     unless promoteTo in ['58','uhouzz','market']
#       promoteTo = null
#     cfg = {
#       # tml:req.l10n('My Listing'),
#       # tlm:req.l10n('Listing Market'),
#       user:user,
#       type:type,
#       ltype:req.param 'ltype'
#       ml_num:ml_num
#       to : promoteTo
#       propTypes: [
#         {k:req.l10n('Exclusive Listing'),v:'exlisting'}
#         {k:req.l10n('Assignment'),v:'assignment'}
#         {k:req.l10n('Rental'),v:'rent'}
#       ]
#       gmap_url:getGoogleAPILink(req,{tp:'googleapi',cn:req.isChinaIP(), lang:'en',src:'mrkt'})
#       gmap_static: getGoogleAPILink(req,{tp:'staticMap',cn:req.isChinaIP(),src:'mrkt'})
#     }
#     if req.hasRole 'realtor', user
#       cfg.propTypes.push {k:req.l10n('MLS Rental'),v:'mlsrent'}
#       cfg.propTypes.push {k:req.l10n('MLS Listing'),v:'mlslisting'}
#     #TODO: check if listing already exists?
#     resp.ckup 'market-page', cfg


# VIEW "mylisting-filter-modal", ->
#   div id:'filterModal', class:'modal  ', style:'z-index:20;', ->
#     header class: 'bar bar-nav', ->
#       a class: 'icon icon-close pull-right nobusy', ngClick:"toggleModal('filterModal');", href: 'javascript:void 0'
#       h1 class: 'title',->
#         text _ "Filter"
#     div class:'bar bar-standard bar-footer' ,  ->
#       # a class: 'icon fa fa-plus ', ngClick:"toggleModal('newWecardModal')"
#       button class:"btn btn-half btn-fill btn-split", ngClick:"applyFilter()", ->
#         text _ "Filter"
#       button class:"btn btn-half btn-fill btn-split", ngClick:"clearFilter()", ->
#         text _ "Clear"
#     div class: 'content', style:"background-color: white; padding-bottom: 0;",->
#       div class:"fields", ->
#         div class:'row', ngRepeat:"t in propTypes", ngClick:"setFilter($event, 'tp', t)", ->
#           div class:'field-name', ->
#             label ngClass:"", ->
#               text  "{{t.k}}"
#           div class:'field-input',  ->
#             i class:"pull-right fa ", style:"", ngClass:"{'fa-circle-thin': !(filtData['tp'] && filtData['tp'] == t.v), 'fa-dot-circle-o active': filtData['tp'] && filtData['tp'] == t.v }"


# # promote page, includes leads, mylisting, listing market
# # use lazy initiallzation
# # need to modify leads
# VIEW 'market-page',->
#   css '/css/angular-datepicker.min.css'
#   div id:'realtorUtilPage', ngApp:'listingApp', scroll:'', ngController:'listingCtrl', class:'',-> #ngApp
#     div id:"schoolDetailModal", class:"modal", ->
#       text ckup "schoolDetailPane", {fromList:1}
#     text ckup 'shareDialog2', {page:'market', req:@req}
#     div id:'propDetailModal', class:'modal ', style:'z-index:12;', ->
#       text ckup "prop-detail-pane", {fromMarket:1}
#     text ckup "brkg-info-modal"
#     text ckup "busy-icon", {}
#     text ckup "mylisting-filter-modal"
#     text ckup 'flashMessage',{id:'server-error',msg:_("Server Error")}
#     # ckup views here

#     div class:'backdrop ng-cloak', ngHide:'hideBackdrop'#, ngClick:'hideBackdrop = true'

#     header class: 'bar bar-nav', ->
#       # text ckup 'headerbarUserModalBtn'
#       h1 class: 'title',-> text _ "RealMaster Listing Market"
#       a href:'/1.5/index', class:'icon fa fa-back pull-left'
#       a href:'javascript:;', class:'icon fa fa-filter pull-right', style:'', ngClick:"toggleModal('filterModal')"
#     # nav class:'bar bar-standard bar-header-secondary',->
#     #   div class:'segmented-control scrollable ',->
#     #     if @req.isAllowed('shareSign')
#     #       a class:'control-item ', href:'/1.5/crm/leads', -> text _ "Leads"
#     #     a class:'control-item ', href:'/1.5/promote/mylisting', ->#ngClick:"loadListings('mylisting', 0)", ngClass:"{'active': type == 'mylisting'}",->
#     #       text _ "MyListing"
#     #     a class:'control-item active', href:'#', ->#ngClick:"loadListings('market', 0)", ngClass:"{'active': type == 'market'}",->
#     #       text _ "Listing Market"
#       bclass = 'bar bar-standard bar-footer list-footer'
#       if @req.isAllowed 'marketAdmin'
#         bclass += ' admin'
#       div class: bclass, id:"newWecardBar", ->
#         if @req.isAllowed 'marketAdmin'
#           a class:"fa fa-trash",href:"#", ngClick:"terminateListings()"
#           a class:"share", href:"#", ngClick:"shareListings()", ->
#             span class:"", ->
#               text _ "Share"
#         # a class:"pull-right", href:"#",  ngClick:"toggleModal('filterModal')",->
#         #   span class:"icon fa fa-filter", style:"color: #2f3e46;"
#         #   span class:"tab-label", ->
#         #     text _ "filter"
#         a class:'btn btn-positive btn-half btn-sharp btn-fill', href:"/1.5/promote/mylisting?action=showSMB",->
#           text _ "Post Listing"
#         a class:'btn btn-default btn-half btn-sharp btn-fill', href:"/1.5/promote/mylisting",->
#           text _ "My Listings"
#         # a class:"pull-left create", href:"", ->
#         #   div ->
#         #     text _ ""
#         # span class:"pull-left split"
#         # a class:"pull-left create", href:"", ->
#         #   div ->
#         #     text _ ""

#     div class:'WSBridge',style:'display:none',->
#       span id:'m-share-title',    ->
#         text _ "RealMaster Exclusive-Listing Market Report",'property' #房大师独家-房源市场报告
#       span id:'m-share-title-en', ->
#         text "RealMaster Exclusive-Listing Market Report"
#       timeNow = new Date()
#       formatDate = timeNow.getFullYear() + '.' + (timeNow.getMonth() + 1) + '.' + timeNow.getDate()
#       span id:'m-share-desc',     ->
#         text "#{formatDate} #{_('Total {{selectedProp.length}} Exclusive Listings, Assignments, Rental Listings and MLS Promoted Listings.','property')}"#个独家房源, 楼花, 出租房源和MLS推广房源
#       span id:'m-share-desc-en',  ->
#         text "#{formatDate} Total {{selectedProp.length}} Exclusive Listings, Assignments, Rental Listings and MLS Promoted Listings."

#       text ckup "listing-share-desc"

#       user = @req.session.get('user')
#       span id:'share-data',->
#         data_text = "id={{prop.id}}&tp=mylisting&lang=#{@req.locale()}"
#         if user?.id and @req.isAllowed 'shareSign'
#           data_text += "{{wSign ? '&aid=#{@req.getShareUID(user)}':''}}{{wDl ? '&wDl=1':''}}"
#         else
#           data_text += "&wDl=1"
#           if @owner or user
#             data_text += "&uid=#{@req.getShareUID(@owner or user)}"
#         text data_text

#       span id:'share-url',->
#         # link_url = "http://#{@req.host}/html/propDetailPage.html?id={{prop.id}}&lang=#{@req.locale()}" #selectedProp.length? selectedProp.toString()
#         link_url = "http://#{@req.host}/1.5/prop/detail?id={{prop.id}}&lang=#{@req.locale()}" #selectedProp.length? selectedProp.toString()
#         if user?.id and @req.isAllowed 'shareSign'
#           link_url += "{{wDl ? '&wDl=1':''}}{{wSign ? '&aid=#{user._id or user.id}':''}}"
#         else
#           link_url += "&wDl=1"
#           if @owner or user
#             link_url += "&aid=#{@req.getShareUID(@owner or user)}"
#         text link_url

#       span id:'m-share-data', ->
#         data_text = "id={{selectedProp.join(',')}}&tp=listingList&lang=#{@req.locale()}"
#         if user?.id and @req.isAllowed 'shareSign'
#           data_text += "{{wSign ? '&aid=#{@req.getShareUID(user)}':''}}{{wDl ? '&wDl=1':''}}"
#         else
#           data_text += "&wDl=1"
#           if @owner or user
#             data_text += "&uid=#{@req.getShareUID(@owner or user)}"
#         text data_text

#       span id:'m-share-url',->
#         link_url = "http://#{@req.host}/1.5/search/prop?id={{selectedProp.join(',')}}&lang=#{@req.locale()}&share=1"
#         if @req.isAllowed 'shareSign'
#           link_url += "{{wSign ? '&aid=#{@req.getShareUID(user)}':''}}{{wDl ? '&wDl=1':''}}"
#         else
#           link_url += "&aid=#{@req.getShareUID(user)}&wDl=1"
#         text link_url

#       span id:'m-share-image',->
#         text "http://#{@req.host}/img/create_exlisting.png"

#       span id:'share-image',->
#         text "{{picUrls.length > 0 ? picUrls[0] : 'http://#{@req.host}/img/create_exlisting.png'}}"
#       if @singleView #have this in list result
#         span id:'wx-url',->
#           text @req.fullUrl()

#     div class: 'content', style:"padding-bottom:55px;", ->
#       div class:'content-list', ->
#         div class:'listing-wrapper ng-cloak',  ngIf:"type === 'market'", ngRepeat:"prop in marketListings",->
#           div class:'card market-listing-card',->
#             div class:'status-wrapper', ngClass:"{'active':prop.status=='Active'}",->
#               span ngShow:"prop.status=='Active'",->
#                 span ngIf:"prop.ltp == 'exlisting'", -> _ "Exclusive","listingType"
#                 span ngIf:"prop.ltp == 'assignment'", -> _ "Assignment","listingType"
#                 span ngIf:"prop.ltp == 'rent'", -> _ "Rental","listingType"
#                 span ngIf:"prop.ltp == 'mlslisting'", -> _ "MLS Listing","listingType"
#                 span ngIf:"prop.ltp == 'mlsrent'", -> _ "MLS Rental","listingType"
#                 br()
#                 span class:"date", style:"font-size:11px",->
#                   text "{{prop.mt | date: 'yyyy.MM.dd'}}"
#                 # span class:"status",  ngClass:"{'active':prop.status=='Active' || prop.status=='Inactive'}", ->
#                 #   text "{{prop.status}}"
#               span ngHide:"prop.status=='Active'", style:"font-size: 14px;",->
#                 # span class:"status",  ngClass:"{'active':prop.status=='Active' || prop.status=='Inactive'}", ->
#                 text "{{prop.status}}"
#             if @req.isAllowed 'marketAdmin'
#               a class:"fa verify-btn",ngClass:"{'fa-verify':prop.ver, 'fa-verify-o':!prop.ver}", href:"#", ngClick:"verifyListings(prop)"
#               div class:"share-selector", style:"", ngClick:"toggleSelect(prop.id)",->
#                 # input type:"checkbox"
#                 i class:"fa ", style:"", ngClass:"{'fa-square-o': selectedProp.indexOf(prop.id) < 0, 'fa-check-square-o active': selectedProp.indexOf(prop.id) >= 0 }"


#             div class:'card-content prop-content',->
#               # div class:"tri-wrapper",->
#               #   div class:"triangle", ngClass:"{'active':prop.status=='Active' || prop.status=='Inactive'}", ->
#               #     div class:"status", ->
#               #       text "{{prop.status}}"

#               div class:'img-wrapper', ngClick:"viewListing(prop)",->
#                 img ngSrc:"{{prop.img || '/img/noPic.png' }} "
#               div class:'detail-wrapper', ngClick:"viewListing(prop)",->
#                 div ->
#                   span class:'addr', ngShow:"!(prop.daddr == 'N')",->
#                     text "{{prop.unt}} {{prop.addr?prop.addr+',':''}} {{prop.city?prop.city+',':''}} {{prop.prov}} "
#                   span class:'addr', ngShow:"prop.daddr == 'N'",->
#                     text "{{prop.city?prop.city+',':''}} {{prop.prov}} {{prop.zip}}"
#                 div ->
#                   span class:'', style:"font-size: 17px;",->
#                     text "{{prop.lp | currency:\'$\':0}}"
#                     text " for "
#                     text "{{prop.stp}}"
#                 div ->
#                   # text "{{prop.ptp}} {{prop.pstyl}} "
#                   text _ "Bedrm"
#                   text " {{prop.bdrms}}{{prop.br_plus?'+'+prop.br_plus:''}} "
#                   text _ "washrm"
#                   text " {{prop.bthrms}}"
#                   # br()
#                   # text "ID: {{prop.id}}"
#                 # div class:'',->
#                 #   p class:'pull-left', style:"margin-bottom: 0",->
#                 #     text _ 'Comm'
#                 #     text " : "
#                 #     text "{{prop['market'].cmstn}}"

#             div class:"agent-wrapper", ->
#               div ->
#                 img src:"/img/logo.png", ngSrc:"{{prop.avt}}", fallbackSrc:"/img/logo.png", \
#                   ngClick:"openTBrowser('/1.5/wesite/' + prop.uid + '?inFrame=1')"
#               div class:"agent", ->
#                 div class:"link", ->
#                   a href:"#", ngClick:"openTBrowser('/1.5/wesite/' + prop.uid + '?inFrame=1')", ->
#                     text "{{prop.fnm ? prop.fnm : prop.ln + ' ' + prop.fn}}"
#                     img src:"/img/realtor.png", ngShow:'prop.realtor'
#                     img src:"/img/vip.png", ngShow:'prop.vip'
#                   span class:"pull-right", style:"font-size:14px; padding-right: 7px;", ngShow:"prop['market'].cmstn", ->
#                     text _ "Comm: ",'RealtorCommission'
#                     span style:"color:#E03131", ->
#                       text "{{prop['market'].cmstn | limitTo:12}}"
#                   br()
#                   span class:"pull-right", style:"font-size:11px; padding-right: 7px;", ngShow:"prop['market'].adok", ->
#                     text _ "AD. OK"
#                 div class:"cpny", ->
#                   p style:"font-size:11px; ", ->
#                     text "{{prop.cpny}}"

#         div class:'spinner-wrapper', style:" height: 40px; padding: 10px;", ngShow:"loading",->
#           div class:"pull-spinner ", ngShow:"loading"
#         # button class:"btn btn-block btn-long btn-positive", ngClick:"loadMore()", style:"margin-top:10px;", ngShow:"hasMore",-> _ "Load More"

#   # js '/socket/socket.io.js'
#   ###
#   js '/js/angular-touch.min.js'
#   ###
#   # tlm = title listing market
#   # tml = title my listings
#   em_url = @req.exMapURL()
#   js @gmap_url
#   js '/js/proplib.min.js'
#   js '/js/lz-string.min.js' #minify data to be posted
#   js '/js/preventDefault.min.js'
#   coffeejs { vars:{
#     RealMaster:@req.l10n('RealMaster'),
#     gmap_static:@gmap_static,
#     emurl:em_url,
#     type:@type,
#     ltype:@ltype,
#     s3bucket:@s3bucket,
#     gurl:@gmap_url,
#     ml_num:@ml_num,
#     to:@to,
#     lang: @req.locale(),
#     vipTipStr: @req.l10n("Available only for Premium VIP user! Upgrade and get more advanced features.")
#     vipLaterStr: @req.l10n('Later')
#     vipSeeStr: @req.l10n('See More')
#     isVipUser:@req.isAllowed('vipUser')
#     propTypes : @propTypes,
#     isApp:@isApp}},->
#     # necessary to do this?
#     # window.name = "NG_DEFER_BOOTSTRAP!"
#     document.getElementById('busy-icon').style.display = 'block'
#     RMSrv.onReady ->
#       RMSrv.clearCache()
#       RMSrv.enableBackButton false
#     gmap = null
#     mapLoaded = false

#     #un-comment this if in pc
#     # setTimeout (->
#     #   unless angular.element(document).scope()
#     #     angular.resumeBootstrap()
#     #   return
#     # ), 1000
#     # if RMSrv?.getKeyboard #http://stackoverflow.com/questions/25668958/angular-is-missing-resumebootstrap-function
#     #   RMSrv.getKeyboard (k)->
#     #     setTimeout (->
#     #       angular?.resumeBootstrap()
#     #     ),100
#     # else
#     #   setTimeout (->
#     #     #angular may not inited
#     #     angular?.resumeBootstrap()
#     #     return
#     #   ), 500

#     app = angular.module('listingApp',[]) #'ngTouch''ng', 'datePicker'
#     listingCtrl = ($scope, $http, $timeout, proplibService)->
#       # socket = io '/market-page',{path:'/socket'}
#       $scope.type = vars.type
#       # $scope.title = vars.tml
#       # if vars.type isnt 'mylisting'
#       #   $scope.title = vars.tlm
#       $scope.propTypes = vars.propTypes or {}
#       $scope.hideBackdrop = true
#       $scope.isArray = angular.isArray
#       $scope.filtData = {}
#       if vars.ltype
#         $scope.filtData.tp = vars.ltype

#       $scope.ml_num = vars.ml_num
#       $scope.roomNumbers = [1..12]
#       $scope.hasMore = false
#       $scope.wSign = true
#       $scope.wDl = true
#       $scope.curBrkg = {} #display brkg info
#       $scope.selectedProp = [] #select array of id for deletion
#       $scope.toShare = 1

#       $scope.getImgEncodeUrl = ()->
#         return null unless $scope.picUrls
#         return encodeURIComponent($scope.picUrls[0]) if $scope.picUrls[0]
#         null

#       $scope.toggleModal = (a, b)->
#         toggleModal(a, b)
#       $scope.showInBrowser = (url)->
#         RMSrv.showInBrowser(url)

#       $scope.showBrkgInfo = ->
#         toggleModal('brkgInfoModal');
#         $scope.hideBackdrop = false

#       $scope.setFilter = (e, tag, v) ->
#         $scope.filtData[tag] = v.v
#         e.preventDefault()

#       $scope.clearFilter = ()->
#         $scope.filtData = {}
#         $scope.applyFilter()

#       $scope.applyFilter = ()->
#         # console.log $scope.filtData
#         $scope.loading = true
#         $scope.loadListings vars.type, 0, $scope.filtData.tp
#         toggleModal 'filterModal'

#       $scope.toggleSelect = (id)->
#         index = $scope.selectedProp.indexOf(id)
#         if index < 0
#           $scope.selectedProp.push id
#         else
#           $scope.selectedProp.splice index,1
#         return

#       $scope.iShareShare = () ->
#         unless vars.isVipUser
#           # return flashMessage('vip-only')
#           return $scope.dialogAlertVip()
#         $scope.hideBackdrop=true
#         toggleModal('savePromoteModal','close')
#         RMSrv.share('hide')
#         RMSrv.shareLang()
#         RMSrv.share('i-share',null)

#       $scope.dialogAlertVip = ()=>
#         tip = vars.vipTipStr or "Available only for Premium VIP user! Upgrade and get more advanced features."
#         later = vars.vipLaterStr or 'Later'
#         seemore = vars.vipSeeStr or 'See More'
#         _doShowVip = (idx)=>
#           url = '/event?tpl=getvip'
#           if vars.lang
#             url += "&lang=" + vars.lang
#           RMSrv.showInBrowser(url) if idx+'' is '2'
#         return RMSrv.dialogConfirm(tip, _doShowVip, "VIP", [later, seemore])
#       $scope.verifyListings = (prop)->
#         _doTerminate = (r)->
#           if r is 2
#             $http.post('/1.5/market/api', {cmd:'pmt-p-verify', data:{id:prop.id, ver:prop.ver}})
#             .success (data)->
#               if data.ok
#                 RMSrv.dialogAlert data.msg
#                 $scope.loadListings $scope.type, 0
#               else
#                 RMSrv.dialogAlert data.err
#               return
#             .error (data)->
#               RMSrv.dialogAlert(data.toString())
#           else
#             console.log 'cancled terminate'
#           return
#         # console.log $scope.selectedProp
#         msg = "Are you sure? " + $scope.selectedProp.join ','
#         RMSrv.dialogConfirm msg,_doTerminate,"Message",['Cancel','Verify']
#         # _doTerminate(2)
#         return
#       $scope.terminateListings = ()->
#         _doTerminate = (r)->
#           if r is 2
#             $http.post('/1.5/market/api', {cmd:'pmt-p-temint', data:{id:$scope.selectedProp}})
#             .success (data)->
#               if data.ok
#                 $scope.selectedProp = []
#                 RMSrv.dialogAlert data.msg
#                 $scope.loadListings $scope.type, 0
#               else
#                 RMSrv.dialogAlert data.err
#               return
#             .error (data)->
#               RMSrv.dialogAlert(data.toString())
#           else
#             console.log 'cancled terminate'
#           return
#         # console.log $scope.selectedProp
#         msg = "Are you sure? " + $scope.selectedProp.join ','
#         RMSrv.dialogConfirm msg,_doTerminate,"Message",['Cancel','Terminate']
#         # _doTerminate(2)
#         return

#       $scope.shareListings = ()->
#         return unless $scope.selectedProp?.length
#         RMSrv.showSMB 'show','m-share-'
#         # RMSrv.share('show');
#         return

#       $scope.openTBrowser = (url)->
#         cfg = #mediaPlaybackRequiresUserAction:'yes',suppressesIncrementalRendering:'yes'
#           toolbar:
#             height: 44
#             color: '#E03131'
#           closeButton:
#             image: 'close'
#             imagePressed: 'close_pressed'
#             align: 'right'
#             event: 'closePressed'
#           fullscreen:false
#         # alert url
#         ref = RMSrv.openTBrowser(url, cfg)
#         ref.addEventListener cordova.ThemeableBrowser.EVT_ERR, (e)->
#           console.log e

#       $scope.exMap = ->
#         proplib.exMap($scope.prop, true)

#       $scope.height = (elem) -> # TODO: move to the only location where use it
#         elem = elem[0] or elem
#         if isNaN(elem.offsetHeight)
#           elem.document.documentElement.clientHeight
#         else
#           elem.offsetHeight

#       $scope.gotoTop = () ->
#         # TODO: goes to top
#         # body = document.querySelector("#propDetailModal .content")

#       $scope.previewPic = (e) ->
#         proplibService.previewPic(e, $scope)
#         return
#       $scope.translate_m = (prop) ->
#         $scope.translating = true
#         proplibService.translate_m prop, $scope, (ret)->
#           prop.m_zh = ret
#           $timeout ->
#             $scope.$apply()

#       $scope.showSchool = (id)->
#         proplibService.showSchool($scope, id)

#       $scope.viewListing = (prop) ->
#         RMSrv.clearCache()
#         $scope.setCurBrkg(prop)
#         toggleModal("propDetailModal")
#         $http.post('/1.5/rmprop/detail', {rmid:prop.id, t:1 })
#         .success (data)->
#           if data.ok is 1
#             # cts -> dom
#             if data.prop.pic
#               data.prop.pic.ml_num = data.prop.sid or data.prop.ml_num
#               $scope.picUrls = proplib.convert_rm_imgs($scope, data.prop.pic, 'reset')
#               $scope.sizes = []
#             # diff = (new Date() - new Date(data.prop['ts']))/(24*3600*1000)
#             # data.prop.dom = parseInt diff
#             data.prop.mt = new Date(data.prop.mt)
#             $scope.prop = data.prop
#             proplibService.loadMap($scope.prop)
#             proplibService.getSchoolsInfo($scope, $scope.prop) if $scope.prop.lat
#             $timeout (()->
#               $scope.setCurBrkg(data.prop)
#             ), 500
#             $scope.unit = "Ft"
#             $scope.lang = 'En'
#             $timeout ->
#               $scope.showPropM = if document.getElementsByClassName('prop_m')[0].clientHeight >= 60 then 0 else 1
#               $scope.$apply()
#           else
#             RMSrv.dialogAlert data.err
#         .error (data)->
#           RMSrv.dialogAlert data.toString()
#         unless $scope.exrates
#           proplibService.getExchangeRates($scope)

#       $scope.setCurBrkg = (prop)->
#         # index -> listing detail has no brkg info
#         if not prop.fnm and $scope.marketListings
#           # try find brkg info in listings
#           for i in $scope.marketListings
#             if i.id is prop.id
#               prop = i
#               break
#         else
#           return null
#         $scope.curBrkg = {
#           realtor : prop.realtor
#           _uid: prop._uid
#           fnm:  prop.fnm
#           fn:   prop.fn # TODO
#           ln:   prop.ln
#           cpny: prop.cpny
#           mbl:  prop.mbl
#           eml:  prop.eml
#           tel:  prop.tel
#           avt:  prop.avt
#         }
#       $scope.resetSlider = ->
#         resetSlider()

#       $scope.loadListings = (type, skip, ltype)->
#         # alert type
#         $scope.loading = true
#         $scope.type = type
#         # if type is 'mylisting'
#         #   $scope.title = vars.tml
#         # else
#         #   $scope.title = vars.tlm
#         #send listings command to server and get response
#         data = {type:type, ltype:ltype}
#         data.skip = skip or 0
#         $http.post('/1.5/market/api', {cmd:'pmt-p-lst', data:data })
#         .success (data)->
#           getFirstImg = (data)->
#             return unless data
#             for i in data.l
#               if i.pic
#                 i.pic.ml_num = i.sid or i.ml_num
#                 i.img = proplib.convert_rm_imgs($scope, i.pic, 'reset')[0]
#           # $scope.template = data.tpl
#           if data.ok is 1
#             #market has user list
#             if data.type is 'market'
#               fld = "marketListings"
#               userDict = {}
#               for user in data.ul
#                 userDict[user._id] = user
#               for prop in data.l
#                 if prop.flwng #use followed for this prop
#                   user = userDict[data.uid]
#                   prop.uid = data.uid
#                 else
#                   user = userDict[prop.uid]
#                 user ?= {}
#                 prop.fn   = user.fn
#                 prop._uid  = user._id
#                 prop.ln   = user.ln
#                 prop.avt  = user.avt
#                 prop.cpny = user.cpny
#                 prop.mbl  = user.mbl
#                 prop.eml  = user.eml
#                 prop.tel  = user.tel
#                 prop.fnm   = user.fnm
#                 prop.realtor = user.realtor
#                 prop.vip = user.vip
#             else
#               fld = "mylistings"
#           else
#             return console.log data.err
#           getFirstImg(data)
#           if data.skip is 0
#             $scope[fld] = data.l
#           else if $scope[fld]
#             $scope[fld] = $scope[fld].concat data.l
#           else
#             $scope[fld] = data.l
#           $scope.hasMore = if data.l.length >= 20 then  true else false
#           # remove loading
#           $scope.loading = false
#           document.getElementById('busy-icon').style.display = 'none'
#           $timeout ->
#             $scope.$apply()
#         .error (data)->
#           RMSrv.dialogAlert(data.toString())

#       $scope.loadMore = ()->
#         return unless $scope.hasMore
#         return if $scope.loading
#         fld =  if $scope.type is 'market' then "marketListings" else "mylistings"
#         skip = $scope[fld].length
#         $scope.loadListings($scope.type, skip, $scope.filtData.tp)

#       $scope.showSMB = ()->
#         RMSrv.share('show')

#       unless $scope.marketListings
#         $scope.loadListings vars.type, 0, $scope.filtData.tp

#       # show listing according to id
#       if $scope.ml_num and $scope.type is 'market'
#         #view anylisting without timeout
#         $scope.viewListing({id:$scope.ml_num})
#         # TODO: show brkg Info, view anylisting has no brkg info


#       null
#     app.controller 'listingCtrl', ['$scope', '$http', '$timeout', 'proplibService', listingCtrl ]

#     app.directive 'scroll',['$window', ($window) ->
#       (scope, element, attrs) ->
#         scrollables = document.querySelector("#realtorUtilPage > div.content")
#         angular.element(scrollables).bind 'scroll', ()->
#           frameHeight = scope.height( document.querySelector("#realtorUtilPage > div.content") ) - (44 + 50)
#           listHeight = scope.height(document.querySelector("#realtorUtilPage > div.content .content-list"))
#           # console.log  'list: ' + listHeight
#           # console.log "scrollTop: " + @scrollTop
#           # console.log "ls-fh-10: " + (listHeight - frameHeight - 10)
#           scope.nextLoadPosition = listHeight - frameHeight - 10
#           # console.log  'next:' + scope.nextLoadPosition
#           if @scrollTop > scope.nextLoadPosition
#             # console.log  'load more'
#             scope.loadMore()
#             # debug @pageYOffset
#           else
#             # scope.boolChangeClass = false
#             # debug 'current: ' + @pageYOffset
#             # debug scope.nextLoadPosition
#           scope.$apply()
#         return
#       ]
#     app.directive 'fallbackSrc', ->
#       fallbackSrc = link: (scope, iElement, iAttrs) ->
#         iElement.bind 'error', ->
#           angular.element(this).attr 'src', iAttrs.fallbackSrc
#           return
#         return
#       fallbackSrc
#     RMSrv.clearCache()
#     null
#   js '/js/proplibAfter.min.js'

# STYLUS 'market-page','''
#   #editListingModal.active, #propDetailModal.modal.active, #promoteModal.modal.active, #schoolDetailModal.modal.active
#     -webkit-transform: translate3d(0,0,0);
#   #propDetailModal #schoolDetailModal .content
#     background-color white
#   #realtorUtilPage
#     .tri-wrapper
#       .triangle
#         width: 0;
#         z-index: 10;
#         height: 0;
#         position: absolute;
#         border-bottom: 40px solid transparent;
#         border-right: 40px solid transparent;
#         border-top:  40px solid rgba(25, 25, 25, 0.8);
#         border-left: 40px solid rgba(25, 25, 25, 0.8);
#       .triangle.active
#         border-top:  40px solid rgba(224, 49, 49, 0.8);
#         border-left: 40px solid rgba(224, 49, 49, 0.8);
#       .status
#         width: 70px;
#         height: 20px;
#         font-size: 13px;
#         color: white;
#         position: relative;
#         top: -21px;
#         left: -48px;
#         transform: rotate(-45deg);
#         text-align center

#     .content
#       background-color: #e6e6e6
#       padding-bottom: 44px;
#       .content-list
#         .listing-wrapper:not(:first-child)
#           margin-top 10px
#         .listing-wrapper
#           .card-content
#             padding 0px
#       .share-selector,.verify-btn
#         height: 40px;
#         margin-bottom: -40px;
#         z-index: 1;
#         position: absolute;
#         width: auto;
#         right: 0;
#         padding: 10px;
#       .verify-btn
#         right: 40px;
#         color: #e03131;
#         font-weight: bold;
#       .share-selector i.active
#         color #3B99FC
#       .share-selector .fa-square-o
#         padding-right: 3px;
#       .status-wrapper
#         height: 40px;
#         margin-bottom: -40px;
#         z-index: 1;
#         position: relative;
#         width: 92px;
#         overflow: hidden;
#         color: white;
#         text-align: center;
#         font-size: 14px;
#         background-color #E03131
#         padding-top: 10px;
#       .status-wrapper.active
#         background: #8BB048;
#         padding-top 0
#       .agent-wrapper
#         margin-top: -35px;
#         >div
#           overflow hidden
#           display inline-block
#         >div:first-child
#           padding-left 10px
#           vertical-align: top;
#           z-index: 9;
#           width 82px
#           img
#             width: 70px;
#             height: 70px;
#             border-radius: 50%;
#             position: relative;
#             border: 1px solid white;
#         a
#           img
#             height: 14px;
#             width: 14px;
#             margin-left 5px
#         .agent
#           color black
#           padding: 41px 0 6px 15px;
#           width: calc(100% - 83px);
#         .cpny
#           p
#             margin-bottom 0
#             font-size 13px

#     #newWecardBar
#       padding:0;
#       .btn
#         padding-top: 13px;
#       .btn-default
#         color: black
#     #newWecardBar.admin
#       .btn-half.btn-fill
#         width: 100px;
#       padding:0;
#     .bar-standard
#       .fa-trash
#         color: #e03131;
#         padding: 11px;
#         font-size: 21px;
#         z-index: 21;
#         float: left;
#         width: 30px;
#       .share
#         color: #2f3e46;
#         float: left;
#         padding: 10px 0 10px 0;
#         margin-left: 1px;
#       .create
#         div
#           display: inline-block;
#           vertical-align: top;
#           padding: 7px 0 0 0px;
#         padding: 5px 7px 0 0px;
#         color: #E03131;
#         font-size: 15px;
#       .split
#         display: inline-block;
#         width: 11px;
#         height: 45%;
#         border-left: 1px solid #e03131;
#         margin-top: 13px;
#     .card
#       .img-wrapper
#         height: 200px;
#         overflow: hidden;
#       .img-wrapper img
#         width: 100%;
#         height: 100%;
#       .detail-wrapper
#         .pull-left
#           color red
#         vertical-align: top;
#         text-align: right;
#         background-color: black;
#         color: white;
#         opacity: 0.7;
#         margin-top: -77px;
#         padding: 7px;
#         font-size: 14px;
#         height: 77px;
#         overflow: hidden;
#       .promote-wrapper
#         border-top: 1px solid #ECECEC;
#         padding-top: 10px;
#         height: 64px;
#         overflow: hidden;
#       .detail-wrapper .price
#         color: red
#       .detail-wrapper .addr
#         padding-left: 0px
#       .promote-wrapper img
#         width: 35px
#         height: 35px
#         margin-right 10px
#         margin-top 4px
#       .promote-wrapper p
#         font-size: 12px
#         margin-top: 9px;
#         margin-right: 13px;
# '''
