###
#conf = CONFIG()
return if conf.chinaMode

mysql = INCLUDE 'lib.mysql'
mysql_client = mysql.getDb conf.sqldbs.vow
helpers = INCLUDE 'lib.helpers'

SysData = COLLECTION 'chome', 'sysdata'
TREB_FILTER = 'treb-filter'
toBlock = [
  'COMFREE COMMONSENSE NETWORK, BROKERAGE'
  'ONE PERCENT REALTY LTD., BROKERAGE'
  'MY MOVE REALTY, BROKERAGE'
  'MODERN SOLUTION REALTY INC., BROKERAGE'
]

SysData.findOneAndUpdate {_id:TREB_FILTER},{$addToSet:rltr:$each:toBlock},{upsert:true,returnDocument:'after'},(err,filter)->
  if err then console.error err
  console.log filter.value

updateDatabase = ->
  SysData.findOne {_id:TREB_FILTER},(err,filter)->
    return console.error err if err
    if Array.isArray(rltr = filter?.rltr) and rltr.length > 0
      q = []
      for i in rltr
        q.push '?'
      sql = "UPDATE mls_treb SET flgb = 1 WHERE rltr in (" + q.join(",") + ")"
      mysql_client.query sql,rltr,(err,r)->
        if err then console.error err
        console.log r
helpers.repeat (1000 * 3600),updateDatabase

updateDatabase()
###
