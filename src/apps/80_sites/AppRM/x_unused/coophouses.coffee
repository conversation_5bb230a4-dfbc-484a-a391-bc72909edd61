# limiter = INCLUDE 'lib.limiter'
# # conf = CONFIG()

# notifyAdmin = DEF 'notifyAdmin'

# CoopHouses = COLLECTION 'vow','coophouses'
# CoopHouses.createIndex {loc:'2dsphere'}
# CoopHouses.createIndex {lat:1,lng:1}

# ###
# Properties = MODEL 'Properties'
# Properties.searchBBox ...,(err,list)
# Properties.updateMany listingObj,update,(err)->
# listing = new Properties(listingObj)
#   for l in [1m]
#     l = new Properties(obj)
# ###



# UserModel = MODEL 'User'
# APP 'coophouses'

# DEFAULT_LIMIT = 50
# MAX_LIMIT = 100

# findOne = (req,resp,id)->
#   fields = {title:1,addr:1,city:1,provider:1,primary_sponsor:1,property_type:1,type:1,lat:1,lng:1,article_body:1,how_to_apply:1}
#   return CoopHouses.findOne {_id:id},{fields:fields},(err,ret)->
#     if err
#       console.error "CoopHouses findOne Error"
#       console.error err
#       return resp.send {e:"Error when search coopHouses"}
#     resp.send {item:ret}

# POST (req,resp)->
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
#     findError = (err)->
#       console.error err
#       e = if 'string' is typeof err then err else 'Error'
#       return resp.send {e:e,cnt:0,items:[]}
#     rhLimit = req.session.get('rhLimit') or DEFAULT_LIMIT
#     limiter.check user,'coopHouses',rhLimit,(u,evt,cnt,notify)->
#       if u
#         if notify # when 70,140,210...
#           notifyAdmin "#{evt} reached limit(#{cnt}). #{req.remoteIP()} User:#{u._id}:#{u.eml} #{u.fn} #{u.ln} #{u.nm} #{u.roles}"
#         if cnt < MAX_LIMIT
#           return findError req.l10n "Reached Daily Usage Limit"
#           # do not ask share yet
#           #return resp.send {eurl:"/event?tpl=coophouses&u=/coophouses/shared"}
#         return findError req.l10n "Reached Daily Usage Limit"
#       limit = 50
#       q = req.body
#       # search by id
#       if id = q.id
#         return findOne req,resp,id
#       # search by map
#       if (sw = q.sw or q['sw[]']) and (ne = q.ne or q['ne[]'])
#         page = q.p or 0
#         query =
#           $and:[
#             {lat:{$gt:sw[0]}},
#             {lat:{$lt:ne[0]}},
#             {lng:{$gt:sw[1]}},
#             {lng:{$lt:ne[1]}}
#           ]
#       # search by location
#       if (lat = q.lat) and (lng = q.lng) and (dis = q.dis)
#         dis = 1000 unless 'number' is typeof (dis = parseInt dis)
#         dis = 5000 if dis > 5000
#         query ?= {}
#         query.loc =
#           $near:
#             $geometry:
#               type: "Point"
#               coordinates: [ lng , lat ]
#             $maxDistance: dis
#         page = 0
#       unless query
#         return resp.send {cnt:0, items:[]}
#       CoopHouses.count query, (err, cnt)->
#         if err
#           console.error "coophouses count Error:"
#           console.error err
#           return findError "System Error. Please try again."
#         if q.count # count only
#           return resp.send {cnt:cnt}
#         # get locations
#         skip = limit * page
#         CoopHouses.findToArray query, {fields:{lat:1,lng:1},limit:limit, sort:{dt:-1}, skip:skip}, (err, ret)->
#           if err
#             console.error "coophouses search Error:"
#             console.error err
#             return findError "System Error. Please try again."
#           resp.send {cnt:cnt,items:ret}


# POST 'shared',(req,resp)->
#   UserModel.appAuth {req,resp},(user)->
#     update = {$addToSet:{tskDn:'coophouses'}}
#     UserModel.setTskDn user._id, {tskDn:'coophouses'},(err)->
#       if err
#         console.error err
#       notifyAdmin "Shared coophouses. #{req.remoteIP()} User:#{user._id}:#{user.eml} #{user.nm_zh or user.nm_en} #{user.nm} #{user.roles}"
#       resp.send {ok:1,j:'/coophouses'}

# getGoogleAPILink = DEF 'getGoogleAPILink'
# GET (req,resp)->
#   unless req.getDevType() is 'app'
#     return resp.redirect "/adPage/needAPP"
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
#     UserModel.findByTskDnAndId user._id,'coophouses',(err,found)->
#       if err then console.error err
#       #unless found
#       #  return resp.redirect "/event?tpl=creepyHouse&u=/stigma/house/shared"
#       if found
#         req.session.set "rhLimit",MAX_LIMIT,->
#       #,places,weather&sensor=false"
#       googleapiJs = getGoogleAPILink(req,{tp:'googleapi',cn:req.isChinaIP(),src:'cphse'})
#       resp.ckup "map",{googleapiJs:googleapiJs},'_',{noAngular:true}

# VIEW "map",->
#   js @googleapiJs
#   div id:'vueBody',->
#     text """<coophouses-map></coophouses-map>"""
#   js '/js/entry/commons.js'
#   js '/js/entry/coophousesMap.js'
