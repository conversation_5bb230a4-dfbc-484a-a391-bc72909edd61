# Properties = COLLECTION 'vow','properties'


APP '1.5/prop'

# /1.5/prop/updatepredict
# POST 'updatepredict', (req, resp) ->
#   appAuth req,resp,null, (user) ->
#     error = (err) ->
#       resp.send {ok:0, err:err}
#     return error('No Auth') unless req.isAllowed('predict')
#     id = req.param 'id'
#     err = req.param 'err'
#     isOk = req.param('pred_ok')
#     return error('No id') unless id
#     return error('No err detail') unless (err or isOk)
#     set = {pred_err:err}
#     #TODO: remove pred_ok if err/ vise versa
#     # if isRMlisting(id)
#     k = 'pred_err'
#     unset = {pred_ok:1}
#     if req.param 'pred_ok'
#       set = {pred_ok:1}
#       k = 'pred_ok'
#       unset = {pred_err:1}
#     predlog = {
#       k:k,
#       _id:user._id,
#     }
#     push = {predlog}
#     update = {$set:set, $push:push, $unset:unset}
#     # NOTE: THIS FILE IS NOT USED ANY MORE
#     Properties.updateOne {_id:id}, update,{upsert: false}, (err,prop)->
#       return error(err.toString()) if err
#       return resp.send {ok:1, msg:req.l10n('Updated')+' '+k}

# GET 'predlist',(req,resp)->
#   appAuth req,resp,null, (user) ->
#     error = (err) ->
#       resp.send {ok:0, err:err}
#     return error('No Auth') unless req.isAllowed('predict')
#     resp.ckup 'predlist',{},'_',{noAngular:1}

STYLUS 'predlist','''
#sortSelect
  width calc(100% - 40px)
.detailLink
  color: #428bca
#listWrapper
  .prop
    padding 10px
    border-bottom 1px solid #f1f1f1
  .prop > div
    display inline-block
  .prop .img
    width 0%
    color #428bca
    font-weight bold
  .prop .detail
    width 100%
    font-size: 14px;
    .data > div
      display: flex
.label,.val
  display inline-block
.label
  width 30%
.val
  width 69%
.fa-check-circle,.fa-arrow-down
  color #5cb85c
.fa-exclamation-circle,.fa-arrow-up
  color #e03131
'''
VIEW 'predlist',->
  js "/js/jquery-1.10.1.min.js"
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', onclick:'goBack()'
    h1 class: 'title',-> _ 'RealMaster'
  div class:'bar bar-standard bar-footer' , ->
    input type:'text',id:'errString',placeholder:'err text'
  div class: 'content',->
    div class:'sort',->
      div ->
        span -> text 'Sort'
        select id:'sortSelect',->
          # option value:'lp-pred_sp-inc',-> text 'lp-pred_sp-inc low->high'
          option value:'lp-pred_sp-dec',-> text 'lp-pred_sp-dec high->low'
          # option value:'sp-pred_sp-inc',-> text 'sp-pred_sp-inc low->high'
          option value:'sp-pred_sp-dec',-> text 'sp-pred_sp-dec hign->low'
          option value:'lp-pred_sp-per-dec',-> text 'lp-pred_sp-dec% hign->low'
          option value:'sp-pred_sp-per-dec',-> text 'sp-pred_sp-dec% hign->low'
      # div class:'btn btn-outline',-> ''
    div ->
      div id:'listWrapper',->
      div class:'btn btn-block btn-positive',id:'getMoreResults',-> text 'Get More'
  coffeejs ->
    $ ->
      page = 0
      goBack = ()->
        if document.url.href.indexOf('more')>0
          document.url.href = '/1.5/index/more'
        else
          document.url.href = '/1.5/index'
      genPropPredictErrLabel = (p)->
        style = 'display:none'
        if p.pred_err
          style = ''
        else
          p.pred_err = ''
        text = "
        <span id='#{p._id}Err' class='fa fa-exclamation-circle' style='#{style}'>#{p.pred_err}</span>
        <span id='#{p._id}Ok' class='fa fa-check-circle' style='display:none'></span>
        "
        text
      genPropPredictLabel = (p)->
        updown = if p.pred_sp > p.sp then 'fa-arrow-up' else 'fa-arrow-down'
        text = "<span class='fa #{updown}'></span>"
        text
      currency = (x, sign = '$', dig) ->
        try
          if((typeof(x) is 'string') and (x.indexOf(sign) isnt -1))
            return x
          tmp = parseInt(x)
          if isNaN(tmp)
            return ''
          parts = x.toString().split('.')
          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          if dig == 0
            parts[1] = undefined
          else if dig > 0
            if parts[1]
              parts[1] = parts[1].substr(0, dig)
          return sign + parts.filter((val) ->
            val
          ).join('.')
        catch e
          console.error e
          return ''
        return
      percentage = (v)->
        (v*100).toFixed(1)+'%'
      generatePropHtml = (p,idx)->
        html = ''
        html += "<div class='prop'>
        <div class='img'>#{idx+1}
        </div>
        <div class='detail'>
          <div class='data'>
            <div>
              <span class='label'>Addr:</span>
              <span class='val'>#{p.addr}, #{p.city}</span>
            </div>
            <div>
              <span class='label '>ID:</span>
              <span class='val detailLink' onclick='showDetail(\"#{p._id}\")' data-id='#{p._id}'>#{p._id}#{genPropPredictErrLabel(p)}</span>
            </div>
            <div>
              <span class='label'>List Price:</span>
              <span class='val'>#{currency(p.lp,'',0)} (#{currency(p.diff1,'',0)} -> #{percentage(p.diff1Per)})</span>
            </div>
            <div>
              <span class='label'>Sold Price:</span>
              <span class='val'>#{currency(p.sp,'',0)} (#{currency(p.diff2,'',0)} -> #{percentage(p.diff2Per)})</span>
            </div>
            <div>
              <span class='label'>Pred Price:</span>
              <span class='val'>#{currency(p.pred_sp,'',0)} (σ: #{p.pred_spsd})#{genPropPredictLabel(p)}</span>
            </div>
          </div>
          <div class='ctrls'>
            <span class='btn btn-outline btn-positive predOk' data-id='#{p._id}'>Set PredOk</span>
            <span class='btn btn-outline btn-negative predErr' data-id='#{p._id}'>Set Error</span>
          </div>
        </div>
        </div>"

        html
      renderPropList = (l)->
        html = ''
        for prop,idx in l
          html += generatePropHtml(prop,idx)
        return html
      handlePropList = (l)->
        html = renderPropList(l)
        $('#listWrapper').html(html)
      initPropList = (sort,page)->
        $('#listWrapper').html('')
        sort ?= 'lp-pred_sp-dec'
        data = {sort:sort}
        if page
          data.page = page
        $.post '/1.5/prop/predlist',data,(ret)->
          console.log ret
          if ret.l?.length
            handlePropList(ret.l)
          else
            RMSrv.dialogAlert 'No result'
      initPropList()
      $('#sortSelect').change (e)->
        initPropList(this.value)
      $(document).on('click','.predErr',(e)->
        e.stopPropagation()
        e.preventDefault()
        id = $(this).data('id')
        err = $('#errString').val()
        data = {id,err}
        #alert JSON.stringify data
        $.post '/1.5/prop/updatepredict',data,(ret)->
          if ret.ok
            RMSrv.dialogAlert ret.msg
            $('#'+id+'Err').show()
            $('#'+id+'Ok').hide()
            $('#'+id+'Err').text(err)
          else
            RMSrv.dialogAlert ret.err
      )
      $('#getMoreResults').on 'click',(e)->
        initPropList($('#sortSelect').val(),++page)
      $(document).on('click','.predOk',(e)->
        e.stopPropagation()
        e.preventDefault()
        id = $(this).data('id')
        data = {id:id,pred_ok:1}
        #alert JSON.stringify data
        $.post '/1.5/prop/updatepredict',data,(ret)->
          if ret.ok
            $('#'+id+'Ok').show()
            $('#'+id+'Err').hide()
            RMSrv.dialogAlert ret.msg
          else
            RMSrv.dialogAlert ret.err
      )
      showDetail = (id)->
        base = "/1.5/prop/detail/inapp?lang=en"
        url = base+"&id="+id
        # alert url
        # RMSrv.openTBrowser url,{},(val)->
        #   console.log val
        # return
        RMSrv.getPageContent url, '#callBackString',{hide:false}, (val) ->
          if val == ':cancel'
            console.log 'canceled'
            return
          if /^redirect/.test(val)
            return window.location = val.split('redirect:')[1]
          try
            if /^cmd-redirect:/.test(val)
              url = val.split('cmd-redirect:')[1]
              return window.location = url
            d = self.urlParamToObject(val)
            window.bus.$emit 'school-prop', d
          catch e
            console.error e
          return
      window.showDetail = showDetail
      # $(document).on('click','.detailLink',(e)->
      #   id = $(this).data('id')
      #   showDetail(id)
      # )
    null

# get list of not ok results
# POST 'predlist',(req, resp) ->
#   appAuth req,resp,null, (user) ->
#     error = (err) ->
#       resp.send {ok:0, err:err}
#     return error('No Auth') unless req.isAllowed('predict')
#     skip = 0
#     limit = 50
#     if p = req.param 'page'
#       skip = parseInt(p)*limit
#     filter = []
#     # $match: {
#     #   pred_sp:{$exists:true},
#     #   lp:{$exists:true},
#     #   sp:{$exists:true},
#     #   pred_ok:{$exists:false}
#     #  }
#     match = {
#       pred_sp:{$gte:1},
#       lp:{$gte:1},
#       sp:{$gte:1},
#       pred_ok:{$exists:false}
#     }
#     sort = {}
#     if sortMethod = req.param 'sort'
#       if /inc/.test sortMethod
#         val = 1
#       else
#         val = -1
#       # lp-pred_sp-per-dec
#       if /lp\-pred/.test sortMethod
#         fld = 'diff1'
#         delete match.sp
#       else
#         fld = 'diff2'
#       if /per/.test sortMethod
#         fld = fld+'Per'
#       sort[fld] = val
#       # if sortMethod is 'lp-pred_sp-inc'
#       #   sort = {diff1:1}
#       # else if sortMethod is 'lp-pred_sp-dec'
#       #   sort = {diff1:-1}
#       # else if sortMethod is 'sp-pred_sp-inc'
#       #   sort = {diff2:1}
#       # else if sortMethod is 'sp-pred_sp-dec'
#       #   sort = {diff2:-1}
#         # TODO: care about sp?
#         # delete match.sp
#     ag = [
#       {
#         $match: match,
#       },
#       {
#          $addFields: {
#            diff1: { $abs: {$subtract:["$lp","$pred_sp"]} },
#            diff2: { $abs: {$subtract:["$sp","$pred_sp"]} },
#          }
#       },
#       {
#         $project: {
#           addr:1,
#           city:1,
#           prov:1,
#           pred_err:1,
#           lp: 1,
#           lpr: 1,
#           sp: 1,
#           pred_sp: 1,
#           pred_spsd:1,
#           diff1:1,
#           diff2:1,
#           diff1Per: { $divide: [ "$diff1", "$lp" ] },
#           diff2Per: { $divide: [ "$diff2", "$sp" ] }
#         },
#       },
#       { $sort:sort},
#       { "$skip": skip },
#       { "$limit": limit },
#     ]
#     Properties.aggregate ag, (err,list)->
#       return error(err.toString()) if err
#       return resp.send {ok:1, l:list}
