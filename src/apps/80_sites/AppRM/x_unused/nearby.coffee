# conf = CONFIG()

# APP 'nearby'
# getGoogleAPILink = DEF 'getGoogleAPILink'

# GET (req,resp)->
#   return if req.redirectHTTPWhenCnDitu()
#   unless req.getDevType() is 'app'
#     return resp.redirect "/adPage/needAPP"
#   googleapiJs = getGoogleAPILink(req,{tp:'googleapi',cn:req.isChinaIP(),lib:'places,geometry',src:'nrby'})
#   resp.ckup "nearby",{googleapiJs:googleapiJs},'_',{noAngular:true}

# VIEW "nearby",->
#   js @googleapiJs
#   div id:'vueBody',->
#     text """<nearby-search></nearby-search>"""
#   js '/js/mapBasic.min.js'
#   js '/js/entry/commons.js'
#   js '/js/entry/nearbySearch.js'
