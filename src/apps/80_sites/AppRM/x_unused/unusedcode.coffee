###
buildSql = ->
  for k,o of city_list_object
    if k.indexOf('.') > 0
      if k.indexOf('_') > 0 # complex toronto region
        # get inclusion
        throw new Error "Unknown city name #{k}:#{o.n}" unless mclist = city_inclusion[k]
        ml = []
        cl = []
        sql = null
        sql2 = null
        for mc in mclist
          if (dots = (mc.split('.').length - 1)) is 1
            if mc.indexOf('_') < 0
              ml.push mc
          else # 2
            cl.push mc
        if ml.length > 0
          sql = "municipality_code IN ('" + ml.join("','") + "')"
        if cl.length > 0
          sql2 = "community_code IN ('" + cl.join("','") + "')"
          if sql
            sql = "( " + sql + " OR " + sql2 + " )"
          else
            sql = sql2
        o.sql = sql
      else # municipality
        o.sql = "municipality_code = '#{k}'"
    else # area only
      o.sql = "area_code = '#{k}'"

buildCommunityList = ()->
  addKV = (ary,o)->
    if (i = o.indexOf('.')) > 0
      ac = o.substr(0,i)
      mc = o
    else
      ac = o
      mc = null
    if not (a = city_tree[ac])?
      console.log "No area for #{ac}"
      return null
    m = if mc? then a[mc] else a
    for k,v of m
      if k isnt 'nm' and k isnt 'tp'
        ary.push {k:k,n:v.nm}
  for o in city_list
    ary = []
    if 0 < o.k.indexOf('_')
      for v in city_inclusion[o.k]
        addKV(ary,v) if 0 > v.indexOf('_')
    else
      addKV ary,o.k
    community_list[o.k] = ary
  null

sortor_function = (a,b)->
    if a.n < b.n
      return -1
    else if a.n > b.n
      return 1
    0

cities = (mysql_client,cb)->
  mysql_client.query "select distinct municipality_code as mc , municipality as m, area as a, area_code as ac, community as c, community_code as cc from mls_treb where source = 'treb' order by 1;",(err,list)->
    if err then return cb err
    # build city_tree
    city_tree = {}
    for l in list
      if l.mc and l.ac
        a = city_tree[l.ac] ?= {nm:helpers.trim(l.a),tp:'area'}
        m = a[l.mc] ?= {nm:helpers.trim(l.m),tp:'municipality'}
        if l.cc
          m[l.cc] = {nm:helpers.trim(l.c),tp:'community'}
    # build city_list
    _city_list = city_list
    city_list = []
    for c in _city_list
      if 'object' is typeof c # already there
        city_list.push c
        unless c.noconsume # when it's not partial area
          ac = c.k.substr(0,c.k.indexOf('.'))
          city_tree[ac].used = true
      else if 0 < (p = c.indexOf '.') # municipal code +
        ac = c.substr 0,p
        if (mc = c.substr p+1).length > 0 # municipal code
          #check if existes due to db incomplete test db
          if city_tree[ac]?[c]?.nm
            city_list.push {k:c,n:city_tree[ac][c].nm}
            city_tree[ac].used = true
        else # area code to get municipals
          if city_tree[ac]?
            for mc,m of city_tree[ac]
              if mc isnt 'nm' and mc isnt 'tp'
                city_list.push {k:mc,n:m.nm}
            city_tree[ac].used = true
      else # area code only
        #check if exists
        if city_tree[c]?.nm
          city_list.push {k:c,n:city_tree[c].nm}
          city_tree[c].used = true
    # sort often used names 1st
    city_list.sort sortor_function
    # save often used list to fav
    city_list_fav = city_list.slice 0

    # city_list
    # add extra areas
    tmp = []
    tmp2 = []
    for ac,a of city_tree
      if a.used
        delete a.used
      else if /[x-zX-Z]/.test ac
        tmp2.push {k:ac,n:a.nm}
      else
        tmp.push {k:ac,n:a.nm}
    tmp.sort sortor_function
    #debug tmp
    city_list = city_list.concat tmp,tmp2
    city_list_extra = tmp.concat tmp2
    # build city_list_object
    for c in city_list
      city_list_object[c.k] = c
    # build sql
    buildSql()
    # build community list
    buildCommunityList()
    cb null,{city_tree:city_tree,city_list:city_list,city_list_object:city_list_object,city_inclusion:city_inclusion}
###
# PLSWAIT _city_list_resource
# cities mysql_client,(err,cities)->
#   if err then throw err
#   #console.log "City List >>> "
#   #console.log city_list
#   #console.log "City Tree >>> "
#   #console.log city_tree
#   #console.log "City List Object >>> "
#   #console.log city_list_object
#   PROVIDE _city_list_resource
###
  Create Query
     idx_created : get poor performance results, stop use: 2016-05
###
# accessAllowed = DEF '_access_allowed'
# create_query = (p,user,maskMode)->
#   q = []
#   where = []
#   labels = [] # save named labels {n:'name',v:'value'}
#   sql = "SELECT [field] FROM [table] WHERE "
#   count_sql = "SELECT count(*) as count FROM [table] WHERE "
#   tables = ['mls_treb USE INDEX (idx_composed,PRIMARY,idx_municipality_code,idx_lp_dol,idx_latlng_created,idx_latlng_lp_dol,idx_addr,idx_agent,idx_agent2)']
#   fields = ["ml_num","bcf","lp_dol","lp_code",'addr','apt_num','gar_spaces','gar','park_spcs',
#     'area','area_code','municipality','municipality_code','community','community_code','taxes','yr','sqft',
#     's_r','style','type_own1_out','bath_tot','rltr','num_kit','kit_plus','br','br_plus',
#     'bus_type','prop_type','tot_area','tot_areacd','pic_num','lat','lng','cross_st','tour_url','idx','source',
#     'DATEDIFF(CURDATE(),created) as dom',
#     'FORMAT(lp_dol,0) as lp_price']
#   status = "status in ('A','Lc','Sc','Dft')"
#
#   p.pgsz ?= 50
#   if not helpers.isNumber p.pgsz
#     p.pgsz = 50
#   if p.pgsz > 100
#     p.pgsz = 100
#   if p.pgsz < 1
#     p.pgsz = 1
#
#   hasnt_id = true
#   # list
#   if (l = p.list)? and (('string' is typeof l and (l = helpers.trimore(l)).length > 0) or (Array.isArray l))
#     l = l.split ',' if 'string' is typeof l
#     a = []
#     norental = true
#     for i in l
#       a.push '?'
#     where.push "ml_num in (#{a.join ','})"
#     q = q.concat l
#     labels.push {n:'Keys',v:l}
#     hasnt_id = false
#     # when from fav properties, may save for 30 days
#     if p.d? and helpers.isNumber(p.d) and p.d > 0
#       status = "DATE(_mt) >= DATE_SUB(CURDATE(), INTERVAL #{p.d} DAY)"
#
#   # areas TODO: use getCitySql
#   if hasnt_id and (aa = p.aa)? and aa.length > 0
#     where.push "area_code = ?"
#     q.push aa
#     labels.push {n:'Area',v:city_list_object[aa].n}
#     if (am = p.am)? and am.length > 0
#       where.push "municipality_code = ?"
#       am = aa + '.' + am
#       q.push am
#       labels.push {n:'Municipality',v:city_list_object[am].n}
#       if (ac = p.ac)? and ac.length > 0
#         where.push "community_code = ?"
#         ac = am + '.' + ac
#         q.push ac
#         labels.push {n:'Community',v:city_list_object[ac].n}
#   if hasnt_id and (city = p.city)? and city.length > 0
#     if city_obj = city_list_object[city]
#       where.push city_obj.sql
#       labels.push {n:'Municipality',v:city_obj.n}
#     else
#       console.log "Unknown Community Code #{community}"
#
#   # lat/lng
#   if hasnt_id and p.lat1 and p.lat2 and p.lng1 and p.lng2
#     if p.lat1 > p.lat2
#       tmp = p.lat1
#       p.lat1 = p.lat2
#       p.lat2 = tmp
#     if p.lng1 > p.lng2
#       tmp = p.lng1
#       p.lng1 = p.lng2
#       p.lng2 = tmp
#     where.push "lat > ? AND lat < ? AND lng > ? AND lng < ?"
#     q.push p.lat1,p.lat2,p.lng1,p.lng2
#     labels.push {n:'Map area',v:"(#{p.lat1},#{p.lng1})-(#{p.lat2},#{p.lng2})"}
#
#   # addr
#   if hasnt_id and p.addr
#     where.push "addr like ?"
#     q.push "%" + p.addr + "%"
#     labels.push {n:'Address',v:p.addr}
#
#   # mode
#   if hasnt_id and (m = p.mode)? and (m.length > 0)
#     mtp = null
#     switch m
#       when 'fc'
#         where.push "bcf in ('f','c') AND s_r = 'Sale'"
#         mtp = 'Residential for Sale'
#       when 'fcr'
#         where.push "bcf in ('f','c') AND s_r = 'Lease'"
#         mtp = 'For Rent'
#       when 'fr'
#         where.push "bcf = 'f' AND s_r = 'Lease'"
#         mtp = "For Rent"
#       when 'cr'
#         where.push "bcf = 'c' AND s_r = 'Lease'"
#         mtp = "For Rent"
#       when 'f'
#         where.push "bcf = 'f' AND s_r = 'Sale'"
#         mtp = "Residential Freehold"
#       when 'c'
#         where.push "bcf = 'c' AND s_r = 'Sale'"
#         mtp = "Residential Condo"
#       when 'b'
#         where.push "bcf = 'b' AND s_r = 'Sale'"
#         mtp = "Commercial"
#       when 'br'
#         where.push "bcf = 'b' AND s_r in ('Lease','Sub-Lease')"
#         mtp = "Commercial Rent"
#       else
#         console.log "Unsupported mode #{m}"
#     labels.push {n:'Type',v:mtp}
#
#   # type
#   if hasnt_id and (t = p.type)? and (t.length > 0)
#     where.push "type_own1_out IN ('" + typeOut2In(t).join("','")+ "')"
#     labels.push {n:'Property Type',v:t}
#
#   # br
#   if hasnt_id and (br = p.br)? and (br.length > 0)
#     brn = br
#     if br.slice(-1) is '+'
#       brn = parseInt br.slice(0,-1)
#       where.push "br >= ?"
#       labels.push {n:'Bedrooms',v:">=#{brn}"}
#     else
#       where.push "br = ?"
#       labels.push {n:'Bedrooms',v:br}
#     q.push brn
#
#   if hasnt_id and (bath_tot = p.bath_tot)? and (bath_tot.length > 0)
#     bath_totn = bath_tot
#     if bath_tot.slice(-1) is '+'
#       bath_totn = parseInt bath_tot.slice(0,-1)
#       where.push 'bath_tot >= ?'
#       labels.push {n:'Bathrooms',v:">=#{bath_totn}"}
#     else
#       where.push 'bath_tot = ?'
#       labels.push {n:'Bathrooms',v:bath_totn}
#     q.push bath_totn
#
#   # parking/garage spaces
#   if hasnt_id and (gr = p.gr)? and (gr.length > 0)
#     grn = gr
#     if gr.slice(-1) is '+'
#       grn = parseInt gr.slice(0,-1)
#       where.push "((gar_spaces >= ? AND bcf = 'f') OR (park_spcs >= ? AND bcf = 'c'))"
#       labels.push {n:'Garage',v:">=#{grn}"}
#     else
#       where.push "((gar_spaces = ? AND bcf = 'f') OR (park_spcs = ? AND bcf = 'c'))"
#       labels.push {n:'Garage',v:grn}
#     q.push grn
#     q.push grn
#
#
#   # price
#   if hasnt_id
#     if (p1 = p.pf)? and (p1 = parseFloat(p1.toString().replace(/,/g,'')))? and (p1 isnt NaN) and (p1 > 0)
#       if p1 < 0 then p1 = -p1
#       pf = p1
#       if p.mode is 'fcr'
#         where.push "(lp_dol >= ? OR lp_dol = 0)"
#       else
#         where.push "lp_dol >= ?"
#       q.push pf
#     if (p2 = p.pt)? and (p2 = parseFloat(p2.toString().replace(/,/g,'')))? and (p2 isnt NaN) and (p2 > 0)
#       if p2 < 0 then p2 = -p2
#       pt = p2
#       if p.mode is 'fcr'
#         where.push "(lp_dol <= ? OR lp_dol = 0)"
#       else
#         where.push "lp_dol <= ?"
#       q.push pt
#     if (pf? and pt?) and (pt < pf)
#       q.pop()
#       q.pop()
#       q.push pt
#       q.push pf
#       labels.push {n:'Price',v:"#{pf} - #{pt}"}
#     else if pt?
#       labels.push {n:'Price',v:"<=#{pt}"}
#     else if pf?
#       labels.push {n:'Price',v:">=#{pf}"}
#
#
#   # dom
#   if hasnt_id and (d = p.dom)? and (d.length > 0) and (d = parseInt d)?
#     if d < 0
#       d = -d
#       where.push "DATE(created) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)"
#       labels.push {n:'DOM',v:"<=#{d}"}
#     else if d > 0
#       where.push "DATE(_mt) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)"
#       labels.push {n:'OFFMARKET',v:d}
#       status = "status NOT IN ('A','Lc','Sc','Dft')"
#     else # d == 0
#       where.push "DATE(created) = DATE_SUB(CURDATE(), INTERVAL ? DAY)"
#       labels.push {n:'DOM',v:d}
#     q.push d
#
#   # maintenance fee: maint
#   if hasnt_id and (mfee = p.mfee)
#     where.push "maint <= ?" #TODO: OR maint is null?
#     q.push mfee
#     labels.push {n:'maintenance',v:"<=#{mfee}"}
#
#   # timestamp
#   if hasnt_id and ((dt1 = p.tf)? or (dt2 = p.tt))
#     if dt1
#       where.push "timestamp_sql >= ?"
#       q.push dt1
#       labels.push {n:'timestamp',v:">=#{dt1}"}
#     if dt2
#       where.push "timestamp_sql <= ?"
#       q.push dt2
#       labels.push {n:'timestamp',v:"<=#{dt2}"}
#
#   # school
#   if hasnt_id and (schid = p.schid)? and (helpers.isNumber(schid) or ((schid.length > 0) and (schid = parseInt schid)?) )
#     tables.push 'listing_schools'
#     where.push 'sch_id = ?'
#     where.push 'mls_treb.ml_num = listing_schools.mls_num'
#     q.push schid
#     if (schbnd = p.schbnd)? and (helpers.isNumber(schbnd) or ((schbnd.length > 0) and (schbnd = parseInt schbnd)?) )
#       where.push 'bnd_id = ?'
#       q.push schbnd
#     labels.push {n:'School',v:schid}
#
#   # agent_id
#   if hasnt_id and (agent_id = p.agent_id)?
#     where.push '(agent_id = ? OR agent2_id = ?)'
#     q.push agent_id
#     q.push agent_id
#     labels.push {n:'Agent',v:agent_id}
#
#   # only show available listings
#   #where.push "status <> 'U' AND (rltr NOT IN ('#{excluded_brokerage.join('\',\'')}') OR rltr IS NULL)"
#   where.push status
#
#   # user filter
#   if not maskMode and not user?
#     if p.mode is 'fcr'
#       where.push "(idx = 'x' or source <> 'treb')"
#     else
#       where.push "idx = 'x'"
#
#   # show all listings or filter them
#   unless accessAllowed 'allListings',user
#     where.push "flgb is NULL"
#
#   count_sql += where.join " AND "
#
#   getOrderSql = ->
#     r = []
#     if p.sort? and /^[a-z0-9\s\-\_]+$/.test p.sort
#       #for k,v of p.sort
#       #  r.push "#{k} #{v}"
#       if (p.sort = helpers.trimore p.sort).length > 0
#         r.push p.sort
#     if r.length > 0
#       " ORDER BY #{r.join(',')}"
#     else
#       ""
#   sql += where.join(" AND ") + getOrderSql() + " LIMIT #{p.pgsz}"
#
#   if p.pg and helpers.isNumber(p.pg) and p.pg >= 0
#     sql += " OFFSET #{p.pgsz * p.pg}"
#
#   # extra fields
#   if p.ad_text
#     fields.push 'ad_text'
#
#   if p.id_only
#     fields = ['ml_num']
#
#   # replace tables
#   tables = tables.join ','
#   count_sql = count_sql.replace '[table]',tables
#   sql = sql.replace '[table]',tables
#   sql = sql.replace '[field]',fields.join ','
#
#   {sql:sql,p:q,count:count_sql,labels:labels}
#
# DEF 'createQuery',create_query
_idx_all_stored = null
# DEF 'countIDX',(cb)->
#   sql = "select count(*) as vow, count(idx) as idx from mls_treb where status in ('A','Lc','Sc','Dft')"
#   re = ->
#     #console.log "idx #{_idx_all_stored.idx}"
#     cb {idx_count:_idx_all_stored.idx,vow_count:_idx_all_stored.vow}
#   if _idx_all_stored?.t > Date.now()
#     return re()
#   mysql_client.query sql, (err,r)->
#     if err then return cb {idx_count:0,vow_count:0}
#     _idx_all_stored = {idx:r[0].idx,vow:r[0].vow,t:(Date.now() + 3600000)}
#     return re()
# get_ad_user_from_prop = (prop, cb)->
#   return cb 'No yet in this ver'
#   error = (err)->
#     cb req.l10n err.toString()
#   loc = {
#     lat: prop.lat
#     lng: prop.lng
#   }
#   unless loc
#     return error('no location data')
#   try
#     lat = parseFloat(loc.lat)
#     lng = parseFloat(loc.lng)
#     if NaN is lat or NaN is lng
#       return error 'not valid loc data'
#   catch err
#     return error err
#   ret = {
#     eml:'<EMAIL>'
#     mbl:'2312321'
#     fn:'FirsN'
#     lg:'LastN'
#     _id:'555b95bae8006e3e5624cd6a'
#   }
#   return cb null, ret
#   # User.findOne {}, (err, ret)->
#   #   return error(err) if err
#   #   return cb ret
# DEF 'get_ad_user_from_prop', get_ad_user_from_prop

###
APP '1.5'

GET 'getvip', (req, resp)->
  resp.ckup 'getvip', {}, '_', {noAngular:1, noRatchetJs:1, noCordova:1}

VIEW 'getvip', ->
  text """
  <style>
  #vipDetailPage .headerBar{background:#e03131; height:44px;
    color: white;
    text-align: center;
    padding-top: 12px;}
  #vipDetailPage .pageContent{background:#EBEBEB;
    text-align: center; padding-bottom: 50px;}
  #vipDetailPage .pageContent > * {
    padding-left:12px;
    padding-right:12px;
  }
  #vipDetailPage .vip-detail{padding-left:0; padding-right:0}
  #vipDetailPage .icons > div, .split > div {display:inline-block; width:33.33%}
  #vipDetailPage .vip-detail .detail-wrapper{
    height: auto;
    width: 100%; background: white;}
  #vipDetailPage .icons > div > img {
    width:  55px;
    height: 55px;
    border-radius:50%;
  }
  #vipDetailPage .vip-detail {margin-bottom: 20px;}
  #vipDetailPage p {color: #888; margin-bottom:0;}
  #vipDetailPage .tl { padding: 30px 10px 40px 10px;}
  #vipDetailPage .split{margin: 20px 0 10px 0;}
  #vipDetailPage .learn-more .btn { font-size: 16px;}
  #vipDetailPage .learn-more .btn i { font-size: 19px;
    vertical-align: text-bottom;
    padding-right: 8px;
  }
  #vipDetailPage .learn-more div:first-child{ margin-bottom: 10px; }
  #vipDetailPage .more {margin-top: 40px;}
  #vipDetailPage .split .line{border-bottom:1px solid #BFBFBF;     vertical-align: middle;}
  </style>
  """
  div id:"vipDetailPage",->
    div class:'headerBar', ->
      text "房大师VIP服务介绍"
    div class:'pageContent', ->
      div class:'tl' ,->
        p ->
          text _ "Make you stand out among vast realtors!" # 让您在众多经纪中脱颖而出
      div class:'icons', ->
        div ->
          img src:'/img/icon_get_vip_01.png'
          div ->
            p -> text _ "Leads" #潜在客户
          div ->
            p -> text _ 'Recommendation' # 精准推荐
        div ->
          img src:'/img/icon_get_vip_02.png'
          div ->
            p -> text _ "Verification" # 品牌认证
          div ->
            p -> text _ "Improve Value" # 提升价值
        div ->
          img src:'/img/icon_get_vip_03.png'
          div ->
            p -> text _ "Search Prop" # 一站式找房
          div ->
            p -> text _ "Sale" # 多平台营销
      div class:'split', ->
        div class:'line',->
        div ->
          p ->
            text _ "VIP detail" # 详细VIP服务
        div class:'line',->
      div class:'vip-detail', ->
        div class:"detail-wrapper", ->
          img src:'/img/vip_intro_cn.png', style:'width:100%;vertical-align: top;'
      div class:'learn-more', ->
        div ->
          p ->
            text _ "Learn More Service Detail" # 了解更多服务详情
        div ->
          a href:'tel:9056142609', class:"btn btn-block btn-long btn-rm", ->
            i class:'fa fa-phone'
            text "************"
          a hre:'mailto:<EMAIL>', class:"btn btn-block btn-long btn-rm", ->
            i class:'fa fa-envelope'
            text "<EMAIL>"
      div class:'more', ->
        div ->
          p ->
            text "More Help Info:" # 更多帮助信息详见
        div ->
          a href:'http://www.realmaster.ca', ->
            text "www.realmaster.ca"
###
# update_sql_json = (sql_base, params, cb) ->
#   return unless mysql_client
#   mysql_client.query sql_base,params,(err,dbret)->
#     return cb err if err
#     return cb null, dbret

# check_jdoc2_exists = (id, cb) ->
#   check_query = "SELECT jdoc2 FROM `properties`  WHERE `id` = ?;"
#   mysql_client.query check_query,[id],(err,dbret)->
#     return cb err if err
#     return cb false unless dbret[0]?.jdoc2
#     try
#       return cb true if JSON.parse dbret[0].jdoc2
#       return cb false
#     catch err
#       return cb false

# setup_jdoc2 = (id, cb) ->
#   setup_q = "UPDATE properties SET jdoc2 = JSON_OBJECT('ts', NOW())  WHERE id = ?;"
#   mysql_client.query setup_q,[id],(err,dbret)->
#     return cb err if err
#     return cb null, dbret

# update_sql_jdoc2 = (obj, id, cb) ->
#   return cb 'no id' unless id
#   return cb 'nothing to do ' unless Object.keys(obj).length
#   check_jdoc2_exists id, (err, exist) ->
#     _update = () ->
#       params = []
#       qs = ''
#       for k, v of obj
#         params.push v
#         qs += ", '$." + k + "'"
#         qs += ', ?'
#       params.push(id)
#       sql_base = "UPDATE properties SET jdoc2 = JSON_SET(jdoc2" + qs + ")  WHERE id = ?;"
#       # return cb 'here'
#       update_sql_json sql_base, params, (err, ret)->
#         return cb err if err
#         return cb null, ret
#     if exist
#       _update()
#     else
#       setup_jdoc2 id, (err, ret)->
#         return cb err if err
#         _update()
# sql_base = "SELECT id, jdoc, jdoc2 FROM properties WHERE id = ?"
# mysql_client.query sql_base,[id],(err,dbret)->
#   if err then return error err
#   if dbret?.length > 0 # found by ID
#     prop = dbret[0]
#     jdoc = JSON.parse prop.jdoc if jdoc
#     jdoc2 = JSON.parse prop.jdoc2
#     if jdoc?.sp or jdoc2?.sp
#       updateRet(jdoc2)
#       updateRet(jdoc) #jdoc has sp, prefer
#   return resp.send ret
# sql_base = "SELECT id, jdoc2 FROM properties WHERE id = ?"
# mysql_client.query sql_base,[id],(err,dbret)->
#   if err then return error err
#   if dbret?.length > 0 and (prop = dbret[0]) and (jdoc2 = JSON.parse(prop.jdoc2)) and jdoc2?.m_zh# found by ID
#     ret.m_zh = jdoc2.m_zh
#   else
#     ret.m = replaceM(m)
#   return resp.send ret
#console.log req.body
# { 'loc[]': [ '43.72199', '-79.45175' ] }
# { 'sw[]': [ '43.721990000000005', '-79.45175' ],
# 'ne[]': [ '43.721990000000005', '43.721990000000005' ] }
# the result of this was overwritten by sw and ne, always bndsChanged
# TODO: $geoNear
# if loc = q.loc or q['loc[]']
#   project = {
#     $project: {
#       dis: { $add: [
#         {$pow:[
#           { $subtract: [ "$lat", loc[0]] }
#           ,2]},
#         {$pow:[
#           { $subtract: [ "$lng", loc[1] ] }
#           ,2]}
#       ] }
#       # document: "$$ROOT"
#     }
#   }
#   for i in ['_id','id','addr','city','prov','zip','cnty','ptp','thumb','type','title','m','ref1','ref2','dt','lat','lng','llq']
#     project.$project[i] = 1
#   mq = [
#     project,
#     { $sort: { dis:1 } },
#     { $limit : limit }
#   ]
#   Stigmatized.aggregate mq,(err,ret)->
#     return findError err if err
#     resp.send {cnt:ret.length,items:ret}
# if loc = q.loc or q['loc[]']
#   a = "(POW((lat - ?),2) + POW((lng - ?),2))"
#   sql = "SELECT *, #{a} as dis FROM stigmatized ORDER BY dis LIMIT 50"
#   params = [loc[0],loc[1]]
#   return mysql_client.query sql,params,(err,ret)->
#     return findError err if err
#     resp.send {cnt:ret.length,items:ret}
# else if (sw = q.sw or q['sw[]']) and (ne = q.ne or q['ne[]'])
#   page = q.p or 0
#   sqlCnt = "SELECT count(*) as cnt FROM stigmatized"
#   sqlGet = "SELECT * FROM stigmatized"
#   sqlWhere = " WHERE lat > ? AND lat < ? AND lng > ? AND lng < ?"
#   sqlOrder = " ORDER BY dt DESC LIMIT ? OFFSET ?"
#   params = [sw[0],ne[0],sw[1],ne[1]]
#   mysql_client.query (sqlCnt + sqlWhere),params,(err,r)->
#     return findError err if err
#     params.push limit
#     params.push limit * page
#     mysql_client.query (sqlGet + sqlWhere + sqlOrder),params,(err,ret)->
#       resp.send {cnt:r[0].cnt,items:ret}


# switch grade
#   when '3',3
#     where.push " g3rank >= 0 "
#     orderby = ['g3rank','firank']
#     fields = ['g3rank','g3total','g3stu','firank','firate','filast5rank','fiincome','fitotal','filast5total']
#   when '6',6
#     where.push " g6rank >= 0 "
#     orderby = ['g6rank','firank']
#     fields = ['g6rank','g6total','g6stu','firank','firate','filast5rank','fiincome','fitotal','filast5total']
#   when '9',9
#     where.push " g9acrank >= 0 "
#     orderby = ['g9acrank','firank']
#     fields = ['g9acrank','g9actotal','g9acm','g9aprank','g9aptotal','g9apm','g9acstu','g9apstu','firank','firate','filast5rank','fiincome','fitotal','filast5total']
#   when 'fi'
#     where.push " firank >= 0 and fitotal > 1000 "
#     orderby = ['firank']
#     fields = ['firank','firate','filast5rank','fiincome','fitotal','filast5total']
#   when 'hfi'
#     where = [" firank >= 0 and fitotal < 1000 "]
#     orderby = ['firank']
#     fields = ['firank','firate','filast5rank','fiincome','fitotal','filast5total']
#   else
#     where.push " firank >= 0 "
#     orderby = ['firank']
#     fields = ['firank','firate','filast5rank','fiincome','fitotal','filast5total']
#     #orderby = ["g9acrank",'g6rank','g3rank']
#     #fields = ['g3rank','g3total','g3stu','g6rank','g6total','g6stu','g9acrank','g9actotal','g9acm','g9acstu','g9aprank','g9apm','g9aptotal','g9apstu','firank','firate','filast5rank','fiincome','fitotal','filast5total']      warn "Unknown grade: #{grade}"
# if city? and city isnt 'all'
#   where.push "city = ?"
#   param.push city
#   # use original city names
#   #mycities = {}
#   #if schs = school_city_object[city]?.schools
#   #  for s in schs
#   #    mycities[s.city] = 1
#   #  mycities = Object.keys mycities
#   #  where.push "city IN (#{('?' for i in [1..mycities.length]).join ','})"
#   #  param = param.concat mycities
# mysql.query "SELECT MAX(yearmark) as ym FROM school_scores WHERE #{where.join ' AND '}",param,(err,ymret)->
#   debug ymret
#   if err
#     console.error err
#     ym = current_yearmark
#   ym = ymret[0].ym
#   where.push " yearmark = ? "
#   param.push ym
#   sql = "SELECT school_id,name,city,sbid,#{fields.join ','} FROM school_scores WHERE #{where.join ' AND '} ORDER BY #{orderby.join(',')} LIMIT #{limit} OFFSET #{skip}"
#   debug sql
#   debug param
#   mysql.query sql,param,(err,list)->
#     if err
#       console.error err
#       return cb []
#     mysql.query "SELECT count(*) as c FROM school_scores WHERE #{where.join ' AND '}",param,(err,ret)->
#       if err then console.error err
#       debug ret
#       return cb list, ret[0].c