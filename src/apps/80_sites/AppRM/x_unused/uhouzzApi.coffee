# appConfig = CONFIG()
# UserListings = COLLECTION 'chome', 'user_listing'
# APP 'api'
# POST 'uhouzz',(req,resp)->
#   findError = (msg)->
#     console.error "Error:#{msg}"
#     resp.send {ok:0,msg:msg.toString()}
#   # TODO: filter IPs
#   unless (act = req.param('act')) is 'status-update'
#     return findError "Unsupported action command #{act}"
#   unless data = req.param('data')
#     return findError "No Data"
#   try
#     data = JSON.parse data
#   catch e
#     return findError "Bad JSON format"
#   unless data.ids and Array.isArray data.ids
#     return findError "Bad IDs"
#   unless data.status is 'Published' or data.status is 'Rejected'
#     return findError "Unsupported status type #{data.status}"
#   set = "uhouzz.status":data.status
#   if data.reason
#     set["uhouzz.reason"] = data.reason.toString()
#   UserListings.updateMany {_id:$in:data.ids},{$set:set},{multi:1},(err)->
#     if err then return findError err.toString()
#     resp.send {ok:1}
