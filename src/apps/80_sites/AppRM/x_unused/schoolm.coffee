# # TODO: remove this file, not used
# helpers = INCLUDE 'lib.helpers'
# Areas = COLLECTION 'vow','area'
# Schools = COLLECTION 'vow','school'
# SchoolBoards = COLLECTION 'vow','school_board'
# current_yearmark = "2014-2015"
# debug = console.log
# uniform_string = (s)-> s.toLowerCase().replace /[^0-9a-z\:\-]/g,'-'

# getSchoolDetail = (schid,cb)->
#   Schools.findOne {_id:schid},(err,school)->
#     if err
#       console.error err
#       return cb null
#     if not school? then return cb null
#     Areas.findOne {$or:[{srhkey:uniform_string(school.city)},{inclusion:school.city}]},(err,city)->
#       if err then console.error err
#       if city? then school.city_info = city
#       SchoolBoards.findOne {_id:school.sbid},(err,board)->
#         if err then console.error err
#         if board? then school.board_info = board
#         return cb school

# APP '1.5'
# APP 'sch',true
# getGoogleAPILink = DEF 'getGoogleAPILink'
# GET 'home',(req,resp)->
#   loc = req.param 'loc'
#   googleapiJs = getGoogleAPILink(req,{tp:'googleapi',cn:req.isChinaIP(),src:'schhome'})
#   resp.ckup 'sch',{loc:loc, googleapiJs:googleapiJs}
# GET 'd.:schid',(req,resp,next)->
#   schid = req.param 'schid'
#   html = null
#   schid = parseInt schid
#   getSchoolDetail schid,(school)->
#     school ?= {}
#     school.sbj = req.l10n("School Detail ") + (school.nm or '')
#     school.dscp = req.l10n("Fraser Institute/EQAO. For Sale/For Rent Properties.")
#     resp.ckup 'detail',school

# POST 'search',(req,resp)->
#   d = req.body
#   Schools.findToArray {'bns.sw.0':{$lte:d.loc[0]},'bns.sw.1':{$lte:d.loc[1]},'bns.ne.0':{$gte:d.loc[0]},'bns.ne.1':{$gte:d.loc[1]},IsActive:1},
#     {fields:{_id:1,addr:1,bns:1,catholic:1,city:1,nm:1,loc:1,url:1,addr:1,tel:1,fiurl:1,gf:1,gt:1,fi:1,ef:1,fraser:1,eqao:1,ele:1,mid:1,hgh:1},sort:{catholic:1,ef:1,fi:1,gf:1}},
#     (err,results)->
#       if err then console.error err
#       if not results? then return resp.send {e:err.toString()}
#       bnds = []
#       schs = {}
#       for sch in results
#         for bn in sch.bns
#           if helpers.isInBoundary bn.bn,d.loc
#             if d.useSch # use school info, instead of boundary info
#               for k in ['gf','gt','ef','fi','ele','mid','hgh','catholic']
#                 delete bn[k]
#             bn = helpers.shallowExtendObject bn,sch
#             delete bn.bns
#             delete bn.bn
#             delete bn.ref
#             bn.c = bn.catholic
#             delete bn.catholic
#             delete bn.fraser
#             delete bn.eqao
#             bnds.push bn
#             schs[sch._id] = sch # only school in boundary
#         delete sch.bns
#         if (frs = sch.fraser?[0])?
#           sch.firate = frs.firank + '/' + frs.fitotal
#         if (eqao = sch.eqao?[0])?
#           if eqao.g6rank and eqao.g6total
#             sch.eqaorate = eqao.g6rank + '/' + eqao.g6total
#           else if eqao.g3rank and eqao.g3total
#             sch.eqaorate = eqao.g3rank + '/' + eqao.g3total
#           else if eqao.g9rank and eqao.g9total
#             sch.eqaorate = eqao.g9rank + '/' + eqao.g9total
#         delete sch.fraser
#       bnds.sort (a,b)->
#         r = a.c - b.c
#         if r is 0 then r = a.ef - b.ef
#         if r is 0 then r = a.fi - b.fi
#         if r is 0 then r = a.gf - b.gf
#         r
#       resp.send {bnds:bnds,schs:schs}


# # not used
# LAYOUT 'sch',->
#   doctype '5'
#   html ngApp:'schSrv',->
#     head ->
#       meta charset: 'utf-8'
#       meta name:'viewport',content:'initial-scale=1, minimum-scale=1.0, maximum-scale=1, user-scalable=no'
#       meta name:"apple-mobile-web-app-capable",content:"yes"
#       meta name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"
#       title -> _ "School Search"
#       meta name: 'description', content:_("GPS or Click to find home school, ranking and school for sale/for rent properties")
#       css 'http://code.jquery.com/mobile/1.3.1/jquery.mobile-1.3.1.min.css'
#     block 'body',->
#       body ->
#         text @content()

# VIEW 'show-school-info',->
#   div 'itemscope',itemtype:"http://data-vocabulary.org/Organization",class:'school-info',->
#     h1 itemprop:'name',-> text @nm
#     span class:'city',-> text _ @city
#     div ->
#       span itemprop:'address',class:'address',-> "#{@addr}, #{@city} #{@prov} #{@nation} #{@zip}"
#     div ->
#       label -> text _("Tel") + ":"
#       a itemprop:'telephone',class:'tel',href:('tel:'+@tel),-> @tel
#       label -> text _("Fax") + ":"
#       span itemprop:'faxNumber',class:'fax',-> @fax
#     div ->
#       a itemprop:'url',href:@url,target:'_blank',-> @url
#     div ->
#       div class:'school-grade',->
#         span -> text if @gf then @gf else _ "Kindergarden"
#         span -> _ "to"
#         span -> text @gt
#       div ->
#         if @ele then span -> _ "Elementary",'School'
#         if @mid then span -> _ "Middle",'School'
#         if @hgh then span -> _ "Secondary",'School'
#         if @pub then span -> _ "Public School"
#         if @catholic then span -> _ "Catholic School"
#         text "("
#         if @eng then span -> _ "English"
#         if @fi then span -> _ "French Immersion"
#         if @ef then span -> _ "Extended French"
#         text ")"
#       input id:'id_school_id',type:'hidden',value:@_id

# VIEW 'show-school-fraser',->
#   titlemap = {firank:'Rank',fitotal:'Total',fiincome:"Parents' Income",firate:"Rating",filast5rank:"Last 5 Year Rank",filast5total:"Last 5 Year Total",fiesl:"ESL %",fispecial:"Special Edu Needs %",yearmark:"Year"}
#   titles = ['yearmark','firank','filast5rank','firate','fiincome','fiesl','fispecial']
#   div class:'title',-> text _ "Fraser Institute Ranking and Rating"
#   table ->
#     tbody ->
#       for t in titles
#         tr ->
#           th -> text _ titlemap[t]
#           for r in @fraser
#             td ->
#               if t.slice(-4) is 'rank'
#                 text r[t] + '/' + r[t.slice(0,-4) + 'total']
#               else if t is 'fiincome'
#                 text (r[t] / 1000) + 'k'
#               else
#                 text r[t]
# VIEW 'show-school-eqao',->
#   titlemap = {yearmark:"Year",rank:'Rank',total:'Total',aw:"Writing",ar:"Reading",am:"Math",stu:"Students",acm:"Academic Math",apm:"Applied Math",acstu:"Academic Students",apstu:'Applied Students',acrank:'Academic Rank',aprank:'Applied Rank'}
#   titlesg3 = ['yearmark','rank','ar','aw','am','stu']
#   titlesg9 = ['yearmark','rank','acm','acstu','acrank','apm','apstu','aprank']
#   titles =
#     g3: titlesg3
#     g6: titlesg3
#     g9: titlesg9
#   show_it = (g,eqao)->
#     eqao.length = 3 if eqao.length > 3
#     div class:'title',-> text "EQAO Scores #{g.toUpperCase()}"
#     table ->
#       tbody ->
#         for t in titles[g]
#           tr ->
#             th -> text _ titlemap[t]
#             for r in eqao
#               td ->
#                 if t.slice(-4) is 'rank'
#                   text Math.round(r[g + t] or 0) + '/' + r[g + t.slice(0,-4) + 'total']
#                 else if t is 'yearmark'
#                   text r[t]
#                 else
#                   text Math.round(r[g + t] or 0)
#   orig_eqao = @eqao
#   if Array.isArray orig_eqao
#     eqao = orig_eqao
#   else
#     eqao = []
#     for k,v of orig_eqao
#       if /\d+\-\d+/.test k
#         v.yearmark = k
#         eqao.push v
#     eqao.sort (a,b)-> a.yearmark < b.yearmark
#   if eqao?[0]?.g3total > 0
#     show_it 'g3',eqao
#   if eqao?[0]?.g6total > 0
#     show_it 'g6',eqao
#   if eqao?[0]?.g9total > 0
#     show_it 'g9',eqao

# VIEW 'detail',->
#   div dataRole:'page',id:'schdetailview',dataTheme:'c',ngmSharedController:'ctrl:schCtrl',->
#     div dataRole:'header',dataTheme:'b',dataPosition:"fixed",->
#       #a href:"/sch/home",dataIcon:"back",dataIconpos:"notext",dataCorners:true,dataTheme:"a",title:_('Back'),-> _ 'Back'
#       h1 -> @nm
#     div dataRole:'content',->
#       div -> text ckup 'show-school-info',@
#       if @fraser?.length > 0
#         #div class:'title',-> _ "Fraser Institute"
#         div -> text ckup 'show-school-fraser',@
#       if @eqao?.length > 0
#         #div class:'title',-> _ "EQAO Scores"
#         div -> text ckup 'show-school-eqao',@
#     div dataRole:'footer',dataPosition:"fixed",-> div class:'ad-footer',->



# VIEW 'sch',->
#   #js '/js/jma/jquery-mobile-angular-adapter-standalone.js'
#   #js '/js/angular-resource.min.js'
#   #js '/js/jquery.cookie.min.js'
#   js @googleapiJs
#   if @req.isChinaIP()
#     input id:'china-ip',type:'hidden',name:'chinaIP',value:'1'
#   js '/js/google/markerwithlabel_packed.js'
#   js '/js/loading-bar.min.js'
#   css '/css/loading-bar.css'
#   div class:'wrapper',id:'schmapview',ngApp:'schSrv',ngController:'schCtrl',->
#   #div dataRole:'page',id:'schmapview',dataTheme:'c',ngApp:'schSrv',ngmSharedController:'ctrl:schCtrl',->
#     if @loc then input type:'hidden',id:'loc_pos',value:@loc
#     header class:'bar bar-nav',->
#       a class: 'icon icon-left-nav pull-left', href:'javascript:goBack();'
#       h1 class:'title',-> _ "Find Schools"
#       # text ckup 'headerbarUserModalBtn'
#     div class: 'bar bar-standard bar-footer', ->
#       a href:'javascript:void(0);',class:'fa fa-map-marker pull-left func-button',ngClick:'locateMe()' #TODO:
#       a href:'/1.5/map',class: 'fa fa-home pull-right func-button',dataIgnore:'push',style:'font-size: 22px;line-height: 17px;'
#       a href:'javascript:void(0);',class: 'fa fa-list pull-right func-button',dataIgnore:'push',ngClick:"toggleModal('schList')"
#     div class: 'bar bar-standard bar-header-secondary', ->
#       addrparam = id:'id_addr',type:'search',ngModel:'addr',placeholder:_('Current Location'),style:'width:90%;'
#       if addr = @req.param('addr') then addrparam.ngInit = "addr='#{addr.replace("'","\\'")}'"
#       input addrparam
#       span class:"icon icon-search pull-right",ngClick:'search()'
#     div class:'content',->
#       div class:'fullHeight',->
#         #div id:'id_info_bar',->
#         div id:'id_map'

#     div id:'schList',class:'modal',->
#       header class:'bar bar-nav',->
#         a class:'icon icon-close  pull-right detailModal',dataIgnore:'push',ngClick:"toggleModal('schList')"
#         h1 class:'title',-> '{{bnds.length}} ' + _('Schools')
#       div class:"content",->
#         ul class:'table-view',->
#           li ngRepeat:'s in bnds',class:'table-view-cell sm-right',->
#             div ngClick:'open(s._id)',style:'width:100%;',->
#             # a style:'display:block',ngHref:'/1.5/school/all#/id.{{s._id}}',style:'width:100%;',dataIgnore:'push',->
#               div class:'heading' ,->
#                 span style:'color:blue;',->
#                   # text '{{schs[s._id].nm}}'
#                   text '{{s.nm}}'
#                 span class:'badge pull-right' ,style:'color:white;font-size:0.7em;background-color:#898787;',->
#                   # text "schs.(s.id).nm"
#                   text "{{schs[s._id].firate}}"
#               div class:'small ', ->
#                 span class:'pull-right',style:'color:#666;font-size:0.6em',->
#                   text '{{s.gf}}-{{s.gt}} '

#                   text _('Grades','school-grade')
#                   text ' '
#                   text _('Only','school-grade')

#                 span style:'font-size:0.7em;color:#666;margin-right:10px;',->
#                   text "{{s.addr}}"
#                 span class:'disdistance',style:'color:red;font-size:0.7em;',->
#                   text "{{s.dis}}km"
#               div class:'', style:'font-size:0.6em;margin-top:10px;',->
#                 span class:'',style:'margin-right:5px',->
#                   i class:'fa fa-check-square-o' ,ngShow:'s.ele'
#                   i class:'fa fa-square-o', ngHide:'s.ele'
#                   text ' '
#                   text _ 'Elementary'
#                 span style:'margin-right:5px',->
#                   i class:'fa fa-check-square-o', ngShow:'s.mid'
#                   i class:'fa fa-square-o', ngHide:'s.mid'
#                   text ' '
#                   text _ 'Middle'
#                 span style:'margin-right:5px',->
#                   i class:'fa fa-check-square-o',ngShow:'s.hgh'
#                   i class:'fa fa-square-o', ngHide:'s.hgh'
#                   text ' '
#                   text _ 'Secondary'
#               div style:'font-size:0.6em;',->
#                 span ngShow:'s.c',->
#                   img src:'/img/sch_catholic.png', class:'img-sm'
#                   text 'Catholic'
#                 span ngShow:'s.ef',->
#                   img src:'/img/sch_ef.png' , class:'img-sm'
#                   text 'Extended French'
#                 span ngShow:'s.fi',->
#                   img src:'/img/sch_fi.png' , class:'img-sm'
#                   text 'French Immersion Program'


#     div id:'schSearch',class:'modal modal-short',->
#       div class:'',style:'font-weight:bold;margin-left:5%',->
#         text _('Search Home School')
#       hr style:'width:90%;margin-top:10px; margin-bottom:5px;'

#       div class:'latlng',style:'margin-left:10px;',->
#         text _('Current Position')
#         text '  {{addr}}'
#       button class:'btn btn-block btn-positive btn-long',ngClick:'schSearch()',-> _('OK')
#       button class:'btn btn-block btn-long',ngClick:'schSearchCancel()',-> _('Cancel')

#     div id:'bndList',class:'modal modal-row-4',->
#       header class:'bar bar-nav', ->
#         h1 class:'title', style:'color:black;background-color:white;text-align:left;padding-left:15px;',->
#           text _ "Select Boundary"
#       div class:'content',->
#         hr style:'width:90%;margin-top:0px; margin-bottom:0px;'

#         div class:'table-view',style:"",->

#           div ngRepeat:'s in sch.bnds',class:' sm-right',style:'padding: 5px 10px 0px 15px;',->
#             span ngClick:'selBnd(s,1)',->
#               span class:'grade pull-right',->
#                 text '{{s.gf}}-{{s.gt}} '
#                 text _ 'Grades'
#                 text ' '
#                 text _ 'Only'
#               # span class:'distance',-> '{{s.dis}}km'
#               span class:'name',-> '{{s.nm}}'
#               img src:'/img/sch_catholic.png',ngShow:'s.c',class:'img-sm'
#               img src:'/img/sch_ef.png',ngShow:'s.ef',class:'img-sm'
#               img src:'/img/sch_fi.png',ngShow:'s.fi',class:'img-sm'
#           div ngHide:'sch.bnds.length > 5',class:' sm-right',style:'padding: 5px 10px 0px 15px;height:21px;',->
#             text ''
#           div ngHide:'sch.bnds.length > 4',class:' sm-right',style:'padding: 5px 10px 0px 15px;height:21px;',->
#             text ''
#           div ngHide:'sch.bnds.length > 3',class:' sm-right',style:'padding: 5px 10px 0px 15px;height:21px;',->
#             text ''

#         hr style:'width:90%'

#         button class:'btn btn-block btn-long ', id:'no-border',style:'border: 1px solid white;', ngClick:"toggleModal('bndList','close')",style:'',-> _ "Cancel"

#     div id:'schPreview',class:'modal modal-short',->
#       header class:'bar bar-nav',->
#         h1 class:'title', style:'font-size:15px;',-> '{{bnd.fnm}}'
#         a class:'icon icon-close pull-right' ,ngClick:"toggleModal('schPreview','close')"

#           # a href:'#schLegend',->
#           #   img src:'/img/sch_catholic.png',ngShow:'bnd.c'
#           #   img src:'/img/sch_ef.png',ngShow:'bnd.ef'
#           #   img src:'/img/sch_fi.png',ngShow:'bnd.fi'
#       div class:'centent',->

#         a ngHref:'/1.5/school/all#/id.{{sch._id}}', dataIgnore:'push' ,class:'content', style:'margin:45px 10px 10px 10px;height:45px;',->
#           div class:'small ', style:'margin-left:0px ; margin-top:5px;',->
#             span class:'badge pull-right' ,style:'color:white;font-size:0.82em;margin-top: 3px;background-color:#898787;',->
#               # text "schs.(s.id).nm"
#               text "{{sch.firate}}"

#             span style:'font-size:0.75em;color:#666;margin-right:10px;',->
#               text "{{sch.addr}}"
#             span class:'disdistance',style:'color:red;font-size:0.7em;',->
#               text "{{bnd.dis}}km"

#           div class:'small',->
#             span class:'pull-right',style:'color:#666;font-size:0.6em',->
#               text '{{bnd.gf}}-{{bnd.gt}} '

#               text _('Grades','school-grade')
#               text ' '
#               text _('Only','school-grade')

#           # # br()
#         div class:'bottom-content',style:'margin-top:85px;',->
#           div class:'', style:'font-size:0.7em;margin-top:10px;margin-left:10px;',->
#             span class:'',style:'margin-right:5px',->
#               i class:'fa fa-check-square-o' ,ngShow:'bnd.ele'
#               i class:'fa fa-square-o', ngHide:'bnd.ele'
#               text ' '
#               text _ 'Elementary'
#             span style:'margin-right:5px',->
#               i class:'fa fa-check-square-o', ngShow:'bnd.mid'
#               i class:'fa fa-square-o', ngHide:'bnd.mid'
#               text ' '
#               text _ 'Middle'
#             span style:'margin-right:5px',->
#               i class:'fa fa-check-square-o',ngShow:'bnd.hgh'
#               i class:'fa fa-square-o', ngHide:'bnd.hgh'
#               text ' '
#               text _ 'Secondary'

#           #   # span class:'grade',->

#           div style:'font-size:0.7em;margin-bottom:10px;margin-left:10px;',->
#             span ngShow:'bnd.c',->
#               img src:'/img/sch_catholic.png', class:'img-sm',style:'vertical-align: bottom;'
#               text 'Catholic'
#             span ngShow:'bnd.ef',->
#               img src:'/img/sch_ef.png' , class:'img-sm',style:'vertical-align: bottom;'
#               text 'Extended French'
#             span ngShow:'bnd.fi',->
#               img src:'/img/sch_fi.png' , class:'img-sm',style:'vertical-align: bottom;'
#               text 'French Immersion Program'
#             span class:'pull-right',style:'font-size:1.5em;z-index:20;margin-right:15px;',->
#               a ngHref:"/1.5/map-school?sid={{bnd._id}}&m=fc",dataIgnore:'push',->
#                 i class:'fa fa-home'

#     div id:'addrList',class:'modal',->
#       header class:'bar bar-nav',-> h1 class:'title',-> _ "Select a location"
#       ul class:'table-view',->
#         li ngRepeat:'addr in addrs',ngClick:'selAddr(addr)',->
#           span -> '{{addr.formatted_address}}'

#     div id:'schLegend',class:'modal modal-short',->
#       div ->
#         img src:'/img/sch_catholic.png'
#         span -> _('Catholic School')
#       div ->
#         img src:'/img/sch_ef.png'
#         span -> _ "Extended French Program"
#       div ->
#         img src:'/img/sch_fi.png'
#         span -> _ "French Immersion Program"
#       button class:'btn btn-block',ngClick:"toggleModal('schLegend','close')",-> _ "Cancel"
#     div id:'errorPopup',class:'modal',->
#       div ngBindHtmlUnsafe:'error',-> '{{error}}'
#       button class:'btn btn-block',ngClick:"toggleModal('errorPopup','close')",-> _ "Cancel"



# # #schPreview
# #     min-width 270px
# #     text-align center
# #     padding 3px
# #     img
# #       vertical-align middle
# STYLUS 'sch',"""
# #no-border
#   border none

# .img-sm
#   height: 24px;
#   width: 24px;
# .popup-container
#   max-height 350px
#   overflow-y auto
# #id_addr
#   text-align center
# #schmapview
#   .counter
#     padding 8px
#     margin 3px
#   .ui-content
#     padding 0
#     margin 0
#   span.grade
#     margin 0 3px 0 0
#     letter-spacing 0px
#     font-size 11px
#     color #666
#   span.distance
#     margin 0 3px 0 0
#     font-size 13px
#     color red
#   span.address
#     font-size 13px
#     color black
#   span.telephone
#     font-size 13px
#   span.name
#     font-size 15px
#     color blue
#     margin 0 2px
#   #id_map
#     min-height 200px
#     height 100%
#     overflow hidden
#     .marker-label
#       color black
#       font-size 11px
#       border 1px solid green
#       white-space nowrap
#       background-color white
#   #id_info_bar
#     height 44px
#     a
#       text-decoration none
#       img
#         vertical-align middle
#     line-height 38px
#   #id_search_bar
#     table
#       width 100%
#       border-spacing 0
#   #schList, #bndList, #schLegend
#     img
#       vertical-align middle
#     .ui-li
#       padding 0px 5px
#       line-height 42px
#       height 44px

# #schdetailview
#   .title
#     text-align center
#     font-size 20px
#     font-weight bold
#     background-color #aaa
#   table
#     th
#       font-size 14px
#       background-color #ccc
#       text-align right
#       padding 1px 3px
#     td
#       font-size 16px
#       border-bottom 1px solid #ccc
#       text-align center
# #schSearch
#   padding 5px
#   .latlng
#     text-align center
#     padding 4px

# #errorPopup
#   padding 5px
# """
# #border 1px solid #ccc

# COFFEE 'sch',->
#   debug = (e)-> console?.log e
#   appSch = angular.module 'schSrv',['angular-loading-bar']#,'ngAnimate',]
#   #cri = {myurl:'search'}
#   #app = angular.module('schSrv', ['ngResource']).factory 'SchSearch',['$resource',($resource)->
#   #  $resource '/sch/:myurl',{},{query:{method:'POST', params:cri, isArray:false},get:{method:'POST',params:{myurl:'getDetail'}}}]
#   #app.config ['$compileProvider',($compileProvider)->
#   #  $compileProvider.urlSanitizationWhitelist /^\s*(https?|ftp|mailto|file|tel):/
#   #  ]
#   #schCtrl = ['$scope','SchSearch','$location',($scope,SchSearch,$location)->
#   cri = {}
#   appSch.controller 'schCtrl',['$scope','$http','$location',($scope,$http,$location)->
#     gmap = null
#     curMarker = null
#     schMarker = null
#     clkMarker = null
#     loc = [43.618865,-79.374097]
#     if loc_pos = document.getElementById('loc_pos')?.value
#       try
#         loc1 = (parseFloat(x) for x in loc_pos.split(','))
#         if loc1.length is 2 then loc = loc1
#         debug loc
#       catch e
#     round = (n,d = 1)-> Math.round(n * Math.pow(10,d)) / Math.pow(10,d)
#     $scope.toggleModal = (a,b)-> toggleModal a,b
#     $scope.schSearch = ->
#       toggleModal 'schSearch','close'
#       $scope.search()
#     $scope.schSearchCancel = ->
#       toggleModal 'schSearch','close'
#       $scope.addr = $scope.lastAddr
#       clkMarker?.setMap null
#     $scope.search = ->
#       if $scope.addr?.length > 0
#         if (addr = $scope.addr) and (/(\-?[0-9\.]+),(\-?[0-9\.]+)/.test addr)
#           loc = (parseFloat(v) for v in $scope.addr.split(','))
#           search()
#         else
#           url = "http://maps.googleapis.com/maps/api/geocode/json?address=#{addr}&sensor=false"
#           $http.get(url).success (result,status)->
#             if result.status isnt 'OK' then return debug result.status
#             #debug result.results
#             if result.results.length > 1
#               $scope.addrs = result.results
#               try
#                 $scope.$apply()
#               catch e
#               addrList = $('#addrList')
#               #addrList.popup()
#               #addrList.trigger 'create'
#               #addrList.popup 'open',{transition:'pop'}
#               toggleModal 'addrList','open'
#               return null
#             else
#               loc = result.results[0].geometry.location
#               loc = [loc.lat,loc.lng]
#               search()
#       else
#         locateMe()
#     $scope.selAddr = (addr)->
#       loc = addr.geometry.location
#       loc = [loc.lat,loc.lng]
#       toggleModal 'addrList','close'
#       search()
#     search = ->
#       #show_loading()
#       cri.loc = loc
#       clkMarker?.setMap null
#       clkMarker = null
#       curMarker?.setMap null
#       curMarker = new google.maps.Marker {
#         position: new google.maps.LatLng loc[0],loc[1]
#         optimized: false
#         map: gmap }
#       if $scope.schs?
#         for sid,s of $scope.schs
#           s.marker?.setMap null
#           delete s.marker
#       #ret = SchSearch.query cri,->
#       #  gotResult()
#       #  hide_loading()
#       schSrhHttp = $http.post '/1.5/sch/search',cri
#       schSrhHttp.success (data)-> gotResult data
#       gotResult = (ret)->
#         if ret.err
#           $scope.error = ret.err
#           setTimeout (->
#             toggleModal 'errorPopup','open'
#             ),250
#           return null
#         bnds = []
#         schs = {}
#         debug ret
#         if p = curMarker?.getPosition()
#           for r in ret.bnds
#             r.latlng = new google.maps.LatLng r.loc[0],r.loc[1]
#             r.dis = Math.ceil(google.maps.geometry.spherical.computeDistanceBetween(r.latlng,p) / 100) / 10
#             r.fnm = r.nm
#             r.nm = r.fnm.replace( /\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi , '')
#             if r.gf is 0 then r.gf = 'K'
#             bnds.push r
#             sch = schs[r._id] ?= {marker: getMarker(r),bnds:[]}
#             ctx = sch.marker.get 'labelContent'
#             ctx += "<div>#{r.gf}-#{r.gt}#{if r.fi then '[FI]' else ''}#{if r.ef then '[EF]' else ''}</div>"
#             sch.marker.set 'labelContent',ctx
#             sch.bnds.push r
#         for sid,sch of ret.schs
#           schs[sid] ?= sch
#           for k,v of sch
#             schs[sid][k] = v
#           schs[sid].firate ?= 'N/A'
#         #debug schs
#         $scope.schs = schs
#         $scope.bnds = bnds
#         if ret.bnds.length > 0
#           #setTimeout (-> toggleModal('schList','open')),250
#           # select the first one by default.
#           $scope.selBnd ret.bnds[0],false,false # not resize, no popup
#     getIcon2 = (r,sel)->
#       return '/img/sch_' + (if r.c then 'c' else 'p') + (if sel then '_sel' else '') + '.png'
#     getMarker = (r,sel)->
#       r.marker = new google.maps.Marker { #new MarkerWithLabel {
#         position: r.latlng
#         icon: getIcon2 r,sel
#         flat: false
#         labelAnchor: new google.maps.Point(16, 0)
#         labelContent: ''
#         labelClass: "marker-label"
#         labelInBackground: false
#         optimized: false
#         map: gmap}
#       google.maps.event.addListener r.marker,'click',->
#         if (sch = $scope.schs[r._id])
#           $scope.sch = sch
#           if sch.bnds.length is 1
#             $scope.selBnd r,true
#           else
#             $scope.$apply()
#             toggleModal 'bndList','open'
#       r.marker
#     $scope.open = (id)->
#       window.location = "/1.5/school/all#/id." + id;
#     $scope.selBnd = (s,keepBoundary,showInfo = true)->
#       clkMarker?.setMap null
#       clkMarker = null
#       if sid = $scope.bnd?._id
#         $scope.schs[sid]?.marker?.set 'icon',getIcon2($scope.bnd)
#       $scope.bnd = s
#       toggleModal 'schList','close'
#       toggleModal 'bndList','close'
#       # show map
#       show_school_bounds keepBoundary
#       sid = s._id
#       $scope.sch = $scope.schs[sid]
#       $scope.sch?.marker?.set 'icon',getIcon2(s,true)
#       try
#         $scope.$apply()
#       catch e
#       if showInfo
#         setTimeout (-> toggleModal 'schPreview','open'),250
#     sch_overlay = null
#     show_school_bounds = (noFit)->
#       sch_overlay?.setMap null
#       if (bndid = $scope.bnd.bnid) and (sw = $scope.bnd.sw) and (ne = $scope.bnd.ne)
#         schbounds = new google.maps.LatLngBounds(new google.maps.LatLng(sw[0],sw[1]),new google.maps.LatLng(ne[0],ne[1]))
#         schboundsImg = "http://realmaster.com/schimgs/#{$scope.bnd._id}_#{bndid}.png";
#         sch_overlay = new google.maps.GroundOverlay schboundsImg,schbounds,{clickable:false}
#         sch_overlay.setMap gmap
#         if not noFit
#           gmap.fitBounds schbounds
#     #schIcon = new google.maps.MarkerImage("http://chart.apis.google.com/chart?chst=d_map_pin_letter&chld=%E5%AD%A6|99FF99")
#     schIcon = "/img/mapmarkers/yellow_MarkerS.png"
#     initView = ->
#       opts =
#         center: new google.maps.LatLng loc[0],loc[1]
#         zoom: 13
#         mapTypeId: google.maps.MapTypeId.ROADMAP
#         #draggableCursor:'crosshair'
#         disableDoubleClickZoom: true
#       gmap = new google.maps.Map document.getElementById('id_map'),opts
#       google.maps.event.addListener gmap,'click',(e)->
#         loc = e.latLng
#         $scope.lastAddr = $scope.addr
#         $scope.addr = round(loc.lat(),5) + ',' + round(loc.lng(),5)
#         clkMarker?.setMap null
#         clkMarker = new google.maps.Marker {
#           position: e.latLng
#           optimized: false
#           map: gmap }
#         $scope.$apply()
#         toggleModal 'schPreview','close'
#         toggleModal 'schSearch','open'
#     if app?.geolocation
#       locationService = app.geolocation
#     else
#       locationService = navigator.geolocation
#     # old
#     #$scope.locateMe = locateMe = ->
#     #  navigator.geolocation.getCurrentPosition (l)->
#     #    loc[0] = l.coords.latitude
#     #    loc[1] = l.coords.longitude
#     #    gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
#     #    search()
#     # need to wait for height been set
#     _getSavedLoc = ->
#       if gpsLoc = localStorage.lastGPSLoc # use last cache, but still do real search
#         return (parseFloat s for s in gpsLoc.split ',')
#       null
#     realLocateMe = ->
#       success = (l)->
#         #hideLocating()
#         locOld = _getSavedLoc()
#         loc[0] = l.coords.latitude
#         loc[1] = l.coords.longitude
#         localStorage.lastGPSLoc = '' + loc[0] + ',' + loc[1] # save to local cache for next use
#         return if locOld and (Math.abs(locOld[0] - loc[0]) < 0.0001) and (Math.abs(locOld[1] - loc[1]) < 0.0001)
#         gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
#         search()
#       failed = (e)->
#         #hideLocating()
#         console?.log e
#       locationService.getCurrentPosition success,failed,{enableHighAccuracy: true,timeout: 15000}
#     $scope.locateMe = locateMe = ->
#       #showLocating()
#       if gpsLoc = _getSavedLoc()
#         loc = gpsLoc
#         gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
#         search()
#       realLocateMe()
#     inited = false
#     heightSetDone = ->
#       if inited then return null
#       inited = true
#       initView()
#       if loc_pos
#         #console.log loc
#         gmap?.setCenter new google.maps.LatLng loc[0],loc[1]
#         search()
#       else
#         locateMe()

#     #ua = navigator.userAgent
#     #iphone = ~ua.indexOf('iPhone') || ~ua.indexOf('iPod')
#     #ipad = ~ua.indexOf('iPad')
#     #ios = iphone or ipad
#     #android = ~ua.indexOf 'Android'
#     fullHeight = (self)->
#       tHeight = window.outerHeight or screen.height #document.getElementById('schmapview').outerHeight()
#       #debug tHeight
#       #hfHeight = 0
#       #self.find('[data-role="header"], [data-role="footer"]').each ->
#       #  hfHeight += $(@).outerHeight()
#       #debug hfHeight
#       theHeight = tHeight - (44 * 3) #hfHeight #- (if ipad then 100 else if portrait then 64 else 0)
#       document.querySelector('div.fullHeight').style.height = theHeight + 'px'
#     fullHeight()
#     heightSetDone()
#     #if gmap?
#     #  setTimeout (->
#     #    google.maps.event.trigger gmap,'resize'
#     #    ),200
#     ###
#     cur_page = null
#     $(document,window).on 'resize orientationchange webkitfullscreenchange mozfullscreenchange fullscreenchange', ->
#       if cur_page
#         cur_page.width window.innerWidth
#         cur_page.trigger 'pageshow'
#     $('#schmapview').on 'pageshow',(e)->
#       cur_page = $(@)
#       setTimeout (->
#         fullHeight cur_page
#         heightSetDone()
#         if gmap?
#           google.maps.event.trigger gmap,'resize'
#         ),500
#     ###
#   ]
#   null
