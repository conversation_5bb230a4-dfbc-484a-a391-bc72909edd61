UserModel = MODEL 'User'
# UserBilling = COLLECTION 'user_billing'
# Cart = COLLECTION 'shopping_cart'
config = CONFIG(['stripe'])
Stripe = INCLUDE("stripe")

#  data-zip doesn't support canadian zip
VIEW 'stripe-payment',->
  form method:'POST',action:'/bill/pay/stripe',->
    text """<script
    src="https://js.stripe.com/v3/" class="stripe-button"
    data-key="#{@stripePubKey}"
    data-amount="#{@amount}"
    data-name="RealMaster Technology Inc."
    data-description="#{@desc or 'Widget'}"
    data-image="https://realmaster.com/img/logo.png"
    data-locale="auto"
    data-currency="cad">
  </script>
    """
    input type:'hidden',name:'amount',value:@amount
    input type:'hidden',name:'desc',value:@desc

APP 'bill/pay'
GET 'stripe',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.ckup 'stripe-test',{
      amount:50,
      desc:"Test Payment",
      user:user,
      stripePubKey:config.stripe?.pubKey}

VIEW 'stripe-test',->
  a href:'/1.5/index',-> _ "Go Back"
  hr()
  # p -> "Welcome #{@req.fullNameOrNickname(user)}"
  text ckup 'stripe-payment',@

POST 'stripe',(req,resp)->
  console.log req.body
  stripeToken = req.param 'stripeToken'
  stripe = Stripe config.stripe?.secret
  charge = stripe.charges.create {
    amount: req.param 'amount' # or from session or cart
    currency: "cad"
    source: stripeToken
    description: req.param 'desc' # or from session or cart
  }, (err, charge)->
    if err
      if err.type is 'StripeCardError'
        # The card has been declined
        console.log "Declined"
      console.error err
    else
      console.log charge
    resp.ckup "stripe-test-done",{charge:charge,err:err}

VIEW 'stripe-test-done',->
  a href:'/1.5/index',-> _ "Go Back"
  hr()
  p -> text JSON.stringify @charge
  p -> text JSON.stringify @err



###
{ amount: '0.5',
  desc: 'Test Payment',
  stripeToken: 'tok_189eEfHeEWJMEr3ImTbizlQ8',
  stripeTokenType: 'card',
  stripeEmail: '<EMAIL>' }

{ id: 'ch_189ek8HeEWJMEr3Iipyc9tL4',
  object: 'charge',
  amount: 50,
  amount_refunded: 0,
  application_fee: null,
  balance_transaction: 'txn_189ek8HeEWJMEr3IfDkBSQKl',
  captured: true,
  created: 1462901640,
  currency: 'cad',
  customer: null,
  description: 'Test Payment',
  destination: null,
  dispute: null,
  failure_code: null,
  failure_message: null,
  fraud_details: {},
  invoice: null,
  livemode: false,
  metadata: {},
  order: null,
  paid: true,
  receipt_email: null,
  receipt_number: null,
  refunded: false,
  refunds:
   { object: 'list',
     data: [],
     has_more: false,
     total_count: 0,
     url: '/v1/charges/ch_189ek8HeEWJMEr3Iipyc9tL4/refunds' },
  shipping: null,
  source:
   { id: 'card_189ek4HeEWJMEr3IokQWu30u',
     object: 'card',
     address_city: null,
     address_country: null,
     address_line1: null,
     address_line1_check: null,
     address_line2: null,
     address_state: null,
     address_zip: '55555',
     address_zip_check: 'pass',
     brand: 'Visa',
     country: 'US',
     customer: null,
     cvc_check: null,
     dynamic_last4: null,
     exp_month: 10,
     exp_year: 2017,
     fingerprint: 'b0QUWmppFe8WUGmt',
     funding: 'credit',
     last4: '4242',
     metadata: {},
     name: '<EMAIL>',
     tokenization_method: null },
  source_transfer: null,
  statement_descriptor: null,
  status: 'succeeded' }

###
