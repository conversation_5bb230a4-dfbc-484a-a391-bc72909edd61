
APP 'spevent'
VIEW 'cordovaJs', ->
  if (@req.getDevType() is 'app') and (not @pubOnly)
    unless 'app' is @req.getAppVer() # simulate app from browser, use this key
      if @req.isIOS()
        if (ver = @req.getAppVer())? and (ver > '3.2')
          js '/js/ios32.min.js' #'/js/ios32/cordova.js'
        else
          js '/js/ios.min.js' # '/js/ios/cordova.js'
      else if @req.isAndroid()
        js '/js/android.min.js' # '/js/android/cordova.js'
    js '/js/rmsrv.min.js?v=1.8'
  else if @req.isWeChat()
    js '/js/wx/jweixin-1.1.0.js'
    if wxcfg = @wxcfg or @vctx?.wxcfg
      script -> "var wxConfig = #{JSON.stringify wxcfg};"
    js '/js/wechatsrv.min.js'
  else
    js '/js/srv.min.js'

LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      #meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'
      meta name:"apple-mobile-web-app-capable", content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"
      meta name:"format-detection", content:"telephone=no"
      meta name:"referrer", content:"no-referrer" if @noref
      g4tags js:new Date()
      title -> @vctx.title or _ "RealMaster"
      css '/css/ratchet.min.css'
      text ckup 'cordovaJs', {wxcfg:@wxcfg} if @cordova
      css '/css/smb.css'
      css '/css/font-awesome.min.css' if @cordova
      js '/js/rmapp.min.js'
    block 'body', ->
      body ->
        g4body()
        text @content()
        #img src:@req.getLogParams(),id:'imgNotUsed',onload:"var _e$=document.getElementById('imgNotUsed');_e$.parentNode.removeChild(_e$);"

SpEvents =  COLLECTION 'chome','sp_events'
helpers = INCLUDE 'lib.helpers'
getRandomInt = (min, max) ->
  return Math.floor(Math.random() * (max - min + 1)) + min

# bids = {
#   'xasdasdasds':{bid:123, ts:ISODate, vc:12, avt, fn, ln}
# }
getRandomBids = (bids, uid)->
  ret = []
  arr = Object.keys(bids) or []
  # randNumsArr = (getRandomInt(0,arr.length) for n in [0...9])
  # unique = randNumsArr.filter((elem, index, self) ->
  #   return index == self.indexOf(elem);
  # )
  if uid and ((idx = arr.indexOf(uid.toString())) isnt -1)
    arr.splice(idx,1)
  _idArr = arr.slice(-20)
  # _idArr = helpers.shuffle(arr).slice(0,10) #better ways?
  # _idArr = []
  # while _idArr.length < 10
  #   randId = arr[getRandomInt(0,arr.length)]
  #   if _idArr.indexOf(randId) < 0
  #     _idArr.push randId
  for _id in _idArr
    bid = bids[_id]
    ret.push {_id:_id, bid:bid.bid, avt:bid.avt, fn:bid.fn, ln:bid.ln}
  ret
  # UserModel.getListByIds _idArr, {projection:{avt:1, fn:1, ln:1}}, (err,usrs) ->
  #   console.error err if err
  #   usrs ?= []
  #   for u in usrs
  #     ret.push {_id:u._id, bid:bids[i].bid, avt:u.avt, fn:u.fn, ln:u.ln}
  #   ret

getHost = (host)->
  #TODO: use config
  if (/app\.test/.test host) or (/jj\.test/.test host)
    return 'http://app.test:8081'
  else if /i\.jiajia\.ca/.test host #prop detail goes to child site /1.5
    return 'https://i.realmaster.com'
  else if /jiajia\.ca/.test host #prop detail goes to child site /1.5
    return 'https://realmaster.com'
  return 'http://'+host

gConfig = CONFIG(['share'])
getEventEntry = (host)->
  ret = gConfig.share.host or 'https://www.realmaster.cn'
  # ret = 'http://jj.test:8081'
  # if /\i.realmaster/.test host
  #   ret = 'http://i.jiajia.ca'
  # else if /realmaster/.test host
  #   ret = 'http://jiajia.ca'
  return ret+'/spevent/'

processSpEventReq = (req, resp, user, cb)->
  #no track hasViewed
  fnError = (err)->
    resp.redirect 'https://realmaster.com/mp/false'
  evtid = req.param 'evtid'
  uid = req.param 'uid'
  inc = {$inc:{vc:1}}
  if /^[a-f0-9]{24}$/i.test uid
    inc.$inc['bidUsr.'+uid+'.vc'] = 1
  SpEvents.findOneAndUpdate {_id:evtid}, inc, {upsert:false,returnDocument:'after'}, (err, ret)->
    return fnError(null) if err or not ret?.value
    evt = ret.value
    cfg = {
      isApp:req.getDevType() is 'app'
      isWeChat:req.isWeChat()
    }
    if evt.bidUsr?
      cfg.rand = getRandomBids(evt.bidUsr, user?._id)
      if mb = (evt.bidUsr[user?._id] || evt.bidUsr[uid])
        cfg.mybid = mb.bid
        cfg.avt = mb.avt
      evt.vc = mb?.vc or 0
    delete evt.bidUsr
    cfg.evt = evt
    cfg.mbl = user?.mbl
    if evt.end
      cfg.expd = evt.end <= new Date()
    else
      cfg.expd = false
      cfg.noend = true
    cfg.host = getHost(req.host)
    if cfg.isApp
      shareUrl = getEventEntry(req.host)+evt._id+'?uid='+user?._id
    else
      shareUrl = req.fullUrl()
    cfg.shareUrl = encodeURIComponent(shareUrl)
    cb cfg

DEF 'processSpEventReq',processSpEventReq

wechat = INCLUDE 'lib.wechat'
SysData = COLLECTION 'chome', 'sysdata'
conf = CONFIG(['wechat'])
WXObj = wechat.init SysData,conf.wechat?.appName,conf.wechat?.appId

getWXConfig = (req,url,cb)->
  unless req.isWeChat()
    return cb null
  wxcfg = WXObj.getJsConfig url
  cb null,wxcfg

GET ':evtid', (req, resp)->
  processSpEventReq req, resp, null, (cfg)->
    if req.isWeChat()
      getWXConfig req, req.fullUrl(), (err, wxcfg) ->
        console.error err if err
        # if /i.jiajia/.test req.hostname
        #   wxcfg?.debug = true
        resp.ckup 'ldrawPage', cfg, '_', {wxcfg:wxcfg, cordova:true}
    else
      resp.ckup 'ldrawPage', cfg
