
VIEW 'ldrawPage', ->
  text """
<div id="ldrawCtrl" v-cloak>
  <img src="/img/ldraw/share_img01.png" alt="share-image" :src="host+evt.logo" style="height:200px;width:200px;"/>
  <div class="" style="display:none">
    <span id="share-title"><span v-if="!isApp">{{_('RealMaster')}}</span>{{computedTitle}}</span>
    <span id="share-desc">{{computedDesc}}</span>
    <span id="share-image">{{evt.share_img || '/img/ldraw/share_img01.png' || host+evt.logo}}</span>
    <span id="share-url">{{shareUrl}}</span>
  </div>
  <div id="propDetailModal" class="modal" v-if="isApp">
    <prop-detail :from-share="true" :in-frame="true" :show-close="true" :no-share-image="true"></prop-detail>
  </div>
  <!-- <get-app-bar v-show="computedShowDlBar"></get-app-bar> -->
  <flash-message></flash-message>
  <share-dialog :w-dl.sync="true" :w-sign.sync="true" :disp-var="dispVar"></share-dialog>
  <header class="bar bar-nav" v-if="isApp">
    <a class="icon icon-left-nav pull-left" href="javascript:;" style="color:white", onclick="javascript:window.history.back();"></a>
    <h1 class="title"> 房大师 </h1>
  </header>
  <div class="content">
    <div class="content-wrapper">
      <div class="share-wrapper" v-show="isApp" @click="showShare()">
        <div class="share-btn">分享给好友</div>
        <div class="view">
          阅读 {{evt.vc || 0}}
        </div>
      </div>
      <div class="random-bid inline-block fade" v-show="randOne.bid" :class="{hide:!show}">
        <div class="avt">
          <img src="/img/logo_s.png" alt="Avator" :src="randOne.avt || '/img/logo_s.png'"/>
        </div>
        <div class="bid">
          我猜
          <span>{{compute(randOne.bid)}}万</span>
        </div>
      </div>
      <div class="exp-date">
        <span v-show="!noend && !expd">
          出价截止至：{{evt.end | dotdate}}
        </span>
        <span v-show="!noend && expd">
          出价已经结束
        </span>
      </div>
      <div class="sp-wrapper">
        <div class="sp-info inline-block">
          <div class="sp-logo">
            <img src="/img/ldraw/sp_logo01.png" alt="" :src="evt.sp_img"/>
          </div>
          <div class="sp-name">
            {{evt.sp_name || '新趋势地产多伦多'}}
          </div>
        </div>
        <div class="prop-preview inline-block">
          <div class="prop-img">
            <img src="/img/p1.png" alt="" :src="evt.prop_img"/>
          </div>
          <div class="prop-desc">
            <div class="guess">
              最终成交价？
            </div>
            <div class="price">
              叫价： {{evt.prop_lp | currency '$' 0}}
            </div>
            <div class="addr">
              {{evt.prop_addr}}
            </div>
            <div class="view-detail" @click="viewPropDetail()">
              查看详情
            </div>
          </div>
        </div>
        <div class="sp-content">
          <img src="/img/ldraw/evt_banner01.png" alt="" :src="evt.ad_banner_img"/>
        </div>
      </div>
      <div class="rules-wrapper">
        <div class="my-bid random-bid inline-block" v-show="showMybid">
          <div class="avt">
            <img src="/img/logo_s.png" alt="Avator" :src="avt || '/img/logo_s.png'"/>
          </div>
          <div class="bid">
            我猜
            <span>{{compute(mybid)}}万</span>
          </div>
        </div>
        <div class="input" v-show="isApp">
          <input type="number" name="name" :class="{disabled:expd}" placeholder="输入你猜的成交价(加币,如:890000)" v-model="price" inputmode="numeric" pattern="[0-9]*">
        </div>
        <div class="input" v-show="isApp">
          <input type="number" name="name" :class="{disabled:expd}" placeholder="输入电话号码(仅用于中奖时联系您)" v-model="mbl" inputmode="numeric" pattern="[0-9]*">
        </div>
        <div class="submit-btn" v-show="isApp && !expd">
          <button type="button" class="btn btn-block btn-long btn-warn" :class="{disabled:(!this.price || submitted)}" name="button" @click="placeBid()">提交</button>
        </div>
        <div class="mbl-tip" v-show="isApp">
          温馨提醒：为了保证获奖后我们可以联系到您，请确认您的注册邮箱和手机号码真实有效。
        </div>
        <div class="submit-btn" v-show="!isApp">
          <button type="button" class="btn btn-block btn-long btn-warn" name="button" @click="showDl()">{{dlWording}}</button>
        </div>
      </div>
      <div class="follow" v-show="false">
        <div class="tip">
          请关注下方公众号，第一时间获知成交价
        </div>
        <div class="img">
          <img src="/img/ldraw/sp_oa01.png" alt="Realmaster" />
        </div>
        <div class="" id="RMWeChatOA">
          NuStreamToronto
        </div>
        <div class="">
          <button type="button" class="btn btn-block btn-long btn-warn" name="button" @click="copyWeChat()">点击这里复制公众号</button>
        </div>
      </div>
      <div class="rules" v-show="!isWeChat">
        <div class="">
          1.活动规则：
          <ol type="a">
            <li>竞猜价格最接近最终成交价的20位用户入围</li>
            <li>以分享本活动到微信所获得的阅读数的多少，从入围用户中选出阅读数最多的7位获奖者（分享按钮与阅读数显示均在本页面右上角）</li>
            <li>房源成交价以及活动最终获奖名单将在活动结束后公布</li>
            <li>请确保您的注册邮箱或手机号码正确，否则将失去获奖资格。</li>
          </ol>
        </div>
        <div class="">
          2.活动声明：
          <ol type="a">
            <li>未关联任何地产经纪的房大师注册用户均可参与</li>
            <li>恶意刷阅读数等作弊行为将会被取消获奖资格</li>
            <li>新趋势地产公司拥有对本次活动的最终解释权</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</div>
"""
  css '/css/apps/ldraw.css'
  coffeejs {vars:{
    isApp:@isApp,
    isWeChat:@isWeChat,
    mybid:@mybid,
    mbl:@mbl,
    evt:@evt,
    avt:@avt,
    rand:@rand,
    expd:@expd,
    noend:@noend,
    host:@host,
    shareUrl:@shareUrl
    }}, ->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appLuckyDraw.js'