SITE 'Fallback'

GET 'robots.txt',(req,resp)-> resp.send "",404
GET '*',(req,resp)->
  resp.statusCode = 404
  resp.ckup "site_notfound",{q:req.query}
GET (req,resp)->
  resp.statusCode = 404
  resp.ckup "site_notfound"

LAYOUT '_',->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      title -> text _ "Site Not Found"
      style type:'text/css',->
        text '''
        body {background-color:#CCC;}
        div.error {
          background-color: white;
          margin: 20px auto;
          text-align: center;
          padding: 10px;
          border: 3px solid red;
          -moz-border-radius: 10px;
          -webkit-border-radius: 10px;
          width: 60%;
        }
        div.error h2 {
          color: red;
        }
        '''
    body ->
      text @content()

VIEW 'site_notfound',->
  div class:'error',->
    h2 ->
      text _ "Can not find the site."
    p -> text @req.hostname
    if @q
      p ->
        if 'object' is typeof @q
          for k,v of @q
            text "#{k} => #{v}"
            br()
        else
          text @q