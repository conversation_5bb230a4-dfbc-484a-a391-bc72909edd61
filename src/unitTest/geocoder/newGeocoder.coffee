#NOTE: this file should run once a day or once a week to aviod to many geocoding
# Sometimes geocoder will fail due to server ever, try again may resolve the problem.
should = require('should')
testHelpers = require('../00_common/helpers')
helpersFunction = require '../../built/lib/helpers_function'
geoCoder = null
geoCoderLocal = null
NO_REAL_GEOCODING = false
request = null

gGeoCache = null
gGeoCacheOrig = null
# gGeocodingStat = null
geoCodingAsync = null
reverseGeocodingAsync = null
PropertiesCol = null

describe 'Geocoding',->
  before (done) ->
    @timeout(300000)
    request = testHelpers.getServer()
    geoCoder = testHelpers.MODEL 'GeoCoder'
    geoCoderLocal = testHelpers.MODEL 'GeoCoderLocal'
    gGeoCache = testHelpers.COLLECTION('vow','geo_cache')
    gGeoCacheOrig = testHelpers.COLLECTION('vow','geoCacheOrig')
    # gGeocodingStat = testHelpers.COLLECTION('vow','geo_coding_stat')
    PropertiesCol = testHelpers.COLLECTION('vow','properties')
    geoCodingAsync = helpersFunction.addAsyncSupport geoCoderLocal.geocoding
    reverseGeocodingAsync = helpersFunction.addAsyncSupport geoCoderLocal.reverseGeocoding
    # geoCoder.setGeocodingStat gGeocodingStat
    # NOTE:当前module需要做geocoding,跑整体modules时,propertyImport module会设置noGeocoding===true,会对当前module造成影响
    geoCoder.noGeocoding(false)
    dbs = [
      { db: 'vow', table: 'geo_cache' },
      { db: 'vow', table: 'geoCacheOrig' },
      # { db: 'vow', table: 'geo_stat' },
      { db: 'vow', table: 'properties' },
    ]
    testHelpers.cleanDBs dbs, () ->
      testHelpers.checkAndsetUpFixture {folder:__dirname}, ()->
        testHelpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
          testHelpers.userCookies.adminCookie = cookie
          console.error err if err
          setTimeout(done, 2500)
    return

  # NOTE:geocoding时会调用该func，不需要单独测试
  # describe 'test selBing key',->
  #   it 'should sel bing key', (done)->
  #     SysData = testHelpers.COLLECTION('chome','sysdata')
  #     geoCoder.selBingKey SysData,(err,ret)->
  #       console.log ret
  #       done()

  # NOTE:geocoding时会调用该func，不需要单独测试
  # describe 'test incBing key',->
  #   it 'should inc bing key', (done)->
  #     SysData = testHelpers.COLLECTION('chome','sysdata')
  #     geoCoder.incBingKey SysData,100,(err,ret)->
  #       console.log ret
  #       done()

  describe 'POST 1.5/geocoding/getGeocodingByEngine', ->
    # @timeout(30000)
    inputParams =
      addr : '114 Ossington Ave'
      city : 'Toronto'
      prov : 'ON'
      cnty : 'CA'

    hereParam = Object.assign {},inputParams,{useCache:false,type:'here'}
    mapboxParam = Object.assign {},inputParams,{useCache:false,type:'mapbox',city:'Toronto'}
    googleParam = Object.assign {},inputParams,{useCache:false,type:'google', prov:'Ontario'}
    azureParam = Object.assign {},inputParams,{useCache:false,type:'azure'}
    # NOTE:mapquest,bing unused
    # bingParam = Object.assign {},inputParams,{useCache:false,type:'bing', cnty:'Canada'}
    # mapquestParam = Object.assign {},inputParams,{useCache:false,type:'mapquest'}

    it 'should return ACCESS_DENIED if not admin', (done)->
      if NO_REAL_GEOCODING
        @skip()
      request
        .post('1.5/geocoding/getGeocodingByEngine')
        .set('Accept', 'application/json')
        .send(inputParams)
        .expect('Content-Type', /json/)
        .expect(200)
        .end (err, res)->
          res.body.err.should.be.exactly(testHelpers.MSG_STRINGS.ACCESS_DENIED)
          done()
      return
    
    tests = [
      {
        desc:'should do geocoding with here engine for admin',
        args: hereParam,
        expected: {
          result: Object.assign {},inputParams,{src:'here'}
        },
      },
      {
        desc:'should do geocoding with mapbox engine for admin',
        args: mapboxParam,
        expected: {
          result: Object.assign {},inputParams,{src:'mapbox'}
        },
      },
      {
        desc:'should do geocoding with google engine for admin',
        args: googleParam,
        expected: {
          result: Object.assign {},inputParams,{src:'google'}
        },
      },
      {
        desc:'should do geocoding with azure engine for admin',
        args: azureParam,
        expected: {
          result: Object.assign {},inputParams,{src:'azure'}
        },
      },
      {
        desc:'should error for no addr',
        args: {
          city : 'Toronto'
          prov : 'ON'
          cnty : 'CA'
        },
        expected: {e:'Bad Parameter'}
      },
      {
        desc:'should return error if engine type is not support',
        args: {
          city : 'Toronto'
          prov : 'ON'
          cnty : 'CA'
          type:'unknowEngine'
        },
        expected: {e:'No uaddr'}
      },
      {
        desc:'should return error if has addr but no city',
        args: {
          prov:'ON'
          cnty : 'CA'
          addr: '250 consumer rd'
        },
        expected: {e:'No uaddr'}
      },
      {
        desc:'should return error if prov is number',
        args:{
          addr: '250 consumer rd'
          city : 'Toronto'
          cnty : 'CA'
          prov: 12345
        },
        expected: {e:'prov Expect String'}
      },
      {
        desc:'should return error if addr is number',
        args:{
          addr: '250'
          city : 'Toronto'
          cnty : 'CA'
          prov: 'On'
        },
        expected: {e:'addr Expect String'}
      },
      {
        desc:'should return error if city is number',
        args:{
          addr: '250 consumer rd'
          city : '131'
          cnty : 'CA'
          prov: 'ON'
        },
        expected: {e:'city Expect String'}
      },
      {
        desc:'should return error if cnty is number',
        args:{
          addr: '250 consumer rd'
          city : 'toronto'
          cnty : '123'
          prov: 'ON'
        },
        expected: {e:'cnty Expect String'}
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout(300000)
        if NO_REAL_GEOCODING
          @skip()
        request
          .post('1.5/geocoding/getGeocodingByEngine')
          .set('Cookie', testHelpers.userCookies['adminCookie'])
          .set('Accept', 'application/json')
          .send(test.args)
          .expect('Content-Type', /json/)
          .expect(200)
          .end (err, res)->
            if result = res.body.result
              should.exists(result)
              console.log result
              console.log test.expected.result
              for k,v of test.expected.result
                # console.log k,v
                should.exists(result[k])
                result[k].should.be.exactly(v)
            else if err = res.body.err
              err.should.be.exactly(test.expected.err)
            done()
        return
    
    # NOTE: getGeocodingStat unused
    # tests=[
    #   {
    #     args: {
    #       addr:'Co A L14, Rr #1',
    #       city:'Alnwick/Haldimand'
    #       prov:'Ontario',
    #       type:'google',
    #     },
    #     expected: {
    #       result: {src:'google'}
    #     },
    #   },
    #   {
    #     args: {
    #       addr:'Rr 4.0',
    #       city:'Rural Mountain View County'
    #       prov:'Alberta',
    #       type:'google',
    #     },
    #     expected: {
    #       result: {src:'google'}
    #     },
    #   },
    #   {
    #     args: {
    #       addr:'Lot 149b 101 Cortland Rdg',
    #       city:'Spryfield'
    #       prov:'Nova Scotia',
    #       type:'google',
    #     },
    #     expected: {
    #       result: {src:'google'}
    #     },
    #   },
    #   {
    #     args: {
    #       addr:'#21 -32000 Dufferin St',
    #       city:'Mississagua'
    #       prov:'Ontario',
    #       type:'bing',
    #     },
    #     expected: {
    #       result: {src:'bing'}
    #     },
    #   },
    #   {
    #     args: {
    #       addr:'1038 Florence',
    #       city:'Windsor'
    #       prov:'Ontario',
    #       type:'mapquest',
    #     },
    #     expected: {
    #       result: {src:'mapquest'}
    #     },
    #   }
    # ]
    #google may return different result.
    # tests.forEach (test)->
    #   it "should return gecoding and set q<100 for #{test.args.addr},and check geocoding stat", (done)->
    #     if NO_REAL_GEOCODING
    #       @skip()
    #     request
    #       .post('1.5/geocoding/getGeocodingStat')
    #       .set('Cookie', ['apsv=appDebug'])
    #       .set('Cookie', testHelpers.userCookies['adminCookie'])
    #       .set('Accept', 'application/json')
    #       .send({src:test.args.type})
    #       .expect('Content-Type', /json/)
    #       .expect(200)
    #       .end (err, beforeStat)->
    #         beforeStat = beforeStat.body.result[0]
    #         beforeStat?={coded:0}
    #         request
    #           .post('1.5/geocoding/getGeocodingByEngine')
    #           .set('Cookie', testHelpers.userCookies['adminCookie'])
    #           .set('Accept', 'application/json')
    #           .send(test.args)
    #           .expect('Content-Type', /json/)
    #           .expect(200)
    #           .end (err, res)->
    #             if result = res.body.result
    #               should.exists(result)
    #               console.log result
    #               console.log result.q
    #               result.q.should.be.below(100)
    #               console.log test.expected.result
    #               for k,v of test.expected.result
    #                 # console.log k,v
    #                 should.exists(result[k])
    #                 result[k].should.be.exactly(v)
    #             request
    #               .post('1.5/geocoding/getGeocodingStat')
    #               .set('Cookie', ['apsv=appDebug'])
    #               .set('Cookie', testHelpers.userCookies['adminCookie'])
    #               .set('Accept', 'application/json')
    #               .send({src:test.args.type})
    #               .expect('Content-Type', /json/)
    #               .expect(200)
    #               .end (err, afterStat)->
    #                 afterStat = afterStat.body.result[0]
    #                 console.log  'afterStat',afterStat,'beforeStat.coded',beforeStat.coded
    #                 afterStat.coded.should.be.equal(beforeStat.coded+1)
    #                 done()
    #       return

    it 'should do geocoding with no engine specified and save to geocache', (done)->
      @timeout 10000
      if NO_REAL_GEOCODING
        @skip()
      input =
        addr : '250 consumers rd'
        city : 'Toronto'
        prov : 'ON'
        cnty : 'CA'
      request
        .post('1.5/geocoding/getGeocodingByEngine')
        .set('Cookie', ['apsv=appDebug'])
        .set('Cookie', testHelpers.userCookies['adminCookie'])
        .set('Accept', 'application/json')
        .send(input)
        .expect('Content-Type', /json/)
        .expect(200)
        .end (err, res)->
          # console.log res.body.result
          res.body.ok.should.be.exactly(1)
          should.exists(res.body.result)
          expected =
            lat: res.body.result.lat
            lng: res.body.result.lng
            q: res.body.result.q
            cnty: res.body.result.cnty
            prov: res.body.result.prov
            city: res.body.result.city
          request
            .post('1.5/geocoding/getGeocodingByEngine')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', testHelpers.userCookies['adminCookie'])
            .set('Accept', 'application/json')
            .send(input)
            .expect('Content-Type', /json/)
            .expect(200)
            .end (err, res)->
              expected.cached = true
              should.exists(res.body.result)
              should.exists(res.body.ok)
              res.body.ok.should.be.exactly(1)
              for fld in ['prov','city','lat','lng','cached']
                should.exists(res.body.result[fld])
                res.body.result[fld].should.be.exactly(expected[fld])
              done()
        return

    #prop has bigger geoq, use prop geoq.update cache
    # 优先使用Google,返回信息变化
    expected =
      addr:'250 Consumers Rd'
      cnty: 'CA'
      prov: 'ON'
      lat: 43.7705644
      lng: -79.3302562
      city: 'Toronto'
      # ptype:'r'
      # ptype2:['condo']
    
    prop =
      addr : '250 consumers road'
      city : 'Toronto'
      prov : 'ON'
      cnty : 'CA'
      lat: 43.77,
      lng: -79.33,
      geoq:130,
      ptype:'r'
      ptype2:['condo']
      aUaddr:'250 consumers road, toronto,ontario'
      
    
    it 'should use prop geo if prop has bigger geoq and save', (done)->
      @timeout 10000
      prop.upsert = true
      request
        .post('1.5/geocoding/getGeocodingByEngine')
        .set('Cookie', ['apsv=appDebug'])
        .set('Cookie', testHelpers.userCookies['adminCookie'])
        .set('Accept', 'application/json')
        .send(prop)
        .expect('Content-Type', /json/)
        .expect(200)
        .end (err, res)->
          res.body.ok.should.be.exactly(1)
          newExpected = Object.assign {},expected
          should.exists(res.body.result)
          for fld in Object.keys(newExpected)
            should.exists(res.body.result[fld])
            # For lat/lng fields, only compare up to 4 decimal places
            if fld in ['lat', 'lng']
              res.body.result[fld].toFixed(4).should.be.exactly(newExpected[fld].toFixed(4))
              continue
            res.body.result[fld].should.be.exactly(newExpected[fld])
          request
            .post('1.5/geocoding/getGeocodingByEngine')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', testHelpers.userCookies['adminCookie'])
            .set('Accept', 'application/json')
            .send({
              addr : '250 consumers rd'
              city : 'Toronto'
              prov : 'ON'
              cnty : 'CA'
            })
            .expect('Content-Type', /json/)
            .expect(200)
            .end (err, res)->
              # console.log res.body.ok
              res.body.ok.should.be.exactly(1)
              should.exists(res.body.result)
              newExpected = Object.assign {},expected,{cached: true}
              for fld in Object.keys(newExpected)
                # console.log res.body.result[fld]
                should.exists(res.body.result[fld])
                if fld in ['lat', 'lng']
                  res.body.result[fld].toFixed(4).should.be.exactly(newExpected[fld].toFixed(4))
                  continue
                res.body.result[fld].should.be.exactly(newExpected[fld])
              done()
        return
    

    tests = [
      {
        desc:'should return same result for a typo address',
        param:{
            addr : '250 consumer road'
            city : 'Toronto'
            prov : 'ON'
            cnty : 'CA'
          }
      },
      {
        desc:'should return same result for address with unit',
        param:{
          addr : '#911 250 consumer road'
          city : 'Toronto'
          prov : 'ON'
          cnty : 'CA'
        }
      },
      # { not suppport now. only support addr for now.
      #   desc:'should return same result if no addr but st and st_num',
      #   param:{
      #     st : 'consumer road'
      #     st_num:'250'
      #     city : 'Toronto'
      #     prov : 'ON'
      #     cnty : 'CA'
      #   },
      #   expected:{cached: true}
      # }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout 10000
        request
          .post('1.5/geocoding/getGeocodingByEngine')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', testHelpers.userCookies['adminCookie'])
          .set('Accept', 'application/json')
          .send(test.param)
          .expect('Content-Type', /json/)
          .expect(200)
          .end (err, res)->
            # console.log '123131'
            # console.log res.body
            res.body.ok.should.be.exactly(1)
            expected =
              addr:'250 Consumers Rd'
              cnty: 'CA'
              prov: 'ON'
              city: 'Toronto'
            newExpected = Object.assign {},expected,test.expected
            should.exists(res.body.result)
            # console.log 'newExpected',newExpected
            for fld in Object.keys(newExpected)
              # console.log fld
              # console.log res.body.result[fld]
              should.exists(res.body.result[fld])
              res.body.result[fld].should.be.exactly(newExpected[fld])
            done()
        return

  describe 'POST 1.5/geocoding', ->
    @timeout(30000)
    addr = '250 consumer rd,Toronto,ON,CA'
    tests = [
      {
        desc:'should return ACCESS_DENIED if not using app',
        args: {
          addr: addr
        },
        expected: {e:'Access Denied'},
        cookie: []
      },
      {
        desc:'should return BAD_PARAMETER if not addr',
        args: {
          addr: ''
        },
        expected: {e:'Bad Parameter'},
        cookie: ['apsv=appDebug']
      },
      {
        desc:'should return BAD_PARAMETER if addr length is less then 4 ',
        args: {
          addr: 'Toronto,ON,CA'
        },
        expected: {e:'Bad Parameter'},
        cookie: ['apsv=appDebug']
      },
      {
        desc:'should cached result for 250 consumer road,Toronto,ontario,canada',
        args: {
          addr: '250 consumer road,Toronto,ontario,canada'
        },
        expected: {result:{
          prov:'ON',
          city:'Toronto',
          cnty:'CA',
          addr:'250 Consumers Rd',
          cached:true
          }},
        cookie: ['apsv=appDebug']
      },
      {
        desc:'should return coded result for york university,Vaughan,ontario,canada',
        args: {
          addr: 'york university,Vaughan,ontario,canada'
        },
        expected: {result:{prov:'ON',city:'Toronto',cnty:'CA',doCoding:true}},
        cookie: ['apsv=appDebug']
      }
    ]

    tests.forEach (test)->
      it test.desc, (done)->
        if NO_REAL_GEOCODING
          @skip()
        request
          .post('1.5/geocoding')
          .set('Cookie', test.cookie)
          .set('Accept', 'application/json')
          .send(test.args)
          .expect('Content-Type', /json/)
          .expect(200)
          .end (err, res)->
            should.not.exists(err)
            if test.expected.e
              res.body.e.should.be.exactly(test.expected.e)
              return done()
            ret = res.body.result
            for k,v of test.expected.result
              if typeof v is 'string'
                ret[k].should.be.exactly(v)
              # else
              #   for k1,v1 of v
              #     ret[k][k1].should.be.exactly(v1)
            done()
        return
    
  # NOTE: geoCodeOneOrUpsert unused
  # describe 'test geoCodeOneOrUpsert function',->
  #   halfYearAgo = new Date(new Date() - 300*24*3600*1000)
  #   tests=[
  #     {
  #       desc:'should do real geocoding when cached is 80 and is too old, and no lat in prop',
  #       geoCacheObj:
  #         _id:'CA:ON:MISSISSAUGA:1150 EGLINTON AVE E'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:MISSISSAUGA:1150 EGLINTON AVE E']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       qaddr:'1150 Eglinton Ave E,Mississauga, ON, CA'
  #       param:
  #         addr : '1150 Eglinton Ave E'
  #         city : 'Mississauga'
  #         prov : 'ON'
  #         cnty : 'CA'
  #      expected:
  #       doCoding:true
  #     },
  #     {
  #       desc:'should do real geocoding if cached is 80 and halfYearAgo and prop.geoq is \
  #         100 and prop is Active and in half a year',
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:25 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:25 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       param:
  #         addr : '25 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 100
  #         status:'A'
  #         ts: new Date()
  #       expected:
  #         doCoding:true
  #     },
  #     {
  #       desc:'should do real geocoding if cached is 80 and halfYearAgo and prop.geoq is \
  #         100 and prop is Sold and in half a year',
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:26 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:26 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       qaddr:'26 Bamboo Grve,Toronto, ON, CA'
  #       param:
  #         addr : '26 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 100
  #         status:'U'
  #         lst:'Sld'
  #         ts: new Date()
  #       expected:
  #         doCoding:true
  #     },
  #     {
  #       desc:'should set geoq=90 if cached is is 80 and halfYearAgo, prop.geoq is 100 and before half a year',
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:27 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:27 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       param:
  #         addr : '27 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 100
  #         status:'U'
  #         lst:'Sld'
  #         ts: halfYearAgo
  #       expected:
  #         q:90,
  #         useprop:true
  #     },
  #     {
  #       desc:'should use prop if cached q is 80 and halfYearAgo, prop.geoq<100 and before half a year',
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:28 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:28 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       param:
  #         addr : '28 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 80
  #         status:'U'
  #         lst:'Sld'
  #         ts:halfYearAgo
  #       expected:
  #         useprop:true,
  #         lat: 43.77,
  #         lng: -79.33,
  #         q:80
  #     },
  #     {
  #       desc:'should do real geocoding if cached q is 80 and halfYearAgo \
  #         prop.geoq<100 and Sld within half a year',
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:29 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:29 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       qaddr:'29 Bamboo Grve,Toronto, ON, CA'
  #       param:
  #         addr : '29 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 80
  #         status:'U'
  #         lst:'Sld'
  #         ts:new Date()
  #       expected:
  #         doCoding:true
  #     },
  #     {
  #       desc:'should do real geocoding if  cached q is 80 and halfYearAgo\
  #         prop.geoq<100 and active within half a year',
  #       uaddr:'CA:ON:TORONTO:30 BAMBOO GRVE',
  #       qaddr:'30 Bamboo Grve,Toronto, ON, CA'
  #       geoCacheObj:
  #         _id:'CA:ON:TORONTO:30 BAMBOO GRVE'
  #         q:80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:30 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       param:
  #         addr : '30 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 43.77,
  #         lng: -79.33,
  #         geoq: 80
  #         status:'A'
  #         ts: new Date()
  #       expected:
  #         doCoding:true
  #     },
  #     {
  #       desc: 'should not do real geocoding if prop is noGeocoding， cached q is 80 and halfYearAgo\
  #         prop.geoq<100 and active within half a year',
  #       uaddr: 'CA:ON:TORONTO:30 BAMBOO GRVE',
  #       geoCacheObj:
  #         _id: 'CA:ON:TORONTO:30 BAMBOO GRVE'
  #         q: 80,
  #         src:'bing',
  #         lat: 43.77,
  #         lng: -79.33,
  #         aUaddr:['CA:ON:TORONTO:30 BAMBOO GRVE']
  #         mt:halfYearAgo
  #         ts:halfYearAgo
  #       param:
  #         addr : '30 Bamboo Grve'
  #         city : 'Toronto'
  #         prov : 'ON'
  #         cnty : 'CA'
  #         lat: 42.77,
  #         lng: -70.33,
  #         geoq: 80
  #         status:'A'
  #         ts: new Date()
  #         noGeocoding:true
  #       expected:
  #         noGeocoding:1
  #         lat: 42.77,
  #         lng: -70.33,
  #     },
  #     {
  #       desc: 'trebman should save to geoCacheOrig but \
  #           not to geocache when geocache q is 130',
  #       uaddr: 'CA:AB:PEACE RIVER:112 106 AVE'
  #       geoCacheObj:
  #         _id: 'CA:AB:PEACE RIVER:112 106 AVE'
  #         q: 130,
  #         src: 'manual',
  #         lat: 43
  #         lng: 456
  #         aUaddr:['CA:AB:PEACE RIVER:112 106 AVE']
  #       param:
  #         addr: '112 106 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src: 'TRB'
  #         geoq: 105
  #         upsert: true
  #       expected:
  #         upsertResult:{cached:true}
  #         geoCoding:{q:130,lat:43,lng:456,src:'manual'},
  #     },
  #     #
  #     {
  #       desc: 'trebman should save to geoCacheOrig and update geocache when geocache geoq is 100',
  #       uaddr:'CA:AB:PEACE RIVER:113 106 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:113 106 AVE'
  #         q:100,
  #         src:'manual',
  #         lat:'43'
  #         lng:'456'
  #         aUaddr:['CA:AB:PEACE RIVER:113 106 AVE']
  #       param:
  #         addr: '113 106 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src:'TRB'
  #         geoq:105
  #         upsert:true
  #       expected:
  #         upsertResult:{useprop:true,lng: '-117',lat: '56'}
  #         geoCoding:{q:105, lng: '-117',lat: '56',src:'TRB'},
  #         geoCodingOrigExpectedRet:{'TRB':{
  #           addr: '113 106 Ave',
  #           city: 'Peace River',
  #           prov: 'AB',
  #           lng: '-117',
  #           lat: '56'
  #           src:'TRB'
  #           q:105
  #         }}
  #     },
  #     {
  #       desc: 'should save ddf source to geoCacheOrig and geocache',
  #       uaddr:'CA:AB:PEACE RIVER:114 106 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:114 106 AVE'
  #         q:100,
  #         src:'bing',
  #         lat:'43'
  #         lng:'456'
  #         aUaddr:['CA:AB:PEACE RIVER:114 106 AVE']
  #       qaddr:'114 106 Ave,Peace River, AB, CA'
  #       param:
  #         addr: '114 106 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src: 'DDF'
  #         geoq: 101
  #       expected:
  #         upsertResult:{useprop:true,lng: '-117',lat: '56',q:101}
  #         geoCoding:{q:101, lng: '-117',lat: '56',src:'DDF'},
  #         geoCodingOrigExpectedRet:{'DDF':{
  #           addr: '114 106 Ave',
  #           city: 'Peace River',
  #           prov: 'AB',
  #           lng: '-117',
  #           lat: '56'
  #           src:'DDF'
  #           q:101
  #         }}
  #     },
  #    {
  #       desc: 'ddf should save to geoCacheOrig but \
  #           not to geocache when geocache geoq is 80',
  #       uaddr:'CA:AB:PEACE RIVER:115 106 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:115 106 AVE'
  #         q:80,
  #         src:'bing',
  #         lat:'43'
  #         lng:'456',
  #         aUaddr:['CA:AB:PEACE RIVER:115 106 AVE']
  #       param:
  #         addr: '115 106 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src:'DDF'
  #         geoq:101
  #         upsert:true
  #       expected:
  #         upsertResult:{useprop:true,lng: '-117',lat: '56'}
  #         geoCoding:{q:101, lng: '-117',lat: '56',src:'DDF'},
  #         geoCodingOrigExpectedRet:{'DDF':{
  #           addr: '115 106 Ave',
  #           city: 'Peace River',
  #           prov: 'AB',
  #           lng: '-117',
  #           lat: '56'
  #           src:'DDF'
  #           q:101
  #           upsert:true
  #         }}
  #     },
  #     {
  #       desc: 'ddf should use cached and not save when geocache geoq is 130',
  #       uaddr:'CA:AB:PEACE RIVER:116 106 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:116 106 AVE'
  #         q:130,
  #         src:'bing',
  #         lat:'43'
  #         lng:'456'
  #         aUaddr:['CA:AB:PEACE RIVER:116 106 AVE']
  #       param:
  #         addr: '116 106 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src:'DDF'
  #         geoq:101
  #         upsert:true
  #       expected:
  #         upsertResult:{cached:true,lng: '456',lat: '43'}
  #         geoCoding:{q:130, lng: '456',lat: '43',src:'bing'}
  #     },
  #   ]

  #   tests.forEach (test)->
  #     it "#{test.desc}",(done)->
  #       @timeout(300000)
  #       cachedCoding = Object.assign {},test.param,test.geoCacheObj
  #       gGeoCache.insertOne cachedCoding,(err,ret)->
  #         setTimeout ()->
  #           geoCoder.geoCodeOneOrUpsert gGeoCache,test.param,(err,ret)->
  #             console.log 'geoCodeOneOrUpsert',ret
  #             console.log 'test.expected.upsertResult',test.expected.upsertResult
  #             for k, v of test.expected.upsertResult
  #               ret[k].should.be.exactly(v)
  #             gGeoCache.findOne {_id:test.uaddr},(err,ret)->
  #               console.log 'findOne',ret
  #               console.log 'test.expected.geoCoding',test.expected.geoCoding
  #               for k, v of test.expected.geoCoding
  #                 ret[k].should.be.exactly(v)
  #               if test.qaddr
  #                 gGeoCacheOrig.findOne {_id:test.qaddr},(err,ret)->
  #                   console.log 'gGeoCacheOrig',ret
  #                   console.log 'test.expected.geoCodingOrigExpectedRet',test.expected.geoCodingOrigExpectedRet
  #                   if geoCodingOrigExpectedRet = test.expected.geoCodingOrigExpectedRet
  #                     for type, result of geoCodingOrigExpectedRet
  #                       should.exist(ret[type])
  #                       for k, v of result
  #                         should.exist(ret[type][k])
  #                         ret[type][k].should.be.exactly(v)
  #                   done()
  #               else
  #                 done()
  #         ,1000


  describe 'POST 1.5/geocoding with lat and lng', ->
    @timeout(30000)
    addr = '250 consumer rd,Toronto,ON,CA'
    tests = [
      {
        desc:'should do geocoding  250 consumer road,Toronto,ontario,canada',
        args: {
          lat: 43.771989124749346,
          lng: -79.33087218904066
        },
        expected: {
          prov:'ON',
          city:'Toronto',
          cnty:'CA',
          addr:'250 Consumers Rd',
        },
        cookie: ['apsv=appDebug']
      }
    ]

    tests.forEach (test)->
      it test.desc, (done)->
        if NO_REAL_GEOCODING
          @skip()
        request
          .post('1.5/geocoding')
          .set('Cookie', test.cookie)
          .set('Accept', 'application/json')
          .send(test.args)
          .expect('Content-Type', /json/)
          .expect(200)
          .end (err, res)->
            console.log res.body
            for k,v of test.expected
              console.log 'res.body.result[k]',res.body.result[k],'v',v
              res.body.result[k].should.be.exactly(v)
            done()
        return

  # NOTE:geoCodeOneOrUpsert unused
  # describe 'test geoCodeOneOrUpsert function added to geo conflict',->
  #   tests=[
  #     {
  #       desc: 'update lat,lng distance > 1000, added to resolve geo conflict',
  #       uaddr:'CA:AB:PEACE RIVER:113 110 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:113 110 AVE'
  #         q:100,
  #         src:'bing',
  #         lat:'43'
  #         lng:'456'
  #         aUaddr:['CA:AB:PEACE RIVER:113 110 AVE']
  #       param:
  #         addr: '113 110 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta',
  #         lng: '-117',
  #         lat: '56'
  #         src:'TRB'
  #         geoq:105
  #         upsert:true
  #       expected:
  #         upsertResult:{useprop:true,lng: '-117',lat: '56'}
  #         geoConflict: [
  #           { tp: 'bing', lat: '43', lng: '456' },
  #           { tp: 'TRB', lat: '56', lng: '-117' }
  #           ]
  #     }
  #   ]

  #   tests.forEach (test)->
  #     it "#{test.desc}",(done)->
  #       @timeout(300000)
  #       cachedCoding = Object.assign {},test.param,test.geoCacheObj
  #       gGeoCache.insertOne cachedCoding,(err,ret)->
  #         setTimeout ()->
  #           geoCoder.geoCodeOneOrUpsert gGeoCache,test.param,(err,ret)->
  #             console.log 'geoCodeOneOrUpsert',ret
  #             console.log 'test.expected.upsertResult',test.expected.upsertResult
  #             for k, v of test.expected.upsertResult
  #               ret[k].should.be.exactly(v)
  #             gGeoCache.findOne {_id:test.uaddr},(err,ret)->
  #               console.log 'findOne',ret
  #               expConflict = JSON.stringify test.expected.geoConflict
  #               retConflict = JSON.stringify ret.geoConflict
  #               retConflict.should.be.exactly(expConflict)
  #               done()
  #         ,1000

  # NOTE:geoCodeOneOrUpsert unused
  # describe 'if has rlat/rlng, use rlat/rlng first',->
  #   tests=[
  #     {
  #       desc: 'find uaddr return rlat/rlng data',
  #       uaddr:'CA:AB:PEACE RIVER:133 244 AVE'
  #       geoCacheObj:
  #         _id:'CA:AB:PEACE RIVER:133 244 AVE'
  #         q:100,
  #         src:'bing',
  #         lat: 43.771989124749346,
  #         lng: -79.33087218904066,
  #         rlat: 44,
  #         rlng: -80,
  #         aUaddr:['CA:AB:PEACE RIVER:133 244 AVE']
  #       param:
  #         addr: '133 244 Avenue',
  #         city: 'Peace River',
  #         prov: 'Alberta'
  #       expected:
  #         upsertResult:{lng: -80,lat: 44}
  #     }
  #   ]

  #   tests.forEach (test)->
  #     it "#{test.desc}",(done)->
  #       @timeout(300000)
  #       cachedCoding = Object.assign {},test.param,test.geoCacheObj
  #       gGeoCache.insertOne cachedCoding,(err,ret)->
  #         setTimeout ()->
  #           geoCoder.geoCodeOneOrUpsert gGeoCache,test.param,(err,ret)->
  #             console.log 'geoCodeOneOrUpsert',ret
  #             console.log 'test.expected.upsertResult',test.expected.upsertResult
  #             for k, v of test.expected.upsertResult
  #               ret[k].should.be.exactly(v)
  #             done()
  #         ,1000
  describe 'Test same city has multiple streets with the same name', ->
    it 'should return uaddr with no zip3',(done)->
      @timeout 10000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Toronto'
        ptype:'r'
        addr:'120 Homewood Ave'
        cmty:'North St. Jamestown'
        zip:'M4Y2J3'
        ptype2:['Apartment','Loft']
      }
      geoCoder.geocoding testInput,(err,ret)->
        if err
          console.error err
        should.not.exists(err)
        ret?._id?.should.be.exactly('CA:ON:TORONTO:120 HOMEWOOD AVE')
        done()
      return
    it 'should return uaddr with zip3',(done)->
      @timeout 10000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Toronto'
        ptype:'r'
        addr:'120 Homewood Ave'
        cmty:'Newtonbrook West'
        zip:'M2M1K3'
        ptype2:['House','Detached']
      }
      geoCoder.geocoding testInput,(err,ret)->
        if err
          console.error err
        should.not.exists(err)
        ret?._id?.should.be.exactly('CA:ON:TORONTO:M2M:120 HOMEWOOD AVE')
        done()
      return

    # TRBW8461112
    it 'should return uaddr with no zip3 ',(done)->
      @timeout 10000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Toronto'
        ptype:'r'
        addr:'205 Franklin Ave'
        cmty:'Dovercourt-Wallace Emerson-Junction'
        zip:'M6P3Z2'
        ptype2:['House','Semi-Detached']
      }
      geoCoder.geocoding testInput,(err,ret)->
        if err
          console.error err
        should.not.exists(err)
        ret?._id?.should.be.exactly('CA:ON:TORONTO:205 FRANKLIN AVE')
        done()
      return
    # TRBC4795612
    it 'should return uaddr with zip3',(done)->
      @timeout 10000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Toronto'
        ptype:'r'
        addr:'205 Franklin Ave'
        cmty:'Lansing-Westgate'
        zip:'M2N1C8'
        ptype2:['House','Detached']
      }
      geoCoder.geocoding testInput,(err,ret)->
        if err
          console.error err
        should.not.exists(err)
        ret?._id?.should.be.exactly('CA:ON:TORONTO:M2N:205 FRANKLIN AVE')
        done()
      return

  describe 'Test geoCodingAsync', ->
    it 'should return normally when doGeoCoding and no similar record',()->
      @timeout 30000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Newmarket'
        ptype:'r'
        addr:'503 Silken Laumann Dr'
        cmty:'Stonehaven-Wyndham'
        zip:'L4G7Y3'
        ptype2:['Apartment','Loft']
      }
      try
        ret = await geoCodingAsync testInput
      catch err
        console.error err
        should.not.exists(err)
      should.exists(ret)
      ret._id?.should.be.exactly('CA:ON:NEWMARKET:503 SILKEN LAUMANN DR')
      return
    
    it 'should return normally when doGeoCoding and has similar record',()->
      @timeout 30000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Newmarket'
        ptype:'r'
        addr:'471 Silken Laumann Dr'
        cmty:'Stonehaven-Wyndham'
        zip:'L4G7Y3'
        ptype2:['Apartment','Loft']
      }
      try
        ret = await geoCodingAsync testInput
      catch err
        console.error err
        should.not.exists(err)
      should.exists(ret)
      ret._id?.should.be.exactly('CA:ON:NEWMARKET:471 SILKEN LAUMANN DR')
      return

  describe 'Test geocoding finding similar uaddr when ptype2 isnt Apartment', ->
    it 'shoul find similar geoCache when ptype2 isnt Apartment',(done)->
      @timeout 10000
      testInput = {
        cnty:'CA'
        prov:'Ontario'
        city:'Newmarket'
        ptype:'r'
        addr:'471 Silken Laumann Dr'
        st_dir:'N'
        cmty:'Stonehaven-Wyndham'
        zip:'L3X2H9'
        ptype2:['House', 'Detached', 'Bungalow']
      }
      geoCoder.geocoding testInput,(err,ret)->
        if err
          console.error err
        should.not.exists(err)
        ret?._id?.should.be.exactly('CA:ON:NEWMARKET:471 SILKEN LAUMANN DR N')
        done()
      return

  describe 'Test mergeGeoCache', ->
    it 'should merge geoCache',()->
      @timeout 30000
      testIds = [
        'CA:ON:MISSISSAUGA:25 KINGSBRIDGE GARDEN CIR',
        'CA:ON:MISSISSAUGA:25 KINGSBRIDGE GARD CIR'
      ]
      records = await gGeoCache.findToArray {_id:{$in:testIds}},{sort:{_id:1}}
      try
        ret = await geoCoderLocal.mergeGeoCache records
      catch err
        console.error err
        should.not.exists(err)
      
      should.exists(ret)
      mergedRec = await gGeoCache.findToArray {aUaddr:{$in:testIds}}
      mergedRec.length.should.be.exactly(1)
      mergedRec[0]?._id.should.be.exactly('CA:ON:MISSISSAUGA:25 KINGSBRIDGE GARD CIR')
      rec = await gGeoCache.findOne {_id:'CA:ON:MISSISSAUGA:25 KINGSBRIDGE GARDEN GDNS'}
      should.exists(rec)
      should.deepEqual(rec.aUaddr,['CA:ON:MISSISSAUGA:25 KINGSBRIDGE GARDEN GDNS'])
      return

    tests = [
      {
        desc:'should merge geoCache'
        uaddrs:[
          'CA:ON:TORONTO:106 CHESTER LE BLVD',
          'CA:ON:TORONTO:106 CHESTER LE BLVD S',
          'CA:ON:TORONTO:106 CHESTER LE BLVD W',
          'CA:ON:TORONTO:106 CHESTER LE BLVD N'
        ]
        isExist:true
        expectedId:'CA:ON:TORONTO:106 CHESTER LE BLVD'
      },
      {
        desc:'should not merge when records isnt array'
        records:'test'
        isExist:false
      },
      {
        desc:'should not merge when records isnt array'
        records:{}
        isExist:false
      },
      {
        desc:'should not merge when records isnt array'
        records:111
        isExist:false
      },
      {
        desc:'should not merge when records isnt geoCache'
        records:[1,'123',{a:'test'}]
        isExist:false
      },
      {
        desc:'should not merge when no records'
        isExist:false
      },
      {
        desc:'should merge geoCache'
        uaddrs:[
          'CA:ON:NEWMARKET:471 SILKEN LAUMANN DR',
          'CA:ON:NEWMARKET:471 SILKEN LAUMANN DR N'
        ]
        isExist:true
        expectedId:'CA:ON:NEWMARKET:471 SILKEN LAUMANN DR N'
      },
    ]
    tests.forEach (test)->
      it test.desc, () ->
        @timeout 10000
        if test.uaddrs?.length
          records = await gGeoCache.findToArray {aUaddr:{$in:test.uaddrs}},{sort:{_id:1}}
        else
          records = test.records

        try
          ret = await geoCoderLocal.mergeGeoCache records
        catch err
          console.error err
          should.not.exists(err)

        if test.isExist
          should.exists(ret)
          mergedRec = await gGeoCache.findToArray {aUaddr:{$in:test.uaddrs}}
          mergedRec.length.should.be.exactly(1)
          mergedRec[0]?._id.should.be.exactly(test.expectedId)
        else
          should.not.exists(ret)
        return
  
  describe 'Test saveGeoCache', ->
    tests = [
      {
        desc:'should return error when no geoCodingResult'
        input:{}
        errMsg:'no geoCodingResult'
      },
      {
        desc:'should return error when no uaddrAfterFormat'
        input:{geoCodingResult:{}}
        errMsg:'no valid geocache id'
      },
      {
        desc:'should return error when no record, no uaddrZipAfterFormat and uaddrAfterFormat is invalid'
        input:{
          geoCodingResult:{
            zip:'L4M'
          },
          uaddrAfterFormat:'CA:ON:GRIMSBY'
        },
        errMsg:'Duplicate street with different zip'
      },
      {
        desc:'should insert new geo_cache',
        input:{
          geoCodingResult:{
            lat: 43.8732494,
            lng: -79.3132534,
            nelat: 43.8744192302915,
            nelng: -79.3118229197085,
            swlat: 43.8717212697085,
            swlng: -79.31452088029151,
            q: 100,
            faddr: '286 Main St, Unionville, ON L3R 2H2, Canada',
            geoAddr: '286 Main St',
            locality: [ 'Markham', 'Markham' ],
            st_num: '286',
            st: 'Main Street',
            cmty: 'Unionville',
            city: 'Markham',
            subCity: 'Markham',
            region: 'Regional Municipality of York',
            prov: 'Ontario',
            cnty: 'Canada',
            zip: 'L3R 2H2'
          },
          uaddrAfterFormat:'CA:ON:TORONTO:286 MAIN ST'
        },
        expectedId:'CA:ON:TORONTO:286 MAIN ST',
        expectedAuaddr:['CA:ON:TORONTO:286 MAIN ST']
        expectedFlds:{
          lat: 43.8732494,
          lng: -79.3132534,
          q: 100,
          zip: 'L3R2H2'
        }
      },
      {
        desc:'should update geo_cache',
        input:{
          geoCodingResult:{
            lat: 43.87325,
            lng: -79.31325,
            q: 130,
            src:'manual'
          },
          uaddrAfterFormat:'CA:ON:TORONTO:286 MAIN ST',
        },
        expectedId:'CA:ON:TORONTO:286 MAIN ST',
        expectedAuaddr:['CA:ON:TORONTO:286 MAIN ST'],
        expectedFlds:{
          lat: 43.87325,
          lng: -79.31325,
          q: 130,
          src:'manual'
          zip: 'L3R2H2'
        }
      },
      {
        desc:'should inset new geo_cache with uaddrZipAfterFormat',
        input:{
          geoCodingResult:{
            lat: 43.6875543,
            lng: -79.3016408,
            nelat: 43.6889163802915,
            nelng: -79.3002400697085,
            swlat: 43.6862184197085,
            swlng: -79.3029380302915,
            q: 100,
            faddr: '286 Main St, Toronto, ON M4C 4X4, Canada',
            geoAddr: '286 Main St',
            locality: [ 'Old Toronto', 'Toronto', 'Toronto' ],
            st_num: '286',
            st: 'Main Street',
            city: 'Toronto',
            subCity: 'Toronto',
            region: 'Toronto',
            prov: 'Ontario',
            cnty: 'Canada',
            zip: 'M4C 4X4'
          },
          uaddrAfterFormat:'CA:ON:TORONTO:286 MAIN ST',
          uaddrZipAfterFormat:'CA:ON:TORONTO:M4C:286 MAIN ST'
        },
        expectedId:'CA:ON:TORONTO:M4C:286 MAIN ST',
        expectedAuaddr:['CA:ON:TORONTO:M4C:286 MAIN ST']
      },
      {
        desc:'should update geo_cache with no uaddrZipAfterFormat, but id has zip',
        input:{
          geoCodingResult:{
            lat:43.753569
            lng:-79.421658
            q:130
            src:'manual',
            zip: 'M2N 1C8'
          }
          uaddrAfterFormat:'CA:ON:TORONTO:205 FRANKLIN AVE'
        },
        expectedId:'CA:ON:TORONTO:M2N:205 FRANKLIN AVE',
        expectedAuaddr:['CA:ON:TORONTO:M2N:205 FRANKLIN AVE','CA:ONTARIO:TORONTO:M2N:205 FRANKLIN AVE']
        expectedFlds:{
          lat:43.753569
          lng:-79.421658
          q:130
          src:'manual'
          zip: 'M2N1C8'
        }
      },
      {
        desc:'should merge geoCache records',
        input:{
          geoCodingResult:{
            src: 'bing', # 不涉及geocoding,可以不修改
            q: 100,
            qaddr: '75 St Nicholas, Bay Street Corridor, Toronto, ON M4Y0A5, CA',
            zip: 'M4Y0A5',
            lat: 43.667508,
            lng: -79.386485,
            nelat: 43.66364528242932,
            nelng: -79.39360450768443,
            swlat: 43.671370717570674,
            swlng: -79.37936549231556,
            faddr: '75 St Nicholas St, Toronto, ON M4Y 0A5, Canada',
            geoAddr: '75 St Nicholas St',
            locality: [ 'Bay Street Corridor', 'Toronto' ],
            cmty: 'Bay Street Corridor',
            city: 'Toronto',
            addr: '75 St Nicholas St',
            region: 'Toronto',
            cnty: 'CA',
            prov: 'ON',
            st: 'St Nicholas',
            st_sfx: 'St',
            st_num: '75',
            bingGeocode: 100
          },
          uaddrBeforeFormat:'CA:ONTARIO:TORONTO:75 NICHOLAS ST',
          uaddrAfterFormat:'CA:ON:TORONTO:75 NICHOLAS ST',
          geoUaddr:'CA:ON:TORONTO:75 ST NICHOLAS',
        },
        expectedId:'CA:ON:TORONTO:75 NICHOLAS ST',
        expectedAuaddr:['CA:ON:TORONTO:75 NICHOLAS ST','CA:ON:TORONTO:75 ST NICHOLAS','CA:ONTARIO:TORONTO:75 NICHOLAS ST']
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout 30000
        geoCoderLocal.saveGeoCache test.input,(err,ret)->
          if test.errMsg
            should.exists(err)
            errMsg = err.message or err.toString()
            errMsg.should.be.exactly(test.errMsg)
            return done()
          else
            should.not.exists(err)
            should.exists(ret)
            ret._id.should.be.exactly(test.expectedId)
            ret.aUaddr.sort()
            should.deepEqual(test.expectedAuaddr,ret.aUaddr)
            if test.expectedFlds
              for fld,val in test.expectedFlds
                ret[fld].should.be.exactly(val)
            return done()
        return
  
  describe 'Test api of manageLoc', ->
    tests = [
      {
        desc:'should only update prop loc'
        body:{
          id: 'TRBC9030458',
          lat: '43.7897268',
          lng: '-79.3598320',
          zip: 'M2J 4L4',
          st: 'Bracken Fernway',
          st_num: '16',
          cnty: 'CA',
          prov: 'Ontario',
          city: 'Toronto'
        },
        expected:{
          uaddr:'CA:ON:TORONTO:16 BRACKEN FERNWAY'
          lat: '43.7897268',
          lng: '-79.3598320',
          zip: 'M2J4L4',
        }
      },
      {
        desc:'should update prop loc and uaddr'
        body:{
          id: 'TRBE7364096',
          lat: '43.87271',
          lng: '-78.92949',
          zip: 'L5C 4P4',
          st: 'Prentice',
          st_sfx: 'Dr',
          st_num: '69',
          cnty: 'CA',
          prov: 'Ontario',
          city: 'Whitby'
        },
        expected:{
          uaddr:'CA:ON:WHITBY:L5C:69 PRENTICE DR'
          lat: '43.87271',
          lng: '-78.92949',
          zip: 'L5C4P4',
        }
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        @timeout 30000
        request
          .post('1.5/prop/manageLoc')
          .set('Cookie', testHelpers.userCookies['adminCookie'])
          .set('Accept', 'application/json')
          .send(test.body)
          .expect('Content-Type', /json/)
          .expect(200)
          .end (err, res)->
            if err
              console.error err
            should.not.exists(err)
            should.exists(res.body)
            res.body.ok?.should.be.exactly(1)
            PropertiesCol.findOne {_id:test.body.id},(err,prop)->
              if err
                console.error err
              should.not.exists(err)
              should.exists(prop)
              for key,val in test.expected
                prop[key].should.be.exactly(val)
              gGeoCache.findOne {aUaddr:test.expected.uaddr},(err,geo)->
                if err
                  console.error err
                should.not.exists(err)
                should.exists(geo)
                for key,val in test.expected
                  if key is 'uaddr'
                    continue
                  geo[key].should.be.exactly(val)
                done()
              return
            return
        return

  describe 'Test reverse geocoding with azure engine', ->
    it 'should reverse geocoding with azure engine by loc', ()->
      @timeout 30000
      input = {useCache:false,engineType:'azure',lat:43.6467616,lng:-79.4199928}
      expected = {addr : '114 Ossington Ave',city : 'Toronto',prov : 'ON',cnty : 'CA',src:'azure'}
      
      try
        result = await reverseGeocodingAsync(input)
        should.exists(result)
        for key,val of expected
          result[key].should.be.exactly(val)
      catch err
        console.error err
        should.not.exists(err)
      return
        
        
  describe 'Test function of isSimilarAddrOfPropAndGeo', ->
    tests = [
      {
        desc: 'should return false when prop and geoCache are empty objects'
        prop: {}
        geoCache: {}
        expected: false
      },
      {
        desc: 'should return false when st_num are different'
        prop: {
          st_num: '111'
          st: 'Elizabeth St'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        geoCache: {
          st_num: '112'
          st: 'Elizabeth St'
          st_sfx: 'St'
          addr: '112 Elizabeth St'
        }
        expected: false
      },
      {
        desc: 'should return false when st_sfx are different'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'Ave'
          addr: '111 Elizabeth Ave'
        }
        expected: false
      },
      {
        desc: 'should return false when st_dir are different'
        prop: {
          st_num: '111'
          st: 'Elizabeth St'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth St'
          st_sfx: 'St'
          st_dir: 'E'
          addr: '111 Elizabeth St E'
        }
        expected: false
      },
      {
        desc: 'should return true when all fields match exactly'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should return true when fields match case-insensitively'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'st'
          st_dir: 'w'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'ST'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should decode st_sfx and st_dir from addr when not present in prop'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should decode st_sfx and st_dir from addr when not present in geoCache'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should decode st_sfx and st_dir from addr when not present in both'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should use addr for comparison when st is missing in prop'
        prop: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should use addr for comparison when st is missing in geoCache'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should use addr for comparison when st is missing in both'
        prop: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        expected: true
      },
      {
        desc: 'should handle similar street names (Elizabeth vs Elizabeths)'
        prop: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeths'
          st_sfx: 'St'
          addr: '111 Elizabeths St'
        }
        expected: true
      },
      {
        desc: 'should handle partial street names (Spring Garden Ave vs Spring Ave)'
        prop: {
          st_num: '123'
          st: 'Spring Garden'
          st_sfx: 'Ave'
          addr: '123 Spring Garden Ave'
        }
        geoCache: {
          st_num: '123'
          st: 'Spring'
          st_sfx: 'Ave'
          addr: '123 Spring Ave'
        }
        expected: true
      },
      {
        desc: 'should return false when addr are completely different'
        prop: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 Elizabeth St W'
        }
        geoCache: {
          st_num: '111'
          st_sfx: 'St'
          st_dir: 'W'
          addr: '111 King St W'
        }
        expected: false
      },
      {
        desc: 'should handle numerical st_num as strings'
        prop: {
          st_num: 111
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        expected: true
      },
      {
        desc: 'should return false when one st_num is null/undefined'
        prop: {
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: 'Elizabeth St'
        }
        geoCache: {
          st_num: '111'
          st: 'Elizabeth'
          st_sfx: 'St'
          addr: '111 Elizabeth St'
        }
        expected: false
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        result = geoCoderLocal._isSimilarAddrOfPropAndGeoCache test.prop, test.geoCache
        result.should.be.exactly(test.expected)
        return
