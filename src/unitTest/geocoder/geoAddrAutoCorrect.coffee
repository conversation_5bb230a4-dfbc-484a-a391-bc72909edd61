###
coffee ./setup.coffee
./mate4.sh compile
./test.sh -f geocoder/geoAddrAutoCorrect.js

###
should = require('should')
# i18n  = require('../../built/lib/i18n')
# l10n = i18n.getFun 'zh-cn'
# _ab = (a,b)-> l10n abbreviator.ab(a,b),b
helpers = null
helpersLib = require('../../built/lib/helpers')
debug = null
Properties = null
User = null
Geocoder = null
request = null

    
describe 'test geocoder _fixAddrByFindSimilarAddrInGeoCacheResults',->
  before (done) ->
    @timeout(30000)
    helpers = require('../00_common/helpers')
    debug = helpers.DEBUG()
    request = helpers.getServer()

    Properties = helpers.MODEL 'Properties'
    Geocoder = helpers.MODEL 'GeoCoderLocal'
    User = helpers.COLLECTION 'chome','user'

    dbs = [
      { db: 'chome', table: 'user' },
      { db: 'vow', table: 'geo_cache' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, ()->
        setTimeout(done, 2500)
    return
  
  describe 'test _fixAddrByFindSimilarAddrInGeoCacheResults',->
    tests = [
      {
        desc:'should correct prop using geo_cache (st 字段 90% 的内容相同)',
        prop:{st_num:111,st:'Elizabeth St',addr:'111 Elizabeth St',st_sfx:'St'},
        geoRet:{lat: 43.65525232304722,lng: -79.38482735767127,st_num:111,st:'Elizabeths St',q:100},
        output:'CANADA:ONTARIO:TORONTO:111 ELIZABETHS ST'
      },
      {
        desc:'should correct prop using geo_cache (st 字段可分为两个部分，少了一段，但其中一段完全相同)',
        prop:{st_num:123,st:'Spring Garden Ave',addr:'123 Spring Garden Ave',st_sfx:'Ave'},
        geoRet:{lat: 43.76513172258374,lng: -79.40827664232874,st_num:123,st:'Spring Ave',q:100},
        output:'CANADA:ONTARIO:TORONTO:123 SPRING AVE'
      },
      {
        desc:'should correct prop using geo_cache',
        prop:{st_num:111,st:'Elizabeths St',addr:'111 Elizabeths St',st_sfx:'St'},
        geoRet:{lat: 43.65525232304722,lng: -79.38482735767127,st_num:111,st:'Elizabeths St',q:100},
        output:'CANADA:ONTARIO:TORONTO:111 ELIZABETHS ST'
      },
      {
        desc:'should correct prop using geo_cache (st_sfx 需要一致)',
        prop:{st_num:111,st:'Elizabeths St',addr:'111 Elizabeths St',st_sfx:'Dr'},
        geoRet:{lat: 43.65525232304722,lng: -79.38482735767127,st_num:111,st:'Elizabeths St',q:100},
        output:false
      },
      {
        desc:'should not correct prop using geo_cache(st_num 需要完全相同)',
        prop:{st_num:112,st:'Elizabeth St'},
        geoRet:{lat: 43.65525232304722,lng: -79.38482735767127,st_num:111,st:'Elizabeth St',q:100},
        output:false
      },
      {
        desc:'should not correct prop using geo_cache(st_sfx需要一致)',
        prop:{st_num:18,st:'Owen',addr:'18 Owen Dr'},
        geoRet:{lat: 43.74803,lng: -79.40102,st_num:18,st:'Owen',q:100,addr:'18 Owen Blvd'},
        output:false
      },
      {
        desc:'should not correct prop using geo_cache(st_dir需要一致)',
        prop:{st_num:79,st:'Clairfields',st_dir:'W',addr:'79 Clairfields Dr W',st_sfx:'Dr'},
        geoRet:{lat: 43.506111,lng: -80.1900625,st_num:79,st:'Clairfields',q:100,st_dir:'E',addr:'79 Clairfields Dr E',st_sfx:'Dr'},
        output:false
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        try
          ret = await Geocoder._fixAddrByFindSimilarAddrInGeoCacheResults test.prop,test.geoRet
        catch error
          debug.error error
          should.not.exists(error)
          return
        should.equal(test.output,ret)
        return

   
