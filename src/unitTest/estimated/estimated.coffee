# sh ./unitTest/test.sh -m estimated

should = require('should')
testHelpers = require('../00_common/helpers')
debug = null
request = null
testHelpers.userCookies = {}
EstimatedModel = null
i18n  = require('../../built/lib/i18n')
l10n = i18n.getFun 'en'
{formatMtBasedOnYear,dateFormat} = require('../../built/lib/helpers_date')

describe 'Estimated', ->
  before (done) ->
    @timeout(300000)
    request = testHelpers.getServer()
    debug = testHelpers.DEBUG()
    EstimatedModel = testHelpers.MODEL 'Estimated'
    dbs = [
      { db: 'chome', table: 'login' }
      { db: 'chome', table: 'user' }
      { db: 'chome', table: 'rm_group' }
      { db: 'vow', table: 'prop_estimate' }
    ]
    testHelpers.userCookies.empty = []
    testHelpers.cleanDBs dbs, () ->
      testHelpers.checkAndsetUpFixture {folder:__dirname}, ()->
        nameArray = ['userwithnorole','inrealgroup','noteadmin']
        nameArray.forEach (username)->
          request
            .post('1.5/user/login')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .expect('Content-Type', /json/)
            .send({isApi: true,email:username+'@realmaster.cc',pwd:123456})
            .end (err, res)->
              if err
                debug.error err
              if res.body.ok
                testHelpers.userCookies[username]= res.headers['set-cookie']
        setTimeout(done, 20000)
    return

  describe 'canEstimate', ->
    data = [
      {
        desc: 'merged房源不能估价'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: true
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '省份undefined'
        post:
          prov: undefined
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: undefined
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '省份是数组类型'
        post:
          prov: ['AB']
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: undefined
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '省份不存在'
        post:
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: undefined
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '省份不在允许范围内'
        post:
          prov: 'QC'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '上市日期小于最小日期'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230403'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '上市日期不是数字'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: 'abc'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型不正确'
        post:
          prov: 'ON'
          ptype: 'c'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型不存在'
        post:
          prov: 'ON'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型undefined'
        post:
          ptype: undefined
          prov: 'ON'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型null'
        post:
          ptype: null
          prov: 'ON'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型是数组且不正确'
        post:
          prov: 'ON'
          ptype: ['c']
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '房产类型是数组但正确'
        post:
          prov: 'ON'
          ptype: ['r']
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: true
      },
      {
        desc: '缺少ptype2'
        post:
          prov: 'ON'
          ptype: 'r'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: 'ptype2是undefined'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: undefined
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: 'ptype2不匹配任何类型'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Invalid'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: 'ptype2是数组但是不匹配任何类型'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: ['Invalid']
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '销售房源符合所有条件'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: true
      },
      {
        desc: 'ptype2为数组且有一个匹配'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: ['Invalid', 'Detached', 'Condo']
          onD: '20230405'
          merged: false
          saletp: "Sale"
        expected: true
      }
      {
        desc: 'ptype2为数组且都不匹配'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: ['Invalid1', 'Invalid2']
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: false
      },
      {
        desc: '上市日期转成数字，saletp是array'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected: true
      },
      {
        desc: '上市日期转成数字，saletp是Lease'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Lease"]
        expected: false
      },
      {
        desc: '上市日期转成数字，saletp是Lease,string'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: "Lease"
        expected: false
      },
      {
        desc: '上市日期转成数字，saletp是false'
        post:
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: false
        expected: false
      }
    ]
    data.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        result = EstimatedModel.canEstimate test.post
        should.deepEqual(result,test.expected)
        return

  describe '/estimated/getEstimate', ->
    data = [
      {
        desc: '缺少必要参数(propId, screenWidth, pointHalfWidth)'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBE5763487'
          propStatus: 'A'
          screenWidth: 375
        expected:
          ok: 0
          e: 'Bad Parameter'
      }
      {
        desc: '未登录且非分享模式'
        cookie: 'empty'
        post:
          propId: 'TRBE5763487'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
        expected:
          ok: 0
          e: 'Need login'
      }
      {
        desc: '房源状态为Active时可以查看'
        cookie: 'userwithnorole'
        post:
          propId: 'TRBE5763487'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
        expected:
          ok: 1
          result:
            estVal: 650000,
            estMin: 500000,
            estMax: 800000,
            estPost: 'left:187.5px',
            posLabel: 'left:167.5px',
            direction: 'center',
            greenWidth: '100%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      }
      {
        desc: '分享模式下查看Active房源'
        cookie: 'userwithnorole'
        post:
          propId: 'TRBE5763487'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
          shortUrlId: 'X4D8KIHvwG'
          saleType: 'Sale'
        expected:
          ok: 1
          result:
            estVal: 'Need login',
            estMin: 'Need login',
            estMax: 'Need login',
            estPost: 'left:187.5px',
            posLabel: 'left:167.5px',
            direction: 'center',
            greenWidth: '100%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      }
      {
        desc: '分享模式下不可以查看已售房源'
        cookie: 'userwithnorole'
        post:
          propId: 'TRBE5763487'
          propStatus: 'U'
          soldPrice: 700000
          screenWidth: 375
          pointHalfWidth: 11
          shortUrlId: 'X4D8KIHvwG'
          saleType: 'Sold'
        expected:
          ok: 1
          result: null
      }
      {
        desc: 'noteAdmin权限查看已售房源(售价高于估价)'
        cookie: 'noteadmin'
        post:
          propId: 'TRBE5763487'
          propStatus: 'U'
          soldPrice: 900000
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sold'
        expected:
          ok: 1
          result:
            estVal: 650000,
            estMin: 500000,
            estMax: 800000,
            soldPrice: 900000,
            soldPost: 'right:-11px',
            direction: 'right',
            redWidth: '25%',
            estPost: 'left:140.625px',
            posLabel: 'left:120.625px',
            greenWidth: '75%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      }
      {
        desc: 'inReal组成员查看已售房源(售价低于估价)'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBE5763487'
          propStatus: 'U'
          soldPrice: 400000
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sold'
        expected:
          ok: 1
          result:
            estVal: 650000,
            estMin: 500000,
            estMax: 800000,
            soldPrice: 400000,
            soldPost: 'left:11px',
            direction: 'left',
            blueWidth: '25%',
            estPost: 'left:234.375px',
            posLabel: 'left:214.375px',
            greenWidth: '75%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      }
      {
        desc: '普通用户无权查看已售房源'
        cookie: 'userwithnorole'
        post:
          propId: 'TRBE5763487'
          propStatus: 'U'
          soldPrice: 700000
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sold'
        expected:
          ok: 1
          result: null
      }
      {
        desc: '房源不存在'
        cookie: 'inrealgroup'
        post:
          propId: 'INVALID_ID'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
        expected:
          ok: 1
          result: null
      }
      {
        desc: '售价太靠左,标签方向left'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBW12018223'
          propStatus: 'U'
          screenWidth: 375
          pointHalfWidth: 11
          soldPrice: 1423000
        expected:
          ok: 1
          result:
            estVal: 1542000,
            estMin: 1419000,
            estMax: 1773000,
            soldPrice: 1423000,
            soldPost: 'left:11px',
            direction: 'left',
            estPost: 'left:130.29661016949154px',
            posLabel: 'left:110.29661016949154px',
            greenWidth: '100%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      },
      {
        desc: '售价太靠右,标签方向right'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBW12018233'
          propStatus: 'U'
          screenWidth: 375
          pointHalfWidth: 11
          soldPrice: 1763000
        expected:
          ok: 1
          result:
            estVal: 1700000,
            estMin: 1419000,
            estMax: 1773000,
            soldPrice: 1763000,
            soldPost: 'right:-11px',
            direction: 'right',
            estPost: 'left:297.6694915254237px',
            posLabel: 'left:277.6694915254237px',
            greenWidth: '100%',
            estMt: formatMtBasedOnYear('2024-03-20T10:00:00.000Z',l10n)
      }
      {
        desc: '估价靠近最小值'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBW12018235'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sale'
        expected:
          ok: 1
          result:
            estVal: 1429000,
            estMin: 1419000,
            estMax: 1773000,
            estPost: 'left:11px',
            posLabel: 'left:35px',
            direction: 'left',
            greenWidth: '100%',
            estMt: formatMtBasedOnYear('2024-03-21T10:00:00.000Z',l10n)
      }
      {
        desc: '未找到估价数据,且不能估价'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBW12018238'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sale'
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: false
        expected:
          ok: 1
          result: null
      },
      {
        desc: '未找到估价数据,能估价'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBW12018238'
          propStatus: 'A'
          screenWidth: 375
          pointHalfWidth: 11
          saleType: 'Sale'
          prov: 'ON'
          ptype: 'r'
          ptype2: 'Detached'
          onD: '20230405'
          merged: false
          saletp: ["Sale"]
        expected:
          ok: 1
          result:
            estVal: 'Waiting'
      }
    ]
    
    data.forEach (test)->
      it test.desc, (done)->
        @timeout(30000)
        request
          .post('estimated/getEstimate')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', testHelpers.userCookies[test.cookie])
          .send(test.post)
          .end (err, res)->
            if err
              debug.error err
            should.not.exists(err)
            debug.debug 'res.body',res.body,'test.expected',test.expected
            should.exists(res.body)
            should.deepEqual(test.expected,res.body)
            done()
        return 

  describe '/estimated/getAllEstimate', ->
    data = [
      {
        desc: '缺少必要参数(propId)'
        cookie: 'inrealgroup'
        post: {}
        expected:
          ok: 0
          e: 'Bad Parameter'
      }
      {
        desc: 'inReal组成员可以访问'
        cookie: 'inrealgroup'
        post:
          propId: 'TRBE5763487'
        expected:
          ok: 1
          result:
            mt_predict_n: '2024-03-20T10:00:00.000Z'
            sp_mn_result: 650000
            sp_mn_min: 500000
            sp_mn_max: 800000
            ml_mt: dateFormat('2024-03-20T10:00:00.000Z','YY-MM-DD HH:MI')
            estVal: 650000
            _id: "TRBE5763487"
      }
      {
        desc: 'noteAdmin可以访问'
        cookie: 'noteadmin'
        post:
          propId: 'TRBW12018235'
        expected:
          ok: 1
          result:
            mt_predict_n: '2024-03-20T10:00:00.000Z'
            mt_predict_d: '2024-03-21T10:00:00.000Z'
            sp_mn_result: 1700000
            sp_mn_min: 1419000
            sp_mn_max: 1773000
            sp_md_result: 1429000
            sp_md_min: 1419000
            sp_md_max: 1773000
            ml_mt: dateFormat('2024-03-21T10:00:00.000Z','YY-MM-DD HH:MI'),
            _id: "TRBW12018235"
            estVal: 1429000
      }
      {
        desc: '房源不存在'
        cookie: 'inrealgroup'
        post:
          propId: 'INVALID_ID'
        expected:
          ok: 1
          result: null
      }
    ]
    
    data.forEach (test)->
      it test.desc, (done)->
        @timeout(30000)
        request
          .post('estimated/getAllEstimate')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', testHelpers.userCookies[test.cookie])
          .send(test.post)
          .end (err, res)->
            if err
              debug.error err
            should.not.exists(err)
            debug.debug 'res.body',res.body,'test.expected',test.expected
            should.exists(res.body)
            should.deepEqual(test.expected,res.body)
            done()
        return 