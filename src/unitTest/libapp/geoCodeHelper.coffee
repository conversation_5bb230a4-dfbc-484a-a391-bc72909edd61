should = require('should')
libGeoCodeHelper = require('../../built/libapp/geoCodeHelper')
# ./test.sh -f libapp/geoCodeHelper.js
describe 'GeoCodeHelper function tests',->
  before (done) ->
    @timeout(300000)
    done()
    
  describe 'adjustGeocodingQuality ', ->
    tests = [
      {
        geoCodingResult:{
          lat: 43.8732494,
          lng: -79.3132534,
          nelat: 43.8744192302915,
          nelng: -79.3118229197085,
          swlat: 43.8717212697085,
          swlng: -79.31452088029151,
          q: 100,
          faddr: '286 Main St, Unionville, ON L3R 2H2, Canada',
          st_num: '286',
          st: 'Main Street',
          cmty: 'Unionville',
          city: 'Markham',
          subCity: 'Markham',
          region: 'Regional Municipality of York',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'L3R 2H2'
          geoAddr: '286 Main St',
          locality: [ 'locality', 'administrative_area_level_3' ],
        },
        qaddr:'286 Main St W, Toronto, ON L3R2H2, CA',
        zip:'L3R2H2',
        expected:{
          lat: 43.8732494,
          lng: -79.3132534,
          nelat: 43.8744192302915,
          nelng: -79.3118229197085,
          swlat: 43.8717212697085,
          swlng: -79.31452088029151,
          q: 90,
          faddr: '286 Main St, Unionville, ON L3R 2H2, Canada',
          st_num: '286',
          st: 'Main Street',
          cmty: 'Unionville',
          city: 'Markham',
          subCity: 'Markham',
          region: 'Regional Municipality of York',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'L3R 2H2',
          geoAddr: '286 Main St',
          locality: [ 'locality', 'administrative_area_level_3' ],
          badFlds: [ 'city' ]
        }
      },
      {
        geoCodingResult:{
          lat: 50.7056325,
          lng: -119.2818864,
          nelat: 50.7069783802915,
          nelng: -119.2805613197085,
          swlat: 50.7042804197085,
          swlng: -119.2832592802915,
          q: 100,
          faddr: '750 Marine Park Dr, Salmon Arm, BC V1E 2W7, Canada',
          st_num: '750',
          st: 'Marine Park Drive',
          city: 'Salmon Arm',
          region: 'Columbia-Shuswap',
          prov: 'British Columbia',
          cnty: 'Canada',
          zip: 'V1E 2W7'
        },
        qaddr:'750 Marine Park Dr, Alberta, AB V1E2W7, CA',
        # zip:'V1E2W7',
        expected:{
          lat: 50.7056325,
          lng: -119.2818864,
          nelat: 50.7069783802915,
          nelng: -119.2805613197085,
          swlat: 50.7042804197085,
          swlng: -119.2832592802915,
          q: 80,
          faddr: '750 Marine Park Dr, Salmon Arm, BC V1E 2W7, Canada',
          st_num: '750',
          st: 'Marine Park Drive',
          city: 'Salmon Arm',
          region: 'Columbia-Shuswap',
          prov: 'British Columbia',
          cnty: 'Canada',
          zip: 'V1E 2W7',
          badFlds: [ 'prov', 'city' ]
        }
      },
      {
        geoCodingResult:{
          lat: 33.7700504,
          lng: -118.1937395,
          nelat: 33.8854591,
          nelng: -118.0632639,
          swlat: 33.714957,
          swlng: -118.248966,
          q: 75,
          faddr: 'Long Beach, CA, USA',
          city: 'Long Beach',
          region: 'Los Angeles County',
          prov: 'California',
          cnty: 'United States'
        },
        qaddr:'5652 Longbeach Rd, Balfour, AB, CA',
        # zip:'V1L5P6',
        expected:{
          lat: 33.7700504,
          lng: -118.1937395,
          nelat: 33.8854591,
          nelng: -118.0632639,
          swlat: 33.714957,
          swlng: -118.248966,
          q: 35,
          faddr: 'Long Beach, CA, USA',
          city: 'Long Beach',
          region: 'Los Angeles County',
          prov: 'California',
          cnty: 'United States',
          badFlds: [ 'cnty', 'prov', 'city' ]
        }
      },
      {
        geoCodingResult:{
          lat: 43.6875543,
          lng: -79.3016408,
          nelat: 43.6889163802915,
          nelng: -79.3002400697085,
          swlat: 43.6862184197085,
          swlng: -79.3029380302915,
          q: 100,
          faddr: '286 Main St, Toronto, ON M4C 4X4, Canada',
          st_num: '286',
          st: 'Main Street',
          city: 'Toronto',
          subCity: 'Toronto',
          region: 'Toronto',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'M4C 4X4',
          geoAddr: '286 Main St',
          locality: [ 'sublocality', 'locality', 'administrative_area_level_3' ],
        },
        qaddr:'286 Main St W, Toronto, ON, CA',
        zip:'L3R2H2',
        expected:{
          lat: 43.6875543,
          lng: -79.3016408,
          nelat: 43.6889163802915,
          nelng: -79.3002400697085,
          swlat: 43.6862184197085,
          swlng: -79.3029380302915,
          q: 90,
          faddr: '286 Main St, Toronto, ON M4C 4X4, Canada',
          st_num: '286',
          st: 'Main Street',
          city: 'Toronto',
          subCity: 'Toronto',
          region: 'Toronto',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'M4C 4X4',
          geoAddr: '286 Main St',
          locality: [ 'sublocality', 'locality', 'administrative_area_level_3' ],
          badFlds: [ 'zip' ]
        }
      },
      {
        geoCodingResult:{
          lat: 51.244502,
          lng: -114.485706,
          nelat: 51.24600788029149,
          nelng: -114.4844872697085,
          swlat: 51.24330991970849,
          swlng: -114.4871852302915,
          q: 100,
          faddr: '35 Cochrane Lake Trail, Cochrane, AB T4C 2A8, Canada',
          st_num: '35',
          st: 'Cochrane Lake Trail',
          city: 'Cochrane',
          region: 'Rocky View',
          prov: 'Alberta',
          cnty: 'Canada',
          zip: 'T4C 2A8'
        },
        qaddr:'35 Cochrane Lake Tr, Cochrane Lake, AB T4C2A8, CA',
        zip:'T4C2A8',
        expected:{
          lat: 51.244502,
          lng: -114.485706,
          nelat: 51.24600788029149,
          nelng: -114.4844872697085,
          swlat: 51.24330991970849,
          swlng: -114.4871852302915,
          q: 100,
          faddr: '35 Cochrane Lake Trail, Cochrane, AB T4C 2A8, Canada',
          st_num: '35',
          st: 'Cochrane Lake Trail',
          city: 'Cochrane',
          region: 'Rocky View',
          prov: 'Alberta',
          cnty: 'Canada',
          zip: 'T4C 2A8'
        }
      },
      {
        geoCodingResult:{m:'should not change anything when no qaddr'},
        expected:{m:'should not change anything when no qaddr'}
      }
    ]
    tests.forEach (test)->
      it 'should adjust geocode result by country,province,city,zip', (done)->
        libGeoCodeHelper.adjustGeocodingQuality test.geoCodingResult,test.qaddr,test.zip
        should.deepEqual(test.expected,test.geoCodingResult)
        done()

  describe 'parseGoogleResult ', ->
    tests = [
      {
        geoResult:{
          address_components: [
            { long_name: '286', short_name: '286', types: ['street_number'] },
            { long_name: 'Main Street', short_name: 'Main St', types: ['route'] },
            {
              long_name: 'Old Toronto',
              short_name: 'Old Toronto',
              types: ['political', 'sublocality', 'sublocality_level_1'],
            },
            {
              long_name: 'Toronto',
              short_name: 'Toronto',
              types: ['locality', 'political'],
            },
            {
              long_name: 'Toronto',
              short_name: 'Toronto',
              types: ['administrative_area_level_3', 'political'],
            },
            {
              long_name: 'Toronto',
              short_name: 'Toronto',
              types: ['administrative_area_level_2', 'political'],
            },
            {
              long_name: 'Ontario',
              short_name: 'ON',
              types: ['administrative_area_level_1', 'political'],
            },
            {
              long_name: 'Canada',
              short_name: 'CA',
              types: ['country', 'political'],
            },
            {
              long_name: 'M4C 4X4',
              short_name: 'M4C 4X4',
              types: ['postal_code'],
            },
          ],
          formatted_address: '286 Main St, Toronto, ON M4C 4X4, Canada',
          geometry: {
            location: { lat: 43.6875543, lng: -79.3016408 },
            location_type: 'ROOFTOP',
            viewport: {
              northeast: { lat: 43.6889163802915, lng: -79.3002400697085 },
              southwest: { lat: 43.6862184197085, lng: -79.3029380302915 },
            },
          },
          place_id: 'ChIJ8Qe74y7N1IkRw4ygUSklU-Y',
          plus_code: {
            compound_code: 'MMQX+28 Toronto, ON, Canada',
            global_code: '87M2MMQX+28',
          },
          types: ['street_address'],
        }
        expected:{
          lat: 43.6875543,
          lng: -79.3016408,
          nelat: 43.6889163802915,
          nelng: -79.3002400697085,
          swlat: 43.6862184197085,
          swlng: -79.3029380302915,
          q: 100,
          faddr: '286 Main St, Toronto, ON M4C 4X4, Canada',
          geoAddr: '286 Main St',
          locality: [ 'Old Toronto', 'Toronto', 'Toronto' ],
          st_num: '286',
          st: 'Main Street',
          city: 'Toronto',
          subCity: 'Toronto',
          region: 'Toronto',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'M4C 4X4'
        }
      },
      {
        geoResult:{
          address_components: [
            {
              long_name: '1010',
              short_name: '1010',
              types: ['street_number'],
            },
            {
              long_name: 'Cobblestone Boulevard Southwest',
              short_name: 'Cobblestone Blvd SW',
              types: ['route'],
            },
            {
              long_name: 'Balzac',
              short_name: 'Balzac',
              types: ['locality', 'political'],
            },
            {
              long_name: 'Rocky View',
              short_name: 'Rocky View',
              types: ['administrative_area_level_2', 'political'],
            },
            {
              long_name: 'Alberta',
              short_name: 'AB',
              types: ['administrative_area_level_1', 'political'],
            },
            {
              long_name: 'Canada',
              short_name: 'CA',
              types: ['country', 'political'],
            },
            {
              long_name: 'T0M 0E0',
              short_name: 'T0M 0E0',
              types: ['postal_code'],
            },
          ],
          formatted_address: '1010 Cobblestone Blvd SW, Balzac, AB T0M 0E0, Canada',
          geometry: {
            location: {
              lat: 51.2626564,
              lng: -114.0420046,
            },
            location_type: 'ROOFTOP',
            viewport: {
              northeast: {
                lat: 51.2639804302915,
                lng: -114.0408650697085,
              },
              southwest: {
                lat: 51.2612824697085,
                lng: -114.0435630302915,
              },
            },
          },
          place_id: 'ChIJSSkrrFNecVMRH14BYqJ2hSQ',
          plus_code: {
            compound_code: '7X75+35 Balzac, AB, Canada',
            global_code: '95377X75+35',
          },
          types: ['street_address'],
        }
        expected:{
          lat: 51.2626564,
          lng: -114.0420046,
          nelat: 51.2639804302915,
          nelng: -114.0408650697085,
          swlat: 51.2612824697085,
          swlng: -114.0435630302915,
          q: 100,
          faddr: '1010 Cobblestone Blvd SW, Balzac, AB T0M 0E0, Canada',
          geoAddr: '1010 Cobblestone Blvd SW',
          locality: [ 'Balzac' ],
          st_num: '1010',
          st: 'Cobblestone Boulevard Southwest',
          city: 'Balzac',
          region: 'Rocky View',
          prov: 'Alberta',
          cnty: 'Canada',
          zip: 'T0M 0E0'
        }
      },
      {
        geoResult:{
          address_components: [
            { long_name: '286', short_name: '286', types: ['street_number'] },
            { long_name: 'Main Street', short_name: 'Main St', types: ['route'] },
            {
              long_name: 'Unionville',
              short_name: 'Unionville',
              types: ['neighborhood', 'political'],
            },
            {
              long_name: 'Markham',
              short_name: 'Markham',
              types: ['locality', 'political'],
            },
            {
              long_name: 'Markham',
              short_name: 'Markham',
              types: ['administrative_area_level_3', 'political'],
            },
            {
              long_name: 'Regional Municipality of York',
              short_name: 'Regional Municipality of York',
              types: ['administrative_area_level_2', 'political'],
            },
            {
              long_name: 'Ontario',
              short_name: 'ON',
              types: ['administrative_area_level_1', 'political'],
            },
            { long_name: 'Canada', short_name: 'CA', types: ['country', 'political'] },
            { long_name: 'L3R 2H2', short_name: 'L3R 2H2', types: ['postal_code'] },
          ],
          formatted_address: '286 Main St, Unionville, ON L3R 2H2, Canada',
          geometry: {
            location: { lat: 43.8732494, lng: -79.3132534 },
            location_type: 'ROOFTOP',
            viewport: {
              northeast: { lat: 43.8744192302915, lng: -79.3118229197085 },
              southwest: { lat: 43.8717212697085, lng: -79.31452088029151 },
            },
          },
          place_id: 'ChIJq5ufmbjV1IkRgI6CkSYlfCw',
          plus_code: {
            compound_code: 'VMFP+7M Markham, ON, Canada',
            global_code: '87M2VMFP+7M',
          },
          types: ['street_address'],
        },
        expected:{
          lat: 43.8732494,
          lng: -79.3132534,
          nelat: 43.8744192302915,
          nelng: -79.3118229197085,
          swlat: 43.8717212697085,
          swlng: -79.31452088029151,
          q: 100,
          faddr: '286 Main St, Unionville, ON L3R 2H2, Canada',
          geoAddr: '286 Main St',
          locality: [ 'Markham', 'Markham' ],
          st_num: '286',
          st: 'Main Street',
          cmty: 'Unionville',
          city: 'Markham',
          subCity: 'Markham',
          region: 'Regional Municipality of York',
          prov: 'Ontario',
          cnty: 'Canada',
          zip: 'L3R 2H2'
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of google', (done)->
        ret = {}
        libGeoCodeHelper.parseGoogleResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()

  describe 'parseBingResult ', ->
    tests = [
      {
        geoResult:{
          __type: 'Location:http://schemas.microsoft.com/search/local/ws/rest/v1',
          bbox: [
            43.88667318242933, -79.29119521026597, 43.89439861757068,
            -79.27690298973404,
          ],
          name: '77 James Parrott Ave, Markham, ON L6E 0E4, Canada',
          point: { type: 'Point', coordinates: [43.8905359, -79.2840491] },
          address: {
            addressLine: '77 James Parrott Ave',
            adminDistrict: 'ON',
            adminDistrict2: 'York',
            countryRegion: 'Canada',
            formattedAddress: '77 James Parrott Ave, Markham, ON L6E 0E4, Canada',
            locality: 'Markham',
            neighborhood: 'Markham 16th',
            postalCode: 'L6E 0E4',
          },
          confidence: 'High',
          entityType: 'Address',
          geocodePoints: [
            {
              type: 'Point',
              coordinates: [43.8905359, -79.2840491],
              calculationMethod: 'Rooftop',
              usageTypes: ['Display'],
            },
            {
              type: 'Point',
              coordinates: [43.8907808, -79.2840582],
              calculationMethod: 'Rooftop',
              usageTypes: ['Route'],
            },
          ],
          matchCodes: ['Good'],
        },
        expected:{
          lat: 43.8905359,
          lng: -79.2840491,
          q: 100,
          nelat: 43.88667318242933,
          nelng: -79.29119521026597,
          swlat: 43.89439861757068,
          swlng: -79.27690298973404,
          faddr: '77 James Parrott Ave, Markham, ON L6E 0E4, Canada',
          geoAddr: '77 James Parrott Ave',
          locality: [ 'Markham 16th', 'Markham' ],
          zip: 'L6E 0E4',
          cmty: 'Markham 16th',
          city: 'Markham',
          addr: '77 James Parrott Ave',
          region: 'York',
          cnty: 'Canada',
          prov: 'ON'
        }
      },
      {
        geoResult:{
          __type: 'Location:http://schemas.microsoft.com/search/local/ws/rest/v1',
          bbox: [
            43.68395828242932, -79.30981891793898, 43.691683717570676, -79.295575082061,
          ],
          name: '286 Main St, Toronto, ON M4C 0B3, Canada',
          point: { type: 'Point', coordinates: [43.687821, -79.302697] },
          address: {
            addressLine: '286 Main St',
            adminDistrict: 'ON',
            adminDistrict2: 'Toronto',
            countryRegion: 'Canada',
            formattedAddress: '286 Main St, Toronto, ON M4C 0B3, Canada',
            locality: 'Toronto',
            neighborhood: 'East End',
            postalCode: 'M4C 0B3',
          },
          confidence: 'Medium',
          entityType: 'Address',
          geocodePoints: [
            {
              type: 'Point',
              coordinates: [43.687821, -79.302697],
              calculationMethod: 'Rooftop',
              usageTypes: ['Display'],
            },
            {
              type: 'Point',
              coordinates: [43.6880584, -79.3017779],
              calculationMethod: 'Rooftop',
              usageTypes: ['Route'],
            },
          ],
          matchCodes: ['Ambiguous'],
        },
        expected:{
          lat: 43.687821,
          lng: -79.302697,
          q: 45,
          nelat: 43.68395828242932,
          nelng: -79.30981891793898,
          swlat: 43.691683717570676,
          swlng: -79.295575082061,
          faddr: '286 Main St, Toronto, ON M4C 0B3, Canada',
          geoAddr: '286 Main St',
          locality: [ 'East End', 'Toronto' ],
          zip: 'M4C 0B3',
          cmty: 'East End',
          city: 'Toronto',
          addr: '286 Main St',
          region: 'Toronto',
          cnty: 'Canada',
          prov: 'ON'
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of bing', (done)->
        ret = {}
        libGeoCodeHelper.parseBingResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()

  describe 'parseMapquestResult ', ->
    tests = [
      {
        geoResult:{
          providedLocation: { location: '21 Steamboat Way,Whitby, ON, CA' },
          locations: [
            {
              street: '21 Steamboat Way',
              adminArea6: '',
              adminArea6Type: 'Neighborhood',
              adminArea5: 'Whitby',
              adminArea5Type: 'City',
              adminArea4: 'Durham',
              adminArea4Type: 'County',
              adminArea3: 'ON',
              adminArea3Type: 'State',
              adminArea1: 'CA',
              adminArea1Type: 'Country',
              postalCode: 'L1N0M6',
              geocodeQualityCode: 'P1AAA',
              geocodeQuality: 'POINT',
              dragPoint: false,
              sideOfStreet: 'R',
              linkId: 'r28233062|p102542944|n221257625',
              unknownInput: '',
              type: 's',
              latLng: { lat: 43.852577, lng: -78.943376 },
              displayLatLng: { lat: 43.852634, lng: -78.943187 },
              mapUrl: 'http://www.mapquestapi.com/staticmap/v5/map?key=********************************&type=map&size=225,160&\
              locations=43.852577,-78.943376|marker-sm-50318A-1&scalebar=true&zoom=15&rand=205241605'
            },
          ],
        },
        expected:{
          lat: 43.852634,
          lng: -78.943187,
          zip: 'L1N0M6',
          addr: '21 Steamboat Way',
          area: 'Durham',
          cnty: 'CA',
          prov: 'ON',
          city: 'Whitby',
          q: 100
        }
      },
      {
        geoResult:{
          providedLocation: { location: '3590 Hwy 3,Rock Creek/Bridesville, BC, CA' },
          locations: [
            {
              street: '[1595 - 1595] Rock Creek Rd, 3',
              adminArea6: '',
              adminArea6Type: 'Neighborhood',
              adminArea5: 'Bridesville',
              adminArea5Type: 'City',
              adminArea4: 'Kootenay Boundary',
              adminArea4Type: 'County',
              adminArea3: 'BC',
              adminArea3Type: 'State',
              adminArea1: 'CA',
              adminArea1Type: 'Country',
              postalCode: 'V0H',
              geocodeQualityCode: 'B3BAA',
              geocodeQuality: 'STREET',
              dragPoint: false,
              sideOfStreet: 'R',
              linkId: 'rnr7750006|i65865098',
              unknownInput: '',
              type: 's',
              latLng: { lat: 49.053058, lng: -118.997624 },
              displayLatLng: { lat: 49.053118, lng: -118.997433 },
              mapUrl: 'http://www.mapquestapi.com/staticmap/v5/map?key=********************************&type=map&size=225,160&\
              locations=49.053058,-118.997624|marker-sm-50318A-1&scalebar=true&zoom=15&rand=-65515934',
            }
          ],
        },
        expected:{
          lat: 49.053118,
          lng: -118.997433,
          zip: 'V0H',
          addr: '[1595 - 1595] Rock Creek Rd, 3',
          area: 'Kootenay Boundary',
          cnty: 'CA',
          prov: 'BC',
          city: 'Bridesville',
          q: 10
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of mapquest', (done)->
        ret = {}
        libGeoCodeHelper.parseMapquestResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()

  describe 'parseHereResult ', ->
    tests = [
      {
        geoResult:{
          title: '286 Main St, Toronto, ON M4C 0B3, Canada',
          id: 'here:af:streetsection:7-19ABTh1aiRb8jfuH6xFD:CggIBCDvuKe6AhABGgMyODY',
          resultType: 'houseNumber',
          houseNumberType: 'PA',
          address: {
            label: '286 Main St, Toronto, ON M4C 0B3, Canada',
            countryCode: 'CAN',
            countryName: 'Canada',
            stateCode: 'ON',
            state: 'Ontario',
            county: 'Toronto',
            city: 'Toronto',
            district: 'East End-Danforth',
            street: 'Main St',
            postalCode: 'M4C 0B3',
            houseNumber: '286',
          },
          position: { lat: 43.68748, lng: -79.30187 },
          access: [{ lat: 43.68751, lng: -79.30171 }],
          mapView: {
            west: -79.30311,
            south: 43.68658,
            east: -79.30063,
            north: 43.68838,
          },
          scoring: {
            queryScore: 0.74,
            fieldScore: {
              country: 1,
              state: 1,
              city: 1,
              streets: [1],
              houseNumber: 1,
            },
          },
        },
        expected:{
          q: 74,
          lat: 43.68748,
          lng: -79.30187,
          faddr: '286 Main St, Toronto, ON M4C 0B3, Canada',
          geoAddr: '286 Main St',
          locality: [ 'Toronto', 'East End-Danforth' ],
          zip: 'M4C 0B3',
          cnty: 'Canada',
          region: 'Toronto',
          prov: 'Ontario',
          city: 'Toronto',
          subCity: 'East End-Danforth',
          st_num: '286',
          st: 'Main St'
        }
      },
      {
        geoResult:{
          title: '1328 Birchmount Rd, Toronto, ON M1R 0B6, Canada',
          id: 'here:af:streetsection:glon8u.r6xPYGaOC4pmwyC:CggIBCC8jsD9ARABGgQxMzI4',
          resultType: 'houseNumber',
          houseNumberType: 'PA',
          address: {
            label: '1328 Birchmount Rd, Toronto, ON M1R 0B6, Canada',
            countryCode: 'CAN',
            countryName: 'Canada',
            stateCode: 'ON',
            state: 'Ontario',
            county: 'Toronto',
            city: 'Toronto',
            district: 'Wexford-Maryvale',
            street: 'Birchmount Rd',
            postalCode: 'M1R 0B6',
            houseNumber: '1328',
          },
          position: { lat: 43.74851, lng: -79.28607 },
          access: [{ lat: 43.74867, lng: -79.28528 }],
          mapView: {
            west: -79.28731,
            south: 43.74761,
            east: -79.28483,
            north: 43.74941,
          },
          scoring: {
            queryScore: 0.95,
            fieldScore: {
              country: 1,
              state: 1,
              city: 1,
              streets: [1],
              houseNumber: 1,
              postalCode: 1,
            },
          },
        },
        expected:{
          q: 95,
          lat: 43.74851,
          lng: -79.28607,
          faddr: '1328 Birchmount Rd, Toronto, ON M1R 0B6, Canada',
          geoAddr: '1328 Birchmount Rd',
          locality: [ 'Toronto', 'Wexford-Maryvale' ],
          zip: 'M1R 0B6',
          cnty: 'Canada',
          region: 'Toronto',
          prov: 'Ontario',
          city: 'Toronto',
          subCity: 'Wexford-Maryvale',
          st_num: '1328',
          st: 'Birchmount Rd'
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of here', (done)->
        ret = {}
        libGeoCodeHelper.parseHereResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()

  describe 'parseMapboxResult ', ->
    tests = [
      {
        geoResult:{
          id: 'address.6594312695891112',
          type: 'Feature',
          place_type: ['address'],
          relevance: 0.962963,
          properties: {
            accuracy: 'rooftop',
            mapbox_id:'dXJuOm1ieGFkcjo4ZTA2MjU4MS03M2U5LTQ2YWYtOTAxNC1jYmE4MTA5M2ZmMzA',
          },
          text_en: 'Main Street',
          place_name_en: '286 Main Street, Toronto, Ontario M4C 4X5, Canada',
          text: 'Main Street',
          place_name: '286 Main Street, Toronto, Ontario M4C 4X5, Canada',
          center: [-79.301863, 43.687474],
          geometry: { type: 'Point', coordinates: [-79.301863, 43.687474] },
          address: '286',
          context: [
            {
              id: 'neighborhood.23997479',
              mapbox_id: 'dXJuOm1ieHBsYzpBVzRzSnc',
              text_en: 'The Danforth',
              text: 'The Danforth',
            },
            { id: 'postcode.6594312695891112', text_en: 'M4C 4X5', text: 'M4C 4X5' },
            {
              id: 'locality.27150887',
              mapbox_id: 'dXJuOm1ieHBsYzpBWjVLSnc',
              text_en: 'East End',
              text: 'East End',
            },
            {
              id: 'place.80693287',
              mapbox_id: 'dXJuOm1ieHBsYzpCTTlJSnc',
              wikidata: 'Q172',
              text_en: 'Toronto',
              language_en: 'en',
              text: 'Toronto',
              language: 'en',
            },
            {
              id: 'region.17447',
              mapbox_id: 'dXJuOm1ieHBsYzpSQ2M',
              wikidata: 'Q1904',
              short_code: 'CA-ON',
              text_en: 'Ontario',
              language_en: 'en',
              text: 'Ontario',
              language: 'en',
            },
            {
              id: 'country.8743',
              mapbox_id: 'dXJuOm1ieHBsYzpJaWM',
              wikidata: 'Q16',
              short_code: 'ca',
              text_en: 'Canada',
              language_en: 'en',
              text: 'Canada',
              language: 'en',
            },
          ],
        }
        expected:{
          q: 96.2963,
          lat: 43.687474,
          lng: -79.301863,
          faddr: '286 Main Street, Toronto, Ontario M4C 4X5, Canada',
          geoAddr: '286 Main Street',
          st_num: '286',
          st: 'Main Street',
          locality: [ 'The Danforth', 'East End', 'Toronto' ],
          cmty: 'The Danforth',
          zip: 'M4C 4X5',
          subCity: 'East End',
          city: 'Toronto',
          prov: 'Ontario',
          cnty: 'Canada'
        }
      },
      {
        geoResult:{
          id: 'address.8126723024477018',
          type: 'Feature',
          place_type: ['address'],
          relevance: 1,
          properties: {
            accuracy: 'point',
            mapbox_id:'dXJuOm1ieGFkcjo3NTZkMzdmNC05NjQzLTQ0ZmUtOTZmMi03MGI1OWU3ZWVlMjY',
          },
          text_en: 'Bloor Street West',
          place_name_en: '248 Bloor Street West, Toronto, Ontario M5S 1V4, Canada',
          text: 'Bloor Street West',
          place_name: '248 Bloor Street West, Toronto, Ontario M5S 1V4, Canada',
          center: [-79.3981, 43.668217],
          geometry: { type: 'Point', coordinates: [-79.3981, 43.668217] },
          address: '248',
          context: [
            {
              id: 'neighborhood.23948327',
              mapbox_id: 'dXJuOm1ieHBsYzpBVzFzSnc',
              wikidata: 'Q3048712',
              text_en: 'The Annex',
              language_en: 'en',
              text: 'The Annex',
              language: 'en',
            },
            { id: 'postcode.8126723024477018', text_en: 'M5S 1V4', text: 'M5S 1V4' },
            {
              id: 'locality.63089191',
              mapbox_id: 'dXJuOm1ieHBsYzpBOEtxSnc',
              wikidata: 'Q6843067',
              text_en: 'Midtown',
              language_en: 'en',
              text: 'Midtown',
              language: 'en',
            },
            {
              id: 'place.80693287',
              mapbox_id: 'dXJuOm1ieHBsYzpCTTlJSnc',
              wikidata: 'Q172',
              text_en: 'Toronto',
              language_en: 'en',
              text: 'Toronto',
              language: 'en',
            },
            {
              id: 'region.17447',
              mapbox_id: 'dXJuOm1ieHBsYzpSQ2M',
              wikidata: 'Q1904',
              short_code: 'CA-ON',
              text_en: 'Ontario',
              language_en: 'en',
              text: 'Ontario',
              language: 'en',
            },
            {
              id: 'country.8743',
              mapbox_id: 'dXJuOm1ieHBsYzpJaWM',
              wikidata: 'Q16',
              short_code: 'ca',
              text_en: 'Canada',
              language_en: 'en',
              text: 'Canada',
              language: 'en',
            },
          ],
        },
        expected:{
          q: 100,
          lat: 43.668217,
          lng: -79.3981,
          faddr: '248 Bloor Street West, Toronto, Ontario M5S 1V4, Canada',
          geoAddr: '248 Bloor Street West',
          st_num: '248',
          st: 'Bloor Street West',
          locality: [ 'The Annex', 'Midtown', 'Toronto' ],
          cmty: 'The Annex',
          zip: 'M5S 1V4',
          subCity: 'Midtown',
          city: 'Toronto',
          prov: 'Ontario',
          cnty: 'Canada'
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of mapbox', (done)->
        ret = {}
        libGeoCodeHelper.parseMapboxResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()

  describe 'parseAzureResult ', ->
    tests = [
      {
        geoResult:{
          "type": "Point Address",
          "id": "feKl4IHwdp-QSgiWk0NWcw",
          "score": 1,
          "matchConfidence": {
            "score": 1
          },
          "address": {
            "streetNumber": "114",
            "streetName": "Ossington Avenue",
            "municipality": "Toronto",
            "countrySubdivision": "ON",
            "countrySubdivisionName": "Ontario",
            "countrySubdivisionCode": "ON",
            "postalCode": "M6J",
            "extendedPostalCode": "M6J 2Z4",
            "countryCode": "CA",
            "country": "Canada",
            "countryCodeISO3": "CAN",
            "freeformAddress": "114 Ossington Avenue, Toronto ON M6J 2Z4",
            "localName": "Toronto"
          },
          "position": {
            "lat": 43.6467616,
            "lon": -79.4199928
          },
          "viewport": {
            "topLeftPoint": {
              "lat": 43.64766,
              "lon": -79.42124
            },
            "btmRightPoint": {
              "lat": 43.64586,
              "lon": -79.41875
            }
          },
          "entryPoints": [
            {
              "type": "main",
              "position": {
                "lat": 43.64679,
                "lon": -79.41979
              }
            }
          ]
        },
        expected:{
          lat: 43.6467616,
          lng: -79.4199928,
          q: 100,
          faddr: "114 Ossington Avenue, Toronto ON M6J 2Z4",
          geoAddr: "114 Ossington Avenue",
          st_num: "114",
          st: "Ossington Avenue",
          zip: "M6J 2Z4",
          city: "Toronto",
          prov: "ON",
          cnty: "CA",
          nelat: 43.64766,
          nelng: -79.41875,
          swlat: 43.64586,
          swlng: -79.42124,
          locality: ["Toronto"]
        }
      },
      {
        geoResult:{
          "type": "Point Address",
          "id": "qr823MyJiG0JudETStkApQ",
          "score": 1,
          "matchConfidence": {
            "score": 1
          },
          "address": {
            "streetNumber": "286",
            "streetName": "Main Street",
            "municipality": "Toronto",
            "countrySubdivision": "ON",
            "countrySubdivisionName": "Ontario",
            "countrySubdivisionCode": "ON",
            "postalCode": "M4C",
            "extendedPostalCode": "M4C 0B3",
            "countryCode": "CA",
            "country": "Canada",
            "countryCodeISO3": "CAN",
            "freeformAddress": "286 Main Street, Toronto ON M4C 0B3",
            "localName": "Toronto"
          },
          "position": {
            "lat": 43.6874805,
            "lon": -79.3018637
          },
          "viewport": {
            "topLeftPoint": {
              "lat": 43.68838,
              "lon": -79.30311
            },
            "btmRightPoint": {
              "lat": 43.68658,
              "lon": -79.30062
            }
          },
          "entryPoints": [
            {
              "type": "main",
              "position": {
                "lat": 43.68752,
                "lon": -79.30171
              }
            }
          ]
        },
        expected:{
          lat: 43.6874805,
          lng: -79.3018637,
          q: 100,
          faddr: "286 Main Street, Toronto ON M4C 0B3",
          geoAddr: "286 Main Street",
          st_num: "286",
          st: "Main Street",
          zip: "M4C 0B3",
          city: "Toronto",
          prov: "ON",
          cnty: "CA",
          nelat: 43.68838,
          nelng: -79.30062,
          swlat: 43.68658,
          swlng: -79.30311,
          locality: ["Toronto"]
        }
      },
      {
        geoResult:{
          "address": {
            "buildingNumber": "114",
            "streetNumber": "114",
            "routeNumbers": [],
            "street": "Ossington Avenue",
            "streetName": "Ossington Avenue",
            "streetNameAndNumber": "114 Ossington Avenue",
            "countryCode": "CA",
            "countrySubdivision": "ON",
            "municipality": "Toronto",
            "postalCode": "M6J",
            "country": "Canada",
            "countryCodeISO3": "CAN",
            "freeformAddress": "114 Ossington Avenue, Toronto ON M6J 2Z4",
            "boundingBox": {
              "northEast": "43.646926,-79.419702",
              "southWest": "43.646510,-79.419827",
              "entity": "position"
            },
            "extendedPostalCode": "M6J 2Z4",
            "countrySubdivisionName": "Ontario",
            "countrySubdivisionCode": "ON",
            "localName": "Toronto"
          },
          "position": "43.646793,-79.419785",
          "id": "feKl4IHwdp-QSgiWk0NWcw"
        },
        expected:{
          "city": "Toronto"
          "cnty": "CA"
          "faddr": "114 Ossington Avenue, Toronto ON M6J 2Z4"
          "geoAddr": "114 Ossington Avenue"
          "lat": 43.646793
          "lng": -79.419785
          "locality": [
            "Toronto"
          ]
          "q": 60,
          "nelat": 43.646926
          "nelng": -79.419702
          "prov": "ON"
          "st": "Ossington Avenue"
          "st_num": "114"
          "swlat": 43.64651
          "swlng": -79.419827
          "zip": "M6J 2Z4"
        }
      }
    ]
    tests.forEach (test)->
      it 'should parse geocode result of azure', (done)->
        ret = {}
        libGeoCodeHelper.parseAzureResult test.geoResult,ret
        should.deepEqual(test.expected,ret)
        done()