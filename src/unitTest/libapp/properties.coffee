# ./test.sh -f libapp/properties.js

common = require('../../built/libapp/common')
should = require('should')
libProperties = require('../../built/libapp/properties')
helpers = require('../00_common/helpers')

debug = null


describe 'Properties Helper function tests',->
  before (done) ->
    @timeout(300000)
    done()
  
  describe 'getOfferDateFromBm',->
    tests = [
      {
        input: {
          bm:null
        }
        ret: null
      },
      {
        input: {
          bm: '**Hot Water Tank Is Rental. All Offers To Be Reviewed On '
        }
        ret: 0
      },
      {
        input: {
          bm: '**Hot Water Tank Is Rental. All Offers To Be Reviewed On '
          onD: '20220101'
        }
        ret: 0
      },
      {
        input: {
          onD: 20231211,
          bm: '**Hot Water Tank Is Rental. All Offers To Be Reviewed On December 19, 2021 @ 2:00P.M. Please Register By 1:00P.M'
        },
        ret: 20231219
      },
      {
        input: {
          onD: 20201211,
          bm: '**Hot Water Tank Is Rental. All Offers To Be Reviewed On 2:00P.M. 19th December,2021 Please Register By 1:00P.M'
        },
        ret: 20201219
      },
      {
        input: {
          'onD' : 20211210,
          bm:'Legal Cont\'d: Thorold* Hot Water Tank Is A Rental. Offer Presentation On Thurs, Dec 16th At 3 Pm, Register By 2Pm. '
        }
        ret: 20211216
      },
      {
        input: {
          'onD' : 20221110,
          bm:'Legal Cont\'d: Thorold* Hot Water Tank Is A Rental. Offer Presentation On Thurs, Dec 16th At 3 Pm, Register By 2Pm. '
        }
        ret: 0
      },
      {
        input: {
          'onD' : 20211230,
          bm:'Legal Cont\'d: Thorold* Hot Water Tank Is A Rental. Offer Presentation On Thurs, Jan 16th At 3 Pm, Register By 2Pm. '
        }
        ret: 20220116
      },
      {
        input: {
          'onD' : 20211230,
          bm:'Legal Cont\'d: Thorold* Hot Water Tank Is A Rental. Offer Presentation On Thurs, Dec 16th At 3 Pm, Register By 2Pm. '
        }
        ret: 0
      },

    ]

    tests.forEach (test)->
      it "offerD of #{test.input.bm} should parsed to #{test.ret}", (done)->
        offerD = libProperties.getOfferDateFromBm test.input
        should.equal offerD, test.ret
        done()
  describe 'genRmPsn',->
    tests = [
        {
          input: {psn:"9/8/2017", onD: 20170307}
          ret: { rmPsn: [], rmPsnDate: undefined }
        },
        {
          input: {psn:"30-60", onD: 20060308}
          ret: { rmPsn: [], rmPsnDate: undefined }
        },
        {
          input: {psn:"Immediate/Tba", onD: 20060309}
          ret: { rmPsn: [ 'tba', 'imm' ], rmPsnDate: undefined }
        },
        {
          input: {psn:"Aug10", onD: 20060309}
          ret: { rmPsn: [], rmPsnDate:20060810 }
        },
        {
          input: {psn:"Aug 10", onD: 20070309}
          ret: { rmPsn: [], rmPsnDate:20070810 }
        },
        {
          input: {psn:"September 2016", onD: 20160309}
          ret: { rmPsn: [], rmPsnDate: 20160901  }
        },
        {
          input: {psn:"29th June", onD: 20160309}
          ret: { rmPsn: [], rmPsnDate: 20160601 }
        },
        {
          input: {psn:"June 29th", onD: 20160309}
          ret: { rmPsn: [], rmPsnDate: 20160629 }
        },
        {
          input: {psn:"June 1st/2016", onD: 20160309}
          ret:{ rmPsn: [], rmPsnDate: 20160601 }
        },
        {
          input: {psn:"5/22/2016", onD: 20160309}
          ret: { rmPsn: [], rmPsnDate: undefined }
        },
        {
          input: {psn:"30Days/Tba", onD: 20160309}
          ret: { rmPsn: [ 'tba' ], rmPsnDate: undefined }
        }
    ]
    tests.forEach (test)->
      it "genRmPsn of #{test.input.psn} should parsed to #{test.ret.rmPsn}", (done)->
        ret = libProperties.genRmPsn test.input
        console.log ret
        should.deepEqual ret, test.ret
        done()
  describe 'filter merged props by permission',->
    tests = [
      {
        desc:'should remove merged properties'
        props:{
          cnt:7,
          list:[
            {_id:'TRBC5991076',src:'TRB',sid:'C5991076'},
            {_id:'DDF25585654',merged:'TRBE5990972',src:'DDF',sid:'E5990972'},
            {_id:'TRBE5990972',src:'TRB',sid:'E5990972'},
            {_id:'TRBE5990964',src:'TRB',sid:'E5990964'},
            {_id:'DDF25575252',merged:'DDF25581196',src:'DDF',sid:'10273770'},
            {_id:'DDF25585653',merged:'TRBE5990964',src:'DDF',sid:'E5990964'},
            {_id:'TRBX6733170',merged:'RHBH4171250',src:'TRB',sid:'X6733170'},
          ]
        }
        isPropAdmin:false
        ret:{
          cnt:4,
          list:[
            {_id:'TRBC5991076',src:'TRB',sid:'C5991076'},
            {_id:'TRBE5990972',src:'TRB',sid:'E5990972'},
            {_id:'TRBE5990964',src:'TRB',sid:'E5990964'},
            {_id:'TRBX6733170',merged:'RHBH4171250',src:'TRB',sid:'X6733170'}
          ]
        }
      },
      {
        desc:'should put merged properties at the end'
        props:{
          cnt:7,
          list:[
            {_id:'TRBC5991076',src:'TRB',sid:'C5991076'},
            {_id:'DDF25585654',merged:'TRBE5990972',src:'DDF',sid:'E5990972'},
            {_id:'TRBE5990972',src:'TRB',sid:'E5990972'},
            {_id:'TRBE5990964',src:'TRB',sid:'E5990964'},
            {_id:'DDF25575252',merged:'DDF25581196',src:'DDF',sid:'10273770'},
            {_id:'DDF25585653',merged:'TRBE5990964',src:'DDF',sid:'E5990964'},
            {_id:'TRBX6733170',merged:'RHBH4171250',src:'TRB',sid:'X6733170'}
          ]
        }
        isPropAdmin:true
        ret:{
          cnt:7,
          list:[
            {_id:'TRBC5991076',src:'TRB',sid:'C5991076'},
            {_id:'TRBE5990972',src:'TRB',sid:'E5990972'},
            {_id:'TRBE5990964',src:'TRB',sid:'E5990964'},
            {_id:'DDF25585654',merged:'TRBE5990972',src:'DDF',sid:'E5990972'},
            {_id:'DDF25575252',merged:'DDF25581196',src:'DDF',sid:'10273770'},
            {_id:'DDF25585653',merged:'TRBE5990964',src:'DDF',sid:'E5990964'},
            {_id:'TRBX6733170',merged:'RHBH4171250',src:'TRB',sid:'X6733170'}
          ]
        }
      },
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        libProperties.filterPropsByPermission test.props, test.isPropAdmin
        should.deepEqual test.props, test.ret
        done()

  describe 'propPassSearchCondition', ->
    it 'should return false for top listing with status U', (done)->
      prop = { status: 'U' }
      conditions = {}
      result = libProperties.propPassSearchCondition prop, conditions
      result.should.be.exactly(false)
      done()

    it 'should return true for property passing all conditions', (done)->
      prop = {
        "_id": "TRB********",
        "_mt": new Date(Date.now() - 24 * 3600000),
        "ac": "Central Air",
        "addr": "3950 Erin Centre Blvd",
        "amen": [
          "Bbqs Allowed",
          "Car Wash",
          "Visitor Parking"
        ],
        "apt_num": "36",
        "area_code": "05",
        "asmt": 469000,
        "ass_year": 2023,
        "bdrms": 3,
        "blcny": "None",
        "bndCity": {
          "_id": "CA:ON:MISSISSAUGA",
          "nm": "Mississauga",
          "tp": "CY",
          "sz": 1
        },
        "bndCmty": {
          "_id": "CA:ON:MISSISSAUGA:CHURCHILL MEADOWS",
          "nm": "Churchill Meadows"
        },
        "bndProv": {
          "_id": "CA:ON",
          "nm": "ON"
        },
        "bndRegion": {
          "_id": "CA:ON:REGION PEEL",
          "nm": "Peel"
        },
        "bnds": [
          1020824,
          1020873,
          1020963,
          1020825,
          1020872,
          1020888,
          1025688,
          1020965,
          1020577,
          1020616,
          1029947
        ],
        "bsmt": [
          "Fin",
          "W/O"
        ],
        "bsmt1_out": "Fin W/O",
        "bthrms": 4,
        "bths": [
          {
            "t": 1,
            "l": "Main",
            "p": 2
          },
          {
            "t": 2,
            "l": "2nd",
            "p": 4
          },
          {
            "t": 1,
            "l": "Bsmt",
            "p": 4
          }
        ],
        "cases": [
          "org"
        ],
        "city": "Mississauga",
        "city_d": "Mississauga",
        "citycd": "05.03",
        "clsNm": "CondoProperty",
        "cmty": "Churchill Meadows",
        "cmtycd": "05.03.0020",
        "cnty": "CA",
        "comel_inc": "Y",
        "comm": "2.5%",
        "condo_corp": "PSCC",
        "constr": [
          "Brick"
        ],
        "corp_num": 783,
        "crsst": "Ninth Line / Erin Centre Blvd",
        "cs16": "355350516.49",
        "cs21": "352021S051235212062",
        "cur": "CND",
        "daddr": "Y",
        "den_fr": "N",
        "exp": 20240911,
        "faddr": "3950 Erin Centre Blvd,Mississauga, ON L5M0A5, CA",
        "fce": "N",
        "feat": [
          "Fenced Yard",
          "Library",
          "Park",
          "School"
        ],
        "fpl": "N",
        "fuel": "Gas",
        "gatp": "Built-In",
        "geoq": 120,
        "gr": 1,
        "heat": "Forced Air",
        "his": [
          {
            "s": "New",
            "lp": 799000,
            "ts": new Date('2024-06-12'),
            "d": 20240612
          },
          {
            "s": "addPic",
            "ts": new Date('2024-06-12'),
            "d": 20240612
          }
        ],
        "id": "TRB********",
        "input_date": 20240612,
        "insur_bldg": "Y",
        "internet": "Y",
        "kch": 1,
        "lastupdated": new Date(Date.now() - 24 * 3600000),
        "lat": 43.54021779466637,
        "lcTp": "New",
        "ld": 20240612,
        "lkr": "None",
        "lng": -79.73865729251405,
        "loc": {
          "type": "Point",
          "coordinates": [
            -79.73865729251405,
            43.54021779466637
          ]
        },
        "lp": 799000,
        "lsrc": "evow",
        "lst": "New",
        "ltp": "MLS",
        "lup": new Date(Date.now() - 24 * 3600000),
        "lvl": 1,
        "m": "Luxurious Townhouse In Daniel's Woodland Ridge In Sought-After Churchill Meadows Location; 1912 Sqft Of Finished Living Area Builder-Finished W/O Basement With 4-Pc Bath) Gleaming Hardwood Floors On Main Level; Oak Stairs; Premium Laminate Floors On Upper Level. Upgraded washrooms, kitchen with new fridge and stove, Pot light on main floor, Very Convenient West Mississauga Location With Public Transit, Plaza's, STEPS New Churchill Meadows Community Centre And Sports Park. S/S Fridge, S/S Stove, B/I Dishwasher, Washer & Dryer, All Existing Window Coverings, All Light Fixtures, Central Air, Central Vacuum",
        "metaInfo": {},
        "mfee": 324.91,
        "mt": new Date(Date.now() - 24 * 3600000),
        "num_kit": 1,
        "olp": 799000,
        "onD": 20240612,
        "origBsmt": [
          "Fin W/O"
        ],
        "origId": [
          "DDF27031059",
          "TRB********"
        ],
        "origProv": "Ontario",
        "origUnt": "36",
        "parcel_id": 197830029,
        "park_desig": "Owned",
        "park_fac": "Private",
        "park_spcs": 1,
        "pets": "Restrict",
        "phomt": new Date(Date.now() - 24 * 3600000),
        "poss_date": 20240711,
        "prkg_inc": "Y",
        "prop_mgmt": "GSA Property Management",
        "prov": "ON",
        "psn": "Flexible",
        "pstyl": "2-Storey",
        "ptp": "Condo Townhouse",
        "ptype": "r",
        "ptype2": [
          "Townhouse",
          "Attached"
        ],
        "region": "Peel",
        "rltr": "ROYAL LEPAGE REAL ESTATE SERVICES LTD.",
        "rmBltYr": 2005,
        "rmPsn": [
          "flex"
        ],
        "rmSqft": 1560,
        "rms": [
          {
            "t": "Living",
            "l": "Main",
            "w": 2.87,
            "h": 7.01,
            "d": [
              "Hardwood Floor",
              "Combined W/Dining",
              "Pot Lights"
            ]
          },
          {
            "t": "Dining",
            "l": "Main",
            "w": 2.87,
            "h": 7.01,
            "d": [
              "Hardwood Floor",
              "Combined W/Living"
            ]
          },
          {
            "t": "Kitchen",
            "l": "Main",
            "w": 2.18,
            "h": 4.28,
            "d": [
              "Ceramic Floor",
              "Ceramic Back Splash"
            ]
          },
          {
            "t": "Breakfast",
            "l": "Main",
            "w": 2.18,
            "h": 2.44,
            "d": [
              "Ceramic Floor",
              "O/Looks Backyard"
            ]
          },
          {
            "t": "Prim Bdrm",
            "l": "2nd",
            "w": 3.73,
            "h": 5.03,
            "d": [
              "Laminate",
              "4 Pc Ensuite",
              "Large Closet"
            ]
          },
          {
            "t": "2nd Br",
            "l": "2nd",
            "w": 2.88,
            "h": 6.49,
            "d": [
              "Laminate",
              "Closet"
            ]
          },
          {
            "t": "3rd Br",
            "l": "2nd",
            "w": 3.05,
            "h": 3.73,
            "d": [
              "Laminate",
              "Closet"
            ]
          },
          {
            "t": "Rec",
            "l": "Bsmt",
            "w": 3.38,
            "h": 5.13,
            "d": [
              "Broadloom",
              "4 Pc Bath",
              "W/O To Yard"
            ]
          }
        ],
        "rooms_plus": 1,
        "saletp": [
          "Sale"
        ],
        "saveResult": true,
        "schools": [
          {
            "_id": 1020405,
            "nm": "St. Bernard Of Clairvaux Catholic Elementary School",
            "ele": 1,
            "mid": 1,
            "hgh": 0,
            "gt": 8,
            "gf": -1,
            "c": 1,
            "fir": 8.1,
            "g6r": 98,
            "g3r": 80,
            "dist": 963
          },
          {
            "_id": 1020418,
            "nm": "St. Francis Xavier Secondary School",
            "ele": 0,
            "mid": 0,
            "hgh": 1,
            "gt": 12,
            "gf": 9,
            "c": 1,
            "fir": 8.1,
            "g9r": 48,
            "dist": 10017
          },
          {
            "_id": 1020426,
            "nm": "St. Joan Of Arc Catholic Secondary School",
            "ele": 0,
            "mid": 0,
            "hgh": 1,
            "gt": 12,
            "gf": 9,
            "c": 1,
            "fir": 7,
            "g9r": 153,
            "dist": 1497
          },
          {
            "_id": 1020570,
            "nm": "Artesian Drive Public School",
            "ele": 1,
            "mid": 0,
            "hgh": 0,
            "gt": 5,
            "gf": -1,
            "c": 0,
            "fir": null,
            "g3r": 753,
            "dist": 1615
          },
          {
            "_id": 1020603,
            "nm": "Erin Centre Middle School",
            "ele": 0,
            "mid": 1,
            "hgh": 0,
            "gt": 8,
            "gf": 6,
            "c": 0,
            "fir": null,
            "g6r": 367,
            "dist": 1140
          },
          {
            "_id": 1020670,
            "nm": "Stephen Lewis",
            "ele": 0,
            "mid": 0,
            "hgh": 1,
            "gt": 12,
            "gf": 9,
            "c": 0,
            "fir": 6.6,
            "g9r": 198,
            "dist": 1905
          }
        ],
        "schs": [
          1020405,
          1020418,
          1020426,
          1020570,
          1020603,
          1020616,
          1020633,
          1020670,
          1020671
        ],
        "showAddr": "3950 Erin Centre Blvd",
        "sid": "********",
        "spcts": new Date(Date.now() - 24 * 3600000),
        "spec": [
          "Unknown"
        ],
        "sptp": "T",
        "sqft": "1400-1599",
        "sqft1": 1400,
        "sqft2": 1599,
        "sqftQ": 2,
        "sqftSrc": "unit",
        "src": "TRB",
        "sstp": "S",
        "st": "Erin Centre",
        "st_num": "3950",
        "st_sfx": "Blvd",
        "status": "A",
        "synced": new Date(Date.now() - 24 * 3600000),
        "tax": 3888,
        "taxyr": 2023,
        "tbdrms": 3,
        "tgr": 2,
        "trbtp": [
          "evow"
        ],
        "trms": 7,
        "ts": new Date(Date.now() - 24 * 3600000),
        "type_own_srch": ".T.",
        "uaddr": "CA:ON:MISSISSAUGA:3950 ERIN CENTRE BLVD",
        "unit_num": 29,
        "unt": "36",
        "useCache": true,
        "vend_pis": "N",
        "vturl": "https://www.winsold.com/tour/351972",
        "zip": "L5M0A5",
        "avgpicsz": 500712.2,
        "maxpicsz": 703677,
        "pho": 40,
        "phodl": new Date(Date.now() - 24 * 3600000),
        "phosrc": "trb",
        "phots": new Date(Date.now() - 24 * 3600000),
        "picTrb": {
          "picNum": 40,
          "maxPicSz": 703677,
          "avgPicSz": 500712.2
        },
        "vc": 20,
        "vcapp": 15,
        "vcc": 19,
        "vcd": 20,
        "vcr": 1,
        "shr": 1,
        "shrappr": 1,
        "AmmenitiesNearBy": [
          "Park",
          "Schools"
        ],
        "Board": 82,
        "Building": {
          "BathroomTotal": 4,
          "BedroomsTotal": 3,
          "BedroomsAboveGround": 3,
          "Amenities": [
            "Car Wash",
            "Visitor Parking"
          ],
          "Appliances": [
            "Dishwasher",
            "Dryer",
            "Refrigerator",
            "Stove",
            "Washer",
            "Window Coverings"
          ],
          "BasementDevelopment": "Finished",
          "BasementFeatures": "Walk out",
          "BasementType": "N/A (Finished)",
          "CoolingType": "Central air conditioning",
          "ExteriorFinish": "Brick",
          "FireplacePresent": "False",
          "HeatingFuel": "Natural gas",
          "HeatingType": "Forced air",
          "SizeInterior": "",
          "StoriesTotal": "2",
          "TotalFinishedArea": "",
          "Type": "Row / Townhouse"
        },
        "BuildingType": "Row / Townhouse",
        "Business": {
          "Franchise": ""
        },
        "CommunityFeatures": "Pet Restrictions",
        "Features": "In suite Laundry",
        "Land": {
          "Acreage": "false",
          "Amenities": [
            "Park",
            "Schools"
          ]
        },
        "ListingContractDate": 20240612,
        "OwnershipType": "Condominium/Strata",
        "ParkingSpaceTotal": 2,
        "PropertyType": "Single Family",
        "TransactionType": "For sale",
        "ddfID": "DDF27031059",
        "la2": {
          "agnt": [
            {
              "id": "1993696",
              "_id": "DDF1993696",
              "nm": "MAHMOUD ABDELMEGID",
              "pstn": "Salesperson",
              "tel": "(*************",
              "office": {
                "id": "51449",
                "_id": "DDF51449",
                "nm": "ROYAL LEPAGE REAL ESTATE SERVICES LTD.",
                "city": "Mississauga",
                "prov": "ON",
                "addr": "2520 EGLINTON AVE WEST #207B",
                "zip": "L5M0Y4",
                "tel": "(*************",
                "fax": "(*************"
              }
            }
          ],
          "id": "51449",
          "_id": "DDF51449",
          "nm": "ROYAL LEPAGE REAL ESTATE SERVICES LTD.",
          "city": "Mississauga",
          "prov": "ON",
          "addr": "2520 EGLINTON AVE WEST #207B",
          "zip": "L5M0Y4",
          "tel": "(*************",
          "fax": "(*************"
        },
        "link": "https://www.realtor.ca/real-estate/27031059/36-3950-erin-centre-boulevard-mississauga-churchill-meadows",
        "mfeeunt": "Monthly",
        "ohz": null,
        "origSrc": [
          "DDF",
          "TRB"
        ],
        "picDdf": {
          "picNum": 40,
          "maxPicSz": 369841,
          "avgPicSz": 139462.025
        }
      }
      conditions = {
        "label" : 1,
        "page" : 0,
        "bbox" : [
            -79.78098750114441,
            43.44825024456908,
            -79.6078971773386,
            43.672415842975326
        ],
        "src" : "mls",
        "saletp" : "sale",
        "saleDesc" : "Sale",
        "ptype" : "Residential",
        "ptype2" : [
            "Semi-Detached",
            "Townhouse"
        ],
        "bdrms" : "3+",
        "bthrms" : "2+",
        "gr" : "1+",
        "min_lp" : 750000,
        "max_lp" : 1100000,
        "no_mfee" : false,
        "sort" : "auto-ts",
        "oh" : false,
        "sq_f" : 1400,
        "soldOnly" : true,
        "bnds" : [
            43.50015976003824,
            -79.67982865869999,
            43.55103084257192,
            -79.76449057459831,
            43.62661705355204,
            -79.67837389558554,
            43.59992274142224,
            -79.64450664818287,
            43.57128447841606,
            -79.62439443916081,
            43.55265354173502,
            -79.64732330292463,
            43.526802388889514,
            -79.6479154232958,
            43.50015976003824,
            -79.67982865869999
        ],
        "ts" : new Date(),
        "hasWechat" : true,
        "rmProp" : true,
        "locale" : "zh-cn",
        "notApp" : false,
        "isCip" : false,
        "isPad" : false,
        "isPropAdmin" : false,
        "isVipRealtor" : true,
        "isAssignAdmin" : false,
        "isAdmin" : false,
        "isVipUser" : true,
        "isUserAdmin" : false,
        "isRealtor" : true,
        "isRealGroup" : "62a3561b39039a20c4454107",
        "status" : "A"
      }
      result = libProperties.propPassSearchCondition(prop, conditions, false)
      result.should.be.exactly(true)
      done()

  describe 'isSimilarAddr',->
    tests = [
      {
        desc:'should return true when spell wrong but similar gte 90%'
        srcAddr:'Elizabeth St'
        compareAddr:'Elizabeths St'
        expected:true
      },
      {
        desc:'should return true when missing a part '
        srcAddr:'Spring Garden Ave'
        compareAddr:'Spring Ave'
        expected:true
      },
      {
        desc:'should return true when missing direction'
        srcAddr:'Elizabeth St W'
        compareAddr:'Elizabeth St'
        expected:true
      },
      {
        desc:'should return false when different 3 parts'
        srcAddr:'Bowood Ave'
        compareAddr:'Boxwood Ave W'
        expected:false
      },
      {
        desc:'should return false when no srcAddr'
        compareAddr:'Boxwood Rd'
        expected:false
      },
      {
        desc:'should return false when no compareAddr'
        srcAddr:'Boxwood Rd'
        expected:false
      }
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        ret = libProperties.isSimilarAddr test.srcAddr,test.compareAddr
        ret.should.be.exactly(test.expected)
        done()
  
  describe 'getSaleTpTagAndTagColorAndLstStr', ->
    tests = [
      {
        desc: '应该返回空对象当传入参数为空时'
        input: null
        expected: {}
      },
      {
        desc: '应该返回空对象当传入参数不是object类型'
        input: []
        expected: {}
      },
      {
        desc: '应该返回空对象当传入参数是空object'
        input: {}
        expected: {}
      },
      {
        desc: '活跃状态的销售房源返回正确的标签和颜色'
        input: {
          status: 'A'
          saletp: ['Sale']
          MlsStatus: 'Active'
          src: 'TRB'
        }
        expected: {
          saleTpTag: 'Sale'
          tagColor: 'green'
        }
      },
      {
        desc: '活跃状态的租赁房源返回正确的标签和颜色'
        input: {
          status: 'A'
          saletp: ['Lease']
          MlsStatus: 'Active'
          src: 'TRB'
        }
        expected: {
          saleTpTag: 'Lease'
          tagColor: 'green'
        }
      },
      {
        desc: '应该使用 MlsStatus 映射中的 saleTpTag 当状态不是活跃时'
        input: {
          status: 'U'
          saletp: ['Sale']
          MlsStatus: 'Sold'
          src: 'TRB'
          lst: 'Sld'
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
        }
      },
      {
        desc: '应该包含 lstStr Expired当映射中有 lstStr 字段时'
        input: {
          status: 'U'
          saletp: ['Sale']
          MlsStatus: 'Expired'
          src: 'TRB'
          lst: 'Exp'
        }
        expected: {
          saleTpTag: 'Delisted'
          lstStr: 'Expired'
        }
      },
      {
        desc: 'mapping不存在,lst不在包含范围内'
        input: {
          status: 'U'
          saletp: ['Sale']
          src: 'TRB'
          lst: 'Sld'
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
        }
      },
      {
        desc: '应该根据 lst 状态判断已售出房源当没有映射时'
        input: {
          status: 'U'
          saletp: ['Sale']
          lst: 'Sld'
          sp: 500000
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
        }
      },
      {
        desc: '应该根据 lst 状态判断已租出房源当没有映射时'
        input: {
          status: 'U'
          saletp: ['Lease']
          lst: 'Lsd'
        }
        expected: {
          saleTpTag: 'Leased'
          tagColor: 'red'
        }
      },
      {
        desc: '应该根据 lst Pnd状态判断已租出房源当没有映射时'
        input: {
          status: 'U'
          saletp: ['Sale']
          lst: 'Pnd'
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
        }
      },
      {
        desc: '应该返回 Delisted 标签当房源下架且没有映射时'
        input: {
          status: 'U'
          saletp: ['Sale']
          lst: 'Exp'
        }
        expected: {
          saleTpTag: 'Delisted'
          lstStr: 'Expired'
        }
      },
      {
        desc: '应该正确处理 CREB 的 Pending 状态'
        input: {
          status: 'U'
          saletp: ['Sale']
          MlsStatus: 'Pending'
          src: 'CLG'
          lst: 'Sus'
        }
        expected: {
          saleTpTag: 'Delisted'
          lstStr: 'Pending'
        }
      },
      {
        desc: '应该正确处理 EDM 的 Pending 状态'
        input: {
          status: 'U'
          saletp: ['Sale']
          MlsStatus: 'Pending'
          src: 'EDM'
          lst: 'Sus'
        }
        expected: {
          saleTpTag: 'Delisted'
          lstStr: 'Pending'
        }
      },
      {
        desc: '应该正确处理 status_en 和 saletp_en 字段'
        input: {
          status_en: 'A'
          saletp_en: 'Sale'
          MlsStatus: 'Active'
          src: 'TRB'
        }
        expected: {
          saleTpTag: 'Sale'
          tagColor: 'green'
        }
      },
      {
        desc: '应该正确处理数组形式的 saletp'
        input: {
          status: 'A'
          saletp: ['Sale', 'Lease']
          MlsStatus: 'Active'
          src: 'TRB'
        }
        expected: {
          saleTpTag: 'Sale'
          tagColor: 'green'
        }
      },
      {
        desc: '应该正确处理 sp 字段判断已售出状态'
        input: {
          status: 'U'
          saletp: ['Sale']
          sp: 500000
          lst: 'Oth' # 非标准状态
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
          lstStr: 'Oth'
        }
      },
      {
        desc: 'sale, not lst, not Mlsstatus'
        input: {
          status: 'A'
          saletp: ['Sale']
        }
        expected: {
          saleTpTag: 'Sale'
          tagColor: 'green'
        }
      },
      {
        desc: 'Sold, not lst, not Mlsstatus,have sp'
        input: {
          status: 'U'
          saletp: ['Sale']
          sp: 500000
        }
        expected: {
          saleTpTag: 'Sold'
          tagColor: 'red'
        }
      },
      {
        desc: 'Sold, not lst, not Mlsstatus, not sp'
        input: {
          status: 'U'
          saletp: ['Sale']
        }
        expected: {
          saleTpTag: 'Delisted'
        }
      }
    ]
    tests.forEach (test) ->
      it test.desc, (done) ->
        result = libProperties.getSaleTpTagAndTagColorAndLstStr(test.input)
        should.deepEqual(result, test.expected)
        done()

  describe 'isPropObjectId', ->
    tests = [
      {
        desc: '当输入为空时应返回 false'
        input: null
        expected: false
      },
      {
        desc: '当输入为空字符串时应返回 false'
        input: ''
        expected: false
      },
      {
        desc: '当输入为 undefined 时应返回 false'
        input: undefined
        expected: false
      },
      {
        desc: '当输入为 MLS 前缀时应返回 false'
        input: 'TRB********'
        expected: false
      },
      {
        desc: '当输入为小写 MLS 前缀时应返回 false'
        input: 'trbw8432924'
        expected: false
      },
      {
        desc: '当输入为有效的 ObjectID 时应返回 true'
        input: '507f1f77bcf86cd799439011'
        expected: true
      },
      {
        desc: '当输入为无效的 ObjectID 格式时应返回 false'
        input: '507f1f77bcf86cd79943901'  # 少一位
        expected: false
      },
      {
        desc: '当输入包含非法字符时应返回 false'
        input: '507f1f77bcf86cd79943901g'  # 包含 g
        expected: false
      },
      {
        desc: '当输入为12位字符串(ObjectId.isValid会返回true)时应返回 false'
        input: '50 McCaul st'
        expected: false
        objectIdIsValid: false  # ObjectId.isValid 会返回 false in v6.x
      },
      {
        desc: '当输入为12位MLS号(ObjectId.isValid会返回true)时应返回 false'
        input: 'RHBXH4206770'
        expected: false
        objectIdIsValid: false  # ObjectId.isValid 会返回 false in v6.x
      },
      {
        desc: '当输入为12位随机字符串(ObjectId.isValid会返回true)时应返回 false'
        input: 'testMLisq pe'
        expected: false
        objectIdIsValid: false  # ObjectId.isValid 会返回 false in v6.x
      }
    ]
    
    tests.forEach (test)->
      it test.desc, (done)->
        ret = libProperties.isPropObjectId test.input
        ret.should.be.exactly(test.expected)
        
        # 如果测试用例中指定了 objectIdIsValid，则验证与 MongoDB 的 ObjectId.isValid 的行为差异
        if test.objectIdIsValid?
          # 这里假设我们可以访问 MongoDB 的 ObjectId
          helpers.ObjectId.isValid(test.input).should.be.exactly(test.objectIdIsValid)
          # 确认我们的实现与 ObjectId.isValid 在这些边缘情况下有相同的行为在v6.x中
          (ret == helpers.ObjectId.isValid(test.input)).should.be.exactly(true)
        
        done()

  describe 'getIconChar', ->
    it 'should return "S" when ptype2 contains only Semi-Detached', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Semi-Detached'] }
      result.should.equal 'S'
      done()

    it 'should return "D" when ptype2 contains only Detached', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Detached'] }
      result.should.equal 'D'
      done()

    it 'should return "C" when ptype2 contains only Condo', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Condo'] }
      result.should.equal 'C'
      done()

    it 'should return "T" when ptype2 contains only Townhouse', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Townhouse'] }
      result.should.equal 'T'
      done()

    it 'should return "B" when ptype2 contains only Office (business)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Office'] }
      result.should.equal 'B'
      done()

    it 'should return "S" when ptype2 contains both Semi-Detached and Detached (priority S)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Semi-Detached', 'Detached'] }
      result.should.equal 'S'
      done()

    it 'should return "D" when ptype2 contains both Detached and Condo (priority D)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Detached', 'Condo'] }
      result.should.equal 'D'
      done()

    it 'should return "C" when ptype2 contains both Condo and Townhouse (priority C)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Condo', 'Townhouse'] }
      result.should.equal 'C'
      done()

    it 'should return "T" when ptype2 contains both Townhouse and Office (priority T)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Townhouse', 'Office'] }
      result.should.equal 'T'
      done()

    it 'should return "S" when ptype2 contains all types (priority S)', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Semi-Detached', 'Detached', 'Condo', 'Townhouse', 'Office'] }
      result.should.equal 'S'
      done()

    it 'should return empty string when ptype2 contains no matching type', (done) ->
      result = libProperties.getIconChar { saletp:[ 'Sale' ], ptype2: ['Land', 'Parking'] }
      result.should.equal ''
      done()

    it 'should return "P" when isProj is true and tp1/tp2 are null', (done) ->
      result = libProperties.getIconChar { isProj: true, tp1: null, tp2: null }
      result.should.equal 'P'
      done()

    it 'should return "B" when isProj is true and isBusiness', (done) ->
      result = libProperties.getIconChar { isProj: true, tp1: 'Office', tp2: null, ptype2: ['Office'] }
      result.should.equal 'B'
      done()