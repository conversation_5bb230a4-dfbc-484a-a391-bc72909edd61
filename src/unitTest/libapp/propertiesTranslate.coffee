should = require('should')
helpers = require('../00_common/helpers')
propertiesTranslate = require('../../built/libapp/propertiesTranslate')

describe 'propertiesTranslate', ->
  before (done) ->
    @timeout(30000)
    request = helpers.getServer()
    done()
    return

  describe 'translate_rmprop_detail', ->
    it 'should correctly translate DDF source property data', ->
      # 输入参数
      input = {
        ddfID: 'DDF123456',
        src: 'DDF',
        id: 'DDF123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [
          { t: 'Bedroom', l: 'Master' },
          { t: 'Bathroom', l: 'Full' }
        ],
        rmPsn: 'Immediate',
        bsmt: 'Finished',
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        den: 1,
        blcny: 0,
        grdn: 1,
        gym: 0,
        pool: 1,
        fpop: 0,
        lkr: 1,
        fpl: 0,
        htb: 1,
        wifi: 0,
        tv: 1,
        wsr: 0,
        dyr: 1,
        frdg: 0,
        utl: 1,
        hydro_inc: 0,
        water_inc: 1,
        smok: 0,
        pets: 1,
        movn: 0,
        frnshd: 1,
        ptp2: ['Residential'],
        saletp: ['Sale'],
        m_zh: '这是一个测试房源',
        'FireplaceYN': 'Y',
        Building: {
          'Age': 10,
          'Appliances': 'Dishwasher, Refrigerator, Stove',
          'ArchitecturalStyle': 'Modern',
          'BuildingFeatures': 'Security System',
          'ConstructedDate': '2013',
          'ConstructionMaterials': 'Wood Frame',
          'FireplaceFeatures': 'Gas',
          'FireplacesTotal': 2,
          'Flooring': 'Hardwood',
          'FoundationDetails': 'Poured Concrete',
          'HeatingType': 'Forced Air',
          'HeatingFuel': 'Natural Gas',
          'LotFeatures': 'Fenced Yard',
          'NumberOfBuildings': 1,
          'NumberOfUnitsTotal': 1,
          'PropertyCondition': 'Good',
          'Roof': 'Asphalt Shingle',
          'Type': 'Single Family'
        },
        'PropertyAttachedYN': 'N',
        'BasementFeatures': 'Separate Entrance',
        'BasementType': 'Finished'
        Utilities: [
          { t: 'Electric', m: 'Available' },
          { t: 'OtherEquipment', m: 'Central Vacuum' },
          { t: 'UtilitiesAvailable', m: 'All' },
          { t: 'WaterBodyName', m: 'Lake' },
          { t: 'WaterfrontFeatures', m: 'Dock' },
          { t: 'WaterSource', m: 'Municipal' }
        ],
        'AmmenitiesNearBy': 'Schools, Shopping',
        'CommunityFeatures': 'Park',
        'ExteriorFeatures': 'Deck',
        'LocationDescription': 'Quiet neighborhood',
        'RoadSurfaceType': 'Paved',
        'RoadType': 'Public',
        'ViewType': 'Lake',
        'Zoning': 'Residential',
        'ZoningDescription': 'Single Family',
        'ZoningType': 'Residential',
        'AccessibilityFeatures': 'Wheelchair Access',
        'AssociationFee': 200,
        'AssociationFeeFrequency': 'Monthly',
        'AssociationFeeIncludes': 'Water, Insurance',
        'AssociationName': 'Homeowners Association'
      }
      
      # 期望的输出
      expected = {
        ddfID: 'DDF123456',
        src: 'DDF',
        id: 'DDF123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        den: 'Yes',
        blcny: 'No',
        grdn: 'Yes',
        gym: 'No',
        pool: 'Yes',
        fpop: 'No',
        lkr: 'Yes',
        fpl: 'No',
        htb: 'Yes',
        wifi: 'No',
        tv: 'Yes',
        wsr: 'No',
        dyr: 'Yes',
        frdg: 'No',
        utl: 'Yes',
        hydro_inc: 'No',
        water_inc: 'Yes',
        smok: 'No',
        pets: 'Yes',
        movn: 'No',
        frnshd: 'Yes',
        ptp2: [ 'Residential' ],
        saletp_en: [ 'Sale' ],
        saletp: [ 'Sale' ],
        m_zh: '这是一个测试房源',
        FireplaceYN: 'Y',
        PropertyAttachedYN: 'N',
        CommunityFeatures: 'Park',
        ExteriorFeatures: 'Deck',
        LocationDescription: 'Quiet neighborhood',
        RoadSurfaceType: 'Paved',
        RoadType: 'Public',
        ViewType: 'Lake',
        Zoning: 'Residential',
        ZoningDescription: 'Single Family',
        ZoningType: 'Residential',
        BasementFeatures: 'Separate Entrance',
        BasementType: 'Finished',
        AmmenitiesNearBy: 'Schools, Shopping',
        AccessibilityFeatures: 'Wheelchair Access',
        AssociationFee: 200,
        AssociationFeeFrequency: 'Monthly',
        AssociationFeeIncludes: 'Water, Insurance',
        AssociationName: 'Homeowners Association',
        stplabel: 'Sale',
        Building: {
          n: 'Building',
          v: [
            { t: 'Age', m: 10 },
            { t: 'Appliances', m: 'Dishwasher, Refrigerator, Stove' },
            { t: 'Architectural Style', m: 'Modern' },
            { t: 'Building Features', m: 'Security System' },
            { t: 'Constructed Date', m: '2013' },
            { t: 'Construction Materials', m: 'Wood Frame' },
            { t: 'Fireplace Features', m: 'Gas' },
            { t: 'Fireplaces Total', m: 2 },
            { t: 'Flooring', m: 'Hardwood' },
            { t: 'Foundation Details', m: 'Poured Concrete' },
            { t: 'Heating Type', m: 'Forced Air' },
            { t: 'Heating Fuel', m: 'Natural Gas' },
            { t: 'Lot Features', m: 'Fenced Yard' },
            { t: 'Number Of Buildings', m: 1 },
            { t: 'Number Of Units Total', m: 1 },
            { t: 'Property Condition', m: 'Good' },
            { t: 'Roof', m: 'Asphalt Shingle' },
            { t: 'Type', m: 'Single Family' },
            { t: 'Fireplace', m: 'Yes' },
            { t: 'Property Attached', m: 'No' }
          ]
        },
        Basement: {
          n: 'Basement',
          v: [
            { t: 'Basement Features', m: 'Separate Entrance' },
            { t: 'Basement Type', m: 'Finished' }
          ]
        },
        Utilities: {
          n: 'Utilities',
          v: [
            { t: 'Electric', m: 'Available' },
            { t: 'Other Equipment', m: 'Central Vacuum' },
            { t: 'Utilities Available', m: 'All' },
            { t: 'Water Body Name', m: 'Lake' },
            { t: 'Waterfront Features', m: 'Dock' },
            { t: 'Water Source', m: 'Municipal' }
          ]
        },
        Surrounding: {
          n: 'Surrounding',
          v: [
            { n: 'Ammenities Near By', v: 'Schools, Shopping' },
            { n: 'Community Features', v: 'Park' },
            { n: 'Exterior Features', v: 'Deck' },
            { n: 'Location Description', v: 'Quiet neighborhood' },
            { n: 'Road Surface Type', v: 'Paved' },
            { n: 'Road Type', v: 'Public' },
            { n: 'View Type', v: 'Lake' },
            { n: 'Zoning', v: 'Residential' },
            { n: 'Zoning Description', v: 'Single Family' },
            { n: 'Zoning Type', v: 'Residential' }
          ]
        },
        Other: {
          n: 'Other',
          v: [
            { n: 'Accessibility Features', v: 'Wheelchair Access' },
            { n: 'Association Fee', v: 200 },
            { n: 'Association Fee Frequency', v: 'Monthly' },
            { n: 'Association Fee Includes', v: 'Water, Insurance' },
            { n: 'Association Name', v: 'Homeowners Association' }
          ]
        },
        _orig: {
          stp: 'Residential',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes',
          saletp: ['Sale'],
          status: 'A'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate BRE source property data', ->
      # 输入参数
      input = {
        src: 'BRE',
        id: 'BRE123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        den: 1,
        blcny: 0,
        grdn: 1,
        gym: 0,
        pool: 1,
        fpop: 0,
        lkr: 1,
        fpl: 0,
        htb: 1,
        wifi: 0,
        tv: 1,
        wsr: 0,
        dyr: 1,
        frdg: 0,
        utl: 1,
        hydro_inc: 0,
        water_inc: 1,
        smok: 0,
        pets: 1,
        movn: 0,
        frnshd: 1,
        ptp2: [ 'Residential' ],
        saletp: [ 'Sale' ],
        m_zh: '这是一个测试房源',
        OwnershipType: 'Freehold',
        SeniorCommunityYN: 'Y',
        GarageYN: 'N',
        Building: {
          'Age': 10,
          'Appliances': 'Dishwasher, Refrigerator, Stove',
          'ArchitecturalStyle': 'Modern',
          'BuildingFeatures': 'Security System',
          'ConstructedDate': '2013',
          'ConstructionMaterials': 'Wood Frame',
          'FireplaceFeatures': 'Gas',
          'FireplacesTotal': 2,
          'Flooring': 'Hardwood',
          'FoundationDetails': 'Poured Concrete',
          'HeatingType': 'Forced Air',
          'HeatingFuel': 'Natural Gas',
          'LotFeatures': 'Fenced Yard',
          'NumberOfBuildings': 1,
          'NumberOfUnitsTotal': 1,
          'PropertyCondition': 'Good'
        },
        Land: {
          'LotSzSqMtrs': 500,
          'LotSzHectares': 0.05,
          'LotSzAcres': 0.12,
          'DirectionalExpRearYard': 'South'
        },
        Utilities: [
          { t: 'Electric', m: 'Available' },
          { t: 'OtherEquipment', m: 'Central Vacuum' },
          { t: 'UtilitiesAvailable', m: 'All' },
          { t: 'WaterBodyName', m: 'Lake' },
          { t: 'WaterfrontFeatures', m: 'Dock' },
          { t: 'WaterSource', m: 'Municipal' }
        ],
        'Parking': 'Garage',
        'ParkingAccess': 'Self Park',
        'GarageDoorHt': 8,
        'ParkingType': 'Attached',
        'ParkingFeatures': 'Heated',
        'DisttoSchoolSchoolBus': 1.5,
        'CouncilParkApprv': 'Yes',
        'WaterfrontFeatures': 'Dock',
        'CommunityFeatures': 'Park',
        'DistancetoPubRapidTr': 0.5,
        'FloorAreaFinBasement': 1000,
        'BasementArea': 'Finished',
        'ProcessedDate': '2023-01-01',
        'PropertyBrochureURL': 'https://example.com/brochure',
        'PID': '123456789',
        'SewerType': 'Municipal',
        'CancelEffectiveDate': '2023-12-31',
        'GSTIncl': 'Yes',
        'SiteInfluences': 'Quiet',
        'NoBuyerAgency': 'Yes',
        'AgeType': 'Resale',
        'PropertyDisclosure': 'Yes',
        'ByLawRestrictions': 'None',
        'ServicesConnected': 'All',
        'RainScreen': 'Yes',
        'ViewSpecify': 'Lake',
        'FixtRntdLsdSpecify': 'All',
        'AddressNumberLow': 123,
        'FixturesRemvd': 'None',
        'ofPets': 'Negotiable',
        'BrokerReciprocity': 'Yes',
        'FixturesRemovedYN': 'N',
        'FixturesRntdLsd': 'All',
        'FloodPlain': 'No',
        'ApproxYrofRenosAddns': '2020',
        'NotInclinTaxSewer': 'No',
        'MgmtCoName': 'ABC Management',
        'MgmtCoPhone': '************',
        'Cats': 'Yes',
        'Dogs': 'Yes',
        'MaintFeeIncludes': 'Water, Insurance',
        'ShortTermLseDetails': 'None',
        'RestrictedAge': 'None',
        'PropDisclosureStatement': 'Yes',
        Type: 'Single Family',
        Roof: ['Asphalt','Shingle'],
        Parking: [ {Name: 'Garage'},{name:'Private'} ]
      }
      
      # 期望的输出
      expected = {
        src: 'BRE',
        id: 'BRE123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        den: 'Yes',
        blcny: 'No',
        grdn: 'Yes',
        gym: 'No',
        pool: 'Yes',
        fpop: 'No',
        lkr: 'Yes',
        fpl: 'No',
        htb: 'Yes',
        wifi: 'No',
        tv: 'Yes',
        wsr: 'No',
        dyr: 'Yes',
        frdg: 'No',
        utl: 'Yes',
        hydro_inc: 'No',
        water_inc: 'Yes',
        smok: 'No',
        pets: 'Yes',
        movn: 'No',
        frnshd: 'Yes',
        ptp2: [ 'Residential' ],
        saletp: [ 'Sale' ],
        saletp_en: [ 'Sale' ],
        m_zh: '这是一个测试房源',
        OwnershipType: {
          n: 'Ownership Type',
          v: 'Freehold'
        },
        stplabel: 'Sale',
        Building: {
          n: 'Building',
          v: [
            { t: 'Age', m: 10 },
            { t: 'Appliances', m: 'Dishwasher, Refrigerator, Stove' },
            { t: 'Architectural Style', m: 'Modern' },
            { t: 'Building Features', m: 'Security System' },
            { t: 'Constructed Date', m: '2013' },
            { t: 'Construction Materials', m: 'Wood Frame' },
            { t: 'Fireplace Features', m: 'Gas' },
            { t: 'Fireplaces Total', m: 2 },
            { t: 'Flooring', m: 'Hardwood' },
            { t: 'Foundation Details', m: 'Poured Concrete' },
            { t: 'Heating Type', m: 'Forced Air' },
            { t: 'Heating Fuel', m: 'Natural Gas' },
            { t: 'Lot Features', m: 'Fenced Yard' },
            { t: 'Number Of Buildings', m: 1 },
            { t: 'Number Of Units Total', m: 1 },
            { t: 'Property Condition', m: 'Good' },
            { t: 'Type', m: 'Single Family' },
            { t: 'Roof', m: 'Asphalt,Shingle' },
            { t: 'Senior Community', m: 'Yes' },
            { t: 'Garage', m: 'No' }
          ]
        },
        Land: {
          n: 'Land',
          v: [
            { t: 'Lot Sz Sq Mtrs', m: 500 },
            { t: 'Lot Sz Hectares', m: 0.05 },
            { t: 'Lot Sz Acres', m: 0.12 },
            { t: 'Directional Exp Rear Yard', m: 'South' }
          ]
        },
        Utilities: {
          n: 'Utilities',
          v: [
            { t: 'Electric', m: 'Available' },
            { t: 'Other Equipment', m: 'Central Vacuum' },
            { t: 'Utilities Available', m: 'All' },
            { t: 'Water Body Name', m: 'Lake' },
            { t: 'Waterfront Features', m: 'Dock' },
            { t: 'Water Source', m: 'Municipal' }
          ]
        },
        Parking: {
          n: 'Parking',
          v: [
            { t: 'Parking', m: 'Garage,Private' },
            { t: 'Parking Access', m: 'Self Park' },
            { t: 'Garage Door Ht', m: 8 },
            { t: 'Parking Type', m: 'Attached' },
            { t: 'Parking Features', m: 'Heated' }
          ]
        },
        Surrounding: {
          n: 'Surrounding',
          v: [
            { n: 'Distto School School Bus', v: 1.5 },
            { n: 'Council Park Approve', v: 'Yes' },
            { n: 'Waterfront Features', v: 'Dock' },
            { n: 'Community Features', v: 'Park' },
            { n: 'Distanceto Pub Rapid Tr', v: 0.5 }
          ]
        },
        Basement: {
          n: 'Basement',
          v: [
            { t: 'Floor Area Finished Basement', m: 1000 },
            { t: 'Basement Area', m: 'Finished' }
          ]
        },
        Other: {
          n: 'Other',
          v: [
            { n: 'Processed Date', v: '2023-01-01' },
            { n: 'Property Brochure URL', v: 'https://example.com/brochure' },
            { n: 'Pid', v: '123456789' },
            { n: 'Sewer Type', v: 'Municipal' },
            { n: 'Cancel Effective Date', v: '2023-12-31' },
            { n: 'Gst Included', v: 'Yes' },
            { n: 'Site Influences', v: 'Quiet' },
            { n: 'No Buyer Agency', v: 'Yes' },
            { n: 'Age Type', v: 'Resale' },
            { n: 'Property Disclosure', v: 'Yes' },
            { n: 'By Law Restrictions', v: 'None' },
            { n: 'Services Connected', v: 'All' },
            { n: 'Rain Screen', v: 'Yes' },
            { n: 'View Specify', v: 'Lake' },
            { n: 'Fixt Rented Leased Specify', v: 'All' },
            { n: 'Address Number Low', v: 123 },
            { n: 'Fixtures Removed', v: 'None' },
            { n: 'of Pets', v: 'Negotiable' },
            { n: 'Broker Reciprocity', v: 'Yes' },
            { n: 'Fixtures Removed', v: 'No' },
            { n: 'Fixtures Rented Leased', v: 'All' },
            { n: 'Flood Plain', v: 'No' },
            { n: 'Approx Year of Renovations Addns', v: '2020' },
            { n: 'Not Included in Tax Sewer', v: 'No' },
            { n: 'Mgmt Co Name', v: 'ABC Management' },
            { n: 'Mgmt Co Phone', v: '************' },
            { n: 'Cats', v: 'Yes' },
            { n: 'Dogs', v: 'Yes' },
            { n: 'Maint Fee Includes', v: 'Water, Insurance' },
            { n: 'Short Term Lse Details', v: 'None' },
            { n: 'Restricted Age', v: 'None' },
            { n: 'Prop Disclosure Statement', v: 'Yes' }
          ]
        },
        _orig: {
          stp: 'Residential',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes',
          saletp: ['Sale'],
          status: 'A'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate RHB source property data', ->
      # 输入参数
      input = {
        src: 'RHB',
        id: 'RHB123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        BusinessType: 'Residential'
      }
      
      # 期望的输出
      expected = {
        src: 'RHB',
        id: 'RHB123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        rmPsn_en: 'Immediate',
        BusinessType: {
          n: 'Business Type',
          v: 'Residential'
        },
        _orig: {
          stp: 'Residential',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes',
          rmPsn: 'Immediate',
          status: 'A'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate OTW source property data', ->
      # 输入参数
      input = {
        src: 'OTW',
        id: 'OTW123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        Building: {
          'ExteriorFinish': 'Brick',
          'FireRetrofit': 'Completed',
          'FireplaceFuel': 'Gas',
          'FloorCovering': 'Hardwood',
          'FoundationDescription': 'Poured Concrete',
          'InterboardListing': 'Yes',
          'LegalDescription': 'Lot 1, Plan 123',
          'NumberFinishedFireplaces': 2,
          'NumberofCoveredSpaces': 2,
          'NumberofEnsuiteBathrooms': 2,
          'NumberofFireplaces': 2,
          'NumberofGarageSpaces': 2,
          'RoomCount': 8,
          'SeasonalDwelling': 'No',
          'AppliancesIncluded': 'Dishwasher, Refrigerator, Stove',
          'RoofDescription': 'Asphalt Shingle',
          'TypeofLot': 'Regular',
          'CondoName': 'Sunset Gardens',
          'NumberofHydroMeters': 1,
          'NumberofRefrigerators': 1,
          'NumberofStoves': 1,
          'NumberofTownhouseUnits': 1,
          'NumberofWashersDryers': 1,
          'TotalNumberofUnits': 1,
          'NoofVacantUnits': 0,
          'NumberofOtherUnits': 0,
          'NumberofOfficeUnits': 0,
          'TotalNumberofOtherUnits': 0,
          'NumberofLevelsInUnit': 2,
          'NumberofStories': 2,
          'TitleForm': 'Freehold'
        },
        'RoomingHouseLicenseMultiYN': 'N',
        'HasSecondaryDwellingUnitYN': 'N',
        'IrregularLotYN': 'N',
        Land: {
          'LotSzSqMtrs': 500,
          'LotSzHectares': 0.05,
          'LotSzAcres': 0.12,
          'DirectionalExpRearYard': 'South'
        },
        Utilities: [
          { t: 'Electric', m: 'Available' },
          { t: 'OtherEquipment', m: 'Central Vacuum' },
          { t: 'UtilitiesAvailable', m: 'All' },
          { t: 'WaterBodyName', m: 'Lake' },
          { t: 'WaterfrontFeatures', m: 'Dock' },
          { t: 'WaterSource', m: 'Municipal' }
        ],
        'SiteInfluences': 'Quiet',
        'SPLP': 0.5,
        'SewerType': 'Municipal',
        'Restrictions': 'None',
        'SurveyYear': '2020',
        'ModelName': 'Classic',
        'AdditionalPropertyUsesICI': 'None',
        'DistributeOnInternetYN': 'Y',
        'RoadAccess': 'Paved',
        'AdditionalImagesLinkURL': 'https://example.com/images',
        'Amenities': 'Park, Pool',
        'CondoFee': 500,
        'CondoFeeFrequency': 'Monthly',
        'FeeIncludes': 'Water, Insurance',
        'LaundryFacilities': 'In Suite',
        'SpecialLevyYN': 'N',
        'AssistiveFeaturesRemarks': 'None',
        'HasAdditionalCommissionYN': 'N',
        'RentPriceRate': 'Monthly',
        'TenantPays': 'Electricity',
        'AreaCleared': 1000,
        'AreaCultivated': 500,
        'AreaTileDrained': 0,
        'CropsIncludedYN': 'N',
        'FarmRemarks': 'None',
        'FarmResidenceYN': 'N',
        'LivestockIncludedYN': 'N',
        'TypeofFarm': 'None',
        'CurrentUse': 'Residential',
        'AnnualDebtService': 12000,
        'AnnualGrossIncome': 60000,
        'CashFlowAmount': 24000,
        'EffectRentalIncome': 5000,
        'GarbageExpense': 1200,
        'GrossOperatingIncome': 60000,
        'HeatingExpense': 2400,
        'HydroExpense': 3600,
        'InsuranceExpense': 1200,
        'LaundryOtherIncome': 0,
        'LawnExpense': 1200,
        'NetOperatingIncome': 48000,
        'NumberofBachelorUnits': 0,
        'OtherExpense': 0,
        'SupplyExpense': 0,
        'TotalIncomeperMonth': 5000,
        'SecurityExpense': 1200,
        'TotalOperatExpPct': 20,
        'TotalOperatingExpenses': 12000,
        'VacancyLossAmount': 0,
        'VacancyPercentage': 0,
        'WaterSewerExpense': 2400,
        'NumberofDishwashers': 1,
        'DevelopmentStatus': 'Completed',
        'LotServiced': 'Yes',
        'RoadSurface': 'Paved',
        'EnvironmentAssessment': 'Completed',
        'FireProtection': 'Smoke Detectors',
        'HospitalityType': 'None',
        'WaterSupply': 'Municipal',
        'FeaturesEquipmentIncluded': 'All',
        'FeaturesforDisabledYN': 'N',
        'PowerDescription': '200 Amp Service',
        'NumofAcres': 0.12,
        'NumberofParcels': 1,
        'TotalAreaAcres': 0.12,
        'ApproximateBuildingSqft': 2000,
        'NumberofLots': 1,
        'ParkingDescription': 'Double Garage',
        'ParkingAtAddCostGencomYN': 'N',
        'ParkingIncluded': 'Yes',
        'ParkingforGencom': 'None',
        'ParkingIncome': 0,
        'SurfaceParkingforUnits': 0,
        'UnderParkingforUnits': 0,
        'DeckParkingforUnits': 0
      }
      
      # 期望的输出
      expected = {
        src: 'OTW',
        id: 'OTW123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [{t: 'Bedroom', l: 'Master'}, {t: 'Bathroom', l: 'Full'}],
        rmPsn: 'Immediate',
        rmPsn_en: 'Immediate',
        bsmt: ['Finished'],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        SiteInfluences: 'Quiet',
        SPLP: 0.5,
        SewerType: 'Municipal',
        Restrictions: 'None',
        SurveyYear: '2020',
        ModelName: 'Classic',
        AdditionalPropertyUsesICI: 'None',
        DistributeOnInternetYN: 'Y',
        RoadAccess: 'Paved',
        AdditionalImagesLinkURL: 'https://example.com/images',
        Amenities: 'Park, Pool',
        CondoFee: 500,
        CondoFeeFrequency: 'Monthly',
        FeeIncludes: 'Water, Insurance',
        LaundryFacilities: 'In Suite',
        SpecialLevyYN: 'N',
        AssistiveFeaturesRemarks: 'None',
        HasAdditionalCommissionYN: 'N',
        RentPriceRate: 'Monthly',
        TenantPays: 'Electricity',
        AreaCleared: 1000,
        AreaCultivated: 500,
        AreaTileDrained: 0,
        CropsIncludedYN: 'N',
        FarmRemarks: 'None',
        FarmResidenceYN: 'N',
        LivestockIncludedYN: 'N',
        TypeofFarm: 'None',
        CurrentUse: 'Residential',
        AnnualDebtService: 12000,
        AnnualGrossIncome: 60000,
        CashFlowAmount: 24000,
        EffectRentalIncome: 5000,
        GarbageExpense: 1200,
        GrossOperatingIncome: 60000,
        HeatingExpense: 2400,
        HydroExpense: 3600,
        InsuranceExpense: 1200,
        LaundryOtherIncome: 0,
        LawnExpense: 1200,
        NetOperatingIncome: 48000,
        NumberofBachelorUnits: 0,
        OtherExpense: 0,
        SupplyExpense: 0,
        TotalIncomeperMonth: 5000,
        SecurityExpense: 1200,
        TotalOperatExpPct: 20,
        TotalOperatingExpenses: 12000,
        VacancyLossAmount: 0,
        VacancyPercentage: 0,
        WaterSewerExpense: 2400,
        NumberofDishwashers: 1,
        DevelopmentStatus: 'Completed',
        LotServiced: 'Yes',
        RoadSurface: 'Paved',
        EnvironmentAssessment: 'Completed',
        FireProtection: 'Smoke Detectors',
        HospitalityType: 'None',
        WaterSupply: 'Municipal',
        FeaturesEquipmentIncluded: 'All',
        FeaturesforDisabledYN: 'N',
        PowerDescription: '200 Amp Service',
        NumofAcres: 0.12,
        NumberofParcels: 1,
        TotalAreaAcres: 0.12,
        ApproximateBuildingSqft: 2000,
        NumberofLots: 1,
        ParkingDescription: 'Double Garage',
        ParkingAtAddCostGencomYN: 'N',
        ParkingIncluded: 'Yes',
        ParkingforGencom: 'None',
        ParkingIncome: 0,
        SurfaceParkingforUnits: 0,
        UnderParkingforUnits: 0,
        DeckParkingforUnits: 0,
        RoomingHouseLicenseMultiYN: 'N',
        HasSecondaryDwellingUnitYN: 'N',
        IrregularLotYN: 'N',
        Building: {
          n: 'Building',
          v: [
            {t: 'Exterior Finish', m: 'Brick'},
            {t: 'Fire Retrofit', m: 'Completed'},
            {t: 'Fireplace Fuel', m: 'Gas'},
            {t: 'Floor Covering', m: 'Hardwood'},
            {t: 'Foundation Description', m: 'Poured Concrete'},
            {t: 'Interboard Listing', m: 'Yes'},
            {t: 'Legal Description', m: 'Lot 1, Plan 123'},
            {t: 'Number Finished Fireplaces', m: 2},
            {t: 'Numberof Covered Spaces', m: 2},
            {t: 'Numberof Ensuite Bathrooms', m: 2},
            {t: 'Numberof Fireplaces', m: 2},
            {t: 'Numberof Garage Spaces', m: 2},
            {t: 'Room Count', m: 8},
            {t: 'Seasonal Dwelling', m: 'No'},
            {t: 'Appliances Included', m: 'Dishwasher, Refrigerator, Stove'},
            {t: 'Roof Description', m: 'Asphalt Shingle'},
            {t: 'Typeof Lot', m: 'Regular'},
            {t: 'Condo Name', m: 'Sunset Gardens'},
            {t: 'Numberof Hydro Meters', m: 1},
            {t: 'Numberof Refrigerators', m: 1},
            {t: 'Numberof Stoves', m: 1},
            {t: 'Numberof Townhouse Units', m: 1},
            {t: 'Numberof Washers Dryers', m: 1},
            {t: 'Total Numberof Units', m: 1},
            {t: 'Noof Vacant Units', m: 0},
            {t: 'Numberof Other Units', m: 0},
            {t: 'Numberof Office Units', m: 0},
            {t: 'Total Numberof Other Units', m: 0},
            {t: 'Numberof Levels In Unit', m: 2},
            {t: 'Numberof Stories', m: 2},
            {t: 'Title Form', m: 'Freehold'},
            {t: 'Has Secondary Dwelling Unit', m: 'No'},
            {t: 'Rooming House License Multi', m: 'No'},
            {t: 'Irregular Lot', m: 'No'},
          ]
        },
        Land: {
          n: 'Land',
          v: [
            {t: 'Lot Sz Sq Mtrs', m: 500},
            {t: 'Lot Sz Hectares', m: 0.05},
            {t: 'Lot Sz Acres', m: 0.12},
            {t: 'Directional Exp Rear Yard', m: 'South'},
            {t: 'Numof Acres', m: 0.12},
            {t: 'Numberof Parcels', m: 1},
            {t: 'Total Area Acres', m: 0.12},
            {t: 'Approximate Building Sqft', m: 2000},
            {t: 'Numberof Lots', m: 1}
          ]
        },
        Utilities: {
          n: 'Utilities',
          v: [
            {t: 'Electric', m: 'Available'},
            {t: 'Other Equipment', m: 'Central Vacuum'},
            {t: 'Utilities Available', m: 'All'},
            {t: 'Water Body Name', m: 'Lake'},
            {t: 'Waterfront Features', m: 'Dock'},
            {t: 'Water Source', m: 'Municipal'},
            {t: 'Water Supply', m: 'Municipal'},
            {t: 'Features Equipment Included', m: 'All'},
            {t: 'Featuresfor Disabled', m: 'No'},
            {t: 'Power Description', m: '200 Amp Service'}
          ]
        },
        Other: {
          n: 'Other',
          v: [
            {n: 'Site Influences', v: 'Quiet'},
            {n: 'SPLP', v: 0.5},
            {n: 'Sewer Type', v: 'Municipal'},
            {n: 'Restrictions', v: 'None'},
            {n: 'Survey Year', v: '2020'},
            {n: 'Model Name', v: 'Classic'},
            {n: 'Additional Property Uses ICI', v: 'None'},
            {n: 'Distribute On Internet', v: 'Yes'},
            {n: 'Road Access', v: 'Paved'},
            {n: 'Additional Images Link URL', v: 'https://example.com/images'},
            {n: 'Amenities', v: 'Park, Pool'},
            {n: 'Condo Fee', v: 500},
            {n: 'Condo Fee Frequency', v: 'Monthly'},
            {n: 'Fee Includes', v: 'Water, Insurance'},
            {n: 'Laundry Facilities', v: 'In Suite'},
            {n: 'Special Levy', v: 'No'},
            {n: 'Assistive Features Remarks', v: 'None'},
            {n: 'Has Additional Commission', v: 'No'},
            {n: 'Rent Price Rate', v: 'Monthly'},
            {n: 'Tenant Pays', v: 'Electricity'},
            {n: 'Area Cleared', v: 1000},
            {n: 'Area Cultivated', v: 500},
            {n: 'Crops Included', v: 'No'},
            {n: 'Farm Remarks', v: 'None'},
            {n: 'Farm Residence', v: 'No'},
            {n: 'Livestock Included', v: 'No'},
            {n: 'Typeof Farm', v: 'None'},
            {n: 'Current Use', v: 'Residential'},
            {n: 'Annual Debt Service', v: 12000},
            {n: 'Annual Gross Income', v: 60000},
            {n: 'Cash Flow Amount', v: 24000},
            {n: 'Effect Rental Income', v: 5000},
            {n: 'Garbage Expense', v: 1200},
            {n: 'Gross Operating Income', v: 60000},
            {n: 'Heating Expense', v: 2400},
            {n: 'Hydro Expense', v: 3600},
            {n: 'Insurance Expense', v: 1200},
            {n: 'Lawn Expense', v: 1200},
            {n: 'Net Operating Income', v: 48000},
            {n: 'Total Incomeper Month', v: 5000},
            {n: 'Security Expense', v: 1200},
            {n: 'Total Operat Exp Pct', v: 20},
            {n: 'Total Operating Expenses', v: 12000},
            {n: 'Water Sewer Expense', v: 2400},
            {n: 'Numberof Dishwashers', v: 1},
            {n: 'Development Status', v: 'Completed'},
            {n: 'Lot Serviced', v: 'Yes'},
            {n: 'Road Surface', v: 'Paved'},
            {n: 'Environment Assessment', v: 'Completed'},
            {n: 'Fire Protection', v: 'Smoke Detectors'},
            {n: 'Hospitality Type', v: 'None'}
          ]
        },
        Parking: {
          n: 'Parking',
          v: [
            {t: 'Parking Description', m: 'Double Garage'},
            {t: 'Parking At Add Cost Gencom', m: 'No'},
            {t: 'Parking Included', m: 'Yes'},
            {t: 'Parkingfor Gencom', m: 'None'}
          ]
        },
        _orig: {
          rmPsn: 'Immediate', stp: 'Residential', status: 'A', ptp: 'Single Family',
          pstyl: 'Detached', rtp: 'Freehold', rgdr: 'Yes'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate CLG source property data', ->
      # 输入参数
      input = {
        src: 'CLG',
        id: 'CLG123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        Building: {
          'Age': '5-10 years',
          'Appliances': 'Dishwasher, Refrigerator, Stove',
          'ArchitecturalStyle': 'Modern',
          'BasementDevelopment': 'Finished',
          'BasementType': 'Full',
          'ConstructionMaterial': 'Wood Frame',
          'ExteriorFeatures': 'Deck, Patio',
          'Fireplace': 'Yes',
          'Flooring': 'Hardwood, Carpet',
          'Foundation': 'Poured Concrete',
          'Heating': 'Forced Air',
          'HeatingFuel': 'Natural Gas',
          'InteriorFeatures': 'Central Vacuum, In-floor Heating',
          'Roof': 'Asphalt Shingle',
          'TotalBathrooms': 3,
          'TotalBedrooms': 4,
          'TotalRooms': 8,
          'Type': 'Residential',
          'YearBuilt': 2015
        },
        WaterSource: 'Municipal',
        Gas: 'Available',
        Utilities: [
          { t: 'Electric', m: 'Available' },
        ],
        ParkingAssigned: 'Double',
        ParkingCommonSpaces: 4,
        ParkingPlanType: 'Attached'
        CommunityFeatures: 'Nearby',
        CountyOrParish: 'CA',
        NineOneOneAddress: 'Nearby'
        Restrictions: 'None',
        TaxBlock: '123',
        Sewer: 'Municipal',
        AcresOfFreehold: '1.00',
        AssociationFee: '100',
      }
      
      # 期望的输出
      expected = {
        src: 'CLG',
        id: 'CLG123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [
          { t: 'Bedroom', l: 'Master' },
          { t: 'Bathroom', l: 'Full' }
        ],
        rmPsn: 'Immediate',
        rmPsn_en: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        WaterSource: 'Municipal',
        Gas: 'Available',
        ParkingAssigned: 'Double',
        ParkingCommonSpaces: 4,
        ParkingPlanType: 'Attached',
        CommunityFeatures: 'Nearby',
        CountyOrParish: 'CA',
        NineOneOneAddress: 'Nearby',
        Restrictions: 'None',
        TaxBlock: '123',
        Sewer: 'Municipal',
        AcresOfFreehold: '1.00',
        AssociationFee: '100',
        Building: {
          n: 'Building',
          v: [
            { t: 'Age', m: '5-10 years' },
            { t: 'Appliances', m: 'Dishwasher, Refrigerator, Stove' },
            { t: 'Architectural Style', m: 'Modern' },
            { t: 'Basement Development', m: 'Finished' },
            { t: 'Basement Type', m: 'Full' },
            { t: 'Construction Material', m: 'Wood Frame' },
            { t: 'Exterior Features', m: 'Deck, Patio' },
            { t: 'Fireplace', m: 'Yes' },
            { t: 'Flooring', m: 'Hardwood, Carpet' },
            { t: 'Foundation', m: 'Poured Concrete' },
            { t: 'Heating', m: 'Forced Air' },
            { t: 'Heating Fuel', m: 'Natural Gas' },
            { t: 'Interior Features', m: 'Central Vacuum, In-floor Heating' },
            { t: 'Roof', m: 'Asphalt Shingle' },
            { t: 'Total Bathrooms', m: 3 },
            { t: 'Total Bedrooms', m: 4 },
            { t: 'Total Rooms', m: 8 },
            { t: 'Type', m: 'Residential' },
            { t: 'Year Built', m: 2015 }
          ]
        },
        Utilities: {
          n: 'Utilities',
          v: [
            { t: 'Electric', m: 'Available' },
            { t: 'Water Source', m: 'Municipal' }
          ]
        },
        Surrounding: {
          n: 'Surrounding',
          v: [
            { n: 'Community Features', v: 'Nearby' },
            { n: 'County Or Parish', v: 'CA' },
            { n: 'Nine One One Address', v: 'Nearby' }
          ]
        },
        Other: {
          n: 'Other',
          v: [
            { n: 'Restrictions', v: 'None' },
            { n: 'Tax Block', v: '123' },
            { n: 'Sewer', v: 'Municipal' },
            { n: 'Acres Of Freehold', v: '1.00' },
            { n: 'Association Fee', v: '100' }
          ]
        },
        Parking: {
          n: 'Parking',
          v: [
            { t: 'Parking Assigned', m: 'Double' },
            { t: 'Parking Common Spaces', m: 4 },
            { t: 'Parking Plan Type', m: 'Attached' }
          ]
        },
        _orig: {
          rmPsn: 'Immediate',
          stp: 'Residential',
          status: 'A',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate EDM source property data', ->
      # 输入参数
      input = {
        src: 'EDM',
        id: 'EDM123456',
        dir_desc: 'Directions Description',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        Building: {
          'HeatingType': 'Forced Air',
          'HeatingFuel': 'Natural Gas',
        },
        'LotSizeArea': 5000,
        'Zoning': 'R1',
        'CommunityFeatures': 'Double',
        'ParkingFeatures': 'Heated, Insulated',
        'Schools': 'Nearby',
        'HighSchool': 'Within 1 km',
        'Development': 'Finished',
        'Type': 'Full',
        'Features': 'Separate Entrance, Walk-out',
      }
      
      # 期望的输出
      expected = {
        src: 'EDM',
        id: 'EDM123456',
        dir_desc: 'Directions Description',
        trbtp: [],
        psn: '2023-01-01',
        rms: [
          { t: 'Bedroom', l: 'Master' },
          { t: 'Bathroom', l: 'Full' }
        ],
        rmPsn: 'Immediate',
        rmPsn_en: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        LotSizeArea: 5000,
        Zoning: 'R1',
        CommunityFeatures: 'Double',
        ParkingFeatures: 'Heated, Insulated',
        Schools: 'Nearby',
        HighSchool: 'Within 1 km',
        Development: 'Finished',
        Type: 'Full',
        Features: 'Separate Entrance, Walk-out',
        Building: {
          n: 'Building',
          v: [
            { t: 'Heating Type', m: 'Forced Air' },
            { t: 'Heating Fuel', m: 'Natural Gas' },
            { t: 'Directions Description', m: 'Directions Description' }
          ]
        },
        Surrounding: {
          n: 'Surrounding',
          v: [
            { n: 'Community Features', v: 'Double' },
            { n: 'High School', v: 'Within 1 km' },
            { n: 'Zoning', v: 'R1' }
          ]
        },
        Land: {
          n: 'Land',
          v: [
            { t: 'Lot Size Area', m: 5000 }
          ]
        },
        Parking: {
          n: 'Parking',
          v: [
            { t: 'Parking Features', m: 'Heated, Insulated' }
          ]
        },
        _orig: {
          rmPsn: 'Immediate',
          stp: 'Residential',
          status: 'A',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate CAR source property data', ->
      # 输入参数
      input = {
        src: 'CAR',
        id: 'CAR123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        dir_desc: 'North facing',
        Appliances: 'Dishwasher, Refrigerator, Stove',
        ArchitecturalStyle: 'Modern',
        Amenities: 'Park, Pool',
        FireplacePresent: 'Yes',
        BedroomsAboveGround: 3,
        HeatingType: 'Forced Air',
        HeatingFuel: 'Natural Gas',
        AboveGradeFinishedArea: 2000,
        AboveGradeFinishedAreaSource: 'Appraiser',
        AboveGradeFinishedAreaUnits: 'Square Feet',
        AssociationAmenities: 'Clubhouse, Pool',
        BelowGradeFinishedArea: 1000,
        BuildingType: 'Single Family',
        BuildingAreaSource: 'Appraiser',
        BuildingAreaTotal: 3000,
        BuildingAreaUnits: 'Square Feet',
        FireplaceFeatures: 'Gas',
        Flooring: 'Hardwood, Carpet',
        FullBaths: 2,
        IrrigationWaterRightsYN: 'Y',
        HalfBaths: 1,
        HomeWarrantyYN: 'Y',
        LivingAreaUnits: 'Square Feet',
        LotFeatures: 'Fenced Yard',
        MainLevelBathrooms: 1,
        MainLevelBedrooms: 1,
        NewConstructionYN: 'N',
        NumberOfBuildings: 1,
        NumberofStories: 2,
        OccupantType: 'Owner',
        OriginatingSystemName: 'MLS',
        PatioAndPorchFeatures: 'Deck, Patio',
        PropertyAttachedYN: 'N',
        PropertyCondition: 'Good',
        Roof: 'Asphalt Shingle',
        RoomsTotal: 8,
        SeniorCommunityYN: 'N',
        CommunityFeatures: 'Park, Pool',
        ExteriorFeatures: 'Deck, Patio',
        ElementarySchool: 'Nearby',
        HighSchool: 'Within 1 km',
        MiddleOrJuniorSchool: 'Within 2 km',
        Zoning: 'R1',
        LotSizeArea: 5000,
        LotSizeAreaUnits: 'Square Feet',
        LotSizeSource: 'Appraiser',
        ParkingFeatures: 'Heated, Insulated',
        ParkingTotal: 2,
        PoolFeatures: 'In Ground',
        View: 'City',
        WaterSource: 'Municipal',
        Sewer: 'Municipal',
        TaxAnnualAmount: 5000,
        TaxLot: '123',
        TaxTract: '456',
        InternetAutomatedValuationDisplayYN: 'Y',
        InternetEntireListingDisplayYN: 1,
        AssociationFeeFrequency: 'Monthly',
        Utilities: [
          { t: 'Electric', m: 'Available' },
          { t: 'Gas', m: 'Available' },
          { t: 'Water', m: 'Available' }
        ]
      }
      
      # 期望的输出
      expected = {
        src: 'CAR',
        id: 'CAR123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [
          { t: 'Bedroom', l: 'Master' },
          { t: 'Bathroom', l: 'Full' }
        ],
        rmPsn: 'Immediate',
        rmPsn_en: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        dir_desc: 'North facing',
        Appliances: 'Dishwasher, Refrigerator, Stove',
        ArchitecturalStyle: 'Modern',
        Amenities: 'Park, Pool',
        FireplacePresent: 'Yes',
        BedroomsAboveGround: 3,
        HeatingType: 'Forced Air',
        HeatingFuel: 'Natural Gas',
        AboveGradeFinishedArea: 2000,
        AboveGradeFinishedAreaSource: 'Appraiser',
        AboveGradeFinishedAreaUnits: 'Square Feet',
        AssociationAmenities: 'Clubhouse, Pool',
        BelowGradeFinishedArea: 1000,
        BuildingType: 'Single Family',
        BuildingAreaSource: 'Appraiser',
        BuildingAreaTotal: 3000,
        BuildingAreaUnits: 'Square Feet',
        FireplaceFeatures: 'Gas',
        Flooring: 'Hardwood, Carpet',
        FullBaths: 2,
        IrrigationWaterRightsYN: 'Y',
        HalfBaths: 1,
        HomeWarrantyYN: 'Y',
        LivingAreaUnits: 'Square Feet',
        LotFeatures: 'Fenced Yard',
        MainLevelBathrooms: 1,
        MainLevelBedrooms: 1,
        NewConstructionYN: 'N',
        NumberOfBuildings: 1,
        NumberofStories: 2,
        OccupantType: 'Owner',
        OriginatingSystemName: 'MLS',
        PatioAndPorchFeatures: 'Deck, Patio',
        PropertyAttachedYN: 'N',
        PropertyCondition: 'Good',
        Roof: 'Asphalt Shingle',
        RoomsTotal: 8,
        SeniorCommunityYN: 'N',
        CommunityFeatures: 'Park, Pool',
        ExteriorFeatures: 'Deck, Patio',
        ElementarySchool: 'Nearby',
        HighSchool: 'Within 1 km',
        MiddleOrJuniorSchool: 'Within 2 km',
        Zoning: 'R1',
        LotSizeArea: 5000,
        LotSizeAreaUnits: 'Square Feet',
        LotSizeSource: 'Appraiser',
        ParkingFeatures: 'Heated, Insulated',
        ParkingTotal: 2,
        PoolFeatures: 'In Ground',
        View: 'City',
        WaterSource: 'Municipal',
        Sewer: 'Municipal',
        TaxAnnualAmount: 5000,
        TaxLot: '123',
        TaxTract: '456',
        InternetAutomatedValuationDisplayYN: 'Y',
        InternetEntireListingDisplayYN: 1,
        AssociationFeeFrequency: 'Monthly',
        Utilities: {
          n: 'Utilities',
          v: [
            { t: 'Electric', m: 'Available' },
            { t: 'Gas', m: 'Available' },
            { t: 'Water', m: 'Available' },
            { t: 'Sewer', m: 'Municipal' },
            { t: 'Water Source', m: 'Municipal' }
          ]
        },
        Building: {
          n: 'Building',
          v: [
            { t: 'Appliances', m: 'Dishwasher, Refrigerator, Stove' },
            { t: 'Architectural Style', m: 'Modern' },
            { t: 'Amenities', m: 'Park, Pool' },
            { t: 'Fireplace Present', m: 'Yes' },
            { t: 'Bedrooms Above Ground', m: 3 },
            { t: 'Heating Type', m: 'Forced Air' },
            { t: 'Heating Fuel', m: 'Natural Gas' },
            { t: 'Above Grade Finished Area', m: 2000 },
            { t: 'Above Grade Finished Area Source', m: 'Appraiser' },
            { t: 'Above Grade Finished Area Units', m: 'Square Feet' },
            { t: 'Association Amenities', m: 'Clubhouse, Pool' },
            { t: 'Below Grade Finished Area', m: 1000 },
            { t: 'Building Type', m: 'Single Family' },
            { t: 'Building Area Source', m: 'Appraiser' },
            { t: 'Building Area Total', m: 3000 },
            { t: 'Building Area Units', m: 'Square Feet' },
            { t: 'Fireplace Features', m: 'Gas' },
            { t: 'Flooring', m: 'Hardwood, Carpet' },
            { t: 'Full Baths', m: 2 },
            { t: 'Irrigation Water Rights', m: 'Yes' },
            { t: 'Half Baths', m: 1 },
            { t: 'Home Warranty', m: 'Yes' },
            { t: 'Living Area Units', m: 'Square Feet' },
            { t: 'Lot Features', m: 'Fenced Yard' },
            { t: 'Main Level Bathrooms', m: 1 },
            { t: 'Main Level Bedrooms', m: 1 },
            { t: 'New Construction', m: 'No' },
            { t: 'Number Of Buildings', m: 1 },
            { t: 'Numberof Stories', m: 2 },
            { t: 'Occupant Type', m: 'Owner' },
            { t: 'Originating System Name', m: 'MLS' },
            { t: 'Patio And Porch Features', m: 'Deck, Patio' },
            { t: 'Property Attached', m: 'No' },
            { t: 'Property Condition', m: 'Good' },
            { t: 'Roof', m: 'Asphalt Shingle' },
            { t: 'Rooms Total', m: 8 },
            { t: 'Senior Community', m: 'No' }
          ]
        },
        Surrounding: {
          n: 'Surrounding',
          v: [
            { n: 'Community Features', v: 'Park, Pool' },
            { n: 'Exterior Features', v: 'Deck, Patio' },
            { n: 'Elementary School', v: 'Nearby' },
            { n: 'Middle Or Junior School', v: 'Within 2 km' },
            { n: 'High School', v: 'Within 1 km' },
            { n: 'Zoning', v: 'R1' }
          ]
        },
        Other: {
          n: 'Other',
          v: [
            { n: 'Association Fee Frequency', v: 'Monthly' },
            { n: 'Internet Automated Valuation Display', v: 'Yes' },
            { n: 'Internet Entire Listing Display', v: 'Yes' }
          ]
        },
        Land: {
          n: 'Land',
          v: [
            { t: 'Lot Size Area', m: 5000 }
          ]
        },
        Parking: {
          n: 'Parking',
          v: [
            { t: 'Parking Features', m: 'Heated, Insulated' }
          ]
        },
        _orig: {
          rmPsn: 'Immediate',
          stp: 'Residential',
          status: 'A',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)

    it 'should correctly translate TRB source property data', ->
      # 输入参数
      input = {
        src: 'TRB',
        id: 'TRB123456',
        trbtp: [],
        psn: new Date('2023-01-01T09:00:00.000Z'),
        rms: [ { t: 'Bedroom', l: 'Master' }, { t: 'Bathroom', l: 'Full' } ],
        rmPsn: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        status: 'A',
        ptp: 'Single Family',
        pstyl: 'Detached',
        rtp: 'Freehold',
        rgdr: 'Yes',
        AssociationAmenities: 'Clubhouse, Pool',
        ArchitecturalStyle: 'Modern',
        FireplaceYN: 'Y',
        HeatingYN: 'Y',
        MainLevelBathrooms: 1,
        MainLevelBedrooms: 1,
        PrivateEntranceYN: 'Y',
        PropertyAttachedYN: 'N',
        PropertyFeatures: 'Security System',
        RoomsAboveGrade: 6,
        RoomsTotal: 8,
        BuildingAreaTotal: 3000,
        BuildingAreaUnits: 'Square Feet',
        SeniorCommunityYN: 'N',
        FireplaceFeatures: 'Gas',
        FireplacesTotal: 2,
        Roof: 'Asphalt Shingle',
        ExteriorFeatures: 'Deck, Patio',
        HeatSource: 'Natural Gas',
        HeatType: 'Forced Air',
        Water: 'Municipal',
        Locker: 'Yes',
        NewConstructionYN: 'N',
        LaundryLevel: 'Main Floor',
        OtherStructures: 'Shed',
        DenFamilyroomYN: 'Y',
        DepositRequired: 'First and Last',
        EmploymentLetterYN: 'Y',
        EnergyCertificate: 'Yes',
        Inclusions: 'Dishwasher, Refrigerator, Stove',
        InteriorFeatures: 'Central Vacuum',
        InternetEntireListingDisplayYN: 'Y',
        LaundryFeatures: 'In Suite',
        PaymentFrequency: 'Monthly',
        PaymentMethod: 'Pre-Authorized Debit',
        ReferencesRequiredYN: 'Y',
        SecurityFeatures: 'Smoke Detectors',
        Sewer: 'Municipal',
        AccessibilityFeatures: 'Wheelchair Access',
        InsuranceExpense: 1200,
        ElectricYNA: 'Yes',
        mfeeInc: ['Hydro Included', 'Water Included']
      }
      
      # 期望的输出
      expected = {
        src: 'TRB',
        id: 'TRB123456',
        trbtp: [],
        psn: '2023-01-01',
        rms: [
          { t: 'Bedroom', l: 'Master' },
          { t: 'Bathroom', l: 'Full' }
        ],
        rmPsn: 'Immediate',
        rmPsn_en: 'Immediate',
        bsmt: [ 'Finished' ],
        stp: 'Residential',
        stp_en: 'Residential',
        status: 'A',
        status_en: 'A',
        ptp: 'Single Family',
        ptp_en: 'Single Family',
        pstyl: 'Detached',
        pstyl_en: 'Detached',
        rtp: 'Freehold',
        rtp_en: 'Freehold',
        rgdr: 'Yes',
        rgdr_en: 'Yes',
        AssociationAmenities: 'Clubhouse, Pool',
        ArchitecturalStyle: 'Modern',
        FireplaceYN: 'Y',
        HeatingYN: 'Y',
        MainLevelBathrooms: 1,
        MainLevelBedrooms: 1,
        PrivateEntranceYN: 'Y',
        PropertyAttachedYN: 'N',
        PropertyFeatures: 'Security System',
        RoomsAboveGrade: 6,
        RoomsTotal: 8,
        BuildingAreaTotal: 3000,
        BuildingAreaUnits: 'Square Feet',
        SeniorCommunityYN: 'N',
        FireplaceFeatures: 'Gas',
        FireplacesTotal: 2,
        Roof: 'Asphalt Shingle',
        ExteriorFeatures: 'Deck, Patio',
        HeatSource: 'Natural Gas',
        HeatType: 'Forced Air',
        Water: 'Municipal',
        Locker: 'Yes',
        NewConstructionYN: 'N',
        LaundryLevel: 'Main Floor',
        OtherStructures: 'Shed',
        DenFamilyroomYN: 'Y',
        DepositRequired: 'First and Last',
        EmploymentLetterYN: 'Y',
        EnergyCertificate: 'Yes',
        Inclusions: 'Dishwasher, Refrigerator, Stove',
        InteriorFeatures: 'Central Vacuum',
        InternetEntireListingDisplayYN: 'Y',
        LaundryFeatures: 'In Suite',
        PaymentFrequency: 'Monthly',
        PaymentMethod: 'Pre-Authorized Debit',
        ReferencesRequiredYN: 'Y',
        SecurityFeatures: 'Smoke Detectors',
        Sewer: 'Municipal',
        AccessibilityFeatures: 'Wheelchair Access',
        InsuranceExpense: 1200,
        ElectricYNA: 'Yes',
        mfeeInc: ['Hydro Included', 'Water Included'],
        Building: {
          n: 'Building',
          v: [
            { t: 'Association Amenities', m: 'Clubhouse, Pool' },
            { t: 'Architectural Style', m: 'Modern' },
            { t: 'Fireplace', m: 'Yes' },
            { t: 'Heating', m: 'Yes' },
            { t: 'Main Level Bathrooms', m: 1 },
            { t: 'Main Level Bedrooms', m: 1 },
            { t: 'Private Entrance', m: 'Yes' },
            { t: 'Property Attached', m: 'No' },
            { t: 'Property Features', m: 'Security System' },
            { t: 'Rooms Above Grade', m: 6 },
            { t: 'Rooms Total', m: 8 },
            { t: 'Building Area Total', m: 3000 },
            { t: 'Building Area Units', m: 'Square Feet' },
            { t: 'Senior Community', m: 'No' },
            { t: 'Fireplace Features', m: 'Gas' },
            { t: 'Fireplaces Total', m: 2 },
            { t: 'Roof', m: 'Asphalt Shingle' },
            { t: 'Exterior Features', m: 'Deck, Patio' },
            { t: 'Heat Source', m: 'Natural Gas' },
            { t: 'Heat Type', m: 'Forced Air' },
            { t: 'Water', m: 'Municipal' },
            { t: 'Locker', m: 'Yes' },
            { t: 'New Construction', m: 'No' },
            { t: 'Laundry Level', m: 'Main Floor' },
            { t: 'Other Structures', m: 'Shed' }
          ]
        },
        Other: {
          n: 'Other',
          v: [
            { n: 'Den Familyroom', v: 'Yes' },
            { n: 'Deposit Required', v: 'First and Last' },
            { n: 'Employment Letter', v: 'Yes' },
            { n: 'Energy Certificate', v: 'Yes' },
            # { n: 'Inclusions', v: 'Dishwasher, Refrigerator, Stove' },
            { n: 'Interior Features', v: 'Central Vacuum' },
            { n: 'Internet Entire Listing Display', v: 'Yes' },
            { n: 'Laundry Features', v: 'In Suite' },
            { n: 'Payment Frequency', v: 'Monthly' },
            { n: 'Payment Method', v: 'Pre-Authorized Debit' },
            { n: 'References Required', v: 'Yes' },
            { n: 'Security Features', v: 'Smoke Detectors' },
            { n: 'Sewer', v: 'Municipal' },
            { n: 'Accessibility Features', v: 'Wheelchair Access' },
            { n: 'Insurance Expense', v: 1200 }
          ]
        },
        Utilities: {
          n: 'Utilities',
          v: [
            { t: 'Electric YNA', m: 'Yes' }
          ]
        },
        MaintenanceFeeType: {
          n: 'Maintenance Fee Type',
          v: ['Hydro', 'Water']
        },
        _orig: {
          rmPsn: 'Immediate',
          stp: 'Residential',
          status: 'A',
          ptp: 'Single Family',
          pstyl: 'Detached',
          rtp: 'Freehold',
          rgdr: 'Yes'
        }
      }
      
      # 执行测试
      result = propertiesTranslate.translate_rmprop_detail({locale: 'en', transDDF: true}, input)
      
      # 验证结果
      result.should.deepEqual(expected)
    