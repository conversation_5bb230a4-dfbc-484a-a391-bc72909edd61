# NOTE: requires kafka cfg for propSqft!!!
# coffee ./setup.coffee
# ./mate4.sh compile
# ./test.sh -f libapp/propSqft.js
# NOTE: put INCLUDE inside describe before, otherwise will report error

helpers = null
debug = null
propSqft = null
should = require('should')

 # TODO: unit test for these cases
  # 123
  # Ph8: 130
  # Ph 1: 103
  # -ph01: 70
  # Ph 02: 68
  # Ph203: 46
  # Lph3: 46
  # Lph12: 30
  # Th9: 27
  # Lph 05: 16
  # Th 1: 15
  # Sph02: 9
  # Ph-01: 8
  # Gph03: 7
  # Ph1004: 6
  # Rg10: 6  Roof Garden
  # 304b: 6; 12a, 507b, 407w, 3d, 618e, 3f, 3k, 12j
  # B406: 4; A9, N503, M6, G03, G20, S710, L10, F4
  # Lp3: 3
  # 4: 3
  # S-515: 2
  # Lph-G: 2
  # Tph10: 2
  # L 18: 2
  # 0: 2
  # Ph B: 2
  # P H 9: 2
  # Lot 19: 2
  # Ph2-04: 2
  # Ph-B: 2
  # PH E: 2
testProps = [
  {
    prop:{
      m: "***Beautiful  South Facing 1+1 With 628 Sqft Private Terrace*** Perfect For Entertaining, Having Bbqs, And Even A Hot Tub! It's Like Having Your Own Backyard In The Middle Of The City! Bright And Spacious Unit Features Builders Upgraded: Kitchen W/ Centre Island, 9 Ft Ceilings, Engineered Hardwood & Washroom! ***Prime Location At Yonge & Eglinton***Amenities Include: 24Hr Concierge, Rooftop Pool, Hot Tub, Bar/Lounge, Gym, Meeting/Party Room, Games Room. All Appliances, Custom Window Coverings W/ Remote.  All Light Fixtures *** Owned Locker ***  Walk To Subway, Shops, Restaurants & Entertainment!",
      _id:'TRBC5883647',
      sqft: "500-599",
      sqft1: 500,
      sqft2: 599,
    },
  },
  {
    prop:{
      m:"***Beautiful South Facing 1+1 With 628 Sqft Private Terrace*** Perfect For Entertaining, Having Bbqs, And Even A Hot Tub! It's Like Having Your Own Backyard In The Middle Of The City! Bright And Spacious Unit Features Builders Upgraded: Kitchen W/ Centre Island, 9 Ft Ceilings, Engineered Hardwood & Washroom! ***Prime Location At Yonge & Eglinton***Amenities Include: 24Hr Concierge, Rooftop Pool, Hot Tub, Bar/Lounge, Gym, Meeting/Party Room, Games Room. All Appliances, Custom Window Coverings W/ Remote. All Light Fixtures *** Owned Locker *** Walk To Subway, Shops, Restaurants & Entertainment!",
      _id:'TRBC5930557',
      sqft: "500-599",
      sqft1: 500,
      sqft2: 599,
    },
  },
  {
    prop:{
      _id:'TRBC5746000',
      m:"The \"Beyond Luxurious Skygarden House\" In Dt Core!Unique Oversized Terrace+Balc Corner Unit(2Beds+Den)Boasts: 10.5Ft Ceil,Flr To Ceil Wdws In All Rms,Top Notch/House Size Appliances(30\"),Quartz Counters, Waterfall Island,Water Purifier,Rh Lightings, Wall Panels W/B/I Cabinets, Wine Cellar,Wallpapers,Oversized Masterbedrms..Most Of All,Fulfill Your Backyard Oasis Dreams On 1082Sqft Terrace.Entertain Your Loved Ones In The Lush Garden!P1 Parking Next To Elev 2 Adjacent Premium Lockers.Gas Line.World-Class Amen:Rooftop Terrace,Gym,Indoor Pool,Outdoor Dining,2 Level-Sky Lounge&Bar,Guest Suites..A Prime Dt Location-Steps To Financial District, St Lawrence Market,Union Station,Path,Subway,Google Hq",
      sqft: "1000-1199",
      sqft1: 1000,
      sqft2: 1199,
    }
  },
  {
    prop:{
      _id:'TRBC5869473',
      m:"Come Check Out This Super Spacious 1 Bedroom Plus Den In The Heart Of Downtown. The Unit Is Perfectly Laid Out With A Large 211 Sqft Terrace Perfect For Entertaining And Parking/Locker Combo. Desirable Well Managed Condo Located Steps To Iconic Toronto Locations. Well Connected To Activities, Shopping, Restaurants, Transit, Lake, Parks And Trails. Plus Conveniently Located Across The Street From Loblaws, Lcbo, Shoppers, Starbucks/Tim Hortons And Much More. S/S Fridge, Stove, Microwave Hood, Dishwasher. Washer & Dryer. Window Coverings, Elfs. Amenities: Gym, Sauna, Yoga, Party Room, Media Rm, Games Rm, Meeting Rm, Rooftop Terr, Concierge, And Visitor Parking. *Virtual Tour*",
      sqft: "600-699",
      sqft1: 600,
      sqft2: 699,
    }
  },
  {
    prop:{
      _id:'TRBC5804209',
      "m" : "*Beautiful, Bright And Spacious South East Corner Suite At King & John *  686 Sqft,  Can B Convert To 2 Bed Room Plus Den *  9 Ft Ceilings *  Flr To Ceiling Windows *  Modern Kitchen With Top Notch Appliances * Amazing Facilities : 24Hr Bldg Reception, Gym, Spa, Outdoor Lounge Area With Bbq * Excellent Location:  Walking Distance To Restaurants, Bars, Tiff, Cn Tower, Subway , Roger's Centre. Air Canada Center, Lake... * Stainless Steel Fridge, B/I Dishwasher, Stove, Microwave Open W/Exhaust Hood Fan, Washer/Dryer, Blinds * Laminate Floors Thru Out * South East Exposure. Juliet Balcony *",
      sqft: "600-699",
      sqft1: 600,
      sqft2: 699,
      # rmSqft: 686,
    }
  },
  {
    prop:{
      _id:'TRBW5804211',
      "m" : "Ready To Move In, This Extremely Well Kept Apartment On 7th Floor Is Offering Spacious 2 Bedrooms, 2 Bathrooms, Very Bright And Sunny Living Space With Oversized Balcony Combined With Dining Room For Easy Entertaining. Featuring Wall To Wall Window. Fantastic Central Location, Close To Cooksville Go, Trillium Hospital, Square One City Centre, Fenced Private Grounds W/ Playground,Outdoor Pool,Tennis Court,Bbq Pitts. Ample Storage Throughout, No Carpet! Fridge, Stove, Dishwasher, Front Fixtures & Window Coverings Front Load Washer And Dryer. Wall Ac Unit. All Electrical Light Fixtures. Excluded: Microwave And Sheers In The Dining Room.",
      sqft: "1000-1199",
      sqft1: 1000,
      sqft2: 1199,
      # rmSqft: 1145,
    }
  },
  {
    prop:{
      _id:'TRBW5804207',
      "m" : "A Must See 4 Bed, 3 Bath Semi-Detached In Highly Desirable West Oak Trails. Walking Distance To Oakville Trafalgar Memorial Hospital, Top Rated Schools, Parks, Restaurants And A Lot More. Hardwood Floor On Main, Bright, And Open Concept. Sun Filled Kitchen Combine With Dining. Spacious Primary Bed With 4 Pc Ensuite. Partially Finished Basement Easily Converted To A Family Recreation Area. Stove, Rangehood, Refrigerator, B/I Dishwasher, Washer/Dryer, All The Existing Window Covering",
      # sqft: "1000-1199",
      # sqft1: 1000,
      # sqft2: 1199,
      # rmSqft: 1750,
    }
  },
  {
    prop:{
      _id:'TRBW5672417',
      "m" : "Welcome To 6 Turtle Lake Dr Acton! This Beautiful Custom Built Home Backing Onto Greenspace With Almost 8000Sqft Of Living Space On A Treed Lot Of 1.80 Acres Of Stunning Westerly Views Of Sunsets Over Hole 7 At Blue Springs Golf Course, Offers 7 Bedrooms, 6 Bathrooms, 2 Laundry Rooms And 3 Kitchens. The Main Floor Primary Bedroom Has A Large Walk-In Closet And Ensuite. Making Your Way Through The Main Floor You Find An Office, Great Room With Lots Of Natural Light, Eat In Kitchen With Custom Cabinetry, Separate Dining Room, Living Room And Is Completed With Another 3Pc Bathroom. The Enclosed Breezeway Leads To Inside Entry From The Triple Car Garage And A 1 Bedroom Accessory  Apartment. The Upper Level Offers 4 Spacious Bedrooms, Laundry Room, Games Loft And 4Pc Bathroom. The Lower Level Has Another 1 Bedroom Apartment With Walk Out, Kitchen, A 4Pc Bathroom And Another 2Pc. Outside Seating Area With Natural Gas Hook-Up To Enjoy The Views! This Home Is Sure To Impress! Carbon Monoxide Detector, Central Vac(As Is), 2 Dishwashers, 2 Dryers, Gdo Opener, Gas Stove, 2 Hwt(Owned), Range Hood, 2 Fridges, Smoke Detector,2 Stoves,2 Washers, Window Coverings, Wine Cooler. Excl:Brass Maple Leaf On Front Of The House",
      sqft: "5000+",
      sqft1: 5000,
      # sqft2: 1199,
      # rmSqft: 1750,
    }
  },
  {
    prop:{
      _id:'TRBN7208442',
      m:'* Meticulously Maintained Home By Owner Of 27 Years * Situated On A Quiet St In The Heart Of Prestigious Unionville * Walking Distance To Park, \
      Unionville High, Coledale Public, St. Justin Martyr * Major Renovations Included: All Windows (2022), Modern Kitchen, Bathrooms, Hardwood Floor On Main, \
      Patterned Concrete Driveway, Garage Doors With Aluminum Wrap, Wooden Deck & Fence, Basement Finished W/800 Sf Of Living Space, Hardwood Floor,\ Large Bedroom, \
      3Pc Washroom & Large Storage Areas * Stainless Steel Fridge/Stove (2022), Stainless Steel Washer/Dryer (2023), Built-In Dishwasher, Furnace (Owned 2022), \
      Hot Water Tank (Owned 2023), All Light Fixtures, California Shutters, Garage Door Openers & Remotes.',
    }
  }
]
describe 'propSqft tests start',->
  before (done) ->
    @timeout(3000000)
    helpers = require('../00_common/helpers')
    # request = helpers.getServer()
    debug = helpers.DEBUG()

    # propSqft = require('../../built/libapp/propSqft')
    propSqft = helpers.INCLUDE('libapp.propSqft')

    done()
    return
  describe 'split Level Ending',->
    tests = [
      {
        input: '123',
        expected: {level:'1',ending:'23',levelNum:1, unit: '123'}
      },
      {
        input: '1203',
        expected: {level:'12',ending:'03',levelNum:12, unit: '1203'}
      },
      {
        input: '701',
        expected: {level:'7',ending:'01',levelNum:7, unit: '701'}
      },
      {
        input: '45a',
        expected: {level:'', ending:'45A',levelNum: undefined, unit: '45A'}
      },
      {
        input: 'Ph8',
        expected: {level:'PH',ending:'8',levelNum: undefined, unit: 'PH8'}
      },
      {
        input: 'Ph 1',
        expected: {level:'PH',ending:'1',levelNum: undefined, unit: 'PH1'}
      },
      {
        input: '2nd Flr',
        expected: {level:'',ending:'', unit: '2NDFLR'}
      },
      #TODO:
      {
        input: '-ph01',
        expected: {level:'PH',ending:'01',levelNum: undefined, unit: '-PH01'}
      },
      {
        input: 'Ph 02',
        expected: {level:'PH',ending:'02',levelNum: undefined, unit: 'PH02'}
      },
      
      {
        input: 'Ph203',
        expected: {level:'PH2',ending:'03',levelNum: undefined, unit: 'PH203'}
      },
      {
        input: 'Lph3',
        expected: {level:'LPH',ending:'3',levelNum: undefined, unit: 'LPH3'}
      },
      {
        input: 'Lph12',
        expected: {level:'LPH',ending:'12',levelNum: undefined, unit: 'LPH12'}
      },
      {
        input: 'Th9',
        expected: {level:'TH',ending:'9',levelNum: undefined, unit: 'TH9'}
      },
      {
        input: 'Lph 05',
        expected: {level:'LPH',ending:'05',levelNum: undefined, unit: 'LPH05'}
      },
      {
        input: 'Th 1',
        expected: {level:'TH',ending:'1',levelNum: undefined, unit: 'TH1'}
      },
      {
        input: 'Sph02',
        expected: {level:'SPH',ending:'02',levelNum: undefined, unit: 'SPH02'}
      },
      {
        input: 'Ph-01',
        expected: {level:'PH',ending:'01',levelNum: undefined, unit: 'PH-01'}
      },
      {
        input: 'Gph03',
        expected: {level:'GPH',ending:'03',levelNum: undefined, unit: 'GPH03'}
      },
      {
        input: 'Ph1004',
        expected: {level:'PH10',ending:'04',levelNum: 10, unit: 'PH1004'}
      },
      {
        input: 'Rg10',
        expected: {level:'RG',ending:'10',levelNum: undefined, unit: 'RG10'}
      },
      #    # 304b: 6; 12a, 507b, 407w, 3d, 618e, 3f, 3k, 12j
      {
        input: '304b',
        expected: {level:'3',ending:'04B',levelNum: 3, unit: '304B'}
      },
      {
        input: '6',
        expected: {level:'', ending:'6', levelNum: undefined, unit: '6'}
      },
      {
        input: '12a',
        expected: {level:'', ending:'12A', levelNum: undefined, unit: '12A'}
      },
      {
        input: '507b',
        expected: {level:'5', ending:'07B', levelNum: 5, unit: '507B'}
      },
      {
        input: '3f',
        expected: {level:'', ending:'3F', levelNum: undefined, unit: '3F'}
      },
      #B406: 4; A9, N503, M6, G03, G20, S710, L10, F4??
      {
        input: 'B406',
        expected: {level:'', ending:'B406', levelNum: undefined, unit: 'B406'}
      },
      {
        input: 'A9',
        expected: {level:'', ending:'A9', levelNum: undefined, unit: 'A9'}
      },
      {
        input: 'A15',
        expected: {level:'', ending:'A15', levelNum: undefined, unit: 'A15'}
      }
      {
        input: 'G03',
        expected: {level:'', ending:'G03', levelNum: undefined, unit: 'G03'}
      },
      # Lp3: 3
      {
        input: 'Lp3',
        expected: {level:'LP', ending:'3', levelNum: undefined, unit: 'LP3'}
      },
      # 4: 3
      # S-515: 2
      {
        input: 'S-515',
        expected: {level:'S', ending:'515', levelNum: undefined, unit: 'S-515'}
      },
      # Lph-G: 2
      {
        input: 'Lph-G',
        expected: {level:'LPH', ending:'G', unit: 'LPH-G'}
      },
      # Tph10: 2
      {
        input: 'Tph10',
        expected: {level:'TPH', ending:'10', levelNum: undefined , unit: 'TPH10'}
      },
      # L 18: 2
      {
        input: 'L 18',
        expected: {level:'L', ending:'18', levelNum: undefined , unit: 'L18'}
      },
      # 0: 2
      {
        input: '0',
        expected: {level:'', ending:'0', levelNum: undefined , unit: '0'}
      },
      # Ph B: 2
      {
        input: 'Ph B',
        expected: {level:'PH', ending:'B', unit: 'PHB'}
      },
      # P H 9: 2
      {
        input: 'Ph 9',
        expected: {level:'PH', ending:'9', levelNum: undefined , unit: 'PH9'}
      },
      # Lot 19: 2
      {
        input: 'Lot 19',
        expected: {level:'LOT', ending:'19', levelNum: undefined , unit: 'LOT19'}
      },
      # Ph2-04: 2
      {
        input: 'Ph2-04',
        expected: {level:'PH2', ending:'04', levelNum: undefined , unit: 'PH2-04'}
      },
      # Ph-B: 2
      {
        input: 'Ph-B ',
        expected: {level:'PH', ending:'B',  unit: 'PH-B'}
      },
      # PH E: 2
      {
        input: 'PH E',
        expected: {level:'PH', ending:'E',  unit: 'PHE'}
      },
    ]
    tests.forEach (test)->
      it "unt ending #{test.input} should be:#{JSON.stringify(test.expected)}" ,(done)->
        ret = propSqft.splitLevelEnding(test.input)
        console.log(ret)
        should.deepEqual(test.expected, ret)
        done()

  describe 'computeUntStorey',->
    tests = [
      {
        unt: '1204',
        expected: null
      },
      {
        unt: '1203',
        numLvls: [1,2,3,5,6,7,8,9,10,11,12],
        expected: 11
      },
      {
        unt: '45a',
        numLvls: [1,2,3,5,6,7,8,9,10,11,12],
        expected: null
      },
      {
        unt: 'Ph8',
        numLvls: [1,2,3,5,6,7,8,9,10,11,12,'PH'],
        expected: 12
      },
      {
        unt: 'Lph3',
        numLvls: [1,2,3,5,6,7,8,'LPH'],
        expected: 8
      }
    ]
    tests.forEach (test)->
      it "level of unt ending #{test.unt} should be:#{JSON.stringify(test.expected)}" ,(done)->
        ret = propSqft.computeUntStorey(test.unt,test.numLvls)
        console.log(ret)
        should.deepEqual(test.expected, ret)
        done()
  describe 'test check is terrance',->
    tests = [
      {tailing:"Sf + 240 Sf Terrace",expected:false},
      {tailing:"Sf Terrace",expected:true},
      
      {tailing:"Sqft Plus 272 Sqft Terrace",expected:false},
      {tailing:"Sqft Terrace",expected:true},

      {tailing:"With 628 Sqft Private Terrace***",expected:false},
      {tailing:"Sqft Private Terrace***",expected:true},
     
      {tailing:"Sqft Terrace Perfect For",expected:true},
      {tailing:" With A Large 211 Sqft Terrace Perfect For",expected:false},
    ]
    tests.forEach (test)->
      it "test tailing is terrance:#{test.tailing}",(done)->
        ret = propSqft.checkIsTerrace('',test.tailing)
        should.equal(ret,test.expected)
        done()
  describe 'testing getPossibleNumbersWithHeadingTailingFromRemarks',->
    testsResults = [
      {
        matches:{
          length:5
        }
        wrong_rmSqft: 628,
        expected:{
          emptySqft:true
        }
      },
      {
        matches:{
          length:5,
        },
        wrong_rmSqft: 628,
        expected:{
          emptySqft:true
        }
      },
      {
        matches:{
          length:8,
        },
        wrong_rmSqft: 1082,
        expected:{
          emptySqft:true
        }
      },
      {
        matches:{
          length:2,
        },
        # wrong_rmSqft: 650,
        expected:{
          emptySqft:true
        }
      },
      {
        matches:{
          length:4,
        },
        expected:{
          emptySqft:false,
          rmSqft: 686,
        }
      },
      {
        matches:{
          length:3,
        },
        expected:{
          emptySqft:true,
          # rmSqft: 1145,
        }
      },
      {
        matches:{
          length:3,
        },
        expected:{
          emptySqft:true,
          # rmSqft: 1750,
        }
      },
      {
        matches:{
          length:22,
        },
        expected:{
          emptySqft:false,
          rmSqft: 8000,
        }
      }
      {
        matches:{
          length:8,
        },
        expected:{
          emptySqft:true,
        }
      },
    ]
    testsResults.forEach (test,index)->
      prop = testProps[index].prop
      it "prop sqfts #{prop._id}: #{prop.sqft}, estimate sqft should be #{test.expected.rmSqft}" ,(done)->
        {tailings,headings,allNumbers,matchedStrings} = propSqft.getPossibleNumbersWithHeadingTailingFromRemarks(prop.m)
        # console.log(allNumbers)
        should.equal(allNumbers.length,test.matches.length)

        ret = propSqft.getSqftsFromMatchedNumbersWithRange(prop,allNumbers,headings,tailings)
        {sqfts, estSqfts, type, sqft, estSqft} = ret
        # console.log ret
        if test.expected.emptySqft
          sqfts.should.be.empty()

        ret2 = propSqft.findSqftFromProp(prop)
        # console.log '=====',ret2
        should.equal(test.matches.length,ret2.matchedStrings.length)
        should.equal(test.matches.length,ret2.numbers.length)
        if test.expected.emptySqft
          should.not.exists(ret2.sqfts)
          should.not.exists(ret2.estSqfts)
        else
          recordSqft = ret2.sqft or ret2.estSqft
          should.equal(test.expected.rmSqft,recordSqft)
        # should.equal(test.expected.*, ret)
        done()

  describe 'testing isAssignment',->
    tests = [
      {
        prop: {
          AssignmentYN: 'Y'
        },
        expected: true,
        description: 'should return true when AssignmentYN is Y'
      },
      {
        prop: {
          AssignmentYN: 'N'
        },
        expected: false,
        description: 'should return false when AssignmentYN is N'
      },
      {
        prop: {
          AssignmentYN: ''
        }
        expected: false,
        description: 'should return false when AssignmentYN is empty'
      },
      {
        prop: {
          m: 'This is an assignment property'
        },
        expected: true,
        description: 'should return true when m contains assignment'
      },
      {
        prop: {
          m: 'This is an ASSIGNMENT property'
        },
        expected: true,
        description: 'should return true when m contains ASSIGNMENT (case insensitive)'
      },
      {
        prop: {
          m: 'This is a regular property'
        },
        expected: false,
        description: 'should return false when m does not contain assignment'
      },
      {
        prop: {
          m: null
        },
        expected: false,
        description: 'should return false when m is null'
      },
      {
        prop: {},
        expected: false,
        description: 'should return false when prop has no AssignmentYN or m'
      }
    ]
    tests.forEach (test)->
      it test.description, (done)->
        ret = propSqft.isAssignment(test.prop)
        should.equal(ret, test.expected)
        done()