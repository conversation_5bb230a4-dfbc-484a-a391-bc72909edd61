# coffee ./setup.coffee
# ./mate4.sh compile
# ./test.sh -f libapp/propListSort.js -b
should = require('should')
{calculatePropertyScore} = require('../../built/libapp/propElasticSearch')
debug = null

calcAddressScore = (addr,prop)->
  score = 0
  searchAddr = prop.searchAddr.toLowerCase()
  tmpAddr = addr.toLowerCase()
  reg = new RegExp('^\\w+\\s'+tmpAddr)
  if searchAddr.startsWith(tmpAddr)
    score += 100
  else if reg.test(searchAddr)
    score += 90
  else
    score += 80
  if prop.ptype2.includes('Locker')
    score -= 25
  if prop.ptype2.includes('Parking')
    score -= 30
  return score * 500

calcESScore = (addr,prop)->
  score = 0
  score += calcAddressScore(addr,prop)
  score += calculatePropertyScore(prop)
  return score

sortProps = (addr,props)->
  for prop in props
    prop.score = calcESScore(addr,prop)
  
  # 按照status升序(A优先)、saletp降序(Sale优先)、score降序的顺序对props进行排序
  props.sort (a,b)->
    # 首先比较status (A < U)
    if a.status isnt b.status
      return if a.status is 'A' then -1 else 1
      
    # status相同时比较saletp (Sale > Lease)
    if a.saletp[0] isnt b.saletp[0]
      return if a.saletp[0] is 'Sale' then -1 else 1
      
    # status和saletp都相同时按score降序
    if a.score isnt b.score
      return b.score - a.score
    
    # status,saletp,score相同时按onD降序
    return b.onD - a.onD
    
  return props

INPUT_PROPS = [
  {
    _id: 'TEST_2',
    searchAddr: '32 Nottingham Dr',
    status: 'U',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_22',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'r',
    ptype2: [ 'Locker' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_1',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_15',
    searchAddr: '232 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_20',
    searchAddr: '32 Nottingham Dr',
    status: 'U',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250401
  },
  {
    _id: 'TEST_23',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'r',
    ptype2: [ 'Parking' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_8',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment', 'Parking' ],
    src: 'CAR',
    onD: 20250601
  },
  {
    _id: 'TEST_17',
    searchAddr: '232 Nottingham Dr',
    status: 'U',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_24',
    searchAddr: '303 32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'r',
    unt: '303',
    ptype2: [ 'Locker' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_6',
    searchAddr: '32 Nottingham Dr',
    status: 'U',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601,
    merged: 'TEST_2'
  },
  {
    _id: 'TEST_18',
    searchAddr: '232 Nottingham Dr',
    status: 'U',
    saletp: [ 'Sale' ],
    ptype: 'r',
    ptype2: [ 'Detached' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_5',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601,
    merged: 'TEST_1'
  },
  {
    _id: 'TEST_12',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'b',
    ptype2: [ 'Apartment' ],
    src: 'DDF',
    onD: 20250601
  },
  {
    _id: 'TEST_11',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_14',
    searchAddr: '101 32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_19',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250401
  },
  {
    _id: 'TEST_10',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'DDF',
    onD: 20250601
  },
  {
    _id: 'TEST_7',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment', 'Locker' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_16',
    searchAddr: '232 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'r',
    ptype2: [ 'Detached' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_4',
    searchAddr: '32 Nottingham Dr',
    status: 'U',
    saletp: [ 'Lease' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_21',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Lease' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250401
  },
  {
    _id: 'TEST_3',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Lease' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment' ],
    src: 'TRB',
    onD: 20250601
  },
  {
    _id: 'TEST_13',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    ptype: 'o',
    ptype2: [ 'Apartment' ],
    src: 'DDF',
    onD: 20250601
  },
  {
    _id: 'TEST_9',
    searchAddr: '32 Nottingham Dr',
    status: 'A',
    saletp: [ 'Sale' ],
    unt: '101',
    ptype: 'r',
    ptype2: [ 'Apartment', 'Parking', 'Locker' ],
    src: 'TRB',
    onD: 20250601
  }
]

describe 'Properties ES Query List Sort Test ',->
  before (done) ->
    @timeout(300000)
    done()

  describe 'Test Prop List Sort',->
    it 'test input addr 32 Nottingham Dr', ()->
      inputAddr = '32 Nottingham Dr'
      expectedIds = ['TEST_1', 'TEST_19', 'TEST_11', 'TEST_10', 'TEST_12', 'TEST_13', 'TEST_5', \
      'TEST_14', 'TEST_15', 'TEST_16', 'TEST_22', 'TEST_7', 'TEST_8', 'TEST_23', 'TEST_24', \
      'TEST_9', 'TEST_3', 'TEST_21', 'TEST_2', 'TEST_20', 'TEST_6', 'TEST_17', 'TEST_18', 'TEST_4']
      result = sortProps(inputAddr,INPUT_PROPS)
      for id,idx in expectedIds
        result[idx]._id.should.be.equal(id)
      return
