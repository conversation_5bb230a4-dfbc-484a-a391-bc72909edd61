# ./unitTest/test.sh -f propertyImport/01_propertyImport.js
###
mongodump --db listingtest3 -c geo_cache  --gzip
mongodump --db listingtest3 -c properties_expected  --gzip
mongodump --db listingtest3 -c properties  --gzip
mongodump --db rnitest -c mls_bcre_manual_records  --gzip
mongodump --db rnitest -c mls_crea_ddf_records  --gzip
mongodump --db rnitest -c mls_treb_dta_records  --gzip
mongodump --db rnitest -c mls_treb_manual_records  --gzip
mongodump --db rnitest -c mls_treb_master_records  --gzip
./unitTest/test.sh -f propertyImport/01_propertyImport.js
###


should = require('should')
helpers = require('../00_common/helpers')
debugHelper = require('../../built/lib/debug')
{inputToDateNum} = require '../../built/lib/helpers_date'
debug = debugHelper.getDebugger(3)
MSG_STRINGS = helpers.MSG_STRINGS
saveToMaster = null
{deletDateFields} = require('./propertyCommon')
GeoCoder = null
#deep compare object
NO_REAL_GEOCODING = false
request = null
Cols = {}
gPropertiesNew = null
gOldProperties=null
gProperties =  null
gGeoCache =null
gTrebRecords = null
gBcreManRecords = null
gBcreRecords = null
gDDFRecords = null
gRabhRecords = null
gCrebRecords = null
gResoRecords = null
gDDFResoRecords = null
gBcreResoRecords = null
gTransitStop =  null
gSchool = null
gCensus = null
gCensus2016 = null
gCensus2016ReversKeys = null
gProperties_expected = null
gBoundary = null
gProvCityCmty = null
gPropertiesImportLog = null
gTrebManualRecords=null
SqftsAggregate=null

gTransitCities = null # TransitStop.distinct 'city', {}

TYPE_COL_MAPPING = {}
#property->propertyNew test case.
#Properties mightbe Properties collection or propertiesnew collection
importProp = ({srcType,prop},cb)->
  return new Promise((resolve)->
  #targetProperties = if srcType is 'property' \
  #then gPropertiesNew else gProperties
    targetProperties = gProperties
    saveToMaster.convertAndSaveRecord {
      srcType,
      targetProperties,
      record:prop,
      TransitStop: gTransitStop,
      transitCities:gTransitCities
      School: gSchool,
      Boundary: gBoundary,
      Census2016: gCensus2016,
      Census2016ReversKeys: gCensus2016ReversKeys,
      # Census2021:'',
      Census: gCensus,
      TrebRecords:gTrebRecords,
      DDFRecords:gDDFRecords,
      BcreManualRecords:gBcreManRecords,
      PropertiesImportLog:gPropertiesImportLog,
      SqftsAggregate,
      GeoCoder,
      ProvCityCmty:gProvCityCmty
    },(err,old,newItem) ->
      if err
        debug.error 'importProp error:', err
        return reject(err)
      debug.debug 'importProp success, newItem._id:', newItem?._id
      return resolve({old,newItem})
  )

describe 'Property Import',->
  before (done) ->
    @timeout(300000)
    request = helpers.getServer()
    gPropertiesNew = helpers.COLLECTION('vow','propertiesNew')
    gProperties =  helpers.COLLECTION('vow','properties')
    gGeoCache = helpers.COLLECTION('vow','geo_cache')
    gTrebRecords = helpers.COLLECTION('rni','mls_treb_master_records')
    gTrebManualRecords = helpers.COLLECTION('rni','mls_treb_manual_records')
    gBcreManRecords = helpers.COLLECTION('rni','mls_bcre_manual_records')
    gBcreRecords = helpers.COLLECTION('rni','mls_bcre_records')
    gDDFRecords = helpers.COLLECTION('rni','mls_crea_ddf_records')
    gRabhRecords = helpers.COLLECTION('rni','mls_rahb_records')
    gCrebRecords = helpers.COLLECTION('rni','mls_creb_master_records')
    gOrebRecords = helpers.COLLECTION('rni','mls_oreb_master_records')
    gEdmRecords = helpers.COLLECTION('rni','mls_rae_master_records')
    gCarRecords = helpers.COLLECTION('rni','mls_car_master_records')
    gResoRecords = helpers.COLLECTION('rni','reso_treb_evow_merged')
    gDDFResoRecords = helpers.COLLECTION('rni','reso_crea_merged')
    gBcreResoRecords = helpers.COLLECTION('rni','bridge_bcre_merged')
    gTransitStop =  helpers.COLLECTION('vow','transit_stop')
    gSchool = helpers.COLLECTION('vow','sch_rm_merged')
    gCensus = helpers.COLLECTION('vow','census')
    gCensus2016 = helpers.COLLECTION('vow','census2016')
    gCensus2016ReversKeys = helpers.COLLECTION('vow','census2016ReversKeys')
    gProperties_expected = helpers.COLLECTION('vow','properties_expected')
    gBoundary= helpers.COLLECTION('vow','boundary')
    gProvCityCmty = helpers.COLLECTION('vow','prov_city_cmty')
    # gPropertiesNew.drop()
    gPropertiesImportLog =helpers.COLLECTION('tmp','properties_import_log')

    SqftsAggregate = helpers.COLLECTION('vow','sqfts_aggregate')

    GeocodingStat    = helpers.COLLECTION 'vow','geo_coding_stat'
    saveToMaster = helpers.INCLUDE 'libapp.saveToMaster'
    GeoCoder = helpers.MODEL 'GeoCoder'
    GeoCoder.noGeocoding(true)  # 不进行geocode操作
    TYPE_COL_MAPPING = {
      'treb':gTrebRecords
      'ddf':gDDFRecords
      'bcreMan':gBcreManRecords
      'bcre':gBcreRecords
      'trebMan':gTrebManualRecords
      'rahb':gRabhRecords
      'creb':gCrebRecords
      'oreb':gOrebRecords
      'edm':gEdmRecords
      'car':gCarRecords
      'reso':gResoRecords
      'creaReso':gDDFResoRecords
      'bcreReso':gBcreResoRecords
    }
    dbs = [
      { db: 'vow', table: 'geo_cache' },
      { db: 'vow', table: 'properties_expected' },
      { db: 'vow', table: 'properties' },
      { db: 'rni', table: 'mls_treb_master_records' },
      { db: 'rni', table: 'mls_treb_manual_records' },
      { db: 'rni', table: 'mls_treb_dta_records' },
      { db: 'rni', table: 'mls_crea_ddf_records' },
      { db: 'rni', table: 'mls_bcre_manual_records' },
      { db: 'rni', table: 'mls_bcre_records' },
      { db: 'rni', table: 'mls_rahb_records' },
      { db: 'rni', table: 'mls_creb_master_records' },
      { db: 'rni', table: 'mls_oreb_master_records' },
      { db: 'rni', table: 'mls_rae_master_records' },
      { db: 'rni', table: 'mls_car_master_records' },
      { db: 'rni', table: 'reso_treb_evow_merged' },
      { db: 'rni', table: 'reso_crea_merged' },
      { db: 'tmp', table: 'properties_import_log' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, () ->
        setTimeout(done, 2500)
    return

  #not used any more, migration finished.
  # describe 'should import property from property to propertyNew', ->
  #   tests = [
  #     {id:'DDF22715017',desc:'ddf has no ListingContractDate but ctrdt'},
  #     {id:'TRBW5172289',desc:'has video link'},
  #     {id:'BRER2329548',desc:'disp_addr:{$ne:null},daddr:null'},
  #     {id:'TRBN4847267',desc:'{lp=lpr>5000}, "ptype" : "Residential"'},
  #     {id:'TRBC5123876',desc:'{lp=lpr>30000}, "ptype" : "Residential"'},
  #     {id:'TRBN5177059',desc:'{5000<=lp=lpr<=30000}, "ptype" : "Residential",saletp:Lease'},
  #     {id:'TRBX5105104',desc:'lp lpr = 113000, sould delete lpr and set saletp=sale'},
  #     {id:'TRBW5054246',desc:'treb house for sale'},
  #     {id:'TRBN5054233',desc:'treb condo Lease '},
  #     {id:'TRBE5075593',desc:'trebMan Sold'},
  #     {id:'DDF22701445',desc:'ddf detached Sale'},
  #     {id:'DDF22688693',desc:'ddf apratement Lease'},
  #     {id:'BREH2152989',desc:'bcreMan apratement Sale'},
  #     {id:'BRER2036623',desc:'bcreMan Detached Sold'},
  #     {id:'BRER2100679',desc:'bcreMan Detached Sold'},
  #     {id:'BRER2331287',desc:'not obj.daddr, copy obj.disp_addr'},
  #     {id:'TRBW3609486',desc:'lp=lpr=1900'},
  #     {id:'DDF17402826',desc:'lp=lpr>100000 commercial'},
  #     {id:'TRBC3607820',desc:'lp=lpr 5800, saletp lease'},
  #     {id:'DDF17410392',desc:'lease, lpr=12000,lp null'},
  #     {id:'DDF17309381',desc:'city not in canada'},
  #     {id:'DDF16506615',desc:'lp: 625600,lpr : 35000'},
  #     {id:'DDF17407953',desc:'lp:{$lt:5000},lpr:null'}
  #     {id:'BRER2020143',desc:'src is BRE,prov is Ontario'}
  #     {id:'TRBN3415039',desc:'saletp is sale, lp = lpr =1'}
  #     {id:'DDF17359908',desc:'lp=lpr>100000 residential,'}
  #     {id:'DDF18895083',desc:'lp=lpr=11000 residential,saletp lease and sale, log error'}
  #     {id:'DDF19904518',desc:'lp != lpr log error'}
  #     {id:'DDF17404356',desc:'no ListingContractDate, use ctrdt'}
  #     {id:'BREN233590',desc:'bre, has area, set subCity as area'},
  #     {id:'TRBC3608601',desc:'uaddr:CANADA:ONTARIO:TORONTO:3018 YONGE ST,slddom=42'},
  #     {id:'TRBC3477759',desc:'uaddr:CANADA:ONTARIO:TORONTO:3018 YONGE ST,slddom=99'}
  #     # {id:'TRBN3613663',desc:'fix saletp from sale to lease'}
  #     {id:'DDF17217320',desc:'lpr out of range'}
  #   ]
  #   tests.forEach (test)->
  #     it "should import from property, #{test.id}, #{test.desc}", ()->
  #       @timeout(100000)
  #       expId = test.expId or test.id
  #       try
  #         await gPropertiesNew.deleteOne {_id:expId}
  #         prop = await gProperties.findOne {_id:test.id}
  #         {old,newItem} = await importProp {srcType:'property',prop}
  #       catch err
  #         debug.error err
  #         should.not.exists(err)
          
  #       should.exists(prop,"#{test.id} is not found in gProperties")
  #       should.exists(newItem)
  #       try
  #         insertItem = await gPropertiesNew.findOne {_id:expId},
  #         prop = await gProperties_expected.findOne {_id:expId}
  #       catch err
  #         debug.error err
  #         should.not.exists(err)

  #       should.exists(insertItem, "#{expId} is not found in gPropertiesNew")
  #       should.exists(prop, "#{expId} is not found in gProperties_expected")
  #       deletDateFields(insertItem)
  #       deletDateFields(prop)
  #       for key of prop
  #         should.deepEqual(prop[key],insertItem[key])
  #       return

  describe 'should not import prop with missing fields',->
    @timeout(100000)
    tests = [
      {id:'TRBW3609439',deleteFld:'city',desc:'no city'},
      {id:'TRBC3589415',desc:'src is TRB,prov is Deray'},#should not import
      {id:'TRBW3609439',deleteFld:'prov',desc:'no prov'},
      {id:'TRBW3609439',deleteFld:'cnty',desc:'no cnty'},
      {id:'TRBW3609440',missingfields:'lp&lpr',desc:'no lp,lpr'},
      {id:'TRBW3609439',deleteFld:'src',desc:'no src'},
      {id:'TRBW3609439',numberFld:'city',desc:'city isnumber'},
      {id:'TRBW3609439',numberFld:'prov',desc:'prov isnumber'},
      {id:'TRBW3609439',numberFld:'cnty',desc:'cnty isnumber'},
      {id:'BRER2339188',missingfields:'saletp',desc:'stp For Rent,no saletp'},
      {id:'BRER2523085',missingfields:'lp&lpr',\
      desc:'no lp,lpr, should not import'},
      {id:'TRBZ3985616',missingfields:'city',desc:'bad city format'}
    ]
    tests.forEach (test)->
      it "should no import from property: #{test.id}, #{test.desc}", ()->
        expId = test.expId or test.id
        try
          await gPropertiesNew.deleteOne {_id:expId}
          prop = await gProperties.findOne {_id:test.id}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(prop,"#{test.id} is not found in gProperties")
        delete prop[test.deleteFld]
        if test.numberFld
          prop[test.numberFld]='123456'

        try
          await importProp {srcType:'property',prop}
          insertItem = await gPropertiesNew.findOne {_id:expId}
          log = await gPropertiesImportLog.findOne {id:expId}
        catch err
          debug.error err
          should.not.exists(err)

        should.not.exists(insertItem)
        if fld = test.deleteFld or test.missingfields or test.numberFld
          debug.debug 'log',log
          # debug.debug 'log[fld]',log[fld]
          log.criticalError?.should.containDeep([{fld:fld}])
        return

    tests=[
      {id:'DDF20857574',errfld:'onD',desc:'onD is future date'}
      #lprOutOfRange only check residential
      # {id:'DDF16442260',errfld:'lprOutOfRange',desc:'lpr:{$gt:30000},lp:null'}
    ]
    tests.forEach (test)->
      it "should log  error for #{test.id}, #{test.desc}", ()->
        try
          await gPropertiesNew.deleteOne {_id:test.id}
          prop = await gProperties.findOne {_id:test.id}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(prop)

        try
          await importProp {srcType:'property',prop}
          insertItem = await gPropertiesNew.findOne {_id:test.id}
          log = await gPropertiesImportLog.findOne {id:test.id}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(log[test.errfld])
        return

  describe 'should be able to unset required fields, do not unset non required fields',->
    tests = [
      {
        _id:'TRBN5212034',
        type:'treb',
        srcKey:'Br_plus',
        descKey:'br_plus'
        newValue:null
        expected:null
      },
      {
        _id:'TRBN5212034',
        type:'treb',
        srcKey:'Tour_url',
        descKey:'vturl'
        newValue:''
        expected:'tour_url'
      },
      {
        _id:'DDF13494075',
        type:'ddf',
        srcKey:'MunicipalId',
        descKey:'city_id'
        newValue:null
        expected:'0012420353'
      },
      {
        _id:'*********:0',
        type:'bcreMan',
        expId:'BRER2390058'
        srcKey:'ForAppointmentCall',
        descKey:'ForAppointmentCall'
        newValue:null
        expected:'Leslie Macinnis'
      },
      {
        _id:'TRBX10477068',
        type:'reso',
        expId:'TRBX10477068'
        srcKey:'TaxAnnualAmount',
        descKey:'tax'
        newValue:null
        expected:null
      }
    ]
    @timeout(100000)
    tests.forEach (test)->
      it "should be able to unset empty fields #{test._id}",()->
        Col = TYPE_COL_MAPPING[test.type]
        expId = test.expId or test._id
        try
          await gProperties.deleteOne {_id: test._id}
          srcProp = await Col.findOne {_id:test._id}
          await importProp {srcType:test.type, prop:srcProp}
          prop = await gProperties.findOne {_id:expId}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(prop[test.descKey])
        srcProp[test.srcKey]=test.newValue
        try
          await importProp {srcType: test.type, prop: srcProp}
          prop = await gProperties.findOne {_id:expId}
        catch err
          debug.error err
          should.not.exists(err)

        should.equal(prop[test.descKey],test.expected)
        return

    it 'DDF22876492 should no unset vturl of TRBN5212034',()->
      try
        await gProperties.deleteOne {_id: 'TRBN5212034'}
        trebProp = await gTrebRecords.findOne {_id:'TRBN5212034'}
        await importProp {srcType:'treb',prop:trebProp}
        prop = await gProperties.findOne {_id:'TRBN5212034'}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop.vturl)
      try
        ddfProp = await gDDFRecords.findOne {_id:'DDF22876492'}
        ddfProp.Photo.PropertyPhoto[0].VideoLink=''
        await importProp {srcType:'ddf',prop:ddfProp}
        prop = await gProperties.findOne {_id:'TRBN5212034'}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop.vturl)
      return

  describe 'should import property from different source', ->
    @timeout(100000)
    tests = [
      {id:'TRBE4870390',type:'treb'},
      {id:'TRBC5210586',type:'treb'},
      {id:'TRBC5134309',type:'treb'},
      {id:'TRBE5196754',type:'treb'},
      {id:'DDF23100903',type:'ddf',expId:'DDF23100903'},
      {id:'TRBX3396080',type:'treb',desc:'Status is U, status is A, should use U and save to log'},#pass
      {id:'TRBW5077602',type:'treb',saletp:'Lease',ptype2:'Townhouse'},#pass
      {id:'TRBE5077588',type:'treb',saletp:'Sale',ptype2:'Townhouse'},#pass
      {id:'TRBN4929568',type:'treb',saletp:'Sale',ptype2:'Land'},#pass
      {id:'TRBN5077615',type:'treb',saletp:'Lease',ptype2:'Apartment'},#pass
      {id:'TRBW5077605',type:'treb',saletp:'Lease', ptype2:'House'},#pass
      {id:'TRBW5077616',type:'treb',saletp:'Lease',ptype2:'Detached'},#pass
      {id:'TRBN5077613',type:'treb',saletp:'Sale',ptype2:'Apartment'},#pass
      # #TRBW4887684,"Lsc" : "Dft","Pr_lsc" : "Sld",
      {id:'TRBW4887684',type:'treb',desc:'has open house'},
      # #TODO:rms:11,cnty:Other cnty,geocoding?
      #cnty "OUT OF AREA
      # {id:'TRBZ3411729',type:'treb',desc:'Area,out of county'}
      #rms 13,area CANADA
      {id:'TRBX3442850',type:'treb',desc:'sold,Out of Area'},#rms has bug
      {id:'TRBX3417132',type:'treb',desc:'Lotsz_code:Metres,County:Ontario'},#pass
      #
      {id:'TRBX3950501',type:'treb',\
      desc:'BC,Type_own1_out:Investment,Prop_type:Apartment, sould set ptype to r'},#pass
      {id:'TRBX4361290',type:'treb',\
      desc:'ON,Type_own1_out:Investment,Prop_type:Recreational, sould set ptype to C'},#pass
      {id:'TRBX3392233',type:'treb',desc:'AB,style Stacked Townhse'},#rms
      {id:'TRBC4023996',type:'treb',desc:'AB,style loft'},
      {id:'TRBX4056382',type:'treb',desc:'ON,has co_lagt_id'},
      #la info might from trebman
      # #Park_spcs:{$ne:null},Gr:null,Gar:null,Style:/Apartment/
      # #city is corrected
      {id:'TRBX3906392',type:'treb', desc:'Municipality:out of area'},
      {id:'TRBN5053763',type:'treb',desc:'Lease'},
      # area Other Country
      # treb sld
      # treb lotsz_code) === 'Metres'
      # pstyl semi,Stacked,Townhouse,loft
      {id:'DDF15931267',type:'ddf',desc:'agent office pe'}, #TODO: not passed
      {id:'DDF22701441',type:'ddf',desc:'room feet, inch,land ft'},
      {id:'DDF22701369',type:'ddf',desc:'building type:Duplex'},
      {id:'DDF15634461',type:'ddf',desc:'building type:Fourplex'},
      {id:'DDF16078211',type:'ddf',desc:'building type:Triplex'},
      {id:'DDF22699332',type:'ddf',desc:'Land.SizeFrontage 22 ft'},
      {id:'DDF22701330',type:'ddf',col:'mls_crea_ddf_records',desc:'apartment'},
      {id:'DDF14400694',type:'ddf',desc:'land size ft,in'}
      {id:'DDF16445470',type:'ddf',desc:'land size m'} # TODO:to confirm
      {id:'DDF15999989',type:'ddf',col:'mls_crea_ddf_records'},
      {id:'DDF13494075',type:'ddf',desc:'age years.'},
      {id:'DDF22715147',type:'ddf',desc:'split Dimension'},
      {id:'DDF13134057',type:'ddf',col:'mls_crea_ddf_records',desc:'age /(\d+)\s?to\s?(\d+)\s?years/.'},#pass
      {id:'DDF19676819',type:'ddf',desc:'age Under construction'},
      {id:'DDF21488799',type:'ddf',desc:'over 26 years'},#TODO:age2? confirm in stat
      {id:'DDF20991370',type:'ddf',desc:'age Renovated'}, #TODO:
      #AgentDetails.Office.Address.Province：null，city：TOBERMORY,TODO:confim，set agent prov as TOBERMORY
      {id:'DDF14527166',type:'ddf',desc:'AgentDetails.Office.Address.Province：null，city：TOBERMORY'}, #
      # # #.
      {id:'DDF12728127',type:'ddf',
      desc:'building type is office,AgentDetails.Office is array, has different office id'},#pass
      {id:'DDF15629261',type:'ddf',desc:'city:Out of Province_BC'},  #todo confirm
      {id:'DDF17597641',type:'ddf',desc:'office is barrie，no prov'},#pass
      # "City" : "Kelowna, B.C.",
      {id:'DDF14818828',type:'ddf',desc:'city is Kelowna, B.C.'},#pass
      #"McKenzie Towne, Calgary",
      {id:'DDF16686088',type:'ddf',desc:'city is McKenzie Towne, Calgary'},#pass
      #DDF16487055
      {id:'DDF15448583',type:'ddf',desc:'POINTE AU BARIL, ARCHIPELAGO',\
      expect:{city:'Pointe Au Baril',cmty:'Archipelago'}},#pass
      {id:'DDF13899560',type:'ddf',desc:'city is Rural Grande Prairie No. 1, County of',\
      expect:{city:'Rural Grande Prairie No. 1'}},#pass
      {id:'DDF16487055',type:'ddf',desc:'city is MCKELLAR, PARRY SOUND, MUSKOKA,SEGUIN, prov is ontario'},#pass
      #DDF14555059
      {id:'DDF14555059',type:'ddf',desc:'Agent phone is string,(*************'}, #pass
      {id:'DDF15435974',type:'ddf',desc:'business'} #pass
      {id:'DDF18735398',type:'ddf',desc:'Building.Type:Offices'} #pass
      {id:'DDF20224537',type:'ddf',desc:'Building.Type:Parking'} #pass
      {id:'DDF21241831',type:'ddf',desc:'Building.ConstructionStyleAttachment:Stacked'}
      {id:'DDF20691806',type:'ddf',desc:'Building.ArchitecturalStyle:Cottage'}
      {id:'DDF20436527',type:'ddf',desc:'Building.ArchitecturalStyle:Loft'}
      #Accomodation, Other, Residential
      {id:'DDF16234475',type:'ddf',desc:'businesstype:Accomodation, Other, Residential'}
      # openhouse is object
      {id:'DDF22454830',type:'ddf',col:'mls_crea_ddf_records'},
      {id:'DDF16212225',type:'ddf',desc:'openhosue is array, room is 11ft, 6inch'}, # openhouse is array
      #failed?
      {id:'DDF16161688',type:'ddf',expId:'DDF16161688',desc:'Building.SizeInterior 60573 m2'},#todo
      {id:'262280542:0',type:'bcreMan',expId:'BRER2258915',desc:'tpdwel 1/2 Duplex'},
      #'Typedwel':'Row House (Non-Strata)'
      {id:'262521056:0',type:'bcreMan',expId:'BRER2499429',desc:'Residential Attached,Exp, has mfee Apartment/Condo'},
      {id:'262114001:0',type:'bcreMan',expId:'BRER2092374',desc:'Apartment/Condo'},
      {id:'*********:0',type:'bcreMan',expId:'BRER2390058',desc:'Class Rental'},
      {id:'262274914:0',type:'bcreMan',expId:'BRER2253287',desc:'stp for sale'},
      {id:'262360583:0',type:'bcreMan',expId:'BRER2338956',desc:'stp for rent'},
      {id:'262243254:0',type:'bcreMan',expId:'BRER2221627',desc:'Apartment sold'},
      {id:'TRBX4421627',type:'treb',expId:'TRBX4421627',desc:'has maxPicSize'},
      {id:'262601910:0',type:'bcreMan',expId:'BRER2580283',desc:'no BdsNotInBsmt'},
      {id:'DDF26650670',type:'ddf',desc:'test format open house time'}
      {id:'BRER2862471',type:'bcre',expId:'BRER2862471',desc:'test bcreAuto sale import'},
      {id:'BRER2862452',type:'bcre',expId:'BRER2862452',desc:'test bcreAuto lease import'},
      {id:'RHBH4187495',type:'rahb',expId:'RHBH4187495',desc:'test rahb lease import'},
      {id:'RHBH4187133',type:'rahb',expId:'RHBH4187133',desc:'test rahb sale import'},
      # NOTE: RHBH4189933与DDF26712247存在merge的情况,deepEqual需要去掉merged字段
      {id:'RHBH4189933',type:'rahb',expId:'RHBH4189933',desc:'rahb has lotsz infos'},
      {id:'RHBH4190286',type:'rahb',expId:'RHBH4190286',desc:'rahb has lotsz infos,unit is acres'},
      {id:'BRER2866564',type:'bcre',expId:'BRER2866564',desc:'bcre has lotsz infos'},
      {id:'TRBC138276',type:'treb',expId:'TRBC138276',desc:'treb has lotsz infos'},
      {id:'DDF26712247',type:'ddf',expId:'DDF26712247',desc:'ddf has lotsz infos'},
      {id:'DDF26261722',type:'ddf',expId:'DDF26261722',desc:'ddf has lotsz infos'},
      {id:'DDF26717235',type:'ddf',expId:'DDF26717235',desc:'ddf has lotsz infos'},
      {id:'DDF25585660',type:'ddf',expId:'DDF25585660',desc:'ddf has lotsz infos'},
      {id:'DDF25585615',type:'ddf',expId:'DDF25585615',desc:'ddf has lotsz infos'},
      {id:'BRER2880516',type:'bcre',expId:'BRER2880516',desc:'test bc new status -> Active Under Contract'},
      {id:'BRER2877564',type:'bcre',expId:'BRER2877564',desc:'test bc new status -> Pending'},
      {id:'BRER2877679',type:'bcre',expId:'BRER2877679',desc:'test bc new status -> Closed'},
      {id:'94148730',type:'creb',expId:'CLGA2159918',desc:'test creb import'},
      {id:'94094081',type:'creb',expId:'CLGA2159424',desc:'test creb import'},
      {id:'94009652',type:'creb',expId:'CLGA2158439',desc:'test creb import'},
      {id:'93618413',type:'creb',expId:'CLGA2155316',desc:'test creb import'}
      {id:'8098061',type:'oreb',expId:'OTW731271',desc:'test oreb sale import'},
      {id:'38726173',type:'oreb',expId:'OTW1405230',desc:'test oreb lease import'},
      {id:'27742916',type:'oreb',expId:'OTW1134917',desc:'test oreb sale import'},
      {id:'TRBC8380464',type:'treb',expId:'TRBC8380464',desc:'test treb new fields'},
      {id:'TRBC8395146',type:'treb',expId:'TRBC8395146',desc:'test treb new fields'},
      {id:'TRBS8276498',type:'treb',expId:'TRBS8276498',desc:'test treb new fields'},
      {id:'EDME4412296',type:'edm',expId:'EDME4412296',desc:'test edm import'},
      {id:'EDME4412300',type:'edm',expId:'EDME4412300',desc:'test edm import'},
      {id:'EDME4416243',type:'edm',expId:'EDME4416243',desc:'test edm import'},
      {id:'91946926',type:'creb',expId:'CLGA2139899',desc:'test creb import'},
      {id:'CAR40659537',type:'car',expId:'CAR40659537',desc:'test car import'},
      {id:'TRBX10477068',type:'reso',expId:'TRBX10477068',desc:'test reso import'},
      {id:'DDF27904386',type:'creaReso',expId:'DDF27904386',desc:'test ddf reso import'}
      {id:'EDME4426498',type:'edm',expId:'EDME4426498',desc:'test edm no AssociationFee import'}
      {id:'EDME4367201',type:'edm',expId:'EDME4367201',desc:'test edm has AssociationFee and CondoFee import'}
      {id:'BRER2986703',type:'bcreReso',expId:'BRER2986703',desc:'test bcre reso import'}
      {id:'BRER2736961',type:'bcreReso',expId:'BRER2736961',desc:'test bcre import when city is No City Value'}
      {id:'TRBS11937464',type:'reso',expId:'TRBS11937464',desc:'test reso StreetDirSuffix different with StreetDirPrefix'}
      {id:'TRBW11923308',type:'reso',expId:'TRBW11923308',desc:'test reso only has StreetDirPrefix'}
      {id:'TRBC11912586',type:'reso',expId:'TRBC11912586',desc:'test reso StreetDirSuffix same with StreetDirPrefix'}
    ]
    needRemoveMergedIds = ['RHBH4189933','EDME4405803']
    tests.forEach (test)->
      it "should import #{test.type}, #{test.id}, #{test.desc}", ()->
        # debug.debug 'test.col',test.col
        expId = test.expId or test.id
        Col = TYPE_COL_MAPPING[test.type]
        try
          await gProperties.deleteOne {_id:expId}
          prop = await Col.findOne {_id:test.id}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(prop)

        try
          {old,newItem} = await importProp {srcType:test.type,prop}
        catch err
          debug.error err
          should.not.exists(err)

        should.exists(newItem)
        insertItem = await gProperties.findOne {_id:expId}
        should.exists(insertItem)
        deletDateFields(insertItem)
        prop = await gProperties_expected.findOne {_id:expId}
        deletDateFields(prop)
        if expId in needRemoveMergedIds
          delete prop.merged
          delete insertItem.merged
        should.deepEqual(prop,insertItem)
        return

  describe 'test m and lp change, should delete m_zh, update price', ->
    @timeout(100000)
    propId = 'TRBE8225374'
    type = 'treb'
    it 'import TRBE8225374 again with a revised m, should delete m_zh', ()->
      Col = TYPE_COL_MAPPING[type]
      try
        prop = await gProperties.findOne {_id:propId}
      catch err
        debug.error err
        should.not.exists(err)
      should.exists(prop)
      prop.m_zh.should.not.equal('')
      prop.lp.should.equal(950000)
      
      # only change price first
      try
        prop = await Col.findOne {_id:propId}
        prop.Lp_dol = 900000
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)
      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      insertItem.lp.should.equal(900000)
      insertItem.lst.should.equal('New')
      pcEntry = insertItem.his.find((item) -> item.s is 'Pc' and item.n is 900000 and item.o is 950000)
      should.exist(pcEntry)
      insertItem.m_zh.should.not.equal('')

      # change m and price
      try
        prop = await Col.findOne {_id:propId}
        prop.Ad_text = 'test'
        prop.Lp_dol = 910000
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)
      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      should.equal(insertItem.m_zh,'')
      insertItem.lp.should.equal(910000)
      insertItem.lst.should.equal('New')
      pcEntry = insertItem.his.find((item) -> item.s is 'Pc' and item.n is 910000 and item.o is 900000)
      chMEntry = insertItem.his.find((item) -> item.s is 'chM')
      should.exist(pcEntry)
      should.exist(chMEntry)
      return

  describe 'test rni price change and reimport, should change prop', ->
    @timeout(100000)
    propId = 'TRBE8225374'
    type = 'treb'
    it 'should reimport TRBE8225374 and price should change to 900000', ()->
      Col = TYPE_COL_MAPPING[type]
      try
        await gProperties.deleteOne {_id:propId}
        prop = await Col.findOne {_id:propId}
      catch err
        debug.error err
        should.not.exists(err)
      should.exists(prop)

      try
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      insertItem.lp.should.equal(950000)

      try
        prop.Lp_dol = 900000
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      insertItem.lp.should.equal(900000)
      insertItem.lst.should.equal('New')
      pcEntry = insertItem.his.find((item) -> item.s is 'Pc')
      should.exist(pcEntry)
      return

  describe 'test rni.unt change, should change origUnt', ->
    @timeout(100000)
    propId = 'TRBE8225374'
    type = 'treb'
    it 'should import TRBE8225374 and unt should be 59', ()->
      Col = TYPE_COL_MAPPING[type]
      try
        await gProperties.deleteOne {_id:propId}
        prop = await Col.findOne {_id:propId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)

      try
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      deletDateFields(insertItem)
      prop = await gProperties_expected.findOne {_id:propId}
      deletDateFields(prop)

      should.deepEqual(prop,insertItem)
      
      # set rni.unt = ''
      try
        prop = await Col.findOne {_id:propId}
        prop.Apt_num = ''
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      should.not.exists(insertItem.unt)
      should.not.exists(insertItem.origUnt)
      return

  describe 'test rni status change from A to U, should change spcts, \
  and reimport without price and status change, spcts should not change ay more', ->
    @timeout(100000)
    propId = 'TRBE8225374'
    type = 'treb'
    nowDate = new Date()
    it 'should import TRBE8225374 and spcts,his should correct', ()->
      Col = TYPE_COL_MAPPING[type]
      try
        await gProperties.deleteOne {_id:propId}
        prop = await Col.findOne {_id:propId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)

      # importProp1
      try
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.equal((new Date(insertItem.spcts)).getTime(),Math.min(insertItem.ts,insertItem.lup))
      should.exists(insertItem)
      deletDateFields(insertItem)
      prop = await gProperties_expected.findOne {_id:propId}
      deletDateFields(prop)
      should.deepEqual(prop,insertItem)
      
      # importProp2
      try
        prop = await Col.findOne {_id:propId}
        prop.lsc = 'Sld'
        prop.status = 'U'
        prop.Unavail_dt = new Date()
        prop.Unavail_dt.setDate(nowDate.getDate() + 1)
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      # spcts should be updated
      insertItem.spcts.toDateString().should.equal(nowDate.toDateString())
      spcts1 = insertItem.spcts

      # his 'Sld' should have correct sldd and ts
      sldHis = insertItem.his.find((item) -> item.s is 'Sld')
      should.exists(sldHis)
      sldHis.sldd.should.equal(inputToDateNum nowDate)
      # his.ts should be new Date
      sldHis.ts.toDateString().should.equal(nowDate.toDateString())


      # importProp3
      setTimeout ->
        console.log "wait 30 seconds"
        , 30000
      try
        prop = await Col.findOne {_id:propId}
        prop.lsc = 'Sld'
        prop.status = 'U'
        prop.Unavail_dt = new Date()
        prop.Unavail_dt.setDate(nowDate.getDate() + 5)
        {old,newItem} = await importProp {srcType:type,prop}
      catch err
        debug.error err
        should.not.exists(err)

      insertItem = await gProperties.findOne {_id:propId}

      should.exists(insertItem)
      insertItem.spcts.toDateString().should.equal(nowDate.toDateString())
      spcts2 = insertItem.spcts
      # spcts should not change since no price and status change
      (spcts1.getTime()).should.equal(spcts2.getTime())

      return

  describe 'test import prop when prop exists, should skip geocoding', ->
    @timeout(100000)
    it 'should import TRBW12111895 when prop exists, should skip geocoding', ()->
      propId = 'TRBW12111895'
      try
        prop = await gResoRecords.findOne {_id:propId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)
      try
        {old,newItem} = await importProp {srcType:'reso',prop}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(newItem)
      insertItem = await gProperties.findOne {_id:propId}
      should.exists(insertItem)
      deletDateFields(insertItem)
      prop = await gProperties_expected.findOne {_id:propId}
      deletDateFields(prop)

      should.deepEqual(prop,insertItem)
      return

  describe 'test trebReso new status import', ->
    @timeout(100000)
    it 'should not import trebReso with status:Draft', ()->
      testPropId = 'TRBW9237550'
      try
        await gProperties.deleteOne {_id:testPropId}
        prop = await gResoRecords.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)

      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord = await gProperties.findOne {_id:testPropId}
        log = await gPropertiesImportLog.findOne {id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.not.exists(importRecord)
      should.exists(log)
      log.criticalError?.should.containDeep([{
        fld: 'lst',
        msg: "Skip import for TRB property with lst='Draft'"
      }])
      return

    it 'should not import trebReso with status:Previous Status when properties no record', ()->
      testPropId = 'TRBW12111895'
      try
        await gProperties.deleteOne {_id:testPropId}
        prop = await gResoRecords.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)
      prop.MlsStatus = 'Previous Status'

      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord = await gProperties.findOne {_id:testPropId}
        log = await gPropertiesImportLog.findOne {id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.not.exists(importRecord)
      should.exists(log)
      log.criticalError?.should.containDeep([{
        fld: 'lst',
        msg: "Skip import for TRB property with lst='Previous Status' since it doesn't exist in properties table"
      }])
      return

    it 'should import trebReso with status:Previous Status when properties has record', ()->
      testPropId = 'TRBW12111895'
      try
        await gProperties.deleteOne {_id:testPropId}
        prop = await gResoRecords.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(prop)
      # prop.MlsStatus = 'Previous Status'

      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)
      
      should.exists(importRecord)
      deletDateFields(importRecord)
      insertProp = await gProperties_expected.findOne {_id:testPropId}
      deletDateFields(insertProp)
      delete insertProp.keepPropGeocoding # 房源被删除,重新导入时没有当前字段
      delete insertProp.keepQ # 房源被删除,重新导入时没有当前字段
      delete insertProp.trbtp # 房源被删除,重新导入时没有当前字段
      delete insertProp.his # chVturl首次import没有信息
      should.deepEqual(insertProp,importRecord)

      prop = await gResoRecords.findOne {_id:testPropId}
      prop.MlsStatus =  'Previous Status'

      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord2 = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      should.exists(importRecord2)

      deletDateFields(importRecord2)
      insertProp2 = await gProperties_expected.findOne {_id:testPropId}
      deletDateFields(insertProp2)
      should.deepEqual(insertProp2,importRecord2)
      return

  describe 'test addStatusChangeHistory update spcts', ->
    @timeout(100000)
    it 'should not update spcts when price/status not changed and status is active, lup updated', ()->
      testPropId = 'TRBW12207877'
      try
        await gProperties.deleteOne {_id:testPropId}
        prop = await gResoRecords.findOne {_id:testPropId}
        should.exists(prop)
      catch err
        debug.error err
        should.not.exists(err)

      prop.ModificationTimestamp = new Date('2025-05-01') # lup
      prop._mt = new Date('2025-05-01')
      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      # 修改rni房源记录后再次import,此时spcts不应该修改
      prop.ModificationTimestamp = new Date('2025-06-01') # lup
      prop._mt = new Date('2025-06-01')
      prop.PublicRemarks += ' add test.'
      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord2 = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      oldSpcts = new Date(importRecord.spcts).getTime()
      newSpcts = new Date(importRecord2.spcts).getTime()
      should.equal(oldSpcts,newSpcts)
      return

    it 'should update spcts when lst changed and status is active, lup updated', ()->
      testPropId = 'TRBW12207877'
      try
        await gProperties.deleteOne {_id:testPropId}
        prop = await gResoRecords.findOne {_id:testPropId}
        should.exists(prop)
      catch err
        debug.error err
        should.not.exists(err)

      prop.ModificationTimestamp = new Date('2025-05-01') # lup
      prop._mt = new Date('2025-05-01')
      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      # 修改rni房源记录后再次import,此时spcts不应该修改
      prop.ModificationTimestamp = new Date('2025-06-01') # lup
      prop._mt = new Date('2025-06-01')
      prop.MlsStatus = 'Sold Conditional' # New -> Sc
      try
        {old,newItem} = await importProp {srcType:'reso',prop}
        importRecord2 = await gProperties.findOne {_id:testPropId}
      catch err
        debug.error err
        should.not.exists(err)

      oldSpcts = new Date(importRecord.spcts).getTime()
      newSpcts = new Date(importRecord2.spcts).getTime()
      oldSpcts.should.not.equal(newSpcts)
      return
