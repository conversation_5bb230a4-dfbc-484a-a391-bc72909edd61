# ./unitTest/test.sh -f propertyImport/03_propertyImportMerge.js

should = require('should')
helpers = require('../00_common/helpers')
debugHelper = require('../../built/lib/debug')
debug = debugHelper.getDebugger(3)
MSG_STRINGS = helpers.MSG_STRINGS
saveToMaster = null
GeoCoder = null
#deep compare object
NO_REAL_GEOCODING = false
request = null
Cols = {}
gPropertiesNew = null
gOldProperties=null
gProperties =  null
gGeoCache =null
gTrebRecords = null
gBcreRecords = null
gBcreManRecords = null
gDDFRecords = null
gRabhRecords = null
gOrebRecords = null
gEdmRecords = null
gCarRecords = null
gResoRecords = null
gDDFResoRecords = null
gTransitStop =  null
gSchool = null
gCensus = null
gCensus2016 = null
gCensus2016ReversKeys = null
gProperties_expected = null
gBoundary = null
gPropertiesImportLog = null
gTrebManualRecords=null
gTrebDtaRecords = null
SqftsAggregate = null
{deletDateFields} = require('./propertyCommon')

TYPE_COL_MAPPING = {}
#property->propertyNew test case.
#Properties mightbe Properties collection or propertiesnew collection
importProp = ({srcType,prop},cb)->
  return new Promise((resolve)->
  #targetProperties = if srcType is 'property' \
  #then gPropertiesNew else gProperties
    if srcType is 'property'
      targetProperties = gPropertiesNew
    else
      targetProperties = gProperties
    saveToMaster.convertAndSaveRecord {
      srcType,
      targetProperties,
      record:prop,
      TransitStop: gTransitStop,
      School: gSchool,
      Boundary: gBoundary,
      Census2016: gCensus2016,
      Census2016ReversKeys: gCensus2016ReversKeys,
      # Census2021:'',
      Census: gCensus,
      TrebRecords:gTrebRecords,
      DDFRecords:gDDFRecords,
      BcreManualRecords:gBcreManRecords,
      PropertiesImportLog:gPropertiesImportLog,
      SqftsAggregate,
      GeoCoder
    },(err,old,newItem) ->
      if err
        return reject(err)
      return resolve({old,newItem})
  )

isEqualASIDs = (aSIDsA = [],aSIDsB = [])->
  if (not aSIDsA.length) or (not aSIDsB.length)
    return false
  if aSIDsA.length isnt aSIDsB.length
    return false
  keyMap = {}
  for obj in aSIDsA
    key = "#{obj.sid}_#{obj.id}"
    keyMap[key] = 1
  for obj in aSIDsB
    key = "#{obj.sid}_#{obj.id}"
    if not keyMap[key]
      return false
  return true

describe 'Property Import',->
  before (done) ->
    @timeout(3000000)
    request = helpers.getServer()
    gPropertiesNew = helpers.COLLECTION('vow','propertiesNew')
    gProperties =  helpers.COLLECTION('vow','properties')
    gGeoCache = helpers.COLLECTION('vow','geo_cache')
    gTrebRecords = helpers.COLLECTION('rni','mls_treb_master_records')
    gTrebManualRecords = helpers.COLLECTION('rni','mls_treb_manual_records')
    gBcreManRecords = helpers.COLLECTION('rni','mls_bcre_manual_records')
    gBcreRecords = helpers.COLLECTION('rni','mls_bcre_records')
    gDDFRecords = helpers.COLLECTION('rni','mls_crea_ddf_records')
    gRabhRecords = helpers.COLLECTION('rni','mls_rahb_records')
    gOrebRecords = helpers.COLLECTION('rni','mls_oreb_master_records')
    gEdmRecords = helpers.COLLECTION('rni','mls_rae_master_records')
    gCarRecords = helpers.COLLECTION('rni','mls_car_master_records')
    gResoRecords = helpers.COLLECTION('rni','reso_treb_evow_merged')
    gDDFResoRecords = helpers.COLLECTION('rni','reso_crea_merged')
    gTransitStop =  helpers.COLLECTION('vow','transit_stop')
    gSchool = helpers.COLLECTION('vow','sch_rm_merged')
    gCensus = helpers.COLLECTION('vow','census')
    gCensus2016 = helpers.COLLECTION('vow','census2016')
    gCensus2016ReversKeys = helpers.COLLECTION('vow','census2016ReversKeys')
    gProperties_expected = helpers.COLLECTION('vow','properties_expected')
    gBoundary= helpers.COLLECTION('vow','boundary')
    gPropertiesImportLog =helpers.COLLECTION('tmp','properties_import_log')
    gTrebDtaRecords = helpers.COLLECTION('rni','mls_treb_dta_records')
    GeocodingStat    =  helpers.COLLECTION 'vow','geo_coding_stat'
    SqftsAggregate = helpers.COLLECTION('vow','sqfts_aggregate')
    saveToMaster = helpers.INCLUDE 'libapp.saveToMaster'
    GeoCoder = helpers.MODEL 'GeoCoder'
    GeoCoder.noGeocoding(true)  # 不进行geocode操作
    TYPE_COL_MAPPING = {
      'treb':gTrebRecords
      'ddf':gDDFRecords
      'bcreMan':gBcreManRecords
      'bcre':gBcreRecords
      'trebMan':gTrebManualRecords
      'dta':gTrebDtaRecords
      'oreb':gOrebRecords
      'edm':gEdmRecords
      'reso':gResoRecords
      'creaReso':gDDFResoRecords
    }
    
    dbs = [
      { db: 'vow', table: 'geo_cache' },
      { db: 'vow', table: 'properties_expected' },
      { db: 'vow', table: 'properties' },
      { db: 'rni', table: 'mls_treb_master_records' },
      { db: 'rni', table: 'mls_treb_manual_records' },
      { db: 'rni', table: 'mls_treb_dta_records' },
      { db: 'rni', table: 'mls_crea_ddf_records' },
      { db: 'rni', table: 'mls_bcre_manual_records' },
      { db: 'rni', table: 'mls_bcre_records' },
      { db: 'rni', table: 'mls_rahb_records' },
      { db: 'rni', table: 'mls_oreb_master_records' },
      { db: 'rni', table: 'mls_rae_records'},
      { db: 'rni', table: 'reso_treb_evow_merged' },
      { db: 'rni', table: 'reso_crea_merged' },
      { db: 'tmp', table: 'properties_import_log' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, () ->
        setTimeout(done, 2500)
    return


#三种merge case
# ddf merge treb or bcre, has same _id, do not overwrite.
# trebMan merge treb
# different sid save both.

  #bcre update bcre
  #bcre & ddf
  #treb & ddf
  #ddf & ddf
  #treb & treb
  #treb & trebMan
  #ddf, treb & trebMan
  #dta
  #优先级。不同的字段。
  #watch
        # |new 		        |old
        # |---------------|------------------
        # |	DDF       	  |DTA,TRB,BC
        # |	DTA           |TRB,BC
        # |	TRB           |src is BCRE, prov is BC
        # |	BCRE          |src is TRB, prov is ON

        #   |new 		        |old
        # |---------------|------------------
        # |	DDF       	  |DDF
        # |	DTA           |DDF,DTA
        # |	TRB           |DDF,DTA,TRB
        # |	BCRE          |DDF,DTA,BCR

  describe 'Merge ddf and bcre',->
    @timeout(100000)
    tests = [
      {id1:'262285260:0',type1:'bcreMan',expId:'BRER2263633',\
      id2:'DDF19387412',type2:'ddf',\
      desc:'Import BCRE first and then DDF,same sid, Class Residential Attached'},

      {id1:'DDF19384413',type1:'ddf',\
      id2:'262282338:0',type2:'bcreMan',expId:'BRER2260711',\
      desc:'Import DDF first and then BCRE,same sid, Class Multifamily'}, #sldd

      #TODO
      {id1:'262284841:0',type1:'bcreMan',expId:'BRER2263214',\
      id2:'DDF19368988',type2:'ddf',\
      desc:'Class Land'}

      # #DDF19464472 no school found.
      {id1:'262288356:0',type1:'bcreMan',expId:'BRER2266729',\
      id2:'DDF19464472',type2:'ddf',\
      desc:'Class Residential Detached'},

      {id1:'262286686:0',type1:'bcreMan',expId:'BRER2265059',\
      id2:'DDF19466281',type2:'ddf',\
       desc:'Class Attached tpdwel townhouse' },#townhouse

      {id1:'262281013:0',type1:'bcreMan',expId:'BRER2259386',\
      id2:'DDF19322958',type2:'ddf',
      desc:'Class Residential Detached,tpdwel townhouse, sold'},#townhouse

      {id1:'262220427:0',type1:'bcreMan',expId:'BRER2198800',\
      id2:'DDF18564018',type2:'ddf',\
      desc:'Row House (Non-Strata),sold'},

      {id1:'262285224:0',type1:'bcreMan',expId:'BRER2263597',\
      id2:'DDF19414340',type2:'ddf',\
      desc:'Apartment/Condo'},#
      {
        id2:'262652746:0', type2:'bcreMan',id1:'DDF23799607',type1:'ddf',\
        expId:'BRER2631119',desc:'bcreMan no bdrms. calc bdrms from ddf'
      }
    ]
    tests.forEach (test)->
      it "should import #{test.type1} #{test.id1}, #{test.type2},#{test.id2},#{test.desc}", ()->
        expId = test.expId or test.id1
        Col1 = TYPE_COL_MAPPING[test.type1]
        Col2 = TYPE_COL_MAPPING[test.type2]
        await gProperties.deleteOne {_id:expId}
        prop = await Col1.findOne {_id:test.id1}
        should.exists(prop,'prop not exist')
        try
          await importProp {srcType:test.type1,prop}
          prop = await Col2.findOne {_id:test.id2}
        catch err
          debug.error err
          should.not.exist(err)
        # console.log 'merge ddf id', prop._id
        should.exists(prop)
        try
          await importProp {srcType:test.type2, prop}
          insertItem = await gProperties.findOne {_id:expId}
        catch err
          debug.error err
          should.not.exist(err)

        should.exists(insertItem)
        deletDateFields(insertItem)
        prop = await gProperties_expected.findOne {_id:expId}
        # console.log 'expected._id', insertItem._id
        should.exists(prop)
        deletDateFields(prop)
        should.deepEqual(prop,insertItem)
        return


  describe 'should import from 2 source and merge correctly',->
    @timeout(100000)
    tests = [
      {id1:'TRBW5077370',type1:'treb',id2:'DDF22701026',\
      type2:'ddf',saletp:'Lease',ptype2:'Apartment',\
      col2:'mls_crea_ddf_records'},
      {id1:'DDF22700712',type1:'ddf',id2:'TRBE5077241',\
      type2:'treb',saletp:'Sale',ptype2:'Apartment',\
      col1:'mls_crea_ddf_records',expId:'TRBE5077241'},
      {id1:'TRBC5076963',type1:'treb',id2:'DDF22699518',type2:'ddf',
      saletp:'Sale',ptype2:'Apartment',
      col2:'mls_crea_ddf_records'},
      {id1:'262546160:0',type1:'bcreMan',id2:'DDF22699518',\
      type2:'ddf',saletp:'Sale',ptype2:'Apartment',\
      expId:'BRER2524533'}
      {id1:'DDF22699518',type1:'ddf',id2:'262546160:0',\
      type2:'bcreMan',saletp:'Sale',ptype2:'Apartment',col1:'mls_crea_ddf_records',\
      col2:'mls_bcre_manual_records',expId:'BRER2524533'}
      {expId:'TRBC3708978',id1:'DDF17816763',type1:'ddf',id2:'TRBC3708978',\
      type2:'treb',col1:'mls_crea_ddf_records',desc:'exp addr 29 Queens Quay E'}
      {expId:'TRBC3725037',id2:'DDF17885783',type2:'ddf',id1:'TRBC3725037',\
      type1:'treb',
      desc:'exp addr 29 Queens Quay E'}

      {expId:'TRBC3737114',id1:'DDF17931305',type1:'ddf',id2:'TRBC3737114',\
      type2:'treb',col1:'mls_crea_ddf_records',
      desc:'exp addr 29 Queens Quay E'}

      {expId:'TRBC3748302',id1:'TRBC3748302',type1:'treb',id2:'DDF17974141',\
      type2:'ddf',
      desc:'exp addr 29 Queens Quay E'}


      {id1:'TRBC4999486',type1:'treb',id2:'TRBC4999486',\
      type2:'trebMan',saletp:'Lease',\
      desc:'trebman lease'},#pass
      {id1:'TRBC5055709',type1:'treb',id2:'TRBC5055709',type2:'trebMan',\
      
      desc:'trebman furnished Y'},
      {id1:'TRBW4992553',type1:'treb',id2:'TRBW4992553',type2:'trebMan',\
      
      desc:'trebman furnished N, has co_list'},
      
      # saletp 与 price 不匹配,不会import
      # {id1:'TRBW5001296',type1:'treb',id2:'TRBW5001296',type2:'trebMan',\
      # desc:'trebman class Condo sale,has xd, u,price 2500, should be lease'},
      
      {id1:'TRBC4993946',type1:'treb',id2:'TRBC4993946',\
      type2:'trebMan',desc:'has coop2 agent'},
      {id1:'TRBW5001182',type1:'treb',id2:'TRBW5001182',type2:'trebMan',\
      desc:'trebman active, hs xd'},
      
      {id1:'TRBE3337519',type1:'treb',\
      id2:'DDF22699518',type2:'ddf',\
      desc:'ON,Type_own1_out:Investment,Prop_type:Raw \
      (Outside Official Plan), sould set ptype to C'},
      
      {id1:'TRBE4473801',type1:'treb',id2:'DDF20764585',type2:'ddf',\
      desc:'AB,Type_own1_out:Condo Apt,no gr, should use park space'},
      
      {id1:'TRBN4827939',type1:'treb',id2:'DDF22096573',type2:'ddf',\
      desc:'other country'}
      {id1:'TRBC5075714',type1:'treb',id2:'TRBC5075714',type2:'dta',\
      desc:''}
      
    ]
    
    tests.forEach (test)->
      it "should import #{test.type1} #{test.id1}, #{test.type2},#{test.id2},and merge correctly", ()->
        expId = test.expId or test.id1
        Col1 = TYPE_COL_MAPPING[test.type1]
        Col2 = TYPE_COL_MAPPING[test.type2]
        await gProperties.deleteOne {_id:expId}
        prop = await Col1.findOne {_id:test.id1}
        should.exists(prop,'prop not exist')
        try
          await importProp {srcType:test.type1,prop}
          prop = await Col2.findOne {_id:test.id2}
        catch err
          debug.error err
          should.not.exist(err)
        # console.log 'merge ddf id', prop._id
        should.exists(prop)
        try
          await importProp {srcType:test.type2, prop}
          insertItem = await gProperties.findOne {_id:expId}
        catch err
          debug.error err
          should.not.exist(err)
        console.log 'insertItem._id', insertItem._id
        should.exists(insertItem)
        deletDateFields(insertItem)
        prop = await gProperties_expected.findOne {_id:expId}
        # console.log 'expected._id', insertItem._id
        should.exists(prop)
        deletDateFields(prop)
        should.deepEqual(prop,insertItem)
        return

  # should add price change his
  # should add laHis
  # should unset required fields,TRBW5073279 has bdrms,TRBW5073279 has no bdrms.ddf has bdrms.final result should not has bdrms.
  

  describe 'should import treb, ddf and treb man',->
    #TRBW5073279 has agent info
    @timeout(100000)
    it 'should import treb TRBW5073279, ddf DDF22690613 and merge correctly', ()->
      try
        await gProperties.deleteOne {_id:'TRBW5073279'}
        prop = await gTrebManualRecords.findOne {_id:'TRBW5073279'}
        await importProp {srcType:'trebMan',prop}
        # console.log newItem
        prop = await gTrebRecords.findOne {_id:'TRBW5073279'}
        await importProp {srcType:'treb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF22690613'}
        # console.log 'merge ddf id', prop._id
        await importProp {srcType:'ddf',prop}
        insertItem = await gProperties.findOne {_id:'TRBW5073279'}
      catch err
        debug.error err
        should.not.exist(err)

      should.exists(insertItem)
      deletDateFields(insertItem)
      prop = await gProperties_expected.findOne {_id:'TRBW5073279'}
      # console.log prop
      should.exists(prop)
      deletDateFields(prop)
      should.deepEqual(prop,insertItem)
      insertItem.trbtp?.length.should.be.exactly(4)
      expectedTtbtp = ['br','vow', 'idx', 'dta']
      for tp in insertItem.trbtp
        isExpected = expectedTtbtp.includes(tp)
        isExpected.should.be.exactly(true)
      return


  describe 'should merge props correctly',->
    @timeout(100000)
    #do not merge same source.
    #idx merge ddf
    #evow merge ddf
    
    #different id, new is not lower priority, set old as merged
    # TODO: 相同source不merge，当merge逻辑变更时需要测试
    # xit 'should merge TRBX126705,TRBX126680,TRBX126681', ()->
    #   await gPropertiesNew.deleteMany {_id:$in:['TRBX126705','TRBX126680','TRBX126681']}
    #   prop = await gProperties.findOne {_id:'TRBX126705'}
    #   await importProp {srcType:'property',prop}
    #   prop = await gProperties.findOne {_id:'TRBX126680'}
    #   await importProp {srcType:'property',prop}
    #   prop = await gProperties.findOne {_id:'TRBX126681'}
    #   await importProp {srcType:'property',prop}
    #   item1 = await gPropertiesNew.findOne {_id:'TRBX126680'}
    #   should.exists(item1)
    #   should.deepEqual(item1.merged,'TRBX126681')
    #   should.deepEqual(item1.origId,['TRBX126705','TRBX126680'])

    #   propTRBX126705 = await gPropertiesNew.findOne {_id:'TRBX126705'}
    #   should.exists(propTRBX126705)
    #   console.log 'propTRBX126705',propTRBX126705
    #   should.exists(propTRBX126705.merged)
    #   propTRBX126705.merged.should.equal('TRBX126680')

    #   propTRBX126681 = await gPropertiesNew.findOne {_id:'TRBX126681'}
    #   should.exists(propTRBX126681)
    #   deletDateFields(propTRBX126681)
    #   console.log 'propTRBX126681',propTRBX126681
    #   should.deepEqual(propTRBX126681.origId,['TRBX126705','TRBX126680','TRBX126681'])

    #   prop = await gProperties_expected.findOne {_id:'TRBX126681'}
    #   console.log 'prop',prop
    #   should.exists(prop,'TRBX126681 is not exist in gProperties_expected')
    #   deletDateFields(prop)
    #   for key of prop
    #     should.deepEqual(prop[key],propTRBX126681[key])
    #   return
    
    it 'should merge TRBC3663357,DDF17618495', ()->
      try
        await gPropertiesNew.deleteMany {_id:$in:['TRBC3663357','DDF17618495']}
        prop = await gProperties.findOne {_id:'TRBC3663357'}
        await importProp {srcType:'property',prop}
        prop = await gProperties.findOne {_id:'DDF17618495'}
        await importProp {srcType:'property',prop}
        item1 = await gPropertiesNew.findOne {_id:'TRBC3663357'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['TRBC3663357','DDF17618495']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF17618495 = await gPropertiesNew.findOne {_id:'DDF17618495'}
      should.exists(propDDF17618495,'DDF17618495 not exist in gPropertiesNew')
      should.exists(propDDF17618495.merged)
      propDDF17618495.merged.should.equal('TRBC3663357')
      prop = await gProperties_expected.findOne {_id:'TRBC3663357'}
      should.exists(prop,'TRBC3663357 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return
    
    # NOTE: RHB需要merge到DDF
    it 'should merge RHBH4162866,DDF25559593', ()->
      try
        prop = await gRabhRecords.findOne {_id:'RHBH4162866'}
        await importProp {srcType:'rahb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF25559593'}
        await importProp {srcType:'ddf',prop}
        rhbProp = await gProperties.findOne {_id:'RHBH4162866'}
        ddfProp = await gProperties.findOne {_id:'DDF25559593'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(ddfProp)
      should.not.exists(ddfProp.merged)
      ddfProp.origId?.length.should.be.exactly(2)
      expectedId = ['RHBH4162866','DDF25559593']
      for id in ddfProp.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      should.exists(rhbProp)
      should.exists(rhbProp.merged)
      rhbProp.merged.should.equal('DDF25559593')
      prop = await gProperties_expected.findOne {_id:'DDF25559593'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(ddfProp)
      should.deepEqual(prop,ddfProp)
      return

    it 'should merge TRBW5973208,RHBH4163056', ()->
      try
        prop = await gTrebRecords.findOne {_id:'TRBW5973208'}
        await importProp {srcType:'treb',prop}
        prop = await gRabhRecords.findOne {_id:'RHBH4163056'}
        await importProp {srcType:'rahb',prop}
        trebProp = await gProperties.findOne {_id:'TRBW5973208'}
        rahbProp = await gProperties.findOne {_id:'RHBH4163056'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(trebProp)
      should.not.exists(trebProp.merged)
      trebProp.origId?.length.should.be.exactly(2)
      expectedId = ['TRBW5973208','RHBH4163056']
      for id in trebProp.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      should.exists(rahbProp)
      should.exists(rahbProp.merged)
      rahbProp.merged.should.equal('TRBW5973208')
      prop = await gProperties_expected.findOne {_id:'TRBW5973208'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(trebProp)
      should.deepEqual(prop,trebProp)
      return

    # 相同sid,status,uaddr,onD相距半个月内
    it 'should merge BRER2861975,DDF26654239', ()->
      try
        prop = await gBcreRecords.findOne {_id:'BRER2861975'}
        await importProp {srcType:'bcre',prop}
        prop = await gDDFRecords.findOne {_id:'DDF26654239'}
        await importProp {srcType:'ddf',prop}
        bcreProp = await gProperties.findOne {_id:'BRER2861975'}
        ddfProp = await gProperties.findOne {_id:'DDF26654239'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(bcreProp)
      should.not.exists(bcreProp.merged)
      bcreProp.origId?.length.should.be.exactly(3)
      expectedId = ['BRER2861975','DDF26654239','262883602']
      for id in bcreProp.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      should.exists(ddfProp)
      should.exists(ddfProp.merged)
      ddfProp.merged.should.equal('BRER2861975')
      prop = await gProperties_expected.findOne {_id:'BRER2861975'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(bcreProp)
      should.deepEqual(prop,bcreProp)
      return

    # 不同status 相同sid,uaddr,onD相距半个月内
    it 'should merge BRER2861625,DDF26648640', ()->
      try
        prop = await gBcreRecords.findOne {_id:'BRER2861625'}
        await importProp {srcType:'bcre',prop}
        prop = await gDDFRecords.findOne {_id:'DDF26648640'}
        await importProp {srcType:'ddf',prop}
        bcreProp = await gProperties.findOne {_id:'BRER2861625'}
        ddfProp = await gProperties.findOne {_id:'DDF26648640'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(bcreProp)
      should.not.exists(bcreProp.merged)
      bcreProp.origId?.length.should.be.exactly(3)
      expectedId = ['BRER2861625','DDF26648640','262883252']
      for id in bcreProp.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      should.exists(ddfProp)
      should.exists(ddfProp.merged)
      ddfProp.merged.should.equal('BRER2861625')
      prop = await gProperties_expected.findOne {_id:'BRER2861625'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(bcreProp)
      should.deepEqual(prop,bcreProp)
      return

    # 相同sid,status,uaddr,onD相距超过半个月内
    it 'should not merge BRER2862244,DDF26658854', ()->
      try
        prop = await gBcreRecords.findOne {_id:'BRER2862244'}
        await importProp {srcType:'bcre',prop}
        prop = await gDDFRecords.findOne {_id:'DDF26658854'}
        await importProp {srcType:'ddf',prop}
        bcreProp = await gProperties.findOne {_id:'BRER2862244'}
        ddfProp = await gProperties.findOne {_id:'DDF26658854'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(bcreProp)
      should.not.exists(bcreProp.merged)
      bcreProp.origId?.length.should.be.exactly(2)
      isExpected = bcreProp.origId.includes('DDF26658854')
      isExpected.should.be.exactly(false)
      should.exists(ddfProp)
      should.not.exists(ddfProp.merged)
      prop = await gProperties_expected.findOne {_id:'BRER2862244'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(bcreProp)
      should.deepEqual(prop,bcreProp)
      return

    it 'should merge DDF17234807,OTW1022974', ()->
      try
        await gProperties.deleteMany {_id:$in:['OTW1022974','DDF17234807']}
        prop = await gOrebRecords.findOne {_id:'23161105'}
        await importProp {srcType:'oreb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF17234807'}
        await importProp {srcType:'ddf',prop}
        item1 = await gProperties.findOne {_id:'OTW1022974'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['OTW1022974','DDF17234807']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF17234807 = await gProperties.findOne {_id:'DDF17234807'}
      should.exists(propDDF17234807,'DDF17234807 not exist in gProperties')
      should.exists(propDDF17234807.merged)
      propDDF17234807.merged.should.equal('OTW1022974')
      prop = await gProperties_expected.findOne {_id:'OTW1022974'}
      should.exists(prop,'OTW1022974 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

    # NOTE: 修改merge的打分逻辑后,相同得分的房源暂时不merge
    xit 'should merge OTW1302584,OTW1303188', ()->
      try
        await gProperties.deleteMany {_id:$in:['OTW1303188','OTW1302584']}
        prop = await gOrebRecords.findOne {_id:'34400651'}
        await importProp {srcType:'oreb',prop}
        prop = await gOrebRecords.findOne {_id:'34376922'}
        await importProp {srcType:'oreb',prop}
        item1 = await gProperties.findOne {_id:'OTW1303188'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['OTW1303188','OTW1302584']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propOTW1302584 = await gProperties.findOne {_id:'OTW1302584'}
      should.exists(propOTW1302584,'OTW1302584 not exist in gProperties')
      should.exists(propOTW1302584.merged)
      propOTW1302584.merged.should.equal('OTW1303188')
      prop = await gProperties_expected.findOne {_id:'OTW1303188'}
      should.exists(prop,'OTW1303188 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

    it 'should merge DDF27423090,EDME4406705', ()->
      try
        await gProperties.deleteMany {_id:$in:['EDME4406705','DDF27423090']}
        prop = await gDDFRecords.findOne {_id:'DDF27423090'}
        await importProp {srcType:'ddf',prop}
        prop = await gEdmRecords.findOne {_id:'EDME4406705'}
        propDDF27423090 = await gProperties.findOne {_id:'DDF27423090'}
        await importProp {srcType:'edm',prop}
        item1 = await gProperties.findOne {_id:'EDME4406705'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['EDME4406705','DDF27423090']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF27423090 = await gProperties.findOne {_id:'DDF27423090'}
      should.exists(propDDF27423090,'DDF27423090 not exist in gProperties')
      should.exists(propDDF27423090.merged)
      propDDF27423090.merged.should.equal('EDME4406705')
      prop = await gProperties_expected.findOne {_id:'EDME4406705'}
      should.exists(prop,'EDME4406705 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

    # NOTE: 修改merge的打分逻辑后,相同得分的房源暂时不merge
    xit 'should merge EDME4412300,EDME4412301', ()->
      try
        await gProperties.deleteMany {_id:$in:['EDME4412300','EDME4412301']}
        prop = await gEdmRecords.findOne {_id:'EDME4412300'}
        await importProp {srcType:'edm',prop}
        prop = await gEdmRecords.findOne {_id:'EDME4412301'}
        await importProp {srcType:'edm',prop}
        item1 = await gProperties.findOne {_id:'EDME4412301'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['EDME4412301','EDME4412300']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propEDME4412300 = await gProperties.findOne {_id:'EDME4412300'}
      should.exists(propEDME4412300,'EDME4412300 not exist in gProperties')
      should.exists(propEDME4412300.merged)
      propEDME4412300.merged.should.equal('EDME4412301')
      prop = await gProperties_expected.findOne {_id:'EDME4412301'}
      should.exists(prop,'EDME4412301 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

    #saletp first sale, then lease.
    #should have origId
    #different id, new is lower priority, set new as merged, old has all info
    # it 'import DDF15947109, then TRBX3264479, different m, should not merge', (done)->
    #   idsToRemove = ['DDF15947109','TRBX3264479']
    #   gPropertiesNew.deleteMany {_id:$in:idsToRemove},(err,ret)->
    #     gProperties.findOne {_id:'DDF15947109'},(err,prop)->
    #       importProp {srcType:'property',prop},(err,newItem)->
    #         gProperties.findOne {_id:'TRBX3264479'},(err,prop)->
    #           importProp {srcType:'property',prop},(err,newItem)->
    #             gPropertiesNew.findOne {_id:'DDF15947109'},(err,item1)->
    #               should.exists(item1)
    #               should.not.exists(item1.merged)
    #               gPropertiesNew.findOne {_id:'TRBX3264479'},(err,insertItem)->
    #                 should.exists(insertItem)
    #                 deletDateFields(insertItem)
    #                 gProperties_expected.findOne {_id:'TRBX3264479'},\
    #                 (err,prop)->
    #                   console.log insertItem
    #                   deletDateFields(prop)
    #                   should.deepEqual(insertItem.origId,['TRBX3264479'])
    #                   should.deepEqual(insertItem.srcOrig,['TRB'])
    #                   for fld in ['srcOrig','origId']
    #                     delete insertItem[fld]
    #                     delete prop[fld]
                      
    #                   should.deepEqual(prop,insertItem)
    #                   gPropertiesImportLog.findOne {id:'TRBX3264479'},(err,log)->
    #                     console.log 'log.offD',log.offD
    #                     should.exists(log.offD)
    #                     done()
    #                     return
  
  # describe 'should not update merged prop',->
    # TODO: 相同source不merge，当merge逻辑变更时需要测试
    # xit 'should not update merged prop but save in log', ()->
    #   trebId1 = 'TRBC457439'
    #   trebId2 = 'TRBC457444'
    #   try
    #     await gProperties.deleteMany {_id:{$in:[trebId1,trebId2]}}
    #     trebRecord1 = await gTrebRecords.findOne {_id:trebId1}
    #     trebRecord2 = await gTrebRecords.findOne {_id:trebId2}
    #     await importProp {srcType:'treb',prop:trebRecord1}
    #     await importProp {srcType:'treb',prop:trebRecord2}
    #     prop1 = await gProperties.findOne {_id:trebId1}
    #   catch err
    #     debug.error err
    #     should.not.exist(err)

    #   should.deepEqual(prop1.merged,'TRBC457444')
    #   prop2 = await gProperties.findOne {_id:trebId2}
    #   should.not.exist(prop2.merged)
    #   trebRecord1.testFld = 'test'
    #   try
    #     await importProp {srcType:'treb',prop:trebRecord1}
    #     prop3 = await gProperties.findOne {_id:trebId1}
    #   catch err
    #     debug.error err
    #     should.not.exist(err)

    #   should.deepEqual(prop3.merged,'TRBC457444')
    #   should.not.exist(prop3.testFld)
    #   log = await gPropertiesImportLog.findOne {_id:'TRBC457439_treb'}
    #   should.deepEqual(log.wasMerged,1)
    #   return

  describe 'should add bnd Correctly',->
    it 'should not update merged prop but save in log', ()->
      trebId1 = 'TRBC5205529'
      try
        trebRecord1 = await gTrebRecords.findOne {_id:trebId1}
        await importProp {srcType:'treb',prop:trebRecord1}
        prop1 = await gProperties.findOne {_id:trebId1}
      catch err
        debug.error err
        should.not.exist(err)
        
      should.exists(prop1.bndCity)
      return

  describe 'should merge when import ddf before treb',->
    it 'should merge DDF26855748,TRBW8311740, and uaddr is same', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBW8311740','DDF26855748']}
        prop = await gDDFRecords.findOne {_id:'DDF26855748'}
        await importProp {srcType:'ddf',prop}
        prop = await gTrebRecords.findOne {_id:'TRBW8311740'}
        await importProp {srcType:'treb',prop}
        item1 = await gProperties.findOne {_id:'TRBW8311740'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1,'TRBW8311740 not exists in properties')
      should.not.exists(item1.merged)
      item1.uaddr.should.equal('CA:ON:MISSISSAUGA:3438 ENNISKILLEN CIR')
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['TRBW8311740','DDF26855748']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF26855748 = await gProperties.findOne {_id:'DDF26855748'}
      should.exists(propDDF26855748,'propDDF26855748 not exists in properties')
      should.exists(propDDF26855748.merged,'propDDF26855748 not exists merged field')
      propDDF26855748.merged.should.equal('TRBW8311740')
      prop = await gProperties_expected.findOne {_id:'TRBW8311740'}
      should.exists(prop,'TRBW8311740 not exists in properties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

    it 'should merge DDF28043373,TRBN12027684 when different status and sid is same', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBN12027684','DDF28043373']}
        prop = await gDDFResoRecords.findOne {_id:'DDF28043373'}
        await importProp {srcType:'creaReso',prop}
        prop = await gResoRecords.findOne {_id:'TRBN12027684'}
        await importProp {srcType:'reso',prop}
        item1 = await gProperties.findOne {_id:'TRBN12027684'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1,'TRBN12027684 not exists in properties')
      should.not.exists(item1.merged)
      item1.uaddr.should.equal('CA:ON:AURORA:82 CONKLIN CRES')
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['TRBN12027684','DDF28043373']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF28043373 = await gProperties.findOne {_id:'DDF28043373'}
      should.exists(propDDF28043373,'propDDF28043373 not exists in properties')
      should.exists(propDDF28043373.merged,'propDDF28043373 not exists merged field')
      propDDF28043373.merged.should.equal('TRBN12027684')
      prop = await gProperties_expected.findOne {_id:'TRBN12027684'}
      should.exists(prop,'TRBN12027684 not exists in properties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return
    
  describe 'should merge when ddf board not in 82, 85, 86',->
    it 'should merge DDF27139017,TRBC9016713, and uaddr is same', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBC9016713','DDF27139017']}
        prop = await gTrebRecords.findOne {_id:'TRBC9016713'}
        await importProp {srcType:'treb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF27139017'}
        await importProp {srcType:'ddf',prop}
        item1 = await gProperties.findOne {_id:'TRBC9016713'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1,'TRBC9016713 not exists in properties')
      should.not.exists(item1.merged)
      item1.uaddr.should.equal('CA:ON:TORONTO:66 FOREST MANOR RD')
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['TRBC9016713','DDF27139017']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propDDF27139017 = await gProperties.findOne {_id:'DDF27139017'}
      should.exists(propDDF27139017,'propDDF27139017 not exists in properties')
      should.exists(propDDF27139017.merged,'propDDF27139017 not exists merged field')
      propDDF27139017.merged.should.equal('TRBC9016713')
      prop = await gProperties_expected.findOne {_id:'TRBC9016713'}
      should.exists(prop,'TRBC9016713 not exists in properties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return

  describe 'should not merge RM prop',->
    tests = [
      {id:'RM1-63631'},
      {id:'RM1-63632'}
    ]
    tests.forEach (test)->
      it 'should return only propToKeep when src is RM', ()->
        try
          prop = await gProperties.findOne {id:test.id}
          ret = await saveToMaster.findDuplicatePropAndDeterminePriority {
            prop,
            metaInfo:{},
            Properties:gProperties
          }
        catch err
          debug.error err
          should.not.exist(err)
        should.exists(ret.propToKeep,"#{test.id} has no propToKeep")
        should.not.exists(ret.propToMerge,"#{test.id} has propToMerge")
        should.deepEqual(prop,ret.propToKeep)
        return
  describe 'test scoringPropMerge',->
    tests = [
      {prov:'ON',src:'RHB',score:0,pho:true}
      {prov:'ON',src:'RHB',score:-35,pho:false}
      {prov:'ON',src:'DDF',score:10,pho:true}
      {prov:'ON',src:'DDF',score:-25,pho:false}
      {prov:'BC',src:'TRB',score:50,pho:true}
      {prov:'ON',src:'TRB',score:70,pho:true}
      {prov:'ON',src:'TRB',city:'Toronto',score:80,pho:true}
      {prov:'ON',src:'TRB',city:'Markham',score:80,pho:true}
      {prov:'ON',src:'TRB',trbtp:['dta'],city:'Markham',score:70,pho:true}
      {prov:'ON',src:'BRE',score:50,pho:true}
      {prov:'BC',src:'BRE',score:60,pho:true}
      {prov:'ON',src:'OTW',score:50,pho:true}
      {prov:'ON',src:'OTW',city:'Ottawa',score:65,pho:true}
      {prov:'ON',src:'CLG',score:50,pho:true}
      {prov:'AB',src:'CLG',score:60,pho:true}
      {prov:'AB',src:'CLG',city:'Calgary',score:75,pho:true}
      {prov:'AB',src:'EDM',score:60,pho:true}
      {prov:'AB',src:'EDM',city:'Edmonton',score:75,pho:true}
      {prov:'ON',src:'CAR',score:50,pho:true}
      {prov:'ON',src:'CAR',city:'Mississauga',score:65,pho:true}
      {prov:'ON',src:'CAR',city:'Hamilton',score:65,pho:true}
      {prov:'ON',src:'CAR',city:'Burlington',score:65,pho:true}
    ]
    tests.forEach (test)->
      it 'should score as expected', ()->
        score = saveToMaster.scoringPropMerge(test)
        should.equal(test.score,score)
        return
  describe 'test merged prop uaddr changed',->
    it 'should not unset merged field when uaddr changed but can be merged', ()->
      try
        srcProp = await gProperties.findOne {_id:'DDF26329726'}
        prop = await gDDFRecords.findOne {_id:'DDF26329726'}
        await importProp {srcType:'ddf',prop}
        insertItem = await gProperties.findOne {_id:'DDF26329726'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(srcProp)
      srcProp.merged.should.equal(insertItem?.merged)
      srcProp.city.should.equal('Newmarket')
      srcProp.uaddr.should.equal('CA:ON:NEWMARKET:1100 DAVIS DR')
      insertItem.city.should.equal('Newmarket')
      # NOTE: not condos,no mfee，有origUnt 需要进行unit分离
      insertItem.uaddr.should.equal('CA:ON:NEWMARKET:1100 DAVIS DR')
      return
    
    it 'should unset merged field when uaddr changed and can not be merged', ()->
      try
        srcProp = await gProperties.findOne {_id:'DDF27326273'}
        prop = await gDDFRecords.findOne {_id:'DDF27326273'}
        await importProp {srcType:'ddf',prop}
        insertItem = await gProperties.findOne {_id:'DDF27326273'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(srcProp)
      srcProp.merged.should.equal('TRBW9267726')
      should.not.exists(insertItem?.merged)
      srcProp.uaddr.should.equal('CA:ON:MILTON:716 MAIN ST E')
      insertItem.uaddr.should.equal('CA:ON:MILTON TM TIMBERLEA:716 MAIN ST E')
      return

  describe 'test treb source prop merge',->
    @timeout(30000)
    it 'should merge TRBW10407379 to TRBW9815740', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBW10407379','TRBW9815740']}
        prop = await gTrebRecords.findOne {_id:'TRBW9815740'}
        await importProp {srcType:'treb',prop}
        prop = await gTrebRecords.findOne {_id:'TRBW10407379'}
        await importProp {srcType:'treb',prop}
        item1 = await gProperties.findOne {_id:'TRBW9815740'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(2)
      expectedId = ['TRBW10407379','TRBW9815740']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      item1.aSIDs?.length.should.be.exactly(2)
      expectedSid = ['W10407379','W9815740']
      sids = item1.aSIDs.map((item)->return item.sid)
      for sid in sids
        isExpected = expectedSid.includes(sid)
        isExpected.should.be.exactly(true)
      ids = item1.aSIDs.map((item)->return item.id)
      for id in ids
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propTRBW10407379 = await gProperties.findOne {_id:'TRBW10407379'}
      should.exists(propTRBW10407379,'TRBW10407379 not exist in gProperties')
      should.exists(propTRBW10407379.merged)
      propTRBW10407379.merged.should.equal('TRBW9815740')
      prop = await gProperties_expected.findOne {_id:'TRBW9815740'}
      should.exists(prop,'TRBW9815740 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return
    
    it 'should merge TRBW10407341,DDF27602399 with TRBW9843613', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBW10407341','TRBW9843613','DDF27602399']}
        prop = await gTrebRecords.findOne {_id:'TRBW9843613'}
        await importProp {srcType:'treb',prop}
        prop = await gTrebRecords.findOne {_id:'TRBW10407341'}
        await importProp {srcType:'treb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF27602399'}
        await importProp {srcType:'ddf',prop}
        item1 = await gProperties.findOne {_id:'TRBW9843613'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(3)
      expectedId = ['DDF27602399','TRBW10407341','TRBW9843613']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      item1.aSIDs?.length.should.be.exactly(3)
      expectedSid = ['W9843613','W10407341']
      sids = item1.aSIDs.map((item)->return item.sid)
      for sid in sids
        isExpected = expectedSid.includes(sid)
        isExpected.should.be.exactly(true)
      ids = item1.aSIDs.map((item)->return item.id)
      for id in ids
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      propTRBW10407341 = await gProperties.findOne {_id:'TRBW10407341'}
      should.exists(propTRBW10407341,'TRBW10407341 not exist in gProperties')
      should.exists(propTRBW10407341.merged)
      propTRBW10407341.merged.should.equal('TRBW9843613')

      propDDF27602399 = await gProperties.findOne {_id:'DDF27602399'}
      should.exists(propDDF27602399,'DDF27602399 not exist in gProperties')
      should.exists(propDDF27602399.merged)
      propDDF27602399.merged.should.equal('TRBW9843613')

      prop = await gProperties_expected.findOne {_id:'TRBW9843613'}
      should.exists(prop,'TRBW9843613 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return
    
    it 'should merge TRBW10407385,DDF27609637,DDF27609700 with TRBW10403056', ()->
      try
        await gProperties.deleteMany {_id:$in:['TRBW10407385','TRBW10403056','DDF27609637','DDF27609700']}
        prop = await gTrebRecords.findOne {_id:'TRBW10403056'}
        await importProp {srcType:'treb',prop}
        prop = await gTrebRecords.findOne {_id:'TRBW10407385'}
        await importProp {srcType:'treb',prop}
        prop = await gDDFRecords.findOne {_id:'DDF27609637'}
        await importProp {srcType:'ddf',prop}
        prop = await gDDFRecords.findOne {_id:'DDF27609700'}
        await importProp {srcType:'ddf',prop}
        item1 = await gProperties.findOne {_id:'TRBW10403056'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(item1)
      should.not.exists(item1.merged)
      item1.origId?.length.should.be.exactly(4)
      expectedId = ['DDF27609700','DDF27609637','TRBW10407385','TRBW10403056']
      for id in item1.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      item1.aSIDs?.length.should.be.exactly(4)
      expectedSid = ['W10403056','W10407385','40672568']
      sids = item1.aSIDs.map((item)->return item.sid)
      for sid in sids
        isExpected = expectedSid.includes(sid)
        isExpected.should.be.exactly(true)
      ids = item1.aSIDs.map((item)->return item.id)
      for id in ids
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      mergeIds = ['DDF27609700','DDF27609637','TRBW10407385']
      for id in mergeIds
        mergeProp = await gProperties.findOne {_id:id}
        should.exists(mergeProp,"#{id} not exist in gProperties")
        should.exists(mergeProp.merged)
        mergeProp.merged.should.equal('TRBW10403056')
      prop = await gProperties_expected.findOne {_id:'TRBW10403056'}
      should.exists(prop,'TRBW10403056 not exist in gProperties_expected')
      deletDateFields(prop)
      deletDateFields(item1)
      should.deepEqual(prop,item1)
      return
    
    # NOTE: 由于没有pho时取消src==TRB限制,import顺序会影响merge的检索结果,都存在pho时TRB房源不会查找相同source
    it 'should unset merged field when pho changed and re search and merge', ()->
      try
        propBefore = await gProperties.findOne {_id:'TRBW9815740'}
        prop = await gTrebRecords.findOne {_id:'TRBW10407379'}
        prop.picNum = 25
        await importProp {srcType:'treb',prop}
        insertItem = await gProperties.findOne {_id:'TRBW10407379'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(propBefore)
      should.not.exists(propBefore.merged)
      propBefore.aSIDs?.length.should.be.exactly(2)

      propAfter = await gProperties.findOne {_id:'TRBW9815740'}
      should.not.exists(propAfter.merged)
      propAfter.origId?.length.should.be.exactly(1)
      propAfter.aSIDs?.length.should.be.exactly(1)

      should.not.exists(insertItem?.merged)
      insertItem.origId?.length.should.be.exactly(1)
      insertItem.origId?[0].should.be.exactly('TRBW10407379')
      insertItem.aSIDs?.length.should.be.exactly(0)
      return

  describe 'test reso import and merge',->
    it 'should merge TRBX11897237 to CAR40683655', ()->
      try
        prop = await gResoRecords.findOne {_id:'TRBX11897237'}
        await importProp {srcType:'reso',prop}
        propTRBX11897237 = await gProperties.findOne {_id:'TRBX11897237'}
        propExpTRBX11897237 = await gProperties_expected.findOne {_id:'TRBX11897237'}
        propCAR40683655 = await gProperties.findOne {_id:'CAR40683655'}
        propExpCAR40683655 = await gProperties_expected.findOne {_id:'CAR40683655'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(propTRBX11897237)
      should.exists(propExpTRBX11897237)
      should.exists(propCAR40683655)
      should.exists(propExpCAR40683655)
      equalASIDs = isEqualASIDs propCAR40683655.aSIDs,propExpCAR40683655.aSIDs
      equalASIDs.should.be.exactly(true)
      delete propCAR40683655.aSIDs
      delete propExpCAR40683655.aSIDs
      deletDateFields(propTRBX11897237)
      deletDateFields(propExpTRBX11897237)
      deletDateFields(propCAR40683655)
      deletDateFields(propExpCAR40683655)
      should.deepEqual(propTRBX11897237,propExpTRBX11897237)
      should.deepEqual(propCAR40683655,propExpCAR40683655)
      return

    it 'should merge OTW1421250 to TRBX10845526', ()->
      try
        prop = await gResoRecords.findOne {_id:'TRBX10845526'}
        await importProp {srcType:'reso',prop}
        propTRBX10845526 = await gProperties.findOne {_id:'TRBX10845526'}
        propExpTRBX10845526 = await gProperties_expected.findOne {_id:'TRBX10845526'}
        propOTW1421250 = await gProperties.findOne {_id:'OTW1421250'}
        propExpOTW1421250 = await gProperties_expected.findOne {_id:'OTW1421250'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(propTRBX10845526)
      should.exists(propExpTRBX10845526)
      should.exists(propOTW1421250)
      should.exists(propExpOTW1421250)
      equalASIDs = isEqualASIDs propTRBX10845526.aSIDs,propExpTRBX10845526.aSIDs
      equalASIDs.should.be.exactly(true)
      delete propTRBX10845526.aSIDs
      delete propExpTRBX10845526.aSIDs
      deletDateFields(propTRBX10845526)
      deletDateFields(propExpTRBX10845526)
      deletDateFields(propOTW1421250)
      deletDateFields(propExpOTW1421250)
      should.deepEqual(propTRBX10845526,propExpTRBX10845526)
      should.deepEqual(propOTW1421250,propExpOTW1421250)
      return

  describe 'test different sid onD merge',->
    # 相同status,uaddr,不同sid,onD相距5天
    it 'should merge TRBW12207877,CAR40734284', ()->
      try
        prop = await gResoRecords.findOne {_id:'TRBW12207877'}
        await importProp {srcType:'reso',prop}
        prop = await gCarRecords.findOne {_id:'CAR40734284'}
        await importProp {srcType:'car',prop}
        resoProp = await gProperties.findOne {_id:'TRBW12207877'}
        carProp = await gProperties.findOne {_id:'CAR40734284'}
      catch err
        debug.error err
        should.not.exist(err)
      should.exists(resoProp)
      should.not.exists(resoProp.merged)
      resoProp.origId?.length.should.be.exactly(2)
      expectedId = ['TRBW12207877','CAR40734284']
      for id in resoProp.origId
        isExpected = expectedId.includes(id)
        isExpected.should.be.exactly(true)
      should.exists(carProp)
      should.exists(carProp.merged)
      carProp.merged.should.equal('TRBW12207877')
      prop = await gProperties_expected.findOne {_id:'TRBW12207877'}
      should.exists(prop)
      deletDateFields(prop)
      deletDateFields(resoProp)
      should.deepEqual(prop,resoProp)
      return

    # 相同status,uaddr,不同sid,onD相距10天, 需要手动修改onD
    it 'should not merge TRBW12207877,CAR40734284', ()->
      try
        await gProperties.deleteOne {_id:'TRBW12207877'}
        await gProperties.deleteOne {_id:'CAR40734284'}
        prop = await gResoRecords.findOne {_id:'TRBW12207877'} # onD: 2025-06-04
        await importProp {srcType:'reso',prop}
        prop = await gCarRecords.findOne {_id:'CAR40734284'}
        prop.ListingContractDate = '2025-06-14'
        await importProp {srcType:'car',prop}
        resoProp = await gProperties.findOne {_id:'TRBW12207877'}
        carProp = await gProperties.findOne {_id:'CAR40734284'}
      catch err
        debug.error err
        should.not.exist(err)

      should.exists(resoProp)
      should.not.exists(resoProp.merged)
      should.not.exists(resoProp.aSIDs)
      should.deepEqual(resoProp.origId,['TRBW12207877'])

      should.exists(carProp)
      should.not.exists(carProp.merged)
      should.not.exists(carProp.aSIDs)
      should.deepEqual(carProp.origId,['CAR40734284'])
      return
