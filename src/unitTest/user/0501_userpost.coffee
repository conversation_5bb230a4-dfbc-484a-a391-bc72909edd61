should = require('should')
helpers = require('../00_common/helpers')
request = null
UserCol = null
PnTokenCol = null
# ./test.sh -f user/0501_userpost.js
describe 'user api test: userpost',->
  before ->
    @timeout(30000)
    request = helpers.getServer()
    UserCol = helpers.COLLECTION 'chome','user'
    PnTokenCol = helpers.COLLECTION 'chome','pn_token'
    dbs = [
      { db: 'chome', table: 'pn_token' }
    ]
    
    return new Promise (resolve, reject) ->
      helpers.cleanDBs dbs, () ->
        helpers.checkAndsetUpFixture {folder:__dirname}, ()->
          helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
            if err
              console.log 'error when loginAndThen'
              return reject(err)
            helpers.userCookies.usera = cookie
            helpers.userCookies.nouser = null
            setTimeout resolve, 2500

  describe 'user change pwd', ->
    tests=[
        {
          desc:'should get err, pwd is not current'
          post:{
            }
          expected:{ ok: 0, err: 'Need current password' }
        },
        {
          desc:'should get err, pwd does not match'
          post:{
            oldPwd: '12345623'
            }
          expected:{ ok: 0, err: 'Current password does not match' }
        },
        {
          desc:'should get err, no new pwd'
          post:{
            oldPwd: '123456'
            pwd: '1234'
            }
          expected:{ ok: 0, err: 'New password too short' }
        },
        {
          desc:'should get err, pwd and pwdr does not match'
          post:{
            oldPwd: '123456'
            pwd: '1234578'
            pwdR: '12345678'
            }
          expected:{ ok: 0, err: 'New passwords do not match' }
        },
        {
          desc:'should get ok'
          post:{
            oldPwd: '123456'
            pwd: '12345678'
            pwdR: '12345678'
            }
          expected:{ ok: 1 }
        },
    ]
    tests.forEach (test)->
      it test.desc, (done)->
        request
          .post('1.5/user/changePwd')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', helpers.userCookies.usera)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, res)->
            # console.log res.body
            should.exists(res.body)
            for k,v of test.expected
              should.exists(res.body[k])
              res.body[k].should.be.exactly(v)
            done()
        return

  describe 'update badge count', ->
    tests=[
      {
        desc:'should get err, missing parameters'
        post:{}
        expected:{
          'nouser':{ ok: 0, e: 'Bad Parameter' },
          'usera':{ ok: 0, e: 'Bad Parameter' }
        }
      },
      {
        desc:'should get err, missing pn parameter'
        post:{
          cnt: 5
        }
        expected:{
          'nouser':{ ok: 0, e: 'Bad Parameter' },
          'usera':{ ok: 0, e: 'Bad Parameter' }
        }
      },
      {
        desc:'nouser should get err, invalid pn token length usera update badge success'
        post:{
          pnToken: 'ios:123',
          cnt: 5
        }
        expected:{
          'nouser':{ ok: 0, e: 'No valid tokens found' },
          'usera':{ ok: 1 }
        }
      },
      {
        desc:'nouser should get err, invalid pn token format usera update badge success'
        post:{
          pnToken: '60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43',
          os: 'invalid',
          cnt: 5
        }
        expected:{
          'nouser':{ ok: 0, e: 'No valid tokens found' },
          'usera':{ ok: 1 }
        }
      },
      {
        desc:'should get err, negative count'
        post:{
          pnToken: 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43',
          cnt: -1
        }
        expected:{
          'nouser':{ ok: 0, e: 'Count value out of range' },
          'usera':{ ok: 0, e: 'Count value out of range' }
        }
      },
      {
        desc:'should get err, non-numeric count'
        post:{
          pnToken: 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43',
          cnt: 'invalid'
        }
        expected:{
          'nouser':{ ok: 0, e: 'Invalid count value' },
          'usera':{ ok: 0, e: 'Invalid count value' }
        }
      },
      {
        desc:'should get ok with count 0'
        post:{
          pnToken: 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43',
          cnt: 0
        }
        expected:{
          'nouser':{ ok: 1 },
          'usera':{ ok: 1 }
        }
      },
      {
        desc:'should get ok with valid parameters'
        post:{
          pnToken: 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43',
          cnt: 5
        }
        expected:{
          'nouser':{ ok: 1 },
          'usera':{ ok: 1 }
        }
      }
    ]
    users = ['nouser', 'usera']
    tests.forEach (test)->
      users.forEach (user)->
        it test.desc + ' ' + user, ()->
          @timeout(30000)
          return new Promise((resolve, reject)->
            request
              .post('1.5/settings/badge')
              .set('Accept', 'application/json')
              .set('Cookie', ['apsv=appDebug'])
              .set('Cookie', helpers.userCookies[user])
              .expect('Content-Type', /json/)
              .send(test.post)
              .end (err, res)->
                if err
                  return reject(err)
                should.exists(res.body)
                for k,v of test.expected[user]
                  should.exists(res.body[k])
                  res.body[k].should.be.exactly(v)
                return resolve() unless test.expected[user].ok is 1
                if user is 'usera'
                  UserCol.findOne {eml:'<EMAIL>'},(err,curUser)->
                    console.error err if err
                    should.exist(curUser)
                    should.exist(curUser.pnBadge)
                    curUser.pnBadge.should.be.equal(test.post.cnt)
                    resolve()
                else
                  PnTokenCol.findOne {_id:test.post.pnToken},(err,pn)->
                    console.error err if err
                    should.exist(pn)
                    should.exist(pn.pnBadge)
                    pn.pnBadge.should.be.equal(test.post.cnt)
                    resolve()
          )

    # 测试并发更新
    it 'should handle concurrent badge updates correctly', ->
      @timeout 30000
      pnToken = 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e43'
      
      # 先设置初始值
      await PnTokenCol.updateOne(
        {_id: pnToken},
        {$set: {pnBadge: 0}},
        {upsert: true}
      )
      
      # 创建5个并发请求，每个请求后立即验证
      for i in [1..5]
        # 发送请求
        res = await new Promise (resolve, reject) ->
          request
            .post '1.5/settings/badge'
            .set 'Accept', 'application/json'
            .set 'Cookie', ['apsv=appDebug']
            .set 'Cookie', helpers.userCookies.nouser
            .send
              pnToken: pnToken
              cnt: i
            .end (err, res) ->
              if err
                return reject(err)
              resolve res.body
        
        # 验证请求成功
        res.ok.should.be.equal 1
        
        # 立即验证数据库中的值
        pn = await PnTokenCol.findOne {_id: pnToken}
        should.exist pn
        should.exist pn.pnBadge
        pn.pnBadge.should.be.equal i  # 验证值是否与当前请求的值相同
      
      # 最后再次验证最终值
      pn = await PnTokenCol.findOne {_id: pnToken}
      should.exist pn
      should.exist pn.pnBadge
      pn.pnBadge.should.be.equal 5  # 最终值应该是5

    # 测试历史数据兼容性
    it 'should handle legacy data without pnBadge field', ->
      @timeout 30000
      pnToken = 'ios:60896662a4d49e0712a093ccd3ced0f4fc0b742feed33c93faca7d829c366e44'
      
      return new Promise (resolve, reject) ->
        # 先创建一个没有pnBadge字段的文档
        PnTokenCol.insertOne {_id: pnToken}, (err) ->
          if err
            return reject(err)
          
          # 更新badge
          request
            .post '1.5/settings/badge'
            .set 'Accept', 'application/json'
            .set 'Cookie', ['apsv=appDebug']
            .set 'Cookie', helpers.userCookies.nouser
            .send
              pnToken: pnToken
              cnt: 1
            .end (err, res) ->
              if err
                return reject(err)
              
              res.body.ok.should.be.equal 1
              
              # 验证pnBadge字段被正确创建
              PnTokenCol.findOne {_id: pnToken}, (err, pn) ->
                if err
                  return reject(err)
                should.exist pn
                should.exist pn.pnBadge
                pn.pnBadge.should.be.equal 1
                resolve()

  describe 'update push notification location', ->
    tests=[
      {
        desc:'should get err, missing pn parameter'
        post:{}
        expected:{ e: 'Bad Parameter' }
      },
      {
        desc:'should get ok with valid ios pn location'
        post:{
          pn: 'ios:fcm:test_token;43.6532,-79.3832'
        }
        expected:{ r: 'OK' }
      },
      {
        desc:'should get ok with valid android pn location'
        post:{
          pn: 'android:fcm:test_token;43.6532,-79.3832'
        }
        expected:{ r: 'OK' }
      },
      {
        desc:'should get ok with pn without location'
        post:{
          pn: 'ios:fcm:test_token'
        }
        expected:{ r: 'OK' }
      }
    ]
    tests.forEach (test)->
      it test.desc, ()->
        return new Promise((resolve, reject)->
          request
            .post('1.5/user/updPn')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies.usera)
            .expect('Content-Type', /json/)
            .send(test.post)
            .end (err, res)->
              if err
                return reject(err)
              should.exists(res.body)
              for k,v of test.expected
                should.exists(res.body[k])
                res.body[k].should.be.exactly(v)
              resolve()
        )