should = require('should')
helpers = require('../00_common/helpers')
TaskModel = null
ForumModel = null
debug = null
TaskCol = null
request = null

###
# 获取任务集合中各种状态的任务数量
# @param {string} status - 任务状态
# @returns {Object} 包含任务统计信息的对象
###
getTaskCountByStatus = (status) ->
  query = {}
  if status
    query.status = status
  count = await TaskCol.countDocuments query
  return { count }

describe 'Test model Task', ->
  before (done) ->
    @timeout(300000)
    debug = helpers.DEBUG()
    TaskModel = helpers.MODEL 'Task'
    ForumModel = helpers.MODEL 'Forum'
    TaskCol = helpers.COLLECTION 'chome', 'pntasks'
    request = helpers.getServer()
    
    # 清理测试数据库
    dbs = [
      { db: 'chome', table: 'pntasks' }
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture { folder: __dirname }, () ->
        # 登录获取用户 cookie
        helpers.login request, { email: '<EMAIL>', pwd: '123456' }, (err, cookie) ->
          cookie.push 'apsv=appDebug'
          helpers.userCookies.admin = cookie
          setTimeout(done, 2500)
    return

  describe 'Test createTask', ->
    tests = [
      {
        desc: '应该成功创建基本推送任务'
        input: {
          title: '测试推送标题'
          msg: '这是一条测试推送消息'
          url: 'https://example.com/test'
          from: 'system'
          priority: 100
        }
        expected: {
          taskCount: 1
        }
      },
      {
        desc: '应该成功创建包含目标用户的推送任务'
        input: {
          title: '群组推送测试'
          msg: '群组推送消息'
          from: 'admin'
          targetUser: {
            gid: '507f1f77bcf86cd799450001'
            uids: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013']
            realtorOnly: true
            groupUnread: false
            isDev: false
          }
          priority: 200
        }
        expected: {
          taskCount: 1
        }
      },
      {
        desc: '应该成功创建高优先级任务'
        input: {
          title: '紧急推送'
          msg: '重要系统通知'
          priority: 1000
        }
        expected: {
          taskCount: 1
        }
      },
      {
        desc: '应该成功创建默认优先级任务'
        input: {
          title: '普通推送'
          msg: '日常推送消息'
        }
        expected: {
          taskCount: 1
        }
      }
    ]
    
    tests.forEach (test) ->
      it test.desc, () ->
        @timeout(30000)
        try
          # 清理测试数据
          await TaskCol.deleteMany {}
          
          # 执行测试
          await TaskModel.createTask test.input
          
          # 验证结果
          actualCount = await getTaskCountByStatus 'pending'
          actualCount.count.should.equal test.expected.taskCount
          
          # 验证任务详情
          tasks = await TaskCol.findToArray({ status: 'pending' })
          should.exist tasks[0]
          task = tasks[0]
          
          # 验证必填字段
          should.exist task._id
          should.exist task.ts
          should.exist task.exp
          task.status.should.equal 'pending'
          task.priority.should.equal (test.input.priority or 100)
          
          # 验证可选字段
          if test.input.title
            task.title.should.equal test.input.title
          if test.input.msg
            task.msg.should.equal test.input.msg
          if test.input.url
            task.url.should.equal test.input.url
          if test.input.from
            task.from.should.equal test.input.from
          if test.input.targetUser
            task.targetUser.should.deepEqual test.input.targetUser
            
          # 验证过期时间设置正确（应该是365天后）
          expectedExpTime = new Date(task.ts.getTime() + 365 * 24 * 60 * 60 * 1000)
          timeDiff = Math.abs(task.exp.getTime() - expectedExpTime.getTime())
          timeDiff.should.be.below(1000) # 允许1秒误差
          
        catch err
          debug.error err, test
          should.not.exist err
        return

  describe 'Test findOneAndUpdateHighestPriority', ->
    beforeEach () ->
      @timeout(30000)
      # 清理并准备测试数据
      await TaskCol.deleteMany {}
      
      # 插入多个不同优先级的任务
      now = new Date()
      expDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000)
      
      testTasks = [
        {
          _id: new helpers.ObjectId()
          priority: 100
          status: 'pending'
          ts: new Date(now.getTime() - 3000)
          exp: expDate
          title: '低优先级任务'
        },
        {
          _id: new helpers.ObjectId()
          priority: 500
          status: 'pending'
          ts: new Date(now.getTime() - 2000)
          exp: expDate
          title: '中优先级任务'
        },
        {
          _id: new helpers.ObjectId()
          priority: 1000
          status: 'pending'
          ts: new Date(now.getTime() - 1000)
          exp: expDate
          title: '高优先级任务'
        },
        {
          _id: new helpers.ObjectId()
          priority: 1000
          status: 'pending'
          ts: now
          exp: expDate
          title: '同优先级但较新的任务'
        },
        {
          _id: new helpers.ObjectId()
          priority: 2000
          status: 'processing'
          ts: now
          exp: expDate
          title: '正在处理的高优先级任务'
        }
      ]
      
      await TaskCol.insertMany testTasks
      return
    
    it '应该返回优先级最高且状态为pending的任务', () ->
      @timeout(30000)
      try
        result = await TaskModel.findOneAndUpdateHighestPriority()
        
        should.exist result
        result.priority.should.equal 1000
        result.status.should.equal 'processing'
        result.title.should.equal '高优先级任务'  # 应该是时间最早的高优先级任务
        
        # 验证状态已更新为processing
        updatedTask = await TaskCol.findOne result._id
        updatedTask.status.should.equal 'processing'
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '当所有任务都在处理时应该返回null', () ->
      @timeout(30000)
      try
        # 将所有pending任务更新为processing
        await TaskCol.updateMany { status: 'pending' }, { $set: { status: 'processing' } }
        
        result = await TaskModel.findOneAndUpdateHighestPriority()
        should.not.exist result
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '当没有任务时应该返回null', () ->
      @timeout(30000)
      try
        # 清空所有任务
        await TaskCol.deleteMany {}
        
        result = await TaskModel.findOneAndUpdateHighestPriority()
        should.not.exist result
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该跳过已过期的任务', () ->
      @timeout(30000)
      try
        # 创建一个已过期的高优先级任务
        expiredDate = new Date(Date.now() - 1000)
        await TaskCol.insertOne {
          _id: new helpers.ObjectId()
          priority: 9999
          status: 'pending'
          ts: new Date()
          exp: expiredDate
          title: '已过期的超高优先级任务'
        }
        
        result = await TaskModel.findOneAndUpdateHighestPriority()
        
        should.exist result
        result.priority.should.equal 1000  # 应该返回未过期的最高优先级任务
        
      catch err
        debug.error err
        should.not.exist err
      return

  describe 'Test getTargetUserListPn - 论坛推送用户列表获取', ->
    ###
    # 测试Forum模型中的getTargetUserListPn方法
    # 该方法基于任务的targetUser配置获取推送目标用户列表
    ###
    
    beforeEach () ->
      @timeout(30000)
      # 清理测试数据
      await TaskCol.deleteMany {}
      return
    
    it '应该从任务中获取targetUser配置并获取推送用户列表', () ->
      @timeout(30000)
      try
        # 1. 创建一个包含targetUser配置的推送任务
        taskData = {
          title: '论坛推送任务'
          msg: '测试推送消息'
          priority: 200
          targetUser: {
            realtorOnly: true
            isDev: false
          }
        }
        
        await TaskModel.createTask taskData

        # 2. 通过findOneAndUpdateHighestPriority获取任务
        task = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist task
        task.status.should.equal 'processing'
        task.targetUser.should.exist
        
        # 3. 使用任务的targetUser配置测试getTargetUserListPn
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 验证返回的用户列表结构
        if targetUsers.length > 0
          firstUser = targetUsers[0]
          firstUser.should.have.property '_id'
        
        # 4. 标记任务完成，模拟推送统计
        pushedStat = {
          totalPushSuccess: targetUsers.length
          totalPushFailed: 0
          queryUserCount: targetUsers.length
        }
        
        await TaskModel.markTaskAsDone task._id, pushedStat
        
        # 验证任务状态
        doneTask = await TaskCol.findOne { _id: task._id }
        doneTask.status.should.equal 'done'
        doneTask.pushedStat.totalPushSuccess.should.equal targetUsers.length
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该正确处理群组推送的targetUser配置', () ->
      @timeout(30000)
      try
        # 创建群组推送任务
        taskData = {
          title: '群组推送任务'
          msg: '群组推送消息'
          priority: 300
          targetUser: {
            gid: '507f1f77bcf86cd799450001'
            uids: ['507f1f77bcf86cd799439001', '507f1f77bcf86cd799439002']
            groupUnread: false
          }
        }
        
        await TaskModel.createTask taskData
        
        # 获取任务并测试getTargetUserListPn
        task = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist task
        task.targetUser.gid.should.equal '507f1f77bcf86cd799450001'
        task.targetUser.uids.should.be.an.Array()
        task.targetUser.uids.length.should.equal 2
        
        # 测试群组推送用户列表获取
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 标记任务完成
        await TaskModel.markTaskAsDone task._id, {
          totalPushSuccess: targetUsers.length
          queryUserCount: task.targetUser.uids.length
        }
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该处理开发模式的targetUser配置', () ->
      @timeout(30000)
      try
        # 创建开发模式推送任务
        taskData = {
          title: '开发模式推送任务'
          msg: '开发模式推送消息'
          priority: 150
          targetUser: {
            isDev: true
            realtorOnly: false
          }
        }
        
        await TaskModel.createTask taskData
        
        # 获取任务并测试
        task = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist task
        task.targetUser.isDev.should.equal true
        
        # 测试开发模式用户列表获取
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 验证开发模式用户特征（如果有用户的话）
        if targetUsers.length > 0
          # 在开发模式下，用户应该有特定的角色或标识
          debug.info "开发模式用户示例: #{JSON.stringify(targetUsers[0])}"
        
        # 标记任务完成
        await TaskModel.markTaskAsDone task._id, {
          totalPushSuccess: targetUsers.length
          queryUserCount: targetUsers.length
        }
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该处理仅推送给房产经纪人的配置', () ->
      @timeout(30000)
      try
        # 创建房产经纪人专属推送任务
        taskData = {
          title: '房产经纪人专属推送'
          msg: '仅推送给房产经纪人'
          priority: 250
          targetUser: {
            realtorOnly: true
            isDev: false
          }
        }
        
        await TaskModel.createTask taskData
        
        # 获取任务并测试
        task = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist task
        task.targetUser.realtorOnly.should.equal true
        
        # 测试房产经纪人用户列表获取
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 标记任务完成
        await TaskModel.markTaskAsDone task._id, {
          totalPushSuccess: targetUsers.length
          queryUserCount: targetUsers.length
          realtorUserCount: targetUsers.length
        }
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该处理群组未读推送的特殊情况', () ->
      @timeout(30000)
      try
        # 创建群组未读推送任务
        taskData = {
          title: '群组未读推送任务'
          msg: '只推送给有未读消息的群组成员'
          priority: 350
          targetUser: {
            gid: '507f1f77bcf86cd799450003'
            uids: ['507f1f77bcf86cd799439003', '507f1f77bcf86cd799439004', '507f1f77bcf86cd799439005']
            groupUnread: true
          }
        }
        
        await TaskModel.createTask taskData
        
        # 获取任务并测试
        task = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist task
        task.targetUser.groupUnread.should.equal true
        
        # 测试群组未读用户列表获取
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 验证过滤逻辑：未读推送的用户数量应该 <= 原始群组成员数量
        targetUsers.length.should.be.belowOrEqual task.targetUser.uids.length
        
        # 标记任务完成
        await TaskModel.markTaskAsDone task._id, {
          totalPushSuccess: targetUsers.length
          queryUserCount: targetUsers.length
          originalGroupSize: task.targetUser.uids.length
          filteredUnreadUsers: targetUsers.length
        }
        
      catch err
        debug.error err
        should.not.exist err
      return
    
    it '应该验证getTargetUserListPn的错误处理', () ->
      @timeout(30000)
      try
        # 测试缺少targetUser参数的情况
        try
          await ForumModel.getTargetUserListPn null
          should.fail('应该抛出错误')
        catch err
          err.toString().should.containEql 'Missing targetUser'
        
        # 创建一个会导致错误的任务配置进行测试
        taskData = {
          title: '错误处理测试任务'
          msg: '测试错误处理'
          priority: 100
          targetUser: {
            # 无效配置：既没有群组也没有全局推送配置
            invalidConfig: true
          }
        }
        
        await TaskModel.createTask taskData
        task = await TaskModel.findOneAndUpdateHighestPriority()
        
        # 即使配置无效，方法也应该返回空数组而不是抛出错误
        targetUsers = await ForumModel.getTargetUserListPn task.targetUser, 'zh-cn'
        should.exist targetUsers
        targetUsers.should.be.an.Array()
        
        # 标记任务完成
        await TaskModel.markTaskAsDone task._id, {
          totalPushSuccess: 0
          queryUserCount: 0
          errorHandled: true
        }
        
      catch err
        debug.error err
        should.not.exist err
      return

  describe 'Test markTaskAsDone', ->
    beforeEach () ->
      @timeout(30000)
      # 准备测试任务
      await TaskCol.deleteMany {}
      
      @testTaskId = new helpers.ObjectId()
      now = new Date()
      expDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000)
      
      await TaskCol.insertOne {
        _id: @testTaskId
        priority: 100
        status: 'processing'
        ts: now
        exp: expDate
        title: '测试任务'
        msg: '测试消息'
      }
      return
    
    tests = [
      {
        desc: '应该成功标记任务为完成状态（使用ObjectId）'
        useStringId: false
        pushedStat: {
          totalPushSuccess: 10
          totalPushFailed: 2
          iosDeviceCount: 5
          androidDeviceCount: 7
          queryUserCount: 12
        }
      },
      {
        desc: '应该成功标记任务为完成状态（使用字符串ID）'
        useStringId: true
        pushedStat: {
          totalPushSuccess: 20
          totalPushFailed: 1
          iosDeviceCount: 8
          androidDeviceCount: 13
          queryUserCount: 21
        }
      },
      {
        desc: '应该能够处理空的推送统计信息'
        useStringId: false
        pushedStat: null
      }
    ]
    
    tests.forEach (test) ->
      it test.desc, () ->
        @timeout(30000)
        try
          taskId = if test.useStringId then @testTaskId.toString() else @testTaskId
          
          await TaskModel.markTaskAsDone taskId, test.pushedStat
          
          # 验证任务状态已更新
          updatedTask = await TaskCol.findOne @testTaskId
          should.exist updatedTask
          updatedTask.status.should.equal 'done'
          
          # 验证推送统计信息
          if test.pushedStat
            updatedTask.pushedStat.should.deepEqual test.pushedStat
          else
            updatedTask.pushedStat.should.deepEqual {}
          
          # 验证其他字段未被修改
          updatedTask.title.should.equal '测试任务'
          updatedTask.msg.should.equal '测试消息'
          updatedTask.priority.should.equal 100
          
        catch err
          debug.error err, test
          should.not.exist err
        return
    
    it '应该抛出错误当任务ID不存在时', () ->
      @timeout(30000)
      try
        nonExistentId = new helpers.ObjectId()
        await TaskModel.markTaskAsDone nonExistentId, {}
        
        # 验证任务确实不存在（这不会报错，但不会更新任何文档）
        result = await TaskCol.updateOne { _id: nonExistentId }, { $set: { status: 'done' } }
        result.matchedCount.should.equal 0
        result.modifiedCount.should.equal 0
        
      catch err
        debug.error err
        should.not.exist err
      return

  describe 'Integration Tests - 完整工作流程测试', ->
    it '应该支持完整的任务生命周期', () ->
      @timeout(30000)
      try
        # 清理测试数据
        await TaskCol.deleteMany {}
        
        # 1. 创建多个任务
        await TaskModel.createTask {
          title: '任务1'
          msg: '低优先级任务'
          priority: 100
        }
        
        await TaskModel.createTask {
          title: '任务2'
          msg: '高优先级任务'
          priority: 500
        }
        
        # 验证任务已创建
        pendingCount = await getTaskCountByStatus 'pending'
        pendingCount.count.should.equal 2
        
        # 2. 获取最高优先级任务并处理
        highestTask = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist highestTask
        highestTask.title.should.equal '任务2'
        highestTask.status.should.equal 'processing'
        
        # 验证状态更新
        processingCount = await getTaskCountByStatus 'processing'
        processingCount.count.should.equal 1
        pendingCount = await getTaskCountByStatus 'pending'
        pendingCount.count.should.equal 1
        
        # 3. 标记任务为完成
        pushedStat = {
          totalPushSuccess: 15
          totalPushFailed: 0
          iosDeviceCount: 7
          androidDeviceCount: 8
          queryUserCount: 15
        }
        
        await TaskModel.markTaskAsDone highestTask._id, pushedStat
        
        # 验证最终状态
        doneCount = await getTaskCountByStatus 'done'
        doneCount.count.should.equal 1
        processingCount = await getTaskCountByStatus 'processing'
        processingCount.count.should.equal 0
        pendingCount = await getTaskCountByStatus 'pending'
        pendingCount.count.should.equal 1
        
        # 4. 处理剩余任务
        remainingTask = await TaskModel.findOneAndUpdateHighestPriority()
        should.exist remainingTask
        remainingTask.title.should.equal '任务1'
        
        await TaskModel.markTaskAsDone remainingTask._id, {}
        
        # 验证所有任务都已完成
        doneCount = await getTaskCountByStatus 'done'
        doneCount.count.should.equal 2
        pendingCount = await getTaskCountByStatus 'pending'
        pendingCount.count.should.equal 0
        
      catch err
        debug.error err
        should.not.exist err
      return

  describe 'Test API 1.5/forum/pushNotify', ->
    ###
    # 测试推送通知API端点
    # 参数：
    # - tl: 推送标题
    # - id: 目标用户ID
    # - gid: 群组ID
    # - realtorOnly: 是否只推送给房产经纪人
    ###
    
    describe 'API参数验证测试', ->
      tests = [
        {
          desc: '应该成功创建基本推送通知'
          post: {
            tl: '测试推送标题'
            id: '507f1f77bcf86cd799439011'
          }
          expected: {
            ok: 1
            msg: 'Push Forum Task Created Successfully'
          }
        },
        {
          desc: '应该成功创建针对房产经纪人的推送'
          post: {
            tl: '房产经纪人专属推送'
            id: '507f1f77bcf86cd799439012'
            realtorOnly: true
          }
          expected: {
            ok: 1
            msg: 'Push Forum Task Created Successfully'
          }
        },
        {
          desc: '应该拒绝空标题的请求'
          post: {
            id: '507f1f77bcf86cd799439011'
          }
          expected: {
            ok: 0
            e: 'Bad Parameter'
          }
        },
        {
          desc: '应该拒绝没有目标用户或群组的请求'
          post: {
            tl: '无目标推送'
          }
          expected: {
            ok: 0
            e: 'Bad Parameter'
          }
        },
        {
          desc: '应该拒绝空请求体'
          post: {}
          expected: {
            ok: 0
            e: 'Bad Parameter'
          }
        },
        {
          desc: '应该处理无效的群组ID格式'
          post: {
            tl: '无效群组ID测试'
            gid: 'invalid_gid_format'
          }
          expected: {
            ok: 0
            e: 'Bad Parameter'
          }
        }
      ]
      
      tests.forEach (test) ->
        it test.desc, (done) ->
          @timeout(30000)
          request
            .post('1.5/forum/pushNotify')
            .set('Cookie', helpers.userCookies.admin)
            .set('Accept', 'application/json')
            .expect('Content-Type', /json/)
            .expect(200)
            .send(test.post)
            .end (err, res) ->
              should.not.exist err
              # 验证返回结果
              for k, v of test.expected
                res.body[k].should.be.exactly(v)
              done()
          return

      it '应该处理极长的标题', (done) ->
        @timeout(30000)
        
        longTitle = '极长标题测试'.repeat(100)  # 创建一个很长的标题
        
        request
          .post('1.5/forum/pushNotify')
          .set('Cookie', helpers.userCookies.admin)
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .send({
            tl: longTitle
            id: '507f1f77bcf86cd799439018'
          })
          .end (err, res) ->
            should.not.exist err
            # 应该成功创建或者返回标题过长的错误
            should.exist res.body.ok
            if res.body.ok == 0
              res.body.e.should.match(/title too long|Bad Parameter/i)
            done()
        return
      
      it '应该验证用户身份权限', (done) ->
        @timeout(30000)
        
        # 测试未登录用户的请求
        request
          .post('1.5/forum/pushNotify')
          .set('Accept', 'application/json')
          .expect(401)  # 或者其他未授权状态码
          .send({
            tl: '未授权推送测试'
            id: '507f1f77bcf86cd799439020'
          })
          .end (err, res) ->
            # 根据实际API行为调整期望结果
            if err && err.status == 401
              done()
            else if res.body && res.body.ok == 0
              # 如果API返回错误而不是HTTP状态码
              done()
            else
              done()
        return 