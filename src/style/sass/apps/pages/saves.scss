@import '../../base';
@import '../components/crm';
@import '../../components/flashMessage';
@import '../components/followRealtorPage';
@import '../components/projList.scss';
@import '../components/propAction';
@import '../components/propCard.scss';
@import '../components/savesSearch.scss';
@import '../components/shareDialog.scss';
@import '../components/community/_cmtyCard.scss';
@import '../components/propShareModal.scss';
@import '../components/watching.scss';
@import '../components/watchingPopup.scss';
@import '../components/estimate/descriptionCard.scss';
@import '../components/home/<USER>';
@import '../pages/propNotes.scss';
@import '../components/checkNotification.scss';
@import '../components/boxSelectBtn.scss';
@import '../components/loader.scss';

.bar .mode-switch {
  padding: 12px 0px 8px 0px;
  width: 60px;
  max-width: 60px;
  text-align: center;
  overflow: hidden;
  font-size: 16px !important;
  display: inline-block;
  position: relative;
  white-space: nowrap;
  .fa{
    font-size: 24px !important;
  }
}
.iconPot {
  position: absolute;
  top: 16%;
  right: 2%;
  color: rgba(18, 18, 18, 0.6);
}
.bar .mode-switch span {
  position: relative;
  z-index: 20;
}

.saves-list-nav {
  position: fixed;
  top: 44px;
  height: 42px;
  width: 100%;
  background: #fff;
  z-index: 5;
  border-bottom: 0.5px solid rgb(245, 245, 245);
}
.list-nav{
    // background: #ffffff;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      background-color: transparent;
    }
  
    &-container {
      display: flex;
      align-items: center;
      flex: 1;
      height: 42px;
      // width: calc(100% - 10px);
      // margin-right: 20px;
      overflow-x: auto;
      position: relative;
      -ms-overflow-style: none;
      -webkit-overflow-scrolling: touch;
  
      &::-webkit-scrollbar,
      &::-webkit-scrollbar-track,
      &::-webkit-scrollbar-thumb {
        width: 0;
        height: 0;
        background-color: transparent;
        display: none
      }
  
    }
    &-active{
      left: 0;
      position: absolute;
      height: 2px;
      background-color: #e03131;
      bottom: 0;
      width: 30px;
      @include css3('transition','0.1s');
    }
    &-link {
      padding: 10px 10px 0;
      padding-bottom: 10px;
      white-space: nowrap;
      font-size: 15px;
      height: 42px;
      color: #929292;
      @include css3('transition','0.3s');
  
      &.selected,&:hover {
        color: #333;
        font-weight: bold;
      }
    }
}

#saves-container {
  overflow-y: auto;
  height: 100%;
  &.saved-page {
    background: #f1f1f1;
    height: calc(100% - 86px);
    margin-top: 86px;
  }
  .recentlySaved{
    font-size: 15px;
    font-weight: bold;
    padding: 18px 10px;
  }
  .mktrcmd{
    padding-top: 0;
    .rmProp img{
      border-radius: 2px;
    }
  }
  .img .showOnImg{
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
  }
}


#agents {
  #followRealtorPage {
    .content-padded.user-list {
      margin-top: 0;

      ul {
        margin: 0;
      }
    }
  }
}

.group-selected {
  padding: 10px 0 10px 10px;
  position: sticky;
  top: -1px;
  background: #ffffff;
  width: 100%;
  height: 42px;
  z-index: 2;
  border-bottom: 0.5px solid rgb(245, 245, 245);
  font-size: 15px;
  font-weight: bold;
  i{
    color: #999;
  }
  .pull-right {
    padding: 0 7px;
    height: 21px;
  }
  .pull-left {
    width: 50%;
  }
  .currentName{
    max-width: calc(100% - 30px);
    display: inline-block;
    margin-right: 5px;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: bottom;
    span{
      font-weight: normal;
      font-size: 12px;
    }
  }
}

.icon.sprite16-18 {
  width: 18px;
  height: 18px;
  display: inline-block;
  position: relative;
  vertical-align: top;
}

.no-results-found {
  padding: 10px;
}

.table-view .table-view-cell {
  padding-right: 15px;
  border-bottom: 0.5px solid #F0EEEE;
  color: #333;
  font-size: 16px;

  .icon-pull-right {
    position: absolute;
    top: 50%;
    right: 15px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

.on-top {
  .top {
    margin-right: 10px;
    margin-left: 0;
    float: left;
    border-radius: 12px;
  }

  .avt {
    margin-right: 10px;
    float: left;
  }
}

#realtorContact {
  &.smb-md {
    height: 250px;
  }

  li {
    padding-right: 15px;
    text-align: left;
    color: #666;
    border-bottom: none;

    &.cancel {
      text-align: center;
      border-top: 1px solid #ECE7E7;
    }

    i {
      &.fa {
        padding-right: 10px;
      }

      &.fa-phone {
        padding-left: 3px;
      }
    }
  }

  .table-view {
    padding-top: 0;
    margin-bottom: 5px;
  }

  .tip {
    margin: -11px -15px -11px -15px;
    font-size: 15px;
    color: #666;
    text-align: left;
  }
}

#showingSelect {
  &.table-view {
    border-top: 0;
    border-bottom: 0;
  }
}
.btnBox{
  margin-top: 14px;
}

#communities{
  .block{
    background: #fff;
    padding: 10px 0 15px;
  }
}
.rmProp .bdrms>span{
  padding-right: 4px !important;
}

.showing-agent-card{
  display: flex;
  padding: 10px 15px;
  align-items: center;
  background: #fff;

  .agent-info{
    flex: 1;
    p{
      margin-bottom: 0;
    }
  }
  img{
    width: 50px;
    height: 50px;
    margin-right: 10px;
    border-radius: 50%;
    background: url('/img/logo.png');
    background-size: cover;
  }
  .delbtns{
    position: absolute;
    right: 0;
  }
}
#notes{
  height: 100%;
  overflow: auto;
}
.statusTags{
  white-space: nowrap;
  padding: 10px;
  position: sticky;
  top: 41px;
  overflow-x: auto;
  z-index: 5;
  background: #fff;
}
.statusTags .tag {
  display: inline-block;
  white-space: nowrap;
  font-size: 12px;
  vertical-align: middle;
  padding: 5px 10px;
  background-color: $secondary_grey;
  color: #999999;
  border-radius: 2px;
  padding: 6px 5px;
  margin-right: 10px;
  min-width: 40px;
  max-width: 150px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  &.active {
    background-color: #e9f9f4;
    border: 0.5px solid #40bc93;
    color: #40bc93;
  }
}