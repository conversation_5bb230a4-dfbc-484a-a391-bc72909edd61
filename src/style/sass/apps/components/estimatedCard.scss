.estimated-value-card {
  margin-bottom: 18px;
  .card-title {
    display: flex;
    align-items: center;
    // margin-bottom: 20px;
    padding: 0;
    color: #000;
    font-size: 14px;
    font-weight: 700;
    .fa-question-circle-o {
      margin-left: 10px;
      cursor: pointer;
      color: #666;
      font-size: 16px;
      &:hover {
        color: #333;
      }
    }
  }
  .estimateTime{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #777;
  }
  .viewBtn {
    font-weight: bold;
    color: #4EC29C;
    background-color: #f5f5f5;
    padding: 6px 14px;
    border-radius: 3px;
    text-align: center;
    text-transform: capitalize;
    font-size: 12px;
    margin-left: auto;
  }
  .blur-effect {
    filter: blur(4px);
    color: rgba(0, 0, 0, 0.7);
    user-select: none;
    pointer-events: none;
  }
  .value-range-container {
    padding: 57px 0px 0px;
    
    .slider-container {
      position: relative;
      height: 15px;
      margin: 5px 0;
      .slider-tracks-container{
        display: flex; 
        position: absolute; 
        width: 100%; 
        height: 100%;
        overflow: hidden;
        border-radius: 3px;
      }
      .slider-track{
        height: 100%;
      }
      .blue {
        background-color: #00b1f1;
      }
      .green {
        background-color: #5cb85c;
      }
      .red {
        background-color: #d9534f;
      }
      
      .estimate-point {
        position: absolute;
        top: 46%;
        transform: translate(-50%, -50%);
        width: 22px;
        height: 22px;
        background-color: #fff;
        border: 6px solid #000;
        border-radius: 50%;
        z-index: 2;
        
        .estimate-value {
          position: absolute;
          bottom: 30px;
          background-color: #000;
          color: #fff;
          padding: 0px 10px;
          border-radius: 10px;
          font-size: 12px;
          white-space: nowrap;
          
          &:after {
            content: '';
            position: absolute;
            top: 100%;
            border-width: 5px;
            border-style: solid;
            border-color: #000 transparent transparent transparent;
          }
        }
      }
      // 灰色圆点样式
      .gray-point {
        background-color: #fff;
        border: 6px solid #777;
        z-index: 1; // 确保在轨道上方，但在主要圆点下方
      }
      .estimate-point.left-aligned .estimate-value {
        left: -6px;
        transform: translateX(0);
        &:after {
          top: 20px;
          left: 6px;
          transform: translateX(0);
        }
      }
      
      .estimate-point.right-aligned .estimate-value {
        right: -6px;
        left: auto;
        transform: translateX(0);
        &:after {
          top: 20px;
          right: 6px;
          left: auto;
          transform: translateX(0);
        }
      }
      
      .estimate-point.center-aligned .estimate-value {
        left: 50%;
        transform: translateX(-50%);
        &:after {
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    
    .value-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      position: relative;
      
      .min-label, .max-label, .est-label {
        font-size: 10px;
        color: #666;
      }
      
      .min-label {
        text-align: left;
      }
      
      .est-label {
        position: absolute;
        top: 0;
      }
      
      .max-label {
        text-align: right;
      }
    }

    // 带有值和标签的完整标签样式
    .value-labels.full-labels {
      position: relative;
      height: 28px;
      
      .min-label, .max-label, .est-label {
        position: absolute;
        top: 0;
        line-height: 14px;
      }
      .max-label {
        text-align: right;
      }
      .value-text {
        font-size: 12px;
        color: #666;
        font-weight: bold;
      }
      
      .label-text {
        font-size: 10px;
        color: #666;
      }
    }
  }
  .flexCenter{
    display: flex;
    align-items: center;
  }
}