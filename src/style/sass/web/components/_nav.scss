.site-header {
	border-bottom: 1px solid #e5e6ea;
	background-color: #fff !important;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 2000;
  background-color: transparent;
  padding: 15px 0;
  @include css3('transition','all 0.3s ease-out');
  &.fixed {
    position: fixed;
  }
}
.site-header .col-md-4,
.site-header .col-md-8 {
  min-height: 0;
}
.admin-bar .site-header,
.admin-bar .site-header.float-header {
  top: 32px;
}
.admin-bar:not(.siteScrolled) .slide-item {
  background-position: 50% 0 !important;
}
.site-header.float-header {
  background-color: rgba(0,0,0,0.9);
  /* padding: 5px; */
}
.site-header.float-header #mainnav {
  padding: 0;
}

.site-header.float-header #mainnav > ul > li > a {
  height: 70px;
  line-height: 70px;
}

.site-header.float-header .logo {
  margin: 20px 0;
}
.site-logo {
	height: 30px !important;
}
.header-searchbar {
  position: fixed;
  left: 0;
  right: 0;
  @include css3('transition','0.3s');
  @include css3('transform','scale(0)');
  width: 100%;
  height: 100%;
  background-color: #fff;
  top: 0;
  z-index: 10001;
  #header-searchbar-close {
    position: absolute;
    top: 20px;
    left: 15px;
    font-size: 20px;
    cursor: pointer;
  }
  &.active {
    @include css3('transform','scale(1)');
    padding: 40px 15px 0;
    .search-container-input {
      margin-top: 10px;
      max-width: 100%;
    }
    .searchResult {
      border: none;
    }
    .searchResultItem {
      padding-left: 0;
      padding-right: 0;
    }
    .searchResultTabs {
      padding-left: 0;
      padding-right: 0;
    }
  }
  &-mobileIcon {
    font-size: 18px;
    cursor: pointer;
    position: absolute;
    bottom: 6px;
    right: 60px;
  }
}
/* navigation */
.menu-lang {
	display:flex !important;
	display:-webkit-flex !important;
  align-items: center;
  .fa-globe {
    font-size:16px;
    margin-right:3px;
  }
  .fa-angle-down {
    margin-left:3px;
  }
}
#mainnav {
  display: block;
  float: right;
  @include css3('transition','all 0.3s linear');
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  li {
    display: inline-block;
    vertical-align: middle;
    padding: 0 5px;
  }
  ul ul li {
    padding: 0;
  }
  ul li a {
    position: relative;
    display: block;
    font-size: 14px;
    font-family: "Raleway", sans-serif;
    color: #fff;
    font-weight: 500;
    text-decoration: none;
    outline: none;
    &:hover {
      color: #d65050;
    }
  }
  ul ul a {
    width: 180px;
    font-family: "Source Sans Pro", sans-serif !important;
  }
}
#mainnav > ul > li:last-child {
  padding-right: 0;
}
#mainnav ul li {
  position: relative;
}
#mainnav ul li ul {
  width: 180px;
  position: absolute;
  top: 100%;
  left: initial;
  opacity: 0;
  visibility: hidden;
  @include css3('border-radius','0 0 2px 2px');
  @include css3('transition','all 0.3s ease');
}
#mainnav ul li ul ul {
  top: 0;
}
#mainnav ul li ul:after {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
}
#mainnav ul li ul ul:after {
  top: 0;
}
#mainnav ul li ul li ul {
  left: 100%;
}
#mainnav li:hover > ul {
  opacity: 1;
  padding-top: 10px;
  visibility: visible;
  left: auto;
  display: block !important;
  @include css3('transition','all 0.3s ease');
}
#mainnav li:hover > ul ul {
  padding-top: 0;
  left: 100%;
}
#mainnav .sub-menu {
  z-index: 99999;
}
#mainnav .sub-menu li a {
  color: #494949;
  display: block;
  line-height: 30px;
  background-color: #efefef;
  padding: 0 18px;
  border-top: 1px solid #252525;
  @include css3('transition','all 0.3s ease');
}
#mainnav .sub-menu li:hover > a {
  color: #fff;
}
#mainnav li:hover > .sub-menu > li > a {
  padding: 8px 18px;
}
#mainnav ul li a:hover {
	color: #ff2a00;
}
#mainnav li > .sub-menu > li > a:hover {
	background-color: #ff2a00;
}
/* Icons */
#mainnav ul li::before {
  font-family: Fontawesome;
  color: #fff;
  float: left;
  margin: 5px 5px 0 0;
  font-size: 14px;
  line-height: 1;
  font-weight: 400;
}
#mainnav ul li a {
  float: left;
}
#mainnav .sub-menu li:before {
  content: '';
  margin: 0;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
}
/* #mainnav-mobi ul li a {
  float: none;
} */
#mainnav-mobi ul li::before {
  content: '';
}
/*--------------------------------------------------------------
Mobile navigation
--------------------------------------------------------------*/
#mainnav-mobi {
  display: block;
  margin: 0 auto;
  width: 100%;
  position: absolute;
  top:28px;
  background-color: #1c1c1c;
  margin-top: 15px;
	z-index: 10000;
	height: calc(100vh - 65px);
	overflow-x: scroll;
}
#mainnav-mobi ul {
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
}
#mainnav-mobi ul li {
  margin:0;
  position: relative;
  text-align: left;
  border-top: 1px solid #252525;
  cursor: pointer;
}
#mainnav-mobi ul > li > a {
  display: block;
  text-decoration: none;
  padding: 10px 50px;
  color: #fff;
}
#mainnav-mobi ul.sub-menu {
  top: 100%;
  left: 0;
  z-index: 2000;
  position: relative;
  background-color: #000;
}
#mainnav-mobi > ul > li > ul > li,
#mainnav-mobi > ul > li > ul > li > ul > li {
  position: relative;
  border-top: 1px solid #252525;
  background-color: #222;
}
#mainnav-mobi > ul > li > ul > li >  a {
  padding-left: 70px !important
}
#mainnav-mobi > ul > li > ul > li > ul > li a {
  padding-left: 90px !important
}
#mainnav-mobi ul.sub-menu > li > a {
  display: block;
  text-decoration: none;
  padding: 5px 60px;
  border-top-color: rgba(255,255,255,0.1);
  @include css3('transition','all 0.2s ease-out');
}
#mainnav-mobi ul.sub-menu > li > a.activeLang {
	background-color:#ff2a00;
}
#mainnav-mobi > ul > li > ul > li:first-child a {
  border-top: none;
}
#mainnav-mobi ul.sub-menu > li > a:hover,
#mainnav-mobi > ul > li > ul > li.active > a {
  color: #fff;
}
#page-title {
	font-size: 16px;
	margin:0 0 0 10px;
	display: none;
	vertical-align: middle;
}
.header-wrap .container {
	padding: 0 15px !important;
}
.menu-item-seperator {
	border-right: 1px solid #dd1700;
}
.menu-item-download {
	padding: 5px 14px !important;
	background-color: #dd1700;
	border-radius: 5px;
}
.menu-item-download > a {
	color: #fff !important;
}
#mainnav ul li a.activeLang {
  background-color: #ff2a00;
  color:#fff;
}
.menu-item-lang {
  /* border:1px solid #dd1700; */
  padding: 5px 14px !important;
  border-radius: 5px;
}
.menu-item-lang > a {
	color:#DD1700 !important;
}
.current_page_item a,
.current_page_parent a {
	color: #dd1700 !important;
}
@media only screen and (min-width: 768px) {
	#page-title {
		display: inline-block;
  }
  .header-searchbar {
    position: static;
    display: inline-block;
    width: calc(100% - 135px);
    margin-left: 20px;
    @include css3('transform','scale(1)');
    &-mobileIcon,
    #header-searchbar-close {
      display: none;
    }
    .search-container-input {
      margin-top: 0 !important;
    }
    &.active {
      padding: 0;
    }
  }
}
.btn-menu {
  color: #ef0000;
  font-size: 28px !important;
  font-family: "FontAwesome";
  text-align:center;
  width: 28px;
  height: 28px;
  cursor: pointer;
  position: absolute;
	right: 15px;
	bottom: 1px;
  @include css3('transition','all 0.3s ease-out');
}
.btn-submenu {
  position: absolute;
  right: 20px;
  top: 0;
  font-family: "FontAwesome";
  font-size: 20px !important;
  line-height: 45px !important;
  text-align: center;
  cursor: pointer;
  width: 70px;
  height: 44px;
}
.btn-submenu:before {
  content: "\f107";
  color: #fff;
}
.btn-submenu.active:before {
  content: "\f106"
}
@media only screen and (max-width: 767px) {
  #mainnav-mobi {
    top: auto;
  }
  .site-header.float-header {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
@media only screen and (max-width: 991px) {
  .header-wrap .col-md-8 {
    padding: 0;
  }
}
@media only screen and (max-width: 768px) {
  .menu-item-download {
    padding:0 !important;
    background-color: inherit;
  }
  .menu-item-lang {
    padding:0 !important;
    border:none;
  }
  .menu-item-lang > a {
    color:#fff !important;
  }	
  #mainnav {
    display: none;
  }
  .site-header {
    position: static;
    background-color: rgba(0,0,0,0.9);
  }
  .menu-lang .fa-angle-down {
    display:none;
  }
}
.site-header #mainnav ul li ul {
	right: 14px;
}
.site-header #mainnav ul li ul li ul {
	left: -100%;
}
#header,
#top-bar {
	z-index: 1;
}
.current-page {
	color: $mainColor !important;
}
#mainnav ul li a, #mainnav ul li::before {
	color: #494949;
}