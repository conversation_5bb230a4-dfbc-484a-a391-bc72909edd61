
iframe{
  border: 0;
}
#propsearch-modal-root {
	bottom: 0;
	left: auto;
	overflow-y: auto;
  overflow-x: hidden;
	background-color: #fff;
	z-index: 1000;
	display: block;
	position: fixed;
	width: 560px;
	right: 0;
	top: 77px;
	transform: translateX(100%);
	transition: transform .3s ease-in-out;
}
#propsearch-modal-root.active{
  transform: translateX(0);
}
#propsearch-modal-root .bar{
    position: fixed;
    top: 0px;
    right: 0;
    width: 560px;
    background: #fff;
    border-bottom: .5px solid #f5f5f5;
    padding: 10px 15px 0;
}
#propsearch-modal-root .bar-nav {
    font-size: 17px;
    font-weight: bold;
    white-space: nowrap;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
}
#propsearch-modal-root .pull-right {
    float: right;
}
.displayBlock {
  display: block;
}
.displayNone {
  display: none;
}
#propsearch-modal-root #simPopUpModal {
    position: fixed;
    top:56px;
    right: 0;
    width: 560px;
    height: 100%;
    background-color: white;
    z-index: 1001;
    padding-top: 11px;
}
@media screen and (max-width: 768px) {
    #propsearch-modal-root #simPopUpModal{
    top: 56px;
    width: 100%;
    max-width: initial;
    }
    #propsearch-modal-root{
    top: 63px;
    width: 100%;
    max-width: initial;
    }
		#propsearch-modal-root .bar{
			width: 100%;
		}
}