.listing-prop {
  cursor: pointer;
  position: relative;
  background: #ffffff;
  margin: 0 0 20px;
  margin-bottom: 20px;
  display: block;
  @include css3('transition','all 0.5s ease');
  &:hover,&:focus,&:visited {
    text-decoration: none;
    box-shadow: 5px 3px 6px rgba(0,0,0,.117647), -5px 8px 4px rgba(0,0,0,.117647);
  }
  &-img-container {
    position: relative;
    text-align: center;
    background-color: #ccc;
  }
  &-img {
    padding: 0;
    min-height: 200px;
    height: 200px;
    max-width: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50% 50%;
    &-status {
      position: absolute;
      right: 0;
      top: 0;
      background: $mainColor;
      color: #ffffff;
      padding: 5px 10px;
      border-radius: 3px;
      &.toplisting {
        left:0;
        right:auto;
      }
    }
  }
  &-labels {
    position: absolute;
    bottom: 10px;
    left: 0;
  }
  &-label {
    background-color: #6fce1b;
    padding: 3px 7px;
    margin-left: 10px;
    color: #fff;
    font-size: 12px;
    border-radius: 10px;
  }
  &-require-login {
    color:#fff;
    position: absolute;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 1;
    border: 1px solid #fff;
    background: rgba(210, 57, 51, 0.3);
    border-radius: 3px;
    padding: 3px;
    @include css3('transition','0.3s');
    &:hover {
      color: $mainColor;
      background: rgba(255, 255, 255, 0.5);
      border-color: $mainColor;
    }
    .fa-lock {
      font-size: 45px;
    }
  }
  &-no-img {
    height: 200px;
    position: relative;
    background: #f1f1f1;
    &-container {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0;
      right: 0;
      margin: auto;
      text-align: center;
    }
  }
  &-detail {
    position: relative;
    padding: 20px 20px;
  }
  &-price {
    min-height: 19px;
    margin: 0 0 5px 0;
    font-size: 1.6rem;
    color: $mainColor;
    .detail-lp {
      text-decoration: line-through;
      font-size: 12px;
      padding-left: 5px;
      color: #999;
      font-weight: normal;
    }
  }
  &-dist {
    color: #e8af00;
    font-size: 14px;
    margin-left: 10px;
  }
  .inactive {
    margin-left: 5px;
  }
  &-id {
    position: absolute;
    color: #bfbfbf;
    text-transform: uppercase;
    top: 20px;
    right: 20px;
  }
  &-address {
    color: #626d70;
    font-size: 16px;
    text-transform: capitalize;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
  &-adrltr {
    position: absolute;
    right: 20px;
    top: -40px;
    text-align: center;
    &-avt {
      width:60px;
      height:60px;
      border-radius:50%;
      border:1px solid #fff;
      @include css3('transition','0.3s');
      &:hover {
        @include css3('transform','scale(1.05)');
      }
    }
    &-nm {
      white-space: nowrap;
      overflow: hidden;
      width: 90px;
      font-size: 13px;
      color: #428bca;
      text-overflow: ellipsis;
    }
  }
  &-rooms {
    margin-top: 10px;
    margin-bottom: 0;
    min-height: 25px;
  }
  &-room {
    color: $mainColor;
    margin-right: 10px;
    font-size: 16px;
    span {
      display: inline-block;
      vertical-align: middle;
    }
    .fa {
      margin-left: 2px;
    }
  }
  &-dom {
    margin-right: 5px;
  }
  // listing-prop-link
  &-link {
    display: inline-block;
    text-transform: uppercase;
    position: absolute;
    right: 20px;
    bottom: 10px;
    border: 1px solid #eeeced;
    padding: 6px 10px;
    border-radius: 3px;
    &:hover {
      text-decoration: none;
      color: $mainColor;
      .fa {
        margin-left: 10px;
      }
    }
    .fa {
      @include css3('transition','0.3s');
      margin-left: 5px;
    }
  }
}
.list-view .listing-prop-img {
  border-radius: 5px 5px 0px 0px;
}

.heart-icons{
  &[role="button"]{
    position: absolute;
    right: 2rem;
    top: 1rem;
    font-size: 23px;
    &[role="button"]{
      z-index: 2;
    }
    color: $main_red;
    cursor: pointer;
  }
}
