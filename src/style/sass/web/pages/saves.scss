@import '../../base';
@import '../components/propCommon';
@import '../components/pagination';
@import '../components/drawerSearchId';
@import '../../apps/components/boxSelectBtn.scss';

#saves-root{
  min-height: 100vh;
  .prop-list-nav{
    padding-top: 0;
    padding-bottom: 20px;
  }
  .saves-filter{
    margin-bottom: 1rem;
    select{
      padding: 6px 10px;
      border-radius: 3px;
      border: 1px solid $buttonBorderColor;
    }
  }
  .footerDisplay{
    display: flex;
    align-items: center;
  }
  .displayDiv{
    display: flex;
    align-items: center;
    margin: 15px 0;
    justify-content: space-between;
  }
  #id_with_sign input[type=checkbox]{
    outline: 0;
    -webkit-appearance: none;
    background-color: white;
    border: 1px solid #e03131;
    color: white;
    border-radius: 3px;
    cursor: pointer
  }
  #id_with_sign input[type=checkbox]:checked::after{
    content: '\f00c';
    font: normal normal normal 14px/1 fa;
    font-family: fa;
    font-size: 20px;
    color: #e03131;
    cursor: pointer;
  }
  .langSelect{
    display: table;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
    margin: 0 30px;
  }
  .langLabel{
    display: table-cell;
    padding: 7px 7px;
    overflow: hidden;
    line-height: 1;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-left: 1px solid #ccc;
    color: #666;
    cursor: pointer;
  }
  .langBackground{
    background-color:#e03131;
    color: #fff;
  }
  .printBtn{
    padding: 4px 10px;
    color: #fff;
    background-color: #e03131;
    border-radius: 3px;
    font-size: 14px;
  }
  .cursorDrag:hover{
    cursor: move;
  }
  .cursorDrag:active{
    cursor: move;
  }
  #id_with_sign{
    display: flex;
    align-items: center;
  }
  @media screen and (max-width: 768px) {
    .langSelect{
      margin: 0 5px;
    }
  }
  @media screen and (max-width: 368px) {
    .langSelect{
      margin: 0 2px;
    }
    .langLabel{
      padding: 6px 6px;
    }
    .printBtn{
      padding: 3px 8px;
    }
  }
  .pull-right{
    float: right;
  }
  .btnDelete {
    position: relative;
    display: inline-block;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 1;
    color: #333;
    text-align: center;
    white-space: nowrap;
    vertical-align: top;
    cursor: pointer;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 5px 8px;
  }
  .btn-nooutline {
    outline: 0;
    border: 0;
  }
  .btn-negative {
    color: #fff;
    background-color: #d9534f;
    border: 1px solid #d9534f;
  }
  .positionRight{
    position: absolute;
    right: 0px;
    top: 0px;
  }
}
