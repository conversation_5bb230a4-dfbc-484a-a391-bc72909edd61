# handle upload event from user
sharp = INCLUDE 'sharp'
axios = INCLUDE 'axios'
fs = INCLUDE 'fs'
Path = INCLUDE 'path' # normally lowercase path refer to the library of path
{pathJoinWithUrl} = INCLUDE 'lib.helpers_string'
###
Require the packages:
const nodeUrl = require('url')
const nodePath = require('path')
Start by making a URL object to work with:

> const myUrl = new nodeUrl.URL('https://example.com')
Use pathname= and path.join to construct any possible combination:
> myUrl.pathname = nodePath.posix.join('/search', 'for', '/something/')
'/search/for/something/'
(you can see how liberal path.join is with arguments)

At this point your URL reflects the ultimate desired result:
> myUrl.toString()
'https://example.com/search/for/something/'
###

helpers = INCLUDE 'lib.helpers'
checksum = INCLUDE 'lib.checksum'
moderation = INCLUDE 'libapp.contentModeration'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()

imageModerationAsync = helpers.addAsyncSupport(moderation.imageModeration)
config = CONFIG(['fileBase','s3config','share'])
# debug.info = console.log

unless config.fileBase?.publicImgFilePath
  throw new Error "no publicImgFilePath"
# 支持array/string
publicImgFilePath = config.fileBase.publicImgFilePath

fileDriver = INCLUDE('lib.fs_local').getDriver {rootFolder:(publicImgFilePath)}

s3fileDriver = INCLUDE('lib.fs_awss3').getDriver config.s3config

SHARE_SECRET = config.share?.secret
unless SHARE_SECRET
  throw new Error "no share.secret"

IMG_SERVER_DL_ADDR = config.fileBase?.imgServerDlAddr
unless IMG_SERVER_DL_ADDR
  throw new Error "no imgServerDlAddr"
if IMG_SERVER_DL_ADDR[IMG_SERVER_DL_ADDR.length-1] isnt '/'
  IMG_SERVER_DL_ADDR += '/'

# Do not hardcode path, use config auto fill the path
#REALMASTER_ICON_ADDR = '../src/webroot/public/img/logo_s.png'


publicImgErrorFile = config.fileBase?.publicImgErrorFile
unless publicImgErrorFile
  throw new Error 'no publicImgErrorFile path'

# if publicImgErrorFile
fs.open publicImgErrorFile,'r+',(err,fd)->
  if err
    fs.writeFile publicImgErrorFile, '',(err)->
      if err
        throw err
      debug.info 'publicImgErrorFile file saved'
  else
    debug.info 'publicImgErrorFile file exists'
    fs.close fd

debug.info "Public Image File Path(publicImgFilePath): #{publicImgFilePath}"
debug.info "Image Download Address(IMG_SERVER_DL_ADDR): #{IMG_SERVER_DL_ADDR}"
debug.info "Image Error File(publicImgErrorFile): #{publicImgErrorFile}"


# resize image at server, _ for thumb images
imgLWL = 1600 # for long edge
imgLHL = 1600 # Long Edge Height Limit
imgXLWL = 2400 #限制高清晰度的图片宽度
imgXLHL = 2400 #限制高清晰度的图片高度
imgSWL = 680 #for short edge
imgSHL = 680
thumbH = thumbW = 128
DOWNLOAD_FIEL_PATH = '/tmp/'
BACK_GROUND_S3 = 'backgroundS3'

resizeImg = (inputImg,resizeOpt) ->
  image = sharp(inputImg.path)
  outputBuffer = null
  thumbBuff = null
  metadata = await image.metadata()
  newWidth = 128
  newHeight = 128
  ret = null
  size = metadata.size or inputImg.size
  ratio = 1
  # console.log '++++++',metadata
  if (metadata.width < imgLWL) and (metadata.height < imgLWL)
    if (size < 500*1000)
      # "Error: vips__file_open_read: unable to open file "/tmp/upload_d9a3792cb9084b1b66cc35ca32e8e0b5.jpg_971312_593458724010374_89396129_n.jpg" for reading↵unix error: No such file or directory↵"
      ret = {buff:inputImg,width:metadata.width,height:metadata.height,size:size,hasThumb:false}
      # NOTE: force exit .then steps
      # throw new Error('_End_Of_Process')
      return ret
    else
      ratio = metadata.width/metadata.height
    #throw new Error('EOP')
  if (metadata.width > imgLWL) or (metadata.height > imgLWL)
    widthRatio = imgLWL / metadata.width
    heightRatio = imgLHL / metadata.height
    ratio = Math.min(widthRatio, heightRatio)
  if (metadata.width >= metadata.height) and (metadata.height > imgSHL)
    heightRatio = imgSHL / metadata.height
    ratio = heightRatio  if heightRatio < ratio
  if (metadata.width <= metadata.height) and (metadata.width > imgSWL)
    widthRatio = imgSWL / metadata.width
    ratio = widthRatio  if widthRatio < ratio
  # admin uploaded oversized image splash.jpg
  # only admin has this param=xl
  if resizeOpt.imgSize is 'xl'
    # oversized > 2400
    if (metadata.width > imgXLWL) or (metadata.height > imgXLHL)
      widthRatio = imgXLWL / metadata.width
      heightRatio = imgXLHL / metadata.height
      ratio = Math.min(widthRatio, heightRatio)
    # file is small and not larger than 2400
    else if (size < 500*1000)
      # no thumb for splash for now
      ret = {buff:inputImg,width:metadata.width,height:metadata.height,size:size,hasThumb:false}
      return ret
    else
      ratio = 1 #keep the original
  debug.debug 'ratio',widthRatio,heightRatio,ratio
  newWidth = parseInt(ratio*metadata.width)
  opt = {width:newWidth}
  newHeight = ratio*metadata.height
  # TODO: add .rotate()?
  # https://sharp.pixelplumbing.com/api-operation#rotate
  # If no angle is provided, it is determined from the EXIF data. Mirroring is supported and may infer the use of a flip operation.
  try
    if metadata.format is 'png'
      # TODO: /public/img/testImage.png (900k) -> .jpg (1200k), need to modify compression level quality, etc
      # NOTE: https://sharp.pixelplumbing.com/en/stable/api-output/#parameters_4, requires libimagequant
      outputBuffer = await image.rotate().png({compressionLevel:9,quality:70,colors:128}).resize(opt).toBuffer()
    else
      outputBuffer = await image.rotate().resize(opt).toBuffer()
    
    thumbBuff = await image.rotate().resize(128).toBuffer()
    ret = {
      buff:outputBuffer,
      thumbBuff:thumbBuff,
      width:newWidth,
      height:newHeight,
      origSize:metadata.size,
      size:outputBuffer.length,
      hasThumb:true
    }
  catch err
    if /_End_Of_Process/.test err.toString()
      debug.info 'MSG: no need to resize, end of process, prevent vips_error'
      return ret
    else
      throw err
  return ret

###
# 添加水印
# @param  { inputImg  }  (Buffer | String[path])
# @param  { overlay }  (Buffer | String)
###
waterMarkImg = (inputImg,overlay,pos, cb)->
  # debug.info inputImg
  # debug.info overlay
  gravity = sharp.gravity.southeast
  if pos in ['north', 'northeast', 'east', 'southeast', 'south', 'southwest', 'west', 'northwest', 'center' ,'centre']
    gravity = sharp.gravity[pos]
  try
    sharp inputImg
      .overlayWith(overlay, { gravity: gravity } )
      .sharpen()
      .toBuffer()
      .then (outputBuffer)->
        cb null,outputBuffer
      .catch((err)->
        cb err
      )
  catch error
    cb error

# K/P/A.jpg -> [K/P, A.jpg]
getPathAndNm = (key='')->
  arr = key.split('/')
  nm = arr[arr.length - 1]
  path = arr[0]+'/'+arr[1]
  return [path,nm]

logFileError = (opt)->
  err = opt.err?.toString() or ''
  path = opt.folderPath
  nm = opt.nm
  ts = (new Date()).toISOString()
  errString = "#{path},#{nm},#{ts},#{err}\n"
  fs.appendFile publicImgErrorFile, errString, (err)->
    throw err if err
    debug.info("Error: upload s3 #{path} logged!")

#upload to s3 in backend
uploadAfterWork = (cfg)->
  folderPath = cfg.folderPath
  f = cfg.f
  unless Array.isArray f
    throw new Error('f not array, expected array')
  for file in f
    file.keepFile = true
  nm = cfg.nm
  # NOTE:createFolder return null，目前没有执行的需要
  # s3fileDriver.createFolder folderPath
  await s3fileDriver.uploadFiles f,folderPath
  return

APP 'file'
# 1.verify user
# 2.download file
# 3.post back
fixWebSUrl = (sUrl)->
  return sUrl.replace(/(https?\:\/)/,"$1/")

# /file/downloadImg
POST 'downloadImg',(req,resp)->
  resp.setHeader 'Access-Control-Allow-Origin','*'
  findError = (e)->
    debug.error e.toString()
    resp.send {e:e.toString()}
  b = req.body
  unless (b?.signature)
    return findError 'Incomplete Data'
  [pass, imgObj] = checksum.shareDecode(b.signature,SHARE_SECRET)
  # debug.info imgObj
  try
    imgObj = JSON.parse imgObj
  catch error
    return findError error
  if Date.now() > new Date(imgObj.exp)
    return findError 'Upload Session Expired'
  [folderPath,nm] = getPathAndNm imgObj.key
  return findError('Not authed') unless pass
  if /\.\./g.test folderPath
    req.logger?.log("img upload path includes .. IP:#{req.remoteIP()} \tpath:#{folderPath}")
    return findError 'Invalid path'
  # debug.info imgObj
  unless imgObj.url
    return findError('options.uri is a required argument')
  # helpers.http_get imgObj.url, {binary:true}, (err,ret)->
  try
    response = await axios.get(imgObj.url,{responseType:'arraybuffer'})
  catch err
    return findError err
  if (response?.status is 200) and (response?.data)
    filename = DOWNLOAD_FIEL_PATH + Math.random().toString().split('.').pop()
    try
      await fs.promises.writeFile(filename,response.data)
    catch err
      debug.error err,' finename:',filename
      return findError err
    f = [{name:nm, path:filename,type:'image/jpeg'}]
    debug.info "#{filename} is now complete."
    try
      resultFile = await uploadLocalFileDriver {req,nm,f,folderPath}
    catch err
      debug.error err,' files:',f,' path:',folderPath
      return findError err
    # NOTE: BUG: path.join will change https://realmaster -> https:/realmaster
    # NOTE: always use path.join to make program compitalbe with different platform(FOR FILES WRITE/READ ONLY!!! NOT URL!!!)
    # NOTE: path.join will generate wrong path for http:// -> http:/, sUrl is for web access not for local, no no need to use path.join to compatible with diff platforms
    # @luoxiaowe see #6 for path join and URL
    # sUrl = IMG_SERVER_DL_ADDR + Path.join(folderPath,nm)
    sUrl = pathJoinWithUrl IMG_SERVER_DL_ADDR,folderPath,nm
    if req.param BACK_GROUND_S3
      resp.send {ok:1,sUrl:sUrl}
    try
      await uploadAfterWork {nm,folderPath,f:resultFile}
    catch err
      logFileError {folderPath, err, nm} # log error regardless the parameter
      return findError err unless req.param BACK_GROUND_S3
    if not req.param BACK_GROUND_S3
      resp.send {ok:1,sUrl:sUrl}
  else
    debug.error 'Error: downloading image: \t'+response
    return findError 'Error: downloading image'


uploadLocalFileDriver = (opt)->
  folderPath = opt.folderPath
  nm = opt.nm
  f = opt.f #f is array
  unless Array.isArray f
    throw new Error('opt.f is not array')
  await fileDriver.createFolder folderPath
  for file in f
    file.name = nm if not file.name
    file.moveFile = config.fileBase?.moveFile or 'copy'
  uploaded = await fileDriver.uploadFiles f,folderPath
    # console.log uploaded
  if not Array.isArray uploaded
    uploaded = [uploaded]
  for file in uploaded
    file.path = file.destPath if file.destPath
  # debug.info f
  return uploaded
# 1. verify user action
# 2. write to local storage
# 3. response result
# 4. after work save to amaz-s3
# 5. handle s3 err
thumbFromPath = (p)->
  arr = p.split('/')
  nm = arr[2].split('.')
  nm[0] = nm[0]+'_'
  arr[2] = nm.join('.')
  arr.join('/')

# A.jpg -> A_.jpg
thumbFromNm = (nm='')->
  arr = nm.split('.')
  return arr[0]+'_.'+arr[1]

# /file/uploadImg
POST 'uploadImg',(req,resp)->
  resp.setHeader 'Access-Control-Allow-Origin','*'
  findError = (e, opt)->
    debug.error e
    # req.logger?.log e
    ret = {e:e.toString()}
    ret = Object.assign ret,opt if opt
    resp.send ret
  upfiles = req.files
  if (not upfiles) or (not upfiles.file)
    return findError "No File Found"
  #NOTE: decode  and check for validity
  unless b = req.body
    return findError "Bad Parameters"
  data = {}
  # for i in ['thumbKey','thumbSignature','key','signature','date','IMG_SERVER_ADDR','contentType']
  #   data[i] = b[i]
  if b.contentType not in ['.jpeg','.jpg','image/jpeg','image/gif','image/png']
    return findError 'Not supported contentType'
  unless (b.key and b.signature) #or (b.thumbKey and b.thumbSignature)
    return findError 'Incomplete Data'
  # thumbObj = null
  message = 'Not authed'
  # if b.thumbKey
  #   [pass, imgObj] = checksum.shareDecode(b.thumbSignature,SHARE_SECRET)
  #   debug.info pass
  #   debug.info imgObj
  #   return findError(message) unless pass
  if b.key
    [pass, imgObj] = checksum.shareDecode(b.signature,SHARE_SECRET)
    return findError(message) unless pass
  #check expire
  try
    imgObj = JSON.parse imgObj
  catch error
    return findError error
  if Date.now() > new Date(imgObj.exp)
    return findError 'Upload Session Expired'
  # if b.thumbKey
  #   [folderPath,nm] = getPathAndNm imgObj.thumbKey
  # else
  [folderPath,nm] = getPathAndNm imgObj.key
  # NOTE: CHECK folder path
  if /\.\./g.test folderPath
    req.logger?.log("img upload path includes .. IP:#{req.remoteIP()} \tpath:#{folderPath}")
    return findError 'Invalid path'
  # folderPath
  f = upfiles.file
  resizeOpt = {}
  if b.imgSize
    resizeOpt.imgSize = b.imgSize
  debug.debug 'resize', resizeOpt
  try
    ret = await resizeImg f,resizeOpt
  catch err
    return findError err
  debug.debug 'afterresize',ret
  if ret.hasThumb
    #unlink old file
    try
      await fs.promises.unlink f.path
    catch err
      debug.error err
    # NOTE: removeFiles not allow except @rootFolder
    # fileDriver.removeFiles [f.path],'/tmp',(err)->
    #   console.error if err
    f = []
    f.push {buffer:ret.buff,name:nm,type:'image/jpeg'}
    if ret.thumbBuff
      thumbNm = thumbFromNm(nm)
      f.push {buffer:ret.thumbBuff,name:thumbNm,type:'image/jpeg'}
  else
    f.name = nm
    f = [f]
  try
    resultFile = await uploadLocalFileDriver {req,nm,f,folderPath}
  catch err
    debug.error 'uploadLocalFileDriver error,files:',f,' path:',folderPath
    return findError err
  # sUrl = IMG_SERVER_DL_ADDR + Path.join(folderPath,nm)
  sUrl = pathJoinWithUrl IMG_SERVER_DL_ADDR,folderPath,nm
  # sUrl = fixWebSUrl(Path.join IMG_SERVER_DL_ADDR,folderPath,nm)
  try
    mdrtnRet = await imageModerationAsync sUrl
  catch err
    return findError err
    # ret = {block:true, labels: ['policties','abuse'], msg:'Your submission has policties/abuse contents'}
  if mdrtnRet?.block
    try
      result = await fileDriver.removeFiles [nm], folderPath
    catch err
      debug.error err
      opt = {ecode:'violation',violation:mdrtnRet.labels,result:mdrtnRet}
      return findError mdrtnRet.msg,opt
    return
  if req.param BACK_GROUND_S3
    resp.send {ok:1,sUrl:sUrl,width:ret.width,height:ret.height,size:ret.size,hasThumb:ret.hasThumb}
  try
    await uploadAfterWork {nm,folderPath,f:resultFile}
  catch err
    logFileError {folderPath, err, nm}
    return findError err unless req.param BACK_GROUND_S3
  if not req.param BACK_GROUND_S3
    resp.send {ok:1,sUrl:sUrl,width:ret.width,height:ret.height,size:ret.size,hasThumb:ret.hasThumb}

    # waterMarkImg resultFile.path,REALMASTER_ICON_ADDR,'',(err,base64Data)->
    #   if err
    #     debug.info(err)
    #   waterMarkImgPath = resultFile.path+'w'
    #   fs.writeFile waterMarkImgPath, base64Data, 'base64', (err)->
    #     if err
    #       debug.info(err)


# testServerIPs = []
# serverIPS = []
# VALID_SERVER_IPS = testServerIPs.concat(serverIPS)
# verifyValidSource = (req)->
#   ip = req.remoteIP()
#   return ip in VALID_SERVER_IPS

# /file/deleteFiles
# delete file from both user file and s3
POST 'deleteFiles',(req,resp)->
  # pass = verifyValidSource req
  # return findError('Not authed') unless pass
  findError = (e)->
    debug.error e
    resp.send {ok:0, err:e.toString()}
  # debug.info 'req.body:'
  signature = req.param 'signature'
  unless signature
    return findError 'Incomplete data'
  [pass, delObj] = checksum.shareDecode(signature,SHARE_SECRET)
  return findError('Not authed') unless pass
  # debug.info delObj
  if 'string' is typeof(delObj)
    try
      delObj = JSON.parse delObj
    catch error
      return findError error.toString()
  files = delObj.files
  fldnm = delObj.fldnm
  return findError('Incomplete data2') unless files and fldnm
  errStr = ''
  debug.info 'delete files',files,fldnm
  try
    result = await fileDriver.removeFiles files,fldnm
  catch err
    errStr += err.toString()
  try
    await s3fileDriver.removeFiles files,fldnm
  catch err
    errStr += err.toString()
  ok = 1
  if errStr
    debug.error errStr,' delObj:',delObj
    ok = 0
  resp.send {ok:ok, r:result, err:errStr}

# NOTE: test s3 api for list file, hide this api in production
# /file/listfile?user=test&pass=test
# TODO: need to test s3 list file api
# GET 'listfile',(req,resp)->
#   user = req.params 'user'
#   pass = req.params 'pass'
#   unless (user is 'test') and (pass is 'test')
#     return resp.send 'not authed'