APP ''
GET 'index',(req,resp)->
  resp.ckup 'index', {}

GET (req,resp)->
  resp.ckup 'index', {}

VIEW 'index',->
  div -> 'img server index page @version:1722'

LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      meta name:"apple-mobile-web-app-capable", content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"
      meta name:"format-detection", content:"telephone=no"
      meta name:"referrer", content:"no-referrer" if @noref
    block 'body', ->
      body ->
        text @content()

VIEW 'E404', ->
  header class: 'bar bar-nav', ->
    #a class: 'icon icon-left pull-left', href: '/1.5/index', style:'color:white;',dataTransition: 'slide-out'
    h1 class: 'title', -> @title or 'Error'
  div class: 'content', ->#style:'margin:10px;',->
    div class: '', style: 'margin:10px;', ->
      p style:"text-align:center;color:red;",->
        text "No contents for #{@req.host} #{@req.url}"

