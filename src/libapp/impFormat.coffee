propAddrHelper = require '../lib/propAddress'
{unifyAddress,strCapitalize,isNullOrEmpty} = require '../lib/helpers_string'
{objectStr2NumNClean} = require '../lib/helpers_object'
{parseNumberIfPossible,round} = require '../lib/helpers_number'
{inputToDateNum} = require '../lib/helpers_date'
cityHelper = require '../lib/cityHelper'
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
propertiesHelper = require './properties'
filterZip = propAddrHelper.filterZip
common = require './common'
MAX_LEASE_PRICE = 30000
MIN_SALE_PRICE= 10000
SAFE_LEASE_PRICE = 5000
SAFE_SALE_PRICE = 100000
LEASE_AND_SALE_ERROR = 'prop lp and lpr error'
MAX_SQFT = 100000
MIN_SQFT = 100

NUMBER_FIELDS = [
  'tax',
  'taxyr',
  'lvl',
  'dom',
  'mmap_page',
  'mmap_col',
  'rooms_plus',
  'kch',
  'kit_total',
  'bdrms',
  'tbdrms',
  'bthrms',
  'br',
  'br_plus',
  'num_kit',
  'kit_plus',
  'gr',
  'tgr',
  'park_spcs',
  'sqft',
  'sqft1',
  'sqft2',
  'bltYr1',
  'bltYr2',
  'bltYr',
  'yr_built',
  'age1',
  'age2',
  'mfee',
  'lp',
  'lpr',
  'sp',
  'front_ft',
  'depth'
  ]

exports.ptypeMapping = ptypeMapping = {
  Commercial:'b',
  b:'b',
  Other:'o',
  o:'o',
  Residential:'r',
  r:'r'
}
###
#处理所有的basement。其他的source的basement
#bsmt ['Finished','Fin W/O']
#TODO:distinct basmt after import all to propertynews
[
  [x]"Apartment In Basement",
  [x]"Apt",
  []"Basement Suite - Non Regulation",
  []"Cellar",
  "Cellar (finished)",
  "Cellar (not Applicable)",
  "Cellar (partially Finished)",
  "Cellar (unfinished)",
  "Cellar (unknown)",
  "Common (finished)",
  "Common (not Applicable)",
  "Common (partially Finished)",
  "Common (unfinished)",
  "Crawl Space (finished)",
  "Crawl Space (not Applicable)",
  "Crawl Space (partially Finished)",
  "Crw",
  "Dugout",
  "Dugout (unfinished)",
  "Fin",
  [x]"Full",
  [x]"Full (basement Suite - Non Regulation)",
  [x]"Full (not Applicable)",
  [x]"Full (other",
  [x]"Full (partially Finished)",
  [x]"Full (unknown)",
  "Half",
  "N",
  "N/A",
  "N/A (finished)",
  "N/A (partially Finished)",
  "N/A (unfinished)",
  "NON",
  "None (finished)",
  "None (other",
  "None (partially Finished)",
  "None (unfinished)",
  "None (unknown)",
  "Other",
  "Part Bsmt",
  [x]"Partial",
  [x]"Partial (finished)",
  [x]"Partial (other",
  [x]"Partial (partially Finished)",
  [x]"Partial (unfinished)",
  [x]"Partial (unknown)",
  [x]"Prt",
  "Remodeled Basement",
  "Remodeled Basement (finished)",
  "See Remarks",
  "See Remarks (finished)",
  "See Remarks (not Applicable)",
  "See Remarks (other",
  "See Remarks (partially Finished)",
  "See Remarks (unfinished)",
  "See Remarks)",
  "Sep",
  "Slab",
  "Suite",
  "Unfin",
  "Unknown",
  "Unknown (not Applicable)",
  "Unknown (other",
  "Unknown (partially Finished)",
  "Unknown (unfinished)",
  "Unknown (unknown)",
  "W/O",
  "W/U",
  "Y"
]
save original in another fld.
https://www.squareone.ca/resource-centres/getting-to-know-your-home/basements

###


###
  @param {array} prop.bsmt, only in ddf and treb.
  copy orginal to orgiBsmt. format to prop.bsmt
###

exports.formatBasement = formatBasement = (prop)->
  return unless prop.bsmt?.length
  prop.origBsmt = prop.bsmt
  # if typeof prop.bsmt is 'string'
  #   prop.bsmt = prop.bsmt.split(' ')
  if Array.isArray(prop.bsmt)
    bsmtStr = prop.bsmt.join(' ')
  else
    bsmtStr = prop.bsmt
  bsmtStr = bsmtStr.replace(/,|\(|\)/g,' ')
  # Replace "Walk Out" with "Walk-Out" and "Walk Up" with "Walk-Up" and "Not Applicable" with "Not-Applicable"
  # NOTE: 后面会进行split(' ')操作,会导致Walk Out/Walk Up/Not Applicable元素format错误,所以需要先替换
  bsmtStr = bsmtStr.replace(/\bwalk\s+out\b/i, 'Walk-Out')
  bsmtStr = bsmtStr.replace(/\bwalk\s+up\b/i, 'Walk-Up')
  bsmtStr = bsmtStr.replace(/\bnot\s+applicable\b/i, 'Not-Applicable')
  bsmtArr = bsmtStr.split(' ')
  
  ret = {}
  bsmtMapping = [
    {regx:/^Full/i,fld:'Full'}
    {regx:/^Half/i,fld:'Half'}
    {regx:/^(Finished|Fin|Remodeled)/i, fld:'Fin'},
    {regx:/W\/O|Walk[\-]*Out|Walkout/i, fld:'W/O'},
    {regx:/^(Unfinished|Unfin)/i, fld:'unFin'},
    {regx:/^(Partial|part)/i, fld:'Prt'},
    {regx:/^(Sep|Separate|Entrance|Exterior)/i, fld:'Sep'},
    {regx:/^(Apartment|Apt|Suite)/i, fld:'Apt'},
    {regx:/^(Walk[\-]*Up|W\/U)$/i, fld:'W/U'},
    {regx:/^(None|Non|N|No|N\/A)$/i, fld:'NON'},
    {regx:/^(NAN|Unknown|Not[\-]*Applicable)/i, fld:'NAN'},
    {regx:/^(Crawl|Crw|Low)/i, fld:'Crw'},
    {regx:/^(Slab|Cellar|Dugout)/i, fld:'Slab'},
    {regx:/^Y$/i, fld:'Y'},

  ]
  for b in bsmtArr
    continue unless b
    b = strCapitalize b?.trim(), true
    for obj in bsmtMapping
      if obj.regx.test b
        ret[obj.fld] = 1
  prop.bsmt = Object.keys(ret)
  if not prop.bsmt.length
    delete prop.bsmt
  return prop.bsmt

#if new does not has origId,copy self id to origId.
# BCER origId =262274914:0, should push _id (eg:BCER2253287)in
setOrigId=(obj)->
  if not obj.origId
    obj.origId = [obj._id]
  else if 'string' is typeof obj.origId
    obj.origId = [obj.origId]
  if obj.origId.indexOf(obj._id)<0
    obj.origId.push obj._id
  

formatAddress = (prop, metaInfo)->
  origAddressInfo = {}
  checkDiffFieldsArray = ['addr','unt','city','prov','cmty','st','st_num']
  for fld in checkDiffFieldsArray
    origAddressInfo[fld] = prop[fld]
  try
    propAddrHelper.formatProvAndCity(prop,true)
  catch e #city,prov,cnty必须是number
    debug.error e
  # debug.info 'origAddressInfo: ',origAddressInfo, prop.addr
  # debug.info 'prop addr',prop.addr,prop.unt,prop.st,prop.st_num
  if prop.unt is '0'
    delete prop.unt
  #after format the
  for fld in checkDiffFieldsArray
    if (origAddressInfo[fld]?.toString()) isnt prop[fld]?.toString()
      # only copy origAddr etc when orig is not same with new
      if origAddressInfo[fld]
        prop[propertiesHelper.ADDR_TO_ORIG_FLD_MAPPING[fld]] = origAddressInfo[fld]
      metaInfo[fld] ?= {orig:origAddressInfo[fld]} # for data test
      #origCity 可能已经存在，存在的不覆盖。ddf里可能有origCity.
      metaInfo[fld][fld] = prop[fld]

  #delete fld when value is number
  for fld in ['city','prov','cmty','cnty']
    if prop[fld] and (/^\d*\.?\d*$/.test (prop[fld]))
      metaInfo.deleteFields ?= []
      metaInfo.deleteFields.push {
        fld: fld,
        val: prop[fld],
        msg: "Bad #{fld} type, dropped"
      }
      delete prop[fld]

  #log house has unit number
  if prop.unt and (/House|Detached/.test prop.ptype2)
    metaInfo.houseHasUnt =
      origAddr: prop.origAddr or prop.addr
      unt: prop.unt
      addr: prop.addr
      ptype2: prop.ptype2
      city: prop.city

###
format sqft, parse sqft1 and sqft2 from sqft
###
formatSqft =(prop)->
  if typeof prop.sqft is 'number'
    prop.sqft1 = prop.sqft2 = prop.sqft
    if (prop.ptype is 'r') and \
      ((prop.sqft > MAX_SQFT) or (prop.sqft < MIN_SQFT))
        delete prop.sqft1
        delete prop.sqft
        delete prop.sqft2
  else if prop.sqft and (not (prop.sqft1 or prop.sqft2))
    [prop.sqft1,prop.sqft2] = propertiesHelper.getEdges prop,'sqft'
    if prop.sqft2 < 10
      delete prop.sqft
      delete prop.sqft1
      delete prop.sqft2

    # parse failed.set sqft to sqft1 and sqft2
    # should not copy unmatched pattern? sqft1,sqft2 should be number
    if (not prop.sqft1) and (not prop.sqft2)
      prop.sqft1 = prop.sqft2 = prop.sqft



formatTrbtp =(prop)->
  # trbtp to lowercase
  if prop.trbtp? and Array.isArray(prop.trbtp)
    trbtp = []
    for t in prop.trbtp
      trbtp.push t.toLowerCase()
    prop.trbtp = trbtp


formatNumberFields = (prop)->
  for fld in NUMBER_FIELDS
    if prop[fld]
      prop[fld] = round(parseNumberIfPossible(prop[fld]),2)

copyGrForApartment = (prop)->
  if (prop.ptype2 and ('Apartment' in prop.ptype2)) and \
    (not prop.gr) and (prop.park_spcs)
      prop.gr = prop.park_spcs

module.exports.formatPossDate = formatPossDate =  (poss_date)->
  return inputToDateNum(poss_date)

module.exports.formatProp = formatProp = (prop, metaInfo)->
  # debug.debug 'formatProp unset',prop.metaInfo.unset
  #convert to number for possible fields, keep null value.do this before other convert.
  prop = objectStr2NumNClean prop,true

  setOrigId(prop)
  formatAddress(prop, metaInfo)
  formatSqft(prop)
  formatTrbtp(prop)
  formatBasement(prop)

  prop.uaddr = unifyAddress prop

  # make sure these fields are string
  for fld in ['comm','sid','st']
    prop[fld] = ''+prop[fld] if prop[fld]
  
  prop.src = 'TRB' if prop.src is 'TREB'


  # daddr
  if (not prop.daddr) and prop.disp_addr?
    prop.daddr ?= prop.disp_addr

  #format ptype
  #ptype:['b','r','o'],    "Commercial","Other","Residential"
  prop.ptype = ptypeMapping[prop.ptype] or 'o' if prop.ptype
  
  checkPropPrice(prop, metaInfo)
  #need onD to find duplicate prop
  setImportDate(prop, {}, metaInfo)
  offerD = propertiesHelper.getOfferDateFromBm prop
  if offerD?
    prop.offerD = offerD
  {isEstate, isPOS} = propertiesHelper.parseOwner  \
    {owner:prop.owner, seller:prop.seller, _id:prop._id}
  if isEstate
    prop.isEstate = isEstate
  if isPOS
    prop.isPOS = isPOS

  #parse bltYr from age
  if (prop.age is 'New') and prop.onD
    prop.bltYr = parseInt(prop.onD.toString().substr(0,4))

  formatNumberFields(prop)
  deleteRootEmptyFields(prop)
  copyGrForApartment(prop)
  if prop.poss_date
    prop.poss_date = formatPossDate(prop.poss_date)
  prop.bdrms ?= prop.tbdrms if prop.tbdrms
  checkDom(prop)
  setSstpAndSptp(prop)
  prop


#do not unset empty fields. cause they might from other source.
deleteRootEmptyFields = (prop)->
  for k,v of prop
    if isNullOrEmpty(v)
      delete prop[k]
      # prop.metaInfo.unset?={}
      # prop.metaInfo.unset[k]=1

###*
 * @param {object} prop
 * @return {*}
 * only ddf has For sale or rent, other source一个record都是一种类型。
 * properties->propertiesNew 需要清洗整理。
###
checkPropPrice = (prop, metaInfo)->
  #do not check non residential or lpunt by acre or sqft,lpunt 不是我们加上的。
  if (prop.ptype isnt 'r') or \
    (prop.lpunt and (not (prop.lpunt in ['For Sale','For Lease'])))
      # debug.debug 'checkPropPrice return'
      return

  origSaletp = prop.saletp
  origLp = prop.lp
  origLpr = prop.lpr
  ###
    if lp is same with lpr, delete lp or lpr according to the number
  ###
  if prop.lp and prop.lpr
    if prop.lp is prop.lpr #lpr== lpr
      if 1 < prop.lp < SAFE_LEASE_PRICE # 1<lp< 5000
        metaInfo.lp= {
          lp:prop.lp,
          lpr:prop.lpr,
          saletp:prop.saletp,
          msg:'delete lp,set as Lease'
        }
        delete prop.lp
        prop.saletp=['Lease']
      else if prop.lp > SAFE_SALE_PRICE # lp>100000
        metaInfo.lpr= {
          lp:prop.lp,
          lpr:prop.lpr,
          saletp:prop.saletp,
          msg:'delete lpr,set as sale'
        }
        delete prop.lpr
        prop.saletp=['Sale']
      # 5000<lp<100000,或者lp=lpr<=1 saletp=['lease'] 既可以是lease，也可以是sale，根据saletp确定。
      else if (prop.saletp?.length is 1)
        if prop.saletp?[0] is 'Lease'
          metaInfo.lp= {
            lp:prop.lp,
            lpr:prop.lpr,
            saletp:prop.saletp,
            msg:'delete lp'
            }
          delete prop.lp
        else if prop.saletp?[0] is 'Sale' #no real data like this sale
          metaInfo.lpr= {
            lp:prop.lp,
            lpr:prop.lpr,
            saletp:prop.saletp,
            msg:'delete lpr'
          }
          delete prop.lpr
        else
          #saletp is not lease or sale
          #logError()
          metaInfo.invalidSaleTp= {saletp:prop.saletp}
      else
        #saletp length is 2
        #logError()
        metaInfo.lpEqualLpr= {saletp:prop.saletp,lp:origLp,lpr:origLpr}
    #lp和lpr都存在，但是lpr != lp,log error
    else if (prop.saletp?.length is 1) or \
      (prop.lp < MIN_SALE_PRICE) or (prop.lpr > MAX_LEASE_PRICE)
        #logError()
        metaInfo.lpNotEqualLpr= {saletp:prop.saletp,lp:origLp,lpr:origLpr}
  else if prop.lp?  #只有lp 有的case只是价格写错了，不应该纠正成sale DDF17217320
    if (prop.saletp?.indexOf('Sale')>=0 ) and (prop.lp > MIN_SALE_PRICE)
      prop.saletp=['Sale']
    else
      metaInfo.lpOutOfRange= {saletp:prop.saletp,lp:origLp}
  else if prop.lpr? #只有lpr
    if (prop.saletp?.indexOf('Lease')>=0) and (prop.lpr < MAX_LEASE_PRICE)
      prop.saletp=['Lease']
    else
      metaInfo.lprOutOfRange= {saletp:prop.saletp,lp:origLp}
  else # logError if no lp and lprs
    metaInfo.noLpAndLpr= {saletp:prop.saletp,lp:origLp,lpr:origLpr}

  #save origLp, origLpr, saletp when changed.
  if (origLp or prop.lp)  and (origLp isnt prop.lp)
    prop.origLp = origLp
    metaInfo.lp = {origLp:origLp,fixedLp:prop.lp}
  if (origLpr or prop.lpr) and (origLpr isnt prop.lpr)
    prop.origLpr = origLpr
    metaInfo.lpr = {origLpr:origLp,fixedLpr:prop.lp}
  if JSON.stringify(origSaletp) isnt JSON.stringify(prop.saletp)
    prop.origSaletp = origSaletp
    metaInfo.saletp = {origSaletp:origSaletp,fixSaletp:prop.saletp}

# return prop on market date
getPropOnD = (newProp)->
  onD = newProp.ld \
    or newProp.ListingContractDate \ #ld,input_date,ListingContractDate already mapping to number
    or newProp.input_date \
    or newProp.lstd \
    or newProp.picts or newProp.phomt \
    or newProp.lup \ # lup ddf last updated date.
    or newProp.lud\  #lud treb last update date.
    or newProp.ts  or newProp.timestamp_sql
  return onD

# TODO: use functional programming
# @return onD type date
setPropOnD = (newProp, metaInfo)->
  todayDateNum = inputToDateNum(new Date())
  onD = getPropOnD(newProp)
  newProp.onD = inputToDateNum(onD) if onD
  # debug.debug 'onD',newProp
  if newProp.onD and (newProp.onD > todayDateNum)
    metaInfo.onD={msg:"#{newProp.onD} is a future Date"}
    newProp.onD = todayDateNum
  newProp

#set newProp.onD offD by merge newProp and oldProp
module.exports.setImportDate = setImportDate = (newProp, oldProp, metaInfo)->
  origSldd = newProp.sldd
  setPropOnD(newProp, metaInfo)
  mt = inputToDateNum(newProp.mt) if newProp.mt
  exp = inputToDateNum(newProp.exp) if newProp.exp
  offDComputedByMtAndExp = if mt > exp then exp else mt
  # SoldDate, cd, sldd, unavail_dt
  if (newProp.lst in ['Sld','Lsd','Pnd','Cld']) #Sc,Lc
    soldDate = newProp.SoldDate or newProp.solddate or newProp.Cd or newProp.cd \
      or oldProp?.SoldDate or oldProp?.solddate or oldProp?.Cd or oldProp?.cd \
      or newProp.sldd or oldProp?.sldd \
      or newProp.unavail_dt or newProp.lud \
      or newProp.sremD or newProp.cmpD or offDComputedByMtAndExp
    newProp.sldd = inputToDateNum(soldDate) if soldDate

  #unavail_dt,dt_ter
  if newProp.status is 'U'
    offMarketDate = newProp.sldd or oldProp?.sldd \
      or newProp.unavail_dt or oldProp?.unavail_dt \
      or newProp.dt_ter or oldProp?.offD \
      or newProp.exp or newProp.lud or newProp.lup \
      or newProp.sremD or newProp.cmpD or offDComputedByMtAndExp
    # offD在1970年之前的房源，offD改为1970-01-01
    # if new Date(offMarketDate) < new Date('1970-01-01')
    # NOTE: offMarkeyDate可能为number date,此时使用new Date()会存在问题
    if (inputToDateNum offMarketDate) < 19700101
      debug.warn "prop.offD less than 1970-01-01,prop.id:#{newProp._id},offD:#{offMarketDate}"
      offMarketDate = '19700101' # NOTE: 存在时区问题,可能导致inputToDateNum返回19691231
    newProp.offD =  inputToDateNum(offMarketDate) #number

  #'onD','sldd','offD' should always before  today.
  todayDateNum = inputToDateNum(new Date())
  for fld in ['sldd','offD']
    debug.debug 'fld',fld, newProp[fld],todayDateNum
    if newProp[fld] and (newProp[fld] > todayDateNum)
      metaInfo[fld]={msg:"#{newProp[fld]} is a future Date"}
      newProp[fld] = todayDateNum
      debug.debug 'fix fld', fld,'newProp[fld]',newProp[fld]
    if newProp[fld] and (newProp[fld] < newProp.onD)
      metaInfo[fld] = {msg:"#{newProp[fld]}:#{newProp[fld]} is before onD:#{newProp.onD}"}
      newProp[fld] = todayDateNum

  if origSldd and (newProp.sldd isnt origSldd)
    metaInfo.sldd = {orig:origSldd, sldd:newProp.sldd}
    newProp.origSldd = origSldd

  delete newProp.stp
  delete newProp.lstd

  return newProp

checkDom = (prop) ->
  if (prop.status isnt 'U') or (prop.lst not in ['Sld', 'Pnd', 'Cld', 'Lsd'])
    delete prop.dom

# (node:26056) UnhandledPromiseRejectionWarning: TypeError: Cannot read property 'indexOf' of undefined
setSstpAndSptp = (prop) ->
  prop.sstp = if prop.saletp?.indexOf('Sale') >= 0 then 'S' else 'L'
  if prop.saletp?.length > 1
    if prop.lp and prop.lpr and (prop.lp is prop.lpr) and prop.lp < MIN_SALE_PRICE
      prop.sstp = 'L'

  [sptp, types] = propertiesHelper.getStandardTypeInChar prop
  prop.sptp = sptp
  return prop

module.exports.formatMfee = formatMfee = (record)->
  return unless ('object' is typeof record)
  if record.AssociationFeeFrequency?
    switch record.AssociationFeeFrequency
      when 'Annually'
        mfee = propertiesHelper.getFloat(record.AssociationFee / 12)
      when 'Semi-Annually'
        mfee = propertiesHelper.getFloat(record.AssociationFee / 6)
      when 'Quarterly'
        mfee = propertiesHelper.getFloat(record.AssociationFee / 3)
      when 'Seasonal'
        mfee = propertiesHelper.getFloat(record.AssociationFee / 3)
      when 'Bi-Monthly'
        mfee = propertiesHelper.getFloat(record.AssociationFee / 2)
      when 'Monthly'
        mfee = propertiesHelper.getFloat(record.AssociationFee)
      when 'Semi-Monthly'
        mfee = propertiesHelper.getFloat(record.AssociationFee * 2)
      when 'Weekly'
        mfee = propertiesHelper.getFloat((record.AssociationFee / 7) * 30)
      when 'Bi-Weekly'
        mfee = propertiesHelper.getFloat((record.AssociationFee / 14) * 30)
      when 'Daily'
        mfee = propertiesHelper.getFloat(record.AssociationFee * 30)
      when 'Other', 'One Time'
        debug.info 'cannot convert mfee for AssociationFeeFrequency: ' + record.AssociationFeeFrequency
      else
        debug.error "unknown mfeeunt: #{record.AssociationFeeFrequency}"
  else
    debug.warn "#{record._id} no mfeeunt"
  # 如果mfee计算成功，则设置mfee为计算值，mfeeunt为Monthly
  if mfee
    record.mfee = mfee
    record.mfeeunt = 'Monthly'
  else
    record.mfee = record.AssociationFee
    record.mfeeunt = record.AssociationFeeFrequency
  record.mfeeInc = record.AssociationFeeIncludes