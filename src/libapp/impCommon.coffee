mongodb = require('mongodb')
util = require('util')
zlib = require('zlib')
mongodb = require 'mongodb'
debugHelper = require '../lib/debug'
{ deepCopyObject, isEmpty, objectStr2NumNClean } = require '../lib/helpers_object'
libProp = require './properties'
debug = debugHelper.getDebugger()
FEET2METER = 0.3048
TREB_PHO_IS_LARGE = false

module.exports.PROP_SRC_LOG_MAPPING = PROP_SRC_LOG_MAPPING ={
  'CLG': { 'propKey': 'sid', 'logKey': 'sid'},
  'OTW': { 'propKey': 'sid', 'logKey': 'sid'},
  'TRB': { 'propKey': 'sid', 'logKey': 'id' },
  'RHB': { 'propKey': 'sid', 'logKey': 'id'},
  'DDF': { 'propKey': '_id', 'logKey': 'id', 'transform': (id) -> id.replace(/^DDF/, '') },
  'BRE': { 'propKey': 'SystemID', 'logKey': 'id', 'transform': (id) -> id.toString() }
}

module.exports.PROP_SRC_TS_MAPPING = PROP_SRC_TS_MAPPING = {
  'TRB': ['Timestamp_sql'],
  'DDF': ['@attributes','LastUpdated'],
}

# @description build query for getting the log collection
module.exports.buildGetLogCollQuery = buildGetLogCollQuery = (prop) ->
  findLogQuery = {}
  srcMapping = PROP_SRC_LOG_MAPPING[prop.src]
  if not srcMapping
    debug.error "Unknown source type: #{prop.src}"
    throw new Error("Unknown source type: #{prop.src}")
  propKey = srcMapping['propKey']
  logKey = srcMapping['logKey']
  if prop[propKey] is null or prop[propKey] is undefined
    debug.error "Missing or invalid propKey: #{propKey} for source: #{prop.src}"
    throw new Error("Missing or invalid propKey: #{propKey} for source: #{prop.src}")
  if srcMapping['transform']?
    findLogQuery[logKey] = srcMapping['transform'](prop[propKey])
  else
    findLogQuery[logKey] = prop[propKey]
  return findLogQuery


# @description get log docs in order
module.exports.getLogDocsInOrder = getLogDocsInOrder = (prop, coll) ->
  findLogQuery = buildGetLogCollQuery(prop)
  origProps = await coll.findToArray findLogQuery,{sort:{_mt:1}}
  return origProps


# @description get log docs for different source, and add _source field to each log doc
module.exports.getLogDocsForDiffSrc = getLogDocsForDiffSrc = (prop,COLL_SRC_LOGS_MAP)->
  coll = COLL_SRC_LOGS_MAP[prop.src]      # get the log collection
  if prop.src is 'TRB'
    if not prop.trbtp or prop.trbtp.length is 0
      # debug.info "no trbtp for TRB in prop #{prop._id}"
      return []
    logDocsEvow = []
    logDocsIdx = []
    if 'evow' in prop.trbtp
      logDocsEvow = await getLogDocsInOrder(prop, COLL_SRC_LOGS_MAP['TRB'])
      logDocsEvow.forEach (logDoc) -> logDoc._source = 'TRB'
    if 'idx' in prop.trbtp
      logDocsIdx = await getLogDocsInOrder(prop, COLL_SRC_LOGS_MAP['TRBX'])
      logDocsIdx.forEach (logDoc) -> logDoc._source = 'TRBX'
    # Filter out any empty entries before concatenating
    logDocsEvowFiltered = logDocsEvow.filter (logDoc) -> logDoc
    logDocsIdxFiltered = logDocsIdx.filter (logDoc) -> logDoc
    # Concatenate filtered log documents
    return logDocsEvowFiltered.concat(logDocsIdxFiltered)
  # For other `prop.src` values
  logDocs = await getLogDocsInOrder(prop, coll)
  logDocs.forEach (logDoc) -> logDoc._source = prop.src
  return logDocs


# @description Check if two data objects are the same, excluding specific timestamp fields
# get if two data are same except LastUpdated field
module.exports.sameDataExceptLastUpdated = (data1, data2) ->
  data1Copy = Object.assign({}, data1, {
    '@attributes': Object.assign({}, data1['@attributes'])
  })
  data2Copy = Object.assign({}, data2, {
    '@attributes': Object.assign({}, data2['@attributes'])
  })
  # Remove the @attributes.LastUpdated field from both objects if it exists
  if data1Copy['@attributes']? and data1Copy['@attributes']['LastUpdated']?
    delete data1Copy['@attributes']['LastUpdated']
  if data2Copy['@attributes']? and data2Copy['@attributes']['LastUpdated']?
    delete data2Copy['@attributes']['LastUpdated']
  if data1Copy['Timestamp_sql']?
    delete data1Copy['Timestamp_sql']
  if data2Copy['Timestamp_sql']?
    delete data2Copy['Timestamp_sql']
  return util.isDeepStrictEqual(data1Copy, data2Copy)


# Sample log entry:
# {
#     "_id" : "CarDownload-property",
#     "_mt" : ISODate("2024-11-20T05:32:36.479+0000"),
#     "count" : NumberInt(2797),
#     "mt" : ISODate("2024-11-20T05:32:36.479+0000"),
#     "next" : ISODate("2024-11-20T05:28:51.514+0000"),
#     "ts" : ISODate("2024-11-20T05:32:23.154+0000"),
#     "done" : ISODate("2024-11-20T05:28:51.514+0000")
# }
module.exports.updateResourceRequestLog = ({ MlsImportKeysCol, nextTs, resourceName, count, done=false, downloadName}) ->
  if not MlsImportKeysCol
    debug.error "MlsImportKeysCol is required for updateResourceRequestLog #{downloadName} #{resourceName}."
    return
  if not downloadName
    debug.error "downloadName is required for updateResourceRequestLog #{downloadName} #{resourceName}."
    return
  if not nextTs
    debug.error "nextTs is required for updateResourceRequestLog #{downloadName} #{resourceName}."
    return
  if not resourceName
    debug.error "resourceName is required for updateResourceRequestLog #{downloadName} #{resourceName}."
    return
  if not count? or isNaN(count)
    debug.error "count must be a valid number for updateResourceRequestLog #{downloadName} #{resourceName}."
    return
  keyId = "#{downloadName}-#{resourceName}"
  now = new Date()
  update =
    $set:
      mt: now
      next: new Date(nextTs)
    $setOnInsert:
      ts: now
    $inc:
      count: count

  if done
    update.$set.done = new Date(nextTs)
  else
    update.$unset = { done: "" }

  try
    await MlsImportKeysCol.updateOne {_id:keyId},update,{upsert:true}
    debug.info "Log entry updated successfully for #{resourceName} #{keyId}.", update
  catch error
    debug.error "Error updating log entry for #{resourceName} #{keyId}:", error


validateParams = (MlsImportKeysCol, resourceName, downloadName, functionName) ->
  if not MlsImportKeysCol
    throw new Error("MlsImportKeysCol is required for #{functionName}")
  if not resourceName
    throw new Error("resourceName is required for #{functionName}")
  if not downloadName
    throw new Error("downloadName is required for #{functionName}")


module.exports.getLastResourceRequestInfo = (MlsImportKeysCol, resourceName, downloadName) ->
  validateParams(MlsImportKeysCol, resourceName, downloadName, 'getLastResourceRequestInfo')
  keyId = "#{downloadName}-#{resourceName}"
  try
    log = await MlsImportKeysCol.findOne({ _id: keyId })
    return log
  catch error
    debug.error "Error retrieving lastResoRequestTs for #{resourceName} #{keyId}:", error
    return null


module.exports.ERROR_MESSAGES = ERROR_MESSAGES = {
  400: "Bad Request (400): Check request parameters."
  401: "Authorization Error (401): Check API credentials or permissions."
  403: "Authorization Error (403): Check API credentials or permissions."
  500: "Server Error (500): Bridge API issue, retrying."
}

module.exports.ignorableErrorReso = (error) ->
  errorMessage = if typeof error.message is 'string' then error.message else ''
  regex = /"code":\s*"?(\d+)"?,\s*"message":"([^"]+)"/
  detailedErrorMatch = errorMessage.match(regex)
  statusCode = if detailedErrorMatch then parseInt(detailedErrorMatch[1]) else null
  detailedMessage = if detailedErrorMatch then detailedErrorMatch[2] else 'Unknown error'
  debug.info "Parsed error details - Code: #{statusCode}, Message: #{detailedMessage}"
  # if exception is ignorable, return true
  if (statusCode is 400) and (detailedMessage.includes("Maximum value"))
    debug.warn "Retrying immediately due to Maximum value error."
    return true

  # Log standard errors if available
  if statusCode? and ERROR_MESSAGES[statusCode]?
    debug.error ERROR_MESSAGES[statusCode]
  else
    debug.error "Unexpected Error: #{errorMessage}"

  return false

module.exports.getEmptyFilterNStatsFunction = ->
  savePropertyCounts = 0
  fieldNullCounts = {}
  filterEmptyFieldsNSaveStats = (property,FieldStatsCol,FIELD_STATS_ID)->
    # statistic null and save every 1000 properties
    propertyCopy = deepCopyObject property
    savePropertyCounts++
    for key, value of property
      fieldNullCounts[key]= if fieldNullCounts[key] then fieldNullCounts[key] else 0
      if isEmpty(value)
        fieldNullCounts[key] = fieldNullCounts[key] + 1
        delete propertyCopy[key]
    if savePropertyCounts % 1000 is 0
      fieldNullCounts['savePropertyCounts'] = savePropertyCounts
      savedFieldNullCounts = Object.assign({}, fieldNullCounts)
      # Reset all counters in fieldNullCounts
      fieldNullCounts = {}
      savePropertyCounts = 0
      await FieldStatsCol.updateOne(
        { id: FIELD_STATS_ID },
        {
          $inc: savedFieldNullCounts,  # Increment the numeric fields, like null counts
          $setOnInsert: { ts: new Date() }  # Only set "ts" if the document is inserted
        },
        { upsert: true }  #If the document doesn't exist, create it
      )
    return propertyCopy
   # Return the function
  filterEmptyFieldsNSaveStats


module.exports.ROOM_MAP = ROOM_MAP =
  m: 'RoomDescription'
  features: 'RoomFeatures'
  a: 'RoomArea'
  au: 'RoomAreaUnits'
  asrc: 'RoomAreaSource'
  width: 'RoomWidth'
  length: 'RoomLength'
  wlu: 'RoomLengthWidthUnits'
  wlsrc: 'RoomLengthWidthSource'
  status: 'RoomStatus'
  key: 'RoomKey'


module.exports.convertRoom = (rawRoom) ->
  if not rawRoom?
    debug.error "rawRoom is required for convertRoom"
    return null
  _room = { 
    t: rawRoom?.RoomType
    l: rawRoom?.RoomLevel
  }
  for k,v of ROOM_MAP
    if not isEmpty(rawRoom[v])
      _room[k] = rawRoom[v]
  dimensions = rawRoom.RoomDimensions?.split('x')
  if Array.isArray(dimensions) and dimensions.length is 2
    if w = dimensions[1]
      _room.w = w
    if h = dimensions[0]
      _room.h = h
  if Array.isArray(rawRoom.RoomFeatures) and rawRoom.RoomFeatures.length is 0
    # Convert RoomFeature1, RoomFeature2, RoomFeature3 to array
    features = []
    i = 1
    while rawRoom["RoomFeature#{i}"]?
      if rawRoom["RoomFeature#{i}"]
        features.push(rawRoom["RoomFeature#{i}"])
      i++
    _room.features = features if features.length > 0
  return _room


module.exports.OPEN_HOUSE_MAP = OPEN_HOUSE_MAP =
  tp: 'OpenHouseType' # Public, Private
  status: 'OpenHouseStatus' # Active, Deleted
  f: 'OpenHouseStartTime'
  t: 'OpenHouseEndTime'
  key: 'OpenHouseKey'


module.exports.convertOpenHouse = (rawOpenHouse) ->
  if not rawOpenHouse?
    debug.error "rawOpenHouse is required for convertOpenHouse"
    return null
  openHouse = {}
  for k,v of OPEN_HOUSE_MAP
    if not isEmpty(rawOpenHouse[v])
      openHouse[k] = rawOpenHouse[v]
  return openHouse


module.exports.MEDIA_MAP = MEDIA_MAP =
  id: 'Order'
  status: 'MediaStatus'
  tp: 'MediaType'
  perm: 'Permission'
  h: 'ImageHeight'
  w: 'ImageWidth'
  url: 'MediaURL'
  key: 'MediaKey'
  sizeDesc: 'ImageSizeDescription'
  cat: 'MediaCategory'


module.exports.convertMedia = convertMedia = (rawMedia)->
  if not rawMedia?
    debug.error "rawMedia is required for convertMedia"
    return null
  media = {}
  for k,v of MEDIA_MAP
    if not isEmpty(rawMedia[v])
      media[k] = rawMedia[v]
  # Handle the description priority logic
  shortDesc = rawMedia.ShortDescription
  longDesc = rawMedia.LongDescription
  if shortDesc and longDesc and (shortDesc isnt longDesc)
    media.m = "#{shortDesc} \n#{longDesc}"
  else if shortDesc
    media.m = shortDesc
  else if longDesc
    media.m = longDesc
  return media

###*
# Converts a media object with abbreviated key names to full key names
# @param {Object} media - Media object with abbreviated key names
# @returns {Object} Media object with full key names
###
module.exports.convertMediaToFull = convertMediaToFull = (media) ->
  result = {}
  for key, value of media
    if MEDIA_KEY_REVERSE_MAPPING[key]?
      result[MEDIA_KEY_REVERSE_MAPPING[key]] = value
    else
      result[key] = value
  return result


# convert media abbreviated format to full format 
# and then convert to short readable format
module.exports.convertMediaAbbr = (rawMedia)->
  if not rawMedia?
    debug.error "rawMedia is required for convertMediaAbbr"
    return null
  rawMediaNew = convertMediaToFull(rawMedia)
  return convertMedia(rawMediaNew)

###*
# Compress entire media array using zlib
# @param {Array} mediaArray Array of media objects
# @param {Boolean} useBase64 Whether to return base64 encoded string (default: false)
# @returns {Buffer|String} Compressed media array as Buffer or base64 string
###
module.exports.compressMediaZlib = compressMediaZlib = (mediaArray, useBase64=false)->
  if not mediaArray
    debug.error "mediaArray is required for compressMediaZlib"
    return null
  try
    mediaStr = JSON.stringify(mediaArray)
    if mediaStr.length > 50 * 1024 * 1024 # 50MB 限制
      debug.error "mediaArray too large (> 50MB) for compression"
      return null
    compressed = zlib.gzipSync(mediaStr)
    return if useBase64 then compressed.toString('base64') else compressed
  catch error
    debug.error "Error compressing media array with zlib", {
      error: error.message
      mediaArrayLength: mediaArray.length
      originalSize: mediaStr?.length
    }
    return null

###*
# Decompress media array using zlib
# @param {Buffer|String} compressed Compressed media array as Buffer or base64 string
# @param {Boolean} isBase64 Whether input is base64 encoded (default: false)
# @returns {Array} Original media array
###
module.exports.decompressMediaZlib = (compressed, isBase64=false)->
  if not compressed
    debug.error "compressed data is required for decompressMediaZlib"
    return null
  if isBase64 and typeof compressed isnt 'string'
    debug.error "base64 input must be a string"
    return null
  try
    if compressed?.buffer
      buffer = compressed.buffer
    else if isBase64
      buffer = Buffer.from(compressed, 'base64')
    else
      if not Buffer.isBuffer(compressed)
        debug.error "compressed data must be a Buffer when isBase64 is false"
        return null
      buffer = compressed

    decompressed = zlib.gunzipSync(buffer)
    return JSON.parse(decompressed.toString())
  catch error
    debug.error "Error decompressing media array with zlib", {
      error: error.message
      isBase64
      inputType: typeof compressed
      inputSize: buffer?.length
    }
    return null


# compress media and room
module.exports.compressMediaRoom = compressMediaRoom = (property) ->
  if not property?
    debug.error "property is required for compressMediaRoom"
    return null
  compressed = deepCopyObject property
  if compressed.Media?
    compressed.Media = compressMediaZlib(compressed.Media)
  if compressed.Rooms?
    compressed.Rooms = compressMediaZlib(compressed.Rooms)
  return compressed


###*
# Process media (photos and video tours) from a property document
# @param {Object} mergedDoc The merged property document to process
# @returns {Object} Object containing:
#   - processedMedia: The document with processed media data
#   - fieldsToUnset: Fields that should be removed from the database
###
module.exports.processPropertyMedia = processPropertyMedia = (mergedDoc,id)->
  if not mergedDoc?
    throw new Error "mergedDoc is required for processPropertyMedia"
  if not id?
    debug.warn "id is required for processPropertyMedia"
   
  if mergedDoc.Media? and not Array.isArray(mergedDoc.Media)
    throw new Error "Media must be an array for processPropertyMedia"
  # Create a shallow copy of the input
  result = deepCopyObject mergedDoc
  fieldsToUnset = {}
  # Step 1: Check if we have any media
  hasMedia = result.Media?.length > 0
  if not hasMedia
    handleNoMedia(result, fieldsToUnset)
    return {
      processedMedia: result
      fieldsToUnset
    }
  # Step 2: Process video tours
  processVideoTours(result, fieldsToUnset,id)
  # Step 3: Process photos
  processPhotos(result, fieldsToUnset,id)

  # Step 4: Clean up Media field only if both photos and videos are processed
  fieldsToUnset.Media = true
  delete result.Media
  
  return {
    processedMedia: result
    fieldsToUnset
  }

###*
# Handle case when no media exists
###
handleNoMedia = (doc, fieldsToUnset)->
  handleNoPhotos(doc, fieldsToUnset)
  handleNoVideos(doc, fieldsToUnset)
  # Add Media field to unset
  fieldsToUnset.Media = true
  delete doc.Media

###*
# Handle case when no photos exist
###
handleNoPhotos = (doc, fieldsToUnset)->
  fieldsToUnset.pho = true
  fieldsToUnset.thumbUrl = true
  fieldsToUnset.phoUrls = true
  delete doc.pho
  delete doc.thumbUrl
  delete doc.phoUrls

###*
# Handle case when no videos exist
###
handleNoVideos = (doc, fieldsToUnset)->
  fieldsToUnset.vturl = true
  delete doc.vturl

###*
# Process video tours from media
###
processVideoTours = (doc, fieldsToUnset,id)->
  # Find all video tours
  videoTours = doc.Media.filter (media)->
    media.hasOwnProperty('MediaCategory') and /video/i.test(media.MediaCategory)
  
  if videoTours.length > 0
    # Get all valid video tour URLs
    videoTourUrls = videoTours
      .map((v) -> v.MediaURL) 
      .filter((url) -> url?) # Filter out undefined/null URLs
    
    if videoTourUrls.length > 0
      doc.vturl = videoTourUrls
      debug.debug "Video tours found", {
        id: id,
        listingKey: doc.ListingKey,
        totalVideoTours: videoTours.length,
        videoTourUrls
      }
      return
  handleNoVideos(doc, fieldsToUnset)

###*
# Process photos from media
###
processPhotos = (doc, fieldsToUnset,id)->
  # Filter for photos only
  filteredPhotos = doc.Media.filter (photo)->
    if photo.hasOwnProperty('MediaCategory')
      return /photo/i.test(photo.MediaCategory)
    return true

  debug.debug "Media processing", {
    id: id,
    listingKey: doc.ListingKey,
    originalMediaCount: doc.Media.length,
    filteredPhotoCount: filteredPhotos.length
  }

  if filteredPhotos.length > 0
    # Transform to simplified format,[{url:url,m:description},{url:url,m:description}...]
    simplifiedPhotos = filteredPhotos.map (photo)->
      photoObj = { url: photo.MediaURL }
      # Handle the description priority logic - LongDescription takes precedence
      shortDesc = photo.ShortDescription
      longDesc = photo.LongDescription
      if longDesc and shortDesc and (longDesc isnt shortDesc)
        photoObj.m = "#{longDesc} \n#{shortDesc}"
      else
        photoObj.m = longDesc or shortDesc
      return photoObj

    # Save photo data
    doc.pho = simplifiedPhotos.length
    doc.thumbUrl = simplifiedPhotos[0].url
    compressedPhotos = compressMediaZlib(simplifiedPhotos)
    doc.phoUrls = new mongodb.Binary(compressedPhotos, mongodb.Binary.SUBTYPE_BYTE_ARRAY)
  else
    # No valid photos found
    handleNoPhotos(doc, fieldsToUnset)


###*
# Check if phoUrls is a binary data
# @param {Object} phoUrls - The phoUrls object to check
# @returns {Boolean} True if phoUrls is a binary data, false otherwise
###
module.exports.isBinary = (phoUrls)->
  return phoUrls?.buffer? and phoUrls.buffer instanceof Buffer


module.exports.formatDaddr = formatDaddr = (record)->
  if record.InternetAddressDisplayYN is false
    record.daddr = 'N'
  else
    record.daddr = 'Y'
  delete record.InternetAddressDisplayYN


module.exports.doMapping = doMapping = (origRecord, fieldMap)->
  unless origRecord? and typeof origRecord is 'object'
    throw new Error 'origRecord must be a valid object'
  unless fieldMap? and typeof fieldMap is 'object'
    throw new Error 'fieldMap must be a valid object'

  record = Object.assign {}, origRecord
  record = objectStr2NumNClean(record, true)
  for k, mappedK of fieldMap
    if record.hasOwnProperty k
      if record[mappedK]? and (Array.isArray(record[mappedK])) and (Array.isArray(record[k]))
        record[mappedK] = record[mappedK].concat record[k]
      else
        record[mappedK] ?= record[k]
      delete record[k]
    else
      debug.debug "no #{k} in record"
  return record

# 根据不同来源，MlsStatus，StandardStatus，Status，得到对应的lst，status，saleTpTag, lstStr
# 房源来源是数据库中存的src，便于前端使用src获取mapping数据;
# sold/leased通过房源类型进行判断，这里统一写Sold。sale也是通过房源销售类型进行判断，所以这里不写saleTapTag: 'Sale'
module.exports.statusLstMapping = statusLstMapping = {
  'Active': {
    TRB:{status: 'A', lst: 'New' },
    BRE:{ status: 'A', lst: 'New' },
    CAR:{ status: 'A', lst: 'New' },
    CLG: { status: 'A', lst: 'New' },
    EDM: { status: 'A', lst: 'New' },
    OTW: { status: 'A', lst: 'New' },
    RHB: { status: 'A', lst: 'New' }
  },
  'ACT': {
    TRB:{status: 'A', lst: 'New' }
  },
  'CAN': {
    TRB:{status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1}
  },
  'Draft': {
    TRB:{status: 'U', lst: 'Draft' }
  },
  'Previous Status': {
    TRB:{status: 'U', lst: 'Previous Status' }
  },
  'New': {
    TRB:{status: 'A', lst: 'New' }
  },
  'Extension': {
    TRB:{status: 'A', lst: 'New',lstStr: 1 }
  },
  'Active Under Contract': {
    TRB:{status: 'A', lst: 'Auc', lstStr: 1 },
    BRE: { status: 'A', lst: 'Auc', lstStr: 1}
  },
  'Sold Conditional': {
    TRB:{status: 'A', lst: 'Sc', lstStr: 1 }
  },
  'Sold Conditional Escape': {
    TRB:{status: 'A', lst: 'Sc', lstStr: 1 }
  },
  'Leased Conditional': {
    TRB:{status: 'A', lst: 'Lc', lstStr: 1 }
  },
  'Leased Conditional Escape': {
    TRB:{status: 'A', lst: 'Lc', lstStr: 1 }
  },
  'Conditional': {
    CAR: { status: 'A', lst: 'Sc',  lstStr: 1 },
    EDM: { status: 'A', lst: 'Sc', lstStr: 1 },
    OTW: { status: 'A', lst: 'Sc', lstStr: 1 }
  },
  'Sold': {
    TRB:{status: 'U', lst: 'Sld', saleTpTag: 'Sold' },
    BRE: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' }, # NOTE: No 'Sold' in BCRE RESO
    CAR: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' },
    CLG: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' },
    EDM: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' },
    OTW: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' },
    RHB: { status: 'U', lst: 'Sld', saleTpTag: 'Sold' }
  },
  'Leased': {
    TRB:{status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    CAR:{ status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    CLG: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' }, # NOTE: 目前没有,需要观察
    EDM: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    OTW: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' }
  },
  'Rented': {
    BRE: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    CAR: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    CLG: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' }, # NOTE: 目前没有,需要观察
    EDM: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' },
    OTW: { status: 'U', lst: 'Lsd', saleTpTag: 'Sold' }
  },
  'Expired': {
    TRB:{ status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    BRE: { status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    CAR:{ status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    CLG: { status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    EDM: { status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    OTW: { status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 },
    RHB: { status: 'U', lst: 'Exp', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Terminated': {
    TRB:{ status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    BRE: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    CLG: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Suspended': {
    TRB:{ status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    BRE: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    CAR: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    EDM: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    OTW: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    RHB: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Incomplete': {
    TRB:{ status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Withdrawn': {
    TRB:{ status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    CLG: { status: 'U', lst: 'Wd', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Closed': {
    TRB:{ status: 'U', lst: 'Cld', saleTpTag: 'Sold', lstStr: 1 },
    BRE: { status: 'U', lst: 'Cld', saleTpTag: 'Sold', lstStr: 1 },
    CAR: { status: 'U', lst: 'Cld', saleTpTag: 'Sold', lstStr: 1 }
  },
  'Cancelled': {
    TRB:{ status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    BRE: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    CAR:{ status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    EDM: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    OTW: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 },
    RHB: { status: 'U', lst: 'Ter', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Price Change': {
    TRB:{ status: 'A', lst: 'Pc'}
  },
  'Deal Fell Through': {
    TRB:{ status: 'U', lst: 'Dft', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Pending': {
    TRB:{ status: 'U', lst: 'Pnd', saleTpTag: 'Sold', lstStr: 1 },
    BRE: { status: 'U', lst: 'Pnd', saleTpTag: 'Sold', lstStr: 1 },
    CAR: { status: 'U', lst: 'Pnd', saleTpTag: 'Sold', lstStr: 1 },
    CLG: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 },
    EDM: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 } # NOTE: EDM和CREB的pending含义是suspend
  },
  'Delete': {
    TRB:{ status: 'U', lst: 'Del', saleTpTag: 'Delisted', lstStr: 1 },
    CAR: { status: 'U', lst: 'Del', saleTpTag: 'Delisted', lstStr: 1 }
    EDM: { status: 'U', lst: 'Del', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Cancel Protected': {
    BRE: { status: 'U', lst: 'Cp', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Hold all Action': {
    BRE: { status: 'U', lst: 'Haa', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Hold': {
    BRE: { status: 'U', lst: 'Haa', saleTpTag: 'Delisted', lstStr: 1 },
    CAR: { status: 'U', lst: 'Sus', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Collapsed': {
    BRE: { status: 'U', lst: 'Clp', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'Deleted': {
    BRE: { status: 'U', lst: 'Del', saleTpTag: 'Delisted', lstStr: 1 },
    TRB: { status: 'U', lst: 'Del', saleTpTag: 'Delisted', lstStr: 1 }
  },
  'NotFound':{
    TRB: { status: 'U', dllst:'NoF' }
  }
}

###*
# Filter and compress TrebReso media for active, public, medium-sized photos and virtual tours
# @param {Array} media Array of media objects to process
# @param {String} id Property ID for logging
# @returns {Object} Object containing:
#   - thumbUrl: URL of the first photo (thumbnail)
#   - phoNum: Number of photos after filtering
#   - compressedMedia: Compressed media URLs in MongoDB Binary format
#   - vturl: Array of virtual tour URLs
###
module.exports.filterCompressTrebResoMedia = ({media, id})->
  validateUrl = (url) ->
    return false if not url?
    try
      new URL(url)
      return true
    catch
      return false

  if not media?.length
    debug.debug "No media found for property", {id}
    return {
      thumbUrl: null
      phoNum: 0
      compressedMedia: null
      vturl: null
    }

  # Get virtual tours first
  virtualTours = media.filter (item)->
    item.hasOwnProperty('MediaCategory') and /Virtual/i.test(item.MediaCategory)

  # Process virtual tour URLs
  vturl = if virtualTours.length > 0
    virtualTours
      .map((v) -> v.url)
      .filter(validateUrl)
  else
    null

  mediaPhotos = media.filter (item)->
    if item.cat?
      return /photo/i.test(item.cat)
    return /image|jpeg/.test(item.tp)

  filteredMedia = libProp.filterPhoUrls(mediaPhotos, TREB_PHO_IS_LARGE)
  
  debug.debug "Media filtering results", {
    id,
    totalMedia: media.length,
    filteredCount: filteredMedia.length,
    virtualTourCount: virtualTours.length
  }

  if filteredMedia.length is 0
    debug.debug "No matching media found after filtering", {id}
    return {
      thumbUrl: null
      phoNum: 0
      compressedMedia: null
      vturl: vturl
    }

  firstPhotoId = filteredMedia[0]?.id || 0
  thumbPhoto = media.find (photo)->
    photo.id is firstPhotoId and photo.sizeDesc is 'Thumbnail'
  if thumbPhoto
    thumbUrl = if /^http/i.test(thumbPhoto.url) then thumbPhoto.url else "https:#{thumbPhoto.url}"
  else
    thumbUrl = filteredMedia[0]?.url

  # Transform to simplified format for compression
  simplifiedMedia = filteredMedia.map (media)->
    mediaObj = {
      url: media.url
    }
    if media.m?
      mediaObj.m = media.m
    return mediaObj

  # Compress the filtered media array
  try
    compressedMedia = compressMediaZlib(simplifiedMedia)
    return {
      thumbUrl: thumbUrl
      phoNum: filteredMedia.length
      compressedMedia: new mongodb.Binary(compressedMedia, mongodb.Binary.SUBTYPE_BYTE_ARRAY)
      vturl
    }
  catch error
    debug.error "Error compressing media for property", {
      id,
      error: error.message
    }
    return {
      thumbUrl: null
      phoNum: 0
      compressedMedia: null
      vturl
    }


# Mapping between full and abbreviated media keys
MEDIA_KEY_MAPPING = {
  # Full -> Abbreviated
  ClassName: 'CN',
  ImageHeight: 'IH',
  ImageOf: 'IO',
  ImageSizeDescription: 'ISD',
  ImageWidth: 'IW',
  LongDescription: 'LD',
  MediaCategory: 'MC',
  MediaHTML: 'MHTML',
  MediaKey: 'MK',
  MediaModificationTimestamp: 'MMT',
  MediaObjectID: 'MOID',
  MediaStatus: 'MS',
  MediaType: 'T',
  MediaURL: 'MURL',
  ModificationTimestamp: 'MT',
  Order: 'O',
  Permission: 'P',
  PreferredPhotoYN: 'PPYN',
  ResourceName: 'RN',
  ResourceRecordKey: 'RRK',
  ShortDescription: 'SD',
  SourceSystemID: 'SSID',
  SourceSystemMediaKey: 'SSMK',
  SourceSystemName: 'SSN'
}

# Reverse mapping (Abbreviated -> Full)
MEDIA_KEY_REVERSE_MAPPING = {}
for fullKey, abbrevKey of MEDIA_KEY_MAPPING
  MEDIA_KEY_REVERSE_MAPPING[abbrevKey] = fullKey

###*
# Converts a media object with full key names to abbreviated key names
# @param {Object} media - Media object with full key names
# @returns {Object} Media object with abbreviated key names
###
module.exports.convertMediaToAbbreviated = (media) ->
  result = {}
  for key, value of media
    # Skip null values
    continue if value is null
    
    if MEDIA_KEY_MAPPING[key]?
      result[MEDIA_KEY_MAPPING[key]] = value
    else
      result[key] = value
  return result



###*
# 检查并转换房源占地面积
# 如果lotsz小于50，认为单位是英亩，转换为平方英呎
# 如果lotsz大于等于50，直接返回原值
# @param {Number|String} lotsz - 房源占地面积
# @returns {Number} 转换后的占地面积（平方英呎）
###
module.exports.convertLotSize = (lotsz) ->
  return null if not lotsz?
  
  # 确保lotsz是数字
  numLotsz = if typeof lotsz is 'string' then parseFloat(lotsz) else lotsz
  
  # 检查是否为有效数字
  if isNaN(numLotsz)
    return null
    
  # 如果小于50，认为单位是英亩，需要转换为平方英呎
  # 1英亩 = 43560平方英呎
  if numLotsz < 50
    return numLotsz * 43560
  
  # 如果大于等于50，直接返回
  return numLotsz

###*
# 格式化Virtual Tour URL字段的通用函数
# @description 处理多个VirtualTour字段并合并为vturl字段
# @param {Object} record - 记录对象
# @param {Array} fields - 需要处理的字段名数组
# @returns {Object} 处理后的记录对象
###
module.exports.formatVturl = (record, fields) ->
  if not record? or not Array.isArray(fields)
    debug.error "formatVturl requires valid record and fields array"
    return record
    
  # 提取所有非空的URL
  urls = fields.map((field) -> record[field]).filter((value) -> value?)
  # 数组去重
  urls = Array.from(new Set(urls))
  
  # 根据URL数量设置vturl字段
  record.vturl = if urls.length > 1 then urls else urls[0] or null

  # 清理原始字段
  for field in fields
    delete record[field]
    
  return record
