###
condo_sqft_middle structure:
  #get sqft of prop.sqft or remark, sqft1,sqft2
  _id: uaddr+'#'+unt+'@'+YYYYMMDD
  uaddr: uaddr
  unt: i.e. 2012
  untEnding: i.e. 12 or array for multiple units for the same layout
  untLevel: i.e. 20 or array for multiple levels
  sqft: 750 # for estimated sqft with sqft1 and sqft2
  sqft1: 700
  sqft2: 800
  estSqft: 789 # for estimated sqft without s1/s2
  sqfts:[750,89]
  matchedStrings:['...750 St..','...89 St..']
  blt1: 2012
  blt2: 2018
  srcIds:[srcId]

condo_floorplan structure: (Not finished)
  _id: uaddr+'#'+floorplanId
  uaddr: uaddr
  floorplanId: floorplanId
  sqft: 750
  balcony: 100
  unt: [unt]
  level: [level]
  url: url of floorplan
  m: description

condo_sqfts structure:
  _id: uaddr + '#' + unit or uaddr  + '@' + unit-ending  or uaddr
  uaddr: uaddr
  unt: unit
  ending: last two digits of unit
  sqft: last one
  sqfts:
  estSqft: last one
  estSqfts
  ids:[] mls ids
  blt1/blt2: for uaddr only
Townhouse has no unit-ending records.
###
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
Kafka = INCLUDE '../lib/kafka'
gIsDebug = false

helpers = require '../lib/helpers'
gCondosToGetSqftTopic = null
SQFT_MIN_DIFF = 0.2

module.exports._config_sections = ['kafka','sqft']
exports._config = _config = (cfg)->
  # ignoreThisServer: on avion/dBc dont produce to kafka topic, its a dup record
  if cfg.kafka?.main?.ignoreThisServer
    return
  gCondosToGetSqftTopic = cfg.sqft?.kafka?.topic?.condosToGetSqft

LEVEL_STRINGS = ['PH','LPH','SPH','GPH','RG','TPH','TH','LP']
# return {level,ending}
exports.splitLevelEnding = splitLevelEnding = (unit)->
  unit = unit.toString().toUpperCase().trim()
  unitFormated = unit.replace /[^0-9a-z\/\-]/ig,''
  unitToMatch = unitFormated.replace /[^0-9a-z\/]/ig,''
  if matched = unitToMatch.match /^(\D+)?(\d+)(\D)?$/
    # ["Ph1204", "Ph", "1204", undefined]
    # match B403 -> B4, 03, 4, B403
    if (matched[2].length is 4) or \
      ((matched[2].length is 3) and \
      ((not matched[1]) or (matched[1] in LEVEL_STRINGS)))
        #'04'
        ending = matched[2].substr(-2) + (matched[3] or '')
        #'PH12'
        level = (matched[1] or '') + matched[2].substr(0,matched[2].length-2)
        #12
        levelNum = parseInt(matched[2].substr(0,matched[2].length-2))
        if (matched[1] in LEVEL_STRINGS) and (levelNum <3)
          levelNum = undefined
    else if (matched[2].length is 2) and \
      ((matched[1] in LEVEL_STRINGS))
        # G03
        ending = matched[2]
        level = (matched[1] or '')
    else if m2 = unit.match /^(\D+)?(\d+)?(-|\s)(\S+)$/
      # ["-Ph2-04", "-", "", "04", undefined]
      ending = m2[4]
      level = (m2[1] or '') + (m2[2] or '')
    else # <=3 or >=5 and no seperators
      ending = matched[2] + (matched[3] or '')
      level = ''
      if matched[1]
        if matched[1] in LEVEL_STRINGS
          level = matched[1]
        else
          ending = matched[1] + ending
    return {level,ending,levelNum,unit:unitFormated}
  # PH-G
  if matched = unit.match /^(\w+)-(\w+)$/
    return {level:matched[1],ending:matched[2],unit:unitFormated}
  # PH G
  if (matched = unit.match /^(\w+)\s(\w+)$/) and (matched[1] in LEVEL_STRINGS)
    return {level:matched[1],ending:matched[2],unit:unitFormated}
  # unknown format
  debug.verbose('Unknown unit format',unit)
  return {level:'',ending:'',unit:unitFormated}
  # TODO: unit test for these cases
  # 123
  # Ph8: 130
  # Ph 1: 103
  # -ph01: 70
  # Ph 02: 68
  # Ph203: 46
  # Lph3: 46
  # Lph12: 30
  # Th9: 27
  # Lph 05: 16
  # Th 1: 15
  # Sph02: 9
  # Ph-01: 8
  # Gph03: 7
  # Ph1004: 6
  # Rg10: 6  Roof Garden
  # 304b: 6; 12a, 507b, 407w, 3d, 618e, 3f, 3k, 12j
  # B406: 4; A9, N503, M6, G03, G20, S710, L10, F4
  # Lp3: 3
  # 4: 3
  # S-515: 2
  # Lph-G: 2
  # Tph10: 2
  # L 18: 2
  # 0: 2
  # Ph B: 2
  # P H 9: 2
  # Lot 19: 2
  # Ph2-04: 2
  # Ph-B: 2
  # PH E: 2

HEADING_LENGTH = 10
TAILING_LENGTH = 25
SQFT_LOWER_LIMIT = 350
YEAR_LOWER_LIMIT = 1970
YEAR_UPPER_LIMIT = 2030
HWY_NUMBERS = [400,401,403,407]

exports.saveSqft = saveSqft = ({
  speedMeter,
  prop,
  CollMiddle,
  CollAggregated,
  sqftFromProp, # optional
},callback)->
  if not prop.uaddr or not prop.addr
    return callback()
  if unit = prop.unt?.toString()
    unit = unit.trim()
    levelEnding = splitLevelEnding unit
    propSqft =
      _id: prop.uaddr+'#'+prop.unt+'@'+prop.onD
      unt: levelEnding.unit
      untEnding: levelEnding.ending
      untLevel: levelEnding.level
  else
    propSqft =
      _id: prop.uaddr+'@'+prop.onD
  propSqft.uaddr = prop.uaddr
  propSqft.ptype2 = prop.ptype2
  propSqft.lp = prop.lp or prop.lpr
  if ('number' is typeof prop.sqft) and (prop.sqft >= SQFT_LOWER_LIMIT)
    propSqft.sqft = prop.sqft
    if speedMeter
      speedCheckCounters = {proccessed:1,original:1}
      speedMeter.check speedCheckCounters
  else
    # get sqft
    sqftFromProp ?= findSqftFromProp(prop) or {}
    found = if sqftFromProp.sqft or sqftFromProp.estSqft then 1 else 0
    if speedMeter
      speedCheckCounters = {proccessed:1,found}
      speedCheckCounters[sqftFromProp.type] = 1 if sqftFromProp.type
      speedMeter.check speedCheckCounters
    Object.assign propSqft,sqftFromProp
    # in case we can't find sqft, and the original has sqft lower than limit
    if (not propSqft.sqft) and ('number' is typeof prop.sqft)
      propSqft.sqft = prop.sqft
    if prop.sqft1
      propSqft.sqft1 = prop.sqft1
      propSqft.sqft2 = prop.sqft2
    debug.verbose propSqft,sqftFromProp if gIsDebug
  # get ages
  if prop.age1? and not isAssignment(prop)
    curYear = parseInt(prop.onD.toString().substr(0,4))
    propSqft.blt1 = curYear - prop.age2
    propSqft.blt2 = curYear - prop.age1
    propSqft.age = prop.age
  # save to db, overwrite using new prop。
  CollMiddle.updateOne {_id:propSqft._id},\
  {$set:propSqft,$addToSet:{srcId:prop._id}},\
  {upsert:true},(err)->
    if err
      debug.error('Error inserting',err,prop,propSqft)
      return callback err
    # save to aggregated collection
    if not (CollAggregated)
      debug.error 'No CollAggregated, skip save to AggregatedCollection'
      return callback null
    try
      await saveToAggregatedCollection {propId:prop._id,propSqft,CollAggregated}
    catch err
      return callback err
    callback null, found



# save sqft to aggregated collection.
# return true if saved, null if not saved
exports.saveToAggregatedCollection = saveToAggregatedCollection = ({
  propId,
  propSqft,
  CollAggregated
  source,
})->
  return null unless propId  and CollAggregated and propSqft?.uaddr
  source ?= 'history'
  unit = propSqft.unt
  if unit
    _id = propSqft.uaddr+'#'+unit
  else
    _id = propSqft.uaddr

  aggregatedRecord =
    uaddr: propSqft.uaddr
    src: source

  if propSqft.ptype2
    aggregatedRecord.ptype2 = propSqft.ptype2

  addToSet = {ids:propId}

  if unit
    levelEnding = splitLevelEnding unit
    aggregatedRecord.unt = unit
    aggregatedRecord.ending = levelEnding.ending
  if propSqft.sqft # from original, or from remark
    aggregatedRecord.sqft = propSqft.sqft
    addToSet.sqfts = propSqft.sqft
  if propSqft.estSqft
    aggregatedRecord.estSqft = propSqft.estSqft
    addToSet.estSqfts = propSqft.estSqft

  # save to unit
  if unit and (propSqft.sqft or propSqft.estSqft)
    await CollAggregated.updateOne {_id},{$set:aggregatedRecord,$addToSet:addToSet},{upsert:true}
  # save to ending
  delete aggregatedRecord.unt
  if levelEnding?.ending and (propSqft.sqft or propSqft.estSqft)
    _id = propSqft.uaddr+'@'+levelEnding.ending
    addToSet.unt = unit if unit
    await CollAggregated.updateOne {_id},{$set:aggregatedRecord,$addToSet:addToSet},{upsert:true}
  # save to building or freehold
  # only building need built year
  # chnage to inc {'blt1.2021':1} -> blt1:{2021:1, 2022:3}
  if unit
    addToSet.unt = unit
  if propSqft.untLevel
    addToSet.lvls = propSqft.untLevel
  update = {$addToSet:addToSet}
  update.$addToSet ?= {}
  if propSqft.projectName
    update.$addToSet.projectName = {
      nm: propSqft.projectName,
      src: source
    }
  if propSqft.blt1 and propSqft.blt2
    aggreatedRecord = await CollAggregated.findOne {_id:propSqft.uaddr}
    bltHash = if propSqft.unt then propSqft.unt + ':' else ':'
    bltHash = bltHash + "#{propSqft.blt1}:#{propSqft.blt2}"
    isDuplicate = aggreatedRecord?.bltHash and (bltHash in aggreatedRecord.bltHash)
    if (not aggreatedRecord) or (not isDuplicate)
      #do inc blt
      update.$inc ?= {}
      update.$inc["blt1.#{propSqft.blt1}"] = 1
      update.$inc["blt2.#{propSqft.blt2}"] = 1
      update.$inc['bltCnt'] = 1
      update.$addToSet.bltHash = bltHash
    await CollAggregated.updateOne {_id:propSqft.uaddr},update,{upsert:true}
  else
    await CollAggregated.updateOne {_id:propSqft.uaddr},update,{upsert:true}

  return true

saveSqftAsync = ({prop,CollAggregated,CollMiddle,sqftFromProp})->
  return new Promise((resolve,reject)->
    saveSqft {prop,CollAggregated,CollMiddle,sqftFromProp},(err,result)->
      return reject err if err
      resolve result
    )

# find sqft for a property in remark or aggregatedCollection
# Priority:
#   self.sqft : save to aggregatedRecord
#   unit.sqft
#   self.estSqft : save to aggregatedRecord
#   unit.estSqft
#   ending.sqft => estSqft
#   ending.estSqft
# anything equal to or after ending.sqft, need to be queued for thrid party requests.
# if sqft was found in remark, shall update middle record and aggregatedRecord.
# return: {
#   sqft: higher accuracy
#   estSqft: lower accuracy
#   src: self/unit/ending
# }
SRC_TYPE =
  SELF: 'self'
  UNIT: 'unit'
  ENDING: 'ending'

# NOTE: condos.ca only have gta props
isCondosCaProp = (prop)->
  # if /ottawa/.test prop.city
  #   return false
  return (prop.src is 'TRB') and \
  (prop.prov is 'ON') and\
  # (/toronto|north|york|scarborough|etobicoke|oakville|hamilton|mississauga/ig.test(prop.city)) and \
  (Array.isArray(prop.ptype2) and ('Apartment' in prop.ptype2))

exports.findSqftAsync = ({prop,CollAggregated,CollMiddle,CollQueue})->
  if prop.ptype isnt 'r'
  # NOTE:非residential物业，不估算sqft
    return null
  # TODO: 如果rni数据的sqft作了修改(下调/上升),导致没有从m中获取到sqft,此时不会更新CollAggregated,CollMiddle的数据,会保留错误数据在其中
  sqftFromProp = findSqftFromProp prop
  # save to aggregate when it has either age or sqft or level
  hasSqft = sqftFromProp?.sqft or sqftFromProp?.estSqft
  hasUnt =  prop?.unt?.toString().trim().length > 2
  hasBltyr = prop.age1?
  shouldDoAggregate = hasBltyr or hasSqft or hasUnt
  if CollMiddle and CollAggregated and shouldDoAggregate
    await saveSqftAsync {prop,CollAggregated,CollMiddle,sqftFromProp}
  # 1. use self parsed
  if sqftFromProp?.sqft
    return {rmSqft: sqftFromProp.sqft, src: SRC_TYPE.SELF, sqftQ:1}
  # 2. use unit previous record
  if prop.unt
    levelEnding = splitLevelEnding prop.unt
    _id = prop.uaddr+'#'+levelEnding.unit
  else
    _id = prop.uaddr
  if CollAggregated
    unitSqft = await CollAggregated.findOne {_id:_id}
    debug.debug 'findSqftAsync unitSqft in CollAggregated unitSqft, _id ',_id,\
      'unitSqft',unitSqft, 'sqftFromProp',sqftFromProp, 'prop',prop
  if unitSqft?.sqfts?.length
    #compute use sqfts, estSqft, prop.sqft1,prop.sqft2,
    {rmSqft, rmSqft1, rmSqft2} = pickOneSqft({unitSqft,prop})
    return {rmSqft, rmSqft1, rmSqft2, src:SRC_TYPE.UNIT, sqftQ:2}
  # 3. use self estimated parsed
  if sqftFromProp?.estSqft
    return {rmSqft:sqftFromProp.estSqft, src:SRC_TYPE.SELF, sqftQ: 3}
  # 4. use unit estimated previous
  if unitSqft?.estSqfts?.length # compute using estSqfts sqft1,prop.sqft2,
    {rmSqft,rmSqft1, rmSqft2} = pickOneSqft({unitSqft,prop})
    return {rmSqft, rmSqft1, rmSqft2, src: SRC_TYPE.UNIT, sqftQ: 4} if rmSqft
  # if not find unit, insert into future processing queue
  if CollQueue
    set =
      uaddr: prop.uaddr,
      addr: prop.addr,
      unt: prop.unt,
      sid:prop.sid,
      city:prop.city,
      prov: prop.prov,
      cmty: prop.cmty,
      ptype2: prop.ptype2,
      mt: new Date()

    debug.debug 'update CollQueue', set,'prop.ptype2',prop.ptype2
    await CollQueue.updateOne {_id: prop._id },\
    {$set:set,$setOnInsert:{ts:new Date()}},{upsert:true}
    if isCondosCaProp(prop)
      try
        if not gCondosToGetSqftTopic
          return debug.info 'kafka: ignored due to missing gCondosToGetSqftTopic, could be no config topic name or ignore this server'
        await Kafka.produceToTopic {
          topic: gCondosToGetSqftTopic,
          key: prop._id,
          value: JSON.stringify({ _id: prop._id, ...set })
        }
      catch error
        #only log this error. process can continue
        debug.debug 'Kafka error whe produceToTopic',error

  # 5. use ending, which is not very accurate
  if prop.unt and prop.sqft1 and prop.ptype2 \
  and ('Apartment' in prop.ptype2) and CollAggregated # find by ending
    if not (typeof(prop.unt) in ['string','number'])
      debug.warn prop._id,'prop.unt',prop.unt,typeof prop.unt
    # there are props, their unt is not a string
    levelEnding ?= splitLevelEnding prop.unt
    if levelEnding.ending
      endingSqft = await CollAggregated.findOne \
        {_id:prop.uaddr+'@'+levelEnding.ending}
      if endingSqft? and (endingSqft.sqft or endingSqft.estSqft) # compute from sqfts,estSqfts
        {rmSqft, rmSqft1, rmSqft2} = pickOneSqft({unitSqft:endingSqft, prop})
        return {rmSqft, rmSqft1, rmSqft2, src:SRC_TYPE.ENDING, sqftQ: 5} if rmSqft
  return null
###
unitSqft:{estSqfts,sqfts},
prop: {sqft1,sqft2}
compare sqfts,estSqfts with sqft1,sqft2, return average or rm
###
pickOneSqft = ({unitSqft, prop})->
  # NOTE:@fred -> 没有sqft1，sqft2 不计算rmSqft
  if not (prop.sqft1? or prop.sqft2?)
    return {}
  debug.debug 'pickOneSqft', unitSqft, prop
  sqft1 = prop.sqft1 or 0
  sqft2 = prop.sqft2 or 300000
  if unitSqft?.sqfts?.length
    sqfts = unitSqft.sqfts.filter (sqft)->
      return (sqft >= sqft1) and (sqft <= sqft2)
    if unitSqft?.estSqfts?.length
      sqftsInEst = sqfts.filter (sqft)->
        return unitSqft.estSqfts.indexOf(sqft)>=0
      #如果没有交集，用sqfts
      if sqftsInEst.length
        sqfts = sqftsInEst
  else if unitSqft?.estSqfts?.length
    sqfts = unitSqft.estSqfts.filter (sqft)-> return (sqft >= sqft1) and (sqft <= sqft2)
  if sqfts.length
    return {rmSqft: sqfts[0]} if sqfts.length is 1
    # NOTE: length > 2时只对length === 2 计算rmSqft
    if sqfts.length is 2
      sqfts.sort (a,b)-> return a-b>0
      min = sqfts[0]
      max = sqfts[1]
      diff = max - min
      if diff > 500
        return {} # no good result.
      else if diff >= 10
        rmSqft1 = min
        rmSqft2 = max
      # sum = sqfts.reduce (prev, curr) -> return prev + curr
      # rmSqft = Math.floor(sum/sqfts.length)
      # NOTE: fred -> 有2个数值时，如果diff小于小值的20%，用小值
      if (SQFT_MIN_DIFF > (diff/min))
        rmSqft = min
  debug.debug 'return rmSqft',rmSqft
  return {rmSqft:rmSqft, rmSqft1, rmSqft2}

# unitSqft = {
#   "sqfts" : [
#         900,
#         800,
#         700,
#         600
#     ],
#   "estSqfts" : [
#       20000,
#       670,
#       700,
#       628,
#       708]
#     }
# prop = {sqft1:500, sqft2:700}
# console.log pickOneSqft({unitSqft, prop})

average = (arr)-> arr.reduce((( p, c )-> p + c),0)/arr.length

###
@input
type tailing = string; # words after matched sqft number
###
exports.checkIsTerrace = checkIsTerrace = (heading,tailing) ->
  ###
  If keyword "Terrace" exists, but also has conjunction like "Plus" or "+" exists between sqft number and "Terrace",
  sqft number can still consider as a valid sqft.
  for example:
  "583 Sf + 240 Sf Terrace"
  "751 Sqft Plus 272 Sqft Terrace"
  in those situation, "583 Sf" and "751 Sqft" are still consider as valid sqft.
  ###
  regexIsTerrace = /Balcony|Terrace/gi
  regexConjunction = /With|Plus|And|\(|\+/gi
  terraceMatchIndex = tailing.search(regexIsTerrace)
  if terraceMatchIndex > -1
    conjunctionMatchIndex = tailing.search(regexConjunction)
    if (conjunctionMatchIndex is -1) or (conjunctionMatchIndex > terraceMatchIndex)
      return true
  return false

MAX_SQFT2_WHEN_SQFT2_IS_NULL = 100000

###
find numbers inside remarks, together with  tailing and heading(words before and after matched position)

@input
type m = prop.m; # remarks
@output
type ret = {
}
###
exports.getPossibleNumbersWithHeadingTailingFromRemarks = getPossibleNumbersWithHeadingTailingFromRemarks = (m='')->
  reg = /(\d+\,)?\d+/g
  tailings = []
  headings = []
  allNumbers = []
  matchedStrings = []
  while matched = reg.exec m
    allNumbers.push parseInt matched[0].replace /\D/g,''
    matchedStrings.push m.substr(Math.max(0,matched.index-HEADING_LENGTH),
      HEADING_LENGTH+TAILING_LENGTH+matched[0].length)
    headings.push m.substring(Math.max(0,matched.index-HEADING_LENGTH),matched.index)
    tailings.push m.substr(matched.index+matched[0].length,TAILING_LENGTH)
  return {tailings,headings,allNumbers,matchedStrings}

###
find exactSqft/estSqft from matched numbers,
###
exports.getSqftsFromMatchedNumbersWithRange = getSqftsFromMatchedNumbersWithRange = (prop,allNumbers=[],headings,tailings)->
  sqfts = []
  estSqfts = []
  matchedType = null
  sqft = null
  estSqft = null
  # give 5% allowance for the real number
  sqft1 = prop.sqft1 * 0.95
  sqft2 = (prop.sqft2 or MAX_SQFT2_WHEN_SQFT2_IS_NULL) * 1.05
  for s,i in allNumbers
    # number within range
    if (sqft1 <= s <= sqft2) and (s >= SQFT_LOWER_LIMIT) and (not checkIsTerrace(headings[i], tailings[i]))
      type = sqftType s,headings[i],tailings[i],prop
      if type is NUMBER_TYPE.POSSIBLE
        sqfts.push s
        # we are quite positive about the result
      else if prop.sqft1 <= s <= prop.sqft2 and type in \
      [NUMBER_TYPE.POSSIBLE_YEAR_NUMBER, NUMBER_TYPE.POSSIBLE_HIGHWAY, NUMBER_TYPE.OTHER]
        matchedType = type
        estSqfts.push(s)
  if sqfts.length is 1
    sqft = sqfts[0]
  else if sqfts.length > 1 # use average todo:change to use min. save sqfts
    ###eg: some sqfts is sqft+Terrace
    "matchedStrings" : [
      642 Sqft + 61 Sqft Terr",
      "42 Sqft + 61 Sqft Terrace, Total",
      "ce, Total 703 Sqft Enjoyment. Fac",
    ],
    "sqfts" : [
      642,
      703
    ]
    ###
    #ret.sqft = average sqfts
    sqft = Math.min(...sqfts)
  else if estSqfts.length is 1
    estSqft = estSqfts[0]
  return {sqfts,estSqfts,type:matchedType,sqft,estSqft}

###
find possible matched sqft from all matched numbers
###
getSqftsFromMatchedNumbers = (prop,allNumbers=[],headings,tailings)->
  types = []
  estSqfts = []
  matchedType = null
  for s,i in allNumbers
    type = sqftType s,headings[i],tailings[i],prop
    types.push type
    if type is NUMBER_TYPE.POSSIBLE
      estSqfts.push s
      matchedType = type
    else if type isnt NUMBER_TYPE.TOO_LOW
      # only save one type for caller's statistic purpose
      matchedType = type
  #if more then one est sqft, do not use them.
  if estSqfts.length is 1
    estSqft = estSqfts[0]
  else if estSqfts.length > 1
    estSqft = average estSqfts
  return {estSqft,type:matchedType,types,estSqfts}

###
# Retrieve possible sqft numbers from the remark

@returns
type ret {
  sqft: number,        # possible sqft
  sqfts: number[],     # all possible sqfts
  estSqft: number,     # estimated sqft
  estSqfts: number[],  # several estimated sqfts
  types: string[],     # type of number when the possibility is not so high
  numbers: number[],   # all matched number of m
  matchedStrings: string[], #all strings
}
###
exports.findSqftFromProp = findSqftFromProp = (prop)->
  if not (m = prop.m)
    return null
  ret = {}
  if 'string' isnt typeof m
    debug.warn prop._id,'m is not type string',typeof(m), m
    m = m.toString()
  {tailings,headings,allNumbers,matchedStrings} = getPossibleNumbersWithHeadingTailingFromRemarks(m)
  ret.matchedStrings = matchedStrings
  ret.numbers = allNumbers
  # prop has sqft from source data, eg. 500-599, 2560, 0-400, 1000-1200
  if prop.sqft1?
    {sqfts, estSqfts, type, sqft, estSqft} = getSqftsFromMatchedNumbersWithRange(prop,allNumbers,headings,tailings)
    ret.sqft = sqft if sqft
    if sqfts.length > 0
      ret.sqfts = sqfts
  # NOTE:@fred -> 没有sqft1，sqft2 不计算rmSqft
  # else
  #   {estSqfts, type, types, estSqft} = getSqftsFromMatchedNumbers(prop,allNumbers,headings,tailings)
  #   ret.types = types
  ret.type = type if type
  ret.estSqft = estSqft if estSqft
  if estSqfts?.length > 0
    ret.estSqfts = estSqfts
  if gIsDebug
    ret.headings = headings
    ret.tailings = tailings
    ret.types = types
    debug.verbose ret
  ret



NUMBER_TYPE = {
  TOO_LOW: 'Too low'
  YEAR_NUMBER: 'Year number'
  POSSIBLE_YEAR_NUMBER: 'Possible year number'
  POSSIBLE_HIGHWAY: 'Possible highway'
  PRICE: 'Price'
  HIGHWAY: 'Highway'
  OUT_OF_RANGE: 'Out of range'
  UNIT_NUMBER: 'Unit number'
  STREET_NUMBER: 'Street number'
  OTHER: 'Other'
  POSSIBLE: 'Possible'
}

sqftType = (s,heading,tailing,prop)->
  return NUMBER_TYPE.TOO_LOW if s <= SQFT_LOWER_LIMIT
  yearNumber =  isYearNumber(s,heading, tailing)
  if yearNumber is NUMBER_TYPE.YEAR_NUMBER
    return NUMBER_TYPE.YEAR_NUMBER
  if yearNumber is NUMBER_TYPE.POSSIBLE_YEAR_NUMBER
    return NUMBER_TYPE.POSSIBLE_YEAR_NUMBER
  return NUMBER_TYPE.PRICE if isPrice s,heading,tailing
  highway = isHighway(s,heading, tailing)

  return NUMBER_TYPE.HIGHWAY if highway is NUMBER_TYPE.HIGHWAY
  return NUMBER_TYPE.POSSIBLE_HIGHWAY if highway is NUMBER_TYPE.POSSIBLE_HIGHWAY

  return NUMBER_TYPE.OUT_OF_RANGE if isOutOfRange s,prop
  return NUMBER_TYPE.UNIT_NUMBER if s.toString() is prop.unt if prop.unt
  if 'string' is typeof prop.addr
    return NUMBER_TYPE.STREET_NUMBER if isStreetNumber s,prop.addr
  else
    debug.warn prop._id,'addr type is not a string', typeof(prop.addr), prop.addr
  if /^[\s\*\']*s[\/fqat]/i.test(tailing) # sqft/sf/
    return NUMBER_TYPE.POSSIBLE
  return NUMBER_TYPE.OTHER

isStreetNumber = (num,addr)->
  stNumber = num.toString()
  return addr.substr(0,stNumber.length) is stNumber

isYearNumber = (num,strHeading, strTailing)->
  return false if num < YEAR_LOWER_LIMIT or num > YEAR_UPPER_LIMIT
  if /(in|year|late|early|middle|mid|end)\s*$/i.test(strHeading)
    return NUMBER_TYPE.YEAR_NUMBER
  if /^\s*[\)\]\,]/.test(strTailing)
    return NUMBER_TYPE.YEAR_NUMBER
  return NUMBER_TYPE.POSSIBLE_YEAR_NUMBER

isHighway = (num,strHeading, strTailing)->
  return false if not (num in HWY_NUMBERS)
  return NUMBER_TYPE.HIGHWAY if /hwy\s*$/i.test(strHeading) or /^\s*hwy/i.test(strTailing)
  return NUMBER_TYPE.POSSIBLE_HIGHWAY
  
isPrice = (num,strHeading,strTailing)->
  return true if /\$\s*$/.test(strHeading) or /^\s*\/?(y|m)/i.test(strTailing)
  false

isOutOfRange = (num,prop)->
  return true if num < SQFT_LOWER_LIMIT
  return true if num > 10000 and prop.lp <= 3000000
  false

# use bltyr first if there exists one
exports.getBuildingInfoAsync = (prop,CollAggregated)->
  buildingInfo = {}
  ret = await CollAggregated.findOne {_id:prop.uaddr},\
    {fields:{rmBltYr:1, numLvls:1 ,unt:1}}
  return null unless ret

  #add rmBltYr
  if ret.rmBltYr and ret.unt?.length >= 15
    buildingInfo.rmBltYr = ret.rmBltYr

  #add untStorey
  numLvls =  ret.numLvls
  if numLvls and prop.unt and (untStorey = computeUntStorey prop.unt, numLvls)
    buildingInfo.untStorey = untStorey
  return buildingInfo
  
#@param {string} unt - unit number
#@param {array} numLvls - [numLvls, numLvls2, ...]
#@return {number} untStorey - unit storey.
#parse unt to get the level, return the index of the level in numLvls
exports.computeUntStorey = computeUntStorey = (unt,numLvls)->
  if not unt or not numLvls
    return null
  if (obj = splitLevelEnding(unt)) and  obj.level
    if parsedLevel = parseInt obj.level
      return  numLvls.indexOf(parsedLevel)+1
    return numLvls.indexOf(obj.level)+1
  return null

# prop = {
#           'm' : 'Christmas has come early with this rare opportunity! Welcome to 2018 Stanley Ave!! \
#           near hwy 407, If you\'re looking to build a brand new home in highly sought-after Fernwood, \
#           here\'s your chance.  This level south facing approx 800sqft lot is conveniently'
#           'uaddr': 'CA:ON:TORONTO:43 EGLINTON AVE E'
#           'unt' : '300'
#           'addr' : '2018 Stanley Ave'
#         }
        
# console.log findSqftFromProp prop

exports.isAssignment = isAssignment = (prop)->
  # Check AssignmentYN property first
  if prop.AssignmentYN?
    return String(prop.AssignmentYN).toUpperCase() is 'Y'
  
  # If no AssignmentYN, check m property for assignment keyword
  if prop.m and typeof prop.m is 'string'
    return /\bassignment\b/i.test(prop.m)
  
  return false