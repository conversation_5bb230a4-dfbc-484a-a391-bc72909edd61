debugHelper = require '../lib/debug'
STREET_ABBR_CA_EN = require('../lib/streetAbbr/CA_EN').STREET_ABBR
{ addAsyncSupport } = require '../lib/helpers_function'
{ abbrExposure } = require '../lib/propAddress'
{ doMapping,formatDaddr,statusLstMapping,convertLotSize,formatVturl } = require './impCommon'
propertiesHelper = require './properties'
propertyTagHelper = require './propertyTagHelper'
{ formatMfee } = require './impFormat'

debug = debugHelper.getDebugger()
getBoundaryTagAsync = addAsyncSupport propertyTagHelper.getBoundaryTag

ACRES2SQFT = 43560
FEET2METER = 0.3048

HEATING_FUELS = [
  'Electric', 'Natural Gas', 'Oil', 'Propane', 'Geo Thermal', 'Solar', 'Water', 'Wood'
]
HEATING_TYPES = [
  'Baseboard', 'Combination', 'Fan Coil', 'Forced Air-1', 'Forced Air-2', 'Gravity',
  'Heat Pump', 'Hot Water', 'In Floor Heat System', 'Wood Stove'
]

PROPERTYSUBTYPE_PTYPE2_MAP = {
  'Apartment':['Apartment'],
  'Carriage':['House','Carriage'],
  'Condominium':['Apartment'],
  'Cottage':['House','Detached'],
  'Detached':['House','Detached'],
  'Detached Single Family':['House','Detached'],
  'Duplex':['House','Duplex'],
  'Manufactured Home':['House','Manufactured'],
  'Parking Stall':['other'],
  'Quadruplex':['House','Quadruplex'],
  'Residential Attached':['House','Attached'],
  'Secondary Suite':['Other','Secondary Suite'],
  'Townhouse':['Townhouse','Attached'],
  'Triplex':['House','Triplex'],
  'Vacant Lot/Land':['Other','Land']
}

OTHERPROPERTYTYPES_PTYPE2_MAP = {
  'Business':['Business'],
  'Business with Property':['Business'],
  'Commercial Land':['Land'],
  'Industrial':['Industrial'],
  'Institutional':['Institutional'],
  'Multi-Family':['Other','Multifamily'],
  'Office':['Office'],
  'Retail':['Retail']
}

IGNORE_PTYPE2_REGEX = /(Remarks|Construction|Walk Up)/i

edmFieldMap =
  Basement: 'bsmt'
  BathroomsFull: 'bthrms'
  BathroomsHalf: 'bthhlf'
  BathroomsTotalDecimal:'tbthrms'
  BedroomsTotal:'tbdrms'
  BuildingName:'bldg_name'
  City:'city'
  CloseDate:'sldd'
  ClosePrice:'sp'
  ConstructionMaterials:'constr'
  Country:'cnty'
  CrossStreet:'crsst'
  CumulativeDaysOnMarket:'dom'
  DirectionFaces:'fce'
  Directions:'dir_desc' # new field
  FeedTypes:'rhbtp'
  FoundationDetails:'constr'
  HeatingYN: 'heat_inc'
  Latitude:'lat'
  ListPrice:'lp'
  ListingId:'sid'
  ListingKey:'edmId'
  ListingKeyNumeric:'edmId_n'
  LivingArea:'sqft'
  Longitude:'lng'
  MLSAreaMajor:'cmty'
  ModificationTimestamp:'mt'
  OffMarketDate:'offD'
  Ownership:'OwnershipType'
  ParcelNumber:'parcel_id'
  ParkingTotal:'tgr'
  PhotosChangeTimestamp:'phomt'
  PhotosCount:'pho'
  PostalCode:'zip'
  PublicRemarks:'m'
  RAE_BedroomsAboveGrade:'br'
  AddressSuite:'unt'
  AddressSearchSuite:'AddressSuiteSearch'
  InputDate:'input_date'
  PricePerSQFT:'PricePerSqft'
  SearchPrice:'Price'
  PropertyClass:'tpdwel'
  BUILDINGTYPE:'BuildingType'
  ROADACCESS:'RoadType'
  RAE_MunicipalityCode:'citycd'
  StateOrProvince:'prov'
  StateRegion:'region'
  StoriesTotal:'NumberofStories'
  StreetDirPrefix:'st_dir'
  StreetName:'st'
  StreetNumber:'st_num'
  StreetNumberNumeric:'st_num_n'
  StreetSuffix:'st_sfx'
  UnitNumber:'unt'
  UnparsedAddress:'addr'
  WaterSource:'water'
  YearBuilt:'bltYr'


# Determine garage number based on ParkingFeatures["Double Garage Detached"] and garageYN (grHas)
getGarageNum = (record) ->
  record.gr = 0
  if record.GarageYN and record.ParkingFeatures
    for feature in record.ParkingFeatures
      lowerFeature = feature.toLowerCase()
      if lowerFeature.includes('tandem')
        record.gr = 2
        break
      else if lowerFeature.includes('double')
        record.gr = 2
        break
      else if lowerFeature.includes('single')
        record.gr = 1
        break
      else if lowerFeature.includes('triple')
        record.gr = 3
        break
      else if lowerFeature.includes('quad')
        record.gr = 4
        break
      else
        record.gr = 1



# General function to process a field from list to string
exports.processFieldFromListToString = processFieldFromListToString = (oldValueList) ->
  if not Array.isArray(oldValueList)
    return null

  if oldValueList.length > 1
    result = oldValueList.join(',')
  else
    result = oldValueList[0]
  return result


# if LivingAreaUnits is 'Square Feet', delete it
formatLivingAreaUnits=(record)->
  if record.LivingAreaUnits is 'Square Feet'
    delete record.LivingAreaUnits


# generate phoUrls from Media
generatePhoUrls = (record) ->
  if not record.Media
    delete record.Media
    return
  phoUrls = []
  for item,i in record.Media
    phoUrls.push({
      id: String(i + 1),
      url: item.MediaURL
    })
  record.phoUrls = phoUrls
  delete record.Media


# combine AssessmentType,RAE_LR_remarks22,AttachedGoodsExcluded into remarks
combineRemarks = (record) ->
  remarkFields = ['AssessmentType', 'RAE_LR_remarks22', 'AttachedGoodsExcluded']
  remarks = []

  for field in remarkFields
    if record[field]?
      remarks.push "(#{record[field]})"
      delete record[field]  # Remove the field after processing

  if remarks.length > 0
    record.m = "#{record.m} #{remarks.join(' ')}"



# vow yes -> 'v', no -> 'x'
formatVow = (record) ->
  if record["DisplayAddressY/N"] is 'Yes'
    record.vow = 'v'
  if record["DisplayAddressY/N"] is 'No'
    record.vow = 'x'
  delete record["DisplayAddressY/N"]


# idx true -> 'x'
formatIdx = (record) ->
  if record.IDXParticipationYN is true
    record.idx = 'x'
  delete record.IDXParticipationYN


formatRms = (record)->
  if not record.RoomType
    return
  rms = []
  parseDimensions = (dimString) ->
    return null if not dimString or typeof dimString isnt 'string'
    dimensions = dimString.split('x')
    if dimensions.length is 2
      height = parseFloat(dimensions[0]) * FEET2METER
      width = parseFloat(dimensions[1]) * FEET2METER
      return { w: width, h: height }
    if dimensions.length is 1
      width = parseFloat(dimensions[0]) * FEET2METER
      height = parseFloat(dimensions[0]) *  FEET2METER
      return { w: width, h: height }
    return null

  levels = {
    'DiningRoom':'RoomDiningRoomLevel'
    'FamilyRoom':'RoomFamilyRoomLevel'
    'Kitchen':'RoomKitchenLevel'
    'LivingRoom':'RoomLivingRoomLevel'
  }
  for roomType in record.RoomType
    dimensionKey = "Room#{roomType}Dimensions"
    if dimensionValue = record[dimensionKey]
      parsedDimensions = parseDimensions(dimensionValue)
    if levelKey = levels[roomType]
      rm ={}
      if record[levelKey]
        rm.t = roomType
        rm.l = record[levelKey]
      if parsedDimensions
        rm.w = parsedDimensions.w
        rm.h = parsedDimensions.h
      # Push only non-empty objects
      if Object.keys(rm).length > 0
        rms.push(rm)
  record.rms = rms


convertToEdmontonTime = (isoString) ->
  # Parse the ISO string to a Date object in UTC
  utcDate = new Date(isoString)

  # Set options for Edmonton time zone (America/Edmonton)
  options =
    timeZone: "America/Edmonton"
    year: "numeric"
    month: "2-digit"
    day: "2-digit"
    hour: "2-digit"
    minute: "2-digit"
    hour12: false  # 24-hour format

  # Format the date for Edmonton time zone
  edmontonTime = new Intl.DateTimeFormat('en-CA', options).format(utcDate)

  # Reformat to match desired output format: YYYY-MM-DD HH:mm
  FormatedEdmontonTime = edmontonTime.replace(',', '')
  return FormatedEdmontonTime


formatOpenHouse = (record) ->
  if not record.openHouse
    return
  openHouseDetails = []
  for oh in record.openHouse
    detail =
      f: convertToEdmontonTime(oh.OpenHouseStartTime)
      t: convertToEdmontonTime(oh.OpenHouseEndTime)
    if processFieldFromListToString(oh.FeedTypes)
      detail.fp = processFieldFromListToString(oh.FeedTypes)
    if oh.OpenHouseRemarks?
      detail.m = oh.OpenHouseRemarks
    if oh.LivestreamOpenHouseURL?
      detail.vturl = oh.LivestreamOpenHouseURL
    if not oh.LivestreamOpenHouseURL and not oh.VirtualOpenHouseURL
      detail.tp = 'P'
    openHouseDetails.push(detail)
  record.ohz=openHouseDetails
  delete record.oh


# 处理Edmonton特有的CondoFee与CondoFeePaymentSched字段生成mfee
exports.processEdmCondoFee = processEdmCondoFee = (record) ->
  # 只有在mfee不存在且CondoFee存在时才处理
  if (not record.mfee?) and record.CondoFee?
    # 根据付款周期计算月度维护费
    mfee = switch record.CondoFeePaymentSched
      when 'Annually'
        propertiesHelper.getFloat(record.CondoFee / 12)
      when 'Semi-Annually'
        propertiesHelper.getFloat(record.CondoFee / 6)
      when 'Quarterly', 'Seasonal'
        propertiesHelper.getFloat(record.CondoFee / 3)
      when 'Bi-Monthly'
        propertiesHelper.getFloat(record.CondoFee / 2)
      when 'Semi-Monthly'
        propertiesHelper.getFloat(record.CondoFee * 2)
      when 'Monthly'
        propertiesHelper.getFloat(record.CondoFee)
      when 'Weekly'
        propertiesHelper.getFloat((record.CondoFee / 7) * 30)
      when 'Bi-Weekly'
        propertiesHelper.getFloat((record.CondoFee / 14) * 30)
      when 'Daily'
        propertiesHelper.getFloat(record.CondoFee * 30)
      when 'Other', 'One Time'
        debug.info "#{record._id} cannot convert mfee for CondoFeePaymentSched: #{record.CondoFeePaymentSched}"
        null
      else
        debug.warn "#{record._id} unknown mfeeunt from CondoFeePaymentSched: #{record.CondoFeePaymentSched}"
        null
    
    # 设置相关字段，只有成功计算了mfee才设置mfeeunt为Monthly
    if mfee?
      record.mfee = mfee
      record.mfeeunt = 'Monthly'  # 统一设置为Monthly，因为已经转换为月度费用
    else
      record.mfee = record.CondoFee  # 如果无法计算，则使用原始值
      record.mfeeunt = record.CondoFeePaymentSched
  return record


cleanRecord = (record) ->
  deleteFields = [
    '@odata',
    'BridgeModificationTimestamp',
    'StreetDirPrefix',
    '_mt',
    'ts',
    'GarageYN',
    'Levels'
  ]
  deletePatterns = [
    /^RAE_LA.*/,
    /^Room.*/,
    /^RAE_LFD_.*/,
    /^RAE_LMD_MP_Quality$/,
    /^RAE_LM_.*/,
    /^RAE_LO.*/,
    /^RAE_L_Keyword$/,
    /^ListOffice.*/,
    /^ListAgent.*/,
    /^CoListOffice.*/,
    /^CoListAgent.*/,
  ]
  # Iterate over the fields in the record
  for fieldName of record
    # Check if field is in the deleteFields list
    if fieldName in deleteFields
      delete record[fieldName]

    for pattern in deletePatterns
      if pattern.test(fieldName)
        delete record[fieldName]
        break

  return record


formatBuilding = (record) ->
  record.Building ?= {}
  building={}
  # building={
  #   Appliances:record.Appliances
  #   ArchitecturalStyle:record.ArchitecturalStyle? processFieldFromListToString(record.ArchitecturalStyle)
  # }
  if record.Appliances
    building.Appliances=record.Appliances
  if record.ArchitecturalStyle
    building.ArchitecturalStyle=processFieldFromListToString(record.ArchitecturalStyle)
  if record["AMENITIES/FEATURES"]\
  and record["AMENITIES/FEATURES"].length > 0
    building.Amenities=record["AMENITIES/FEATURES"]
  if record.FireplaceYN
    building.FireplacePresent=record.FireplaceYN
  # get fuel and heat
  [fuel, heat]=formatHeatingDetails record
  if fuel
    building.HeatingFuel=fuel
  if heat
    building.HeatingType=heat
  record.Building=building
  delete record.Appliances
  delete record.FireplaceYN
  delete record["AMENITIES/FEATURES"]
  delete record.ArchitecturalStyle


formatHeatingDetails = (record) ->
  if not record.Heating
    delete record.Heating
    return [null,null]
  result = {HeatingFuel: [], HeatingType: []}
  for heating in record.Heating
    if heating in HEATING_FUELS
      result.HeatingFuel.push(heating)
    else if heating in HEATING_TYPES
      result.HeatingType.push(heating)
  fuel=processFieldFromListToString(result.HeatingFuel)
  heat=processFieldFromListToString(result.HeatingType)
  delete record.Heating
  return [fuel,heat]


formatFurnished = (record) ->
  return if not record.Furnished
  if record.Furnished is 'Yes'
    record.frnshd = 1
  else
    record.frnshd = 0
  delete record.Furnished

formatListingAgent = (record)->
  record.la = []
  if record.ListOfficeMlsId
    if record.ListOfficePhoneExt
      officeTel = record.ListOfficePhone+'-'+record.ListOfficePhoneExt
    else
      officeTel = record.ListOfficePhone
    if record.ListAgentPreferredPhoneExt
      agntTel = record.ListAgentPreferredPhone+'-'+record.ListAgentPreferredPhoneExt
    else
      agntTel = record.ListAgentPreferredPhone
    listingAgent = {
      id: record.ListOfficeMlsId,
      nm: record.ListOfficeName,
      tel: officeTel,
      aor:record.ListOfficeAOR,
      eml:record.ListOfficeEmail,
      edmId:record.ListOfficeKey,
      edmId_n:record.ListOfficeKeyNumeric,
      agnt: [{
        id: record.ListAgentMlsId,
        nm: record.ListAgentFullName,
        tel: agntTel,
        eml: record.ListAgentEmail,
        emdId:record.ListAgentKey,
        emdId_n:record.ListAgentKeyNumeric
      }],
    }
    record.la.push(listingAgent)
  record.rltr = record.ListOfficeName

  if record.CoListOfficeMlsId
    if record.CoListOfficePhoneExt
      officeTel = record.CoListOfficePhone+'-'+record.CoListOfficePhoneExt
    else
      officeTel = record.CoListOfficePhone
    if record.CoListAgentPreferredPhoneExt
      agntTel = record.CoListAgentPreferredPhone+'-'+record.CoListAgentPreferredPhoneExt
    else
      agntTel = record.CoListAgentPreferredPhone
    CoListingAgent2 = {
      id: record.CoListOfficeMlsId,
      nm: record.CoListOfficeName,
      tel: officeTel,
      aor:record.CoListOfficeAOR,
      eml:record.CoListOfficeEmail,
      edmId:record.CoListOfficeKey,
      edmId_n:record.CoListOfficeKeyNumeric,
      agnt: [{
        id: record.CoListAgentMlsId,
        nm: record.CoListAgentFullName,
        tel: agntTel,
        eml: record.CoListAgentEmail,
        emdId:record.CoListAgentKey,
        edmId_n:record.CoListAgentKeyNumeric
      }],
    }
    record.la.push(CoListingAgent2)


# status,lst
exports.formatStatusAndLst = formatStatusAndLst = (record)->
  if not record.MlsStatus
    delete record.MlsStatus
    debug.error 'Edm formatStatusAndLst error, MlsStatus is null', record._id
    return
  if mappingInfo = statusLstMapping[record.MlsStatus]?['EDM']
    record.status = mappingInfo.status
    record.lst = mappingInfo.lst
  else
    debug.error "Edm formatStatusAndLst error, MlsStatus:#{record.MlsStatus}"
  


# get saletp from TransactionType and PropertyType
formatSaletp = (record)->
  #TransactionType can be null, 'For Lease', 'For Sale', 'For Sale or Lease'
  isLease = record.TransactionType is 'For Lease'

  if record.PropertyType
    if /Lease/i.test record.PropertyType
      isLease = true
    else if /Sale/i.test record.PropertyType
      isLease = false
  else
    if record.lp? and (parseInt(record.lp) < propertiesHelper.minSalePrice)
      isLease = true
 
  if isLease
    record.lpr = record.lp
    record.saletp = ['Lease']
    delete record.lp
  else
    record.saletp = ['Sale']

###
# @description 当没有PropertySubType时，根据OTHERPROPERTYTYPES或BUILDINGTYPE获取ptype2
# @param {Object} record - 房源记录
# @return {Array} result - ptype2数组
###
exports.formatPtype2WhenNoSubType = formatPtype2WhenNoSubType = (record)->
  result = []
  
  # 处理OTHERPROPERTYTYPES
  if record.OTHERPROPERTYTYPES
    if Array.isArray(record.OTHERPROPERTYTYPES)
      for item in record.OTHERPROPERTYTYPES
        if OTHERPROPERTYTYPES_PTYPE2_MAP[item]
          result = result.concat OTHERPROPERTYTYPES_PTYPE2_MAP[item]
    else if OTHERPROPERTYTYPES_PTYPE2_MAP[record.OTHERPROPERTYTYPES]
      result = result.concat OTHERPROPERTYTYPES_PTYPE2_MAP[record.OTHERPROPERTYTYPES]
    return result if result.length > 0
    
  # NOTE: 处理BUILDINGTYPE,已经mapping为BuildingType
  if record.BuildingType
    if Array.isArray(record.BuildingType)
      for item in record.BuildingType
        if item and not IGNORE_PTYPE2_REGEX.test(item)
          result.push item
    else if ('string' is typeof record.BuildingType) and (not IGNORE_PTYPE2_REGEX.test(record.BuildingType))
      result.push record.BuildingType
    else
      debug.error 'Not supported BuildingType:', record.BuildingType
      
  return result


# ptype, ptype2
formatPtype = (record) ->
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
  else if /Commercial|Business/i.test(record.PropertyType)
    record.ptype = 'b'
  else
    record.ptype = 'o'
    if record.PropertyType
      record.ptype2 = [record.PropertyType]
  record.ptype2 ?= []
  if record.PropertySubType
    if tpArr = PROPERTYSUBTYPE_PTYPE2_MAP[record.PropertySubType]
      record.ptype2 = record.ptype2.concat tpArr
    else
      record.ptype2.push record.PropertySubType
  else
    record.ptype2 = record.ptype2.concat(formatPtype2WhenNoSubType record)
  record.ptype2 = Array.from(new Set(record.ptype2))

  delete record.PropertyType
  delete record.PropertySubType


formatLandSize = (record) ->
  if record.LotSizeArea
    record.lotsz = propertiesHelper.getFloat record.LotSizeArea
    record.lotsz = convertLotSize(record.lotsz)
    record.lotszUt = 'ft²'
  else if record.LotSizeAcres
    # acres to sqft
    record.lotsz = propertiesHelper.getFloat(record.LotSizeAcres * ACRES2SQFT)
    record.lotszUt = 'ft²'

###
# @description 格式化朝向信息
# @param {Object} record - 记录对象
###
formatFce = (record) ->
  if record.fce
    result = abbrExposure(record.fce)
    record.fce = result if result?
  else if record.UNITEXPOSURE?.length > 0
    result = abbrExposure(record.UNITEXPOSURE)
    record.fce = result if result?
    delete record.UNITEXPOSURE
  return
    

exports.convertFromEdm = convertFromEdm = ({
  srcRecord,
  Boundary
}, cb) ->
  try
    ret = doMapping srcRecord,edmFieldMap
    getGarageNum ret
    formatLivingAreaUnits ret
    # generatePhoUrls ret
    combineRemarks ret
    formatVow ret
    formatIdx ret
    formatVturl ret, ['URL-Brochure', 'URL-Map', 'URL-AddlImages', 'URL-AlternateFeatureSheet', 'VirtualTourURLBranded']
    formatRms ret
    formatOpenHouse ret
    formatMfee ret
    processEdmCondoFee ret  # 添加对CondoFee的处理
    formatBuilding ret
    formatFurnished ret
    formatListingAgent ret
    formatStatusAndLst ret
    formatSaletp ret
    formatPtype ret
    formatLandSize ret
    formatDaddr ret
    formatFce ret
    ret.lvl = processFieldFromListToString(ret.Levels)
    ret.water = processFieldFromListToString(ret.WaterSource)
    ret.cnty  ?= 'CA'
    ret.prov  ?= 'AB'
    ret.orgId = ret._id
    ret._id = 'EDM' + ret.sid
    ret.id = 'EDM' + ret.sid
    ret.src = 'EDM' # 如果更换了这个值，记得修改statusLstMapping
    cleanRecord ret
    debug.debug 'convert ret', ret
  catch err
    debug.error 'convert err', err
    return cb err

  debug.debug 'convert ret', ret
  cb null, ret