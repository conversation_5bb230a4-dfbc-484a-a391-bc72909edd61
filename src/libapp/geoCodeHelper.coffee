debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
cityHelper = require '../lib/cityHelper'
{ isSubstring, stringContainsAny } = require '../lib/helpers_string'
{ filterZip } = require '../lib/propAddress'

#https://developers.google.com/maps/documentation/geocoding/overview
MAP_GOOGLE_LOCATION_TYPE =
  'room':1
  'premise':1
  'subpremise':1
  'intersection':1
  'landmark':1
  'place_of_worship':1
  'street_number':1
  'point_of_interest':1
  'establishment':1
  'plus_code':1
  'street_address':1

# 对国家,省份,城市,邮编进行打分
module.exports.adjustGeocodingQuality = adjustGeocodingQuality = (geoCodingResult, qaddr, zip)->
  return unless qaddr
  qaddrLower = qaddr.toLowerCase()
  qaddrArr = qaddrLower.split(',')
  badFlds = []
  geoCnty = geoCodingResult.cnty?.toLowerCase()
  geoCity = geoCodingResult.city?.toLowerCase()
  geoSubCity = geoCodingResult.subCity?.toLowerCase()
  geoCmty = geoCodingResult.cmty?.toLowerCase()
  geoProv = geoCodingResult.prov?.toLowerCase()

  cntyFull = cityHelper.getCntyFullName geoCodingResult.cnty

  cntyInQaddr = qaddrArr.pop()?.trim()
  cntyFullInQaddr = cityHelper.getCntyFullName cntyInQaddr
  cntyArr = [cntyInQaddr]
  cntyArr.push cntyFullInQaddr.toLowerCase() if cntyFullInQaddr

  sameCnty = (cntyFull?.toLowerCase() in cntyArr) \
    or (geoCnty in cntyArr)
  if not sameCnty
    geoCodingResult.q -= 10
    badFlds.push 'cnty'
  
  provFull = cityHelper.getProvFullName(geoProv, geoCnty)

  provInQaddr = qaddrArr.pop()?.trim().split(' ')[0]
  provFullInQaddr = cityHelper.getProvFullName(provInQaddr, cntyInQaddr)
  provArr = [provInQaddr]
  provArr.push provFullInQaddr.toLowerCase() if provFullInQaddr

  sameProv = (provFull?.toLowerCase() in provArr) \
    or (geoProv in provArr)

  if not sameProv
    geoCodingResult.q -= 10
    badFlds.push 'prov'
  
  cityInQaddr = qaddrArr.pop()?.trim()
  cityArr  = []
  cityArr.push geoCity if geoCity
  cityArr.push geoSubCity if geoSubCity
  cityArr.push geoCmty if geoCmty
  if (cityInQaddr not in cityArr) and (not stringContainsAny(cityInQaddr,geoCodingResult.locality))
    if (isSubstring cityInQaddr,geoCity) or (isSubstring cityInQaddr,geoSubCity)\
    or (isSubstring cityInQaddr,geoCmty)
      # 可能填写时缺少单词, eg:Cochrane Lake, Cochrane
      debug.warn "src.city is substring of geoCity,srcCity:#{cityInQaddr},geoCity:#{geoCity},geoSubCity:#{geoSubCity},geoCmty:#{geoCmty}"
    else
      geoCodingResult.q -= 10
      badFlds.push 'city'
  
  if zip and geoCodingResult.zip and (filterZip(zip) isnt filterZip(geoCodingResult.zip))
    geoCodingResult.q -= 10
    badFlds.push 'zip'

  # st_num不在qaddr中存在
  if geoCodingResult.st_num and (not qaddr.includes(geoCodingResult.st_num))
    geoCodingResult.q -= 20
    badFlds.push 'st_num'

  if badFlds.length
    geoCodingResult.badFlds = badFlds
  
  if not (geoCodingResult.addr or geoCodingResult.st_num)
    geoCodingResult.q = geoCodingResult.q - 10

# 解析google获取的geocodeResult
module.exports.parseGoogleResult = parseGoogleResult = (geoResult,ret)->
  if (g = geoResult.geometry)?.location
    ret.lat = g.location.lat
    ret.lng = g.location.lng
    if (ne = g.viewport?.northeast) and (sw = g.viewport.southwest)
      ret.nelat = ne.lat
      ret.nelng = ne.lng
      ret.swlat = sw.lat
      ret.swlng = sw.lng

    ret.q = 60
    #[ROOFTOP,RANGE_INTERPOLATED,GEOMETRIC_CENTER,APPROXIMATE]
    if g.location_type is 'ROOFTOP'
      ret.q = 100
    else if geoResult.types?
      for tp in geoResult.types
        if MAP_GOOGLE_LOCATION_TYPE[tp]
          ret.q = 100
          break
    if ret.q < 100 # only has example of sublocality_level_1
      if geoResult.types?.indexOf('sublocality_level_5')>=0
        ret.q = 95
      else if geoResult.types?.indexOf('sublocality_level_4')>=0
        ret.q = 90
      else if geoResult.types?.indexOf('sublocality_level_3')>=0
        ret.q = 85
      else if geoResult.types?.indexOf('sublocality_level_2')>=0
        ret.q = 80
      else if geoResult.types?.indexOf('sublocality_level_1')>=0
        ret.q = 75

  if geoResult.formatted_address
    ret.faddr = geoResult.formatted_address
    ret.geoAddr = geoResult.formatted_address.split(',')[0]

  localityFields = ['administrative_area_level_3','administrative_area_level_4','administrative_area_level_5',\
  'administrative_area_level_6','administrative_area_level_7','locality','sublocality',]

  if geoResult.address_components
    ret.locality = []
    for cmp in geoResult.address_components
      # 保存所有city/cmty相关字段
      localityFields.forEach (value)->
        if cmp.types.includes(value)
          ret.locality.push cmp.long_name or cmp.short_name

      if cmp.types?.indexOf('postal_code') >= 0
        ret.zip = cmp.long_name or cmp.short_name
      if (not ret.st_num?) and (cmp.types?.indexOf('street_number') >= 0)
        ret.st_num = cmp.long_name or cmp.short_name
      if (not ret.st?) and \
      ((cmp.types?.indexOf('route') >= 0) or \
      (cmp.types?.indexOf('point_of_interest') >= 0))
        ret.st = cmp.long_name or cmp.short_name
      if (cmp.types.indexOf('administrative_area_level_1') >= 0)
        ret.prov = cmp.long_name or cmp.short_name
      if (cmp.types.indexOf('administrative_area_level_2') >= 0)
        ret.region = cmp.long_name or cmp.short_name
      if (cmp.types.indexOf('locality') >= 0)
        ret.city = cmp.long_name or cmp.short_name
      # NOTE:locality可能不是city,而是县/乡镇,优先使用administrative_area_level_3
      # https://developers.google.cn/maps/documentation/javascript/geocoding?hl=en#GeocodingAddressTypes
      if (cmp.types.indexOf('administrative_area_level_3') >= 0)
        if ret.city
          ret.subCity = ret.city
        ret.city = cmp.long_name or cmp.short_name
      if (cmp.types.indexOf('neighborhood') >= 0)
        ret.cmty = cmp.long_name or cmp.short_name
      if (cmp.types.indexOf('country') >= 0)
        ret.cnty = cmp.long_name or cmp.short_name
      if (ret.q < 100) and (cmp.types?)
        if cmp.types.indexOf('route') >= 0
          ret.q = 85 if ret.q < 85
        else if (cmp.types.indexOf('locality') >= 0)
          ret.q = 75 if ret.q < 75
        else if (cmp.types.indexOf('administrative_area_level_2') >= 0)
          ret.q = 70 if ret.q < 70
        else if (cmp.types.indexOf('administrative_area_level_1') >= 0)
          ret.q = 65 if ret.q < 65

# 解析bing获取的geocodeResult
module.exports.parseBingResult = parseBingResult = (geoResult,ret)->
  if (p = geoResult.point)?.type is 'Point' and p.coordinates
    ret.lat = p.coordinates[0]
    ret.lng = p.coordinates[1]
    ret.q = if geoResult.confidence is 'High' then 100 else \
      if geoResult.confidence is 'Medium' then 45 else 25
  if bbox = geoResult.bbox
    ret.nelat = bbox[0]
    ret.nelng = bbox[1]
    ret.swlat = bbox[2]
    ret.swlng = bbox[3]
  if geoResult.name
    ret.faddr = geoResult.name
    ret.geoAddr = geoResult.name.split(',')[0]
  if (a = geoResult.address)?
    ret.locality = []
    if a.postalCode
      ret.zip = a.postalCode
    if a.neighborhood
      ret.cmty = a.neighborhood
      ret.locality.push a.neighborhood
    if a.locality
      ret.city = a.locality
      ret.locality.push a.locality
    if a.addressLine
      ret.addr = a.addressLine
    if a.adminDistrict2
      ret.region = a.adminDistrict2
    if a.countryRegion
      ret.cnty = a.countryRegion
    if a.adminDistrict
      ret.prov = a.adminDistrict

# 解析mapquest获取的geocodeResult
module.exports.parseMapquestResult = parseMapquestResult = (geoResult,ret)->
  if (p = geoResult.locations?[0]) and (pp = p.displayLatLng or p.latLng)
    ret.lat = pp.lat
    ret.lng = pp.lng
    ret.zip = p.postalCode if p.postalCode
    qc = p.geocodeQualityCode
    ret.addr = p.street if p.street
    ret.area = p.adminArea4 if p.adminArea4
    ret.cnty = p.adminArea1 if p.adminArea1
    ret.prov = p.adminArea3 if p.adminArea3
    ret.city = p.adminArea5 if p.adminArea5
    ret.cmty = p.adminArea6 if p.adminArea6
    if qc is 'P1AAA' or qc is 'L1AAA'
      ret.q = 100
    else if /AAA$/.test qc
      ret.q = 50
    else
      ret.q = 10

# 解析here获取的geocodeResult
module.exports.parseHereResult = parseHereResult = (geoResult,ret)->
  ret.q = geoResult.scoring?.queryScore * 100
  if (g = geoResult.position)
    ret.lat = g.lat
    ret.lng = g.lng
  if address = geoResult.address
    if address.label
      ret.faddr = address.label
      ret.geoAddr = address.label.split(',')[0]
    ret.locality = []
    ret.zip = address.postalCode if address.postalCode
    ret.cnty = address.countryName if address.countryName
    ret.region = address.county if address.county
    ret.prov = address.state if address.state
    if address.city
      ret.city = address.city
      ret.locality.push address.city
    if address.district
      ret.subCity = address.district
      ret.locality.push address.district
    if address.subdistrict
      ret.cmty = address.subdistrict
      ret.locality.push address.subdistrict
    ret.st_num = address.houseNumber if address.houseNumber
    ret.st = address.street if address.street

# 解析mapbox获取的geocodeResult
module.exports.parseMapboxResult = parseMapboxResult = (geoResult,ret)->
  ret.q = geoResult.relevance * 100
  if (g = geoResult.center)
    ret.lat = g[1]
    ret.lng = g[0]
  if geoResult.place_name
    ret.faddr = geoResult.place_name
    ret.geoAddr = geoResult.place_name.split(',')[0]
  ret.st_num = geoResult.address if geoResult.address
  ret.st = geoResult.text if geoResult.text
  if geoResult.context
    ret.locality = []
    for cmp in geoResult.context
      if cmp.id?.indexOf('postcode') >= 0
        ret.zip = cmp.text
      if (cmp.id?.indexOf('region') >= 0)
        ret.prov = cmp.text
      if (cmp.id?.indexOf('place') >= 0)
        ret.city = cmp.text
        ret.locality.push cmp.text
      if (cmp.id?.indexOf('country') >= 0)
        ret.cnty = cmp.text
      if (cmp.id?.indexOf('neighborhood') >= 0)
        ret.cmty = cmp.text
        ret.locality.push cmp.text
      if (cmp.id?.indexOf('locality') >= 0)
        ret.subCity = cmp.text
        ret.locality.push cmp.text
      if (cmp.id?.indexOf('district') >= 0)
        ret.locality.push cmp.text

# 解析azure获取的geocodeResult
module.exports.parseAzureResult = parseAzureResult = (geoResult,ret)->
  # 解析坐标信息
  if (position = geoResult.position)
    # 通过经纬度获取的position为字符串
    if 'string' is typeof position
      [lat,lng] = position.split(',')
      ret.lat = parseFloat lat
      ret.lng = parseFloat lng
      ret.q = 60 # 反编译没有score,设置默认值
    else
      ret.lat = position.lat
      ret.lng = position.lon
  
  # 解析质量评分
  ret.q = geoResult.score * 100 if geoResult.score
  
  # 解析地址组件
  if (address = geoResult.address)
    # 完整格式化地址
    if address.freeformAddress
      ret.faddr = address.freeformAddress
      ret.geoAddr = address.freeformAddress.split(',')[0]
    
    # 门牌号和街道
    ret.st_num = address.streetNumber if address.streetNumber
    ret.st = address.streetName if address.streetName
    
    # 邮编（优先使用完整邮编）
    ret.zip = address.extendedPostalCode or address.postalCode

    # 社区
    ret.cmty = address.neighbourhood if address.neighbourhood
    
    # 城市（优先使用municipality）
    ret.city = address.municipality or address.localName

    # subCity
    ret.subCity = address.municipalitySubdivision if address.municipalitySubdivision
    
    # 省份（按优先级选择）
    ret.prov = address.countrySubdivision or
               address.countrySubdivisionName or
               address.countrySubdivisionCode
    
    # 国家（优先使用国家代码）
    ret.cnty = address.countryCode or address.country

    # 通过经纬度获取时存在该字段
    if address.boundingBox
      [nelat,nelng] = address.boundingBox?.northEast?.split(',')
      [swlat,swlng] = address.boundingBox?.southWest?.split(',')
      ret.nelat = parseFloat nelat if nelat
      ret.nelng = parseFloat nelng if nelng
      ret.swlat = parseFloat swlat if swlat
      ret.swlng = parseFloat swlng if swlng
  
  # 解析视图边界
  if (viewport = geoResult.viewport)
    if (topLeft = viewport.topLeftPoint) and (btmRight = viewport.btmRightPoint)
      # topLeftPoint是西北角(纬度最大,经度最小), btmRightPoint是东南角(纬度最小,经度最大)
      # 需要转换为东北角(nelat/nelng)和西南角(swlat/swlng)
      ret.nelat ?= topLeft.lat      # 东北角纬度 = 西北角纬度(纬度最大值)
      ret.nelng ?= btmRight.lon     # 东北角经度 = 东南角经度(经度最大值)
      ret.swlat ?= btmRight.lat     # 西南角纬度 = 东南角纬度(纬度最小值)
      ret.swlng ?= topLeft.lon      # 西南角经度 = 西北角经度(经度最小值)
  
  # 构建locality数组
  ret.locality = []
  ret.locality.push(ret.city) if ret.city
  ret.locality.push(ret.cmty) if ret.cmty
  ret.locality.push(address.localName) if address?.localName
  ret.locality = Array.from(new Set(ret.locality)) # 去重,city与localName可能重复
