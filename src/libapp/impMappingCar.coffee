debugHelper = require '../lib/debug'
STREET_ABBR_CA_EN = require('../lib/streetAbbr/CA_EN').STREET_ABBR
{ addAsyncSupport } = require '../lib/helpers_function'
{ abbrExposure } = require '../lib/propAddress'
{ formatMfee } = require '../libapp/impFormat'
{ doMapping, formatDaddr,statusLstMapping,convertLotSize,formatVturl } = require './impCommon'
propertiesHelper = require './properties'
propertyTagHelper = require './propertyTagHelper'
debug = debugHelper.getDebugger()
getBoundaryTagAsync = addAsyncSupport propertyTagHelper.getBoundaryTag
ACRES2SQFT = 43560
HECTARES2SQFT = 107639.10417
METER2SQFT = 10.76391
FEET2METER = 0.3048

HEATING_FUELS = [
  'Electric', 'Natural Gas', 'Oil', 'Propane', 'Geo Thermal', 'Solar', 'Water', 'Wood'
]


HEATING_TYPES = [
  'Airtight Stove', 'Baseboard', 'Combo Furnace', 'Electric Forced Air', 'Fireplace(s)',
  'Fireplace-Gas', 'Fireplace-Propane', 'Fireplace-Wood', 'Forced Air', 'Forced Air-Propane',
  'Forced Air-Wood', 'Gas Forced Air Closed', 'Gas Forced Air Open', 'Gas Hot Water', 
  'Gas Well', 'Ground Source', 'Heat Pump', 'Hot Water', 'Hot Water-Other', 
  'Hot Water-Propane', 'None', 'Oil Forced Air', 'Oil Hot Water', 'Other', 
  'Outdoor Furnace', 'Pellet Stove', 'Radiant', 'Radiant Floor', 'Radiator', 
  'Space Heater', 'Unit Heater', 'Wall Furnace', 'Water Radiators', 'Wood Stove'
]


PROPERTYSUBTYPE_PTYPE2_MAP = {
  'Apartment':['Apartment'],
  'Carriage':['House','Carriage'],
  'Condominium':['Apartment'],
  'Cottage':['House','Detached'],
  'Detached':['House','Detached'],
  'Detached Single Family':['House','Detached'],
  'Duplex':['House','Duplex'],
  'Manufactured Home':['House','Manufactured'],
  'Parking Stall':['other'],
  'Quadruplex':['House','Quadruplex'],
  'Residential Attached':['House','Attached'],
  'Secondary Suite':['Other','Secondary Suite'],
  'Townhouse':['Townhouse','Attached'],
  'Triplex':['House','Triplex'],
  'Vacant Lot/Land':['Other','Land'],
  'Condo/Apt Unit':['Apartment'],
  'Deeded Parking':['Other'],
  'Mobile Home':['Mobile Home'],
  'Modular Home':['House','Manufactured'],
  'Row/Townhouse':['Townhouse','Attached'],
  'Single Family Residence':['Detached'],
}

STRUCTURETYPE_PTYPE2_MAP = {
  'Apartment Unit':['Apartment'],
  'Detached':['Detached'],
  'Land':['Land'],
  'Link':['Link'],
  'Mixed Use':['Mixed Use'],
  'Modular':['Other','Modular'],
  'Semi-Detached':['Semi-Detached'],
  'Townhouse':['Townhouse'],
}


carFieldMap =
  Basement: 'bsmt'
  BathroomsFull: 'bthrms'
  BathroomsHalf: 'bthhlf'
  BathroomsTotalDecimal:'tbthrms'
  BedroomsTotal:'tbdrms'
  BuildingName:'bldg_name'
  BuyerAgencyCompensation:'comm'
  BuyerAgencyCompensationType:'commTp'
  City:'city'
  CloseDate:'sldd'
  ClosePrice:'sp'
  CoListAgentDesignation:'tl'
  ConstructionMaterials:'constr'
  Coordinates:'loc'
  Country:'cnty'
  CrossStreet:'crsst'
  DaysOnMarket:'dom'
  DirectionFaces:'fce'
  Directions:'dir_desc' # new field
  ExpirationDate:'exp'
  FeedTypes:'rhbtp'
  FoundationDetails:'constr'
  GarageSpaces:'gr'
  HeatingYN: 'heat_inc'
  Latitude:'lat'
  ListPrice:'lp'
  ListingId:'sid'
  ListingKey:'carId'
  ListingKeyNumeric:'carId_n'
  LivingArea:'sqft'
  Longitude:'lng'
  MLSAreaMinor:'cmty'
  ModificationTimestamp:'mt'
  OriginalListPrice:'olp'
  OffMarketDate:'offD'
  OwnerName:'owner'
  ParcelNumber:'parcel_id'
  ParkingTotal:'tgr'
  PetsAllowed:'pets'
  PhotosChangeTimestamp:'phomt'
  PhotosCount:'pho'
  Possession:'psn'
  PostalCode:'zip'
  PreviousListPrice:'origLp'
  PublicRemarks:'m'
  ITSO_BedsAboveGrade:'br'
  RoomKitchenLevel:'kitchenLevel'
  ShowingInstructions:'showingRemark'
  StateOrProvince:'prov'
  StateRegion:'region'
  Stories:'lvl'
  StoriesTotal:'NumberofStories'
  StreetDirPrefix:'st_dir'
  StreetName:'st'
  StreetNumber:'st_num'
  StreetSuffix:'st_sfx'
  TaxAnnualAmount:'tax'
  TaxYear:'taxyr'
  UnitNumber:'unt'
  UnparsedAddress:'origAddr'
  VirtualTourURLBranded:'vturl'
  VirtualTourURLUnbranded:'vturl'
  YearBuilt:'bltYr'
  YearBuiltDetails:'bltYrDetails'
  YearBuiltSource:'bltYrSrc'


formatAirCondition = (record) ->
  if record.Cooling
    record.ac = record.Cooling
  else if record.CoolingYN is false
    record.ac = 'None'
  delete record.CoolingYN
  delete record.Cooling
  return record


# General function to process a field from list to string
processFieldFromListToString = (oldValueList) ->
  if not oldValueList
    return null
  if oldValueList.length > 1
    result = oldValueList.join(',')
  else
    result = oldValueList[0]
  return result


# if LivingAreaUnits is 'Square Feet', delete it
formatLivingAreaUnits=(record)->
  if record.LivingAreaUnits is 'Square Feet'
    delete record.LivingAreaUnits

# generate phoUrls from Media
generatePhoUrls = (record) ->
  if not record.Media
    delete record.Media
    debug.error 'generatePhoUrls error, no Media', record._id
    return
  phoUrls = []
  for item,i in record.Media
    phoUrls.push({
      id: String(i + 1),
      url: item.MediaURL
    })
  record.phoUrls = phoUrls
  delete record.Media


# vow yes -> 'v', no -> 'x'
formatVow = (record) ->
  if record['ITSO_VOWYN'] is 'Yes'
    record.vow = 'v'
  if record['ITSO_VOWYN'] is 'No'
    record.vow = 'x'
  delete record.RAE_LV_vow_address


parseDimensions = (dimString,Units) ->
  return null if not dimString or typeof dimString isnt 'string'
  dimensions = dimString.split('X')
  if dimensions.length is 2
    height = parseFloat(dimensions[0])
    width = parseFloat(dimensions[1])
    if Units is 'Feet'
      if w = width * FEET2METER
        width = w
      if h = height * FEET2METER
        height = h
    return { w: width, h: height }
  if dimensions.length is 1
    width = parseFloat(dimensions[0])
    height = parseFloat(dimensions[0])
    if Units is 'Feet'
      if w = width * FEET2METER
        width = w
      if h = height * FEET2METER
        height = h
    return { w: width, h: height }
  return null


ROOM_MAP =
  m: 'RoomDescription'
  features: 'RoomFeatures'
  a: 'RoomArea'
  au: 'RoomAreaUnits'
  width: 'RoomWidth'
  length: 'RoomLength'
  wlu: 'RoomLengthWidthUnits'
formatRms = (record) ->
  if not record.propertyRoom
    return
  rooms = []
  for room in record.propertyRoom
    _room = {
      t: room.RoomType
      l: room.RoomLevel
    }
    for k,v of ROOM_MAP
      if value = room[v]
        _room[k] = value
      parsedDimensions = parseDimensions(room.ITSO_RoomDimensionsMetric,'Meters')
      if parsedDimensions
        _room.w = parsedDimensions.w
        _room.h = parsedDimensions.h
    rooms.push _room
  record.rms = rooms
  delete record.propertyRoom


formatOpenHouseDateTime = (dateString) ->
  [datePart, timePart] = dateString.split 'T'
  # Split the time component to remove the milliseconds and timezone if any
  [time] = timePart.split '.'
  # Combine the date and time parts into the desired format
  formattedDate = "#{datePart} #{time.slice(0, 5)}"
  formattedDate

formatOpenHouse = (record) ->
  if not record.oh
    return
  openHouseDetails = []
  for oh in record.oh
    detail =
      f: formatOpenHouseDateTime(oh.OpenHouseStartTime)
      t: formatOpenHouseDateTime(oh.OpenHouseEndTime)
    if processFieldFromListToString(oh.FeedTypes)
      detail.fp = processFieldFromListToString(oh.FeedTypes)
    if oh.OpenHouseRemarks?
      detail.m = oh.OpenHouseRemarks
    if oh.LivestreamOpenHouseURL?
      detail.vturl = oh.LivestreamOpenHouseURL
    if not oh.LivestreamOpenHouseURL and not oh.VirtualOpenHouseURL
      detail.tp = 'P'
    openHouseDetails.push(detail)
  record.ohz=openHouseDetails
  delete record.oh


UNIT_TYPES_MAP =
  m: 'UnitTypeDescription'
  tp: 'UnitTypeType'
  units: 'UnitTypeUnitsTotal'
  actualRent: 'UnitTypeActualRent'
  totalRent: 'UnitTypeTotalRent'
  baths: 'UnitTypeBathsTotal'
  beds: 'UnitTypeBedsTotal'
formatUnitType = (record) ->
  if not record.propertyUnitType
    return
  if record.propertyUnitType.length > 0
    unitTypes = []
    for unitType in record.propertyUnitType
      unit = {}
      for k,v of UNIT_TYPES_MAP
        if value = unitType[v]
          unit[k] = value
      unitTypes.push(unit)
  record.unitTypes = unitTypes
  delete record.propertyUnitType


cleanRecord = (record) ->
  deleteFields = [
    '@odata',
    'BridgeModificationTimestamp',
    'StreetDirPrefix',
    '_mt',
    'ts',
    'BathroomsTotalInteger',
    'GarageYN',
    'Levels',
    'BathroomsTotalInteger',
  ]
  deletePatterns = [
    /^ITSO_*/,
    /^ListOffice.*/,
    /^ListAgent.*/,
    /^CoListOffice.*/,
    /^CoListAgent.*/,
    /^BuyerOffice.*/,
    /^BuyerAgent.*/,
    /^CoBuyerAgent.*/,
    /^CoBuyerOffice.*/,
  ]
  ITSO_dic={}
  # Iterate over the fields in the record
  for fieldName of record
    if /ITSO_/.test(fieldName)
      ITSO_dic[fieldName]=record[fieldName]
    # Check if field is in the deleteFields list
    if fieldName in deleteFields
      delete record[fieldName]
    for pattern in deletePatterns
      if pattern.test(fieldName)
        delete record[fieldName]
        break
    
  record.ITSO=ITSO_dic
  return record




formatBuilding = (record) ->
  record.Building ?= {}
  building={}
  if record.Appliances
    building.Appliances=record.Appliances
  if record.ArchitecturalStyle
    building.ArchitecturalStyle=processFieldFromListToString(record.ArchitecturalStyle)
  if record.FireplaceYN
    building.FireplacePresent=record.FireplaceYN
  if record.ITSO_BedsAboveGrade
    building.BedroomsAboveGround=record.ITSO_BedsAboveGrade
  # get fuel and heat
  [fuel, heat]=formatHeatingDetails record
  if fuel
    building.HeatingFuel=fuel
  if heat
    building.HeatingType=heat
  record.Building=building
  delete record.Appliances
  delete record.FireplaceYN
  delete record.ITSO_BedsAboveGrade
  delete record.ArchitecturalStyle

formatHeatingDetails = (record) ->
  if not record.Heating or not Array.isArray(record.Heating)
    delete record.Heating
    return [null, null]
  result = {HeatingFuel: [], HeatingType: []}
  for heating in record.Heating
    if heating in HEATING_FUELS
      result.HeatingFuel.push(heating)
    else if heating in HEATING_TYPES
      result.HeatingType.push(heating)
  fuel=processFieldFromListToString(result.HeatingFuel)
  heat=processFieldFromListToString(result.HeatingType)
  delete record.Heating
  return [fuel,heat]

formatFurnished = (record) ->
  return if not record.Furnished
  if record.Furnished is 'Furnished'
    record.frnshd = 1
  else if record.Furnished is 'Unfurnished'
    record.frnshd = 0
  else if record.Furnished is 'Partially'
    record.frnshd = 0.5
  delete record.Furnished

formatListingAgent = (record)->
  record.la = []
  if record.ListOfficeMlsId
    if record.ListOfficePhoneExt
      officeTel = record.ListOfficePhone+'-'+record.ListOfficePhoneExt
    else
      officeTel = record.ListOfficePhone
    listingAgent = {
      id: record.ListOfficeMlsId,
      nm: record.ListOfficeName,
      tel: officeTel,
      fax: record.ListOfficeFax,
      aor:record.ListOfficeAOR,
      eml:record.ListOfficeEmail,
      carId:record.ListOfficeKey,
      carId_n:record.ListOfficeKeyNumeric,
      agnt: [{
        id: record.ListAgentMlsId,
        nm: record.ListAgentFullName,
        tel: record.ListAgentPreferredPhone,
        eml: record.ListAgentEmail,
        aor: record.ListAgentAOR,
        carId:record.ListAgentKey,
        carId_n:record.ListAgentKeyNumeric
        designation:record.ListAgentDesignation
      }],
    }
    record.la.push(listingAgent)
  record.rltr = record.ListOfficeName
  if record.CoListOfficeMlsId
    if record.CoListOfficePhoneExt
      officeTel = record.CoListOfficePhone+'-'+record.CoListOfficePhoneExt
    else
      officeTel = record.CoListOfficePhone

    CoListingAgent2 = {
      id: record.CoListOfficeMlsId,
      nm: record.CoListOfficeName,
      tel: officeTel,
      fax: record.CoListOfficeFax,
      eml:record.CoListOfficeEmail,
      carId:record.CoListOfficeKey,
      carId_n:record.CoListOfficeKeyNumeric,
      designation:processFieldFromListToString(record.CoListAgentDesignation),
      agnt: [{
        id: record.CoListAgentMlsId,
        nm: record.CoListAgentFullName,
        tel: record.agntTel,
        eml: record.CoListAgentEmail,
        carId:record.CoListAgentKey,
        carId_n:record.CoListAgentKeyNumeric,
        aor:record.CoListAgentAOR,
      }],
    }
    record.la.push(CoListingAgent2)
  # buyer agent
  if record.BuyerOfficeMlsId
    if record.BuyerOfficePhoneExt
      buyerOfficeTel = record.BuyerOfficePhone+'-'+record.BuyerOfficePhoneExt
    else
      buyerOfficeTel = record.BuyerOfficePhone
    office = {
      id: record.BuyerOfficeMlsId,
      nm: record.BuyerOfficeName,
      tel: buyerOfficeTel,
      carId:record.BuyerOfficeKey,
      carId_n:record.BuyerOfficeKeyNumeric,
      agnt: [{
        id: record.BuyerAgentMlsId,
        nm: record.BuyerAgentFullName,
        tel: record.BuyerAgentPreferredPhone,
        eml: record.BuyerAgentEmail,
        aor: record.BuyerAgentAOR,
        carId:record.BuyerAgentKey,
        carId_n:record.BuyerAgentKeyNumeric,
        telDirect: record.BuyerAgentDirectPhone,
      }],
    }
    record.coop = [office]

  if record.CoBuyerOfficeMlsId
    if record.BuyerOfficePhoneExt
      coBuyerOfficeTel = record.CoBuyerOfficePhone+'-'+record.CoBuyerOfficePhoneExt
    else
      coBuyerOfficeTel = record.CoBuyerOfficePhone
    office = {
      id: record.CoBuyerOfficeMlsId,
      nm: record.CoBuyerOfficeName,
      tel: coBuyerOfficeTel,
      fax: record.CoBuyerOfficeFax,
      carId: record.CoBuyerOfficeKey,
      carId_n: record.CoBuyerOfficeKeyNumeric,
      agnt: [{
        id: record.CoBuyerAgentMlsId,
        nm: record.CoBuyerAgentFullName,
        tel: record.CoBuyerAgentPreferredPhone,
        telDirect: record.CoBuyerAgentDirectPhone,
        mbl: record.CoBuyerAgentCellPhone,
        eml: record.CoBuyerAgentEmail,
        carId: record.CoBuyerAgentKey,
        carId_n: record.CoBuyerAgentKeyNumeric,
      }],
    }
    record.coop ?= []
    record.coop.push office


# status,lst
exports.formatStatusAndLst = formatStatusAndLst = (record)->
  if not record.MlsStatus
    delete record.MlsStatus
    debug.error 'Car formatStatusAndLst error, no MlsStatus', record._id
    return
  if mappingInfo = statusLstMapping[record.MlsStatus]?['CAR']
    record.status = mappingInfo.status
    record.lst = mappingInfo.lst
  else
    debug.error "Car formatStatusAndLst error, MlsStatus:#{record.MlsStatus}"
  
formatSaletp = (record)->
  isLease = record.ITSO_LeasesYN is true
  if record.PropertyType
    if /Lease/i.test record.PropertyType
      isLease = true
    else if /Sale/i.test record.PropertyType
      isLease = false
  else
    if record.lp? and (parseInt(record.lp) < propertiesHelper.minSalePrice)
      isLease = true
 
  if isLease
    record.lpr = record.lp
    record.saletp = ['Lease']
    delete record.lp
  else
    record.saletp = ['Sale']

###
# 格式化房产类型信息
# @param {Object} record - 房产记录对象
# @description
  1. 根据PropertyType设置ptype(r:住宅,b:商业,o:其他)
  2. 根据PropertySubType和StructureType设置ptype2(具体房产类型)
  3. 使用映射表PROPERTYSUBTYPE_PTYPE2_MAP和STRUCTURETYPE_PTYPE2_MAP转换类型
###
formatPtype = (record) ->
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
  else if /Commercial|Business/i.test(record.PropertyType)
    record.ptype = 'b'
  else
    record.ptype = 'o'
    if record.PropertyType
      record.ptype2 = [record.PropertyType]
  record.ptype2 ?= []
  if record.PropertySubType
    if tpArr = PROPERTYSUBTYPE_PTYPE2_MAP[record.PropertySubType]
      record.ptype2.push(...tpArr)
    else
      record.ptype2.push(record.PropertySubType)

  # 处理StructureType
  if structTp = record.StructureType
    structureTypes = if Array.isArray(structTp) then structTp else [structTp]
    for type in structureTypes
      if tpArr = STRUCTURETYPE_PTYPE2_MAP[type]
        record.ptype2.push(...tpArr)
      else
        record.ptype2.push(type)

  # 去重
  record.ptype2 = Array.from(new Set(record.ptype2))

  # 清理原始字段
  delete record.PropertyType
  delete record.PropertySubType
  delete record.StructureType


formatLandSize = (record) ->
  if record.LotSizeUnits is 'Acres'
    record.lotsz = propertiesHelper.getFloat(record.LotSizeArea * ACRES2SQFT)
    record.lotszUt = 'ft²'
  else if record.LotSizeUnits is 'Hectares'
    record.lotsz = propertiesHelper.getFloat(record.LotSizeArea * HECTARES2SQFT)
    record.lotszUt = 'ft²'
  else if (record.LotSizeUnits is 'Sq. Ft. Divisible') or (record.LotSizeUnits is 'Square Feet') or (record.LotSizeUnits is 'Feet')
    record.lotsz = propertiesHelper.getFloat record.LotSizeArea
    record.lotszUt = 'ft²'
  else if (record.LotSizeUnits is 'Sq. M. Divisible') or (record.LotSizeUnits is 'Square Meters')
    record.lotsz = propertiesHelper.getFloat (record.LotSizeArea * METER2SQFT)
    record.lotszUt = 'ft²'
  else
    debug.error "Unknown lot size unit: #{record.LotSizeUnits} for property #{record.sid}"
    record.lotsz = propertiesHelper.getFloat record.LotSizeArea
    record.lotsz = convertLotSize(record.lotsz)

  delete record.LotSizeArea
  delete record.LotSizeUnits
  delete record.LotSizeAcres
  delete record.LotSizeSquareFeet

###
# @description 格式化朝向信息
# @param {Object} record - 记录对象
###
formatFce = (record) ->
  if record.fce
    result = abbrExposure(record.fce)
    record.fce = result if result?
  else if record.FrontageType?.length > 0
    result = abbrExposure(record.FrontageType)
    record.fce = result if result?
    delete record.FrontageType
  return


exports.convertFromCar = convertFromCar = ({
  srcRecord,
  Boundary
}, cb) ->
  try
    ret = doMapping srcRecord,carFieldMap
    formatAirCondition ret
    formatLivingAreaUnits ret
    # generatePhoUrls ret
    formatVow ret
    formatUnitType ret
    formatVturl ret, ['VirtualTourURLBranded', 'VirtualTourURLUnbranded']
    formatRms ret
    formatOpenHouse ret
    formatMfee ret
    formatBuilding ret
    formatFurnished ret
    formatListingAgent ret
    formatStatusAndLst ret
    formatSaletp ret
    formatPtype ret
    formatLandSize ret
    formatDaddr ret
    formatFce ret
    ret.lvl = processFieldFromListToString(ret.Levels)
    ret.water = processFieldFromListToString(ret.WaterSource)
    ret.cnty  ?= 'CA'
    ret.prov  ?= 'ON'
    ret.orgId = ret._id
    ret._id = 'CAR' + ret.sid
    ret.id = 'CAR' + ret.sid
    ret.src = 'CAR' # 如果更换了这个值，记得修改statusLstMapping
    cleanRecord ret
  catch err
    debug.error 'convert err', err
    return cb err
  debug.debug 'convert ret', ret
  cb null, ret