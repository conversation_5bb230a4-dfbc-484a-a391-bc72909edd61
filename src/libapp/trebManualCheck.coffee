debugHelper = require '../lib/debug'
helpers = require '../lib/helpers'
libProperties = require '../libapp/properties'
addLogs = libProperties.addLogs
addLogsAsync = helpers.addAsyncSupport addLogs

debug = debugHelper.getDebugger()

PREFIX = 'TRB'

# 判断是否存在缺少的房源并进行处理
# @desc 进入这里的id都是evow不存在的，从terb evow查询后返回的结果
module.exports.checkMissingProps = ({
  manualIDs,          # rni.mls_treb_manual_ids记录 [{_id:'',id:''}]
  records,            # downloaded records, could be []
  collManualIDs,      # collection rni.mls_treb_manual_ids
  collMasterRecords,  # collection rni.mls_treb_master_records
  collIDXRecords,     # collection rni.mls_treb_idx_records
  collPropertyImportLog # collection tmp.properties_import_log
})->
  if (not manualIDs?.length) or (not collIDXRecords)
    return
  downloadedIdsMap = {}
  records ?= []
  records.forEach (item)-> downloadedIdsMap[item.Ml_num] = 1
  missProps = manualIDs.filter (item)->
    return not downloadedIdsMap[item.id]
  if not missProps.length
    return
  # 房源从evow撤出
  for prop in missProps
    await collManualIDs.deleteOne {_id:PREFIX+prop.id}
    # 确认房源是否为idx,查找idx状态,修改lSrc,status
    idxProp = await collIDXRecords.findOne {_id:PREFIX+prop.id}
    # TODO: idx records 状态可能也不准确，查找idx ids?
    # debug.info 'missProps:',missProps,'idxProp:',idxProp
    if not idxProp
      # debug.info 'idxProp not found',PREFIX+prop.id
      continue
    step = 'idxBatch'
    if (idxProp.Status is 'U') or (idxProp.status is 'U')
      # idx状态是U，把房源状态改为下线
      # NOTE:mls_treb_idx_records中不存在Lsc字段
      log = {status:'U',m:'batch changeStatusOnly'}
      await addLogsAsync {id:"#{PREFIX+prop.id}",step,log,prop,collPropertyImportLog}
      await collMasterRecords.updateOne {_id:PREFIX+prop.id},{
        $set:{status:'U',lSrc:'idx'},
        $addToSet:{src:'idx'}
      }
    else
      # idx状态在线，改lastSrc为idx
      log = {m:'batch changeLSrcOnly'}
      await addLogsAsync {id:"#{PREFIX+prop.id}",step,log,prop,collPropertyImportLog}
      await collMasterRecords.updateOne {_id:PREFIX+prop.id},{
        $set:{lSrc:'idx'},
        $addToSet:{src:'idx'}
      }

# rni现有房源与下载房源对比,判断是否是同一个房源，判断状态与价格判断是否一致
isSameRniAndDownProp = (rniProp,downProp)->
  rniStatus = rniProp.Status or rniProp.status
  downStatus = downProp.Status or downProp.status
  if rniStatus isnt downStatus
    return false
  if rniProp.Lp_dol isnt downProp.Lp_dol
    return false
  return true

# 检查房源是否过期
isExpiredProp = (rniProp,downProp)->
  # 没有xd字段,认为没有过期
  if not rniProp.Xd
    return false
  exp = helpers.inputToDateNum(rniProp.Xd)
  today = helpers.inputToDateNum(new Date())
  # 过期时间大于等于今天,没有过期
  if exp >= today
    return false
  # 过期时间小于今天
  #   最新下载房源是Exp状态,认为过期,不发送邮件
  if (downProp.Status is 'U') and (downProp.Lsc is 'Exp')
    return true
  #   但是最新下载房源不是Exp状态,认为没有过期
  return false

# 根据sid在rni查询现有记录，与下载房源状态对比，并对结果进行处理
module.exports.queryAndComparePropRniWithDownload = ({
  record,             # downloaded record
  collMasterRecords,  # collection rni.mls_treb_master_records
  collManualIDs,      # collection rni.mls_treb_manual_ids
  issueIds=[]        # issues ids list
})->
  rniProp = await collMasterRecords.findOne {_id:PREFIX+record.Ml_num}
  if not rniProp
    debug.error "prop:#{record.Ml_num} has not rni record"
    return

  if not isSameRniAndDownProp(rniProp,record)
    if isExpiredProp(rniProp,record)
      debug.warn "prop:#{PREFIX+record.Ml_num} has been expired in rni db"
    else
      debug.error "Import has issues, prop:#{PREFIX+record.Ml_num} in rni db is not same to downloaded from 3rd party"
      issueIds.push PREFIX+record.Ml_num
  
  await collManualIDs.deleteOne {_id:PREFIX+record.Ml_num}


module.exports.notifyAdminWhenHaveIssue = (sendMailObj, issueIds) ->
  return unless sendMailObj? and issueIds?.length > 0

  try
    msg = """
      TREB Import Issues Report
      
      Total Issues: #{issueIds.length}
      Affected Properties: #{issueIds.join(', ')}
      Time: #{new Date().toISOString()}
      
      These properties in RNI database do not match the downloaded data.
      Please review these properties in the admin dashboard.
      """
    sendMailObj.notifyAdmin(msg)
  catch error
    debug.error "Failed to send admin notification: #{error.message}"