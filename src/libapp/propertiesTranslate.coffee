###*
 # 房源信息翻译模块
 # INCLUDE 'libapp.propertiesTranslate'
###

###*
 # 引入依赖模块
 # @requires i18n - 国际化模块
 # @requires abbreviate - 缩写处理模块
 # @requires helpers - 通用辅助函数
 # @requires helpers_string - 字符串处理辅助函数
 # @requires properties - 房源相关函数
###
i18n = require '../lib/i18n'
abbreviator = require '../lib/abbreviate'
helpers = require '../lib/helpers'
{isSubstring} = require '../lib/helpers_string'
{currencyFormat} = require './properties'
{shouldTransCity} = i18n


splitFieldsKey = (k)->
  # 处理特殊情况，如URL不应该被拆分为单个字母
  splitKey = k.replace(/([A-Z][a-z]+)|([A-Z]+)(?=[A-Z][a-z]|$)/g, ' $&').trim()
  # 移除开头的空格
  splitKey = splitKey.replace(/^\s+/, '')
  return splitKey

###*
 # DDF dont have trbtp or [], ddf only,
 # NOTE: trbtp no present
 # 判断是否为DDF属性
 # @param {Object} prop - 属性对象
 # @returns {boolean} 是否为DDF属性
###
isDDFProp = (prop)->
  if prop.src is 'DDF'
    return true
  if /^RM/.test prop.id
    return false
  if prop.src in ['TRB','RHB','OTW','CLG','EDM','CAR']
    return false
  tmp = prop.trbtp or []
  return tmp.length is 0

###*
 # 判断销售类型是否为销售
 # @param {array} saletp - 房源销售类型
 # @returns {boolean} 是否为销售
###
module.exports.saletpIsSale = saletpIsSale =(saletp)->
  if not saletp
    return true
  if /sale/.test(saletp.toString().toLowerCase())
    return true
  return false

gTranslator = {}
###*
 # 获取翻译器
 # @param {string} locale - 语言环境
 # @returns {Object} 翻译器对象
###
getTranslator = (locale)->
  bundle = gTranslator[locale]
  if not bundle
    bundle = {}
    l10n = i18n.getFun locale
    _ab = (a,b)-> l10n abbreviator.ab(a,b),b
    bundle.l10n = l10n
    bundle._ab = _ab
    gTranslator[locale] = bundle
  bundle

###*
 # 房源信息分类常量
 # @constant
 # @type {Array<string>}
###
# Building
# Basement
# Land
# Parking
# Utilities
# Surrounding
# Other

###*
 # BC地区缩写对照表
 # @constant
 # @type {Object}
###
BC_KEY_ABBR_TO_FULL = {
  Flr: 'Floor'
  Abv: 'Above'
  BLW: 'Below'
  Fin: 'Finished'
  Sz: 'Size'
  Pcs: 'Pieces'
  Sq: 'Square'
  Mtrs: 'Meters'
  Incl: 'Included'
  Inclin: 'Included in'
  Apprv: 'Approve'
  Bldgs: ''
  Rntd: 'Rented'
  Lsd: 'Leased'
  Remvd: 'Removed'
  Wrkshp: 'Workshop'
  Yrof: 'Year of'
  Renos: 'Renovations'
  # Addns: ''
}

###*
 # EDM/CAR地区缩写对照表
 # @constant
 # @type {Object}
###
EDM_CAR_KEY_ABBR_TO_FULL = {
  dir_desc: 'DirectionsDescription'
}

###*
 # BCRE翻译映射表
 # @constant
 # @type {Object}
###
BCRE_TRANS_MAP = {
  # InternetRemarks: 0
  # OutBldgsGarageSz: 0
  # OutBldgsBarnSz: 0
  # LegalDescription: 0
  Type:1
}

###*
 # BCRE扩展字段分类
 # @constant
 # @type {Object}
###
BCRE_EXT_FIELDS = {
  OutdoorArea: {parent: 'Building'}
  PartHeight: {parent: 'Building'}
  FloorAreaGrandTotal: {parent: 'Building'}
  FloorAreaFinMainFlr: {parent: 'Building'}
  FloorAreaFinTotal: {parent: 'Building'}
  FloorAreaFinAbvMain: {parent: 'Building'}
  FloorAreaFinAbvMain2: {parent: 'Building'}
  FloorAreaUnfinished: {parent: 'Building'}
  FloorAreaFinBLWMain: {parent: 'Building'}
  LegalDescription: {parent: 'Building'}
  DrivewayFinish: {parent: 'Building'}
  Fireplaces: {parent: 'Building'}
  BathEnsuiteOfPcs: {parent: 'Building'}
  LotSzSqFt: {parent: 'Building'}
  Type: {parent: 'Building'}
  Foundation: {parent: 'Building'}
  Locker: {parent: 'Building'}
  UnitsinDevelopment: {parent: 'Building'}
  TitletoLand: {parent: 'Building'}
  FireplaceFueledby: {parent: 'Building'}
  HalfBaths: {parent: 'Building'}
  FullBaths: {parent: 'Building'}
  NoFloorLevels: {parent: 'Building'}
  FloorFinish: {parent: 'Building'}
  Roof: {parent: 'Building'}
  Renovations: {parent: 'Building'}
  TotUnitsinStrataPlan: {parent: 'Building'}
  Construction: {parent: 'Building'}
  StoreysinBuilding: {parent: 'Building'}
  Suite: {parent: 'Building'}
  ExteriorFinish: {parent: 'Building'}
  Flooring: {parent: 'Building'}
  FireplacesTotal: {parent: 'Building'}
  SeniorCommunityYN: {parent: 'Building'}
  ExteriorFeatures: {parent: 'Building'}
  AboveGradeFinishedArea: {parent: 'Building'}
  Appliances: {parent: 'Building', isTrans: true}
  CommonWalls: {parent: 'Building'}
  StoriesTotal: {parent: 'Building'}
  OtherStructures: {parent: 'Building'}
  AssociationAmenities: {parent: 'Building', isTrans: true}
  RoomsTotal: {parent: 'Building'}
  BuildingAreaTotal: {parent: 'Building'}
  GarageYN: {parent: 'Building'}
  BelowGradeFinishedArea: {parent: 'Building'}
  MainLevelBathrooms: {parent: 'Building'}
  PropertyCondition: {parent: 'Building'}
  PatioAndPorchFeatures: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  WindowFeatures: {parent: 'Building'}
  LotFeatures: {parent: 'Building'}
  ArchitecturalStyle: {parent: 'Building'}
  CrawlHeight: {parent: 'Other'}
  FullHeight: {parent: 'Other'}
  LaundryFeatures: {parent: 'Other'}
  BCRES_Restrictions: {parent: 'Other'}
  SecurityFeatures: {parent: 'Other', isTrans: true}
  AssociationYN: {parent: 'Other'}
  NetOperatingIncome: {parent: 'Other'}
  InternetEntireListingDisplayYN: {parent: 'Other'}
  RoadSurfaceType: {parent: 'Other'}
  RoadFrontageType: {parent: 'Other'}
  DevelopmentStatus: {parent: 'Other'}
  GrossIncome: {parent: 'Other'}
  InteriorFeatures: {parent: 'Other', isTrans: true}
  Sewer: {parent: 'Other'}
  AccessibilityFeatures: {parent: 'Other'}
  LotSzSqMtrs: {parent: 'Land'}
  LotSzHectares: {parent: 'Land'}
  LotSzAcres: {parent: 'Land'}
  DirectionalExpRearYard: {parent: 'Land', isTrans: true}
  TaxUtilitiesIncl: {parent: 'Utilities', isTrans: true}
  ForTaxYear: {parent: 'Utilities'}
  WaterSupply: {parent: 'Utilities'}
  RoughedInPlumbing: {parent: 'Utilities'}
  FeaturesIncluded: {parent: 'Utilities'}
  FuelHeating: {parent: 'Utilities'}
  NotInclinTaxGarbage: {parent: 'Other'}
  DDFMoreInfoURL: {parent: 'Other'}
  NotInclinTaxDyking: {parent: 'Other'}
  NotInclinTaxOther: {parent: 'Other'}
  NotInclinTaxWater: {parent: 'Other'}
  Parking: {parent: 'Parking'}
  ParkingAccess: {parent: 'Parking'}
  GarageDoorHt: {parent: 'Parking'}
  ParkingType: {parent: 'Parking'}
  OutBldgsGarageSz: {parent: 'Parking'}
  ParkingFeatures: {parent: 'Parking'}
  DisttoSchoolSchoolBus: {parent: 'Surrounding'}
  CouncilParkApprv: {parent: 'Surrounding'}
  WaterfrontFeatures: {parent: 'Surrounding'}
  CommunityFeatures: {parent: 'Surrounding'}
  DistancetoPubRapidTr: {parent: 'Surrounding'}
  FloorAreaFinBasement: {parent: 'Basement'}
  BasementArea: {parent: 'Basement', isTrans: true}
  RoughedinFP: {parent: 'Other'}
  ProcessedDate: {parent: 'Other'}
  OutBldgsPoolSz: {parent: 'Other'}
  PropertyBrochureURL: {parent: 'Other'}
  PID: {parent: 'Other'}
  SewerType: {parent: 'Other'}
  CancelEffectiveDate: {parent: 'Other'}
  GSTIncl: {parent: 'Other', isTrans: true}
  SiteInfluences: {parent: 'Other'}
  PADRental: {parent: 'Other'}
  NoBuyerAgency: {parent: 'Other'}
  AgeType: {parent: 'Other'}
  PropertyDisclosure: {parent: 'Other'}
  ByLawRestrictions: {parent: 'Other'}
  ServicesConnected: {parent: 'Other', isTrans: true}
  RainScreen: {parent: 'Other'}
  ViewSpecify: {parent: 'Other'}
  FixtRntdLsdSpecify: {parent: 'Other'}
  AddressNumberLow: {parent: 'Other'}
  FixturesRemvd: {parent: 'Other'}
  OutBldgsBarnSz: {parent: 'Other'}
  OutBldgsWrkshpShedSz: {parent: 'Other'}
  ofPets: {parent: 'Other'}
  BrokerReciprocity: {parent: 'Other'}
  FixturesRemovedYN: {parent: 'Other'}
  FixturesRntdLsd: {parent: 'Other'}
  CSABCE: {parent: 'Other'}
  AltFeatureSheetURL: {parent: 'Other'}
  FloodPlain: {parent: 'Other'}
  ApproxYrofRenosAddns: {parent: 'Other'}
  NotInclinTaxSewer: {parent: 'Other'}
  MgmtCoName: {parent: 'Other'}
  MgmtCoPhone: {parent: 'Other'}
  Cats: {parent: 'Other'}
  Dogs: {parent: 'Other'}
  SPOLPRatio: {parent: 'Other'}
  MaintFeeIncludes: {parent: 'Other', isTrans: true}
  ShortTermLseDetails: {parent: 'Other'}
  LandLeaseExpiryYear: {parent: 'Other'}
  RestrictedAge: {parent: 'Other'}
  SPLPRatio: {parent: 'Other'}
  PropDisclosureStatement: {parent: 'Other'}
  # or: {parent: 'Other', isTrans: true} # NOTE: 值都为符号,[ '#', '%' ]
}

###*
 # BCRE键名大写转驼峰映射表
 # @constant
 # @type {Object}
###
BCRE_KEY_UPPER_TO_CAMEL = {
  'GST':'Gst'
  'PID':'Pid',
  'BLW': 'Blw'
}

###*
 # CREB扩展字段分类
 # @constant
 # @type {Object}
###
CREB_EXT_FIELDS = {
  EnsuiteYN: {parent: 'Building'}
  GarageYN: {parent: 'Building'}
  LotFeatures: {parent: 'Building'}
  OccupantType: {parent: 'Building'}
  RoomsTotal: {parent: 'Building'}
  BuildingAreaTotal: {parent: 'Building'}
  BuildingAreaTotalMetres: {parent: 'Building'}
  BuildingAreaTotalSF: {parent: 'Building'}
  BuildingName: {parent: 'Building'}
  CondoName: {parent: 'Building'}
  YardSize: {parent: 'Building'}
  Appliances: {parent: 'Building', isTrans: true}
  BelowGradeFinishedArea: {parent: 'Building'}
  FireplacesTotal: {parent: 'Building'}
  FivePieceBath: {parent: 'Building'}
  FivePieceEnsuiteBath: {parent: 'Building'}
  Flooring: {parent: 'Building'}
  FoundationDetails: {parent: 'Building'}
  FourPieceBath: {parent: 'Building'}
  FourPieceEnsuiteBath: {parent: 'Building'}
  OnePieceBath: {parent: 'Building'}
  Roof: {parent: 'Building'}
  SUITE: {parent: 'Building'}
  CommonWalls: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  Cooling: {parent: 'Building', isTrans: true}
  Outbuildings: {parent: 'Building'}
  PatioAndPorchFeatures: {parent: 'Building'}
  Fencing: {parent: 'Building'}
  AdditionalParcelsYN: {parent: 'Land'}
  SoilType: {parent: 'Land'}
  CommunityFeatures: {parent: 'Surrounding'}
  CountyOrParish: {parent: 'Surrounding'}
  NineOneOneAddress: {parent: 'Surrounding'}
  WaterfrontFeatures: {parent: 'Surrounding'}
  ElementarySchoolDistrict: {parent: 'Surrounding'}
  HighSchoolDistrict: {parent: 'Surrounding'}
  Restrictions: {parent: 'Other'}
  TaxBlock: {parent: 'Other'}
  Sewer: {parent: 'Other'}
  AcresOfFreehold: {parent: 'Other'}
  AssociationFee: {parent: 'Other'}
  AssociationFee2: {parent: 'Other'}
  AssociationFeeFrequency: {parent: 'Other', isTrans: true}
  AssociationFeeIncludes: {parent: 'Other', isTrans: true}
  # Inclusions: {parent: 'Other'} # 前端有单独判断显示
  InteriorFeatures: {parent: 'Other'}
  LaundryFeatures: {parent: 'Other'}
  AccessibilityFeatures: {parent: 'Other'}
  HeatingExpense: {parent: 'Other'}
  WaterSource: {parent: 'Utilities'}
  Electric: {parent: 'Utilities', isTrans: true}
  Furnished: {parent: 'Utilities'}
  ParkingAssigned: {parent: 'Parking'}
  ParkingCommonSpaces: {parent: 'Parking'}
  ParkingFeatures: {parent: 'Parking'}
  ParkingPlanType: {parent: 'Parking'}
}

###*
 # OREB扩展字段分类
 # @constant
 # @type {Object}
###
OREB_EXT_FIELDS = {
  ExteriorFinish: {parent: 'Building'}
  FireRetrofit: {parent: 'Building'}
  FireplaceFuel: {parent: 'Building'}
  FloorCovering: {parent: 'Building'}
  FoundationDescription: {parent: 'Building'}
  InterboardListing: {parent: 'Building'}
  LegalDescription: {parent: 'Building'}
  NumberFinishedFireplaces: {parent: 'Building'}
  NumberofCoveredSpaces: {parent: 'Building'}
  NumberofEnsuiteBathrooms: {parent: 'Building'}
  NumberofFireplaces: {parent: 'Building'}
  NumberofGarageSpaces: {parent: 'Building'}
  RoomCount: {parent: 'Building'}
  SeasonalDwelling: {parent: 'Building'}
  AppliancesIncluded: {parent: 'Building', isTrans: true}
  RoofDescription: {parent: 'Building'}
  TypeofLot: {parent: 'Building'}
  CondoName: {parent: 'Building'}
  HasSecondaryDwellingUnitYN: {parent: 'Building'}
  NumberofHydroMeters: {parent: 'Building'}
  NumberofRefrigerators: {parent: 'Building'}
  NumberofStoves: {parent: 'Building'}
  NumberofTownhouseUnits: {parent: 'Building'}
  NumberofWashersDryers: {parent: 'Building'}
  TotalNumberofUnits: {parent: 'Building'}
  NoofVacantUnits: {parent: 'Building'}
  NumberofOtherUnits: {parent: 'Building'}
  NumberofOfficeUnits: {parent: 'Building'}
  TotalNumberofOtherUnits: {parent: 'Building'}
  RoomingHouseLicenseMultiYN: {parent: 'Building'}
  NumberofLevelsInUnit: {parent: 'Building'}
  NumberofStories: {parent: 'Building'}
  IrregularLotYN: {parent: 'Building'}
  TitleForm: {parent: 'Building'}
  SiteInfluences: {parent: 'Other'}
  SPLP: {parent: 'Other'}
  SewerType: {parent: 'Other'}
  Restrictions: {parent: 'Other'}
  SurveyYear: {parent: 'Other'}
  ModelName: {parent: 'Other'}
  AdditionalPropertyUsesICI: {parent: 'Other'}
  DistributeOnInternetYN: {parent: 'Other'}
  RoadAccess: {parent: 'Other'}
  AdditionalImagesLinkURL: {parent: 'Other'}
  Amenities: {parent: 'Other', isTrans: true}
  CondoFee: {parent: 'Other'}
  CondoFeeFrequency: {parent: 'Other', isTrans: true}
  FeeIncludes: {parent: 'Other', isTrans: true}
  LaundryFacilities: {parent: 'Other'}
  SpecialLevyYN: {parent: 'Other'}
  AssistiveFeaturesRemarks: {parent: 'Other'}
  HasAdditionalCommissionYN: {parent: 'Other'}
  RentPriceRate: {parent: 'Other'}
  TenantPays: {parent: 'Other', isTrans: true}
  AreaCleared: {parent: 'Other'}
  AreaCultivated: {parent: 'Other'}
  AreaTileDrained: {parent: 'Other'}
  CropsIncludedYN: {parent: 'Other'}
  FarmRemarks: {parent: 'Other'}
  FarmResidenceYN: {parent: 'Other'}
  LivestockIncludedYN: {parent: 'Other'}
  TypeofFarm: {parent: 'Other'}
  CurrentUse: {parent: 'Other'}
  AnnualDebtService: {parent: 'Other'}
  AnnualGrossIncome: {parent: 'Other'}
  CashFlowAmount: {parent: 'Other'}
  EffectRentalIncome: {parent: 'Other'}
  GarbageExpense: {parent: 'Other'}
  GrossOperatingIncome: {parent: 'Other'}
  HeatingExpense: {parent: 'Other'}
  HydroExpense: {parent: 'Other'}
  InsuranceExpense: {parent: 'Other'}
  LaundryOtherIncome: {parent: 'Other'}
  LawnExpense: {parent: 'Other'}
  NetOperatingIncome: {parent: 'Other'}
  NumberofBachelorUnits: {parent: 'Other'}
  OtherExpense: {parent: 'Other'}
  SupplyExpense: {parent: 'Other'}
  TotalIncomeperMonth: {parent: 'Other'}
  SecurityExpense: {parent: 'Other'}
  TotalOperatExpPct: {parent: 'Other'}
  TotalOperatingExpenses: {parent: 'Other'}
  VacancyLossAmount: {parent: 'Other'}
  VacancyPercentage: {parent: 'Other'}
  WaterSewerExpense: {parent: 'Other'}
  NumberofDishwashers: {parent: 'Other'}
  DevelopmentStatus: {parent: 'Other'}
  LotServiced: {parent: 'Other'}
  RoadSurface: {parent: 'Other'}
  EnvironmentAssessment: {parent: 'Other'}
  FireProtection: {parent: 'Other'}
  HospitalityType: {parent: 'Other'}
  WaterSupply: {parent: 'Utilities'}
  FeaturesEquipmentIncluded: {parent: 'Utilities'}
  FeaturesforDisabledYN: {parent: 'Utilities'}
  PowerDescription: {parent: 'Utilities'}
  NumofAcres: {parent: 'Land'}
  NumberofParcels: {parent: 'Land'}
  TotalAreaAcres: {parent: 'Land'}
  ApproximateBuildingSqft: {parent: 'Land'}
  NumberofLots: {parent: 'Land'}
  ParkingDescription: {parent: 'Parking'}
  ParkingAtAddCostGencomYN: {parent: 'Parking'}
  ParkingIncluded: {parent: 'Parking'}
  ParkingforGencom: {parent: 'Parking'}
  ParkingIncome: {parent: 'Parking'}
  SurfaceParkingforUnits: {parent: 'Parking'}
  UnderParkingforUnits: {parent: 'Parking'}
  DeckParkingforUnits: {parent: 'Parking'}
}

###*
 # CAR扩展字段分类
 # @constant
 # @type {Object}
###
CAR_EXT_FIELDS = {
  Appliances: {parent: 'Building', isTrans: true}
  ArchitecturalStyle: {parent: 'Building'}
  Amenities: {parent: 'Building', isTrans: true}
  FireplacePresent: {parent: 'Building'}
  BedroomsAboveGround: {parent: 'Building'}
  HeatingType: {parent: 'Building'}
  HeatingFuel: {parent: 'Building'}
  AboveGradeFinishedArea: {parent: 'Building'}
  AboveGradeFinishedAreaSource: {parent: 'Building'}
  AboveGradeFinishedAreaUnits: {parent: 'Building'}
  AssociationAmenities: {parent: 'Building', isTrans: true}
  BelowGradeFinishedArea: {parent: 'Building'}
  BuildingType: {parent: 'Building'}
  BuildingAreaSource: {parent: 'Building'}
  BuildingAreaTotal: {parent: 'Building'}
  BuildingAreaUnits: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  Flooring: {parent: 'Building'}
  FullBaths: {parent: 'Building'}
  IrrigationWaterRightsYN: {parent: 'Building'}
  HalfBaths: {parent: 'Building'}
  HomeWarrantyYN: {parent: 'Building'}
  LivingAreaUnits: {parent: 'Building'}
  LotFeatures: {parent: 'Building'}
  MainLevelBathrooms: {parent: 'Building'}
  MainLevelBedrooms: {parent: 'Building'}
  NewConstructionYN: {parent: 'Building'}
  NumberOfBuildings: {parent: 'Building'}
  NumberofStories: {parent: 'Building'}
  OccupantType: {parent: 'Building'}
  OriginatingSystemName: {parent: 'Building'}
  PatioAndPorchFeatures: {parent: 'Building'}
  PropertyAttachedYN: {parent: 'Building'}
  PropertyCondition: {parent: 'Building'}
  Roof: {parent: 'Building'}
  RoomsTotal: {parent: 'Building'}
  SeniorCommunityYN: {parent: 'Building'}
  CommunityFeatures: {parent: 'Surrounding'}
  ExteriorFeatures: {parent: 'Surrounding'}
  ElementarySchool: {parent: 'Surrounding'}
  MiddleOrJuniorSchool: {parent: 'Surrounding'}
  HighSchool: {parent: 'Surrounding'}
  RoadType: {parent: 'Surrounding'}
  Zoning: {parent: 'Surrounding'}
  CountyOrParish: {parent: 'Surrounding'}
  AddressSuiteSearch: {parent: 'Other'}
  AssociationFee: {parent: 'Other'}
  AssociationFee2: {parent: 'Other'}
  AssociationFeeFrequency: {parent: 'Other', isTrans: true}
  AssociationFee2Frequency: {parent: 'Other', isTrans: true}
  # NOTE: CAR房源的该字段值为['Association Fee,Insurance,Maintenance Grounds,Parking,Trash,Property Management Fees,Snow Removal'],不添加翻译
  AssociationFeeIncludes: {parent: 'Other'}
  AssociationYN: {parent: 'Other'}
  CropsIncludedYN: {parent: 'Other'}
  # Inclusions: {parent: 'Other'} # 前端有单独判断显示
  InteriorFeatures: {parent: 'Other'}
  InternetAutomatedValuationDisplayYN: {parent: 'Other'}
  InternetConsumerCommentYN: {parent: 'Other'}
  InternetEntireListingDisplayYN: {parent: 'Other'}
  LaundryFeatures: {parent: 'Other'}
  SignOnPropertyYN: {parent: 'Other'}
  StandardStatus: {parent: 'Other', isTrans: true}
  TaxAssessedValue: {parent: 'Other'}
  FarmLandAreaUnits: {parent: 'Land'}
  LotSizeAcres: {parent: 'Land'}
  LotSizeArea: {parent: 'Land'}
  LotSizeDimensions: {parent: 'Land'}
  OpenParkingSpaces: {parent: 'Parking'}
  ParkingFeatures: {parent: 'Parking'}
  Sewer: {parent: 'Utilities'}
  WaterSource: {parent: 'Utilities'}
  WaterfrontYN: {parent: 'Utilities'}
}

###*
 # RESO TREB扩展字段分类
 # @constant
 # @type {Object}
###
RESO_TREB_EXT_FIELDS = {
  AssociationAmenities: {parent: 'Building', isTrans: true}
  ArchitecturalStyle: {parent: 'Building'}
  FireplaceYN: {parent: 'Building'}
  HeatingYN: {parent: 'Building'}
  MainLevelBathrooms: {parent: 'Building'}
  MainLevelBedrooms: {parent: 'Building'}
  PrivateEntranceYN: {parent: 'Building'}
  PropertyAttachedYN: {parent: 'Building'}
  PropertyFeatures: {parent: 'Building'}
  RoomsAboveGrade: {parent: 'Building'}
  RoomsTotal: {parent: 'Building'}
  BuildingAreaTotal: {parent: 'Building'}
  BuildingAreaUnits: {parent: 'Building'}
  SeniorCommunityYN: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  FireplacesTotal: {parent: 'Building'}
  Roof: {parent: 'Building'}
  ExteriorFeatures: {parent: 'Building'}
  HeatSource: {parent: 'Building'}
  HeatType: {parent: 'Building'}
  Water: {parent: 'Building'}
  Locker: {parent: 'Building'}
  NewConstructionYN: {parent: 'Building'}
  LaundryLevel: {parent: 'Building'}
  OtherStructures: {parent: 'Building'}
  SeasonalDwelling: {parent: 'Building'}
  GarageYN: {parent: 'Building'}
  SewerYNA: {parent: 'Building'}
  WaterYNA: {parent: 'Building'}
  TelephoneYNA: {parent: 'Building'}
  AssociationYN: {parent: 'Building'}
  BasementYN: {parent: 'Building'}
  DenFamilyroomYN: {parent: 'Other'}
  DepositRequired: {parent: 'Other'}
  EmploymentLetterYN: {parent: 'Other'}
  EnergyCertificate: {parent: 'Other'}
  # Inclusions: {parent: 'Other'} # 前端有单独判断显示
  InteriorFeatures: {parent: 'Other'}
  InternetEntireListingDisplayYN: {parent: 'Other'}
  LaundryFeatures: {parent: 'Other'}
  PaymentFrequency: {parent: 'Other', isTrans: true}
  PaymentMethod: {parent: 'Other', isTrans: true}
  ReferencesRequiredYN: {parent: 'Other'}
  SecurityFeatures: {parent: 'Other'}
  Sewer: {parent: 'Other'}
  AccessibilityFeatures: {parent: 'Other'}
  InsuranceExpense: {parent: 'Other'}
  LocalImprovements: {parent: 'Other'}
  LocalImprovementsComments: {parent: 'Other'}
  RoadAccessFee: {parent: 'Other'}
  SignOnPropertyYN: {parent: 'Other'}
  SurveyType: {parent: 'Other'}
  WaterMeterYN: {parent: 'Other'}
  WellDepth: {parent: 'Other'}
  Winterized: {parent: 'Other'}
  AssignmentYN: {parent: 'Other'}
  LeasedTerms: {parent: 'Other'}
  HandicappedEquippedYN: {parent: 'Other'}
  OutsideStorageYN: {parent: 'Other'}
  CraneYN: {parent: 'Other'}
  HeatingExpenses: {parent: 'Other'}
  MaintenanceExpense: {parent: 'Other'}
  ElectricExpense: {parent: 'Other'}
  OperatingExpense: {parent: 'Other'}
  OtherExpense: {parent: 'Other'}
  ProfessionalManagementExpense: {parent: 'Other'}
  WaterExpense: {parent: 'Other'}
  YearExpenses: {parent: 'Other'}
  TotalExpenses: {parent: 'Other'}
  SoilType: {parent: 'Other'}
  SoldAreaUnits: {parent: 'Other'}
  AdditionalMonthlyFeeFrequency: {parent: 'Other'}
  ZoningDesignation: {parent: 'Other'}
  BuyOptionYN: {parent: 'Other'}
  CentralVacuumYN: {parent: 'Other'}
  CreditCheckYN: {parent: 'Other'}
  ElectricYNA: {parent: 'Utilities', isTrans: true}
  ElevatorYN: {parent: 'Utilities'}
  WaterfrontYN: {parent: 'Utilities'}
  GarageType: {parent: 'Parking'}
  ParkingFeatures: {parent: 'Parking'}
  DockingType: {parent: 'Parking'}
  LotFeatures: {parent: 'Land'}
  LotDimensionsSource: {parent: 'Land'}
  LotShape: {parent: 'Land'}
  LotSizeRangeAcres: {parent: 'Land'}
  FarmFeatures: {parent: 'Land'}
  IslandYN: {parent: 'Land'}
  LeasedLandFee: {parent: 'Land'}
  UnderContract: {parent: 'Other', isTrans: true}
  RentIncludes: {parent: 'Other', isTrans: true}
}

DDF_EXT_FIELDS = {
  Age: {parent: 'Building'}
  Appliances: {parent: 'Building', isTrans: true}
  ArchitecturalStyle: {parent: 'Building'}
  BuildingFeatures: {parent: 'Building'}
  ConstructedDate: {parent: 'Building'}
  ConstructionMaterials: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  FireplacesTotal: {parent: 'Building'}
  FireplaceYN: {parent: 'Building'}
  Flooring: {parent: 'Building'}
  FoundationDetails: {parent: 'Building'}
  HeatingType: {parent: 'Building'}
  HeatingFuel: {parent: 'Building'}
  LotFeatures: {parent: 'Building'}
  NumberOfBuildings: {parent: 'Building'}
  NumberOfUnitsTotal: {parent: 'Building'}
  PropertyAttachedYN: {parent: 'Building'}
  PropertyCondition: {parent: 'Building'}
  Roof: {parent: 'Building'}
  Type: {parent: 'Building'}
  BasementFeatures: {parent: 'Basement'}
  BasementType: {parent: 'Basement'}
  Electric: {parent: 'Utilities', isTrans: true}
  OtherEquipment: {parent: 'Utilities'}
  UtilitiesAvailable: {parent: 'Utilities'}
  WaterBodyName: {parent: 'Utilities'}
  WaterfrontFeatures: {parent: 'Utilities'}
  WaterSource: {parent: 'Utilities'}
  AmmenitiesNearBy: {parent: 'Surrounding', isTrans: true}
  CommunityFeatures: {parent: 'Surrounding'}
  ExteriorFeatures: {parent: 'Surrounding'}
  LocationDescription: {parent: 'Surrounding'}
  RoadSurfaceType: {parent: 'Surrounding'}
  RoadType: {parent: 'Surrounding'}
  ViewType: {parent: 'Surrounding'}
  Zoning: {parent: 'Surrounding'}
  ZoningDescription: {parent: 'Surrounding'}
  ZoningType: {parent: 'Surrounding'}
  AccessibilityFeatures: {parent: 'Other'}
  AssociationFee: {parent: 'Other'}
  AssociationFeeFrequency: {parent: 'Other', isTrans: true}
  AssociationFeeIncludes: {parent: 'Other', isTrans: true}
  AssociationName: {parent: 'Other'}
  CommunicationType: {parent: 'Other'}
  Crop: {parent: 'Other'}
  FarmType: {parent: 'Other'}
  Features: {parent: 'Other'}
}

###*
 # EDM扩展字段映射表
 # @type {Object}
###
EDM_EXT_FIELDS = {
  Appliances: {parent: 'Building', isTrans: true}
  ArchitecturalStyle: {parent: 'Building'}
  Amenities: {parent: 'Building', isTrans: true}
  FireplacePresent: {parent: 'Building'}
  BedroomsAboveGround: {parent: 'Building'}
  HeatingType: {parent: 'Building'}
  HeatingFuel: {parent: 'Building'}
  AboveGradeFinishedArea: {parent: 'Building'}
  AboveGradeFinishedAreaUnits: {parent: 'Building'}
  BelowGradeFinishedArea: {parent: 'Building'}
  BelowGradeFinishedAreaUnits: {parent: 'Building'}
  BuildingType: {parent: 'Building'}
  FireplaceFeatures: {parent: 'Building'}
  Flooring: {parent: 'Building'}
  HalfBaths: {parent: 'Building'}
  FullBaths: {parent: 'Building'}
  HomeWarrantyYN: {parent: 'Building'}
  IrrigationWaterRightsYN: {parent: 'Building'}
  dir_desc: {parent: 'Building'}
  LivingAreaUnits: {parent: 'Building'}
  Roof: {parent: 'Building'}
  NumberofStories: {parent: 'Building'}
  CommunityFeatures: {parent: 'Surrounding'}
  ExteriorFeatures: {parent: 'Surrounding'}
  ElementarySchool: {parent: 'Surrounding'}
  MiddleOrJuniorSchool: {parent: 'Surrounding'}
  HighSchool: {parent: 'Surrounding'}
  RoadType: {parent: 'Surrounding'}
  Zoning: {parent: 'Surrounding'}
  AddressSuiteSearch: {parent: 'Other'}
  AssociationFee: {parent: 'Other'}
  AssociationFeeFrequency: {parent: 'Other', isTrans: true}
  AssociationFeeIncludes: {parent: 'Other', isTrans: true}
  InteriorFeatures: {parent: 'Other'}
  StandardStatus: {parent: 'Other', isTrans: true}
  LotSizeAcres: {parent: 'Land'}
  LotSizeArea: {parent: 'Land'}
  LotSizeDimensions: {parent: 'Land'}
  ParkingFeatures: {parent: 'Parking'}
}

###*
 # RAHB房源字段映射表
 # @type {Object}
###
RAHB_TRANS_MAP =
  BusinessType: 1

###*
 # if bool 1/0 -> Yes/No
 # 布尔类型字段列表
 # @type {Array<string>}
###
boolFields = ['den','blcny','grdn','gym','pool','fpop','lkr','fpl','htb','wifi',\
  'tv','wsr','dyr','frdg','utl','hydro_inc','water_inc','smok','pets','movn', 'frnshd']

###*
 # 可用字段列表
 # @type {Array<string>}
###
availableFields = ['ptp','gatp','ac','heat','pstyl','lpunt', 'fce', 'entr',\
 'wrmtp','kchntp','lndrytp','ptype'] # 'city', 'cmty',

###*
 # 数组类型字段列表
 # @type {Array<string>}
###
arrayFields = ['ptype2', 'saletp']

###*
 # 数组或字符串类型的缩写字段列表
 # @type {Array<string>}
###
arrOrStrAbFields = ['bsmt']
# if stp -> stp_en
# stp, status -> condition in angular
# ptp, pstyl  -> share lang select
# following fields need en when sharing or exMap
needEngFields = ['stp', 'status', 'ptp', 'pstyl', 'cmty','city','cnty','prov','ptype']

###*
 # 处理布尔类型字段
 # @param {Function} fn_ - 翻译函数
 # @param {*} v - 待处理的值
 # @param {string} k - 字段名
 # @returns {string} 处理后的字符串
###
process_boolean_field = (fn_, v, k) ->
  if isNaN parseInt(v)
    return v
  return if parseInt(v) is 1 then fn_('Yes',k) else fn_('No',k)

###*
 # 处理需要英文版本的字段
 # @param {Function} fn_ - 翻译函数
 # @param {*} v - 待处理的值
 # @param {string} k - 字段名
 # @returns {Object} 处理后的对象
###
process_need_eng_field = (fn_, v, k) ->
  if (k is 'cmty') or (not v)
    return v
  # city字段特殊处理
  if k is 'city'
    if shouldTransCity(v)
      return fn_(v, null, k)
    else
      return v
  return fn_(v, null, k)

###*
 # 处理房间信息
 # @param {Function} l10n - 本地化函数
 # @param {Array} rooms - 房间数组
 # @returns {Array} 处理后的房间数组
###
process_rooms = (l10n, rooms) ->
  return [] unless Array.isArray(rooms)
  rmlTmp = []
  for rm in rooms
    rm.t = l10n(rm.t,'rms') if rm.t
    rm.l = l10n(rm.l,'rms') if rm.l
    rmlTmp.push rm
  return rmlTmp

###*
 # 处理销售类型状态
 # @param {Object} prop - 房源对象
 # @returns {string} 处理后的状态
###
process_sale_status = (prop) ->
  return null unless prop.saletp
  
  if prop.status is 'A'
    return if Array.isArray(prop.saletp) then prop.saletp.join(' ') else prop.saletp
    
  if prop.status isnt 'A' and ((prop.lst in ['Sld','Pnd','Cld','Lsd']) or prop.sp)
    isSale = saletpIsSale prop.saletp
    if isSale
      return 'sold'
    else if (not isSale) and /Lsd/.test prop.lst
      return 'leased'
      
  return 'inactive'

###*
 # 处理DDF源数据
 # @param {Object} prop - 房源对象
 # @param {boolean} transDDF - 是否翻译DDF数据
 # @param {Function} _ab - 缩写翻译函数
 # @returns {Object} 处理后的翻译函数对象
###
process_ddf_source = (prop, transDDF, _ab) ->
  if not isDDFProp(prop)
    return null

  fn_ab = (a,b) -> _ab(a,b,true)
  if transDDF
    fn_ = (a,b,x) -> if x in needEngFields then _ab(a,b) else a
  else
    fn_ = (a,b) -> a
  return {fn_, fn_ab}

###*
 # 处理日期字段
 # @param {Date} date - 日期对象
 # @returns {string} 格式化后的日期字符串
###
process_date_field = (date) ->
  return date unless date instanceof Date
  return helpers.dateFormat date, 'YYYY-MM-DD'

###*
 # 检查并翻译数据
 # @param {string} v - 需要翻译的文本
 # @param {Function} fn - 翻译函数
 # @returns {string} - 翻译后的文本
###
module.exports.checkAndTranslate = checkAndTranslate = (fn, val, ctx) ->
  # 如果文本为空或不是字符串类型，直接返回原值
  return val if (not val) or ('string' isnt typeof val)
  
  # 检查文本是否包含英文字母
  hasEnglishLetters = /[a-zA-Z]/.test(val)
  
  # 如果包含英文字母则翻译，否则返回原文
  if hasEnglishLetters
    return fn(val, ctx)
  else
    return val


###*
 # 处理BCRE数组数据的翻译
 # @param {string} k - 键名
 # @param {Array} v - 待翻译的数组
 # @param {Function} fn_ - 翻译函数
 # @returns {string} - 翻译后的字符串
###
bcreTransArray = (k,v=[],fn_)->
  arr = []
  for vv in v
    if 'object' is typeof vv
      for vv_k,vv_v of vv
        if vv_k in ['Name','name']
          arr.push checkAndTranslate(fn_, vv_v, 'prop')
    else
      if BCRE_TRANS_MAP[k] is 1
        arr.push checkAndTranslate(fn_, vv, 'prop')
      else
        arr.push vv
  return arr.join(',')

###*
 # 处理yn字段的值,可能存在true/false,1/0,Yes/No,Y/N,等
 # @param {Function} fn_ - 翻译函数
 # @param {*} v - 处理前的值
 # @returns {string} 处理后的值
###
process_yn_value = (fn_,v) ->
  if isNaN parseInt(v)
    return if (v is true) or (/^(y|yes)$/i.test v) then fn_('Yes') else fn_('No')
  else
    return if parseInt(v) is 1 then fn_('Yes') else fn_('No')

###*
 # 处理BCRE特有的字段名转换
 # @param {string} k - 字段名
 # @returns {string} 处理后的字段名
###
process_bcre_field_name = (k) ->
  # 使用通用函数处理YN后缀
  k = process_field_name(k)
  
  # BCRE特有的处理：大写到驼峰的转换
  for upper,camel of BCRE_KEY_UPPER_TO_CAMEL
    k = k.replace(upper,camel)
    
  # BCRE特有的处理：缩写到全称的转换
  splitKey = splitFieldsKey(k).split(' ')
  for i in [0..splitKey.length]
    if val = BC_KEY_ABBR_TO_FULL[splitKey[i]]
      splitKey[i] = val
  # NOTE: 在函数construct_loop_val中会对key进行再次split,这里不额外split
  return splitKey.join('')

###*
 # 处理BCRE特有的值转换
 # @param {string} k - 字段名
 # @param {*} v - 字段值
 # @param {Function} fn_ - 翻译函数
 # @returns {*} 处理后的值
###
process_bcre_value = (k, v, fn_, isTrans) ->
  if Array.isArray(v)
    return bcreTransArray(k, v, fn_)
  else if BCRE_TRANS_MAP[k] is 1
    return checkAndTranslate(fn_, v, 'prop')
  else
    return process_value(fn_, v, k, isTrans)

###*
 # 构建BCRE显示数据
###
construct_bcre_display = ({l10n, fn_, fn_ab}, prop, ret) ->
  # 处理所有权类型
  process_ownership_type({l10n, fn_}, prop, ret)

  # 处理Building字段
  process_building_info(l10n, fn_, prop, ret, BCRE_EXT_FIELDS)

  # 处理Land字段
  process_land_info(l10n, fn_, prop, ret, BCRE_EXT_FIELDS)

  # 处理Utilities字段
  process_utilities(l10n, fn_, prop, ret, BCRE_EXT_FIELDS)

  # 处理字段映射
  for k, fieldConfig of BCRE_EXT_FIELDS
    continue unless v = prop[k]
    delete ret[k] # NOTE: 该字段会处理到其它字段中,删除原有字段
    
    # 处理字段名
    processedKey = process_bcre_field_name(k)

    # 处理字段值
    if k.endsWith('YN')
      processedValue = process_yn_value(fn_, v)
    else
      processedValue = process_bcre_value(processedKey, v, fn_, fieldConfig.isTrans)
    
    # 构建循环值对象
    loopVal = construct_loop_val(l10n, processedKey, processedValue, fieldConfig.parent)
    
    # 添加到结果对象
    ret[fieldConfig.parent] ?= {n: l10n(splitFieldsKey(fieldConfig.parent)), v: []}
    if Array.isArray(ret[fieldConfig.parent])
      ret[fieldConfig.parent] = {n: l10n(splitFieldsKey(fieldConfig.parent)), v: []}
    ret[fieldConfig.parent].v?.push loopVal

###*
 # 处理字段名
 # @param {string} k - 字段名
 # @returns {string} 处理后的字段名
###
process_field_name = (k) ->
  if k.endsWith('YN')
    return k.slice(0,-2)
  return k

###*
 # 构建循环值对象
 # @param {Function} l10n - 本地化函数
 # @param {string} k - 字段名
 # @param {*} v - 值
 # @param {string} parent - 父级分类
 # @param {boolean} isTrans - 是否需要翻译
 # @returns {Object} 循环值对象
###
construct_loop_val = (l10n, k, v, parent) ->
  splitKey = splitFieldsKey(k)
  if parent in ['Surrounding', 'Other']
    return {
      n: l10n(splitKey, k)
      v
    }
  else
    return {
      t: l10n(splitKey, k)
      m: v
    }

###*
 # 处理所有权类型
 # @param {Object} opt - 选项对象
 # @param {Object} prop - 属性对象
 # @param {Object} ret - 返回对象
###
process_ownership_type = ({l10n, fn_}, prop, ret) ->
  if prop.OwnershipType
    ret.OwnershipType = {
      n: l10n('Ownership Type', 'OwnershipType')
      v: fn_(prop.OwnershipType)
    }
    if Array.isArray(ret.OwnershipType.v)
      ret.OwnershipType.v = ret.OwnershipType.v.join ','

###*
 # 构建基础显示数据
 # @param {Object} opt - 选项对象
 # @param {Object} prop - 属性对象
 # @param {Object} ret - 返回对象
 # @param {Object} fields - 字段映射表
 # @param {Object} keyMap - 键名映射表(可选)
###
construct_base_display = ({l10n, fn_, fn_ab}, prop, ret, fields, keyMap = {}) ->
  # 处理所有权类型
  process_ownership_type({l10n, fn_}, prop, ret)

  # 处理Building字段
  process_building_info(l10n, fn_, prop, ret, fields)

  # 处理Land字段
  process_land_info(l10n, fn_, prop, ret, fields)

  # 处理Utilities字段
  process_utilities(l10n, fn_, prop, ret, fields)

  # 处理字段映射
  for k, fieldConfig of fields
    continue unless v = prop[k]
    processedKey = process_field_name(k)
    if keyMap[processedKey]
      processedKey = keyMap[processedKey]
    # 处理字段值
    if k.endsWith('YN')
      processedValue = process_yn_value(fn_, v)
    else
      processedValue = process_value(fn_, v, processedKey, fieldConfig.isTrans)
    loopVal = construct_loop_val(l10n, processedKey, processedValue, fieldConfig.parent)
    
    ret[fieldConfig.parent] ?= {n: l10n(splitFieldsKey(fieldConfig.parent)), v: []}
    ret[fieldConfig.parent].v?.push loopVal

###*
 # 构建DDF显示数据
###
construct_ddf_display = (opt, prop, ret) ->
  construct_base_display(opt, prop, ret, DDF_EXT_FIELDS)

###*
 # 构建RAHB显示数据
###
construct_rahb_display = ({l10n, fn_, fn_ab}, prop, ret) ->
  for k of RAHB_TRANS_MAP
    continue unless v = prop[k]
    delete ret[k]
    splitKey = splitFieldsKey(k)
    loopVal = {
      n: l10n(splitKey, k)
      v: process_value(fn_, v, k, RAHB_TRANS_MAP[k] is 1)
    }
    ret[k] = loopVal

###*
 # 构建OREB/CREB显示数据
###
construct_oreb_creb_display = ({l10n, fn_}, prop, ret) ->
  fields = if prop.src is 'OTW' then OREB_EXT_FIELDS else CREB_EXT_FIELDS
  construct_base_display({l10n, fn_}, prop, ret, fields)

###*
 # 构建EDM显示数据
###
construct_edm_display = ({l10n, fn_, fn_ab}, prop, ret) ->
  construct_base_display({l10n, fn_, fn_ab}, prop, ret, EDM_EXT_FIELDS, EDM_CAR_KEY_ABBR_TO_FULL)

###*
 # 构建CAR显示数据
###
construct_car_display = ({l10n, fn_, fn_ab}, prop, ret) ->
  construct_base_display({l10n, fn_, fn_ab}, prop, ret, CAR_EXT_FIELDS, EDM_CAR_KEY_ABBR_TO_FULL)

###*
 # 构建RESO/TREB显示数据
###
construct_reso_treb_display = ({l10n, fn_}, prop, ret) ->
  construct_base_display({l10n, fn_}, prop, ret, RESO_TREB_EXT_FIELDS)
  
  # 处理维护费用包含项目
  if Array.isArray(prop.mfeeInc) and prop.mfeeInc.length > 0
    loopVal = {
      n: l10n('Maintenance Fee Type', 'MaintenanceFeeType')
      v: []
    }
    for item in prop.mfeeInc
      if item? and /Included/i.test(item)
        tmpV = item.replace(/\s*Included\s*/i, '').trim()
        if tmpV
          loopVal.v.push(fn_(tmpV))
    if loopVal.v.length > 0
      ret.MaintenanceFeeType = loopVal

###*
 # 处理Utilities字段
 # @param {Function} l10n - 本地化函数
 # @param {Function} fn_ - 翻译函数
 # @param {Object} prop - 属性对象
 # @param {Object} ret - 返回对象
 # @param {Object} fieldMap - 字段映射表
###
process_utilities = (l10n, fn_, prop, ret, fieldMap) ->
  parent = 'Utilities'
  return unless prop[parent]
  
  # 删除ret中的Utilities字段
  delete ret[parent]
  
  # 如果Utilities不是数组，直接返回
  return unless Array.isArray(prop[parent])
  
  # 检查数组元素格式
  for item in prop[parent]
    # 如果元素是字符串，忽略该字段
    if typeof item is 'string'
      continue
      
    # 如果元素是对象且包含t和m属性
    if typeof item is 'object' and item.t? and item.m?
      # 检查字段是否需要翻译
      fieldConfig = fieldMap?[item.t]
      shouldTranslate = fieldConfig?.isTrans is true
      
      # 处理字段名和值
      processedKey = item.t
      processedValue = process_value(fn_, item.m, processedKey, shouldTranslate)
      
      # 构建循环值对象
      loopVal = construct_loop_val(l10n, processedKey, processedValue, parent)
      
      # 添加到结果对象
      ret[parent] ?= {n: l10n(splitFieldsKey(parent)), v: []}
      ret[parent].v?.push loopVal

###*
 # 处理建筑信息
 # @param {Function} l10n - 本地化函数
 # @param {Function} fn_ - 翻译函数
 # @param {Object} prop - 属性对象
 # @param {Object} ret - 返回对象
 # @param {Object} fieldMap - 字段映射表
###
process_building_info = (l10n, fn_, prop, ret, fieldMap) ->
  parent = 'Building'
  return unless prop[parent]
  
  # 删除ret中的Building字段
  delete ret[parent]
  
  # 如果Building不是对象，直接返回
  return unless typeof prop[parent] is 'object'
  
  # 处理每个建筑信息字段
  for building_info, v of prop[parent]
    k = building_info
    if k in EDM_CAR_KEY_ABBR_TO_FULL
      k = EDM_CAR_KEY_ABBR_TO_FULL[k]
    
    # 检查字段是否需要翻译
    fieldConfig = fieldMap?[k]
    shouldTranslate = fieldConfig?.isTrans is true
    
    # 处理字段值
    processedValue = process_value(fn_, v, k, shouldTranslate)
    
    # 构建循环值对象
    loopVal = construct_loop_val(l10n, k, processedValue, parent)
    
    # 添加到结果对象
    ret[parent] ?= {n: l10n(splitFieldsKey(parent)), v: []}
    ret[parent].v?.push loopVal

###*
 # 处理土地信息
 # @param {Function} l10n - 本地化函数
 # @param {Function} fn_ - 翻译函数
 # @param {Object} prop - 属性对象
 # @param {Object} ret - 返回对象
 # @param {Object} fieldMap - 字段映射表
###
process_land_info = (l10n, fn_, prop, ret, fieldMap) ->
  parent = 'Land'
  return unless prop[parent]
  
  # 删除ret中的Land字段
  delete ret[parent]
  
  # 如果Land不是对象，直接返回
  return unless typeof prop[parent] is 'object'
  
  # 处理每个土地信息字段
  for land_info, v of prop[parent]
    k = land_info
    
    # 检查字段是否需要翻译
    fieldConfig = fieldMap?[k]
    shouldTranslate = fieldConfig?.isTrans is true
    
    # 处理字段值
    processedValue = process_value(fn_, v, k, shouldTranslate)
    
    # 构建循环值对象
    loopVal = construct_loop_val(l10n, k, processedValue, parent)
    
    # 添加到结果对象
    ret[parent] ?= {n: l10n(splitFieldsKey(parent)), v: []}
    ret[parent].v?.push loopVal

###*
 # 处理值
 # @param {Function} fn_ - 翻译函数
 # @param {*} v - 值
 # @param {string} k - 键名
 # @param {boolean} isTrans - 是否需要翻译
 # @returns {*} 处理后的值
###
process_value = (fn_, v, k, isTrans = false) ->
  if Array.isArray(v)
    tmpV = []
    for vv in v
      if isTrans
        tmpV.push checkAndTranslate(fn_, vv, 'prop')
      else
        tmpV.push vv
    return tmpV.join(',')
  else if 'string' is typeof v and not /URL/.test(k)
    if isTrans
      return checkAndTranslate(fn_, v, 'prop')
    return v
  else if (v is true) or (v is false) or (/^(y|yes|n|no)$/i.test v)
    if isTrans
      return process_yn_value(fn_, v)
    return v
  else
    return v

###*
 # 处理数组字段
 # @param {Function} fn_ - 翻译函数
 # @param {*} v - 字段值
 # @param {string} k - 字段名
 # @param {Object} ret - 返回对象
 # @param {Object} _orig - 原始值对象
 # @returns {void}
###
process_array_fields = (fn_, v, k, ret, _orig) ->
  return unless v
  unless Array.isArray v
    v = v.split(',')
  tmp = []
  for i in v
    tmp.push fn_ i
  ret[k+'_en'] = v
  _orig[k] = v
  ret[k] = tmp
  if k is 'saletp'
    unless ret.stp
      ret.stp_en = v[0]
      _orig[k] = v[0]
      ret.stp = fn_ v[0]

###*
 # 翻译房源详细信息
 # @param {Object} opt - 选项对象
 # @param {string} opt.locale - 语言环境
 # @param {boolean} opt.transDDF - 是否翻译DDF数据
 # @param {Object} prop - 房源数据
 # @returns {Object} 翻译后的房源数据
###
module.exports.translate_rmprop_detail = translate_rmprop_detail = ({locale,transDDF}, prop)->
  return prop unless prop
  
  # 初始化翻译器
  {_ab,l10n} = getTranslator(locale)
  fn_ = (a,b)-> l10n(a,b)
  fn_ab = (a,b)->
    if Array.isArray a
      ret = []
      for i in a
        ret.push _ab(i,b)
      return ret.join(', ')
    return _ab(a,b)
    
  # 处理DDF源数据
  ddfSource = process_ddf_source(prop, transDDF, _ab)
  if ddfSource
    fn_ = ddfSource.fn_
    fn_ab = ddfSource.fn_ab
    noTranslate = true
  else
    noTranslate = false

  # 处理销售类型状态
  if prop.saletp
    prop.stplabel = process_sale_status(prop)

  ret = {}
  _orig = {}
  
  # 处理各种字段
  for k, v of prop
    # 处理日期字段
    if k is 'psn'
      ret[k] = process_date_field(v)
      continue
      
    # 处理房间信息
    if k is 'rms'
      ret['rms'] = process_rooms(l10n, v)
      continue
      
    # 处理特殊字段
    if k is 'rmPsn'
      if noTranslate
        ret[k] = v
        continue
      ret[k] = fn_ab v,'possDay'
      ret[k+'_en'] = v
      _orig[k] = v
      continue
      
    # 处理数组或字符串缩写字段
    if k in arrOrStrAbFields
      continue unless v
      unless Array.isArray v
        v = v.split(',')
      tmp = []
      for i in v
        tmp.push fn_ab i, k
      ret[k] = tmp
      continue
      
    # 处理需要英文版本的字段
    if k in needEngFields
      ret[k] = process_need_eng_field(fn_, v, k)
      ret[k + '_en'] = v
      _orig[k] = v
      continue
      
    # 处理特殊字段
    if k in ['rtp','rgdr']
      ret[k] = fn_ab v, k
      ret[k + '_en'] = v
      _orig[k] = v
      continue
      
    # 处理布尔字段
    if k in boolFields
      ret[k] = process_boolean_field(fn_, v, k)
      continue
      
    # 处理可用字段
    if k in availableFields
      if v
        ret[k] = fn_ v, k
        # ac字段特殊处理
        if k is 'ac' and Array.isArray(v)
          ret[k] = v.join(',')
      continue
      
    # 处理数组字段
    if k in arrayFields
      process_array_fields(fn_, v, k, ret, _orig)
      continue
      
    # 处理中文字段
    if k is 'm_zh'
      ret.m_zh = i18n.JTFTAUTO v,locale
      continue
      
    # 处理其他字段
    ret[k] = v

  # 构建不同来源的显示数据
  ddfOpt = {l10n,fn_, fn_ab, noTranslate}
  construct_ddf_display(ddfOpt, prop, ret) if prop.ddfID
  construct_bcre_display(ddfOpt, prop, ret) if prop.src is 'BRE'
  construct_rahb_display(ddfOpt, prop, ret) if prop.src is 'RHB'
  if prop.src in ['OTW','CLG']
    construct_oreb_creb_display(ddfOpt, prop, ret)
  construct_edm_display(ddfOpt, prop, ret) if prop.src is 'EDM'
  construct_car_display(ddfOpt, prop, ret) if prop.src is 'CAR'
  construct_reso_treb_display(ddfOpt, prop, ret) if prop.src is 'TRB'

  if Object.keys(_orig).length
    ret['_orig'] = _orig
    
  return ret

###*
 # 翻译房源列表
 # @param {Object} req - 请求对象
 # @param {Array} list - 房源列表
 # @returns {Array} 翻译后的房源列表
###
module.exports.translate_rmprop_list = (req, list)->
  ret = []
  return ret unless Array.isArray list
  for p in list
    tmp = translate_rmprop_detail({locale:req.locale(),transDDF:req.getConfig('transDDF')}, p)
    ret.push tmp
  return ret

###*
 # 获取推荐类型
 # @param {Object} r - 房源对象
 # @param {Object} type - 类型对象
 # @returns {string} 推荐类型
###
getRcmdType = (r,type)->
  if /^RM/.test r.id
    if r.topup_pts and (r.topTs > new Date())
      return type.ad
    else
      return null
  ret = type.new
  if r.priceChange
    ret = type.pc
  else if r.topup_pts
    ret = type.ad
  ret

###*
 # 翻译房源列表
 # @param {Object} req - 请求对象
 # @param {Array} l - 房源列表
 # @param {Object} type - 类型对象
 # @returns {Array} 翻译后的房源列表
###
module.exports.translate_prop_list = translate_prop_list = (req, l, type={})->
  req.setupL10n() unless req._ab and req._
  ret = []
  map = {}
  # translateRmPropDetailOpt = {_:req._,_ab:req._ab,l10n:req.l10n,locale:req.locale,headers:req.headers,cookies:req.cookies}
  for r in l
    unless r
      continue
    id = r._id.toString()
    if map[id]
      continue
    map[id] = 1
    tmp = translate_rmprop_detail({locale:req.locale(),transDDF:req.getConfig('transDDF')}, r)
    tmp.type = getRcmdType(r,type)
    ret.push tmp
  return ret

# 处理显示城市信息
# 因为TREB OREB数据的结构，城市的部分需要同时显示origCity/city_en/area。两个内容是一致的，只显示一遍。如果有包含关系，只显示更全的字段
###*
 # 获取格式化的城市名称
 # @param {Object} prop - 房源对象
 # @param {string} locale - 语言环境
 # @returns {string} 格式化后的城市名称
###
module.exports.getFormatCityName = getFormatCityName = (prop, locale) ->
  {_ab,l10n} = getTranslator(locale)
  cityMap = {}
  cities = [prop.origCity, prop.city_en, prop.area].filter(Boolean) # 过滤掉空值
  uniqueCities = new Set()

  for city in cities
    cityLower = city.toLowerCase().replace(/[^\w\s]/g, '') # 转为小写并去除特殊字符
    uniqueCities.add(cityLower)
    cityMap[cityLower] = city

  # 检查唯一性
  if uniqueCities.size == 1
    city = cityMap[Array.from(uniqueCities)[0]]
    return if shouldTransCity(city) then l10n(city) else city # 只有一个唯一值

  # 处理包含关系
  uniqueCities = Array.from(uniqueCities)

  # 使用 isSubstring 判断城市名字是否为包含关系
  for cityKey in uniqueCities
    for otherCityKey in uniqueCities
      if (cityKey isnt otherCityKey) and (cityKey.length < otherCityKey.length) and isSubstring(cityKey, otherCityKey)
        delete cityMap[cityKey]

  # 如果没有包含关系，返回拼接的字符串
  return Object.values(cityMap).map((city) -> 
    if shouldTransCity(city) then l10n(city) else city
  ).join(', ')
