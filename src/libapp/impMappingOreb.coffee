debugHelper = require '../lib/debug'
propertiesHelper = require './properties'
{ objectStr2NumNClean } = require '../lib/helpers'
{ abbrExposure } = require '../lib/propAddress'
{ statusLstMapping,convertLotSize } = require './impCommon'

debug = debugHelper.getDebugger()

METER2FEET = 3.28084

orebFieldMap =
  AirConditioningDescription: 'ac'
  BedrsAboveGrade: 'bdrms'
  BedrsBelowGrade: 'br_plus'
  City: 'city'
  ClosePrice: 'sp'
  CloseDate: 'clsD' # 需要和BCRE的cmpD统一一个内部字段检索用,前端显示按各自的含义处理
  ConstructionDesc: 'constr'
  DOM: 'dom'
  DistrictNeighbourhoodCode: 'cmtycd'
  Latitude: 'lat'
  ListPrice: 'lp'
  Longitude: 'lng'
  LotSizeDepthFeet: 'depth'
  LotSizeFrontageFeet: 'front_ft'
  Municipality: 'city'
  NeighbourhoodName: 'cmty'
  OffMarketDate: 'offD'
  PostalCode: 'zip'
  PropertyTaxes: 'tax'
  PublicRemarks: 'm'
  SoldDate: 'sldd'
  SqFtTotal: 'sqft'
  StreetDirSuffix: 'st_dir'
  StreetName: 'st'
  StreetNumber: 'st_num'
  StreetNumberNumeric: 'st_num'
  StreetSuffix: 'st_sfx'
  TaxYear: 'taxyr'
  TotalBathrooms: 'tbthrms'
  NumberofEnsuiteBathrooms: 'bthrms'
  NumberofPartialBathrooms: 'bthhlf'
  TotalBedrooms: 'tbdrms'
  TotalParking: 'tgr'
  UnitNumber: 'unt'
  YearBuilt: 'bltYr'
  BasementDescription: 'bsmt'
  MLSNumber: 'sid'
  MatrixUniqueID: 'orgId'
  PetsAllowed: 'pets'
  ZoningDescription: 'zoning'
  CommissiontoSellingOff: 'comm'
  HeatDescription: 'heat'
  HeatingFuel: 'fuel'
  PhotoModificationTimestamp: 'phomt'
  ManagementCompany: 'prop_mgmt'
  CondoCorporationNumber: 'condo_corp'
  # MaintenanceExpense: 'mfee'
  # ManagementExpenses: 'mfee'
  CondoFee: 'mfee' # 和fred确认使用该值,其它价格信息放在概要中显示
  CondoFeeFrequency: 'mfeeunt'
  FrontingOn: 'fce'
  CoListOfficeName: 'rltr'
  ListOfficeName: 'rltr'
  FormalOfficeName1: 'rltr'
  FormalOfficeName2: 'rltr'
  NumberofGarageSpaces: 'park_spcs'
  PhotoCount: 'pho'
  TitleForm: 'OwnershipType'
  phoDlTs: 'phodl'
  MatrixModifiedDT: 'mt'

TYPEOFDWELLING_PTYPE2_MAP = {
  '1 1/2 Storey':['2-Storey','House'],
  '2 Storey':['2-Storey','House'],
  '3 Storey':['3-Storey','House'],
  '4 Plex':['Fourplex','House'],
  'Bungalow with Loft':['Bungalow', 'Loft'],
  'Bungalow(1 Storey)':['Bungalow'],
  'Double - Side-By-Side':['Townhouse'],
  'Duplex - Up&Down':['Duplex','House'],
  'Hi Ranch':['Hi Ranch','Other'],
  'Mobile':['Mobile','Other'],
  'Modular':['Other','Modular'],
  'One Level':['Other'],
  'Other':['Other'],
  'Split Level':['Split'],
  'Triplex':['House','Triplex']
}

PROPERTYSUBTYPE_PTYPE2_MAP = {
  'Apartment 5+ Units':['Apartment'],
  'Business':['Business'],
  'Commercial Land':['Land'],
  'Condo Parking':['Parking'],
  'Condominium':['Apartment'],
  'Farm':['Farm'],
  'Industrial':['Industrial'],
  'Institutional/Special Purpose':['Institutional'],
  'Lots/Acreage':['Other'],
  'Multifamily':['Other','Multifamily'],
  'Office':['Office'],
  'Residential':['Residential'],
  'Retail':['Retail'],
  'Rental':['Rental']
}

# NOTE: office,agent,openhouse等相关处理过的字段不进入properties(la,ohz,rms已经在rni中)
UNIMPORT_FIELDS_MAP =
  Brokerage: 1
  ListAgentMLSID: 1
  ListAgentMUI: 1
  ListOfficeMLSID: 1
  ListOfficeMUI: 1
  CoListAgentMLSID: 1
  CoListAgentMUI: 1
  CoListOfficeMLSID: 1
  CoListOfficeMUI: 1
  CoListOfficeName: 1
  CoListAgent: 1
  CoListOffice: 1
  FirmDesignation: 1
  ActiveOpenHouseCount: 1
  OpenHouseCount: 1
  OpenHousePublicCount: 1

# 字段mapping
doMapping = (origRecord)->
  record = Object.assign {}, origRecord
  record = objectStr2NumNClean(record, true)

  for k, mappedK of orebFieldMap
    if record.hasOwnProperty k
      if record[mappedK]? and (Array.isArray(record[mappedK])) and (Array.isArray(record[k]))
        record[mappedK] = record[mappedK].concat record[k]
      else
        record[mappedK] ?= record[k]
      delete record[k]

  return record

# saletp与lp/lpr
formatSaletp = (record)->
  if record.TransactionType
    if /lease|rent/i.test record.TransactionType
      isLease = true
    else if /sale/i.test record.TransactionType
      isLease = false
    else
      debug.error "OREB formatSaletp error,TransactionType:#{record.TransactionType}"
  else if record.PropertySubType is 'Rental'
    isLease = true
  else
    if record.lp? and (parseInt(record.lp) < propertiesHelper.minSalePrice)
      isLease = true
    # else
    #   isLease = false
 
  delete record.TransactionType
  if isLease
    record.lpr = record.lp
    record.saletp = ['Lease']
    delete record.lp
  else
    record.saletp = ['Sale']

# ptype,ptype2
# ptype2 -> StyleofDwelling,TypeofDwelling结合判断
formatPtype = (record) ->
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
  else if /Commercial/i.test(record.PropertyType)
    record.ptype = 'b'
  else
    record.ptype = 'o'
    record.ptype2 = [record.PropertyType]

  # TODO: ptype2要调整为两个字段,可以匹配到的放进去检索用，原始的要保存显示用
  record.ptype2 ?= []
  if record.TypeofDwelling
    if tpArr = TYPEOFDWELLING_PTYPE2_MAP[record.TypeofDwelling]
      record.ptype2 = record.ptype2.concat tpArr
    else
      record.ptype2.push record.TypeofDwelling

  if record.StyleofDwelling
    switch record.StyleofDwelling
      when 'Apartment'
        record.ptype2.push 'Apartment'
      when 'Detached'
        record.ptype2.push 'Detached'
      when 'Row Unit'
        record.ptype2 = record.ptype2.concat ['Townhouse','Attached']
      when 'Semi Detached'
        record.ptype2.push 'Semi-Detached'
      when 'Stacked'
        record.ptype2.push 'Stacked'
      else
        record.ptype2.push record.StyleofDwelling

  if record.PropertySubType
    if (ptype2Arr = PROPERTYSUBTYPE_PTYPE2_MAP[record.PropertySubType])
      record.ptype2 = record.ptype2.concat ptype2Arr
    else
      record.ptype2.push record.PropertySubType

  # 去重
  record.ptype2 = Array.from(new Set(record.ptype2))

  delete record.PropertyType
  delete record.PropertySubType
  delete record.TypeofDwelling
  delete record.StyleofDwelling

# status,lst
exports.formatStatusAndLst = formatStatusAndLst = (record)->
  if mappingInfo = statusLstMapping[record.Status]?['OTW']
    record.status = mappingInfo.status
    record.lst = mappingInfo.lst
  else
    debug.error "OREB formatStatusAndLst error,status:#{record.Status}"
  
  # NOTE: 保留MlsStatus字段用于前端显示
  record.MlsStatus = record.Status
  delete record.Status

# front_ft,depth,lotsz,lotsz_code,dim,lotszUt
formatLandSize = (record)->
  if (not record.front_ft) and (record.LotSizeFrontageMetres)
    record.front_ft = propertiesHelper.getFloat(record.LotSizeFrontageMetres) * METER2FEET
  if (not record.depth) and (record.LotSizeDepthMetres)
    record.depth = propertiesHelper.getFloat(record.LotSizeFrontageMetres) * METER2FEET

  if (record.front_ft and record.depth)
    record.lotsz_code = 'Feet'
    record.dim = "#{record.front_ft} * #{record.depth}"
    record.lotsz = propertiesHelper.getFloat(record.front_ft * record.depth)
  
  if record.lotsz?
    record.lotszUt = 'ft²'

exports.convertFromOreb = convertFromOreb = ({
  srcRecord,
  Boundary
}, cb) ->
  try
    ret = doMapping srcRecord
    formatSaletp ret
    formatPtype ret
    formatStatusAndLst ret
    formatLandSize ret
    ret.cnty  ?= 'CA'
    ret.prov  ?= 'ON'
    ret.rltr ?= ret.Brokerage?[0]?.Name or ret.la?.nm
    ret.fce = abbrExposure ret.fce
    ret.orgId = ret._id
    ret._id = 'OTW' + ret.sid
    ret.id = 'OTW' + ret.sid
    ret.src = 'OTW' # 如果更换了这个值，记得修改statusLstMapping
    for key of UNIMPORT_FIELDS_MAP
      delete ret[key]
  catch err
    return cb err

  debug.debug 'convert ret', ret
  cb null, ret