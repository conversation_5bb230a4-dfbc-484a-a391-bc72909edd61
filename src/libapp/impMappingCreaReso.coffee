debugHelper = require '../lib/debug'
propertiesHelper = require './properties'
{ inputToDateNum, formatUTC2EST } = require '../lib/helpers'
{ abbrExposure } = require '../lib/propAddress'
{ getCityAreaMap, getAreaType } = require './boundaryHelper'
{ formatMfee } = require './impFormat'
{ processFieldFromListToString } = require './impMappingEdm'
{ doMapping, formatDaddr, convertLotSize } = require './impCommon'
{ ddfKeysValues } = require './impMappingDdf'
AREA_TYPE_MAP = getAreaType()
debug = debugHelper.getDebugger()

# Constants for unit conversions
ACRES2SQFT = 43560 # 1 acre = 43,560 square feet
HECTARES2SQFT = 107639.104 # 1 hectare = 107,639.104 square feet
SQMETERS2SQFT = 10.7639 # 1 square meter = 10.7639 square feet


HEATING_FUELS = [
  'Electric', 'Natural Gas', 'Oil', 'Propane', 'Geo Thermal', 'Solar', 'Water', 'Wood'
]
HEATING_TYPES = [
  'Baseboard', 'Combination', 'Fan Coil', 'Forced Air-1', 'Forced Air-2', 'Gravity',
  'Heat Pump', 'Hot Water', 'In Floor Heat System', 'Wood Stove'
]

###
# @description 基础字段映射表
# @constant {Object}
###
creaResoFieldMap =
  AssociationName: 'condo_corp'
  BathroomsTotalInteger: 'tbthrms'
  BedroomsAboveGrade: 'bdrms'
  BedroomsBelowGrade: 'br_plus'
  BedroomsTotal: 'tbdrms'
  BuildingName: 'bldg_name'
  ConstructionMaterials: 'constr'
  Cooling: 'ac'
  Country: 'cnty'
  CrossStreet: 'crsst'
  CityRegion: 'cmty'
  DirectionFaces: 'fce'
  Exposure: 'fce'
  ExpirationDate: 'exp'
  ListingId: 'sid'
  LivingArea: 'sqft'
  StandardStatus: 'MlsStatus'
  PublicRemarks: 'm'
  PhotosCount: 'pho'
  PhotosChangeTimestamp: 'phomt'
  UnparsedAddress: 'addr'
  PostalCode: 'zip'
  StateOrProvince: 'prov'
  StreetName: 'st'
  StreetNumber: 'st_num'
  StreetSuffix: 'st_sfx'
  UnitNumber: 'unt'
  City: 'city'
  Latitude: 'lat'
  Longitude: 'lng'
  ParkingTotal: 'tgr'
  YearBuilt: 'bltYr'
  # LivingAreaUnits
  Basement: 'bsmt'
  Stories: 'lvl'
  Zoning: 'zoning'
  TaxAnnualAmount: 'tax'
  TaxYear: 'taxYr'
  ParcelNumber: 'parcel_id'
  CommonInterest:'OwnershipType' # keep same with old crea


###
# @description 需要转换为数字格式的日期字段列表
# @constant {Array<string>}
###
NUM_DATE_FIELDS = [
  'poss_date', 'sldd', 'onD', 'offD', 'ld', 'exp', 'picTs',
  'ListingContractDate', 'input_date', 'dt_ter', 'lud', 'unavail_dt', 'cd', 'cldd'
]

###
# @description 格式化时间戳字段
# @param {Object} record - 记录对象
###
formatTimestamp = (record)->
  if record.OriginalEntryTimestamp
    record.ts = new Date(record.OriginalEntryTimestamp)
    record.ld = record.OriginalEntryTimestamp
  record.lup = new Date(record.ModificationTimestamp) if record.ModificationTimestamp
  record.mt ?= record.lup
  for fld in NUM_DATE_FIELDS
    if record[fld]? and ('string' is typeof record[fld])
      record[fld] = inputToDateNum(record[fld])
  delete record.OriginalEntryTimestamp
  delete record.ModificationTimestamp


###
# @description 格式化状态和lst字段，已经在download中处理
# @param {Object} record - 记录对象
###
formatStatusAndLst = (record) ->
  return unless (orgStatus = record.status)
  if orgStatus is 'A'
    record.lst = 'New'
  else
    record.lst = 'Unknown'
    delete record.MlsStatus

###
# @description 格式化销售类型
# @param {Object} record - 记录对象
###
formatSaleType = (record) ->
  if (record.LeaseAmount>0) or (record.TotalActualRent>0)
    record.saletp = ['Lease']
    # Prefer LeaseAmount if available, fallback to TotalActualRent
    if record.LeaseAmount > 0
      record.lpr = record.LeaseAmount
      debug.debug "Using LeaseAmount for lpr: #{record.LeaseAmount}"
      delete record.LeaseAmount
    else
      record.lpr = record.TotalActualRent
      debug.debug "Using TotalActualRent for lpr: #{record.TotalActualRent}"
      delete record.TotalActualRent
    return
  lp = record.ListPrice
  if lp? and (lp > propertiesHelper.minSalePrice)
    record.saletp = ['Sale']
    record.lp = lp
  else
    record.saletp = ['Lease']
    record.lpr = lp
  delete record.ListPrice



ddfResoTypeMappingTable =
  PropertySubType:
    'Single Family':
      StructureType:
        'House':        ['cfr','Residential','House']
        'Duplex':       ['cfr','Residential',['House','Duplex']]
        'Fourplex':     ['cfr','Residential',['House','Fourplex']]
        'Triplex':      ['cfr','Residential',['House','Triplex']]
        'Two Apartment House':\
        ['cfr','Residential',['House','Two Apartment House']]
        'Apartment':    ['cr','Residential','Apartment']
        'Row / Townhouse': ['cfr','Residential',['Townhouse','Attached']]
        'Recreational': ['cfr','Residential',['Other','Recreational']]
        'Multi-Family': ['cfr','Residential',['Other','Multifamily']]
        'Garden Home':  ['cfr','Residential',['Other','Garden']]
        'Manufactured Home/Mobile':\
        ['cfr','Residential',['Other','Manufactured','Mobile']]
        'Manufacturing':['cfr','Residential',['Other','Manufacturing']]
        'Mobile Home':  ['cfr','Residential',['Other','Mobile']]
        'Other':        ['cfr','Residential','Other']
        'Modular':      ['cfr','Residential',['Other','Modular']]
        'Parking':      ['cfr','Other','Parking']
        'No Building':  ['cfr','Residential',['Other','No Building']]
        _:              ['cfr','Residential','Other']
      _:                ['cfr','Residential','Other']
    'Recreational':
      StructureType:
        'House':        ['cfr','Residential','House']
        'Row / Townhouse': ['cfr','Residential',['Townhouse','Attached']]
        _:              ['cfr','Other','Other']
      _:                ['cfr','Other','Other']
    'Multi-family':
      StructureType:
        'Duplex':       ['cfr','Residential',['House','Duplex']]
        'Fourplex':     ['cfr','Residential',['House','Fourplex']]
        'Triplex':      ['cfr','Residential',['House','Triplex']]
        'Row / Townhouse': ['cfr','Residential',['Townhouse','Attached']]
        'Apartment':    ['cr','Residential','Apartment']
        'Multi-Family': ['cfr','Residential',['Other','Multifamily']]
        'Other':        ['cfr','Other','Other']
        'Commercial Apartment':\
        ['br','Other',['Other','Commercial Apartment']]
        'Residential Commercial Mix':\
        ['cfr','Other',['Other','Residential Commercial Mix']]
        _:              ['cfrb','Other','Other']
      _:                ['cfrb','Other','Other']
    'Parking':          ['cfrb','Other','Parking']
    'Agriculture':      ['frb','Commercial','Farm']
    'Business':         ['b','Commercial','Business']
    'Retail':           ['b','Commercial','Retail']
    'Industrial':       ['b','Commercial','Industrial']
    'Vacant Land':      ['bcfr','Other','Land']
    'Office':           ['b','Commercial','Office']
    'Institutional - Special Purpose': ['b','Commercial','Institutional']
    'Hospitality':      ['b','Commercial','Hospitality']
    'Other':            ['bcfr','Other','Other']
    _:                  ['bcfr','Other','Other']


###
# @description 格式化房产类型，参考impMappingDdf
# @param {Object} record - 记录对象
###
formatPropertyType = (record) ->
  vals = ddfKeysValues record,ddfResoTypeMappingTable
  if /Residential/i.test(vals[1])
    record.ptype = 'r'
  else if /Commercial|Business/i.test(vals[1])
    record.ptype = 'b'
  else
    record.ptype = 'o'
  record.ptype2 = vals[2]
  


###
# @description 格式化la,coop信息
# @param {Object} record - 记录对象
###
formatListingAgent = (record) ->
  if record.ListOffice?.OfficeName
    record.rltr = record.ListOffice.OfficeName

  createLaOrCoop = (isLa)->
    result = []
    prefixes = if isLa then ['List','CoList'] else ['Buyer','CoBuyer']
    
    for prefix in prefixes
      office = record[prefix + 'Office']
      continue unless office?.OfficeName # 确保office存在且有必要字段
      
      object = {
        nm: office.OfficeName
        tel: office.OfficePhone
        id: office.OfficeKey # 添加备选key
        mlsId: office.OfficeMlsId
        fax: office.OfficeFax
        addr: office.OfficeAddress1 or office.OfficeAddress2
        city: office.OfficeCity
        prov: office.OfficeStateOrProvince
        zip: office.OfficePostalCode
        aor: office.OfficeAOR
      }

      if agent = record[prefix + 'Agent']
        object.agnt = [{
          id: agent.MemberMlsId
          nm: [agent.MemberFirstName, agent.MemberMiddleName, agent.MemberLastName].filter(Boolean).join(' ')
          tel: agent.MemberOfficePhone
          aor: agent.MemberAOR
        }]
      result.push(object)
    return result
  
  record.la = createLaOrCoop(true)
  record.coop = createLaOrCoop(false)
  # 清理空数组
  delete record.la if (record.la?.length is 0)
  delete record.coop if (record.coop?.length is 0)

###*
# Parses room dimensions from a string and returns width and length in feet
# @param {string} dimensionStr - The room dimension string (e.g. "8x7", "9'1" x 10'7"", "9 x 10")
# @returns {Object|null} Object containing width and length in feet, or null if parsing fails
###
parseRoomDimensions = (dimensionStr) ->
  return null unless dimensionStr
  dimensions = dimensionStr.split(/\s+/)[0]  # Get first part before any descriptive text
  
  # Try format: 8x7, 8x7.1, 9 x 10
  if match = dimensions.match(/^(\d+(?:\.\d+)?)\s*[xX]\s*(\d+(?:\.\d+)?)/)
    [_, width, length] = match
    return {
      width: parseFloat(width)
      length: parseFloat(length)
    }
  # Try format: 9'1" x 10'7"
  else if match = dimensions.match(/^(\d+)'(?:(\d+)"?)?\s*[xX]\s*(\d+)'(?:(\d+)"?)?/)
    [_, widthFeet, widthInches, lengthFeet, lengthInches] = match
    width = parseInt(widthFeet) + (parseInt(widthInches || 0) / 12)
    length = parseInt(lengthFeet) + (parseInt(lengthInches || 0) / 12)
    return {
      width: width
      length: length
    }
  
  return null

###
# @description 格式化房间信息
# @param {Object} record - 记录对象
###
formatRms = (record) ->
  return unless record?.Rooms?.length
  rms = []
  for room in record.Rooms
    continue unless room.RoomType
    
    rm = {
      t: room.RoomType
      l: room.RoomLevel
    }

    # Handle width and length from explicit fields
    if room.RoomWidth? and room.RoomLength?
      rm.w = room.RoomWidth
      rm.h = room.RoomLength
    # Try to parse from RoomDimensions if explicit fields not available
    else if room.RoomDimensions?
      if dimensions = parseRoomDimensions(room.RoomDimensions)
        rm.w = dimensions.width
        rm.h = dimensions.length

    rms.push(rm)
  
  record.rms = rms if rms.length

###
# @description 格式化开放参观信息
# @param {Object} record - 记录对象
###
formatOpenHouse = (record) ->
  return unless record?.OpenHouses?.length
  openHouseDetails = []
  for oh in record.OpenHouses
    continue unless (oh.OpenHouseDate and oh.OpenHouseStartTime and oh.OpenHouseEndTime)
    detail = {
      f: "#{oh.OpenHouseDate} #{oh.OpenHouseStartTime.slice(0,5)}"
      t: "#{oh.OpenHouseDate} #{oh.OpenHouseEndTime.slice(0,5)}"
      tp: oh.OpenHouseType
      action: oh.OpenHouseStatus is 'Active'
    }
    openHouseDetails.push(detail)
  record.ohz = openHouseDetails if openHouseDetails.length
  delete record.OpenHouses


###
# @description 获取城市和区域信息
# @param {Object} record - 记录对象
# @return {Object} 城市和区域信息
###
getCityAndArea = (record)->
  CountyOrParish = record.CountyOrParish
  City = record.City
  type = AREA_TYPE_MAP[CountyOrParish]
  if type is 'single-tiers'
    return {city:CountyOrParish, area:CountyOrParish, subCity:City}
  return {city:City, area:CountyOrParish}

###
# @description 格式化地址信息
# @param {Object} record - 记录对象
# @param {Object} srcRecord - 源记录对象
###
formatAddr = (record,srcRecord)->
  return unless record and srcRecord
  for i in ['st','zip','st_sfx','unt']
    if record[i] in ['NA','N/A']
      record[i] = ''
  record = Object.assign record,getCityAndArea(srcRecord)

###
# @description 格式化土地面积
# @param {Object} record - 记录对象
###
formatLandSize = (record) ->
  if record.LotSizeArea
    record.lotsz = propertiesHelper.getFloat(record.LotSizeArea)
    # Convert to square feet based on unit
    if /acres?/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz * ACRES2SQFT)
    else if /hectares?/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz * HECTARES2SQFT)
    else if /(square\s+)?meters?/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz * SQMETERS2SQFT)
    else if /(square\s+)?feet/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz)
    else
      debug.error "Unknown lot size unit: #{record.LotSizeUnits} for property #{record.ListingKey}"
      record.lotsz = convertLotSize(record.lotsz)
  
  if record.lotsz
    record.lotszUt = 'ft²'
    record.lotsz_code = 'Feet'
  record.dim = record.LotSizeDimensions

  delete record.LotSizeArea
  delete record.LotSizeUnits
  delete record.LotSizeDimensions


###
# @description 格式化加热细节
# @param {Object} record - 记录对象
###
formatHeatingDetails = (record) ->
  if not record.Heating
    delete record.Heating
    return [null,null]
  result = {HeatingFuel: [], HeatingType: []}
  for heating in record.Heating
    if heating in HEATING_FUELS
      result.HeatingFuel.push(heating)
    else if heating in HEATING_TYPES
      result.HeatingType.push(heating)
  fuel=processFieldFromListToString(result.HeatingFuel)
  heat=processFieldFromListToString(result.HeatingType)
  delete record.Heating
  return [fuel,heat]


formatHeating = (record) ->
  [fuel, heat] = formatHeatingDetails(record)
  if fuel
    record.HeatingFuel = fuel
  if heat
    record.HeatingType = heat
  delete record.Heating

###
# @description 清理记录中的临时字段
# @param {Object} record - 记录对象
# @return {Object} 清理后的记录
###
cleanRecord = (record) ->
  deletePatterns = [
    /^ListOffice.*/,
    /^ListAgent.*/,
    /^CoListOffice.*/,
    /^CoListAgent.*/,
    /^BuyerOffice.*/,
    /^BuyerAgent.*/,
    /^CoBuyerAgent.*/,
    /^CoBuyerOffice.*/,
    # NOTE: impFormat.formatMfee会转换,这里需要去除转换前的字段
    /^AssociationFee/,
    /^AssociationFeeFrequency/,
    /^AssociationFeeIncludes/,
    /^Rooms/,
    /^OpenHouse/,
    /InternetAddressDisplayYN/
  ]
  
  for fieldName of record
    for pattern in deletePatterns
      if pattern.test(fieldName)
        delete record[fieldName]
        break
  return record

###
# @description 转换CREA RESO格式的房源数据
# @param {Object} params - 参数对象
# @param {Object} params.srcRecord - 源记录
# @param {Function} cb - 回调函数
###
exports.convertFromCreaReso = convertFromCreaReso = ({srcRecord}, cb) ->
  return cb('Missing source record') unless srcRecord
  try
    ret = doMapping(srcRecord, creaResoFieldMap)
    # 处理必要的字段转换
    formatMfee(ret)
    formatTimestamp(ret)
    formatStatusAndLst(ret)
    formatSaleType(ret)
    formatPropertyType(ret)
    formatListingAgent(ret)
    formatRms(ret)
    formatOpenHouse(ret)
    formatLandSize(ret)
    formatAddr(ret,srcRecord)
    formatHeating(ret)
    formatDaddr(ret)
    
    # 设置默认值
    ret.cnty ?= 'CA'
    # Check if OriginatingSystemName contains 'toronto' to set prov
    if /toronto/i.test(srcRecord.OriginatingSystemName)
      ret.prov ?= 'ON'
    ret.id ?= 'DDF' + ret.ListingKey
    ret.src ?= 'DDF'
    ret.fce = abbrExposure ret.fce
    ret.ddfID = ret._id
    if ret.tbthrms?
      ret.bthrms ?= ret.tbthrms
    
    cleanRecord(ret)
  catch err
    debug.error 'CREA Reso convert error:', err
    return cb(err)
    
  debug.debug 'CREA Reso convert result:', ret
  cb(null, ret)
