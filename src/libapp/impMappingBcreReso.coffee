###
# @fileoverview BCRE RESO 数据格式转换模块
# @description 将 BCRE RESO 格式的房源数据转换为系统标准格式
# @module libapp/impMappingBcreReso
# @date 2025-01-04
###

debugHelper = require '../lib/debug'
STREET_ABBR_CA_EN = require('../lib/streetAbbr/CA_EN').STREET_ABBR
{ inputToDateNum, formatUTC2Vancouver} = require '../lib/helpers_date'
{ abbrExposure } = require '../lib/propAddress'
{ formatMfee } = require '../libapp/impFormat'
{ doMapping,formatDaddr,statusLstMapping,convertLotSize } = require './impCommon'
propertiesHelper = require './properties'
propertyTagHelper = require './propertyTagHelper'
debug = debugHelper.getDebugger()
{ getAreaType } = require '../libapp/boundaryHelper'
AREA_TYPE_MAP = getAreaType()

# 常量定义
ACRES2SQFT = 43560
HECTARES2SQFT = 107639.10417
METER2SQFT = 10.76391
FEET2METER = 0.3048

###
# @description BCRE RESO特定的PropertySubType到ptype2的映射
# @constant {Object}
###
PROPERTYSUBTYPE_PTYPE2_MAP = {
  'Apartment/Condo': ['Apartment']
  'Townhouse': ['Townhouse', 'Attached']
  'House/Single Family': ['House', 'Detached']
  'Single Family Residence': ['House', 'Detached']
  'Duplex': ['House', 'Duplex']
  'Half Duplex': ['House', 'Semi-Detached']
  'Triplex': ['House', 'Triplex']
  'Fourplex': ['House', 'Quadruplex']
  'Quadruplex': ['House', 'Quadruplex']
  'Mobile Home': ['Mobile Home']
  'Manufactured Home': ['House', 'Manufactured']
  'Manufactured On Land': ['Land', 'Manufactured']
  'Recreational': ['Other', 'Recreational']
  'Vacant Land': ['Other', 'Land']
  'Other': ['Other']
  'Row House (Non-Strata)': ['Townhouse']
}

###
# @description BCRE RESO字段到系统字段的映射
# @constant {Object}
###
bcreResoFieldMap =
  PhotosCount: 'pho'
  BCRES_KitchenTotal: 'kch'
  ListingId: 'sid'
  ModificationTimestamp: 'mt'
  ListPrice: 'lp'
  OriginalListPrice: 'olp'
  ClosePrice: 'sp'
  City: 'city'
  StateOrProvince: 'prov'
  PostalCode: 'zip'
  BedroomsTotal: 'tbdrms'
  BathroomsTotalInteger: 'tbthrms'
  BathroomsFull: 'bthrms'
  BathroomsHalf: 'bthhlf'
  LivingArea: 'sqft'
  ParkingTotal: 'tgr'
  YearBuilt: 'bltYr'
  PublicRemarks: 'm'
  PrivateRemarks: 'bm'
  Latitude: 'lat'
  Longitude: 'lng'
  VirtualTourURLBranded:'vturl'
  StreetName: 'st'
  StreetNumber: 'st_num'
  StreetSuffix: 'st_sfx'
  StreetDirPrefix: 'st_dir'
  UnparsedAddress: 'origAddr'
  # StatusChangeTimestamp: 'spcts'
  CloseDate: 'cldd'
  # BCRES_DirectionFacesRearYard: 'fce'
  MLSAreaMinor: 'cmty'
  TaxAnnualAmount: 'tax'
  BCRES_Age: 'age'
  WaterSource: 'water'
  PetsAllowed: 'pets'
  PoolFeatures: 'pool'
  Heating: 'heat'
  BuyerAgencyCompensation: 'comm'
  TaxYear: 'taxyr'
  DirectionFaces: 'fce'
  ConstructionMaterials: 'constr'
  FoundationDetails: 'constr'
  CoveredSpaces: 'gr'
  CarportSpaces: 'gr'
  GarageSpaces: 'gr'
  PurchaseContractDate: 'sldd'
  ListingContractDate: 'ld'
  Zoning: 'zoning'
  Basement: 'bsmt'
  FrontageLength: 'front_ft'
  BridgeModificationTimestamp: 'lup'
  Stories: 'lvl'
  ParcelNumber: 'parcel_id'
  OffMarketDate: 'offD'
  Country: 'cnty'
  UnitNumber: 'unt'
  ExpirationDate: 'exp'
  Ownership:'OwnershipType'
  # FeedTypes: 'bretp'

###
# @description 需要转换为数字格式的日期字段列表
# @constant {Array<string>}
###
NUM_DATE_FIELDS = [
  'poss_date', 'sldd', 'onD', 'offD', 'ld', 'exp', 'picTs',
  'ListingContractDate', 'input_date', 'dt_ter', 'lud', 'unavail_dt', 'cd', 'cldd'
]


###
# @description 格式化时间戳字段
# @param {Object} record - 记录对象
###
formatTimestamp = (record)->
  record.ts = new Date(record.ts) if record.ts
  record.lup = new Date(record.lup) if record.lup
  record.mt ?= record.lup
  for fld in NUM_DATE_FIELDS
    if record[fld]? and ('string' is typeof record[fld])
      record[fld] = inputToDateNum(record[fld])

###
# @description 格式化状态和lst字段
# @param {Object} record - 记录对象
###
exports.formatStatusAndLst = formatStatusAndLst = (record) ->
  return unless (orgStatus = record.MlsStatus or record.StandardStatus)
  mappingInfo = statusLstMapping[orgStatus]?['BRE']
  lst = mappingInfo?.lst or 'Unknown'
  status = mappingInfo?.status or 'U'
  if lst is 'Unknown'
    debug.error "RESO prop:#{record._id} formatStatusAndLst error, unknown Status:#{orgStatus}"
  else
    record.status = status
    record.lst = lst
  delete record.StandardStatus

###
# @description 格式化销售类型
# @param {Object} record - 记录对象
###
formatSaleType = (record) ->
  if /Sale/i.test(record.BCRES_SaleOrRent)
    record.saletp = ['Sale']
  else if /(Lease|Rent)/i.test(record.BCRES_SaleOrRent)
    record.saletp = ['Lease']
    record.lpr = record.lp if record.lp? and (not record.lpr?)
    delete record.lp
  else
    debug.error "RESO prop:#{record._id} formatSaleType error, unknown BCRES_SaleOrRent:#{record.BCRES_SaleOrRent}"
  delete record.BCRES_SaleOrRent

###
# @description 格式化房产类型
# @param {Object} record - 记录对象
###
formatPropertyType = (record) ->
  record.ptype2 ?= []
  if /Residential/i.test(record.PropertyType)
    record.ptype = 'r'
  else if /Commercial|Business/i.test(record.PropertyType)
    record.ptype = 'b'
  else
    record.ptype = 'o'
    record.ptype2.push record.PropertyType if record.PropertyType
  
  # ArchitecturalStyle = Bungalow, add to ptype2
  if (record.ptype is 'r') and (record.ArchitecturalStyle?.length)
    record.ptype2 = propertiesHelper.trebResidentialStyle2Ptype2 record.ArchitecturalStyle,record.ptype2
  
  if tpArr = PROPERTYSUBTYPE_PTYPE2_MAP[record.PropertySubType]
    record.ptype2 = record.ptype2.concat tpArr
  else
    record.ptype2.push record.PropertySubType

  record.ptype2 = record.ptype2.concat record.StructureType if record.StructureType?.length
  record.ptype2 = Array.from(new Set(record.ptype2))
  delete record.StructureType
  delete record.PropertySubType
  delete record.PropertyType
  delete record.ArchitecturalStyle

###
# @description 判断是否为图片文件
# @param {Object} media - 媒体对象
# @return {boolean} 是否为图片文件
###
isPicFile = (media={})->
  # 可能会存在其他类型,eg: application|pdf|audio|mpeg|word
  if /image|jpeg/.test media.MimeType
    return true
  false

###
# @description 从Media信息生成图片URL
# @param {Object} record - 记录对象
###
generatePhoUrls = (record) ->
  return unless record.Media?.length
  phoUrls = []
  for item,i in record.Media
    # ignore file media
    if not isPicFile(item)
      continue
    phoUrls.push({
      id: item.Order
      url: item.url or item.MediaURL
      # status: item.status
      # permission: item.perm or item.permission
      # w: item.w
      # h: item.h
      # m: item.m
    })
  if phoUrls.length
    record.phoUrls = phoUrls
    record.pho = phoUrls.length
  delete record.Media

###
# @description 格式化la,coop信息
# @param {Object} record - 记录对象
###
formatListingAgent = (record) ->
  if record.ListOffice?.OfficeName
    record.rltr = record.ListOffice.OfficeName

  createLaOrCoop = (isLa)->
    result = []
    prefixes = if isLa then ['List','CoList'] else ['Buyer','CoBuyer']
    
    for prefix in prefixes
      office = record[prefix + 'Office']
      continue unless office?.OfficeName # 确保office存在且有必要字段
      
      object = {
        nm: office.OfficeName
        tel: office.OfficePhone
        id: office.MainOfficeKey or office.OfficeKey # 添加备选key
        mlsId: office.MainOfficeMlsId or office.OfficeMlsId
        fax: office.OfficeFax
        addr: office.OfficeAddress1 or office.OfficeAddress2
        city: office.OfficeCity
        prov: office.OfficeStateOrProvince
        zip: office.OfficePostalCode
        eml: office.OfficeEmail
        aor: office.OfficeAOR
      }

      if agent = record[prefix + 'Agent']
        object.agnt = [{
          id: agent.MemberMlsId
          nm: agent.MemberFullName
          tel: agent.MemberMobilePhone
          eml: agent.MemberEmail
          aor: agent.MemberAOR
        }]
      result.push(object)
    return result
  
  record.la = createLaOrCoop(true)
  record.coop = createLaOrCoop(false)
  # 清理空数组
  delete record.la if (record.la?.length is 0)
  delete record.coop if (record.coop?.length is 0)
  
###
# @description 家具信息
# @constant {Object}
###
FURNISHED_MAP = {
  'Furnished': 1
  'Unfurnished': 0
  'Partially': 0.5
}
###
# @description 格式化家具信息
# @param {Object} record - 记录对象
###
formatFurnished = (record) ->
  return if not record.Furnished
  record.frnshd = FURNISHED_MAP[record.Furnished]
  delete record.Furnished


###
# @description 获取城市和区域信息
# @param {Object} record - 记录对象
# @return {Object} 城市和区域信息
###
getCityAndArea = (record)->
  CountyOrParish = record.CountyOrParish
  City = record.City
  type = AREA_TYPE_MAP[CountyOrParish]
  if type is 'single-tiers'
    return {city:CountyOrParish, area:CountyOrParish, subCity:City}
  return {city:City, area:CountyOrParish}

###
# @description 格式化地址信息
# @param {Object} record - 记录对象
# @param {Object} srcRecord - 源记录对象
###
formatAddr = (record,srcRecord)->
  for i in ['st','zip','st_sfx','unt']
    if record[i] in ['NA','N/A']
      record[i] = ''
  record = Object.assign record,getCityAndArea(srcRecord)

###
# @description 格式化土地面积
# @param {Object} record - 记录对象
###
formatLandSize = (record) ->
  if record.LotSizeSquareFeet
    record.lotsz = propertiesHelper.getFloat(record.LotSizeSquareFeet)
  else if record.LotSizeAcres
    record.lotsz = propertiesHelper.getFloat(record.LotSizeAcres) * ACRES2SQFT
  else if record.LotSizeArea
    record.lotsz = propertiesHelper.getFloat(record.LotSizeArea)
    if /Feet/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz)
    else if /Acre/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz * ACRES2SQFT)
    else if /(Metre|Meter)/i.test record.LotSizeUnits
      record.lotsz = propertiesHelper.getFloat(record.lotsz * METER2SQFT)
    else
      debug.error "Unknown lot size unit: #{record.LotSizeUnits} for property #{record.sid}"
      record.lotsz = convertLotSize(record.lotsz)
  
  if record.lotsz
    record.lotszUt = 'ft²'
    record.lotsz_code = 'Feet'
  record.dim = record.LotSizeDimensions
  
  delete record.LotSizeUnits
  delete record.LotSizeDimensions
  delete record.LotSizeSquareFeet
  delete record.LotSizeAcres
  delete record.LotSizeArea

###
# @description 格式化开放参观时间
# @param {string} dateString - 日期字符串
# @return {string} 格式化后的日期字符串
###
formatOpenHouseDateTime = (dateString) ->
  return unless dateString
  # UTC iso date string instead of local date time
  if /\dZ$/.test dateString
    return formatUTC2Vancouver(dateString)
  # Split the date and time components
  [datePart, timePart] = dateString.split 'T'
  return unless timePart
  # Split the time component to remove the milliseconds and timezone if any
  [time] = timePart.split '.'
  # Combine the date and time parts into the desired format
  "#{datePart} #{time.slice(0, 5)}"

###
# @description 格式化开放参观信息
# @param {Object} record - 记录对象
###
exports.formatOpenHouse = formatOpenHouse = (record) ->
  return unless record.openHouse?.length
  openHouseDetails = []
  for oh in record.openHouse
    if oh.BCRES_Timezone isnt 'Pacific Time'
      debug.warn "Timezone isnt Pacific Time, #{oh.BCRES_Timezone}"

    startTime = formatOpenHouseDateTime(oh.OpenHouseStartTime)
    endTime = formatOpenHouseDateTime(oh.OpenHouseEndTime)
    continue unless (startTime and endTime)
    
    detail = {
      f: startTime
      t: endTime
      tp: oh.OpenHouseType
      action: oh.OpenHouseStatus is 'Active'
    }
    openHouseDetails.push(detail)
  
  if openHouseDetails.length
    record.ohz = openHouseDetails
  delete record.openHouse

###
# @description 格式化空调信息
# @param {Object} record - 记录对象
###
formatAirCondition = (record) ->
  if record.Cooling
    record.ac = record.Cooling
  # 如果CoolingYN明确为false,则设置为None
  else if record.CoolingYN is false
    record.ac = 'None'
  # 清理临时字段
  delete record.CoolingYN
  delete record.Cooling

###
# @description 将英尺字符串转换为米
# @param {string} feet - 英尺字符串 (格式如: "5'6"")
# @return {number|undefined} 转换后的米数,无效输入返回undefined
###
feetStr2Meter = (feet) ->
  if (not feet) or ('string' isnt typeof feet)
    return
  
  # 分割英尺和英寸部分
  [foot, inch] = feet.split '\''
  
  # 验证英尺部分
  if (not foot) or (isNaN(foot))
    return
  
  # 英寸部分默认为0
  inch = if (inch and (not isNaN(inch))) then parseInt(inch) else 0
  
  # 计算总英尺数并转换为米
  totalFeet = parseInt(foot) + (inch / 12)
  propertiesHelper.getFloat(totalFeet * FEET2METER)

###
# @description 格式化房间信息
# @param {Object} record - 记录对象
###
formatRms = (record) ->
  rms = []
  for i in [1..40]
    type = record["BCRES_Room#{i}RoomType"]
    continue unless type
    
    rms.push({
      t: type
      l: record["BCRES_Room#{i}RoomLevel"]
      w: feetStr2Meter record["BCRES_Room#{i}RoomWidth"]
      h: feetStr2Meter record["BCRES_Room#{i}RoomLength"]
    })
    
    # 清理临时字段
    delete record["BCRES_Room#{i}RoomType"]
    delete record["BCRES_Room#{i}RoomLevel"]
    delete record["BCRES_Room#{i}RoomWidth"]
    delete record["BCRES_Room#{i}RoomLength"]
  
  record.rms = rms if rms.length

###
# @description 清理记录中的临时字段
# @param {Object} record - 记录对象
# @return {Object} 清理后的记录
###
cleanRecord = (record) ->
  deletePatterns = [
    /^ListOffice.*/,
    /^ListAgent.*/,
    /^CoListOffice.*/,
    /^CoListAgent.*/,
    /^BuyerOffice.*/,
    /^BuyerAgent.*/,
    /^CoBuyerAgent.*/,
    /^CoBuyerOffice.*/,
    # NOTE: impFormat.formatMfee会转换,这里需要去除转换前的字段
    /^AssociationFee/,
    /^AssociationFeeFrequency/,
    /^AssociationFeeIncludes/
  ]
  
  for fieldName of record
    for pattern in deletePatterns
      if pattern.test(fieldName)
        delete record[fieldName]
        break
  return record

###
# @description 格式化朝向信息
# @param {Object} record - 记录对象
###
formatFce = (record) ->
  if record.fce
    result = abbrExposure(record.fce)
    record.fce = result if result?
  else if record.BCRES_DirectionFacesRearYard
    result = abbrExposure(record.BCRES_DirectionFacesRearYard)
    record.fce = result if result?
    delete record.BCRES_DirectionFacesRearYard
  return

###
# @description 转换BCRE RESO格式的房源数据
# @param {Object} params - 参数对象
# @param {Object} params.srcRecord - 源记录
# @param {Function} cb - 回调函数
###
exports.convertFromBcreReso = convertFromBcreReso = ({srcRecord}, cb) ->
  try
    ret = doMapping(srcRecord,bcreResoFieldMap)
    
    # 处理必要的字段转换
    formatTimestamp(ret)
    formatStatusAndLst(ret)
    formatSaleType(ret)
    formatPropertyType(ret)
    # generatePhoUrls(ret)

    formatRms(ret)
    formatOpenHouse(ret)
    formatLandSize(ret)
    formatMfee(ret)
    formatListingAgent(ret)
    formatFurnished(ret)
    formatDaddr(ret)
    formatAddr(ret,srcRecord)
    formatAirCondition(ret)
    formatFce(ret)

    # 设置默认值
    ret.cnty ?= 'CA'
    ret.prov ?= 'BC'
    ret.id ?= 'BRE' + ret.sid
    ret.src ?= 'BRE' # 如果更换了这个值，记得修改statusLstMapping
    
    cleanRecord(ret)
  catch err
    debug.error 'Bcre Reso convert error:', err
    return cb(err)
    
  debug.debug 'Bcre Reso convert result:', ret
  cb(null, ret)
