###
Detached: Cottage,Det Condo,Det W/Com Elements,Detached,Link,Rural Resid
Semi-Detached: Semi-Detached,Semi-Det Condo
Freehold Townhouse: Att/Row/Twnhouse
Condo Townhouse: Condo Townhouse
Condo Apartment: Co-Op Apt,Co-Ownership Apt,Comm Element Condo,Condo Apt,Leasehold Condo
Multiplex: Duplex,Fourplex,Multiplex,Triplex
Land:Land,Vacant Land,Vacant Land Condo
Parking Space:Parking Space
Room: Room
Commercial/Retail: Commercial/Retail,Store W/Apt/Offc,Store W/Apt/Office,
Office:Office
Sale Of Business:Sale Of Business
Farm: Farm
Industrial:Industrial
Other:Lower Level,Mobile/Trailer,Other,Time Share,Upper Level,Locker,Investment and all others

Some style may not fall in our Type/Style combination, default style as other or none


Building.Appliances
###

cityHelper = require '../lib/cityHelper'
propSearch = require './propSearch'
libPropertyImage = require './propertyImage'
# propAddrHelper = require '../lib/propAddress'
checksum = require '../lib/checksum'
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
sortHelper = require '../lib/sortHelper'
limiter = require '../lib/limiter'
ObjectId = require('../lib/mongo4').ObjectId
# loop include. Fred 2020.12.13
#libPropertiesSQL = INCLUDE './propertiesPostSql'
{toCamelCase,strCapitalize,unifyAddress,formatLng,\
stringContainsAny,levenshteinDistance,isObjectIDString} = require '../lib/helpers_string'
{isNumber, round} = require '../lib/helpers_number'
{ymd2date,date2num,dateFormat,dayDiff,formatUpdatedMt,inputToDate,\
  inputToDateNum, getMonthNamesToNum,dateNum2str} = require '../lib/helpers_date'
helpers = require '../lib/helpers'
libImpCommon = require '../libapp/impCommon'

RM_CONTROL_CITIES = cityHelper.RM_CONTROL_CITIES

# fields that are logged into PropertyImportLog
LOG_FIELDS = {
  Lsc:1
  lSrc:1
}

module.exports.minSalePrice = MIN_SALE_PRICE = 20000
module.exports.maxSalePrice = MAX_SALE_PRICE = 80000000
module.exports.maxRentPrice = MAX_RENT_PRICE = 100000
NEGOTIABLE_PRICE = 100

module.exports.treb2date = treb2date = (str)->
  unless 'string' is typeof str # only parse string
    return str
  if m = str.match /\/Date\((\d+)\-(\d+)\)\//
    return new Date(parseInt(m[1]))
  ymd2date str

module.exports.date2mdy = date2mdy = (date)->
  return null unless date
  return '' + (date.getMonth() + 1) + '/' + \
    date.getDate() + '/' + date.getFullYear()

module.exports.fnDate2mdy = fnDate2mdy = (str)-> date2mdy treb2date str

module.exports.fnDate2num = fnDate2num = (str)-> date2num treb2date str


module.exports.computedOffDate = (prop,format) ->
  return prop.offD if prop.offD
  if prop.status_en is 'A'
    now = new Date()
    # month = zeroPadLeft(now.getMonth()+1)
    # nowNum = ''+now.getFullYear()+month+zeroPadLeft(now.getDate());
    nowNum = date2num now
    if prop.exp < parseInt(nowNum)
      return ''
    offDate = prop.exp
  else
    offDate = prop.sldd or prop.unavail_dt or prop.mt
  
  if /d{8}/.test (''+offDate)
    return offDate

  if offDate
    try
      dateFormat offDate,format
    catch e
      debug.error e
  offDate

module.exports.computedOnDate = (prop, format) ->
  return prop.onD if prop.onD
  onDate = prop.ld or prop.lstd or prop.ctrdt or prop.ts
  return onDate if (typeof onDate is 'number') and onDate.toString().length is 8
  if onDate
    try
      return dateFormat onDate,format
    catch e
      debug.error e
      onDate

module.exports.computeDayDiff = computeDayDiff = (ts,now)->
  return 0 unless ts
  # ts = new Date (lstd / 10000),(((lstd % 10000) / 100) - 1), (lstd % 100)
  now ?= new Date()
  dayDiff ts, now

module.exports.computePropDom = computePropDom = (prop)->
  if (prop.status is 'U') and /Sld|Lsd|Pnd|Cld/.test(prop.lst) and (prop.sldDom >= 0)
    return prop.sldDom
  lstdDt = prop.onD or prop.ld or prop.lstd or prop.ctrdt or prop.ts
  if prop.src is 'EDM'
    lstdDt = prop.ts or prop.onD or prop.ld or prop.lstd or prop.ctrdt
  if prop.status is 'A'
    return computeDayDiff(lstdDt)
  else
    # or prop.exp?
    return computeDayDiff(lstdDt, (prop.offD or prop.sldd or prop.unavail_dt or prop.mt))

# @input
# interface prop {
#   saletp:string[], # prop from db field
#   saletp_en:string[], #prop from front-end,
# }
module.exports.isSale = (prop)->
  saletp = prop.saletp_en or prop.saletp or []
  return /sale/ig.test saletp.toString()
  #prop.saletp?.indexOf('Sale') >= 0
module.exports.isRent = (prop)->
  saletp = prop.saletp_en or prop.saletp or []
  return /lease|rent/ig.test saletp.toString()
  # prop.saletp?.indexOf('Lease') >= 0


isBusiness = (l = {}) ->
  # l.pclass && l.pclass.indexOf('b') > -1
  ptype = ''
  if Array.isArray(l.ptype2)
    ptype = l.ptype2.join(',')
  else
    ptype = l.ptype or l.tp1
  # console.log('----',ptype)
  /Office|Retail|Commercial|Business/.test ptype

module.exports.getIconChar = getIconChar = (l) ->
  # TODO: NOTE: wont be here, isProj jumped to getProjName
  if l.isProj
    l.saletp_en = [ 'Sale' ]
    l.ptype2_en = [
      l.tp1 or ''
      l.tp2 or ''
    ]
    if not(l.tp1 or l.tp2)
      return 'P'
    if isBusiness(l)
      # l.pclass = ['b']
      # l.ptype = 'Business'
      return 'B'
  saletp = l.saletp_en or l.saletp or ''
  ptype2 = l.ptype2_en or l.ptype2
  #saletp.indexOf('Sale') > -1) {
  word = ''
  if saletp.length
    ptype2 = ptype2.join(',') if Array.isArray(ptype2)
    # SDCBTL
    if /Semi-/.test(ptype2)
      word = 'S'
    else if /Detached|Det\sComm/.test(ptype2)
      word = 'D'
    else if /Apt|Apartment|Condo/.test(ptype2)
      word = 'C'
    else if /Att|Townhouse/.test(ptype2)
      word = 'T'
    else if isBusiness(l)
      word = 'B'
    # console.log('++++',ptype2)
    # else if (/Parking/.test(ptype2)) {
    #   word = 'P';
    # }
    # else if (/Office/.test(ptype2)) {
    #   word = 'O';
    # }
  #else {
  # word = 'L';
  # if (l.pclass.indexOf('b') > -1) {
  #   word = 'L';
  # } else {
  #   word = 'R';
  # }
  word

exports.propPrice = propPrice = (number, toFixed) ->
  # console.log(number);
  if number?
    number = parseInt(number)
    if isNaN(number) or number is 0
      return ''
    # 9000 -> 9k, 800000 -> 800k
    if number < 1000
      # number
      number += ''
    else if number < 10000
      number = (number / 1000).toFixed(1) + 'K'
    else if number < 999500
      number = Math.round(number / 1000).toFixed(0) + 'K'
    else
      #if (number >= 1000000) {
      toFixed = toFixed or 1
      number = (number / 1000000).toFixed(toFixed) + 'M'
    # number = number.replace('.0','');
    number
  else
    ''
getIconSuffix = (p, isFull) ->
  _hasCmstn = ()->
    if p.cmstn or p.market?.cmstn
      return true
    false
  rmtype = undefined
  suffix = undefined
  # if (p.adrltr) {
  #   console.log(p.adrltr)
  # }
  if not p.src
    return ''
  if p.ltp is 'exlisting'
    rmtype = 'Exclusive'
    suffix = 'E'
  else if p.ltp is 'assignment'
    rmtype = 'Assignment'
    suffix = 'A'
  else if p.ltp is 'rent' and (not _hasCmstn())
    rmtype = 'Landlord Direct'
    suffix = 'L'
  else if p.ltp is 'rent' and _hasCmstn()
    rmtype = 'Exclusive Rental'
    suffix = 'R'
  if isFull
    return rmtype
  suffix

###
获取房源的销售状态标签、标签颜色和状态字符串
@param {Object} prop - 房源对象
@param {Boolean} notNeedLstStr - 不需要状态字符串
@return {Object} 包含saleTpTag(销售类型标签)、tagColor(标签颜色)和lstStr(状态字符串)的对象
###
exports.getSaleTpTagAndTagColorAndLstStr = getSaleTpTagAndTagColorAndLstStr = (prop,notNeedLstStr = false)->
  if (not prop) or ('object' isnt typeof prop) or (not Object.keys(prop).length)
    return {}
  # 处理MLS状态映射
  if mlsStatus = prop.MlsStatus
    mapping = libImpCommon.statusLstMapping[mlsStatus]?[prop.src]
    lstStr = mapping?.lstStr
  # 获取房源状态和销售类型
  status = prop.status_en or prop.status
  isSale = /Sale/.test (prop.saletp_en or prop.saletp)
  if /A|Active/.test status # 处理活跃状态的房源，前端统一显示Sale/Lease
    saleTpTag = if isSale then 'Sale' else 'Lease'
    tagColor = 'green'
  else # 处理已售出/已租出/已下架的房源
    if mapping?.saleTpTag # 如果mapping存在优先使用mapping的数据
      saleTpTag = mapping.saleTpTag
    else # 根据sp和lst状态判断
      if prop.sp or /Sld|Lsd|Pnd|Cld/.test prop.lst
        saleTpTag = 'Sold'
      else
        saleTpTag = 'Delisted'
    # 根据销售类型区分"已售"和"已租", Delisted不区分售卖还是租出
    if saleTpTag and /sold/.test(saleTpTag.toLowerCase())
      saleTpTag = if isSale then 'Sold' else 'Leased'
      tagColor = 'red'
  result = {saleTpTag}
  result.tagColor = tagColor if tagColor
  if notNeedLstStr
    return result
  # 有些房源status是U，lst是Pc，例如 DDF26219369
  if (saleTpTag is 'Delisted') and (prop.lst in ['Pc','New','Auc','Lc','Sc'])
    result.lstStr = saleTpTag
  # 设置描述性状态字符串
  else if mapping and lstStr
    result.lstStr = mlsStatus
  else if (not mapping) and prop.lst and (not /New|Active|Sld|Pnd|Cld|Lsd/.test(prop.lst))
    # 对于非标准状态,使用getStatusLongName获取状态描述
    {statusLongName} = getStatusLongName({prop,historyRecord: {s:prop.lst}})
    result.lstStr = statusLongName
  return result

# setup rmbdrm/rmgr/rmbthrm
module.exports.setUpPropRMPlusFields = setUpPropRMPlusFields = (prop,l10n,_ab)->
  l10n ?= (a)->
    return a
  if prop.bdrms?
    prop.rmbdrm = prop.bdrms+(if prop.br_plus then '+'+prop.br_plus else '')
    # prop.rmbdrm += 'M'
  # TODO: bthrms plus
  if prop.bthrms? or prop.tbthrms?
    prop.rmbthrm = prop.tbthrms or prop.bthrms
    # prop.rmbthrm += 'D'
  # TODO: gr_plus
  if prop.gr? or prop.tgr?
    # 3+7(10)=>10(3+7) condo,apartment只显示tgr
    if isCondo prop
      prop.rmgr = prop.tgr
    else
      gr = (if prop.gr? then prop.gr else '')+(if prop.park_spcs then '+'+prop.park_spcs else '')
      prop.rmgr = (if prop.tgr? then prop.tgr else '')
      prop.rmgr += '('+gr+')' if gr.length and (prop.rmgr isnt gr)
  isSale = /Sale/.test (prop.saletp_en or prop.saletp)
  status = prop.status_en or prop.status
  if /A|Active/.test status
    price = if isSale then prop.lp else (prop.lpr or prop.lp)
    if price is 0
      # TODO: value on map->5**,
      # vOnMap, vOnList, vOnPopup, (vOnMap || vOnList || vOnPopup),
      iconChar = getIconChar(prop)
      iconChar = l10n(iconChar,'propMarkerChar') if iconChar
      prop.amount = iconChar+l10n('TBD','propMarkerChar')
      prop.priceValStrRed = l10n 'To Be Negotiated'
    else
      prop.priceValStrRed = currencyFormat price,'CAD$',0
      iconChar = getIconChar(prop)
      iconChar = l10n(iconChar,'propMarkerChar') if iconChar
      prop.amount = iconChar+propPrice(prop.lp or prop.lpr)#+'XXXXXXXBACKEND'
    # {{_('for', 'saleOrRent')}} {{_('Sale')}}
    prop.saleOrRent = l10n('for', 'saleOrRent')+' '+l10n(if isSale then 'Sale' else 'Lease')
  else
    if prop.sp and prop.showSoldPrice
      prop.priceValStrRed = currencyFormat prop.sp,'CAD$',0
      prop.priceValStrRedDesc = l10n(if isSale then 'Sold Price' else 'Leased Price')
      iconChar = getIconChar(prop)
      iconChar = l10n(iconChar,'propMarkerChar') if iconChar
      prop.amount = iconChar+propPrice(prop.sp)
    if prop.lp or prop.lpr
      prop.askingPriceStr = currencyFormat((prop.lp or prop.lpr),'CAD$',0)
      prop.askingPriceStrDesc = l10n('Asking price')
  prop.suffix = getIconSuffix(prop)
  tagInfo = getSaleTpTagAndTagColorAndLstStr(prop)
  prop.tagColor = tagInfo.tagColor if tagInfo.tagColor
  if tagInfo.saleTpTag
    prop.saleTpTag_en = tagInfo.saleTpTag
    prop.saleTpTag = l10n(tagInfo.saleTpTag)
    # 曾经有卖价但是没有交易成功，退市了，则把卖价隐藏不显示
    if (prop.priceValStrRed && prop.saleTpTag_en is 'Delisted')
      delete prop.priceValStrRed
      delete prop.priceValStrRedDesc
  prop.lstStr = l10n(tagInfo.lstStr,'proplst') if tagInfo.lstStr
  if prop.askingPriceStr and ((prop.lst in ['Pnd','Cld','Sld']) or (prop.ltp is 'assignment')) # 房源详情页价格前面的红色Sold标签
    prop.showSoldRedTag = true



trebPtpMap =
  'Detached' : ['Detached', 'Cottage', 'Det Condo', 'Det W/Com Elements',  'Link', 'Rural Resid']
  'Semi-Detached' : ['Semi-Detached', 'Semi-Det Condo']
  'Freehold Townhouse' : ['Freehold Townhouse', 'Att/Row/Twnhouse']
  'Condo Townhouse' : ['Condo Townhouse']
  'Condo Apt' : ['Condo Apt', 'Co-Op Apt', 'Co-Ownership Apt', 'Comm Element Condo',  'Leasehold Condo']
  'Multiplex' : ['Multiplex', 'Duplex', 'Fourplex', 'Triplex']
  'Land' : ['Land', 'Vacant Land', 'Vacant Land Condo']
  'Parking Space' : ['Parking Space']
  'room' : ['room']
  'Retail' : ['Commercial/Retail', 'Store W/Apt/Offc', 'Store W/Apt/Office', 'Retail']
  'Office' : ['Office']
  'Business' : ['Sale Of Business']
  'Farm' : ['Farm']
  'Industrial' : ['Industrial']
  'Other' : ['Other', 'Lower Level', 'Mobile/Trailer', 'Time Share', 'Upper Level', 'Locker', 'Investment and all others']

trebPstylMap =
  '2-Storey' : ['2-Storey', '1 1/2 Storey']
  '3-Storey' : ['3-Storey', '2 1/2 Storey']
  'Bungalow' : ['Bungalow', 'Bungalow-Raised']
  'Split' : ['Split', 'Sidesplit 5', 'Sidesplit 4', 'Sidesplit 3', 'Backsplit 5','Backsplit 4','Backsplit 3']
  'Loft' : ['Loft', 'Bungaloft', 'Industrial Loft']
  'Bachelor/Studio' : ['Bachelor/Studio']
  'Other' : ['Other', 'Stacked Townhse', 'Multi-Level']
  

trebFormatDateTime = (str)->
  # '2/23/2016 12:00:00 AM'
  # debug.debug str
  if ('string' is typeof str) and (m = str.match /(\d+)\/(\d+)\/(\d+)\s+(\d+)\:(\d+)\:(\d+)\s*(AM|PM)/i)
    h = if m[7].toUpperCase() is 'PM' then parseInt(m[4],10) + 12 else m[4]
    r = "#{m[3]}-#{m[1]}-#{m[2]} #{h}:#{m[5]}:#{m[6]}"
    return r
  null

# trebConvertDate = (strDt)->
#   if dt = trebFormatDateTime strDt
#     return new Date dt
#   strDt

initMappingObj = (m)->
  ret = {}
  return ret unless m
  for to, keyList of m
    if Array.isArray keyList
      for key in keyList
        ret[key] = to
  ret

trebPtpObjMap = initMappingObj(trebPtpMap)
trebPstylObjMap = initMappingObj(trebPstylMap)

# #把properties的ptp，ptype简化成几个类型，eg：Cottage->Detached,'1 1/2 Storey'->'2-Storey'
module.exports.simplfyPtp =simplfyPtp= (src)->
  ret = []
  ret[0] = trebPtpObjMap[src.ptp] or 'Other'
  ret[1] = trebPstylObjMap[src.pstyl] or 'Other'
  # if ptp is other then ['Other','']
  # if pstyle is other then ['ptp','']
  if ret.length and ((ret[0] is 'Other') or (ret[1] is 'Other'))
    ret[1] = ''
  unless ret.length
    ret = ['Other','']
  if ret[0] is 'Condo Apt'
    if ret[1] not in ['Loft', '', 'Bachelor/Studio']
      ret[1] = ''
  ret

# do not set to 0 when parse failed
# TODO: should move to helpers_numbers
module.exports.getInt = getInt = (v, defaultVal=null)->
  try
    r = parseInt v
  catch e
    r = defaultVal
  r = defaultVal if isNaN r
  r

module.exports.getFloat = getFloat = (v, defaultVal=null)->
  try
    r = parseFloat v
  catch e
    r = defaultVal
  r = defaultVal if isNaN r
  r = round(r,2)
  r

#Extras + Ad_text => 'm'
module.exports.combineFields = combineFields = (src,fields,glue = '')->
  ret = ''
  for f in fields
    ret += glue if ret.length > 0
    ret += src[f] or ''
  ret

#Num_kit + kit_plus => kch
#Br+Br_plus=>tbdrms
module.exports.plusFields = plusFields = (src,fields)->
  ret = null
  for f in fields
    if src[f]?
      ret ?= 0
      ret += getFloat src[f]
  ret

module.exports.getEdges = getEdges = (src,field)->
  unless s = src[field]
    return [] # no value
  s = ('' + s).trim() # s maybe number
  return [0,0] if s is 'New' # new building
  return [getFloat(s),getFloat(s)] if isNumber s
  #100-200,.50-1.99
  if m = s.match /^([\d]*[\.]*[\d]*)\-([\d]*[\.]*[\d]*)$/
    n1 = getFloat m[1]
    n2 = getFloat m[2]
    if n1<=n2
      return [n1,n2]
    else
      return [n2,n1]
  else if m = s.match /^([\d]+)\+$/
    return [getFloat(m[1]),null]
  #TODO:test sqft<700.
  else if m = s.match /^\<\s*([\d]+)$/
    return [null,getFloat(m[1])]
  else if m = s.match /^\>\s*([\d]+)$/
    return [getFloat(m[1]),null]
  return [null,null] # unmatched pattern


# Oh_date1 '2016-02-07 00:00:00.0', Oh_from1 '2:00PM', Oh_to1 '4:00PM' => oh0,oh0t "2016-02-07 14:00" - "2016-02-07 16:00"
module.exports.twoDigit = twoDigit = (n)->
  if n is 0
    return '00'
  else if n < 10
    return '0' + n
  else
    return '' + n

#"2:00 PM"
module.exports.ampm224 = ampm224 = (s)->
  if m = s.match /(\d+)\:(\d+)\s*([AP])M/i
    try
      hour = parseInt m[1]
      min = parseInt m[2]
      if (hour < 12) and ((ampm = m[3]).toUpperCase() is 'P') # 12PM is 12:00
        hour += 12
      return twoDigit(hour) + ":" + twoDigit(min)
    catch e
      debug.error "AMPM to 24H error: #{s}"
      debug.error e
  debug.debug "ampm224 parse error ", s
  return "00:00"

module.exports.abbrRms =abbrRms= (rm)->
  if rm.t
    rm.t = rm.t.replace(/\sroom/ig,'')
  if rm.l
    rm.l = ("" + rm.l).replace(/basement/ig,'Bsmt').replace(/\slevel/ig,'').replace(/between/ig,'Betwn')
  rm

module.exports.clearEmptyFields = clearEmptyFields = (src)->
  for k,v of src
    unless v? and v isnt ''
      delete src[k]
  src

isShortify = (m)->
  unless m
    return false
  if (m[1][0] is m[2]?[0])
    return true
  else if (m[1][0] is m[3]?[0])
    return true
  false

module.exports.formatCityName = formatCityName = (city)->
  if 'string' isnt typeof city
    debug.error 'Error: '+city
    city+=''
  #windows:/\. "$*<>:|?
  #linux:/\. "$
  city.replace(/\W/g, '_').toUpperCase()

module.exports.sepCmtyName = sepCmtyName = (cmty)->
  cmty += ''
  sn = null
  nm = null
  fmtd = null
  onm = cmty
  if m = cmty.match /^(\w{3,4})\s\-\s(.*)$/
    sn = m[1]
    nm = m[2]
    fmtd = formatCityName(m[2])
    cmty = nm
    found = true
  if (m = cmty.match /^(\w{2})\s(.*)$/) and isShortify(m)
    nm = cmty.substr(3)
    sn = m[1]
    fmtd = formatCityName(nm)
    found = true
  if not found
    return onm:onm,nm:cmty,fmtd:formatCityName(cmty)
  else
    return onm:onm,sn:sn,nm:nm,fmtd:fmtd


module.exports.isPriceChanged = (obj,old,saletp)->
  #debug.debug obj
  #只有obj和old里的price都不是0才计算priceChange
  if (not obj) or (not old) or (not saletp)
    return false
  if saletp.indexOf('Sale') >= 0
    fld = 'lp'
  if saletp.indexOf('Lease') >= 0
    fld = 'lpr'
  if ((p1 = getFloat(old[fld])) isnt (p2 = getFloat(obj[fld]))) and (p1 and p2)
    return [p1,p2]
  null
#see rmapp/listingPicUrlReplace
# module.exports.listingPicUrlReplace = (prop={},num)->
#   # prop.picUrls are added in useOldLogic
#   l = prop.picUrls or []
#   ret = []
#   for p,idx in l
#     if num and idx > num
#       break
#     if (m = p.match(/^(\$[0-9a-zA-Z])(.*)$/)) and (str = prop[m[1]])
#       # str = https://img.realmaster.com/cda/828.1616992866/044/22949044_{{1}}.jpg
#       # m = [ '$11', '$1', '1', index: 0, input: '$11', groups: undefined ]
#       # re = {{1}}
#       # after replace: https://img.realmaster.com/cda/828.1616992866/044/22949044_1.jpg
#       re = new RegExp("\\{\\{#{m[1].substr(1)}\\}\\}",'gi')
#       ret.push str.replace re,m[2]
#     else
#       ret.push p
#   ret

_getSortTimeStamp = (prop,cfg={})->
  ts = prop.phoTs or prop.phots or prop.phoDl or prop.phodl or prop.PhotoDlDate or prop.mt
  #NOTE: phomt might not changed, <- pix_updt
  #add default timestamp in case prop has no fields
  ts = new Date(ts)
  if isNaN(ts)
    ts = new Date(prop._mt)
  str ='' + parseInt((ts).getTime()/1000)
  str.substr(3)
  res = '828.'
  if cfg.isPhone
    res = '414.'
  # if cfg.isPad
  #   res = '828.'
  res+str
_basePicUrl = (isCip,cfg)-> # default use https
  shareHostNameCn = cfg?.shareHostNameCn or 'realmaster.cn'
  if isCip
    if /^(http|img\.)/.test(shareHostNameCn)
      shareHostNameCn
    else
      "https://img.#{shareHostNameCn}"
  else
    "https://img.realmaster.com"
_ddfPicUrl = (ml_base,ddfID,num, prop, opt)->
  # https://img.realmaster.com/img/527/R2581527_1.jpg?828.1621288315
  # "#{ml_base}/img/#{ddfID.substr(-3)}/#{ddfID}_#{num}.jpg?#{_getSortTimeStamp(prop,opt)}"
  "#{ml_base}/cda/#{_getSortTimeStamp prop,opt}/#{ddfID.substr(-3)}/#{ddfID.substr(3)}_#{num}.jpg"
  # https://img.realmaster.com/cda/828.1621371616/174/23214174_1.jpg

_trbPicUrlNew = (ml_base, ml_num, num, prop, opt)->
  if num is 1
    return "#{ml_base}/trb/#{_getSortTimeStamp prop,opt}/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
  else
    return "#{ml_base}/trb/#{_getSortTimeStamp prop,opt}/#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg"

_trbPicUrl = (ml_base,ml_num,num, prop, opt)->
  if num is 1
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg?#{_getSortTimeStamp(prop,opt)}"
  else
    return "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg?#{_getSortTimeStamp(prop,opt)}"

# NOTE: for web trust assignment     picUrls: [ 'http://f.realmaster.cn/P/JQUN/CPM.jpg' ]
genRMListingPicUrls = (prop,num,useThumb,imgServerDlAddr='')->
  # console.log '++++++gen RMListing',prop.img
  imgs = prop.imgs or prop.pic
  if not imgs?.l
    delete prop.picUrls
    return
  ret = []
  base = imgs.base
  # fldr = imgs.fldr #  not used here, used in delete
  mlbase = imgs.mlbase
  ml_num = imgs.ml_num or prop.sid
  unless mlbase #new pic.l data structure removed mlbase
    if useThumb
      prop.thumbUrl = libPropertyImage.replaceRM2REImagePath imgs.l?[0],imgServerDlAddr
    else
      prop.picUrls = libPropertyImage.replaceRM2REImagePathForArray imgs.l,imgServerDlAddr
    return
  thumbUrl = ''
  for i,idx in imgs.l
    # ml case
    # NOTE: not a lot of data in this format
    if num and idx > num
      break
    if i[0] is '/'
      # TODO: $1
      picIndex = parseInt(i.substr(1))
      if picIndex is 1
        thumbUrl = mlbase + i + '/' + ml_num.slice(-3) + '/' + ml_num + '.jpg'
        ret.push thumbUrl
      else
        i2 = i.substr(1)
        prop.$1 = mlbase + '/{{1}}/' + ml_num.slice(-3) + '/' + ml_num + '_{{1}}.jpg'
        thumbUrl = mlbase+'/'+i2+'/'+ml_num.slice(-3)+'/'+ml_num+'_'+i2+'.jpg'
        ret.push '$1/'+i2
        # ret.push mlbase + i + '/' + ml_num.slice(-3) + '/' + ml_num + '_' + i.substr(1) + '.jpg'
    else if i.indexOf("http") == 0
      thumbUrl = i
      ret.push i
    #upload case
    else
      # ret.push base + '/' + i
      prop.$3 = base+'/{{3}}'
      thumbUrl = base+'/'+i
      ret.push '$3'+i
  if useThumb and thumbUrl
    prop.thumbUrl = thumbUrl
  prop.picUrls = ret

# 使用treb在线图片，直接在prop中添加thumbUrl,$1,picUrls:[$11,$12...]
useTrebOnlinePhoto = (prop,num,useThumb)->
  # NOTE: 2024-03-05 treb photo url changed, no longer works for new ids
  defaultUrl = "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index={{1}}"
  if Array.isArray(prop.photonumbers) and prop.photonumbers.length
    prop.$1 = defaultUrl
    for i in prop.photonumbers
      if num and i > num
        return
      if useThumb
        prop.thumbUrl = prop.$1.replace('{{1}}',i)
      prop.picUrls.push '$1'+i
  else if (prop.pho and pho = parseInt(prop.pho))
    # console.log '+++++no prop.photonumbers'
    prop.$1 = defaultUrl
    for i in [1..pho]
      if num and i > num
        return
      if useThumb
        prop.thumbUrl = prop.$1.replace('{{1}}',i)
      prop.picUrls.push '$1'+i

OLD_TREB_PIC_DATE = new Date('2018-01-01')

oldPropOrForceTrebPic = (prop)->
  new Date(prop.ts) < OLD_TREB_PIC_DATE

genOtherListingUrls = (prop,isCip,num,useThumb,cfg)->
  # Newly added flag for sales, force use treb photo
  if num
    numLimit = Math.min(num,prop.pho)
  else
    numLimit = prop.pho
  # reso下载的房源图片保存在phoUrls
  if prop.phoUrls?.length
    genListingPicUrlsByPhoUrls({prop,numLimit,useThumb,isLarge:cfg?.isLarge})
  # 历史的OTW/CLG房源图片根据phoIDs生成
  else if prop.phoIDs?.length
    genListingPicUrlsByPhoIDs({prop,numLimit,useThumb,isCip,cfg})
  else if isTRBProp(prop) and (prop.useTrebPhoto or cfg.use3rdPic)
    useTrebOnlinePhoto(prop,num,useThumb)
  # has pho like pho:12
  else if prop.pho
    if isTRBProp(prop)
      # before 2018-01-01
      if oldPropOrForceTrebPic(prop)
        # console.log 'old data'
        useTrebOnlinePhoto(prop,numLimit,useThumb)
      else
        useOldLogic(prop, isCip,numLimit,useThumb,cfg)
    else if (prop.src is 'BRE') or (prop.phosrc is 'bre')
      bcrePhotoLogic({prop,isCip,numLimit,useThumb,cfg})
    else if prop.src in ['RHB','EDM','CAR','OTW']
      genListingPicUrlsByPhoUrls({prop,numLimit,useThumb,isLarge:cfg?.isLarge})
    else
      useOldLogic(prop, isCip,numLimit,useThumb,cfg)
  # case where has photonumber but not pho, cannot use as 2nd if; otherwise new listing will use treb photo
  else if prop.photonumbers
    useTrebOnlinePhoto(prop,numLimit,useThumb)
  else
    # if treb prop images are not downloading and its ts is less than 12 hours compared to now
    propTs = prop.onD or prop.ts or prop.mt or ''
    if /^\d{8}$/.test propTs
      propTs = dateNum2str(propTs)
    diff = Date.now() - new Date(propTs).getTime()
    if isTRBProp(prop) and (diff >= 0) and (diff < 1000 * 60 * 60 * 12)
      # give one default img from treb
      prop.pho = 1
      prop.downloadingImg = true
      useTrebOnlinePhoto(prop,num,useThumb)

isTRBProp = (prop={})->
  if prop.src is 'TRB'
    return true
  return /^TRB/.test(prop._id)

#TODO: isProj (prop.deposit_m || prop.closingDate)
module.exports.isRMListing = isRMListing = (prop={})->
  return /^RM1-/.test(prop.id)

module.exports.isMlsPrefix = isMlsPrefix = (input) ->
  # 仅支持string,string中仅包含数字和字母
  if (not ('string' is typeof input)) or (/[^a-zA-Z0-9]/.test(input))
    return false
  if /^(TRB|DDF|BRE|RHB|OTW|CLG|CAR|EDM)/.test input
    return true
  return false

exports.isMlNum = (input)->
  # 不支持array
  if Array.isArray(input)
    return false
  # 数字和字母以外的字符
  if /[^a-zA-Z0-9]/.test(input)
    return false
  if isMlsPrefix(input)
    return true
  if /^[a-zA-Z]\d+/.test input
    return true
  if /\d{6,}/.test input
    return true
  return false

# display prop id in detail page
module.exports.getPropId = (prop)->
  return prop.id if isRMListing prop
  return prop.sid

# modifies prop.$1, prop.picUrls, num=1
module.exports.genListingPicReplaceUrls = genListingPicReplaceUrls = (prop={},isCip=false,cfg={},imgServerDlAddr='')->
  num = cfg.num
  useThumb = cfg.useThumb
  # cfg.isPad = 828
  # cfg.isPhone = 414
  # 如果prop.thumbUrl存在,则不再次生成缩略图,直接使用prop.thumbUrl
  # NOTE: 列表查询时,thumbUrl存在,phoUrls等字段不存在,会导致thumbUrl生成逻辑错误
  if prop.thumbUrl
    useThumb = false
  prop.picUrls = []
  if isRMListing(prop)
    genRMListingPicUrls(prop,num,useThumb,imgServerDlAddr)
  else
    genOtherListingUrls(prop,isCip,num,useThumb,cfg)
  # NOTE: 缩略图URL需要替换
  thumbUrlReplace(prop, isCip, _basePicUrl(isCip, cfg))

# prop.thumbUrl = 'http://xxxx'
# TODO: too many params 
module.exports.genListingThumbUrlForList = (list=[],isCip,isPad,use3rdPic,shareHostNameCn) ->
  for p in list
    genListingPicReplaceUrls(p,isCip,{num:1,useThumb:true,isPad,use3rdPic,shareHostNameCn})
    # NOTE: 缩略图URL需要替换
    thumbUrlReplace(p, isCip, _basePicUrl(isCip, {shareHostNameCn}))
    # console.log '++++++',p.thumbUrl,p.picUrls

isNewDDFPicFormat = (prop,res)->
  l = ['H','M','L']
  if res not in l
    # return false
    debug.error 'Error: unsopported res'
  # for i in l
  if prop['_$'+res] and Array.isArray(prop['p'+res+'A'] or prop.pA)
    return [prop['_$'+res], prop['p'+res+'A'] or prop.pA,"{{#{res}}}"]
  return null

# bcre房源根据picUrl生成图片地址
genBcrePicUrl = (picUrl,w,h)->
  brePicUrl = picUrl.replace('[$width]',w).replace('[$high]',h).replace('[pic#]','{{2}}')
  if not brePicUrl.match(/^(http:|https:)/g)
    brePicUrl = 'https:'+brePicUrl
  brePicUrl

###
bcre photo has 3 types
1. from manual import. it has picUrl which should be replcaced by
###
bcrePhotoLogic = ({prop,isCip,numLimit,useThumb,cfg})->
  w=640
  h=480
  picAry = []
  ml_base = _basePicUrl isCip,cfg
  if prop.phoDl and prop.pho and prop.maxPicSz #TO TEST WITH photo server
    return genUrlFromImageServer {ml_base,prop, cfg, id:pho._id, numLimit, imgFolder:'bre', useThumb}
  else if prop.phoUrls
    for num in [0...numLimit]
      if prop.phoUrls[num]
        if useThumb and (not prop.thumbUrl)
          prop.thumbUrl = 'https:'+prop.phoUrls[num].url
        picAry.push 'https:'+prop.phoUrls[num].url
  else if prop.picUrl
    prop.$2 = genBcrePicUrl prop.picUrl,w,h
    # brePicUrl = prop.picUrl.replace('[$width]',w).replace('[$high]',h).replace('[pic#]','{{2}}')
    # # NOTE: some start with //cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCR, no protocol for native;
    # prop.$2 = brePicUrl
    # pho is the total of the images, and image index start from 0
    for num in [0...numLimit]
      if useThumb and (not prop.thumbUrl)
        prop.thumbUrl = prop.$2.replace('{{2}}',num)
      # picAry.push brePicUrl.replace('[pic#]',num)
      picAry.push '$2'+num
  prop.picUrls = picAry

###
# 过滤并选择合适的图片URLs
# @param {Array} phoUrls - 图片URLs数组，支持两种格式：
#   1. [{id:'',url:''}] bcre历史数据
#   2. [{id:0,url:'',status:'Active',permission:['Public'],sizeDesc:'Largest'},...] reso的房源图片
# @param {Boolean} isLarge - 是否使用大图
# @returns {Array} 过滤后的图片URL数组
###
module.exports.filterPhoUrls = filterPhoUrls = (phoUrls, isLarge)->
  return [] if not phoUrls or (not Array.isArray(phoUrls)) or (phoUrls.length is 0)
  
  # 定义尺寸优先级
  sizePriority = ['Medium', 'Largest', 'Large', 'LargestNoWatermark', 'Thumbnail']
  
  # 检查是否所有元素都没有id字段
  hasNoIds = phoUrls.every (photo) -> not photo?.id?
  
  # 如果所有元素都没有id字段，直接返回有效的图片
  if hasNoIds
    return phoUrls.filter (photo) ->
      return false if not photo or not photo.url
      
      # 检查状态
      return false if photo.status? and (photo.status isnt 'Active')
      
      # 检查权限
      permission = photo.perm or photo.permission
      return false if (Array.isArray(permission)) and (not permission.includes('Public'))
      
      return true
  
  # 按id分组
  groupedPhotos = {}
  for photo in phoUrls
    # 跳过无效的图片
    continue if (not photo) or (not photo.url)
    
    # 检查状态
    continue if photo.status? and (photo.status isnt 'Active')
    
    # 检查权限
    permission = photo.perm or photo.permission
    continue if (Array.isArray(permission)) and (not permission.includes('Public'))
    
    # 检查m中是否包含Statement/sheet
    continue if (photo.m?) and (/Statement|sheet/i.test(photo.m))

    # 按id分组，使用默认id如果不存在
    photoId = photo.id or '0'
    groupedPhotos[photoId] ?= []
    groupedPhotos[photoId].push(photo)
  
  # 为每个id选择最合适的图片
  result = []
  
  # 获取排序后的keys
  sortedIds = Object.keys(groupedPhotos).sort((a, b) -> Number(a) - Number(b))
  
  # 按排序后的key遍历
  for id in sortedIds
    photos = groupedPhotos[id]
    targetSize = if isLarge then 'Largest' else 'Medium'
    
    # 首先尝试找到目标尺寸的图片
    selectedPhoto = null
    
    # 如果图片有尺寸描述，按尺寸选择
    if photos[0].sizeDesc?
      # 首先尝试找到目标尺寸的图片
      selectedPhoto = photos.find((p) -> p.sizeDesc is targetSize)
      
      # 如果没有找到目标尺寸，按优先级查找
      if not selectedPhoto
        for size in sizePriority
          selectedPhoto = photos.find((p) -> p.sizeDesc is size)
          break if selectedPhoto
    else
      # 如果图片没有尺寸描述，直接使用第一张
      selectedPhoto = photos[0]
    
    # 如果找到了合适的图片，添加到结果中
    result.push(selectedPhoto) if selectedPhoto
  return result

# TODO: @rain 按照src分类生成picUrls
###*
# 根据图片URLs生成列表图片URLs
# @param {Object} params - 参数对象
# @param {Object} params.prop - 房源对象
# @param {Array} params.prop.phoUrls - 图片URLs数组
# @param {Number} params.numLimit - 图片数量限制
# @param {Boolean} params.useThumb - 是否需要缩略图
# @param {Boolean} params.isLarge - 是否使用大图
# @returns {Array} 处理后的图片URL数组
###
genListingPicUrlsByPhoUrls = ({prop, numLimit, useThumb, isLarge}) ->
  return [] unless prop?.phoUrls?.length
  
  # add decompressed phoUrls
  if libImpCommon.isBinary(prop.phoUrls)
    prop.phoUrls = libImpCommon.decompressMediaZlib(prop.phoUrls)
    if not prop.phoUrls?
      debug.error "Failed to decompress phoUrls for prop #{prop._id}"
      return []
    
  # 根据条件过滤和选择合适尺寸的图片
  filteredUrls = filterPhoUrls(prop.phoUrls, isLarge)

  # 限制图片数量
  picArr = filteredUrls
    .slice(0, numLimit)
    .map (photo) ->
      if /^http/i.test(photo.url)
        photo.url
      else
        "https:#{photo.url}"

  # 设置缩略图
  if useThumb and filteredUrls.length > 0
    # 查找第一张图片对应的缩略图
    firstPhotoId = filteredUrls[0]?.id || 0
    thumbPhoto = prop.phoUrls.find (photo) ->
      photo.id is firstPhotoId and photo.sizeDesc is 'Thumbnail'
    
    # 设置缩略图URL
    prop.thumbUrl = if thumbPhoto
      if /^http/i.test(thumbPhoto.url) then thumbPhoto.url else "https:#{thumbPhoto.url}"
    else
      picArr[0]
  else if useThumb
    prop.thumbUrl = picArr[0]

  prop.picUrls = picArr
  return picArr

genListingPicUrlsByPhoIDs = ({prop,numLimit,useThumb,isCip,cfg})->
  ml_base = _basePicUrl isCip,cfg
  picArr = []
  mui_last34 = prop.orgId?.toString().slice(-4, -2)
  mui_last12 = prop.orgId?.toString().slice(-2)
  # NOTE: OTW RESO下载的房源sid与原来oreb下载的房源sid不同
  propImgId = prop._id.slice(3)
  for imgId in prop.phoIDs
    imgUrl = "#{ml_base}/#{prop.src.toLowerCase()}/#{_getSortTimeStamp prop,cfg}/#{mui_last34}/#{mui_last12}/#{propImgId}_#{imgId}.jpg"
    picArr.push imgUrl
  # Set thumbUrl with {ml_base} placeholder for first photo
  prop.thumbUrl = "{ml_base}/#{prop.src.toLowerCase()}/#{_getSortTimeStamp prop,cfg}/#{mui_last34}/#{mui_last12}/#{propImgId}_#{prop.phoIDs[0]}.jpg" if prop.phoIDs?.length > 0
  prop.picUrls = picArr
  return picArr

#
genUrlFromImageServer = ({ml_base, id, prop, cfg, imgFolder, numLimit, useThumb})->
  picAry = []
  arr = [1..numLimit]
  # url = "#{ml_base}/img/#{ddfID.substr(-3)}/#{ddfID.substr(3)}_{{1}}.jpg?#{_getSortTimeStamp prop}"
  url = "#{ml_base}/cda/#{_getSortTimeStamp prop,cfg}/#{id.substr(-3)}/#{id.substr(3)}_{{1}}.jpg"
  thumbUrl = "{ml_base}/cda/#{_getSortTimeStamp prop,cfg}/#{id.substr(-3)}/#{id.substr(3)}_{{1}}.jpg"
  # ret = null#isNewDDFPicFormat(prop)
  replace = '{{1}}'
  prop.$1 = url
  for num in arr
    if useThumb and not prop.thumbUrl
      prop.thumbUrl = thumbUrl.replace('{{1}}',num)
    # picAry.push _ddfPicUrl ml_base,prop.ddfID.substr(3),num, prop.phomt
    if cfg.use3rdPic
      if prop?.Photo?.PropertyPhoto?.LastUpdated
        phoObj = prop?.Photo?.PropertyPhoto
      else
        phoObj = prop?.Photo?.PropertyPhoto[num]
      theirUrl = phoObj?.PhotoURL
      picAry.push theirUrl if theirUrl
    else
      picAry.push '$1'+num
  prop.picUrls = picAry

#TODO: should deperated
useOldLogic = (prop, isCip, numLimit, useThumb, cfg)->
  w=640
  h=480
  ml_base = _basePicUrl isCip,cfg
  picAry = []
  
  # TODO: ddf pA/pHA
  if ('ddf' is prop.phosrc) and (prop.ddfID)
    ddfID = prop.ddfID
    ddfID = ddfID[0] if Array.isArray ddfID
    ddfID = ddfID.toString()
    return genUrlFromImageServer {ml_base, prop, id:ddfID, cfg, numLimit, imgFolder:'cda', useThumb}
  else if ('bre' is prop.phosrc) and prop.picUrl and prop.pho
    prop.$2 = genBcrePicUrl prop.picUrl,w,h
    # brePicUrl = prop.picUrl.replace('[$width]',w).replace('[$high]',h).replace('[pic#]','{{2}}')
    # # NOTE: some start with //cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCR, no protocol for native;
    # prop.$2 = brePicUrl
    # pho is the total of the images, and image index start from 0
    for num in [0...numLimit]
      if useThumb and (not prop.thumbUrl)
        prop.thumbUrl = prop.$2.replace('{{2}}',num)
      # picAry.push brePicUrl.replace('[pic#]',num)
      picAry.push '$2'+num
  else if ml_num = prop.sid
    for num in [1..numLimit]
      if num is 1
        # NOTE: no need to replace, only one occurance
        # $3 = "#{ml_base}/mls/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg?#{_getSortTimeStamp prop}"
        $3 = "#{ml_base}/trb/#{_getSortTimeStamp prop,cfg}/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
        $3ThumbUrl = "{ml_base}/trb/#{_getSortTimeStamp prop,cfg}/#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
        if useThumb and not prop.thumbUrl
          prop.thumbUrl = $3ThumbUrl
        picAry.push $3
      else
        # prop.$4 ?= "#{ml_base}/mls/{{4}}/#{ml_num.slice(-3)}/#{ml_num}_{{4}}.jpg?#{_getSortTimeStamp prop}"
        prop.$4 ?= "#{ml_base}/trb/#{_getSortTimeStamp prop,cfg}/{{4}}/#{ml_num.slice(-3)}/#{ml_num}_{{4}}.jpg"
        if useThumb and (not prop.thumbUrl)
          prop.thumbUrl = prop.$4.replace(/\{\{4\}\}/g,num)
        # picAry.push _trbPicUrl ml_base,prop.sid+'',num, prop.phomt
        picAry.push '$4'+num
  else
    debug.info 'libProp(useOldLogic): prop no sid',prop.sid
  prop.picUrls = picAry


# Directly generate prop picUrls
module.exports.listingPicUrls = (prop,opt={},w=640,h=480)->
  picAry = []
  cfg={}
  num=1
  if opt.numLimit?
    numLimit = Math.min(num,prop.pho)
  else
    numLimit = prop.pho
  
  # TODO: @luoxiaowei: need to check and refactor
  isCip = opt.isCip
  useThumb = opt.useThumb or false
  if prop.thumbUrl
    useThumb = false
    prop.img = prop.thumbUrl if not prop.img

  # reso下载的房源图片保存在phoUrls
  if prop.phoUrls?.length
    picAry = genListingPicUrlsByPhoUrls({prop,numLimit,useThumb})
  # 历史的OTW/CLG房源图片根据phoIDs生成
  else if prop.phoIDs
    picAry = genListingPicUrlsByPhoIDs({prop,numLimit,useThumb,isCip,cfg})
  # NOTE: 2024-03-05 treb photo url changed, no longer works for new ids
  else if prop.photonumbers
    if Array.isArray(prop.photonumbers) and prop.photonumbers.length
      for i in prop.photonumbers
        picAry.push "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index=#{i}"
    else
      # for i in [0...prop.photonumbers]
      picAry.push "https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index=#{prop.photonumbers}"
  # else if prop.picNum
  else if prop.pho
    ml_base = _basePicUrl opt.isCip
    # NOTE: happens when ddf imported first but TREB not here;
    if ('ddf' is prop.phosrc) and (prop.ddfID)
      ddfID = prop.ddfID #ddfID,_id
      ddfID = ddfID[0] if Array.isArray ddfID
      for num in [1..prop.pho]
        picAry.push _ddfPicUrl ml_base,ddfID,num, prop, opt
    else if ('bre' is prop.phosrc) and prop.picUrl and prop.pho
      brePicUrl = prop.picUrl.replace('[$width]',w).replace('[$high]',h)
      for num in [0...prop.pho]
        picAry.push brePicUrl.replace('[pic#]',num)
    else if prop.src in ['RHB','EDM','CAR','OTW']
      picAry = genListingPicUrlsByPhoUrls({prop,numLimit,useThumb})
    else
      for num in [1..prop.pho]
        picAry.push _trbPicUrlNew ml_base,prop.sid+'',num, prop, opt
  if useThumb and (not prop.thumbUrl)
    prop.thumbUrl = '/img/noPic.png'
  picAry


#blcklist
#delete / new obj?
# isDDFProp = (prop)->
# #
# module.exports.hasDDFid = (prop)->
#   prop.ddfID = prop.ddfID?

module.exports.currencyFormat = currencyFormat =(value, _currency, decimals) ->
  value = parseFloat(value)
  if !isFinite(value) or !value and value != 0
    return ''
  _currency = if _currency? then _currency else '$'
  decimals = if decimals? then decimals else 2
  stringified = Math.abs(value).toFixed(decimals)
  _int = if decimals then stringified.slice(0, -1 - decimals) else stringified
  i = _int.length % 3
  head = if i > 0 then _int.slice(0, i) + (if _int.length > 3 then ',' else '') else ''
  _float = if decimals then stringified.slice(-1 - decimals) else ''
  sign = if value < 0 then '-' else ''
  digitsRE = /(\d{3})(?=\d)/g
  sign + _currency + head + _int.slice(i).replace(digitsRE, '$1,') + _float

# ["_id", "addr", "apt_num", "bdrms", "br_plus", "bthrms", "city", "cmty", "daddr", "ddfID", "favU", "gr", "lat", "lng", "lp", "lpr", "lst", "mt", "pclass", "pho", "phosrc", "picUrl", "prov", "ptype", "ptype2", "rmdaddr", "saletp", "sid", "status", "tax", "taxyr", "tbdrms", "tgr", "topTs", "topup_pts", "trbtp", "ts", "unt", "zip"]
# TODO: use model fields insted of this fields
# NOTE: for import only
fieldsBase = {
  _id:1
  addr:1
  showAddr:1
  apt_num:1
  bdrms:1
  bthrms:1
  tbthrms:1
  city:1
  origCity:1
  cmty:1
  origCmty:1
  daddr:1
  ddfID:1
  # favU:1
  gr:1
  lp:1
  lpr:1
  lst:1
  la:1 #
  la2:1 #move to props_part, keep!
  park_spcs:1
  pho:1
  phosrc:1
  phomt:1
  picUrl:1
  phoUrls:1
  thumbUrl:1
  phoIDs:1 # oreb photo show
  prov:1
  origProv:1
  ptype:1
  ptype2:1
  rmdaddr:1
  rmdpho:1
  dpred:1
  saletp:1
  sid:1
  status:1
  tbdrms:1
  tgr:1
  topTs:1
  trbtp:1
  ts:1
  unt:1
  origUnt:1
  zip:1
  topup_pts:1
  geoq:1
  bsmt:1
  mfee:1
  ohz:1
  # for DOM
  ctrdt:1
  lstd:1
  ld:1
  mt:1
  onD:1
  offD:1
  # slddt:1
  topuids:1
  id:1
  pic:1
  uid:1
  src:1
  # need for list vid render
  ytvid:1
  vurlcn:1,
  vimgcn:1,
  ltp:1,
  cmstn:1,
  br_plus:1,
  sp:1,
  market:1,#promoted rm listing
  photonumbers:1,
  phodl:1,
  PhotoDlDate:1,
  pc:1
  sldDom:1
  topMt:1
  hideInfo:1
  useTrebPhoto:1
  spcts:1
  rltr:1 # need to display in BC site 2022-05-18
  BrokerReciprocity:1
  isPOS:1
  isEstate:1
  rmSqft:1
  front_ft:1
  depth:1
  lotsz_code:1
  irreg:1
  poss_date:1
  psn:1,
  sqft:1
  del:1 # detail与list需要根据del进行房源过滤
  lsp:1   # last sold price
  lspDifPct:1 # difference between current sold price and last sold price (percentage)
  llpDifPct:1 # difference between current listing price and last sold price (percentage)
  private:1 # 楼花转让hide标记
  # TODO: @sam
  # sqft:1 #@allen (2022-06-07 15:37)
  # rmSqft:1 #disp in mapsearch and list
  # WARNING: Error: table columns not exists
  # NOTE: select column "pla" does not exist causes psql return error
  # pLA:1,
  # pMA:1,
  # pHA:1,
  # pA:1,
  # _$L:1,
  # _$M:1,
  # _$H:1,
  orgId:1 # oreb photo show
  resoSrc:1 # reso 图片判断
  MlsStatus:1
}
# bat:1
listFields = {
  # favUsr:1
  lpunt:1
  ltp:1
  lat:1
  lng:1
}
mapFields = {
  loc:1
  lat:1
  lng:1
  mt:1
  # pclass:1
  tax:1
  taxyr:1
}
miniappFields = {
  sp:1,
  sldd:1,
}
rmpropFields = {
  ltp:1
  ptp:1
  pstyl:1
  stp:1
  market:1
  _sysexp:1
  # tl:1,
  # tllck:1
}

advanceQueryFields = {
  trnst:1
  sldDom:1
  schools:1
  pc:1
  pcts:1
  bltYr1:1
  bltYr2:1
  sqft1:1
  sqft2:1
  his:1 # used in edmProperty.coffee
  uaddr:1
  merged:1
}

compareTableFields = {
  fce:1,
  sldd:1,
  bltYr:1,
  age:1,
  rmBltYr:1,
  bltYr1:1,
  bltYr2:1,
  blcny:1,
  tax:1,
  rmStoreys:1,
  sqft:1,
  taxyr:1,
  uaddr:1,
  lat:1,
  lng:1,
  rms:1,
  lkr:1,
  cac_inc:1,
  heat_inc:1,
  hydro_inc:1,
  prkg_inc:1,
  water_inc:1,
  comel_inc:1,
  tv: 1,
  wifi:1,
  poss_date:1,
  kch:1,
  mfeeInc:1,
  Locker: 1,
  lotsz:1,
  sqft1:1,
  sqft2:1,
  rmSqft: 1,
  RentIncludes:1,
  lotszUt: 1
}
listViewBaseFields = {
  _id: 1,
  addr: 1,
  bdrms: 1,
  br_plus: 1,
  bthrms: 1,
  city: 1,
  cmty: 1,
  ctrdt:1
  del: 1, # detail与list需要根据del进行房源过滤
  gr: 1,
  id: 1,
  isV: 1,
  llpDifPct: 1, # difference between current listing price and last sold price (percentage)
  lp: 1,
  ld:1
  lpr: 1,
  lsp: 1, # last sold price
  lspDifPct: 1, # difference between current sold price and last sold price (percentage)
  lst: 1,
  lstd: 1,
  ltp: 1,
  MlsStatus: 1
  mt: 1,
  offD: 1,
  ohz: 1,
  onD: 1,
  orgId: 1, # oreb photo show
  origCity: 1,
  origCmty: 1,
  origProv: 1,
  origUnt: 1,
  park_spcs: 1,
  pc: 1,
  pcts: 1,
  pho: 1,
  phodl:1,
  phoIDs: 1, # oreb photo show
  phomt: 1,
  phosrc: 1,
  PhotoDlDate:1,
  photonumbers:1,
  phoUrls: 1,
  pic: 1,
  picUrl: 1,
  prov: 1,
  ptype2:1,
  resoSrc: 1, # reso 图片判断
  saletp: 1,
  showAddr: 1,
  sid: 1,
  sldDom: 1,
  sldd:1
  sp: 1,
  spcts:1, # 首页速卖和最近成交需要按照这个做group
  sqft: 1,
  src: 1,
  status: 1,
  tbdrms: 1,
  tbthrms: 1,
  tgr: 1,
  ts: 1,
  uid: 1,
  unavail_dt:1
  unt: 1,
  vurlcn: 1,
  ytvid: 1,
  thumbUrl: 1
}
LIST_PROP_FIELDS = Object.assign {}, listFields, fieldsBase
MAP_PROP_FIELDS = Object.assign {}, mapFields, fieldsBase
COMPARE_TABLE_FIELDS = Object.assign {}, compareTableFields, fieldsBase
LIST_SIMPLE_FIELDS = Object.assign {}, listViewBaseFields
MAP_SIMPLE_FIELDS = Object.assign {}, mapFields, listViewBaseFields
#all query fields
ALL_FIELDS = Object.assign {}, miniappFields, mapFields, listFields, \
fieldsBase, rmpropFields, advanceQueryFields

module.exports.LIST_PROP_FIELDS = LIST_PROP_FIELDS
module.exports.MAP_PROP_FIELDS = MAP_PROP_FIELDS
module.exports.ALL_FIELDS = ALL_FIELDS
module.exports.COMPARE_TABLE_FIELDS = COMPARE_TABLE_FIELDS
module.exports.LIST_SIMPLE_FIELDS = LIST_SIMPLE_FIELDS
module.exports.MAP_SIMPLE_FIELDS = MAP_SIMPLE_FIELDS
# FIELD_TYPE_VALIDATOR = {
#   addr:''
# }
###
  la.agnt._id
  la.agnt.id
  la.agnt.bid
  la2.agnt._id
###
genNewLa = (la={}, flds)->
  flds ?= ['_id','id']
  agnt = []
  la.agnt ?= []
  for a in la.agnt
    tmp = {}
    for i in flds
      tmp[i] = a[i]
    agnt.push tmp
  return {
    agnt:agnt
    _id:la._id
    id:la.id
  }
module.exports.minifyPropLa = minifyPropLa = (prop)->
  if la = prop.la
    prop.la = genNewLa(la,['_id','id','bid'])
  if la2 = prop.la2
    prop.la2 = genNewLa(la2)
###
"trnst" : [
        {
            "tp" : "tram",
            "_id" : ObjectId("5e83a16f89e21d30ce86aef3"),
            "nm" : [
                "RONCESVALLES AVE AT HIGH PARK BLVD SOUTH SIDE"
            ],
            "dist" : 246
        }
    ]
###

#keep only neareast trnst.
minifyTransit =(newProp)->
  if not newProp?.trnst?.length
    return
  transits = newProp?.trnst
  # transits.sort((a,b)->
  #   return a?.dist - b?.dist
  # )
  transits.sort sortHelper.getObjectsSortFunction('dist',true)
  newProp.trnst = [{_id:transits[0]._id, dist:transits[0].dist}]

#keep school with higest score and nearst
minifySchool =(newProp)->
  if not newProp?.schools?.length
    return
  schools = newProp.schools
  newProp.schools = []
  for school in schools
    if school.fir
      newSchool = {}
      for fld in ['_id','fir','ele','hgh','c','mid']
        newSchool[fld] = school[fld] if school[fld]
      newProp.schools.push newSchool if Object.keys(newSchool).length>0

module.exports.narrowFields = narrowFields = (origProp)->
  newProp = {_id:origProp._id.toString()}
  for k in Object.keys ALL_FIELDS
    newProp[k] = origProp[k] if origProp[k]?
  # 真楼花将m和m_zh字段写入，用于首页显示
  if origProp.market?.rmProp
    newProp.m = origProp.m if origProp.m
    newProp.m_zh = origProp.m_zh if origProp.m_zh
  #fix missting ts
  if not newProp.ts
    newProp.ts = inputToDate(newProp.onD or newProp.mt)
  if ((newProp.lat is 0) or (newProp.lng is 0)) and newProp?.loc?.coordinates?[1]
    newProp.lat = newProp.loc.coordinates[1]
    newProp.lng = newProp.loc.coordinates[0]
  if (not newProp.lat?) or (not newProp.lng?)
    newProp.lat = 0
    newProp.lng = 0
  minifyPropLa(newProp)
  minifyTransit(newProp)
  minifySchool(newProp)
  #minify transit, nearest trnst:[dist，_id]
  #minify school,school 最近的和最好的。 schools:[_id,fir,dist]
  #sldDom,pc
  newProp


parsePropLa = (newProp,baseProp,k,v, prefix)->
  newProp[prefix] ?= {agnt:[]}
  str = prefix+'_0agnt_id'
  if k is prefix+'_id'
    newProp[prefix].id = v
  else if k is prefix+'_iid'
    newProp[prefix]._id = v
  # NOTE: ignore other, handle at onece
  else if new RegExp('^'+str).test k
    for i in [0,1,2]
      fld = prefix+'_'+i+'agnt_iid'
      if (val = baseProp[fld]) or (baseProp[prefix+'_'+i+'agnt_id'])
        agnt = {}
        agnt.id = val
        agnt._id = baseProp[prefix+'_'+i+'agnt_iid']
        agnt.bid = baseProp[prefix+'_'+i+'agnt_bid']
        newProp[prefix].agnt.push agnt

# one prop may have multip rows in l, with different fav or oh
# TODO: sql数据库整理 favU 字段至prop_fav，添加 favcnt 字段收集收藏人数
module.exports.convertSqlToMongo = convertSqlToMongo = (l) ->
  unless l?.length
    return l
  ret = {}
  for prop in l
    newProp = ret[prop._id] or {}
    for k,v of prop
      if k in ['top_ts','ddf_i_d','pic_url','orig_addr',\
      'orig_city','orig_unt','orig_prov','orig_cmty']
        newProp[toCamelCase(k)] ?= v
      else if k in ['geoq','gr','tgr']
        if parseInt(v) == parseFloat v
          newProp[k] = parseInt v
        else
          newProp[k] = v
      # else if k in ['ptype2','saletp','trbtp','pclass','bsmt','topuids','photonumbers']
      else if k in ['ptype2','saletp','trbtp','bsmt','topuids','photonumbers']
        newProp[k] ?= v?.split(',') or []
      else if k is 'pic'
        newProp[k] ?= JSON.parse(v) if v
      else if k in ['lat','lng']
        newProp[k] = parseFloat v
      else if /^la/.test k
        # console.log k
        prefix = k.split('_')?[0]
        # console.log prefix
        parsePropLa(newProp,prop,k,v,prefix)
      else if /^fav/.test k
        if k is 'favuid'
          newProp.favU?=[]
          fav = {u: prop.favuid, g:prop.favgrp}
          findFav = newProp.favU.find (f)->
            return (f.u.toString() is fav.u.toString()) and (f.g is fav.g)
          unless findFav
            newProp.favU.push fav
      else if /^oh/.test k
        if k is 'oh'
          newProp.ohz?=[]
          oh = {f:prop.oh, t:prop.oht,tp:prop.ohtp,l:prop.ohl}
          if prop.ohidx>=0
            newProp.ohz[prop.ohidx] = oh
      else if (typeof(v) in ['number', 'string','boolean']) or (typeof(v?.getMonth) is 'function')
        newProp[k] ?= v
    ret[prop._id] ?= newProp

  return Object.values(ret)

module.exports.convertsqlFavUToMongo = convertsqlFavUToMongo = (favList,propList)->
  propMap = {}
  for prop in propList
    propMap[prop._id] = prop
  for fav in favList
    continue unless prop = propMap[fav._id]
    prop.favU?=[]
    fav = {u: fav.uid, g:fav.grp}
    findFav = prop.favU.find (f)->
      return (f.u.toString() is fav.u.toString()) and (f.g is fav.g)
    unless findFav
      prop.favU.push fav


module.exports.revokeRMPropRecord = (cfg={},cb)->
  prop = cfg.prop
  return cb 'No id' unless prop?.id
  return cb null if not /^RM/.test prop?.id
  # console.log cfg
  # return cb null,'done'
  # TODO: need to check user is owner
  q = {_id:prop._id}
  unless prop._id
    if not prop.uid
      return cb null,'done'
    q = {id:prop.id, uid: new ObjectId(prop.uid)}
  if cfg.changeStatus
    update = {$set:{
      status:'U',
      'market.st':'Revoked'
      }}
    update.$set.lst = prop.lst if prop.lst
    cfg.db.updateOne q,update,{writeConcern:{w:1}},(err,ret)->
      if err
        return cb err
      return cb err,ret
    return
  cfg.db.deleteOne q,{writeConcern:{w:1}},(err,ret)->
    if err
      return cb err
    return cb err,ret


module.exports.importRMPropRecord = (cfg,cb)->
  prop = cfg.prop
  # return cb(null,'No id') unless prop._id
  # return cb(null,'No addr') unless (prop.prov and prop.city)
  # newProp = narrowFields prop
  debug.info ' import: '+prop.id,prop.addr
  prop = propSearch.convertSingleRMListing(prop)
  if prop.src is 'TRB'
    prop.src = 'RMTRB'
  # return cb null,'done'
  set = Object.assign {}, prop
  # delete set._id
  cfg.db.replaceOne {_id:prop._id},set,{writeConcern:{w:1},upsert:true},(err,ret)->
    if err
      return cb err
    if not cfg.dbNm
      return cb null, ret
    # console.log('import to sql part')
    return cb null, ret


module.exports.importPropRecord = importPropRecord = (cfg,cb)->
  prop = cfg.prop
  return cb(null,'No id') unless prop._id
  return cb(null,'No addr') unless (prop.prov and prop.city)
  return cb(null,'merged') if prop.merged
  lastTwoYear = new Date(Date.now()-(365*2+30)*24*3600*1000)
  if new Date(prop.mt) < lastTwoYear
    # debug.debug 'do not import too old props'
    return cb(null,'too old')
  newProp = narrowFields prop
  set = Object.assign {}, newProp
  #if not delet _id MongoError: Performing an update on the path '_id' would modify the immutable field '_id'
  cfg.db.replaceOne {_id:prop._id},set,{writeConcern:{w:1},upsert:true},(err,ret)->
    return cb err,ret

#TODO: fix this in watchPropAndUpdateMongo
module.exports.deletePropRecord = deletePropRecord = (cfg,cb)->
  prop = cfg.prop
  return cb(null,'No id') unless prop._id
  cfg.db.deleteOne {_id:prop._id},(err,ret)->
    return cb err,ret

module.exports.deleteAndBackupEvowProp = deleteAndBackupEvowProp = ({id,uid,isAdmin,RMdb, db, db_deleted},cb)->
  return cb('db collection needed') unless  db
  return cb('db_deleted collection needed') unless db_deleted
  return cb() unless id
  try
    # NOTE: DDF基本会被merge BRE不进入properties
    if /^TRB/.test id
      q = {_id: id}
      coll = db
    else
      q = {id, uid}
      coll = RMdb
      if isAdmin#admin dont check uid
        q = {id}
    prop = await coll.findOne q
    if not prop
      return cb(null,'No id')

    if q._id and (q._id is not prop._id)
      debug.error 'deleteAndBackupEvowProp _id is not match', q._id, prop._id
      return cb 'Not matched _id'
    # NOTE: MongoError: properties_deleted.updateOne MongoServerError: Performing an update on the path '_id' would modify the immutable field '_id'
    delete prop._id
    debug.info 'Delete prop:',q,prop.id
    await db_deleted.updateOne q,{$set:prop},{upsert:true}
    ret = await coll.deleteOne q
    return cb(null,ret)
  catch error
    return cb(error)
      

module.exports.convert_rm_imgs = convert_rm_imgs = ($scope, imgs, opt) ->
  if opt is 'set'
    # imgs is array of img address
    return {} unless imgs
    ret = {l:[]}
    if $scope.userFiles
      ret.base = $scope.userFiles.base
      ret.fldr = $scope.userFiles.fldr
    else if $scope.formData['pic']
      ret.base = $scope.formData['pic'].base
      ret.fldr = $scope.formData['pic'].fldr
    for i in imgs
      #TODO: DDF use different format #1234
      if i.indexOf('f.i.realmaster') > -1
        ret['l'].push(i.split('/').slice(-1)[0])
      else if (i.indexOf('m.i.realmaster.com') > -1) or (i.indexOf('img.realmaster') > -1)
        ret.mlbase = "https://img.realmaster.com/mls" # TODO: @Rain why use http fixed? A:dont have https before
        tmp = i.split('/')
        ret['l'].push '/' + tmp[4]
        # {num}/{ml_num.slice(-3)}/{ml_num}_#{num}.jpg
        # {num}/{ml_num.slice(-3)}/{ml_num}.jpg
      else
        ret['l'].push libPropertyImage.replaceRM2REImagePath(i)
    return ret
  else if (opt is 'reset')
    return [] unless imgs?.l
    ret = []
    base = imgs.base
    # fldr = imgs.fldr #  not used here, used in delete
    mlbase = imgs.mlbase
    ml_num = imgs.ml_num or $scope.ml_num
    for i in imgs.l
      # ml case
      if i[0] is '/'
        picIndex = parseInt(i.substr(1))
        if picIndex is 1
          ret.push mlbase + i + '/' + ml_num.slice(-3) + '/' + ml_num + '.jpg'
        else
          ret.push mlbase + i + '/' + ml_num.slice(-3) + '/' + ml_num + '_' + i.substr(1) + '.jpg'
      else if i.indexOf("http") > -1
        ret.push i#libPropertyImage.replaceRM2REImagePath(i)
      #upload case
      else
        ret.push libPropertyImage.replaceRM2REImagePath(i)
    return ret
  else
    return {}

module.exports.getBasePropDetailOpt = (req,user)->
  lang = req.param('locale') or req.param('lang') or req.locale()
  # NOTE: setLocale has supported lang check, no need to do check here
  req.setLocale lang
  req.setupL10n() unless req._ab
  # req.setupL10n() unless req._ab and req._
  opt =
    # body:req.body
    suffix:if (req.getDevType() is 'app') then 'A' else ''
    user:user
    id:req.param('id') or req.param('_id')
    sid:req.param('sid')
    nt:req.param('nt') #no translate
    slp:req.param('slp') #simplfyPtp
    apisrc:req.param('apisrc') #requst src
    devType:req.getDevType()
    isShare:req.param('share') or req.param('ec')
    useThumb:req.param('useThumb')
    fubSrc: req.param('fub_src')
    isChinaIP:req.isChinaIP()
    locale:lang
    isPad:req.isPad()
    isAllowedPropAdmin:req.isAllowed('propAdmin',user)
    isAllowedVipUser:req.isAllowed('vipUser',user)
    isAllowedShowSoldPrice:req.isAllowed 'showSoldPrice',user
    isVipRealtor:req.isAllowed('vipRealtor',user)
    isAllowedAdmin:req.isAllowed('admin',user)
    isDevGroup:req.isAllowed('devGroup',user)
    isEdm:req.param('edm')
    isSearchEngine:helpers.isSearchEngine(req)
    _ab:(a,b)->req._ab a,b
    l10n:(a,b)->req.l10n a,b
    # isAllowedEventAdmin:req.isAllowed('eventAdmin',user)
    # auth='admin'/'user'/'vip'<-access1&access2
    # switch auth
    # setupL10n:(a,b)->req.setupL10n a,b
    # _:(a,b)->req._ a,b
    # locale:req.locale
    # cookies:req.cookies
    # headers:req.headers
    # hasRole:(role,u)->req.hasRole role,u
    # isAllowed:(role,u)->req.isAllowed role,u
    # hasRoleRealtor:req.hasRole('realtor',user)
    # hasRoleAdmin:req.hasRole('_admin',user)
    # isAllowedEventAdmin:req.isAllowed('eventAdmin',user)
    # fullNameOrNickname:(u)->req.fullNameOrNickname u
    # userCpny:(u)->req.userCpny u
  return opt


module.exports.isCondo = isCondo = (prop) ->
  return false unless (ptype2 = prop.ptype2_en or prop.ptype2)
  return /condo|apartment/i.test ptype2

module.exports.get_shared_to_from_share = (src)->
  # vcwb / vcdz / vcyx
  dataTo =
    'W' : 'wb'
    'Y' : 'yx'
    'I' : 'dz'
    'A' : 'app'
  dataTo[src] or src or ''

module.exports.generate_shared_to_from_share = (src)->
  dataTo =
    '58'     : 'W'
    'uhouzz' : 'Y'
  dataTo[src] or 'A'

module.exports.generate_share_url_from_data = (req,secret,data)->
  error = (err)->
    return {ok:0, err:err}
  unless data.tp and data.id #and data.to
    debug.error 'Error: Encrypt invalid data!'
    error 'No id or type'
  dataTp =
    'mylisting'   : 'M'
    'listing'     : 'L'
    'listingList' : 'T'
    'wecard'      : 'W'
  message = {}
  message.r = data.id
  message.h = generate_shared_to_from_share dataTo[data.to]
  message.e = dataTp[data.tp] or error('unknown share info')
  message.a = data.uid #if data.uid
  url = req.shareHost() #i.realmaster.com "http://" +
  # secret = config.share?.secret
  message = JSON.stringify message
  # console.log "----------------\nshare Encrypt:" + message + '-\nsecret:' + secret
  encoded = checksum.shareEncode(message,secret)
  if data.tp in ['listing','listingList', 'mylisting']
    # url += '/html/propDetailPage.html?ec='+encoded+'&'
    url += '/1.5/prop/detail?ec='+encoded+'&'
  else
    url += '/mp/'+encoded
    url += '?'
  for i in ['aid','wDl','sgn','lang','dl','ownerid','wSign','uid'] #uid?
    if v = req.param i
      url += i+'='+v+'&'
  return {ok:1, url:url}


# module.exports.setUpUserFav = (uid='', l)->
#   if not uid
#     return
#   if l? and Array.isArray l
#     for p in l
#       if p.fav is true
#         continue
#       p.fav = false
#       p.favGrp = []
#       for fav in (p.favU or [])
#         if fav?.u?.toString() is uid.toString()
#           p.fav = true
#           p.favGrp.push fav?.g
#           # break
#       delete p.favUsr
#       delete p.favU

module.exports.setAdrltr2Prop = ({items,ul}) ->
  adrltrs = {}
  for u in ul
    adrltrs[u._id] = u
  for p in items
    unless p.adrltr
      p.adrltr = adrltrs[p.uid] if adrltrs and adrltrs[p.uid]

module.exports.ptypeArray = [
  'Residential',
  'Exclusive',
  'Assignment',
  'Commercial',
  'Other'
]


# NOTE: not widely used, reformat lst usign prop.MlsStatus or obj.lst
# case1, prop.lst not parsed
# case2, reimport master record
# TODO: remove
module.exports.formatLst=(obj)->
  lstMapping =
    'Active':'New'
    'Cancel Protected':'Cp'
    'Collapsed':'Clp'
    'Expired':'Exp'
    'Hold all Action':'Haa'
    'Terminated':'Ter',
    'Sold':'Sld'
    'Pending':'Pnd' # Sld检索时需要增加pending筛选
    'Rented':'Lsd'
    'Active Under Contract':'Auc' # New检索时需要增加closed筛选
    'Closed':'Cld'  # Sld检索时需要增加closed筛选
  if abbr = lstMapping[obj.MlsStatus or obj.lst]
    obj.lst = abbr
  else
    obj.lst = obj.MlsStatus or obj.lst


strFormatDate = (now) ->
  ret = now.getFullYear()+'-'
  ret += ('0'+(now.getUTCMonth()+1)).slice(-2)+'-'
  ret += ('0' + now.getUTCDate()).slice(-2)
  return ret

exports.isPassed = isPassed = (oh) ->
  now = new Date()
  nowOh = strFormatDate(now)
  currentOh = oh
  # here "2022-02-03 13:00" > "2022-02-03 13:00" 1>b
  # console.log('+++++',nowOh,currentOh)
  if (nowOh > currentOh)
    return true
  return false

module.exports.nearestOhDate = (prop) ->
  ohz = prop.ohz
  return false unless ohz
  for oh in ohz
    return oh unless isPassed(oh.t)
  return null

module.exports.processProps = (props,{isCip}) ->
  if props and props.length
    for prop in props
      genListingPicReplaceUrls(prop,isCip,{num:1,useThumb:true,isPhone:true})
      if isRMListing(prop)
        prop.img = prop.picUrls?[0] or '/img/noPic.png'
      else
        prop.img = prop.thumbUrl or '/img/noPic.png'
#return false, not display in autocomplete/hist list
# 1 status==A no limit
# 1.5 TREB stats != A, <2 year
module.exports.filterPropSpLstField = filterPropSpLstField = ({isAllowedPropAdmin,isAllowedVipUser}, prop)->
  # if (prop.status_en or prop.status) isnt 'A'
  prop.showSoldPrice = true
  # if prop.sp and  #(prop.prov in ['British Columbia','BC'])
  # 2020-11-26 TRB dont show\ # not VIP user
  if isAllowedPropAdmin #or configShowSoldPrice
    return true
  if isAllowedVipUser
    return true
  #2022-08-11 handle bc site sold prop not display sold price
  # if (prop.status_en or prop.status) isnt 'A'
  #   if not /^TRB/.test prop._id #prop.isTRBProp
  #     prop.showSoldPrice = false
  #     delete prop.sp
  #     return false
  #   else
  #     if (new Date(prop.mt) < new Date(Date.now() - 220752000000))
  #     # 7 years 7*365*24*3600*1000
  #       return false
  return true

#addpend search q according to access
###
# 根据用户权限和销售状态过滤房产搜索条件
# @param {Object} params - 输入参数
# @param {Boolean} params.isAllowedPropAdmin - 是否为管理员用户
# @param {Boolean} params.isAllowedVipUser - 是否为VIP用户
# @param {Object} params.q - MongoDB查询条件对象
# @param {String} params.favSort - 销售状态描述('sale'|'sold'|'delisted')
# @return {Boolean} 始终返回true
###
module.exports.filterPropInSearch = ({isAllowedPropAdmin, isAllowedVipUser, q, favSort})->
  # 计算两年前的时间点,用于限制普通用户查询范围
  # 2年 = 63072000000ms (2 * 365 * 24 * 60 * 60 * 1000)
  twoYears = new Date(Date.now() - 63072000000)
  # 判断用户类型
  isNormalUser = not (isAllowedPropAdmin or isAllowedVipUser)
  # 根据销售状态构建查询条件
  switch favSort?.toLowerCase()
    when 'sale'
      # 在售房源
      q.status = 'A'
    when 'sold'
      # 已售房源(包括已售、待售、关闭、已租)
      q.status = 'U'
      q.lst = {$in: ['Sld', 'Pnd', 'Cld', 'Lsd']}
      # 普通用户只能查看两年内的TREB房源
      if isNormalUser
        q._id = /^TRB/
        q.mt = {$gte: twoYears}
    when 'delisted'
      # 下架房源(不包括已售、待售、关闭、已租)
      q.status = 'U' 
      q.lst = {$nin: ['Sld', 'Pnd', 'Cld', 'Lsd']}
      # 普通用户只能查看两年内的TREB房源
      if isNormalUser
        q._id = /^TRB/
        q.mt = {$gte: twoYears}
    else
      # 未指定销售状态时的默认处理
      if isNormalUser
        # 普通用户可以查看:
        # 1. 所有在售房源
        # 2. 两年内的TREB房源
        q.$or = [
          {status: 'A'}
          {status: 'U', _id: /^TRB/, mt: {$gte: twoYears}}
        ]
  return true

module.exports.filterPropDphoField = filterPropDphoField = (prop,opt)->
  if (not (opt?.isAllowedPropAdmin or opt?.isRealGroup)) and (prop?.rmdpho is 'N')
    prop.pho = 0
    prop.vturl = null
    prop.dpho = 'N'
    # thumbUrl,picUrls,pic.l为图片显示字段，存在的情况下应一起重置
    prop.thumbUrl = '' if prop.thumbUrl?
    prop.picUrls = [] if prop.picUrls?
    prop.pic.l = [] if prop.pic?.l?
    delete prop.photonumbers #NOTE: genOtherListingUrls will use photonumbers if no pho and old prop

module.exports.ADDR_TO_ORIG_FLD_MAPPING = ADDR_TO_ORIG_FLD_MAPPING=
  {
    'addr':'origAddr',
    'unt':'origUnt',
    'city':'origCity',
    'prov':'origProv',
    'cmty':'origCmty',
    'region':'origRegion',
    'st':'origSt',
    'st_num':'origStNum',
    'st_sfx': 'origStSfx'
  }


PTYPE_ABBR_TO_FULL= {
  'r':'residential'
  'b':'commercial'
  'o':'other'
}

PTYPE_FULL_TO_ABBR= {}
for k, v of PTYPE_ABBR_TO_FULL
  PTYPE_FULL_TO_ABBR[v] = k

module.exports.getPtypeFull = getPtypeFull = (ptype)->
  return unless ptype
  ptypeFull = PTYPE_ABBR_TO_FULL[ptype.toLowerCase()]
  if ptypeFull
    return strCapitalize(ptypeFull)
  ptype

module.exports.getPtypeAbbr = getPtypeAbbr = (ptype)->
  return unless ptype
  ptypeAbbr = PTYPE_FULL_TO_ABBR[ptype.toLowerCase()]
  return ptypeAbbr or ptype

module.exports.deleteCopiedGeoInfoInSource = (record)->
  for fld in ['lat','lng','geoq','faddr']
    delete record[fld]
TRADE_MARKS = {
  ddf: {
    desc: 'The listing data above is provided under copyright by the Canada Real Estate Association. The listing data is deemed reliable but is not guaranteed accurate by Canada Real Estate Association nor RealMaster. MLS®, REALTOR® & associated logos are trademarks of The Canadian Real Estate Association.',
    poweredBylogoPath: '/img/prop/powered_by_realtor.svg'
    poweredByAlt: 'Powered by: REALTOR.ca'
    poweredBylogoWidth: '125'
  },
  treb: {
    desc: 'The listing data is provided under copyright by the Toronto Real Estate Board. The listing data is deemed reliable but is not guaranteed accurate by the Toronto Real Estate Board nor RealMaster.',
  },
  bcre: {
    desc: 'This representation is based in whole or in part on data generated by the Chilliwack District Real Estate Board, Fraser Valley Real Estate Board or Greater Vancouver REALTORS® which assumes no responsibility for its accuracy',
    poweredBylogoPath: '/img/home/<USER>'
    poweredByAlt:''
    poweredBylogoWidth: '105'
  },
  rhb: {
    desc: 'The listing data is provided under copyright by the REALTORS® Association of Hamilton-Burlington. The listing data is deemed reliable but is not guaranteed accurate by the REALTORS® Association of Hamilton-Burlington nor RealMaster.',
  },
  rm: {
    desc: 'This information is uploaded from registered users. RealMaster cannot in any way guarantee its accuracy or timeliness.',
  }
}

module.exports.setupTradeMarkFields = (prop) ->
  if prop.isTRBProp
    tp = 'treb'
  else if prop.isBREProp
    tp = 'bcre'
  else if prop.isRMProp
    tp = 'rm'
  else if prop.isDDFProp
    tp = 'ddf'
  else if prop.isRHBProp
    tp = 'rhb'

  if tp and (trademark = TRADE_MARKS[tp])
    prop.trademarkDesc = trademark.desc
    prop.poweredBylogoPath = trademark.poweredBylogoPath
    prop.poweredByAlt = trademark.poweredByAlt
    prop.poweredBylogoWidth = trademark.poweredBylogoWidth
    
hasBestSchool = (schools) ->
  return false unless schools
  for school in schools
    return true if school.fir and parseInt(school.fir) >= 9

isNearMtr = (trnst) ->
  return false unless trnst
  for t in trnst
    return true if t.dist <= 1000

isSoldFast = (prop) ->
  {sldDom, sldd, saleTpTag_en} = prop
  SOLD_FAST_DAYS = 10
  # Allen：如果是Delisted，不显示sold fast
  if saleTpTag_en is 'Delisted'
    return false
  if (typeof sldDom is 'number') and (sldDom <= SOLD_FAST_DAYS)
    return true
  return sldd and computeDayDiff(sldd, new Date()) <= SOLD_FAST_DAYS

BEST_SCHOOL = 'Best School'
SOLD_FAST = 'Sold Fast'
NEAR_MTR = 'Near MTR'
PRICE_OFF = 'Price Off'

module.exports.setupPropTags = (prop,{l10n}) ->
  {schools,trnst,pc,sldDom,lst,saleTpTag_en} = prop
  tags = []
  tags.push BEST_SCHOOL if hasBestSchool schools
  tags.push SOLD_FAST if isSoldFast prop
  tags.push NEAR_MTR if isNearMtr trnst
  tags.push PRICE_OFF if pc and (pc < 0) and (lst is 'Pc')
  prop.tags = tags

  isSale = /Sale/.test (prop.saletp_en or prop.saletp)
  calcDecimalPlaces=(num)->
    if isSale
      return parseInt(num)
    else
      return (num).toFixed(1)
    # 判断使用sp时需判断状态为U
  if prop.sp and prop.status is 'U'
    price = prop.sp
  else if isSale
    price = prop.lp
  else
    # NOTE: some BRE prop not have lpr but lp(3150)
    price = prop.lpr or prop.lp
  if price
    if ('number' is typeof prop.sqft)
      prop.pricePerSqft = calcDecimalPlaces(price / prop.sqft)
    else if prop.rmSqft
      prop.pricePerSqft ='~'+calcDecimalPlaces(price / prop.rmSqft)
    else if (prop.sqft1 and prop.sqft2)
      if prop.sqft1 is prop.sqft2
        prop.pricePerSqft = calcDecimalPlaces(price / prop.sqft1)
      else
        prop.pricePerSqft = "#{calcDecimalPlaces(price / prop.sqft2)}-\
          #{calcDecimalPlaces(price / prop.sqft1)}"
    else if (prop.rmSqft1 and prop.rmSqft2)
      if prop.rmSqft1 is prop.rmSqft2
        prop.pricePerSqft ='~'+calcDecimalPlaces(price / prop.rmSqft1)
      else
        prop.pricePerSqft = "~#{calcDecimalPlaces(price / prop.sqft2)}-\
          #{calcDecimalPlaces(price / prop.sqft1)}"

  unless isRMListing(prop)
    prop.formatedMt = formatUpdatedMt(prop.lup or prop.mt or prop.ts or prop._mt,l10n)

module.exports.setupRMListingFields = (prop,{l10n}) ->
  {ltp,cmstn} = prop
  if ltp is 'assignment'
    rmPropDesc = l10n 'Assignment'
  else if ltp is 'exlisting'
    rmPropDesc = l10n 'Exclusive'
  else if (ltp is 'rent') and not cmstn
    rmPropDesc = l10n 'Landlord'
  prop.rmPropDesc = rmPropDesc

module.exports.filterAddress = (prop,opt)->
  # return empty addr/unt/slice zip if prop daddr is N
  if (not (opt?.isAllowedAdmin or opt?.isRealGroup or opt?.isAllowedVipUser))\
      and (prop?.daddr is 'N' or prop?.rmdaddr is 'N')
    prop.addr = ''
    prop.unt = ''
    prop.zip = (''+prop.zip).slice(0,3) # zip may not be a string
    prop.daddr = 'N'
    prop.vturl = null
  prop

module.exports.filterSearchParams = (params)->
  for key,value of params
    if (value is '') or (value is null) or (Array.isArray(value) and (value.length is 0))
      delete params[key]
  if bbox = params.bbox
    bbox[1] = Number(bbox[1])
    bbox[3] = Number(bbox[3])
    bbox[0] = formatLng(bbox[0])
    bbox[2] = formatLng(bbox[2])
    params.bbox = bbox
  if min_lp = params.min_lp
    try
      min_lp = parseInt(min_lp)
      params.min_lp = min_lp
    catch err
      delete params.min_lp
  if max_lp = params.max_lp
    try
      max_lp = parseInt(max_lp)
      params.max_lp = max_lp
    catch err
      delete params.max_lp

# TODO: fix naming
module.exports.getrecos = ({recos,isCip,shareHostNameCn},cb) ->
  exclusives = []
  toplistings = []
  topExclusives = []
  for reco in recos
    if (['exlisting','assignment'].indexOf(reco.ltp) > -1) or (reco.ltp is 'rent' and reco.cmstn)
      exclusives.push reco
    if reco.ltp is 'MLS'
      toplistings.push reco
    if ['exlisting'].indexOf(reco.ltp) > -1
      topExclusives.push reco
    if /^RM/.test(reco.id)
      reco.pic.ml_num = reco.sid or reco.ml_num
      scope = {ml_num:reco.sid}
      reco.thumbUrl = convert_rm_imgs(scope, reco.pic, 'reset')[0] or '/img/noPic.png'
    else if (reco.sid)
      # reco.img = listingPicUrls(reco, {isCip,isPad:req.isPad()})[0] || '/img/noPic.png'
      # NOTE: could be toplisting, has thumb
      if not reco.thumbUrl
        genListingPicReplaceUrls(reco,isCip,{num:1,useThumb:true,isPhone:true,shareHostNameCn})
      reco.thumbUrl ?= '/img/noPic.png'
  return {exclusives,toplistings,topExclusives}

METERS_TO_FEET = 3.28084
exports.getRooms = ({rms,devType})->
  newRms = []
  for rm in rms
    rm ?= {}
    if devType isnt 'app'
      rm.area = (rm.h * rm.w).toFixed(2)
      rm.area_ft = (rm.h * METERS_TO_FEET * rm.w * METERS_TO_FEET).toFixed(2)
    if not isNaN(parseFloat rm.h)
      rm.h = parseFloat rm.h
      rm.h_ft = (rm.h * METERS_TO_FEET).toFixed(2)
      rm.h = rm.h?.toFixed(2)
    if not isNaN(parseFloat rm.w)
      rm.w = parseFloat rm.w
      rm.w_ft = (rm.w * METERS_TO_FEET).toFixed(2)
      rm.w = rm.w?.toFixed(2)
    rm.d = rm.d.join(' ') if Array.isArray rm.d
    newRms.push rm
  return newRms

PROP_SORT_TYPES =
  'auto-ts':1,
  'ts-desc':1,
  'ts-asc':1,
  'lp-desc':1,
  'lp-asc':1,
  'spcts-desc':1,
  'spcts-asc':1
  'mt-desc':1,
  'mt-asc':1,
  'lpr-desc':1,
  'lpr-asc':1,
  'dist-desc':1,
  'dist-asc':1,
  'sortOrder-desc':1,
  'market.sortOrder-desc':1
  'pcts-desc':1,
###
@param {string} sort - sort parameter from post body or url
@return {string} correct prop search sort value
###
exports.getPropSearchSort = (sort)->
  # sort:auto -> auto-ts
  return if PROP_SORT_TYPES[sort] then sort else 'auto-ts'


STATUS_CATEGORIES =
  SOLD: 'Sold'
  LEASED: 'Leased'
  PRICE: 'Price Changed'
  NEW: 'New Sale'
  LEASE: 'New Rent'
  OTHER: 'Other'
  DELISTED: 'Delisted'
  UNKNOWN: 'Unknown'
  WITHDRAWN: 'Withdrawn'
exports.STATUS_CATEGORIES = STATUS_CATEGORIES

# for push message purpose, status is not standard, eg. Sold Over Asking
# @return *category
# @return *statusLongName
exports.getStatusLongName = getStatusLongName = ({prop,historyRecord})->
  if not (lastHis = historyRecord)
    throw new Error('historyRecord is required')
  category = STATUS_CATEGORIES.OTHER
  if lastHis.s is 'Pc' # shall use global constant enum
    category = STATUS_CATEGORIES.PRICE
    statusLongName = 'Price Change'
  else if lastHis.s in ['Sld','Pnd','Cld','Sold']
    category = STATUS_CATEGORIES.SOLD
    if prop?.sp > prop?.lp
      statusLongName = 'Sold Over Asking'
    else if prop?.sldDom? and prop.sldDom <= 7
      statusLongName = "SOLD IN DAYS"
    else
      statusLongName = 'Sold'
  else if lastHis.s is 'Lsd'
    category = STATUS_CATEGORIES.LEASED
    statusLongName = 'Leased'
  else if lastHis.s in ['New','Active','Auc']
    if prop.saletp.indexOf('Sale') >= 0
      category = STATUS_CATEGORIES.NEW
    else if prop.saletp.indexOf('Lease') >= 0
      category = STATUS_CATEGORIES.LEASE
    else
      category = STATUS_CATEGORIES.UNKNOWN
    if lastHis.o
      statusLongName = 'Return to Market'
    else
      if prop?.prevIDs
        statusLongName = 'Relisted'
      else
        statusLongName = 'New Listed'
  else if lastHis.s is 'Exp' or lastHis.s is 'Expired'
    statusLongName = 'Expired'
  else if lastHis.s is 'Ext'
    statusLongName = 'Extended' #belong to OTHER
  else if lastHis.s is 'Sus'
    statusLongName = 'Suspended'
  else if lastHis.s is 'Cp'
    statusLongName = 'Cancel Protected'
  else if lastHis.s is 'Haa'
    statusLongName = 'Hold All Action'
  else if lastHis.s is 'Ter' or lastHis.s is 'Terminated'
    statusLongName = 'Terminated'
    category = STATUS_CATEGORIES.DELISTED
  else if lastHis.s is 'Chg' and lastHis.n is 'U'
    statusLongName = 'Off Market'
    category = STATUS_CATEGORIES.DELISTED
  else if lastHis.s is 'Dft'
    statusLongName = 'Deal Fall Through' #belong to OTHER
  else if lastHis.s is 'Clp'
    statusLongName = 'Collapsed' #belong to OTHER
  else if lastHis.s is 'Sc' or lastHis.s is 'Sold in Condition'
    statusLongName = 'Sold in Condition' #belong to Sold
    category = STATUS_CATEGORIES.SOLD
  else if lastHis.s is 'Sce'
    statusLongName = 'Sold in Condition w/escape' #belong to Sold
    category = STATUS_CATEGORIES.SOLD
  else if lastHis.s is 'Lc'
    statusLongName = 'Leased in Condition' #belong to leased
    category = STATUS_CATEGORIES.LEASED
  else if lastHis.s is 'Wd' or lastHis.s is 'Withdrawn'
    statusLongName = 'Withdrawn'
    category = STATUS_CATEGORIES.WITHDRAWN
  else
    category = STATUS_CATEGORIES.UNKNOWN
    statusLongName = lastHis.s
  return {statusLongName,category}


iconCharToType = {
  S: 'Semi-Detached'
  D: 'Detached'
  T: 'Townhouse' # Freehold and condo townhouse
  C: 'Condo'
  B: 'Business'
  P: 'PreCon'
  O: 'Other'
}
typeToIconChar =
  'Semi-Detached': 'S'
  'Detached': 'D'
  'Townhouse': 'T'
  'Condo': 'C'
  'Business': 'B'
  'Retail': 'B'
  'Office': 'B'
  'Industrial': 'B'
  'Business': 'B'
  'PreCon': 'P'
  'Other': 'O'

# sort to order: S D T C B P O
_sortIconChar = (b,a)->
  return 0 if a is b
  return -1 if a is 'O'
  return 1 if b is 'O'
  return -1 if a is 'P'
  return 1 if b is 'P'
  return -1 if a is 'B'
  return 1 if b is 'B'
  return -1 if a is 'C'
  return 1 if b is 'C'
  return -1 if a is 'T'
  return 1 if b is 'T'
  return -1 if a is 'D'
  return 1 if b is 'D'
  return -1 if a is 'S'
  return 1 if b is 'S'
  return 0

exports.getStandardTypes = getStandardTypes = (prop)->
  stdTypes = []
  return stdTypes unless prop?.ptype2
  for tps in prop.ptype2
    tps ?= ''
    tps = tps.split ','
    for tp in tps
      if stp = trebPtpObjMap[tp]
        stdTypes.push stp
  if stdTypes.length is 0
    char = getIconChar prop
    stdTypes.push iconCharToType[char] or 'Other'
  return stdTypes

exports.getStandardTypeInChar = (prop)->
  stdTypes = getStandardTypes prop
  stdChars = []
  for stdType in stdTypes
    if char = typeToIconChar[stdType]
      stdChars.push char
  if stdChars.length is 0
    return 'O'
  stdChars.sort _sortIconChar
  return [stdChars[0], stdTypes]

###
check if a prop is from GTA.
###
exports.isRMControlProp = (prop)->
  _id = unifyAddress {
    prov: cityHelper.getProvAbbrName(prop.prov_en or prop.prov),
    city:prop.city_en or prop.city
  }
  debug.debug 'RM_CONTROL_CITIES', _id
  return RM_CONTROL_CITIES[_id]

exports.GTA = 'GTA'
exports.OTHER_CITY = 'Other'

###
/offer.*?\s?(\d{1,2}(?:st|nd|rd|th)?)?\D+?(Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|June?|July?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\D*(\d{1,2}(?:st|nd|rd|th)?)?/i
	- 'Offers If Any 2 B Presented On May 1'
	- 'Offers Would Be Reviewed On 14th Nov @5'
[
  'Offers Would Be Reviewed On 14th Nov @5',
   '14th',
   'Nov',
   '5',
   index: 134,
  input: "!! All The Info. Provided Here Is As Per Sellers & Buyer Or Buyer's Agent To Independently Verify If Required !! ** ==>> Agents: Pall Offers Would Be Reviewed On 14th Nov @5Pm! Seller Reserves The Right To Accept Any Pre-Emptive Offers Anytime <<== ** Sch ' B '!!!",
  groups: undefined
]
	Rule: date matched[1/3], 
		- has st/nd/rd/th
		- later than today(or ts)
		- matched[3]
		- matched[1]
get Offer Date From BrokerRemark
###

exports.getOfferDateFromBm = getOfferDateFromBm = (prop)->
  return null unless prop.bm \
    and ('string' is typeof prop.bm) and (prop.bm.trim())
  if not prop.onD
    debug.error 'No onD for prop', prop._id
    return 0
  debug.debug 'prop.bm', prop.bm, typeof prop.bm, prop._id
  regex = /offer.*?\s?(\d{1,2}(?:st|nd|rd|th)?)?\D+?(Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|June?|July?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\D*(\d{1,2}(?:st|nd|rd|th)?)?/i
  matches = prop.bm.match regex
  debug.debug 'matches', matches
  return 0 unless matches?[2] and (matches[1] or  matches[3])
  return 0 unless month = getMonthNamesToNum(matches[2])
  if matches[1] and isDay(matches[1])
    dayStr = matches[1]
  else if matches[3] and isDay(matches[3])
    dayStr = matches[3]
  else
    dayStr = matches[3] or matches[1]

  day = parseInt(dayStr)
  propOnYear = Math.floor(prop.onD/10000)
  
  offerD = propOnYear*10000+ parseInt(month)*100 + parseInt(day)
  if offerD < prop.onD # might because of bad year
    offerD = parseInt(propOnYear+1)*10000 + parseInt(month)*100 + parseInt(day)
  debug.debug  'prop.onD', prop.onD, 'offerD', offerD
  nextMonthOfOnDay = inputToDateNum(
    new Date(inputToDate(prop.onD).getTime()+30*24*3600*1000))
  if (offerD > prop.onD) and (offerD < nextMonthOfOnDay)
    return offerD
  return 0
  #get year, incorrect if it is smaller then prop.ts
  #incorrect if it is two month bigger the prop.ts

isDay = (str)->
  if /st|nd|rd|th/i.test str
    return true

#合并 seller owner.
exports.parseOwner = parseOwner =  ({owner,seller,_id})->
  owner  = owner or seller
  return {} unless owner
  if typeof owner isnt 'string'
    debug.warn 'prop owner is not string', _id, owner
    return {}
  if /(^|\W)estate(\W|\s|$)/i.test owner
    if (/(ltd|inc)(\.)?$/i.test(owner) or /Real Estate/i.test(owner)) and \
      (not /Executor For/i.test owner)
        return {}
    return {isEstate : 1}
  else if (/power of s/i.test owner) \
  or (/(^|\W)POS(\W|\s|$)/i.test owner)
    return {isPOS : 1}
  else
    return {}
# prop = {owner:'\"Estate Of Helen Jean Bajurny\"'}
# prop = {seller:'Krates Keswick Inc. Under Power Of Sale'}
# prop = {seller:'Wong And Associates Real Estate Ltd., And Vip Group Holding Inc.'}
# prop = {seller:'Hyunsook Park (Managed By First Real Estate Service Inc)'}
# prop = {seller:'Savitri Almeida In Trust As Executor For Estate Of 840859 Ontario Inc.'}
# prop = {seller:'Rutmarg Real Estate Investment Inc.'}
# prop = {seller:'Helene Gyselinck, Estate Of Graham Knoll Gyselinck'}
# console.log parseOwner(prop)

#NOTE: login = true ===measn need login to view prop
exports.setUpPropLogin = (isShare, user, p)->
  if isShare
    p.login = false
  else if propSearch.needLoginToViewProp(p) and not user
    p.login = true

###
format psn, return rmPsn,rmPsnDate
@param {string} psn, possession date string
@param {number} onD on date
###
MONTHS_REGEX = /(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)( )?([0-9]+)?(, )?([0-9]{4})?/i

exports.genRmPsn = genRmPsn = ({psn,onD})->
  rmPsn = []
  return rmPsn unless psn
  cleanedPsn = psn.toString().toLowerCase().replace(/(\.|\,|\/|\-)/g,' ')
  if /tba|tbd/.test cleanedPsn
    rmPsn.push('tba')
  # rmPsn.push('tba') if (cleanedPsn.indexOf('tba') >= 0)
  # rmPsn.push('tba') if (cleanedPsn.indexOf('tbd') >= 0)
  if /imm/.test cleanedPsn
    rmPsn.push('imm')
  if /flex/.test cleanedPsn
    rmPsn.push('flex')
  # rmPsn.push('immed') if (cleanedPsn.indexOf('imm') >= 0)
  # rmPsn.push('flex') if (cleanedPsn.indexOf('flex') >= 0)
  
  cleanedPsn = cleanedPsn.replace(/(tba|tbd|flexible|flex|immediate|immed)/g,'')
  
  if MONTHS_REGEX.test(cleanedPsn)
    [cleanedPsn] = MONTHS_REGEX.exec(cleanedPsn)
    if /[0-9](st|th)/.test(cleanedPsn)
      #handle april 1st
      cleanedPsn = cleanedPsn.replace(/(st|th)/g,' ')
    unless /[0-9]{4}/.test(cleanedPsn)
      cleanedPsn = "#{cleanedPsn} #{parseInt(onD/10000)}"
    rmPsnDate = inputToDateNum(new Date(cleanedPsn))
    if rmPsnDate.toString() is 'Invalid Date'
      debug.error cleanedPsn
      rmPsnDate = null
  return {rmPsn,rmPsnDate}

###
处理 bltYr,age,bltYr1,bltYr2,rmBltYr 用于前端显示built year
@param list: 房源列表数组，req：需要使用req中的l10n翻译 Estimated
@return list: 每个组数据中添加 bltYrField 字段用于显示
###
exports.dealBuiltYearField = dealBuiltYearField = ({list,l10n})->
  estimated = l10n 'Estimated'
  for item in list
    if item.bltYr
      item.bltYrField = item.bltYr
    else if item.age and item.rmBltYr
      item.bltYrField = "#{item.age} (#{item.rmBltYr} #{estimated})"
    else if item.age and item.bltYr1 and item.bltYr2 and (item.bltYr1 is item.bltYr2)
      item.bltYrField = "#{item.age} #{item.bltYr1}"
    else if item.age and item.bltYr1 and item.bltYr2 and (item.bltYr1 isnt item.bltYr2)
      item.bltYrField = "#{item.age} (#{item.bltYr1} - #{item.bltYr2})"
    else if item.age
      item.bltYrField = item.age
    else if item.rmBltYr
      item.bltYrField = "#{item.rmBltYr}(#{estimated})"
    else if item.bltYr1 and item.bltYr2 and (item.bltYr1 is item.bltYr2)
      item.bltYrField = item.bltYr1
    else if item.bltYr1 and item.bltYr2 and (item.bltYr1 isnt item.bltYr2)
      item.bltYrField = "#{item.bltYr1} - #{item.bltYr2}"
  return list
###
用于判断列表中是否全都是/全都不是某个字段的某个值
@param list: 需要判断的数据列表，abbr：这组数据中的某个字段，isType：true/false 选择全都是/全都不是，type：被判断的事物
@return true/false
###
exports.maskFieldAllOrNotAll = maskFieldAllOrNotAll = (list,abbr,type,isType)->
  for item in list
    if(isType and (item[abbr] isnt type))
      return false
    if((not isType) and (item[abbr] is type))
      return false
  return true
#TODO:handle "29th June",
# console.log genRmPsn({psn:"29th June", onD: 20160309})

###
change int price to hidded value(maskPriceWithStar)
@param
type pred_sp string:currencyFormat;
@return
type hiddenPrice string;
@eg
$111,111 -> $1**,***
###
maskPriceWithStar = (pred_sp='')->
  hiddenPrice = ''
  predPrice = pred_sp
  for i in [0..(predPrice.length-1)]
    c = predPrice[i]
    if (c is '$') or (c is ',') or (i is 1)
      hiddenPrice += c
    else
      hiddenPrice += '*'
  return hiddenPrice

LIMIT_USER_PRED_SHOWS = 50
LIMIT_VIP_PRED_SHOWs  = 150

###
check if user reached predict display limit
@param
type arguments {}
@return
type ret boolean
###
checkIsUserOverPredLimit = ({prop, user, isVip})->
  limit = if isVip then LIMIT_VIP_PRED_SHOWs else LIMIT_USER_PRED_SHOWS
  { count,shouldNotify,isReachedLimit } = limiter.isReachedLimit {user,evtName:'pred',limit}
  if shouldNotify
    debug.info "pred reached limit(#{count}). User:#{user._id}:#{user.eml}"
  prop.predOutrange = isReachedLimit #count > limit
  return prop.predOutrange


###
用于 filter 房源的predition value， 假如超过quota展示masked值
@param
type arguments {
  isAllowedShowSoldPrice: boolean, #access allowed
  user: object, #session user
  prop: object, #not translated prop
}
@return
type prop object;
###
# TODO: filter ['rent_m1', 'value_m1', 'sp_m1','bltYr_m1','sqft_m1']????
exports.filterPredictionValues = filterPredictionValues = ({isAllowedShowSoldPrice,user,prop})->
  return prop unless prop.pred_sp
  # prop.pred_accuracy = calcPredAccuracy prop
  prop.pred_spl = currencyFormat((prop.pred_sp-prop.pred_spsd),'$',0)
  prop.pred_sph = currencyFormat((prop.pred_sp+prop.pred_spsd),'$',0)
  prop.pred_sp  = currencyFormat(prop.pred_sp,'$',0)
  shouldMaskResults = true
  if user
    prop.showPredict = isAllowedShowSoldPrice
    shouldMaskResults = checkIsUserOverPredLimit { prop,user,isVip:prop.showPredict }
  if shouldMaskResults
    prop.pred_sp  = maskPriceWithStar prop.pred_sp
    prop.pred_spl = maskPriceWithStar prop.pred_spl
    prop.pred_sph = maskPriceWithStar prop.pred_sph
    # prop.pred_accuracy = '******'
    delete prop.pred_his
  return prop
  
###
# @description 判断查询条件是否为成交/租出条件,以及在市天数
# @param {object} conditions - search conditions,eg:{"saletp":"sale","src":"mls","soldOnly":true,"city":"Toronto","sort":"ts-asc"}
# @return {array} - first is isSold:{boolean} 是否为成交/租出;second is dom:{null|number} 在市天数
###
isSoldCondition = (conditions)->
  if conditions.dom? and conditions.dom isnt ''
    dom = parseInt conditions.dom
    return [true,dom] if dom < 0
  if conditions.saleDesc in ['Sold','Leased']
    return [true,null]
  # if params.soldOnly
  #   return [true,null]
  [false,null]

###
# @description 生成房源需要与查询条件比较的字段
# @param {object} conditions - search conditions,eg:{"saletp":"sale","src":"mls","soldOnly":true,"city":"Toronto","sort":"ts-asc"}
# @return {object} fields - Fields need to be checked between property and conditions
###
genCheckFieldsForPropPass = (conditions) ->
  status = 'A'
  [isSold,dom] = isSoldCondition(conditions)
  if isSold
    status = 'U'
  fields = {
    sq_f: conditions.sq_f
    sq_t: conditions.sq_t
    lpChg: conditions.lpChg
    sold: conditions.sold
    recent: conditions.recent
    sch: conditions.sch
    neartype: conditions.neartype
    src: conditions.src
    city: conditions.city
    prov: conditions.prov
    min_lp: conditions.min_lp
    max_lp: conditions.max_lp
    ptype2: conditions.ptype2
    bdrms: conditions.bdrms
    bthrms: conditions.bthrms
    gr: conditions.gr
    bbox:conditions.bbox
    cmty:conditions.cmty
    ptype:conditions.ptype
    saletp:conditions.saletp
    bsmt:conditions.bsmt
    mfee:
      no_mfee:conditions.no_mfee
      max_mfee:conditions.max_mfee
    dom:dom
    status:status
    addr:conditions.addr
    remark:conditions.remark
    min_poss_date:conditions.min_poss_date
    max_poss_date:conditions.max_poss_date
    psn:conditions.psn
    yr_f:conditions.yr_f
    yr_t:conditions.yr_t
    remarks:conditions.remarks
    rltr:conditions.rltr
    frontFt_f:conditions.frontFt_f
    frontFt_t:conditions.frontFt_t
    depth_f:conditions.depth_f
    depth_t:conditions.depth_t
    exposures:conditions.exposures
    isPOS:conditions.isPOS
    isEstate:conditions.isEstate
    ohz:if conditions.oh then conditions.oh else ''
    ltp: if conditions.ltp then conditions.ltp else ''
  }
  if conditions.uaddr
    fields.uaddr = conditions.uaddr
  return fields

###
# @description 根据查询条件过滤房源
# @param {object} prop - property info
# @param {object} conditions - search conditions
# @param {boolean} isTopListing - flag of top listing
# @return {boolean}
###
exports.propPassSearchCondition = propPassSearchCondition = (prop, conditions, isTopListing = true) ->
  if isTopListing and ('U' is prop.status)
    return false #hide top listing
  ok = true
  fields = genCheckFieldsForPropPass conditions
  # 出租房源价格为lpr
  prop.saletp ?= []
  prop.saletp[0] ?= ''
  if prop.saletp[0].toLowerCase() is 'lease'
    lp = prop.lpr
  else
    lp = prop.lp
  for k,v of fields
    if v? and (v isnt '') and (v isnt 'undefined')
      if k is 'min_lp'
        v = parseFloat v
        return false unless lp >= v
      else if k is 'max_lp'
        v = parseFloat v
        return false unless lp <= v
      else if k is 'ptype2'
        # NOTE: some prop has no ptype2
        if not prop.ptype2?.length
          debug.error 'prop no ptype2: '+prop._id+prop.ptype2
        prop.ptype2 ?= []
        if prop[k]?.length < v.length
          longer = v
          shorter = prop[k]
        else
          longer = prop[k]
          shorter = v
        # for ptype2 in shorter
        #   return false unless ((longer.indexOf ptype2) isnt -1)
        # NOTE:没有交集的情况下return false,而不是存在不一致的ptype2就return false
        shorterSet = new Set(shorter)
        hasPtype2 = longer.some((item) -> shorterSet.has(item))
        return false unless hasPtype2
      else if k is 'bbox'
        if (not (prop.lng >= v[0])) or (not (prop.lng <= v[2])) or (not (prop.lat >= v[1])) or (not (prop.lat <= v[3]))
          return false
      else if k is 'saletp'
        if (not prop.saletp) or (not (prop.saletp[0].toLowerCase() is v.toLowerCase()))
          return false
      else if k is 'dom'
        fld = 'ts'
        dom = v
        if dom < 0
          fld = 'mt'
          if fields.soldOnly
            cond = if fields.saletp is 'Lease' then ['Lsd'] else ['Sld','Pnd','Cld']
        ts = new Date()
        ts = new Date(ts.getFullYear(),ts.getMonth(),ts.getDate())
        domDay = Math.abs dom
        ts = new Date(ts.getTime() - (domDay * 24 * 3600000))
        return false unless (new Date(prop[fld])) >= ts or (cond and (prop.lst in cond))
      else if k is 'ohz'
        return false unless prop.ohz
        ohok = false
        ts = new Date()
        for oh in prop.ohz
          if not ohok and (oh.t >= dateFormat(ts,"YYYY-MM-DD"))
            #for day in oh
              #if day >= helpers.dateFormat(ts,"YYYY-MM-DD")
            ohok = true
            break
        return false unless ohok
      else if k is 'mfee'
        if (v.max_mfee? and v.max_mfee isnt 'undefined') or v.no_mfee
          max_mfee = if v.no_mfee then 0 else parseFloat(v.max_mfee)
          if max_mfee > 0
            return false unless prop.mfee <= max_mfee
          else # no MaintenanceFee
            return false unless (not prop.mfee) or (prop.mfee is 0)
      else if ['bdrms','bthrms','gr'].indexOf(k) > -1
        if ('string' is typeof v) and (/\d\+$/.test v)
          rms = parseInt v.slice(0,-1)
          return false unless prop[k] >= rms
        else
          rms = parseInt v
          return false unless prop[k] is rms
      else if k is 'src'
        v1 = v.toUpperCase()
        v2 = prop.src?.toUpperCase()
        # NOTE: cant: return v1 is v2
        # if v1 is 'MLS'
        #   return true if v2 in ['TRB','DDF','BRE']
        if (v1 is 'RM') and (v2 isnt 'RM')
          return false
      else if k is 'ptype'
        # prop already changed ptype
        v = 'Residential' if /Assignment|Exclusive/i.test v
        return false unless getPtypeAbbr(prop[k]) is getPtypeAbbr(v)
      else if k is 'ltp'
        return false if prop.ltp and (prop.ltp isnt v)
      else if (k is 'bsmt') and prop.bsmt
        return false unless (v in prop.bsmt)
      else if (k is 'sq_f') and prop.sqft1
        return false if prop.sqft1 < v
      else if (k is 'sq_t') and prop.sqft2
        return false if prop.sqft2 > v
      else if k is 'lpChg'
        if v is 'off'
          return false unless prop.pc < 0
      else if k is 'sold'
        if v is 'fast'
          return false unless prop.sldDom < 10
      else if k is 'recent'
        if v is 'sold'
          return false unless prop.sldDom < 10
      else if (k is 'sch') and (schools = prop.schools)
        if v is 'best'
          hasBestSchool = false
          for school in schools
            hasBestSchool = true if school.fir? >= 9
          return false unless hasBestSchool
      else if (k is 'neartype') and prop.trnst
        if v is 'mtr'
          hasNearMtr = false
          for trnst in prop.trnst
            hasNearMtr = true if trnst.dist <= 1000
          return false unless hasNearMtr
      else if k is 'prov'
        return false unless cityHelper.getProvAbbrName(prop[k]) is v
      else if k is 'addr'
        return false unless prop[k]?.toLowerCase().includes(v.toLowerCase())
      # else if k is 'remark'
      #   return false unless prop.remark?.includes(v)
      else if k is 'min_poss_date'
        return false unless prop.poss_date >= v
      else if k is 'max_poss_date'
        return false unless prop.poss_date <= v
      else if k is 'psn'
        return false unless prop.rmPsn
        return false unless (v in prop.rmPsn)
      else if k is 'isPOS'
        return false unless prop[k] is 1
      else if k is 'isEstate'
        return false unless prop[k] is 1
      else if k is 'frontFt_f'
        frontFt_f = parseInt v,10
        return false if prop.front_ft < frontFt_f
      else if k is 'frontFt_t'
        frontFt_t = parseInt v,10
        return false if prop.front_ft > frontFt_t
      else if k is 'depth_f'
        depth_f = parseInt v,10
        return false if prop.depth < depth_f
      else if k is 'depth_t'
        depth_t = parseInt v,10
        return false if prop.depth > depth_t
      else if k is 'exposures'
        exposures = if Array.isArray v then v else [v]
        return false unless prop.fce in exposures
      else if k is 'remarks'
        if not prop.m
          return false
        # return false unless prop.m.indexOf(v)
        return false unless stringContainsAny(prop.m,v)
      else if k is 'rltr'
        return false unless prop.rltr is v
      else if k is 'yr_f'
        thisYear = new Date().getFullYear()
        yr_f = parseInt v,10
        min = thisYear - yr_f
        return false unless prop.bltYr1 >= min
      else if k is 'yr_t'
        thisYear = new Date().getFullYear()
        yr_t = parseInt v,10
        max = thisYear - yr_t
        return false unless prop.bltYr2 <= max
      else if k is 'soldLoss'
        if v is 'loss'
          return false unless prop.lspDifPct < 0
      else
        return false unless prop[k] is v
  return ok

# 设置房源的disclaimer信息
# disclaimer:{TREB:{terms:[],icons:[]}}
module.exports.setupDisclaimer = (prop,disclaimer) ->
  for key,val of disclaimer
    if prop.dis[key]
      prop.disclaimerTerms = val.terms
      prop.disclaimerIcons = val.icons
      prop.dis_src = val.source


###
# @description id，addr检索的房源结果，根据角色处理merged房源，local board房源排在merged房源前
# @param
    type propsObj {
      cnt : number  # 检索出的房源数量
      list : array  # 房源列表
      searchAfter : string/array
    }
  @param {boolean} isPropAdmin - 是否为admin
###
# inReal or admin 可能分享merged prop
module.exports.filterPropsByPermission = (propsObj,isPropAdmin,isShare) ->
  needSubCount = 0
  propIdMap = {}  # 非merged房源id
  propList = [] # 非merged房源
  mergedList = [] # merged房源
  for prop in propsObj.list
    if not prop.merged
      propIdMap[prop._id.toString()] = true
      propList.push prop
    else
      needSubCount++
      mergedList.push prop
  for mergedProp in mergedList
    # some shared props has merged, need to show
    # TODO: remove this after no shared props has merged
    if isPropAdmin or isShare
      # admin需要将merged房源放置local board之后
      needSubCount--
      propList.push mergedProp
    else
      # 非admin,orign房源未查出来,merged房源不是DDF需要返回
      if (not propIdMap[mergedProp.merged]) and (mergedProp.src isnt 'DDF')
        needSubCount--
        propList.push mergedProp
  propsObj.list = propList
  # 非admin cnt需要减去merged房源数量
  # NOTE: 剩下的结果里可能还会有merged房源，这个cnt只是估算，对于<20的cnt，会更准确
  if propsObj.cnt? and (not isPropAdmin)
    propsObj.cnt -= needSubCount
  return

###
# @description 确认是否可以查看隐藏(del)房源
# @param {boolean} isAllowedAdmin - 是否为admin
# @param {boolean} isRealGroup - 是否在':inReal' group
# @param {boolean} isAllowedVipUser - 是否为vip用户
# @return {boolean} 是否可以查看
###
module.exports.canViewDelProp = canViewDelProp = ({isAllowedAdmin,isRealGroup,isAllowedVipUser}) ->
  if isAllowedAdmin or isRealGroup or isAllowedVipUser
    return true
  return false


###
# @description 房源列表非admin,inreal用户过滤del房源
# @param {array} props - list of prop
# @param {boolean} isAllowedAdmin - 是否为admin
# @param {boolean} isRealGroup - 是否在':inReal' group
# @param {boolean} isAllowedVipUser - 是否为vip用户
# @return {array} props - filter deleted props
###
module.exports.filterDelPropsByPermission = ({props,isAllowedAdmin,isRealGroup,isAllowedVipUser}) ->
  if canViewDelProp {isAllowedAdmin,isRealGroup,isAllowedVipUser}
    return props
  # 非admin,inreal时过滤del房源
  props = props.filter (item)->
    return not item.del
  return props


# 获取房源列表基础条件参数
module.exports.getBaseForPropList = (req,user)->
  opt =
    # TODO: ip and user for logging when has error
    locale: req.locale()
    notApp: 'app' isnt req.getDevType()
    apisrc: req.param('apisrc')
    hasloc: req.param('loc')
    searchAfter: req.param('searchAfter')
    isCip: req.isChinaIP(),
    isPad: req.isPad(),
    isShare: req.param('share') or req.param('ec')
    isPropAdmin: req.isAllowed('propAdmin',user)
    isVipRealtor: req.isAllowed('vipRealtor',user)
    isVip: req.isAllowed('vipUser',user)
    isAssignAdmin: req.isAllowed('propAdmin',user)
    isAdmin: req.isAllowed('admin',user)
    isVipUser: req.isAllowed('vipUser',user)
    isUserAdmin: req.isAllowed('userAdmin',user)
    isRealtor: req.hasRole('realtor')
  return opt

# 生成merged，del房源查找条件
# NOTE: propAdmin,share可以查找merged房源
# NOTE: admin,inRealGroup可以查找del房源
# NOTE: inRealGroup 按照id查找会查找到的merged房源，会显示2个（DDF+TRB），无法区分Which
module.exports.genSearchMergeOrDelProps = (options = {})->
  # options.isVip options.isRealGroup
  options.searchMergeProps = options.isPropAdmin or options.isShare or false
  options.searchDelProps = options.isAdmin or options.isRealGroup or false

# 根据prop.bcf/prop.ptype2获取ManualImport的className
module.exports.getClassNameForManualImport = (prop) ->
  CLASS_NAME_MAP =
    'b': 'CommercialProperty'
    'c': 'CondoProperty'
    'f': 'ResidentialProperty'
  className = CLASS_NAME_MAP[prop?.bcf]
  
  if not className
    if isCondo prop
      className = 'CondoProperty'
    else
      # NOTE: ManualImport默认为ResidentialProperty
      className = 'ResidentialProperty'
  
  return className

###
# @description add logs to propertiesImportLog
# @params {string}   id     properties_import_log._id eg:RHBH4190286/TRBW1111
# @params {object}   log    记录的log信息 eg:{step:'watch',ts:''',m:'',Lsc:'',lSrc:'',lp:1000}
# @params {object}   prop   prop record detail in rni
# @params {function} cb
# @return {object}   result result of properties_import_log.update
###
module.exports.addLogs = (opt={},cb)->
  {step,id,log,prop,collPropertyImportLog,step} = opt
  step ?= 'watch'
  if not id
    debug.error 'addLogs id is required',log,prop
    return cb()
  if not collPropertyImportLog
    return cb()
  if prop
    # NOTE: 暂时只有TRB,DDF,BRE,RHB价格字段
    for field,value of LOG_FIELDS
      log[field] = prop[field] if prop[field]?
  updateLog = Object.assign {step,ts:new Date()},log
  update = { $push: {logs:{$each:[updateLog], $slice:-200}}}
  collPropertyImportLog.updateOne {_id:id},update,{ upsert: true },cb

# zip = 'm1n2h4'
# @return 'M1N 2H4'
formatZip = (zip='')->
  if not zip
    return zip
  zip = ''+zip
  zip = zip.toUpperCase()
  zip = zip.replace(/[^0-9a-zA-Z]/g,'')
  part1 = zip.slice(0,3)
  part2 = zip.slice(3)
  return part1 + ' ' + part2

# zip = 'M1N 2H4'
# compares first part of zip without case
module.exports.isSameFirstPartOfZip = isSameFirstPartOfZip = (zip1='',zip2='')->
  # without zip we default same zip
  if (not zip1) or (not zip2)
    return true
  zip1 = formatZip zip1
  zip2 = formatZip zip2
  zip1 = zip1.split(' ')[0]
  zip2 = zip2.split(' ')[0]
  zip1 ?= 'DEFAULT1'
  zip2 ?= 'DEFAULT2'
  return zip1.toUpperCase() is zip2.toUpperCase()

module.exports.isSimilarAddr = isSimilarAddr = (srcAddr='',compareAddr='',allowDiffRatio = 0.1)->
  if (not srcAddr) or (not compareAddr)
    return false
  # addr可能不是str, 强制转换格式
  srcAddr += ''
  compareAddr += ''
  # 去掉addr两边空白字符
  # addr可能存在连续多个空白字符,导致levenshteinDistance失败
  srcAddr = srcAddr.trim().replace(/\s+/g,' ')
  compareAddr = compareAddr.trim().replace(/\s+/g,' ')
  
  srcAddrArr = srcAddr.split(' ')
  compareAddrArr = compareAddr.split(' ')
  if (Math.abs srcAddrArr.length - compareAddrArr.length) > 1
    return false
  
  diffStArr = srcAddrArr
    .filter((x) -> not(compareAddrArr.includes(x)))
    .concat(compareAddrArr.filter((x) -> not(srcAddrArr.includes(x))))

  if diffStArr.length > 2
    return false
  else if diffStArr.length is 2
    diff = levenshteinDistance diffStArr[0], diffStArr[1]
    maxDiffCount = Math.ceil(compareAddr.length * allowDiffRatio)
    if diff > maxDiffCount
      return false
  # diffStArr.length is 1 在loc十分接近时(40m/60m)认为是相似的
  # eg:  15 WATER WALK DR(-79.3282922,43.8561203) 和 15 WATER DR(-79.3280273,43.8560431)
  # else is similar addr return true
  return true

# @description filter addr history by accuracy
# @param {array} list
module.exports.filterAddrHistoryByAccuracy = filterAddrHistoryByAccuracy = (list=[],{id,addr,zip})->
  retList = []
  map = {}
  for i in list
    # NOTE: remove this listing from ret
    if (i._id.toString() is id)
      continue
    if not isSameFirstPartOfZip(i.zip, zip)
      continue
    if i.addr isnt addr
      # 地址不一致并且相差超过10%
      if not isSimilarAddr(addr,i.addr,0.1)
        i.warn = true
    if map[i._id] or i.del
      continue
    retList.push i
    map[i._id] = true
  return retList
module.exports.LOG_FIELDS = LOG_FIELDS

# @description get src for prop. some prop has null src
module.exports.guessSrc = guessSrc = (prop={}) ->
  if prop.src
    return prop.src
  if prop.origSrc
    if Array.isArray prop.origSrc
      return prop.origSrc[0]
    return prop.origSrc
  if prop.phosrc
    return "#{prop.phosrc}".toUpperCase()
  if /^DDF/.test prop._id
    return 'DDF'
  else if /^BRE/.test prop._id
    return 'BRE'
  return null

# @description 获取unit type有值的keys
# @param {array} list
UNIT_TYPE_DEFAULT_KEYS = ['m','tp','units','actualRent','totalRent','baths','beds']
module.exports.getUnitTypeKeys = getUnitTypeKeys = (prop)->
  return [] unless prop?.unitTypes?.length
  uniqueKeys = new Set()
  prop.unitTypes.forEach((unit) ->
    UNIT_TYPE_DEFAULT_KEYS.forEach((key) ->
      uniqueKeys.add(key) if unit[key]
    )
  )
  return Array.from(uniqueKeys)

# ObjectId.isValid对于12位字符串会认为是true，所以针对房源_id使用这个进行判断
module.exports.isPropObjectId = isPropObjectId = (id)->
  unless id
    return false
  id = id.toString()
  # id在传进来之前可能被转换为小写，所以这里需要转换为大写判断是否为MLS前缀
  if isMlsPrefix(id.toUpperCase())
    return false
  return isObjectIDString(id)
  
# @description 根据treb的residential style获取ptype2
# @param {string} pstyl
# @param {array} ptype2
# @return {array} ptype2
module.exports.trebResidentialStyle2Ptype2 = trebResidentialStyle2Ptype2 = (pstyl,ptype2=[])->
  if (not pstyl) or (not pstyl?.length)
    return ptype2
  if /Apartment|Bachelor|Studio/i.test pstyl
    ptype2.push 'Apartment'
  if /Bungalow|Bungaloft/i.test pstyl
    ptype2.push 'Bungalow'
  if /Stacked/i.test pstyl
    ptype2.push 'Stacked'
  if /Townhse|Townhouse/i.test pstyl
    ptype2.push 'Townhouse'
  if /Sidesplit|Backsplit/i.test pstyl
    ptype2.push 'Split'
  if /Loft/i.test pstyl
    ptype2.push 'Loft'
  return ptype2

###
检查房源价格是否在合理范围内
sale: 20000 - 80000000
lease: 0 - 100000
@param {Number} price - 房源价格
@param {String|Array} saletp - 交易类型，可以是字符串或数组
@return {Boolean} - 价格是否在合理范围内
###
module.exports.isPriceInRange = isPriceInRange = (price,saletp)->
  unless (price? and saletp?)
    return false
  type = if Array.isArray saletp then saletp[0] else saletp
  unless type
    debug.error "no saletp: #{saletp}, price: #{price}"
    return false
  switch type.toLowerCase()
    when 'sale'
      return ((price >= MIN_SALE_PRICE) and (price <= MAX_SALE_PRICE)) or (price <= NEGOTIABLE_PRICE)
    when 'lease'
      return price <= MAX_RENT_PRICE
    else
      debug.error "Unknown saletp: #{type} for price: #{price}"
  return false

# @description 获取房源缩略图
# @param {object} prop - 房源对象
# @param {boolean} use3rdPic - 是否使用第三方图片
# @return {string} thumbUrl - 缩略图URL
module.exports.getThumbUrlFromPhoUrls = getThumbUrlFromPhoUrls = (prop={},use3rdPic=false,imgServerDlAddr='') ->
  if prop.thumbUrl
    return prop.thumbUrl
  genListingPicReplaceUrls(prop,use3rdPic,{num:1,useThumb:true},imgServerDlAddr)
  # if not prop.thumbUrl
    # NOTE: no need, too many
    # debug.warn 'no thumbUrl for prop',prop._id
  return prop.thumbUrl


# @description 替换单个房源的缩略图URL占位符
# @param {object} prop - 房源对象
# @param {boolean} isCip - 是否中国IP
# @param {string} shareHostNameCn - 分享主机名
# @return {object} prop - 替换后的房源对象
module.exports.thumbUrlReplace = thumbUrlReplace = (prop={}, isCip=false, shareHostNameCn='') ->
  return prop unless prop?.thumbUrl # Return early if no thumbUrl
  cfg = {}
  cfg.shareHostNameCn = shareHostNameCn if shareHostNameCn
  ml_base = _basePicUrl isCip,cfg
  prop.thumbUrl = prop.thumbUrl.replace('{ml_base}',ml_base)
  # TODO: 前端使用的img字段,需要使用thumbUrl替换,后面前端需要统一只使用thumbUrl字段
  if not prop.img
    prop.img = prop.thumbUrl
  return prop

# @description 替换房源列表中的缩略图URL占位符
# @param {array} list - 房源列表
# @param {boolean} isCip - 是否中国IP
# @param {string} shareHostNameCn - 分享主机名
# @return {array} list - 替换后的房源列表
module.exports.thumbUrlListReplace = thumbUrlListReplace = (list=[], isCip=false, shareHostNameCn='') ->
  return list unless list?.length # Return early if empty list
  for prop in list
    thumbUrlReplace(prop, isCip, shareHostNameCn) if prop?.thumbUrl
  return list

###
# @description 解析房源la2中的agent信息
# @param {Object} la1 - 房源la信息
# @param {Object} la2 - 房源la2信息
# @return {Array} 返回解析后的agent信息列表
###
module.exports.parseLa2 = parseLa2 = (la1 = {}, la2 = {}) ->
  laList = []
  retMap = {}
  officeList = []
  
  # 收集所有la信息
  laList.push(la2) if la2
  if Array.isArray(la1)
    laList = laList.concat(la1)
  else
    laList.push(la1) if la1
    
  return [] if laList.length is 0
    
  # 处理每个la对象
  for la in laList
    # 处理代理人信息
    if la.agnt?.length
      for agnt in la.agnt
        agentOffice = agnt.office or la # la1的office信息在agnt外层
        
        # 处理代理人名称，移除特殊标记
        agentName = agnt.nm?.replace(' PREC*', '') or 'Unknown Agent'
        
        # 创建代理人信息对象
        tmp = 
          anm: agnt.nm
          _id: agnt._id
          nm: agentOffice.nm
          addr: agentOffice.addr
          tel: if agentOffice.tel then agentOffice.tel.split(',')[0] else ''
          fax: agentOffice.fax
          ambl: if agnt.tel then agnt.tel.split(',')[0] else ''
          pstn: agnt.pstn
          
        # 记录已处理的办公室
        officeList.push(tmp.nm) if tmp.nm
          
        # 避免重复添加相同代理人
        retMap[agentName] = tmp unless retMap[agentName]
          
    # 处理办公室信息
    if la.nm and officeList.indexOf(la.nm) < 0
      tmp = 
        nm: la.nm
        addr: genAddr(la)
        tel: la.tel
        id: la.id
        fax: la.fax
        
      # 避免重复添加相同办公室
      retMap[tmp.nm] = tmp unless retMap[tmp.nm]
          
  return Object.values(retMap)

###
# @description 生成地址字符串
# @param {Object} la2 - 房源la2信息
# @return {string} 返回格式化后的地址字符串
###
module.exports.genAddr = genAddr = (la2) ->
  return '' unless la2
  
  parts = []
  parts.push("#{la2.addr} #{la2.city or ''}".trim()) if la2.addr
  parts.push(la2.prov) if la2.prov
  
  return parts.join(' ')
