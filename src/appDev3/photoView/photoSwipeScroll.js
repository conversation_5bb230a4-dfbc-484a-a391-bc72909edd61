var photoSwipeScroll = {
  data(){
    return {
      showPhotos: [],
      notesPic:[],
      buildingPic:[],
      floorPlanPic:[],
      uaddr:decodeURIComponent(vars.uaddr),
      propId: vars.id,
      propertiesPic:[],
      datas:[
        'isRealGroup',
        'isAdmin',
        'isApp',
        'lang',
        'sessionUser',
        'isEditorAdmin',
        'isNoteAdmin'
      ],
      dispVar: {
        isRealGroup: false,
        isAdmin: false,
        isApp:false,
        lang:'en',
        sessionUser: {},
        isEditorAdmin:false,
        isNoteAdmin:false
      },
      tabsTitle: {
        propTitle:true,
        buildingTitle:false,
        floorPlanTitle:false,
        notesTitle:false,
      },
      filterCondit: [],
      selectedCondit: '',
      isScroll: true,
      swiper:null,
      userForm:{
        tp: 'feedback',
        title: 'Report Floor Plan Not Match',
        ueml:'<EMAIL>',
        formid:'system',
        sid:'',
        nm:'',
        fn:'',
        ln:'',
        eml:'',
        mbl:'',
        projShareUID:'',
        fnm:'',
        hasWechat: false,
        m:''
      },
      signupTitle: '',
      feedurl: '/1.5/form/forminput',
      prop:{},
      currentPic:{
        display: false,
        src: ''
      },
      isFloorPlan: vars.isFloorPlan,
      bdrm: decodeURIComponent(vars.bdrm),
      strings:{
        matchTip:{key:"Each Building can only be matched once, whether to cancel the previous match",ctx:''},
        cancel:{key:'Cancel',ctx:''},
        yes:{key:'Yes',ctx:''},
        untTip: {key:'Unt does not exist',ctx:''}
      },
      isBuilding: vars.isBuilding,
      photoIndex: -1,
      startDistance: 0,
      endDistance: 0,
      scaleFactor: 1,
      currentPicSwiper: null,
      isExchange: vars.isFloorPlan,
      isScaling: false,
      shortUrlId: decodeURIComponent(vars.shortUrlId)
    }
  },
  mounted(){
    var self = this;
    this.isBuilding = this.changeStrToBoolen(this.isBuilding);
    this.isFloorPlan = this.changeStrToBoolen(this.isFloorPlan);
    this.isExchange = this.changeStrToBoolen(this.isExchange);
    self.$getTranslate(self);
    self.getPageData(this.datas,{},true);
    self.getPropPic();
    if(this.isBuilding){
      self.getfloorPlanAndBuildPhotos();
    }
    if(window.loadLazyImg){
      loadLazyImg()
    };
    self.initSlideMapping();
    bus.$on('pagedata-retrieved',this.pagedata);
    window.addEventListener('load',this.scrollBar);
  },
  beforeUnmount() {
    bus.$off('pagedata-retrieved',this.pagedata);
    window.removeEventListener('load',this.scrollBar);
  },
  computed: {
    showBaseInfoBotm:function(){
      if(!this.isScroll && this.tabsTitle.floorPlanTitle){
        return false;
      }
      return true;
    }
  },
  methods: {
    changeStrToBoolen(variable){
      if(!variable) return false;
      if (typeof variable === 'boolean') return variable;
      if (typeof variable === 'string'){
        if (variable == 'true') return true;
        return false;
      } else {
        return false;
      }
    },
    pagedata(d){
      this.dispVar = Object.assign(this.dispVar, d);
      if (d.sessionUser && d.sessionUser.eml) {
        for (let i of ['nm','eml','mbl','fnm','fn','ln']) {
          this.userForm[i] = d.sessionUser[i];
        }
        if(!this.userForm.nm || this.userForm.fnm){
          this.userForm.nm = this.userForm.fnm || d.sessionUser.nm_en || d.sessionUser.nm_zh
        }
      }
      if (RMSrv.hasWechat){
        RMSrv.hasWechat((has)=>{
          if (has!=null) {
            this.userForm.hasWechat = has;
          }
        })
      }
      if(this.dispVar.isRealGroup || this.dispVar.isNoteAdmin){
        this.getNotesPic();
      }
    },
    genSlideMapping(tp) {
      const [nm, length] = tp;
      var ret = [{k:'', v:'Any'}];
      const start = (nm == 'filterCondit')?0:1;
      for (let i = start; i<length+1; i++) {
        ret.push({k:i+'',v:i+''});
        if (nm != 'filterCondit' || i != 0) {
          ret.push({k:i+'+n',v:i+'+•'});
        }
      }
      return ret;
    },
    initSlideMapping() {
      for (let tp of [['filterCondit',5]]) {
        this[tp[0]] = this.genSlideMapping(tp);
      }
    },
    getPropPic(){
      let self = this;
      let body = {
        id: vars.id
      };
      if (this.shortUrlId !== 'undefined'){
        body.shortUrlId = this.shortUrlId
      }
      setLoaderVisibility('block');
      fetchData('/prop/picUrls',{body},(err,ret)=>{
        setLoaderVisibility('none');
        self.propertiesPic = [];
        if (err || !ret.ok) {
          if (ret.needLogin){
            bus.$emit('flash-message',ret.e);
          }
          return;
        }
        if(ret.picUrls && ret.picUrls.length){
          self.propertiesPic = ret.picUrls.map(url => ({
            img: url
          }));
          if(self.isFloorPlan) return;
          self.showPhotos = self.propertiesPic;
          if(!vars.picIndex) return;
          this.$nextTick(()=>{
            let imgList = document.getElementById('useScroll');
            let imgs = imgList.getElementsByTagName('img');
            imgs[vars.picIndex].scrollIntoView();
          });
        }
      });
    },
    getfloorPlanAndBuildPhotos(){
      let self = this;
      let body = {
        uaddr: this.uaddr,
        propId: vars.id,
        isFloorPlan: this.isFloorPlan
      };
      fetchData('/floorPlan/fpAndBuildingImage',{body},(err,ret)=>{
        if (err || !ret.ok) {
          return;
        }
        if(ret.result.notesImgs){
          self.notesPic = ret.result.notesImgs;
        }
        if(ret.result.buildingPic){
          self.buildingPic = ret.result.buildingPic;
        }
        if(ret.result.floorPlanPic){
          self.floorPlanPic = ret.result.floorPlanPic;
        }
        if(ret.result.prop){
          self.prop = ret.result.prop;
        }
        if(self.isFloorPlan){
          self.selectedCondit = self.bdrm || '';
          self.selectTabTitle('floorPlanPic','floorPlanTitle');
        }
        // if(self.isFloorPlan && self.bdrm){
        //   self.$nextTick(()=>{
        //     self.selecteBd(self.bdrm);
        //   });
        // }
      });
    },
    getNotesPic(){
      let self = this;
      let body = {
        uaddr: this.uaddr
      };
      fetchData('/1.5/notes/showNotesPic',{body},(err,ret)=>{
        if (err || !ret.ok) {
          return;
        }
        if(ret.result.notesImgs){
          self.notesPic = ret.result.notesImgs;
        }
      });
    },
    relateUrlAndSize(data){
      if(data.urls.length > data.sizes.length){
        let diff = data.urls.length - data.sizes.length;
        for(let i=0;i<diff;i++){
          data.sizes.push(data.sizes[0]);
        }
      }
      let items = []
      for(let i=0;i<data.urls.length;i++){
        if(data.urls[i] && data.sizes[i]){
          items.push({
            img: '' + data.urls[i],
            w: data.sizes[i].split('x')[0],
            h: data.sizes[i].split('x')[1]
          })
        }
      }
      if (!items.length){
        items = [
          {
            img: '/img/noPic.png',
            w: 640,
            h: 426
          }
        ]
      }
      return items;
    },
    selectTabTitle(pic,title){
      if(this.tabsTitle[title]){
        return;
      }
      let keys = Object.keys(this.tabsTitle);
      this.showPhotos = [];
      for(let key of keys){
        this.tabsTitle[key] = false;
      }
      this.tabsTitle[title] = true;
      this.$nextTick(()=>{
        this.scrollBar();
      });
      if(title == 'floorPlanTitle'){
        this.selecteBd(this.selectedCondit);
        this.exchangeResource();
        return;
      }
      this.showPhotos = this[pic];
      this.updateSwiperOrScrollToTop();
    },
    selecteBd(selectBd){
      this.showPhotos = [];
      this.selectedCondit = selectBd;
      if(selectBd == ''){
        this.showPhotos = this.floorPlanPic;
        this.updateSwiperOrScrollToTop();
        return;
      }
      splitArr = selectBd.split('+');
      if(this.isFloorPlan && splitArr.length>1){
        this.selectedCondit = `${splitArr[0]}+n`;
      }
      for(let i=0;i<this.floorPlanPic.length;i++){
        if(this.floorPlanPic[i].bdrms){
          splitBdrm = this.floorPlanPic[i].bdrms.split('+');
          if(splitArr.length == splitBdrm.length && splitArr[0] == splitBdrm[0]){
            this.showPhotos.push(this.floorPlanPic[i]);
          }
        }
      }
      this.updateSwiperOrScrollToTop();
    },
    exchangeResource(){
      let self = this;
      if(this.isExchange){
        return
      }
      fetchData('/floorPlan/exchange',{body:{}},(err,ret)=>{
        if (err || !ret.ok) {
          return bus.$emit('flash-message',this.$_('Error'));
        }
        self.isExchange = true;
      });
    },
    reportError(floorplan){
      this.signupTitle = this.$_('Report Error');
      this.userForm.addr = (this.prop.unt?this.prop.unt+' ':'')+this.prop.addr;
      this.userForm.prov = this.prop.prov;
      this.userForm.city = this.prop.city;
      this.userForm.saletp = this.prop.saletp;
      if(this.prop.ptype2){
        this.userForm.ptype2 = this.prop.ptype2;
      }
      if(floorplan.price){
        this.userForm.lp = floorplan.price;
      }
      this.userForm.sid = this.prop.sid;
      this.userForm.img = floorplan.img;
      this.userForm.m = 'The floor plan is not accurate.'
      toggleModal('SignupModal','open');
    },
    confirmMatchUnit(value){
      let self = this;
      let closeTip = this.$_(this.strings.matchTip.key);
      var cancel = this.$_(this.strings.cancel.key);
      var yes = this.$_(this.strings.yes.key);
      let canMatch = true;
      let floopPlanId = null;
      if(!value.isMatch){
        for(let i=0; i<this.floorPlanPic.length;i++){
          if(this.floorPlanPic[i].isMatch){
            canMatch = false;
            floopPlanId = this.floorPlanPic[i].id;
            break;
          }
        }
      }
      function _doClose(idx) {
        if (idx+'' !== '1') {
          self.matchUnit(value,floopPlanId);
        }
      }
      if(canMatch){
        self.matchUnit(value);
      }else{
        RMSrv.dialogConfirm(closeTip, _doClose, '', [cancel, yes]);
      }
    },
    matchUnit(value,floopPlanId){
      if(!this.prop.unt){
        return bus.$emit('flash-message',this.$_(this.strings.untTip.key));
      }
      let self = this;
      setLoaderVisibility('block');
      let body = {
        unt: this.prop.unt,
        propId: this.propId,
        isMatch: value.isMatch,
        id: value.id
      }
      if(floopPlanId){ // 已经被点过match的floorplan _id
        body.floopPlanId = floopPlanId;
      }
      fetchData('/floorPlan/updateMatch',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):ret.e;
          return bus.$emit('flash-message',msg);
        }
        for(let i=0; i<self.floorPlanPic.length;i++){
          let info = self.floorPlanPic[i];
          if(info.id == value.id){ // 存在同一个floorplan id下有多张图片的情况
            if(info.isMatch){
              info.matchCnt -= 1;
              info.score -= 10;
            }else{
              info.matchCnt += 1;
              info.score += 10;
            }
            info.isMatch = !info.isMatch;
            info.untM = self.prop.unt;
            if(!info.matchCnt){
              info.untM = undefined;
            }
          }
          if(floopPlanId && (info.id == floopPlanId)){
            info.isMatch = false;
            info.matchCnt -= 1;
            info.score -= 10;
            if(!info.matchCnt){
              info.untM = undefined;
            }
          }
        }
        self.sortFloorPlanPic();
      });
    },
    goToPhotoSwipe(){
      let self =  this;
      this.isScroll = !this.isScroll;
      if(this.isScroll && this.swiper){
        this.swiper.destroy(true, true);
        this.swiper = null;
        return
      }
      this.scrollBar();
      if (this.swiper) return;
      this.$nextTick(() => {
        self.swiper = new Swiper('.swiper', {
          autoHeight: false,
          centeredSlides: true,
          lazyPreloadPrevNext: 1,
          pagination: {
            el: '.swiper-pagination',
            type: 'fraction'
          },
          zoom: {
            maxRatio: 5,
          },
        });
        if(this.photoIndex > 0){
          self.swiper.slideTo(this.photoIndex);
          this.photoIndex = -1;
        }
        self.controlSwiperSlideHeight();
        self.updateFloorPlanBottom();
      });
    },
    controlSwiperSlideHeight(){
      let swiperSlide = document.getElementsByClassName('swiper-slide');
      for(let i=0;i<swiperSlide.length; i++){
        if(this.tabsTitle.notesTitle){
          swiperSlide[i].style.height = 'auto';
        }else{
          swiperSlide[i].style.height = '100%';
        }
      }
    },
    showOrigImg(src){
      this.currentPic.src = src;
      this.currentPic.display = true;
      this.$nextTick(() => {
        let currentImgDiv = document.getElementsByClassName('currentPic');
        currentImgDiv[0].scrollLeft = 0;
        this.setMarginbyHeight();
        this.zoomableImage();
      });
    },
    setMarginbyHeight(){
      const img = document.getElementById('zoomable-image');
      let renderedHeight = img.offsetHeight;
      let screenHeight = window.innerHeight;
      if (renderedHeight >= screenHeight){
        img.style.margin = '';
      } else {
        img.style.margin = 'auto';
      }
    },
    // scrollToTop(){
    //   if(this.isScroll){
    //     document.getElementById('useScroll').scrollTop = 0;
    //   }else{
    //     this.swiper.slideTo(0);
    //   }
    // },
    zoomableImage(){
      const img = document.getElementById('zoomable-image');
      img.addEventListener('touchstart', this.handleTouchStart);
      img.addEventListener('touchmove', this.handleTouchMove);
      img.addEventListener('touchend', this.handleTouchEnd);
    },
    handleTouchStart(e){
      if (e.touches.length >= 2 && !this.isScaling) {
        this.startDistance = this.getDistance(e.touches[0], e.touches[1]);
      }
    },
    handleTouchMove(e){
      const img = document.getElementById('zoomable-image');
      if (e.touches.length >= 2 && !this.isScaling) {
        this.endDistance = this.getDistance(e.touches[0], e.touches[1]);
        this.scaleFactor = this.endDistance / this.startDistance;
        img.style.transform = `scale(${this.scaleFactor})`;
        img.style.transformOrigin = '0 0';
        this.setMarginbyHeight();
        this.startDistance = this.endDistance;
        this.isScaling = true
      }
    },
    handleTouchEnd(e){
      this.isScaling = false;
    },
    getDistance(touch1, touch2) {
      const dx = touch1.pageX - touch2.pageX;
      const dy = touch1.pageY - touch2.pageY;
      return Math.sqrt(dx * dx + dy * dy);
    },
    closeBigPic(){
      let img = document.getElementById('zoomable-image');
      img.removeEventListener('touchmove', this.handleTouchMove);
      img.removeEventListener('touchstart', this.handleTouchStart);
      img.removeEventListener('touchend', this.handleTouchEnd);
      img.style.transform = '';
      img.style.transformOrigin = '';
      this.currentPic.display = false;
      this.startDistance = 0;
      this.endDistance = 0;
      this.scaleFactor = 1
    },
    scrollBar(){
      let navLinksContainer = document.querySelector('.list-nav-container');
      if (!navLinksContainer) {
        return;
      }
      let navLinkSelected = document.querySelector('.list-nav-link.selected');
      if(!navLinkSelected) return;
      let swiperPagAndIconWidth = document.querySelector('.swiperPagAndIcon').clientWidth;
      let selectedWidth = navLinkSelected.offsetWidth;
      let navListWidth = window.innerWidth - swiperPagAndIconWidth;
      let scrollPos = navLinkSelected.offsetLeft + selectedWidth / 2 - navListWidth / 2;
      navLinksContainer.scrollLeft = scrollPos;
      let navLinkActive = document.querySelector('.list-nav-active');
      navLinkActive.style.left = navLinkSelected.offsetLeft + selectedWidth /2 - 20 + 'px';
    },
    updateSwiperOrScrollToTop(){
      if(!this.isScroll){
        this.$nextTick(()=>{
          this.swiper.update();
          this.swiper.slideTo(0);
          this.controlSwiperSlideHeight();
          this.updateFloorPlanBottom();
        });
      } else {
        document.getElementById('useScroll').scrollTop = 0;
        // this.scrollToTop();
      }
    },
    sortFloorPlanPic(){
      let sortResult = this.floorPlanPic.sort((a,b)=>{return b.score - a.score})
      this.floorPlanPic = sortResult;
      this.selecteBd(this.selectedCondit);
    },
    // 根据底部地址信息的高度，计算floorplan在swiper时距离底部的bottom
    updateFloorPlanBottom(){
      let fpSwipeBotmArry = document.querySelectorAll('.fpSwipeBotm');
      let height = document.querySelector('.infoBottom').clientHeight;
      for(let i=0;i<fpSwipeBotmArry.length;i++){
        fpSwipeBotmArry[i].style.bottom = height + 1 + 'px';
      }
    }
  }
}
initUrlVars();
var app = Vue.createApp(photoSwipeScroll);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('sign-up-form', SignUpForm);
app.component('flash-message', flashMessage);
app.mount('#photoSwipeScroll');