var properties = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: false,
      loading: false,
      hasMap:true,
      showPropTable:false,//切换prop显示样式
      favProps:[],
      currentGrp:{
        idx: "0",
        val: {v: "Default"}},
      grp:0,
      grps:[],
      grpsOptions:false,
      showGrps:false,//切换收藏分组
      showShowing:false,
      showShare:false,
      showFavGroups:false,//收藏分组操作
      prop:{fav:false,favGrp:[]},//待添加收藏的房源
      action:'',//
      rcmdHeight:0,
      propImgHeight:0,
      selectedProp:[],//选择需要分享或收藏的prop
      shareList:[],
      cntTotal : 0,
      wDl: true,
      wSign: true,
      scrollElement:null,
      dispVar:{
        lang:'zh-cn',
        sessionUser: {},
        allowedShareSignProp:false,
        listShareMode:true,
        isCip:false,
        isDevGroup:false,
        isRealGroup:false,
        isLoggedIn: false,
        isApp: false,
        isNoteAdmin:false,
      },
      school:'University of Toronto',
      schoolProv:'ON',
      centerLat: 43.66090860000001,
      centerLng: -79.39595179999999,
      isArchived:false,
      archivedGrps:{},
      hasArchivedGrps:false,
      datas:[
        'allowedEditGrpName',
        'lang',
        'isVipRealtor',
        'isRealtor',
        'sessionUser',
        'allowedShareSignProp',
        'shareUID',
        'isVipUser',
        'isCip',
        'shareAvt',
        'distances',
        'rentalSorts',
        'languageAbbrObj',
        'isDevGroup',
        'isRealGroup',
        'isLoggedIn',
        'coreVer',
        'isEmailVerified',
        'isApp',
        'isNoteAdmin'
      ],
      sort:'dist-asc',
      dist:500,
      recos:[],
      favMap:{},
      archivedMap:{},
      clacClass:'one',
      init:true,
      strings:{
        searchID:{key:"Search",ctx:''},
        alreadyExists:{key:"The current property already exists",ctx:''},
        saveFailed:{key:"Failed",ctx:''}
      },
      folderSort:'time',
      filterStatus: 'All',
      statusType: [{
        k: 'All',
        v: 'All'
      },{
        k: 'Sale',
        v: 'For Sale'
      },{
        k: 'Sold',
        v: 'Sold'
      },{
        k: 'Delisted',
        v: 'Delisted'
      }]
    }
  },
  beforeMounted(){
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    if(vars.grp){
      self.grp=vars.grp
    }
    self.getFavProps();
    self.getRecentProps()
    if (!window.showMapView){
      window.showMapView = this.showMapView;
    }
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.rcmdHeight = parseInt(window.innerWidth/1.6);
    self.propImgHeight = parseInt((window.innerWidth-48)/1.6/2);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('reset',()=>{
      this.reset()
    })
    bus.$on('afterFavAction',(d)=>{
      this.afterFavAction(d)
    })
    bus.$on('prop-fav-retrieved',(grps)=>{
      self.grps = self.parseGrps(grps);
    })
    bus.$on('refresh-archive-folder', ({grp,isArchived})=>{
      self.isArchived = isArchived;
      self.refreshFolder(grp,isArchived);
    });
    bus.$on('prop-fav-changed',({prop,grps})=>{
      self.favProps.forEach((p)=>{
        if(p._id == prop._id){
          p = prop
        }
      })
      self.grps = grps
    })
    bus.$on('selectGroup',(g)=>{
      this.getFavProps(g+'')
    })
    bus.$on('commit-selected',(d) => {
      self.selectedProp = d;
      self.commitSelected();
    })
    if(window.loadLazyImg){
      loadLazyImg()
    };
    this.clacItemClass();
    self.sortByMtWithoutDefault = window.sortByMtWithoutDefault
  },
  computed:{
    sessionUserContact: function(){
      var ret = [], user;
      if (!this.wSign) {
        return '';
      }
      user = this.dispVar.sessionUser || {};
      var nm = '';
      if (this.dispVar.lang == 'en') {
        nm = user.nm_en;
      } else {
        nm = user.nm_zh;
      }
      // ret += user.fn?user.fn+user.ln:user.nm;
      if (nm){
        ret.push(nm);
      }else{
        ret.push(user.nm);
      }
      if(user.mbl){
        ret.push(user.mbl);
      }
      ret.push(user.eml);
      ret = ret.join(', ');
      return ret;
    },
    shareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : '/img/create_exlisting.png';
    },
    mShareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : this.dispVar.shareAvt;
    },
    mShareData: function () {
      var shareList = this.shareList, prop = this.prop, lang = this.dispVar.lang;
      var data_text = `id=${shareList.join(',')}&tp=listingList&lang=${lang}&share=1`
      if (this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        data_text += "&uid=" + this.dispVar.shareUID;
      }
      return data_text;
    },
    shareData: function () {
      function userListing(prop){
        var id = prop.adrltr?prop.adrltr._id:null;
        if (id == prop.uid) {
          return true;
        }
        return false;
      }
      var prop = this.prop, lang = this.dispVar.lang;
      var propID;
      if (this.isRMProp(prop)) {
        propID = prop.id;
      } else {
        propID = prop._id || prop.sid;
      }
      var shareUID = this.dispVar.shareUID;
      var data_text = `id=${propID}&tp=listing&lang=${lang}`
      if (this.isRMProp(prop)) {
        // data_text += '&ownerid='+prop.uid;
        // user(not realtor) not following, use id
        // or from user
        if (!shareUID || userListing(prop)) {
          shareUID = prop.uid;
        }
      }
      if (this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        if (this.dispVar.isLoggedIn) {
          data_text += "&uid=" + shareUID;
        }
      }
      // force append download
      if (this.isRMProp(prop)) {
        if (!/wDl=1/.test(data_text)) {
          data_text += '&wDl=1';
        }
        data_text += "&wSign=1";
      }
      return data_text;
    }
  },
  methods: {
    refreshFolder(grp,isArchived){
      var grpInfo = null,self = this;
      if(isArchived){
        grpInfo = self.favMap[grp];
        delete self.favMap[grp];
        self.archivedMap[grp] = grpInfo;
      }else{
        grpInfo = self.archivedMap[grp];
        delete self.archivedMap[grp];
        self.favMap[grp] = grpInfo;
      }
      self.grps = self.parseGrps(self.favMap);
      self.archivedGrps = self.parseGrps(self.archivedMap);
      if(Object.keys(self.archivedMap).length > 0){
        self.hasArchivedGrps = true;
      }else{
        self.hasArchivedGrps = false;
      }
      setTimeout(() => {
        self.toggleArchived();
      }, 1000);
    },
    getRecentProps(){
      var self = this;
      if (typeof page != undefined && page == 'student_rental') {
        return
      }
      var url = '/1.5/props/recentFavProps';
      setLoaderVisibility('block')
      fetchData(url,{},function(err,ret) {
        setLoaderVisibility('none')
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          if (ret.items && ret.items.length > 0){
            ret.items.map(prop=>{
              prop.selected = false;
              // prop.grpInfo = self.favMap[prop.g]
            })
          } else {
            window.bus.$emit('flash-message',self.getTranslate('noResults'));
          }
          self.recos = ret.items;
        }
      })
    },
    isRMProp (prop) {
      return /^RM/.test(prop.id);
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getFavProps(null,'more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    openSchoolModal() {
      var cfg = {hide:false,title:'Select School'};
      var url = '/1.5/direction/places?prov='+this.schoolProv;
      RMSrv.getPageContent(url, '#callBackString', cfg, (val) => {
        if (val == ':cancel') {
          return;
        }
        var school;
        try {
          school = JSON.parse(val);
        } catch (error) {
          school = {};
        }
        this.school = school.nm;
        this.centerLat = school.lat;
        this.centerLng = school.lng;
        this.schoolProv = school.prov;
        this.getFavProps('studentRental');
      });
    },
    toggleArchived(){
      document.querySelector('.folderSort').classList.toggle('hide')
      if(!document.querySelector('.archivedGrps')){
        return;
      }
      document.querySelector('.archivedGrps').classList.toggle('all')
      document.querySelector('.icon-up-nav').classList.toggle('hide')
      document.querySelector('.icon-down-nav').classList.toggle('hide')
    },
    getFavProps:function(grp,action) {
      var self = this;
      if(grp){
        self.grp = grp;
        self.page = 0;
      }
      if(action == 'archived'){
        self.isArchived = true;
      }
      if(action == 'normal'){
        self.isArchived = false;
      }
      window.bus.$emit('is-archived',self.isArchived);
      var url = '/1.5/props/favProps';
      var body = {grp:self.grp,page:self.page};
      if (this.filterStatus !== 'All'){
        body.favSort = this.filterStatus;
      }
      if (typeof page != undefined && page == 'student_rental') {
        url = '/1.5/props/search';
        body = {ptype:'r',src:'mls',saletp:'lease',page:self.page,sort:self.sort,centerLat:self.centerLat,centerLng:self.centerLng,dist:self.dist};
      }
      setLoaderVisibility('block')
      fetchData(url,{body},function(err,ret) {
        setLoaderVisibility('none')
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          if (ret.items && ret.items.length > 0){
            ret.items.map(prop=>{
              prop.selected = false;
            })
          } else {
            window.bus.$emit('flash-message',self.getTranslate('noResults'));
          }
          if(action == 'more'){
            self.favProps = self.favProps.concat(ret.items)
          }else{
            self.favProps = ret.items;
            self.selectedProp = [];
            window.bus.$emit('reset-sel-prop');
          }
          self.favMap = ret.grps;
          self.grps = self.parseGrps(ret.grps);
          if(ret.archivedGrps && Object.keys(ret.archivedGrps).length>0){
            self.archivedMap = ret.archivedGrps;
            self.hasArchivedGrps = true;
            self.archivedGrps = self.parseGrps(ret.archivedGrps);
          }else{
            self.hasArchivedGrps = false;
          }
          self.showGrps = false;
          if(self.init && self.grps.length >= 10){
            self.showGrps = true;
            self.init = false;
          }
          self.cntTotal = ret.cnt;
          if(self.favProps.length < self.cntTotal && ret.items.length){
            self.hasMoreProps = true;
          }else{
            self.hasMoreProps = false;
          }
        }
      })
    },
    showGroups:function(){
      var self = this;
      self.showGrps = !self.showGrps;
      self.$forceUpdate();
    },
    popupAction:function(act){
      var self = this;
      if(self.action == act){
        self.reset();
      }else{
        trackEventOnGoogle('saves','properties',act)
        if (act == 'edit'){
          window.bus.$emit('editGroups',{grp:this.grp,grpInfo:this.currentGrp.val})
          self.showPropTable = false;
        }else {
          self.showPropTable = true;
          self.action=act;
        }
      }
      // window.bus.$emit('toggle-showing')
      self.$forceUpdate();
    },
    reset:function(){
      var self = this;
      self.grpsOptions = false;
      self.showGrps = false;
      self.showShowing = false;
      self.showFavGroups = false;
      self.showPropTable = false;
      self.action='';
    },
    toggleFav(prop){
     this.showFavGroups = true;
     this.prop = prop;
    },
    parseGrps(grp={}){
      var l = [],self=this;
      for (let k in grp) {
        if (k !== 'cntr') {
          l.push({idx:k, val:grp[k],mt:grp[k].mt || -1});
        }
        if(k == self.grp){
          self.currentGrp = {idx:k, val:grp[k],mt:grp[k].mt || -1}
        }
      }
      return self.sortByMtWithoutDefault(l,this.folderSort);
    },
    afterFavAction(d){
      var self = this;
      if (d.grps) {
        self.grps = self.parseGrps(d.grps);
      }
      if (d.mode == 'clear' && !d.noEmit) {
        // window.bus.$emit('prop-fav-cleared', '');
        self.favProps = [];
      } else if (d.mode == 'delete') {
        self.grp = 0;
        self.getFavProps();
        // window.bus.$emit('prop-fav-grp-deleted', '');
      }
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    goCMA(){
      let url = `/1.5/htmltoimg/dottemplate?lang=${this.dispVar.lang}&showSign=true&selModel=diff&nm=comparetable&ids=`
      var prop = this.selectedProp;
      if (prop) {
        if(typeof(prop) != "string") prop = prop.join(',')
        url += prop;
      }
      let cfg = {title:this.getTranslate('rm')};
      openContent(url,cfg)
    },
    commitSelected(){
      var self = this;
      self.showPropTable = false;
      if(self.action == 'showing'){
        window.bus.$emit('toggle-showing',self.selectedProp);
      }else if(self.action == 'cma'){
        this.goCMA()
      }else{
        if (self.selectedProp.length == 1 ){
          for (var p of self.favProps){
            if (p._id == self.selectedProp[0]){
              this.prop = p;
            }
          }
          RMSrv.showSMB('show');
        }else{
          RMSrv.showSMB('show','m-share-');
        }
      }
      self.shareList = self.selectedProp;
      window.bus.$emit('reset-sel-prop')
      self.selectedProp = [];
      self.favProps.forEach(p => {
        p.selected = false;
      })
      self.$forceUpdate();
    },
    showMapView(){
      var d = vars.d || '';
      if(d){
        d = `&d=${d}`
      }
      var url = `/1.5/mapSearch?appMapMode=fav&mode=map&grp=${this.grp}${d}`
      window.location = url;
    },
    clacItemClass:function(){
      if(this.recos.length==1){
        this.clacClass = 'one'
      }else if(this.recos.length==2){
        this.clacClass = 'two'
      }else{
        this.clacClass = 'three'
      }
    },
    getdotdatetime(t) {
      if (!t) {
        return '';
      }
      var t = new Date(t);
      var today = new Date();
      if(t.getFullYear() ==  today.getFullYear() && t.getMonth() ==  today.getMonth() && t.getDate() == today.getDate()){
        return t.getHours() + ':' + (t.getMinutes()<10?'0':'') + t.getMinutes();
      }else{
        return t.getFullYear() + '.' + (t.getMonth()+1) + '.' + t.getDate()
      }
    },
    sortFolder(sortBy){
      this.grps = this.sortByMtWithoutDefault(this.grps,sortBy);
      this.folderSort = sortBy;
    },
    selecteStatus(type){
      if (this.filterStatus == type) return;
      this.page = 0;
      let divElement = document.querySelector('.prop-list');
      if(divElement){
        divElement.scrollIntoView(true);
      }
      this.filterStatus = type;
      this.getFavProps(this.grp,'normal',type)
    }
  },
};
initUrlVars();
var app = Vue.createApp(properties);
app.component('prop-item', propItem);
app.component('prop-element-scroll-x', propElementScrollX);
app.component('prop-fav-actions', propFavActions);
app.component('prop-sel-table', propSelTable);
app.component('add-to-showing', addToShowing);
app.component('share-dialog', shareDialog);
app.component('listing-share-desc', listingShareDesc);
app.component('flash-message', flashMessage);
app.component('check-notification', checkNotification);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#properties');