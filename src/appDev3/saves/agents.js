var agents = {
  data() {
    return {
      datas:[
        'isCip',
        'isApp',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isMerchant',
        'isVipAlliance',
        'isRealtor',
        'isAdmin',
        'userCity',
        'ownerData',
        'shareHost',
        'isProdSales',
        'verifiedRole'
      ],
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        sessionUser: '',
        reqHost:'',
        shareHost:'',
        isMerchant:false,
        isVipAlliance: false,
        isAdmin: false,
        isRealtor:false,
        userCity: {o:'Toronto',n:'多伦多'},
        ownerData:{},
        isProdSales:false,
        verifiedRole:'realtor'
      },
      sa:{},
      message:'',
      resultList:[],
      error:'',
      name:'',
      loading:false,
      selectedSub:[],
      subcategory:[],
      allPosts:[],
      pgNum: 1,
      posts_more: false,
      currentPage:'person',
      checkedUser:[],
      share:false,
      currentCat: null,
      resultByCat:[],
      shareMode:false,
      curRealtor:{},
      scrollElement:'',
      hasMoreProps:false,
      page:0,
    };
  },
  mounted() {
    var self = this;
    this.$getTranslate(this);
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getFavAgents();
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d)
    });
    bus.$on('update-click', function (opt) {
      self.updateClick(opt.tp, {}, opt.cb);
    });
    bus.$on('set-cur-realtor', function (r) {
      self.setCurRealtor(r);
    });
    bus.$on('set-fold', function ({u,idx,arr}) {
      self.setFold(u,idx,arr);
    });
    bus.$on('show-wecard', function (r) {
      self.showWecard(r);
    });
    bus.$on('toggleFav', function ({fav,uid}) {
      self.fav(fav,uid);
    });
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  methods: {
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        // console.log('do loading more');
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getFavAgents();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    setCurRealtor(r) {
      var self = this;
      self.curRealtor = r;
      self.updateClick('contact');
      trackEventOnGoogle('saves','agent','setCurRealtor')
      window.bus.$emit('toggle-contact-smb',r)
      // return self.toggleSMB();
    },
    setFold(u, idx, arr){
      // var u2 = Object.assign(u, {fold:false});
      // var u2 = {};
      // for (let key of Object.keys(u)) {
      //   u2[key] = u[key];
      // }
      // u2.fold = false;
      // this[arr].$set(idx, u2);
      // this.$set(this[arr],idx,u2);
      this.resultList[idx].fold = false;
      // this.$set(u, 'fold', value);
    },
    showWecard(u){
      this.curRealtor = u;
      this.updateClick('wesite');
      var url = '/1.5/wesite/' + u._id + '?inFrame=1';
      trackEventOnGoogle('saves','agent','showWecard')
      this.tbrowser(url);
    },
    getFavAgents() {
      var self = this;
      var limit = 10
      var self = this, data = {page:self.page,limit};
      fetchData('/1.5/yellowpage/myfav', {body:data},function (err,ret) {
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          var resultList= self.formatResult(ret.resultList)
          if(self.page == 0){
            self.resultList = resultList;
          }else{
            self.resultList = self.resultList.concat(resultList)
          }
          if(resultList.length >= limit){
            self.hasMoreProps = true
          }else{
            self.hasMoreProps = false
          }
        }
      });
    },
    byteCount(s) {
      return encodeURI(s).split(/%..|./).length - 1;
    },
    formatResult(resultList){
      var ret = [];
      var agentIdMap = {};
      var changedTa2 = false;
      // NOTE: set fold and recos
      for (var i = 0; i < resultList.length; i++) {
        let u = resultList[i];
        u.fold = false;
        if (u.itr && this.byteCount(u.itr)>50) {
          u.fold = true;
        }
        ret.push(u);
      }
      return ret;
    },
    fav(fav, uid) {
      event.stopPropagation();
      var self = this;
      if (!this.dispVar.isLoggedIn) {
        if (!this.dispVar.isApp)  {
          this.goTo('/app-download?lang='+this.dispVar.lang);
        } else {
          this.goTo('/1.5/user/login');
        }
      } else {
        fetchData('/1.5/yellowpage/fav', {body:{uid: uid, fav: fav}},function (err,ret) {
          if (err || ret.e) {
            return RMSrv.dialogAlert(err || ret.e);
          } else {
            if (ret.ok) {
              var user = self.resultList.find(function(data) {
                return data._id == uid;
              });
              if (user) {
                user.isfaved = fav;
              }
              window.bus.$emit('flash-message',ret.msg)
              trackEventOnGoogle('saves','agent',fav?'fav':'unfav')
            }
          }
        });
      }
    },
  },
  computed:{
    shareUrl:function() {
      return this.dispVar.shareHost + '/1.5/yellowpage/myfav?share=1&lang='+this.dispVar.lang+'&faved='+this.checkedUser.join(',') ;
    },
  },
};
initUrlVars();
var app = Vue.createApp(agents);
app.component('flash-message', flashMessage);
app.component('contact-realtor', contactRealtor);
app.component('agent', agent);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
app.mixin(userMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#agents');