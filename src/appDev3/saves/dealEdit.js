var dealsEdit={
  mounted() {
    var bus = window.bus, self = this;
    self.$getTranslate(this);
    self.getPageData(self.datas, {}, true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    // bus.$on('change-claim',function(claim) {
    //   if(!claim.stakeholders) claim.stakeholders = {};
    //   // 直接赋值会影响页面显示内容
    //   if(!claim){
    //     claim.pcOfShr = self.claim.maxPcOfShr;
    //   }
    //   try {
    //     self.claim = JSON.parse(JSON.stringify(claim));
    //   } catch (error) {
    //     console.log(error);
    //   }
    // });
    self.showBackdrop = vars.popup;
    self.getClaimInfo();
  },
  data(){
    return {
      claimRoleMap:{
        listing:'Listing agent',
        coop:'Coop agent',
        dual:'Dual agent'
      },
      claimTypeRoleMap:{
        Sale:['Seller','Buyer'],
        Rent:['Landlord','Tenant']
      },
      tokenKeyMap:{
        'RM Direct': 'Firsthand',
        'RM Referral' : 'Secondary',
        'Self' : 'Self',
      },
      stakeholdersRole:[],
      waitEdit:-1,
      peopleTemplate:{
        role:'',
        nm:'',
        source:'',
        pcOfShr:100,
        claimSystemToken:0,
        _id:''
      },
      showRole:false,
      claimTemplate: {
        side:'',
        m:'',
        people:[],
      },
      claimMap:{},
      claimList:[],
      role:'',
      prop:{},
      loading:false,
      strings:{
        CHECKINFO:{key:"Please confirm all information is correct. After submitting, only the manager can assist with deletion."},
        CANCLE:{key:"Cancel"},
        COMFIRM:{key:"Confirm"},
        SOURCENOTMATCH:{key:"Source not match."},
        BADCONTACT:{key:"Bad Contacts info."},
      },
      datas:[
        'sessionUser',
        'isClaimAdmin',
        'isRealGroup'
      ],
      dispVar:{
        sessionUser:{},
        isClaimAdmin:false,
        isRealGroup:false
      },
      claimSystemTokenMap:{}
    }
  },
  methods:{
    checkInput(content){
      if (content && content.length){
        return replaceJSContent(content);
      }
      return content;
    },
    calcHeight(id){
      var textarea = document.querySelector('#'+id);
      textarea.style.height = "inherit";
      textarea.style.height = `${textarea.scrollHeight}px`;
    },
    getClaimInfo(){
      var self = this
      fetchData('/propClaim/detail',{body:{id: vars.id}},function(err,ret) {
        var errMsg = err || ret.e;
        self.loading = false;
        if(errMsg){
          window.bus.$emit('flash-message',errMsg.toString());
          return;
        }
        self.prop = ret.prop;
        if(ret.list){
          self.claimList = ret.list;
        }
        if(ret.prop.claimSystemToken){
          self.stakeholdersRole = self.claimTypeRoleMap[ret.prop.claimInfo.tp];
          self.claimSystemTokenMap = ret.prop.claimSystemToken
        }
      })
    },
    closeModals(info){
      var returnInfo = ':cancel'
      if(info){
        returnInfo = JSON.stringify(info);
      }
      setTimeout(function(){
        window.rmCall(':ctx:'+returnInfo);
      },1000);
    },
    createClaimTemplate(side){
      var self = this,i = side == 'listing' ? 0 : 1;
      var pcOfShr = self.prop.claimInfo[side].maxPcOfShr
      var people = Object.assign({},this.peopleTemplate,{role:this.stakeholdersRole[i],pcOfShr:pcOfShr,maxPcOfShr:pcOfShr});
      var claim = Object.assign({},this.claimTemplate,{side:side,people:[people]});
      return claim;
    },
    setClaimRole(k){
      this.role = k;
      this.claimMap = {};
      if(k == 'dual'){
        this.claimMap.listing = this.createClaimTemplate('listing');
        this.claimMap.coop = this.createClaimTemplate('coop');
      }else{
        this.claimMap[k] = this.createClaimTemplate(k);
      }
      let elements = document.getElementsByClassName('memo');
      for (var i = 0; i < elements.length; i++) {
        elements[i].style.height = "21px";
      }
    },
    editClaim(){
      let body = Object.assign({},this.claimMap),self=this;
      if(self.loading){
        return
      }
      if(! self.canClaim){
        return
      }

      body.pid = self.prop._id;
      body.lst = self.prop.lst;
      body.sldd = self.prop.sldd;
      self.loading = true
      fetchData('/propClaim/edit',{body},function(err,ret) {
        var errMsg = err || ret.e;
        self.loading = false
        if (errMsg) {
          window.bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          self.closeModals({claimed:1});
        }
        if(ret.msg){
          window.bus.$emit('flash-message',ret.msg);
        }
      })
    },
    confirmClaim(){
      var self = this;
      if(this.loading){
        return
      }
      if(! this.canClaim){
        return
      }
      function _doClaim(idx) {
        if (idx+'' == '2') {
          self.editClaim();
        }
      }
      RMSrv.dialogConfirm(this.$_(this.strings.CHECKINFO.key),_doClaim, '', [this.$_(this.strings.CANCLE.key), this.$_(this.strings.COMFIRM.key)]);
    },
    showClientFn(client,i,side){
      var self = this,isNew = false;
      if (!(self.dispVar.isClaimAdmin || self.dispVar.isRealGroup)){
        return
      }
      if(client.maxPcOfShr == 0){
        return
      }
      var cfg = {hide:false,title:this.$_('Contacts','contactCrm')};
      var url = '/1.5/crm?noBar=1&isPopup=1&owner=1&claim=1';
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          var crm = JSON.parse(val);
          let {source,_id,nm} = crm;
          if(!_id || !nm || !source){
            return window.bus.$emit('flash-message',self.$_(self.strings.BADCONTACT.key));
          }
          let tokenKey = self.tokenKeyMap[source];
          if(!tokenKey ){
            return window.bus.$emit('flash-message',self.$_(self.strings.SOURCENOTMATCH.key));
          }
          client._id = _id;
          client.nm = nm;
          client.source = source;
          client.claimSystemToken = self.claimSystemTokenMap[tokenKey];
          client.sourceKey = tokenKey;
          self.$set(self.claimMap[side].people,i,client);
          self.$forceUpdate();
        } catch (e) {
          console.error(e);
        }
      });
    },
    checkPcOfShrRange(people){
      if(!people.pcOfShr) return;
      if(Number.isNaN(people.pcOfShr)) people.pcOfShr = '0'
      people.pcOfShr = parseInt(people.pcOfShr);
      if(people.pcOfShr > people.maxPcOfShr) people.pcOfShr = people.maxPcOfShr;
      if(people.pcOfShr < 0) people.pcOfShr = '0';
    }
  },
  computed:{
    canClaim:function(){
      let body = this.claimMap,canIClaim=true;
      if(Object.keys(body).length == 0) return false;

      for (let k in body) {
        body[k].people.forEach(p => {
          if(p.maxPcOfShr == 0){
            p.claimSystemToken = 0
            p.nm = ""
            p.pcOfShr = 0
            p.source = ""
            p._id = ""
            p.sourceKey = ""
            canIClaim = false;
          }else{
            canIClaim = true;
          }
          if(!p.pcOfShr || !p._id || !p.source || !p.sourceKey || !p.claimSystemToken){
            canIClaim = false;
          }
        });
      }
      // ['coop','listing'].forEach(k => {
      //   body[k].people.forEach(p => {
      //     if(!p.pcOfShr || !p._id || !p.source)
      //       canIClaim = false;
      //   });
      // });
      return canIClaim;
    },
  }
};

initUrlVars();
var app = Vue.createApp(dealsEdit);
app.component('flash-message', flashMessage);
app.component('prop-item', propItem);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#dealsEdit');