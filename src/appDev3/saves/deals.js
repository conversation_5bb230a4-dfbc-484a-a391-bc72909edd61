// saves/deals inreal group认领过的房源，noteAdmin可以看所有
var deals = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: false,
      cntTotal : 0,
      scrollElement:null,
      hasMap:true,
      list:[],
      rcmdHeight:0,
      dispVar:{
        lang:'zh-cn',
        isCip:false,
        isLoggedIn:false,
        isClaimAdmin:false,
        sessionUser:{}
      },
      datas:[
        'lang',
        'isCip',
        'allowedEditGrpName',
        'isVipRealtor',
        'isLoggedIn',
        'isRealGroup',
        'isApp',
        'isClaimAdmin',
        'isDevGroup',
        'monthAndYearArray',
        'sessionUser'
      ],
      sortBy:['tp','date','side','agent','sort'],
      showClaim:false,
      agent:'',
      agentInfo:{},
      tp:'',
      tpList:['All','Sale','Rent','Deal failed'],
      side:'',
      sideList:['All','Coop','Listing'],
      date:'',
      dateList:[],
      filterList:[],
      sort:'tsNum',
      showBackdrop:false,
      users:[],
      waitEditIdx:-1
    }
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.rcmdHeight = parseInt(window.innerWidth/2);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.dateList = d.monthAndYearArray;
      self.dateList.unshift('All');
      if(d.isClaimAdmin){
        self.getInrealMembers();
      }
    });
    self.getClaimedList();
    // 设置全局 showMap 函数
    if (!window.showMap){
      window.showMap = this.goMap;
    }
    bus.$on('close-edit',()=>{
      self.showClaim = false;
    })
    bus.$on('refresh-claim',(claim)=>{
      self.list.splice(self.waitEditIdx,1,claim);
      self.waitEditIdx = -1;
    })
  },
  methods: {
    selectSort(tp){
      if(this.sort == tp) return ;
      this.sort = tp;
      this.searchByFilter()
    },
    deleteClntFilter(){
      this.agentInfo = {};
      this.agent = '';
      this.searchByFilter()
    },
    addFilter(tp){
      if(this.waitFilter == tp && (this.filterList.length || this.showAgents)){
        return this.reset()
      }
      this.waitFilter = tp;
      if(tp == 'agent'){
        this.showAgents = !this.showAgents;
      }else{
        this.filterList = this[tp+'List'];
      }
      this.showBackdrop = true;
    },
    selectFilter(fld){
      if(fld == 'All'){
        this[this.waitFilter] = '';
      }else{
        this[this.waitFilter] = fld;
      }
      this.searchByFilter();
      this.reset()
    },
    // 筛选inreal经济
    selectAgent(u){
      this.agentInfo = u;
      this.agent = u._id;
      this.searchByFilter();
      this.reset()
    },
    searchByFilter(){
      var self = this;
      self.page = 0;
      self.filterList = [];
      if(document.querySelector('.prop-list-container')){
        document.querySelector('.prop-list-container').scrollTop = 0;
      }
      self.getClaimedList();
    },
    // noteAdmin获取inreal经济列表
    getInrealMembers(){
      var self = this;
      var url = '/group/getMembers';
      var body = {page:self.page};
      self.sortBy.forEach(type => {
        if(self[type]){
          body[type] = self[type];
        }
      });
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          if(ret.ok){
            self.users = ret.users;
          }
        }
      })
    },
    getCurrentPropMaxPcOfShare(claim,cb){
      let self = this;
      fetchData('/propClaim/maxPc',{body:claim},function(err,ret) {
        var errMsg = err || ret.e;
        if (errMsg) {
          window.bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          return cb(ret);
        }
      })
    },
    // noteAdmin删除claim过的房源
    confirmDelete(prop,index){
      if(! this.dispVar.isClaimAdmin){
        return
      }
      let self = this;
      let deleteTip = self.$_('Confirm delete?');
      let cancel = self.$_('Cancel');
      let yes = self.$_('Yes');
      function _doDelete(idx) {
        if (idx+'' == '2') {
          self.deleteClaim(prop,index);
        }
        if (idx+'' == '1') {
          prop.showDelete = false;
        }
      }
      return RMSrv.dialogConfirm(deleteTip, _doDelete, '', [cancel, yes]);
    },
    deleteClaim(prop,idx){
      if(! this.dispVar.isClaimAdmin){
        return
      }
      let self = this;
      fetchData('/propClaim/delete',{body:{id:prop._id}},function(err,ret) {
        var errMsg = err || ret.e;
        if (errMsg) {
          window.bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          self.showClaim = false;
          if(self.list[idx+1] && self.list[idx].showDate){
            self.list[idx+1].showDate = true;
          }
          self.list.splice(idx,1);
        }
        if(ret.msg){
          window.bus.$emit('flash-message',ret.msg);
        }
      })
    },
    reset:function(){
      var self = this;
      self.showAgents = false;
      self.filterList = [];
      self.grpsOptions = false;
      self.showFavGroups = false;
      self.showBackdrop = false;
    },
    toggleFav(prop){
     this.showFavGroups = true;
     this.prop = prop;
    },
    // listScrolled:function(){
    //   var self = this, wait = 400;
    //   if (!self.waiting && self.hasMoreProps) {
    //     self.waiting = true;
    //     setLoaderVisibility('block');
    //     setTimeout(function () {
    //       self.waiting = false;
    //       var element = self.scrollElement;
    //       if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
    //         if (self.hasMoreProps) {
    //           self.page += 1;
    //           self.getClaimedList('more')
    //         } else {
    //           setLoaderVisibility('none');
    //         }
    //       } else {
    //         setLoaderVisibility('none');
    //       }
    //     }, wait);
    //   }
    // },
    // 获取claimed房源列表
    getClaimedList:function(more) {
      var self = this;
      var url = '/propClaim/list';
      var body = {page:self.page};

      self.sortBy.forEach(type => {
        if(self[type]){
          body[type] = self[type];
        }
      });
      setLoaderVisibility('block');
      fetchData(url,{body},function(err,ret) {
        setLoaderVisibility('none');
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          self.list = ret.list || [];
        }
      })
    },
    // claimed经济打开claim编辑部分
    openClaim(claim,idx){
      var self = this;
      if(!this.waitEdit || (claim._id != this.waitEdit._id)){
        setLoaderVisibility('block');
        this.getCurrentPropMaxPcOfShare(claim,(claimedInfo)=>{
          setLoaderVisibility('none');
          if(!claim.pcOfShr){
            claimedInfo.pcOfShr = '100';
          }
          claim = Object.assign({},claim,claimedInfo);
          self.waitEdit = claim
          window.bus.$emit('change-claim',claim)
        })
      }
      this.waitEditIdx = idx;
      this.showClaim = true;
    },
    // 跳转到地图页面显示已认领房源
    goMap(){
      var url = `/1.5/map/notesMap?from=claim`;
      this.sortBy.forEach((type) => {
        const val = this[type];
        if (val !== undefined && val !== '') {
          url += `&${type}=${encodeURIComponent(val)}`;
        }
      }); 
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
        } catch (e) {
          console.error(e);
        }
      });
    },
  }
};
initUrlVars();
var app = Vue.createApp(deals);
app.component('prop-item', propItem);
app.component('flash-message', flashMessage);
// app.component('prop-claim', propClaim);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#deals');