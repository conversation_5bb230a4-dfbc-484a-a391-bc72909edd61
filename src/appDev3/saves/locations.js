var locations = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loading: false,
      watchingList:[],
      limit:20,
      dispVar:{
        lang:'zh-cn',
        isCip:false
      },
      datas:[
        'lang',
        'isCip',
        'allowedEditGrpName',
        'isVipRealtor',
        'isDevGroup',
        'coreVer',
        'isEmailVerified',
      ],
      prop:{},
      watchingType:'location',
      showWaitEdit:false,
    }
  },
  beforeMounted(){
  },
  mounted() {
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getWatchingAddr();
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('clickCmty',function (cmty) {
      self.showPropList(cmty)
    });
    bus.$on('watch-prop', function ({unwatch,prop,idx}) {
      if(unwatch){
        self.watchingList.splice(idx, 1);
      }else{
        self.watchingList.splice(idx, 1,prop);
      }
    });
    bus.$on('close-watch-popup', function () {
      self.closePopup()
    });
    bus.$on('showEdit',function ({item,idx}) {
      self.showWaitEdit=true;
      bus.$emit('watch-item-change', {item,idx});
      self.watchingType = item.isBuilding?'building':'location';
      // bus.$emit('checkNotify',{});
      return toggleModal('watchingModal','open')
    });
    bus.$on('showDelete',function ({item,idx}) {
      item.del = true
    });
    bus.$on('remove',function ({item,idx}) {
      self.removeWatch(item)
    });
    bus.$on('goToList',function ({item,sale}) {
      self.goToList(item,sale)
    });
  },
  methods: {
    closePopup(){
      this.showWaitEdit=false;
      toggleModal('watchingModal','close')
    },
    goToList(item,sale){
      // 进入地图， 类似搜索中点击地址效果
      var mode = 'map';
      var searchQuery = '';
      searchQuery += `&saletp=${sale?'sale':'lease'}`;
      if (item.loc) {
        searchQuery += `&loc=${item.loc[0]},${item.loc[1]}`;
      }
      // if (item.range) {
      //   searchQuery += `&range=${item.range}`;
      // }
      trackEventOnGoogle('saves','location','go list')
      location.href =  `/1.5/mapSearch?mode=${mode}${searchQuery}&cMarker=1&zoom=13&d=/1.5/saves/locations`;
    },
    removeWatch(prop,idx){
      var self = this;
      if(!prop.uaddr){
        return
      }
      var params={
        uaddr:prop.uaddr,
      };
      params.unwatch = true;
      trackEventOnGoogle('saves','location','removeWatch')
      getStandardFunctionCaller() ('watchLocation',params,(err,msg)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        self.watchingList.splice(idx, 1);
        bus.$emit('flash-message',msg);
      });
    },
    getWatchText(prop) {
      return prop.isWatching?'Watching':'Watch';
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getWatchingAddr();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getWatchingAddr:function() {
      var self = this;
      var url = '/1.5/user/watching';
      var body = {page:this.page,limit:this.limit,tp:'location'};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        }
        if (ret.items.length < self.limit) {
          self.hasMoreProps = false;
        }
        if (this.page == 0) {
          self.watchingList = ret.items
        } else {
          self.watchingList = self.watchingList.concat(ret.items);
        }
        trackEventOnGoogle('saves','location','getWatch')
      })
    },
  },
};
initUrlVars();
var app = Vue.createApp(locations);
app.component('watching-popup', watchingPopup);
app.component('options-card', optionsCard);
app.component('flash-message', flashMessage);
app.component('check-notification', checkNotification);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#locations');