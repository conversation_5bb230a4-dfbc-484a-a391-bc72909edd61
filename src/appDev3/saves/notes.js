var notes = {
  data(){
    return{
      noteList:[],
      page:0,
      scrollElement:'',
      waiting:false,
      hasMoreProps:false,
      loading:false,
      iconModel:'no',
      datas:[
        'isRealGroup',
        'isApp',
        'isNoteAdmin',
        'isAdmin',
        'lang'
      ],
      dispVar: {
        isRealGroup: false,
        isApp:false,
        isNoteAdmin:false,
        isAdmin: false,
        lang: 'en'
      },
      filterOnlyPic: false,
      uidInfo: [],
      showUidInfo: false,
      filterUid: []
    }
  },
  mounted(){
    var self = this;
    self.$getTranslate(self);
    self.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved',function(d){
      self.dispVar = Object.assign(self.dispVar, d);
      self.iconModel = 'edit';
      self.getNotesList();
      if(self.dispVar.isNoteAdmin){
        self.iconModel = 'praise';
        self.getNoteUserInfo();
      }
    });
    bus.$on('update-note',(val) => {
      if(val){
        if(val.model == 'delete') {
          let index = this.noteList.findIndex((value)=>{
            return value._id == val.id;
          })
          self.noteList.splice(index,1);
        }
      }
      self.tagShow = false;
    });
    self.scrollElement = document.getElementById('notes');
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    if (!window.showMap){
      window.showMap = this.goMap;
    }
  },
  methods:{
    goMap:function(){
      let url = '/1.5/map/notesMap';
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
        } catch (e) {
          console.error(e);
        }
      });
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page++;
              self.getNotesList('more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getNotesList(more){
      let self = this;
      setLoaderVisibility('block');
      let body = {
        page:this.page
      }
      if(this.filterOnlyPic){
        body.filterOnlyPic = this.filterOnlyPic;
      }
      if(this.filterUid.length){
        body.filterUid = this.filterUid;
      }
      fetchData('/1.5/notes/savedList',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        if(more){
          self.noteList = self.noteList.concat(data.noteList)
        }else{
          self.noteList = data.noteList;
        }
        if(data.noteList.length < data.limit){
          self.hasMoreProps = false;
        }else{
          self.hasMoreProps = true;
        }
      });
    },
    getNoteUserInfo(){
      let self = this;
      fetchData('/1.5/notes/allNoteUserInfor',{body:{}},(err,ret)=>{
        if (!ret.ok || err) {
          return bus.$emit('flash-message','Error');
        }
        let data = ret.result;
        self.uidInfo = data.uidInfo;
      });
    },
    filterNotesList(){
      this.filterUid = [];
      this.page = 0;
      this.noteList = [];
      for(let i=0; i<this.uidInfo.length;i++){
        if(this.uidInfo[i].isSel){
          this.filterUid.push(this.uidInfo[i].uid);
        }
      }
      this.showUidInfo = false;
      this.getNotesList();
    },
    filterOnlyPicNotes(){
      this.page = 0;
      this.noteList = [];
      this.filterOnlyPic = !this.filterOnlyPic;
      this.getNotesList();
    }
  }
}
var app = Vue.createApp(notes);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('notes-info', notesInfo);
app.component('prop-item', propItem);
app.component('flash-message', flashMessage);
app.mount('#notes');