var searches = {
  data() {
    return {
      slideMenuItems:[],
      web:false,// 从web端进入没有bar，有面包屑
      noBar:false,// 从Advanced进来没有bar，从notication进入则有bar
      // showCancel:showCancel,// 从Advanced进来有Cancel btn，从notication进入没有Cancel btn
      edit:false,
      isPopup:false,
      subscribeUrl:'/setting/notificationEml',
      dispVar: {
        isRealtor: false,
        lang:'zh',
        isApp:true,
      },
      waitEdit:{
        nm:'',
        sbscb:false,
        clnt:{},
        lang:undefined,
        ts:''
      },
      waitEditIdx:null,
      showWaitEdit:false,
      showCrm:false,
      datas:[
        'isRealtor',
        'isVipPlus',
        'isVipRealtor',
        'isApp',
        'languageAbbrObj',
        'coreVer',
        'isEmailVerified'
      ],
      strings:{
        searchNoName:{key:"Please input a name of saved search!"},
        searchNoLang:{key:"Please select the language for customer!"},
        savedSearch:{key:'Saved search and get notified'}
      },
    }
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getSavedSearch();
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = objectAssignDeep(self.dispVar, d);
    });
    bus.$on('choosed-crm', function (crm) {
      self.showWaitEdit = true;
      self.showCrm = false;
      self.waitEdit.clnt = crm;
    });
    bus.$on('subscribe2client',function({item,idx}){
      self.subscribe2client(item,idx)
    });
    bus.$on('showEditSavedSearch',function({item,idx}){
      self.showEditSavedSearch(item,idx)
    });
    bus.$on('showDelSavedSearch',function({item,idx}){
      self.showDelSavedSearch(item,idx)
    });
    bus.$on('removeSavedSearch',function({item,idx}){
      self.removeSavedSearch(item,idx)
    });
    bus.$on('setAdvSlideVal',function({item,idx}){
      self.goToListPage(item,idx)
    });
    if(noBar == true || vars.noBar == '1'){
      this.noBar = true;
    }else{
      this.noBar = false
    };
    if(web === 'true' || vars.web === '1'){
      this.web = true;
    }else{
      this.web = false;
    };
    if(vars.isPopup === '1'){
      this.isPopup = true;
    };
    if(vars.edit === '1'){
      this.edit = true;
    };
    if(vars.d){
      this.d = vars.d;
    };
    if (vars.i) {
      this.subscribeUrl =this.subscribeUrl+'?i=' +vars.i
      if (vars.lang) {
        this.subscribeUrl =this.subscribeUrl+'&lang=' +vars.lang
      }
    }
  },
  computed:{
    clientName:function(){
      if(this.waitEdit.clnt && this.waitEdit.clnt.nm){
        return this.waitEdit.clnt.nm;
      } else {
        return ''
      }
    }
  },
  methods: {
    // NOTE: have to do this, because ratchet.js is using active as a state for toggle its self
    // TODO: find a better way to do this, eg. use a vue component for switch
    removeActiveClass(id){
      document.querySelector(id).classList.remove('active')
      document.querySelector(id+'-handle').style.transform = 'translate3d(0px, 0px, 0px)'
    },
    goBack(){
      return window.rmCall(':ctx::cancel');
    },
    cancelEidt(){
      this.showWaitEdit=false;
      if (this.edit) {
        this.removeSavedSearch(this.slideMenuItems[0],0)
      }
    },
    getLocFromBbox(bbox=[]){
      let ret = [];
      ret.push((bbox[0]+bbox[2])/2) //-79 lng
      ret.push((bbox[1]+bbox[3])/2) //43 lat
      return ret
    },
    goToListPage: function(i) {
      if(this.web){
        return
      }
      var mode = 'list';
      var searchQuery = '';
      function appendLatLng(loc=[],value){
        ret = ''
        if(loc && loc[0]){
          ret += `&lat=${loc[1]}`;
          ret += `&lng=${loc[0]}`;
          ret += `&delta=${value[3]-value[1]}`;
        }
        return ret
      }
      for (var [key, value] of Object.entries(i.k)) {
        if (value) {
          if ((key == 'dom') && (value <= 0)) {
            searchQuery += '&mapmode=sold';
          }
          if (key == 'bbox') {
            mode = 'map';
            let loc = this.getLocFromBbox(value)
            searchQuery += appendLatLng(loc,value)
          }
          searchQuery += `&${key}=${value}`;
        }
      }
      let tmp = Object.assign({title:i.nm,desc:this.$_(this.strings.savedSearch.key),disp:i.display},i)
      RMSrv.setItemObj({key:'map.feature.prop.nearby_prop',value:null,stringify:false,store:false})
      RMSrv.setItemObj({key:'map.feature.prop.current_saved_search',value:tmp,stringify:false,store:false})
      var str = '&'+this.serializeData({
        prefix:'k',
        data:i.k
      })+'&'+this.serializeData({
        prefix:'v',
        data:i.k
      })
      // NOTE: will use to rest filter do this and ignore native filter changes
      RMSrv.setItemObj({key:'mapSearch.filter',value:str,stringify:true,store:false})
      // var ret = {val:i};
      // try {
      //   ret = JSON.stringify(ret)
      //   // alert(ret)
      // } catch (e){
      //   // alert(e)
      // }
      // console.log(i,searchQuery)
      // return
      trackEventOnGoogle('saves','searches','go list')
      if(this.noBar){
        var ret = {val:i};
        try {
          ret = JSON.stringify(ret)
          // alert(ret)
        } catch (e){
          // alert(e)
        }
        return window.rmCall(':ctx:'+ret)
      }
      if(this.isPopup){
        return RMSrv.closeAndRedirectRoot(`/1.5/mapSearch?mode=${mode}${searchQuery}`);
      }
      // NOTE: new native mapsearch same param as advFilter
      if(this.isNewerVer(this.dispVar.coreVer,'6.2.8')){
        return location.href = `/1.5/mapSearch?mode=${mode}${searchQuery}&d=/1.5/saves/searches${str}`;
      }
      location.href = `/1.5/mapSearch?mode=${mode}${searchQuery}&d=/1.5/saves/searches`;
    },
    getSavedSearch(){
      // post to server
      this.httpSavedSearch({mode:'get'});
    },
    showEditSavedSearch(hist, idx){
      this.showWaitEdit = true;
      this.waitEdit={
        nm:'',
        sbscb:false,
        clnt:{}
      }
      this.waitEdit = Object.assign(this.waitEdit,hist);
      if (hist.nonm){
        this.waitEdit.nm='';
      }
      this.waitEditIdx = idx;
    },
    editSavedSearch(){
      var self = this,update;
      if (self.waitEdit.nm == '') {
        return window.bus.$emit('flash-message', self.$_(this.strings.searchNoName.key));
      }
      if(self.waitEdit.nonm){
        self.waitEdit.nonm = false;
      }
      self.httpSbscbSavedSearch(self.waitEdit)
    },
    httpSbscbSavedSearch(i){
      var self = this,update;
      update = {ts:i.ts}
      update.nm = i.nm
      update.sbscb = i.sbscb
      if(i.clnt && i.clnt._id){
        update.clnt=i.clnt._id
        if (i.lang == "undefined") {
          return window.bus.$emit('flash-message', self.$_(this.strings.searchNoLang.key));
        }
        update.lang=i.lang
      }else{
        update.clnt=null
      }
      if (vars.i) {
        update.i = vars.i
      }
      fetchData('/1.5/props/sbscbSaveSearch', {body:update},function(err,ret) {
        if (err || ret.e) {
          window.bus.$emit('flash-message', err || ret.e);
          return;
        } else {
          window.bus.$emit('flash-message', ret.msg);
          self.slideMenuItems.splice(self.waitEditIdx, 1,i);
          self.showWaitEdit = false;
          if(ret.isDuplicatedName){
            i.nm = ret.nm
          }
          if (self.edit) {
            self.goToListPage(i)
            // if(ret.isDuplicatedName){
            //   setTimeout(function(){                
            //   }, 2000);
            // } else {
            //   self.goToListPage(i)
            // }
          }
        }
      })
    },
    subscribe2client(v,i){
      if(!v){
        return;
      }
      var val = Object.assign({},v),self = this;
      if (val.sbscb) {
        val.sbscb = false;
        this.waitEditIdx = i;
        this.httpSbscbSavedSearch(val);
        return;
      }
      this.checkSbscb((err)=>{
        if (err){
          v.sbscb = false;
          let id = '#sbscb-'+i+'-toggle'
          return self.removeActiveClass(id)
        }
        this.getSavedSearchPropCount(val.k,(err,msg)=>{
          if (err) {
            return RMSrv.dialogAlert(err.toString());
          }
          if (msg) {
            val.sbscb = false;
            return;
          } else {
            val.sbscb = true;
          }
          this.waitEditIdx = i
          this.httpSbscbSavedSearch(val)
        });
      });
    },
    showClientFn(){
      if (!this.dispVar.isVipRealtor)
        return this.confirmVip(this.dispVar.lang);
      if(this.waitEdit.clnt && this.waitEdit.clnt.nm){
        return ;
      }
      this.showWaitEdit = false;
      this.showCrm = true;
      var cfg = {hide:false,title:this.$_('Contacts','contactCrm')};
      var url = RMSrv.appendDomain('/1.5/crm?noBar=1&needEml=1&isPopup=1&owner=1');
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          var crm = JSON.parse(val);
          // alert(JSON.stringify(crm));
          window.bus.$emit('choosed-crm',crm);
        } catch (e) {
          console.error(e);
        }
      });
    },
    resetClnt(){
      this.waitEdit.clnt={};
      this.waitEdit.lang=undefined;
    },
    formatYMD (ts) {
      if (ts) {
        ts = new Date(ts);
        return ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate();
      } else
        return '';
    },
    showDelSavedSearch(hist, idx){
      hist.del = true;
    },
    removeSavedSearch(hist={}, idx){
      if (!hist.k) {
        return;
      }
      this.httpSavedSearch({ts:hist.ts, q:hist.k, idx:idx, mode:'del'});
    },
    httpSavedSearch(d){
      var self = this;
      if (vars.i) {
        d.i = vars.i;
      }
      trackEventOnGoogle('saves','searches',d.mode)
      function parseSavedSearch(l) {
        for (let i = 0; i < l.length; i++) {
          var hist = l[i], d = {};
          for (let j = hist.r.length; j > -1; j--) {
            var record = hist.r[j];
            if (record && ['src','saletp','city','bbox'].indexOf(record.k) > -1) {
              d[record.k] = record.v || record.vv;
              if (record.k == 'bbox') {
                hist.r.splice(j,1);
              }
            }
          }
          hist.del = false;
          hist.d = d;
          if(hist.nm == undefined){
            hist.nm = self.formatYMD(hist.ts);
            hist.nonm=true;
          }
          if(hist.sbscb == undefined){
            hist.sbscb =  true;
          }
          if(hist.clnt == undefined){
            hist.clnt = {};
          }
        }
        l.reverse();
        return l;
      }
      fetchData('/1.5/props/savedSearch', {body:d},function(err,ret) {
        if (err || ret.e) {
          err = err || ret.e
          if (err == 'Need login') {
            if (self.dispVar.isApp) {
              window.location.href = '/1.5/user/login'
            } else {
              window.location.href = '/www/login?state=/1.5/settings/savedSearchModel&lang='+self.dispVar.lang
            }
          } else {
            window.bus.$emit('flash-message', err);
          }
        } else {
          if (ret.l && ret.l.length) {
            self.slideMenuItems = parseSavedSearch(ret.l);
            if (self.edit) {
              self.showEditSavedSearch(self.slideMenuItems[0],0);
            }
          } else if (d.mode == 'del') {
            if (self.edit) {
              self.goBack()
            }
            window.bus.$emit('flash-message', ret.msg);
            self.slideMenuItems.splice(d.idx, 1);
            self.showWaitEdit = false;
          }
        }
      })
    },
    checkSbscb(cb) {
      fetchData('/1.5/props/checkSbscb',{body:{}},(err,ret)=>{
        if (err) {
          return cb(err);
        }
        if (ret && ret.msg) {
          window.bus.$emit('flash-message', ret.msg);
          return cb(ret.msg);
        } else if (ret && !ret.msg) {
          return cb(null);
        } else {
          return cb(ret.e);
        }
      });
    },
    toggleSubscription() {
      var self = this;
      if (this.waitEdit.sbscb) {
        return this.waitEdit.sbscb = false;
      }
      this.checkSbscb((err)=>{
        if (err){
          self.waitEdit.sbscb = false;
          return self.removeActiveClass('#editToggle')
          // return self.waitEdit = Object.assign({},self.waitEdit,{sbscb:false});
        }
        this.getSavedSearchPropCount(this.waitEdit.k,(err,msg)=>{
          if (err) {
            return RMSrv.dialogAlert(err.toString());
          }
          if (msg) {
            self.waitEdit.sbscb = false;
          } else {
            self.waitEdit.sbscb = true;
          }
        });
      })
    },
    getSavedSearchPropCount(savedSearch,cb) {
      var body = Object.assign(savedSearch, {checkMaxCnt:1})
      fetchData('/1.5/props/search',{body},(err,ret)=>{
        if (err) {
          return cb(err);
        }
        if (ret && ret.ok && ret.msg) {
          window.bus.$emit('flash-message', ret.msg);
          return cb(null,ret.msg);
        } else {
          return cb(ret.err);
        }
      });
    },
  }
};
initUrlVars();
var app = Vue.createApp(searches);
app.component('flash-message', flashMessage);
app.component('search-option', searchOption);
app.component('check-notification', checkNotification);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#searches');