var communities = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loading: false,
      watchingList:[],
      limit:20,
      dispVar:{
        lang:'zh-cn',
        isCip:false
      },
      datas:[
        'lang',
        'isCip',
        'coreVer',
        'isEmailVerified',
      ],
      prop:{},
      watchingType:'cmty',
      showWaitEdit:false,
    }
  },
  beforeMounted(){
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getWatchingAddr();
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });

    bus.$on('clickCmty',function (cmty) {
      self.showPropList(cmty)
    });
    bus.$on('watch-prop', function ({unwatch,prop,idx}) {
      if(unwatch){
        self.watchingList.splice(idx, 1);
      }else{
        self.watchingList.splice(idx, 1,prop);
      }
    });
    bus.$on('close-watch-popup', function () {
      self.closePopup()
    });
    bus.$on('showEdit',function ({item,idx}) {
      self.showWaitEdit=true;
      bus.$emit('watch-item-change', {item,idx});
      // bus.$emit('checkNotify',{});
      return toggleModal('watchingModal','open')
    });
    bus.$on('showDelete',function ({item,idx}) {
      item.del = true
    });
    bus.$on('remove',function ({item,idx}) {
      self.removeWatch(item,idx)
    });
    bus.$on('goToList',function ({item,sale}) {
      self.goToList(item,sale)
    });
  },
  methods: {
    closePopup(){
      this.showWaitEdit=false;
      toggleModal('watchingModal','close')
    },
    goToList(item,sale){
      var mode = 'list';
      var searchQuery = '';
      searchQuery += `&saletp=${sale?'sale':'lease'}`;
      if (item.city && item.prov) {
        searchQuery +=  `&city=${item.city}&prov=${item.prov}`;
      }
      if (item.cmty) {
        searchQuery += `&cmty=${item.cmty}`;
      }
      trackEventOnGoogle('saves','cmty','go list')
      location.href =  `/1.5/mapSearch?mode=${mode}${searchQuery}&d=/1.5/saves/communities`;
    },
    removeWatch(prop,idx){
      var self = this;
      if(!prop.uaddr){
        return
      }
      var params={
        uaddr:prop.uaddr,
      };
      params.unwatch = true;
      trackEventOnGoogle('saves','cmty','removeWatch')
      getStandardFunctionCaller() ('watchLocation',params,(err,msg)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        self.watchingList.splice(idx, 1);
        bus.$emit('flash-message',msg);
      });
    },
    getWatchText(prop) {
      return prop.isWatching?'Watching':'Watch';
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getWatchingAddr();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getWatchingAddr:function() {
      var self = this;
      var url = '/1.5/user/watching';
      var body = {page:this.page,limit:this.limit,tp:this.watchingType};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        }
        if (ret.items.length < self.limit) {
          self.hasMoreProps = false;
        }
        if (self.page == 0) {
          self.watchingList = ret.items
          if(vars.id){
            var id = decodeURIComponent(vars.id);
            var popupItem = self.watchingList.find(element => element._id == id);
            if(popupItem){
              window.bus.$emit('showEdit',{item:popupItem})
            }
          }
        } else {
          self.watchingList = self.watchingList.concat(ret.items);
        }
        trackEventOnGoogle('saves','cmty','getWatch')
      })
    },
    showPropList:function(cmty){
      url='/1.5/mapSearch?mode=list&city='+cmty.city_en+'&prov='+cmty.pr_en+'&cityName='+cmty.city+'&cmty='+cmty.nm+'&d=/1.5/saves/communities';
      window.location = url;
    },
    goTo(url) {
      window.location.href=url;
    },
  },
};
initUrlVars();
var app = Vue.createApp(communities);
app.component('watching-popup', watchingPopup);
app.component('options-card', optionsCard);
app.component('flash-message', flashMessage);
app.component('check-notification', checkNotification);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#communities');