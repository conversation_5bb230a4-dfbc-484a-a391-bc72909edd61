var viewed = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loading: false,
      maxLog : 100,
      viewedProps:[],
      limit:20,
      showFavGroups:false,
      prop:{fav:false,favGrp:[]},//待添加收藏的房源
      dispVar:{
        lang:'zh-cn',
        isCip:false,
        isLoggedIn:false
      },
      datas:[
        'lang',
        'isCip',
        'allowedEditGrpName',
        'isVipRealtor',
        'isLoggedIn'
      ],
      rcmdHeight: 0
    }
  },
  beforeMounted(){
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getviewedProps();
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.getPageData(self.datas,{},true);
    self.rcmdHeight = parseInt(window.innerWidth/1.6);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('reset',()=>{
      this.reset()
    })
    bus.$on('prop-fav-changed',({prop})=>{
      this.prop = prop;
    })
  },
  methods: {
    reset:function(){
      var self = this;
      self.grpsOptions = false;
      self.showFavGroups = false;
    },
    toggleFav(prop){
     this.showFavGroups = true;
     this.prop = prop;
    },
    // isRMProp (prop) {
    //   return /^RM/.test(prop.id);
    // },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getviewedProps();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getviewedProps:function() {
      var self = this;
      var url = '/1.5/props/viewedProps';
      var body = {page:this.page,limit:this.limit};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        }
        if (ret.items.length < self.limit) {
          self.hasMoreProps = false;
        }
        if (this.page == 0) {
          self.viewedProps = ret.items
        } else {
          self.viewedProps = self.viewedProps.concat(ret.items);
        }
      })
    },
    // showMore:function(){
    //   var start = this.page * this.limit;
    //   var end = (this.page + 1) * this.limit;
    //   var newProp = this.allProps.slice(start,end)
    //   this.viewedProps = this.viewedProps.concat(newProp)
    //   if(this.viewedProps.length < this.maxLog){
    //     this.hasMoreProps = true;
    //   }else{
    //     this.hasMoreProps = false;
    //   }
    // },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
  },
};
initUrlVars();
var app = Vue.createApp(viewed);
app.component('prop-item', propItem);
app.component('prop-fav-actions', propFavActions);
app.component('flash-message', flashMessage);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#viewed');