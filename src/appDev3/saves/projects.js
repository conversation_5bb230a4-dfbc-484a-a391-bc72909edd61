var projects = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: false,
      cntTotal : 0,
      scrollElement:null,
      loading: false,
      hasMap:true,
      favProjects:[],
      rcmdHeight:0,
      hasWechat:true,
      dispVar:{
      },
      datas:[
        'hasFollowedRealtor',
        'lang',
        'isProjAdmin',
        'isProdSales'
      ]
    }
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    self.getFavProjects();
    if (!window.showMapView){
      window.showMapView = this.showMapView;
    }
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.rcmdHeight = parseInt(window.innerWidth/2);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('remove-proj',function(){
      self.getFavProjects();
    });
    if (RMSrv.hasWechat){
      RMSrv.hasWechat((has)=>{
        if (has!=null) {
          self.hasWechat = has
        }
      })
    }
  },
  methods: {
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getFavProjects('more')
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getFavProjects:function(more) {
      var self = this;
      var url = '/1.5/prop/projects/myFav';
      var body = {page:self.page};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          if(more){
            self.favProjects = self.favProjects.concat(ret.items)
          }else{
            self.favProjects = ret.items;
          }
          self.cntTotal = ret.cnt;
          if(self.favProjects.length < self.cntTotal){
            self.hasMoreProps = true;
          }else{
            self.hasMoreProps = false;
          }
        }
      })
    },
    showMapView(){
      url='/1.5/mapSearch?d='+vars.d+'&mapmode=projects&appMapMode=fav&mode=map';
      window.location = url;
    }
  }
};
initUrlVars();
var app = Vue.createApp(projects);
app.component('proj-item', projItem);
app.component('flash-message', flashMessage);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#projects');