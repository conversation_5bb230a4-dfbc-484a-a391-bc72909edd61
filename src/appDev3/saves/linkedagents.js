var linkedagents = {
  data() {
    return {
      datas:[
        'isLoggedIn',
      ],
      dispVar: {
        isLoggedIn:  false,
      },
      message:'',
      resultList:[],
      error:'',
      scrollElement:'',
      hasMoreProps:false,
      page:0,
    };
  },
  mounted() {
    var self = this;
    this.$getTranslate(this);
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getLinkedAgents();
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d)
    });
    self.scrollElement = document.getElementById('saves-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  methods: {
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getLinkedAgents();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    toggleRemove(l){
      return l.del = !l.del;
    },
    getLinkedAgents() {
      var self = this;
      var limit = 10;
      var self = this, data = {page:self.page,limit};
      fetchData('/1.5/crm/linkedAgents', {body:data},function (err,ret) {
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          var list= ret.list;
          list.forEach((l)=>{
            l.del = false;
          })
          if(list.length < limit){
            self.hasMoreProps = false
          }else{
            self.hasMoreProps = true
          }
          if(self.page == 0){
            self.resultList = list;
          }else{
            self.resultList = self.resultList.concat(list)
          }
        }
      });
    },
    unlinkAgent(agent,idx){
      var self = this;
      var data = {id:agent._id,position:'clnt'};
      fetchData('/1.5/crm/unlinkagent',{body:data},function(err,ret) {
        if (ret.ok) {
          bus.$emit('flash-message',ret.msg);
          self.resultList.splice(idx, 1);
        } else {
          RMSrv.dialogAlert(ret.err);
        }
      });
    },
  },
  computed:{
    shareUrl:function() {
      return this.dispVar.shareHost + '/1.5/yellowpage/myfav?share=1&lang='+this.dispVar.lang+'&faved='+this.checkedUser.join(',') ;
    },
  },
};
initUrlVars();
var app = Vue.createApp(linkedagents);
app.component('flash-message', flashMessage);
app.mixin(baseMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#linkedagents');