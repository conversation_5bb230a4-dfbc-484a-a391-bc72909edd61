
var body = decodeJsonString2Obj('body');
var stripePubKey = document.querySelector('#stripePubKey').innerHTML;
var stripe = null;
function createSession() {
  if (!stripe){
    return initStripe();
  };
  fetchData('/1.5/prop/topup/createSession',{body},(error,res) => {
    if(res.ok) {
      stripe.redirectToCheckout({
        sessionId: res.session.id
      }).then(function (result) {
        RMSrv.dialogAlert(result.error.message);
      });
    } else {
      RMSrv.dialogAlert(res.err);
    }
  });
};
initStripe();

function initStripe (){
  stripe = Stripe(stripePubKey);
  setTimeout(function () {
    self.createSession();
  }, 10);
};