var condoComplex = {
  data(){
    return{
      lat:vars.lat,
      lng:vars.lng,
      uaddr:decodeURIComponent(vars.uaddr),
      addr: decodeURIComponent(vars.addr),
      searchStr: '',
      addrList: [],
      tagShow:false,
      noteTags:[],
      isAddrSearch: false,
      strings:{
        LATLNGTIP: 'Latitude and longitude do not exist!'
      },
      isChangeAddr: false, // 点击了地址选择方框会变成true，关闭页面后刷新笔记页面
      searchInputTimeout: null,
      dispVar: {
        isApp:true
      },
      datas:[
        'isApp'
      ]
    }
  },
  mounted(){
    let self = this;
    this.searchStr = this.addr;
    self.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved',function(d){
      self.dispVar = Object.assign(self.dispVar, d);
    });
    this.nearbyUaddr();
    bus.$on('noteTag-list',(val) => {
      let update = false;
      for(item of self.addrList){
        if(item.isSel){
          update = true;
          break;
        }
      }
      if(update){
        self.updateCondoComplex();
      }
      self.tagShow = false;
    });
  },
  watch:{
    searchStr:function(val){
      var self = this;
      if(val.length < 4 || val == self.addr){
        return 
      }
      self.noteTags = [];
      self.lat = null;
      self.lng = null;
      self.uaddr = null;
      self.isAddrSearch = true;
      clearTimeout(self.searchInputTimeout);
      self.searchInputTimeout = setTimeout(function () {
        self.searchInputTimeout = null;
        self.getAutoCompletes();
      }, 700);
    }
  },
  methods:{
    updateCondoComplex(uaddrSel,index){
      setLoaderVisibility('block');
      let self = this;
      let param={
        uaddrCur: self.uaddr,
        tags: self.noteTags,
        addr: self.addr
      }
      if(uaddrSel){
        param.uaddrSel = uaddrSel._id;
      }
      fetchData('/condoComplex/update',{body:param},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok){
          let info = err? self.$_('Error'):self.$_(ret.e);
          self.addrList[index].isSel = false;
          return bus.$emit('flash-message',info);
        }
        if(index){
          self.addrList[index].isSel = true;
        }
      });
    },
    deleteCondoComplex(uaddrSel,index){
      let self = this;
      setLoaderVisibility('block');
      fetchData('/condoComplex/delete',{body:{uaddrSel:uaddrSel._id}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok){
          let info = err? self.$_('Error'):self.$_(ret.e);
          self.addrList[index].isSel = true;
          return bus.$emit('flash-message',info);
        }
        self.addrList[index].isSel = false;
      });
    },
    nearbyUaddr(suggestAddr){
      let self = this;
      if (!self.lat || !self.lng){
        return bus.$emit('flash-message',self.$_(this.strings.LATLNGTIP));
      }
      setLoaderVisibility('block');
      let body = {
        lat:this.lat,
        lng:this.lng,
        uaddr:self.uaddr,
        addr:this.addr
      }
      if(suggestAddr) { // 给后端拼成uaddr进行查找
        body.city = suggestAddr.city
        body.cnty = suggestAddr.cnty
        body.loc = suggestAddr.loc
        body.prov = suggestAddr.prov
        body.zip = suggestAddr.zip
      }
      fetchData('/condoComplex/nearbyUaddr',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok){
          let info = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',info);
        }
        self.noteTags = ret.result.tags;
        self.addrList = ret.result.nearByCondos;
        self.uaddr = ret.result.uaddr
        if(!self.addrList.length){
          return bus.$emit('flash-message',self.$_('No result found'));
        }
      });
    },
    addrClick(addr,index){
      this.isChangeAddr = true;
      if(addr.isSel){
        this.deleteCondoComplex(addr,index);
      } else {
        this.updateCondoComplex(addr,index);
      }
    },
    getAutoCompletes(){
      let self = this;
      this.addrList = [];
      setLoaderVisibility('block');
      fetchData('/1.5/props/addrSuggest',{body:{s:this.searchStr}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok){
          let info = err? self.$_('Error') : self.$_(ret.err);
          return bus.$emit('flash-message',info);
        }
        self.addrList = ret.suggest;
      });
    },
    selectSearch(addr){
      if (!this.isAddrSearch){ // 选择关联地址和选择地址公用一个div所以需要判断一下
        return;
      }
      if (!addr.loc){
        return bus.$emit('flash-message',this.$_(this.strings.LATLNGTIP));
      }
      this.isAddrSearch = false;
      this.addr = addr.addr;
      this.searchStr = addr.addr;
      this.lat = addr.loc[1];
      this.lng = addr.loc[0];
      this.uaddr = null;
      this.nearbyUaddr(addr);
    },
    reset(){
      this.searchStr = '';
      this.addrList = [];
      this.noteTags = [];
      this.isAddrSearch = true;
      this.lat = null;
      this.lng = null;
      this.uaddr = null;
    },
    closeComplex(){
      let selAddrArry = this.addrList.filter((addr)=>{
        return addr.isSel == true
      });
      if(!selAddrArry.length){
        this.noteTags = [];
      }
      let complexInfo = {
        complexTags: this.noteTags,
        selAddrArry,
        isChangeAddr: this.isChangeAddr
      }
      window.rmCall(':ctx:'+JSON.stringify(complexInfo));
    }
  },
};
initUrlVars();
var app = Vue.createApp(condoComplex);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('tag-info', tagInfo);
app.component('flash-message', flashMessage);
app.mount('#condoComplex');