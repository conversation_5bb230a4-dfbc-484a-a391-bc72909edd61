var statisticNotes = {
  data(){
    return{
      statisticInfo:[],
      allMonth: []
    }
  },
  mounted(){
    let self = this;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    self.$getTranslate(self);
    self.getStatisticInfo();
  },
  methods:{
    getStatisticInfo(){
      let self = this;
      setLoaderVisibility('block');
      fetchData('/1.5/notes/statistic',{body:{}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        self.statisticInfo = ret.result.statisticInfo;
        self.allMonth = ret.result.allMonth;
      });
    }
  },
};
var app = Vue.createApp(statisticNotes);
trans.install(app,{ref:Vue.ref});
app.component('flash-message', flashMessage);
app.mount('#statisticNotes');