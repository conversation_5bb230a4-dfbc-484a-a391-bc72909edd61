var editNotes = {
  data(){
    return{
      uaddr: decodeURIComponent(vars.uaddr),
      unt: decodeURIComponent(vars.unt),
      id: vars.id,
      propId: vars.propId,
      tagShow: false,
      noteTags:[],
      noteDetail:{
        memo:[{
          tag: 'noTag',
          m: ''
        }],
        pic:[],
        propIds:[]
      },
      NOTAG: 'noTag',
      LINEH:21,
      sizes:[],
      showImage: false,
      imageSrc: '',
      showDelete: false,
      imageIndex: -1,
      tagText:[],
      isPaste: false,
      clipboardData: '',
      startIndex: 0,
      picOrig:[],
      memoOrig:[{
        tag: 'noTag',
        m: ''
      }],
      isSaving:false,
      datas:[
        'isApp'
      ],
      dispVar: {
        isApp:true
      }
    }
  },
  computed: {
    showSaveBtn:function(){
      if(this.isSaving) return true;
      let picLength = this.noteDetail.pic.length;
      for(item of this.noteDetail.memo){
        if(item.m.trim().length>0) return false;
      }
      if(picLength > 0) return false;
      return true;
    }
  },
  mounted(){
    var self = this;
    self.$getTranslate(self);
    self.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved',function(d){
      self.dispVar = Object.assign(self.dispVar, d);
    });
    self.initData();
    bus.$on('noteTag-list',(val) => {
      if(val){
        self.noteDetail.memo.push({tag:val,m:''});
        self.noteTags.push(val);
        self.$nextTick(()=>{
          document.getElementById(val).focus();
        })
      }
      self.tagShow = false;
    });
    let opt = {
      animation: 150,
      ghostClass: 'sort-placeholder',
      delay:100,
      delayOnTouchOnly:true,
      touchStartThreshold: 10,
      onUpdate: (evt)=>{
        self.dragEnd(evt)}
      }
    let previewImgBox = document.querySelector("#previewImg")
    new Sortable(previewImgBox,opt)
  },
  methods:{
    initData(){
      var TAGTEXT = decodeJsonString2Obj('TAGTEXT','array');
      this.tagText = TAGTEXT;
      if(this.id){
        this.getNoteDetail();
      }
    },
    getTranslate(txt) {
      return TRANSLATES[txt] || txt;
    },
    closeEditNote(){
      var self = this;
      var closeTip = self.getTranslate('closeTip');
      var cancel = self.getTranslate('cancel');
      var yes = self.getTranslate('yes');
      function _doClose(idx) {
        if (idx+'' == '2') {
          window.rmCall(':ctx::cancel');
        }
      }
      let isChanged = this.isChangedNoteDetail();
      if(!isChanged){
        return window.rmCall(':ctx::cancel');
      }
      return RMSrv.dialogConfirm(closeTip, _doClose, '', [cancel, yes]);
    },
    // 文本框随着输入的内容自适应高度,解决编辑到外部div底部按回车时，光标被遮挡问题
    calcHeight(id){
      let textarea = document.getElementById(id);
      // let lineHeight = parseInt(getComputedStyle(textarea).lineHeight); // 获取行高
      textarea.style.height = this.LINEH + 'px';
      let scrollHeight = textarea.scrollHeight;
      let rows = parseInt(scrollHeight/this.LINEH); // 计算行数
      textarea.style.height = this.LINEH * rows + this.LINEH + "px";
      if(this.isPaste && id==this.NOTAG){
        let m = this.clipboardData.slice(this.startIndex)
        this.noteDetail.memo[0].m = this.noteDetail.memo[0].m.replace(m, '');
        this.isPaste = false;
        this.clipboardData = '';
        setLoaderVisibility('none');
        this.$nextTick(function () {
          this.calcHeight(this.NOTAG);
        })
      }
    },
    deleteTag(tag,content,index=-1){
      if(content.length == 0 && tag != this.NOTAG){
        this.noteDetail.memo.splice(index,1);
        let tagIndex = this.noteTags.indexOf(tag);
        this.noteTags.splice(tagIndex,1);
      }
    },
    getNoteDetail(){
      let self = this;
      setLoaderVisibility('block');
      let body = {
        id:this.id,
        showList:false
      }
      fetchData('/1.5/notes/detail',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result.noteDetail;
        this.noteDetail = Object.assign(this.noteDetail, data);
        for(let i=0;i<this.noteDetail.memo.length;i++){
          if(this.noteDetail.memo[i].tag !== this.NOTAG)
            this.noteTags.push(this.noteDetail.memo[i].tag);
        }
        if(this.noteDetail.memo[0].tag != this.NOTAG) // 保证输入部分顶部有一个没有tag的textarea
          this.noteDetail.memo.unshift({tag: this.NOTAG,m: ''});
        this.memoOrig = [];
        this.picOrig = this.picOrig.concat(this.noteDetail.pic);
        this.noteDetail.memo.forEach(value => {
          this.memoOrig.push(Object.assign({},value))
        });
        this.$nextTick(function () {
          for(let i=0;i<this.noteDetail.memo.length;i++){
            this.calcHeight(this.noteDetail.memo[i].tag);
          }
        })
      });
    },
    saveNote(){
      setLoaderVisibility('block');
      this.isSaving = true;
      this.noteDetail.uaddr = this.uaddr;
      if(this.unt){
        this.noteDetail.unt = this.unt;
      }
      if (this.propId.length > 0 && this.propId != 'undefined'){
        let include = this.noteDetail.propIds.includes(this.propId);
        if(!include){
          this.noteDetail.propIds.push(this.propId);
        }
      }
      if(this.noteDetail.memo[0].m.length == 0)
        this.noteDetail.memo.shift(); // noTag没有内容时不保存在数据库
      let isChanged = this.isChangedNoteDetail();
      if(!isChanged){
        return window.rmCall(':ctx::cancel');
      }
      let body = {
        noteDetail:this.noteDetail,
        type:'edit',
        propId:this.propId
      }
      if(this.id){
        body.noteId = this.id;
      }
      fetchData('/1.5/notes/update',{body:body},(err,ret)=>{
        setLoaderVisibility('none');
        this.isSaving = false;
        if (err || !ret.ok) {
          return bus.$emit('flash-message',this.$_('Save failed'));
        }
        if(ret.tokenMsg && ret.tokenMsg.length){
          bus.$emit('flash-message',this.$_(ret.tokenMsg));
        }
        let result = {
          type: 'edit',
          note: ret.result
        }
        window.rmCall(':ctx:'+JSON.stringify(result));
      });
    },
    deleteNote(){
      setLoaderVisibility('block');
      this.isSaving = true;
      fetchData('/1.5/notes/delete',{body:{id:this.id,propId: this.propId}},(err,ret)=>{
        this.isSaving = false;
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        if(ret.tokenMsg && ret.tokenMsg.length){
          bus.$emit('flash-message',this.$_(ret.tokenMsg));
        }
        let result = {
          type: 'delete',
        }
        window.rmCall(':ctx:'+JSON.stringify(result));
      });
    },
    confirmDelete(){
      let self = this;
      if(!this.id){ // 新创建的笔记还没有保存
        return bus.$emit('flash-message',self.getTranslate('deleteTip'));
      }
      let deleteTip = self.getTranslate('deleteConfim');
      let cancel = self.getTranslate('cancel');
      let yes = self.$_('Delete');
      function _doDelete(idx) {
        if (idx+'' == '2') {
          self.deleteNote();
        }
      }
      return RMSrv.dialogConfirm(deleteTip, _doDelete, '', [cancel, yes]);
    },
    dragEnd(evt){
      let $ul = document.querySelector("#previewImg");
      let newIndex = evt.newIndex;
      let oldIndex = evt.oldIndex;
      let $li;
      $oldLi = $ul.children[oldIndex],$li;
      if (newIndex >= $ul.children.length){
        newIndex = $ul.children.length - 1
      }
      $li = $ul.children[newIndex]
      $ul.removeChild($li)
      if(newIndex > oldIndex) {
          $ul.insertBefore($li,$oldLi)
      } else {
          $ul.insertBefore($li,$oldLi.nextSibling)
      }
      let item = this.noteDetail.pic.splice(oldIndex,1);
      this.noteDetail.pic.splice(newIndex,0,item[0]);
    },
    insertImages(){
      let opt = {
        url: '/1.5/img/insert'
      }
      insertImage(opt,(val)=>{
        if (val == ':cancel') {
          return;
        }
        try {
          let ret = JSON.parse(val);
          let picUrls = ret.picUrls;
          for(let pic of picUrls) {
            if (this.noteDetail.pic.indexOf(pic) === -1) {
              this.noteDetail.pic.push(pic);
            }
          }
        } catch (e) {
        }
      })
    },
    previewPic (src,index) {
      this.showImage = true;
      this.imageSrc = src;
      this.imageIndex = index;
    },
    deleteImage(){
      this.noteDetail.pic.splice(this.imageIndex,1);
      this.showDelete = false;
      this.showImage = false;
      this.imageIndex = -1;
    },
    parseContent(mStr){
      let arrExec = [];
      let reg;
      // NOTE: https://stackoverflow.com/questions/51568821/works-in-chrome-but-breaks-in-safari-invalid-regular-expression-invalid-group
      // safari < 16.4 not support lookbehind
      try {
        reg = /\[[^\]]*(?=\])/gm; // /(?<=\[)[^\]]*(?=\])/gm;
        // throw new Error('test');
      } catch (e) {
        // console.warn(e,'Regex lookbehind not supported in this browser.')
        alert('Regex lookbehind not supported in this browser. Paste content not parsed, please update your browser/system version.')
        this.clipboardData = '';
        this.isPaste = false;
        setLoaderVisibility('none');
        return;
      }
      let result = reg.exec(mStr);
      while (result) {
        let tag = result[0].slice(1); // 去掉最前面的[,eg:result[0] = [Building Entrance
        arrExec.push({k:tag,v:result.index + 1});
        result = reg.exec(mStr); // 只要捕获结果不为null，就继续捕获
      }
      for(let i=0,len=arrExec.length; i<len;i++){
        if(!this.tagText.includes(arrExec[i].k)) continue;
        let mStart = arrExec[i].v + arrExec[i].k.length + 1; // Tag正文字符串开始位置：tag的index + tag长度 + ]
        let curMIdx = this.noteDetail.memo.findIndex((value)=>{return value.tag==arrExec[i].k});
        let content = {}
        if(arrExec[i+1]){
          content = {tag:arrExec[i].k,m:mStr.slice(mStart,arrExec[i+1].v-1)} // arrExec[i+1].v-1:下一个tag的index - [
        } else { //  没有tag了就截取到字符串结尾
          content = {tag:arrExec[i].k,m:mStr.slice(mStart)}
        }
        if(curMIdx == -1){ // 当前note里面没有
          this.noteTags.push(arrExec[i].k);
          this.noteDetail.memo.push(content);
        } else {
          this.noteDetail.memo[curMIdx].m = this.noteDetail.memo[curMIdx].m.concat(content.m);
        }
        this.$nextTick(function () {
          this.calcHeight(arrExec[i].k);
        })
      }
      if(arrExec.length && arrExec[0].v !=2 && this.tagText.includes(arrExec[0].k)){// 捕获到的第一个字段index不是2,[，且是tag
        this.startIndex = arrExec[0].v-1;
      } else {
        this.clipboardData = '';
        this.isPaste = false;
        setLoaderVisibility('none');
      }
    },
    pasteMemo(event,tag){
      if(tag != this.NOTAG){
        return
      }
      if (event.clipboardData || event.originalEvent || window.clipboardData) {
        setLoaderVisibility('block');
        this.clipboardData = (event.clipboardData || event.originalEvent.clipboardData || window.clipboardData).getData("text"); // 获取粘贴内容
        this.isPaste = true;
        this.parseContent(this.clipboardData);
      }
    },
    isChangedNoteDetail(){
      if(this.picOrig.length !== this.noteDetail.pic.length){
        return true;
      }
      for(let i=0,len = this.noteDetail.pic.length;i < len;i++){
        if(this.noteDetail.pic[i] !== this.picOrig[i]){
          return true;
        }
      }
      if(this.memoOrig.length !== this.noteDetail.memo.length){
        return true;
      }
      for(let i=0,len = this.noteDetail.memo.length;i < len;i++){
        if(this.noteDetail.memo[i].tag !== this.memoOrig[i].tag){
          return true;
        }
        if(this.noteDetail.memo[i].m !== this.memoOrig[i].m){
          return true;
        }
      }
      return false;
    }
  },
};
initUrlVars();
var app = Vue.createApp(editNotes);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('tag-info', tagInfo);
app.component('flash-message', flashMessage);
app.mount('#editNotes');
