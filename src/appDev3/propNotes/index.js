var propNotes = {
  data(){
    return{
      uaddr: decodeURIComponent(vars.uaddr),
      addr: decodeURIComponent(vars.addr),
      propId: vars.propId,
      isBuilding: vars.isBuilding,
      showMyNote: true,
      showAddBtn: false,
      tagShow:false,
      noteTags:[],
      noteDetail:null,
      noteList:[],
      propList:[],
      complexNotes:[],
      showAllNotes: false,
      datas:[
        'isRealGroup',
        'isApp',
        'isNoteAdmin',
        'lang'
      ],
      dispVar: {
        isRealGroup: false,
        isApp:false,
        isNoteAdmin:false,
        lang:'en',
      },
      page:0,
      scrollElement:'',
      waiting:false,
      hasMoreProps:false,
      loading:false,
      lat: vars.lat,
      lng: vars.lng,
      totalCount:0,
      complexTags:[],
      unfoldAllNotes: true,
      showComplexNotes: true,
      addrInfo: []
    }
  },
  mounted(){
    var self = this;
    self.$getTranslate(self);
    self.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved',function(d){
      self.dispVar = Object.assign(self.dispVar, d);
      self.getNoteDetail();
      self.getNotesList();
      self.getComplesNotesList();
    });
    bus.$on('noteTag-list',(val) => {
      if(val){
        let tagIndex = this.noteTags.indexOf(val);
        if (tagIndex !== -1){
          self.noteTags.splice(tagIndex,1);
        } else {
          self.noteTags.push(val);
        }
        self.getNotesList();
      }
      self.tagShow = false;
    });
    bus.$on('update-note',(val) => {
      if(val){
        if(val.model == 'delete') {
          this.noteDetail = null;
          this.propList = [];
          this.showAddBtn = true;
        }
        if(val.model == 'praise'){
          let index = self.noteList.findIndex((value)=>{
            return value._id == val.id
          });
          let noteItem = self.noteList.splice(index,1);
          if(val.isPraise){ // 点赞置顶
            self.noteList.unshift(noteItem[0]);
            self.tagShow = false;
            return
          }
          // 取消点赞置顶
          if(self.noteList.length == 0){
            self.noteList.push(noteItem[0])
          }else{
            for(let i=0;i<self.noteList.length;i++){
              if(!self.noteList[i].topMt){
                self.noteList.splice(i,0,noteItem[0]);
                break;
              }
            }
          }
        }
      }
      self.tagShow = false;
    });
    // self.scrollElement = document.getElementById('propNotes');
    // self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  methods:{
    // listScrolled:function(){
    //   var self = this, wait = 400;
    //   if (!self.waiting && self.hasMoreProps) {
    //     self.waiting = true;
    //     self.loading = true;
    //     setTimeout(function () {
    //       self.waiting = false;
    //       var element = self.scrollElement;
    //       if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
    //         if (self.hasMoreProps) {
    //           self.page++;
    //           self.getNotesList('more');
    //         } else {
    //           self.loading = false;
    //         }
    //       } else {
    //         self.loading = false;
    //       }
    //     }, wait);
    //   }
    // },
    getNotesList(more){
      let self = this;
      // if(!more){
      //   this.noteList = [];
      //   this.page = 0;
      // }
      setLoaderVisibility('block');
      fetchData('/1.5/notes/listByUaddr',{body:{uaddr:this.uaddr,tagArray:this.noteTags,page:this.page}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        this.totalCount = data.totalCount;
        self.noteList = data.noteList;
        // if(more){
        //   this.noteList = this.noteList.concat(data.noteList);
        // }else{
        //   this.noteList = data.noteList;
        // }
        // if(data.noteList.length < data.limit){
        //   this.hasMoreProps = false;
        // }else{
        //   this.hasMoreProps = true;
        // }
        if(this.noteList.length != 0) this.showAllNotes = true;
      });
    },
    getNoteDetail(){ // 获取房大师个人笔记
      setLoaderVisibility('block');
      let self = this;
      fetchData('/1.5/notes/detail',{body:{uaddr:this.uaddr,showList:true}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          self.showAddBtn = false; // 出错时不显示添加按钮
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        this.noteDetail = data.noteDetail;
        if(data.noteDetail && data.noteDetail.propDetail){
          self.propList = data.noteDetail.propDetail;
        }
        if(this.noteDetail != null){
          self.showAddBtn = false;
        }else{
          self.showAddBtn = true;
        }
      });
    },
    getComplesNotesList(){
      setLoaderVisibility('block');
      let self = this;
      fetchData('/1.5/notes/getCondoComplexNotes',{body:{uaddr:this.uaddr}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          this.showAddBtn = false; // 出错时不显示添加按钮
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        self.complexNotes = ret.result.complexNotes;
        self.complexTags = ret.result.complexTags;
        self.addrInfo = ret.result.addrInfo;
      });
    },
    unfold(name){
      this[name] = !this[name];
    },
    addNote(){
      let self = this;
      if(!this.uaddr || this.uaddr == 'undefined'){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
      }
      if(!this.propId || this.propId == 'undefined'){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
      }
      setLoaderVisibility('block');
      fetchData('/1.5/notes/findListingByID',{body:{id:this.propId}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
        }
        let getUaddr = ret.result.uaddr;
        if (!getUaddr || getUaddr == 'undefined'){
          return bus.$emit('flash-message',NOTE_TIPS.NOUADDR);
        }
        let url = `/1.5/notes/editNotes?propId=${this.propId}&uaddr=${encodeURIComponent(getUaddr)}`;
        let cfg = {toolbar:false};
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
          self.getNoteDetail();
        });
      })
    },
    closeNotes(){
      let note = {
        totalCount: this.totalCount,
        noteDetail: this.noteDetail
      }
      window.rmCall(':ctx:'+JSON.stringify(note));
    },
    gotoSelectUaddr(){
      let self = this;
      let url = `/1.5/notes/condoComplex?uaddr=${encodeURIComponent(this.uaddr)}&addr=${encodeURIComponent(this.addr)}&lng=${this.lng}&lat=${this.lat}`;
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          let info = JSON.parse(val);
          if(info.isChangeAddr){
            self.getComplesNotesList();
            return
          }
          self.complexTags = info.complexTags;
          self.addrInfo = info.selAddrArry;
        } catch (e) {
          console.error(e);
        }
      });
    }
  },
}
initUrlVars();
var app = Vue.createApp(propNotes);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('notes-info', notesInfo);
app.component('tag-info', tagInfo);
app.component('prop-item', propItem);
app.component('flash-message', flashMessage);
app.mount('#propNotes');
