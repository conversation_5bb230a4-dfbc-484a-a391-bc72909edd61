var communityMap = {
  data() {
    return {
      cMap:null,
      cmtys:[],
      markers:[],
      selected:null,
      cmty:{},
      activeList:false,
      doSearchCmty:true,
      mapType: '',
      moveEndY:0,
      startY:0,
      watchingType:'cmty',
      showWaitEdit:false,
      datas:['isEmailVerified'],
      dispVar:{},
      bbox:'',
      userPos:null,
    }
  },
  components:{
    cmtyCard,
    Search,
    flashMessage,
    keyFacts,
    watchingPopup,
  },
  mounted() {
    var self = this;
    this.$getTranslate(this);
    self.userPos = decodeJsonString2Obj('userPos','array');
    self.bbox = document.querySelector('#bbox').innerHTML;
    this.getPageData(this.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    this.cMap = new Mapbox({zoom:11});
    this.cMap.init('map');
    if (self.bbox != 'undefined') {
      var bboxArray = self.bbox.split(',');
      var bounds = this.getBboxBounds(bboxArray);
      this.cMap.map.fitBounds(bounds);
    } else {
      this.setCenterZoom();
    }
    this.cMap.map.on('load',function() {
      self.getCommunities(self.handleCmtys);
    });
    this.cMap.map.on('zoomend',function() {
      if (self.doSearchCmty) {
        self.handleSearch();
      }
      self.doSearchCmty = true;
      self.saveLoc();
    });
    this.cMap.map.on('dragend',function() {
      self.handleSearch();
      self.saveLoc();
    });
    bus.$on('watch-prop', function ({unwatch,prop,idx}) {
      var idx = idx || prop.idx;
      var fav = true;
      if(unwatch){
        fav = false;
      }
      if (idx) {
        self.cmtys[idx].isFav = fav;
        self.cmtys[idx].watching = fav ? prop.watching:{};
      } else {
        prop.isFav = fav;
        self.cmtys.forEach((m)=>{
          if(m._id == prop._id){
            m.isFav = fav;
            m.watching = fav ? prop.watching:{};
          }
        })
      }
      if(self.cmty && self.cmty._id){
        self.cmty.watching = fav ? prop.watching:{};
      }
    });
    bus.$on('close-watch-popup', function () {
      self.closePopup()
    });
    bus.$on('favCmty',function(cmty) {
      self.openWatch(cmty)
    });
    bus.$on('clickCmty',function(cmty) {
      self.selectCmty(cmty._id);
    });
    bus.$on('search-cmty',function() {
      self.resetMapLayer();
      self.selected = null;
      self.cmty = {};
      self.updateMapStyle();
      self.getCommunities(self.handleCmtys);
    })
  },
  methods:{
    closePopup(){
      this.showWaitEdit=false;
      toggleModal('watchingModal','close')
    },
    getTranslate(key) {
      return TRANSLATES[key];
    },
    setCenterZoom() {
      var lastMapLocZoom = localStorage.lastMapLocZoom;
      try {
        var [lat,lng,zoom] = lastMapLocZoom.split(',');
        this.cMap.map.setCenter([lng,lat]);
        this.cMap.map.setZoom(zoom);
      } catch (error) {
        console.error(error);
        this.cMap.map.setCenter(this.userPos);
      }
    },
    saveLoc() {
      var {lat,lng} = this.cMap.map.getCenter();
      var zoom = this.cMap.map.getZoom();
      localStorage.lastMapLocZoom = `${lat},${lng},${zoom}`;
    },
    getBboxBounds(bbox) {
      return new mapboxgl.LngLatBounds(new mapboxgl.LngLat(bbox[0], bbox[1]), new mapboxgl.LngLat(bbox[2], bbox[3]));
    },
    openSearchPopup() {
      bus.$emit('toggle-filter', {showFilter:true});
    },
    updateMapType() {
      this.cMap.updateStyle();
    },
    updateMapZoom(dir) {
      this.cMap.updateZoom(dir);
    },
    setLocMarker(loc) {
      if (loc) {
        var pos = new mapboxgl.LngLat(loc[1],loc[0]);
        var marker = this.cMap.Cmarker;
        if (!marker) {
          img = '/img/mapmarkers/umarker.png';
          var el = document.createElement('img');
          el.setAttribute('src', img);
          el.setAttribute('style','width:25px;height:25px;z-index:2');
          marker = new mapboxgl.Marker({element:el,anchor:'bottom'}).setLngLat(pos).addTo(this.cMap.map);
          this.cMap.Cmarker = marker;
        }
        this.cMap.map.panTo(pos);
      }
    },
    locateMe() {
      var self = this;
      // loc = [43.8096542879315,-79.3928838588078];
      // self.setLocMarker(loc);
      // self.handleSearch();
      this.cMap.locateMe({},(loc) => {
        self.setLocMarker(loc);
        self.handleSearch();
      });
    },
    openWatch(item){
      this.showWaitEdit=true;
      bus.$emit('watch-item-change', {item});
      // bus.$emit('checkNotify',{});
      toggleModal('watchingModal','open')
    },
    toggleList() {
      this.activeList = !this.activeList;
    },
    onTouchStart(e) {
      e.preventDefault();
      this.startY = e.changedTouches[0].pageY;
    },
    onTouchMove(e) {
      e.preventDefault();
      this.moveEndY = e.changedTouches[0].pageY;
      // click
      if ( this.moveEndY - this.startY < 0) {
        this.activeList = true
      } else {
        this.activeList = false
      }
    },
    onTouchEnd(e) {
      e.preventDefault();
      this.moveEndY = e.changedTouches[0].pageY;
      if ( this.moveEndY - this.startY == 0) {
        this.activeList = !this.activeList;
      }
    },
    handleCmtys(err,ret) {
      if (err) {
        alert(err);
        return;
      }
      if (ret.ok == 1) {
        this.cmtys = ret.cmtys;
        this.renderMarkers(this.cmtys);
      } else {
        alert(ret.err);
      }
    },
    getCmtyDetail(id) {
      var body = {id:id};
      var self = this;
      var cmtySearch = localStorage.getItem('cmtySearch');
      try {
        cmtySearch = JSON.parse(cmtySearch);
        body.ethnicKey = cmtySearch.ethnicKey;
      } catch (error) {
        console.error(error);
      }
      getStandardFunctionCaller()('findCmtyDetail',body,(err,ret)=>{
        if (err || ret.msg) {
          bus.$emit('flash-message',(err || ret.msg).toString());
          return;
        }
        if (ret) {
          self.cmty = ret;
          setTimeout(() => {
            // self.updateMapStyle('openCmtyDetail');
            self.renderMarkerBoundary(ret);
          }, 1);
        }
      });
    },
    updateMapStyle(act) {
      if (act && act == 'openCmtyDetail') {
        var cmtyDetailHeight = document.querySelector('#communityDetail').clientHeight;
        var headerHeight = document.querySelector('#header-bar').clientHeight;
        document.querySelector('#map').style.height = 'calc(100vh - '+cmtyDetailHeight+'px - '+headerHeight+'px)';
      } else {
        document.querySelector('#map').style.height = '100vh';
      }
      this.cMap.map.resize();
    },
    updateMarker(id) {
      var markers = document.querySelectorAll('.marker--cmty');
      for (var i = 0; i < markers.length; i++) {
        markers[i].classList.remove('active');
      }
      var selectedMarker = document.getElementById(id);
      selectedMarker.classList.add('active');
    },
    selectCmty(id) {
      this.activeList = false;
      this.selected = id;
      this.updateMarker(id);
      this.getCmtyDetail(id);
    },
    renderMarker(cmty) {
      var self = this;
      var el = document.createElement('div');
      el.className = 'marker--cmty';
      if (cmty._id == this.selected) {
        el.className += ' active';
      }
      el.setAttribute('id',cmty._id);
      // el.innerHTML = '<span class="avl">'+cmty.avl+'</span>'+'<span>'+cmty.nm+'</span>';
      el.innerText = cmty.nm;
      el.addEventListener('click',function(e) {
        var id = e.target.getAttribute('id');
        self.selectCmty(id);
      });
      var marker = new mapboxgl.Marker({element:el, anchor:'bottom'}).setLngLat(cmty.pos).addTo(this.cMap.map);
      return marker;
    },
    renderMarkers(cmtys) {
      var markers = [];
      for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].remove();
      }
      if (cmtys.length == 0) {
        return;
      }
      for (var i = 0; i < cmtys.length; i++) {
        var cmty = cmtys[i];
        var marker = this.renderMarker(cmty);
        markers.push(marker);
      }
      this.markers = markers;
    },
    handleSearch() {
      clearTimeout(this.debounceTimeout);
      var self = this;
      this.debounceTimeout = setTimeout(function () {
        self.debounceTimeout = null;
        self.getCommunities(self.handleCmtys);
      }, 700);
    },
    getCommunities(cb) {
      var body = {
        search:this.buildSearch(),
        bbox: this.getMapBbox()
      };
      getStandardFunctionCaller()('searchCmtys', body, function(err,cmtys){
        if (err) {
          bus.$emit('flash-message',err.toString());
        }
        cb(err,{ok:1,cmtys});
      });
    },
    buildSearch: function() {
      var cmtySearch = localStorage.getItem('cmtySearch');
      try {
        cmtySearch = JSON.parse(cmtySearch);
      } catch (err) {
        alert(err);
      }
      var cities = cmtySearch.cities;
      var cityProv = Object.keys(cities);
      var cities = [];
      var provs = [];
      for (var i = 0; i < cityProv.length; i++) {
        var [city,prov] = cityProv[i].split('-');
        if (cities.indexOf(city) == -1) {
          cities.push(city);
        }
        if (provs.indexOf(prov) == -1) {
          provs.push(prov);
        }
      }
      cmtySearch.cities = cities;
      cmtySearch.provs = provs;
      if (cmtySearch.school.min == 0 && cmtySearch.school.max == 3) {
        delete cmtySearch.school;
      }
      return cmtySearch;
    },
    getMapBbox: function() {
      var bnds = this.cMap.map.getBounds();
      var ne = bnds.getNorthEast();
      var sw = bnds.getSouthWest();
      return [sw.lng,sw.lat,ne.lng,ne.lat];
    },
    createGeoJSONCircle:function(center, radiusInKm, points) {
      if(!points) points = 64;
      var coords = {
        latitude: center[1],
        longitude: center[0]
      };
      var km = radiusInKm;
      var ret = [];
      var distanceX = km/(111.320*Math.cos(coords.latitude*Math.PI/180));
      var distanceY = km/110.574;
      var theta, x, y;
      for(var i=0; i<points; i++) {
        theta = (i/points)*(2*Math.PI);
        x = distanceX*Math.cos(theta);
        y = distanceY*Math.sin(theta);
        ret.push([coords.longitude+x, coords.latitude+y]);
      }
      ret.push(ret[0]);
      return {
        'type': 'geojson',
        'data': {
          'type': 'FeatureCollection',
          'features': [{
            'type': 'Feature',
            'geometry': {
              'type': 'Polygon',
              'coordinates': [ret]
            }
          }]
        }
      };
    },
    resetMapLayer() {
      if (this.cMap.map.getLayer('boundaryLayer')) {
        this.cMap.map.removeLayer('boundaryLayer');
      }
      if (this.cMap.map.getSource('boundarySrc')) {
        this.cMap.map.removeSource('boundarySrc');
      }
      if (this.cMap.map.getLayer('markerCircle')) {
        this.cMap.map.removeLayer('markerCircle');
      }
      if (this.cMap.map.getSource('markerCircle')) {
        this.cMap.map.removeSource('markerCircle');
      }
    },
    renderMarkerBoundary:function(cmty) {
      // if render cmty boundary, do not search cmtys
      this.doSearchCmty = false;
      this.resetMapLayer();
      if (cmty.hasBndry) {
        this.renderBoundary(cmty);
      } else {
        var pos = cmty.pos;
        // this.cMap.map.panTo(pos);
        var self = this;
        setTimeout(function() {
          if (self.cMap.map.getZoom() != 13) {
            self.cMap.map.setZoom(13);
          }
          self.renderCircle(pos);
        },100);
      }
    },
    calcUpdateCenter:function(pos) {
      var mapBbox = this.getMapBbox();
      var fullHeight = window.innerHeight;
      var dis = Math.abs(mapBbox[1] - mapBbox[3]);
      var unit = dis/fullHeight;
      var mapHeight = fullHeight - 279 - 52;
      var updateDis = (mapHeight/2) * unit;
      return [pos[0],pos[1] - updateDis];
    },
    renderCircle:function(pos) {
      var center = this.calcUpdateCenter(pos);
      this.cMap.map.panTo(center);
      this.cMap.map.addSource('markerCircle', this.createGeoJSONCircle(pos, 1));

      this.cMap.map.addLayer({
        'id': 'markerCircle',
        'type': 'fill',
        'source': 'markerCircle',
        'layout': {},
        'paint': {
          'fill-color': '#fb806d',
          'fill-opacity': 0.3,
          'fill-outline-color': '#fb806d'
        }
      });
    },
    renderBoundary: function(cmty) {
      var img = '/img/boundary/'+cmty.bndryNm+'.png';
      var bbox = cmty.bbox;
      var bounds = this.getBboxBounds(bbox);
      this.cMap.map.addSource('boundarySrc', {
        'type': 'image',
        'url': img,
        'coordinates': [
          [bbox[0], bbox[3]],
          [bbox[2], bbox[3]],
          [bbox[2], bbox[1]],
          [bbox[0], bbox[1]],
        ]
      });
      this.cMap.map.addLayer({
        'id': 'boundaryLayer',
        'source': 'boundarySrc',
        'type': 'raster',
        'paint': {
          'raster-opacity': 1
        }
      });
      var self = this;
      var cmtyDetailHeight = document.querySelector('#communityDetail').clientHeight;
      var headerHeight = document.querySelector('#header-bar').clientHeight;
      var padding = 10;
      var bottom = cmtyDetailHeight + headerHeight + padding;
      self.cMap.map.fitBounds(bounds,{padding:{bottom,top:padding,left:padding,right:padding}});
    },
    closeCmtyDetail() {
      this.cmty = {};
      this.updateMapStyle();
    },
    gotoStatPage() {
      var {city_en,pr_en,nm} = this.cmty;
      var statsUrl = `/1.5/prop/stats?city=${city_en}&prov=${pr_en}&cmty=${nm}&d=/1.5/landlord/owners&isPopup=1&itvl=M`;
      openContent(statsUrl,{toolbar:false});
    },
    goToPropListPage() {
      var cmty = this.cmty;
      var {pathname,search} = window.location;
      var urlQueryArray = [
        'mode=list',
        'city='+cmty.city_en,
        'prov='+cmty.pr_en,
        'cityName='+cmty.city,
        'cmty='+cmty.nm,
        'd='+encodeURIComponent(pathname+search)
      ];
      try {
        var cmtySearch = localStorage.getItem('cmtySearch');
        cmtySearch = JSON.parse(cmtySearch);
      } catch (error) {
        bus.$emit('flash-message',error.toString());
      }
      if (cmtySearch) {
        if (cmtySearch.price) {
          if (cmtySearch.price.min != 0) {
            urlQueryArray.push('min_lp='+cmtySearch.price.min);
          }
          if (cmtySearch.price.max != 3000000) {
            urlQueryArray.push('max_lp='+cmtySearch.price.max);
          }
        }
        if (cmtySearch.ptype) {
          urlQueryArray.push('ptype2='+cmtySearch.ptype);
        }
      }
      openContent(`/1.5/mapSearch?isPopup=1&${urlQueryArray.join('&')}`,{toolbar:false});
    }
  }
};

var app = Vue.createApp(communityMap);
app.component('flash-message', flashMessage);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#community-map');