ONE_DAY = 86400000; //1000 * 3600 * 24
var CHECK_INVIEWPORT_GAP = 82;
var discover = {
  data() {
    return {
      fixDate:false,
      fixTabs:false,
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loadMoreEdmProps:true,
      loading: true,
      scrollLoading: false,
      propTag:'',
      propTags:[],
      selectedCmty:{},
      selectedCmtyIdx:null,
      cmtys:[],
      cmtyProps:[],
      cmtyPropsObj:{},
      sortedDates:[],
      savedSearches:{},
      userCity:{},
      edmLogs:[],
      curFixedEdmIndex:0,
      curDisPlayEdmDate:'',
      curDate:null,
      feedDates:[],
      lastScrollTop:'',
      scrollDirection:'',
      placeholderHeight:'0',
      tabsBoxStyle:"position: relative; top:0;",
      tagTop:0,
      middlePart:41,
      initMiddlePartheight:false,
      showPriceChange:true,
      datas:['userCity','savedSearches','favCmtys','propTags'],
      dispVar:{}
    }
  },
  unmounted () {
  },
  created() {
    
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    this.getEdmProps();
    setTimeout(() => {
      this.scrollBar()
    }, 20);
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.userCity = d.userCity;
      self.savedSearches = d.savedSearches;
      self.cmtys = d.favCmtys;
      self.propTags = d.propTags;
      self.initMiddlePart()
    });
  },
  methods: {
    unSubscribeCmty() {
      // 跳转saves/community页面
      location.href = `/1.5/saves/communities?d=/1.5/community/discover&id=${this.selectedCmty._id}`
    },
    scrollBar(){
      var navLinksContainer = document.querySelector('.tabs-container');
      var navLinkSelected = document.querySelector('.selected');
      var navLinkActive = document.querySelector('.tabs-nav-active');
      var windowWidth = window.innerWidth
      var boundData = navLinkSelected.getBoundingClientRect();
      var diffWidth = (windowWidth - boundData.width) / 2
      var targetOffset = navLinkSelected.offsetLeft - diffWidth;
      navLinkActive.style.left = targetOffset + windowWidth/2 - 15 +'px';
      if (targetOffset < 0) {
        navLinksContainer.scrollLeft = 0;
        return
      }
      navLinksContainer.scrollLeft = targetOffset
    },
    initMiddlePart(){
      var self = this;
      if(self.initMiddlePartheight == false){
        var navLinksContainer = document.querySelector('.tabs-container');
        var boundData = navLinksContainer.getBoundingClientRect();
        self.middlePart = self.selectedCmty.pos? 227 : 41;
        self.tagTop = boundData.top - 44 + self.middlePart;
        self.initMiddlePartheight = true;
      }
    },
    scrollToTop(){
      var content = document.getElementById('discover-container');
      content.scrollTop = 158 ;
      setTimeout(() => {
        this.scrollBar()
      }, 20);
    },
    showSeeAllTagBtn() {
      return ['ts','lpChg','recent','soldLoss'].indexOf(this.propTag.k) > -1;
    },
    handleScroll() {
      this.getScrollDirection();
      this.fixedDate();
      this.handleLoadMore();
      this.initMiddlePart()
    },
    goToTop() {
      this.curFixedEdmIndex = 0;
      this.curDisPlayEdmDate = '';
      this.fixDate = false;
      document.getElementById('discover-container').scrollTop = 158 ;
    },
    isInViewport(elementId) {
      var element = document.getElementById(elementId);
      if (!element) {
        return false;
      }
      var {top,left,width,height} = element.getBoundingClientRect();
      return (
        top < (window.pageYOffset + window.innerHeight) &&
        left < (window.pageXOffset + window.innerWidth) &&
        (top + height - CHECK_INVIEWPORT_GAP) > window.pageYOffset &&
        (left + width) > window.pageXOffset
      );
    },
    getScrollDirection() {
      var discoverWrapper = document.getElementById('discover-container');
      var st = discoverWrapper.pageYOffset || discoverWrapper.scrollTop; // Credits: "https://github.com/qeremy/so/blob/master/so.dom.js#L426"
      if (st > this.lastScrollTop){
        //downscroll code
        this.scrollDirection = 1;
      } else {
        // upscroll code
        this.scrollDirection = 0;
      }
      this.lastScrollTop = st;
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    getDatePropsBlockId(propsBlock){
      return `${propsBlock.id}`;
    },
    getFixedDateClass() {
      var fixedDateClassArray = [];
      if (this.fixDate) {
        fixedDateClassArray.push('fixed');
      }
      if (this.propTag) {
        fixedDateClassArray.push('hasPropTag');
      }
      return fixedDateClassArray.join(' ');
    },
    fixedDate() {
      var propsArray = this.edmLogs;
      var scrollTopThreshHold = 420;
      if (this.propTag) {
        propsArray = this.cmtyProps;
        scrollTopThreshHold = 242;
      }
      if (this.selectedCmty && this.selectedCmty._id) {
        scrollTopThreshHold = 424;
      }
      if (propsArray.length) {
        var discoverWrapper = document.getElementById('discover-container');
        this.fixDate = (discoverWrapper.scrollTop > scrollTopThreshHold);
        if (this.fixDate) {
          var curDisPlayEdmDate = propsArray[this.curFixedEdmIndex].id;
          if (this.scrollDirection) {
            if (this.isInViewport(curDisPlayEdmDate)) {
              this.curDisPlayEdmDate = curDisPlayEdmDate;
            } else {
              this.curFixedEdmIndex += 1;
            }
          } else {
            // scroll up
            if (this.curFixedEdmIndex > 0) {
              var lastEdmDate = propsArray[this.curFixedEdmIndex-1].id;
              if (this.isInViewport(lastEdmDate)) {
                this.curDisPlayEdmDate = lastEdmDate;
                this.curFixedEdmIndex -= 1;
              }
            }
          }
        }
      }
    },
    getEdmProps: function() {
      if (!this.loadMoreEdmProps) {
        return;
      }
      var date = this.curDate;
      var dates;
      if (date) {
        // date = new Date(new Date(date).getTime() - ONE_DAY)
        this.feedDates.push(date);
        dates = this.feedDates;
      }
      var body = {date:dates};
      var self = this;
      fetchData('/1.5/community/edmProps',{body},function(err,ret) {
        if (ret.ok == 1 && ret.edmLog) {
          self.processEdmLog(ret.edmLog);
          self.edmLogs = self.edmLogs.concat(ret.edmLog);
          self.curDate = ret.edmLog.id;
        } else {
          self.loadMoreEdmProps = false;
        }
        setTimeout(() => {
          var curDateContainer = document.getElementById(self.curDate);
          if (curDateContainer) {
            //if edm props not able to scroll, load the next edm props
            var container = document.getElementById('discover-container').getBoundingClientRect();
            var edmBlock = curDateContainer.getBoundingClientRect();
            if (container.bottom > edmBlock.bottom) {
              self.getEdmProps();
            }
          }
        }, 10);
      });
    },
    processEdmLog:function(edmLog) {
      for (var i = 0; i < edmLog.edm.length; i++) {
        var edmFeed = edmLog.edm[i];
        if (edmFeed.new) {
          edmFeed.showAllSale = false;
        }
        if (edmFeed.off) {
          edmFeed.showAllSold = false;
        }
      }
    },
    buildPropListLink:function({search,sold,cmty}) {
      var mode = 'list';
      var searchQuery = '';
      for (var [key, value] of Object.entries(search)) {
        if (value) {
          if (sold && (key == 'dom') && (value >= 0)) {
            value = -90;
          }
          if (key == 'bbox') {
            mode = 'map';
          }
          searchQuery += `&${key}=${value}`;
        }
      }
      if (sold) {
        searchQuery += '&soldOnly=true&dom=-90';
      }
      if (cmty) {
        searchQuery += `&cmty=${cmty}`;
      }
      return `/1.5/mapSearch?mode=${mode}${searchQuery}&d=/1.5/community/discover`;
    },
    goToListPage: function(edm,sold=false) {
      //if savedSearch has no nm, use ts id to get current savedSearch
      var search = this.savedSearches[edm.nm] || this.savedSearches[edm.ts.substring(0,10)];
      var cmty = edm.cmty;
      if (search) {
        location.href = this.buildPropListLink({search,sold,cmty});
      }
    },
    openSaves() {
      var savesUrl = '/1.5/saves/properties?d=/1.5/community/discover&isPopup=1';
      openContent(savesUrl,{toolbar:false});
    },
    gotoStatPage() {
      var {city_en,pr_en,nm} = this.selectedCmty;
      var statsUrl = `/1.5/prop/stats?city=${city_en}&prov=${pr_en}&cmty=${nm}&d=/1.5/community/discover&isPopup=1`;
      openContent(statsUrl,{toolbar:false});
    },
    goToListPageWithTag() {
      var {selectedCmty,userCity,propTag} = this;
      var propListUrl = '/1.5/mapSearch?mode=list&ptype=Residential';
      if (selectedCmty._id) {
        var city = selectedCmty.city_en;
        var prov = selectedCmty.pr_en;
        var cmty = selectedCmty.nm;
      } else {
        var city = userCity.o;
        var prov = userCity.p_ab;
      }
      propListUrl += `&city=${city}&prov=${prov}`;
      if (cmty) {
        propListUrl += `&cmty=${cmty}`;
      }
      if (propTag) {
        propListUrl += `&${propTag.k}=${propTag.v}`;
        if (propTag.k == 'recent' && propTag.v == 'sold') {
          propListUrl += '&dom=-90'
        }
      }
      location.href = `${propListUrl}&d=/1.5/community/discover`;
    },
    selectCmty: function(cmty,cmtyIdx) {
      this.scrollToTop();
      if (!cmty) {
        this.selectedCmty = {};
        this.selectedCmtyIdx = null;
        this.propTag = '';
        setTimeout(() => {
          this.scrollBar()
        }, 10);
        return;
      }
      if (this.selectedCmty._id == cmty._id) {
        return;
      }
      this.handleCmtyTagClick();
      this.selectedCmty = cmty;
      this.selectedCmtyIdx = cmtyIdx;
      this.getCmtyDetail();
      this.getPropsForTag();
    },
    selectTag(tag) {
      if (this.cmtys.length && !this.selectedCmty._id) {
        this.selectedCmty = this.cmtys[0];
        this.getCmtyDetail();
      }
      if (this.propTag.k != tag.k) {
        this.handleCmtyTagClick();
        this.propTag = tag;
        this.getPropsForTag();
      }
      setTimeout(() => {
        this.scrollBar();
        this.scrollToTop()
      }, 10);
    },
    clickFixedCmty() {
      setTimeout(() => {
        this.scrollBar();
        this.scrollToTop()
      }, 10);
    },
    handleCmtyTagClick:function() {
      this.curFixedEdmIndex = 0;
      this.curDisPlayEdmDate = '';
      this.fixDate = false;
      this.sortedDates = [];
      this.page = 0;
      this.cmtyProps = [];
      this.cmtyPropsObj = {};
      this.hasMoreProps = true;
    },
    getCmtyDetail:function() {
      var id = this.selectedCmty._id;
      if (!id) {
        return;
      }
      var body = {id};
      getStandardFunctionCaller()('findCmtyDetail',body,(err,cmty)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        if (cmty) {
          this.selectedCmty = cmty;
        }
      });
    },
    getPropsForTag: function() {
      if (!this.hasMoreProps) {
        return;
      }
      var url = '/1.5/props/search';
      var oneMonth = 2592000000;//1000*3600*24*30;
      var body = {
        page:this.page,
        tsDiff:oneMonth,
        isAllowedVipUser: false,
        ptype:'r',
        label: 1,
        no_mfee: false,
        soldOnly: true,
        oh: false,
        saletp:'Sale',
        sort:'auto-ts',
        src:'mls',
        discover:1
      };
      if (this.propTag) {
        body[this.propTag.k] = this.propTag.v;
        if (this.propTag.k != 'ts' && this.propTag.v != 'new') {
          body.sort = 'spcts-desc';
        }
      }
      if (this.selectedCmty._id) {
        body.cmty = this.selectedCmty.nm;
        body.city = this.selectedCmty.city_en;
        body.prov = this.selectedCmty.pr_en;
      } else {
        body.city = this.userCity.o;
        body.prov = this.userCity.p_ab;
      }
      const self = this;
      fetchData(url,{body},function(err,ret) {
        if (err) {
          console.log(err);
          return;
        }
        var props = ret.items;
        if (ret.cnt < 20) {
          self.hasMoreProps = false;
        } else {
          self.hasMoreProps = true;
        }
        self.cmtyProps = self.getDatePropsFormat(props);
      });
    },
    getDatePropsFormat:function(props) {
      var _cmtyPropsObj = Object.assign({},this.cmtyPropsObj);
      var dateTp = 'spcts';
      if (this.propTag.k == 'ts' && this.propTag.v == 'new') {
        dateTp = 'ts';
      }
      var dates = this.sortedDates;
      props.forEach(prop => {
        var date = (prop[dateTp]||prop.ts||'');
        date = new Date(date).toLocaleDateString();//convert date to locale date
        if (_cmtyPropsObj[date]) {
          _cmtyPropsObj[date].push(prop);
        } else {
          dates.push(date);
          _cmtyPropsObj[date] = [prop];
        }
      });
      this.cmtyPropsObj = _cmtyPropsObj;
      var cmtyProps = [];
      dates.sort().reverse()
      dates.forEach(date => {
        cmtyProps.push({id:date,props:_cmtyPropsObj[date]});
      });
      this.sortedDates = dates;
      return cmtyProps;
    },
    handleLoadMore:function(){
      var self = this, wait = 400;
      self.scrollElement = document.getElementById('discover-container');
      if (!self.waiting) {
        self.waiting = true;
        self.scrollLoading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.propTag) {
              self.page += 1;
              self.getPropsForTag();
            } else {
              self.getEdmProps();
            }
            // else {
            //   self.scrollLoading = false;
            // }
          } 
          // else {
          //   self.scrollLoading = false;
          // }
        }, wait);
      }
      if(this.propTag){
        var hasCmtyCase = this.selectedCmty._id && self.scrollElement.scrollTop >=257;
        var cityCase = self.scrollElement.scrollTop >= (this.tagTop - 3);
        if(hasCmtyCase || cityCase){
          self.tabsBoxStyle = "position: fixed; top:44px;padding: 10px 0;"
          self.placeholderHeight = '42px'
          self.fixTabs = true;
        }else{
          self.tabsBoxStyle = "position: relative; top:0;"
          self.placeholderHeight = '0'
          self.fixTabs = false;
        }
        self.scrollBar()
      }
    }
  }
};
var app = Vue.createApp(discover);
app.component('prop-item', propItem);
app.component('daily-feeds', dailyFeeds);
trans.install(app,{ref:Vue.ref})
app.mixin(pageDataMixins);
app.mount('#discover');