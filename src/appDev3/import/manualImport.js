function save() {
  let id = document.querySelector('#idInput'),
    src = document.querySelector('input[name="src"]:checked'),
    className = document.querySelector('input[name="className"]:checked');
  if (!(id && id.value && src && src.value)) {
    return alert('ID must be entered, source must be selected');
  } else {
    if (src.value === 'TRB' && !(className && className.value)) {
      return alert('when source is TRB, type must be selected');
    }
  }
  $.post(
    '/1.5/import/edit',
    { id: id.value, src: src.value, className: className.value },
    function (data) {
      if (data.ok == 1) {
        alert(data.msg);
        getHist();
      } else {
        alert(data.err);
      }
    });
};
function createTemp(data) {
  var html = `
  <div style="font-size:12px">  
    <span>ID: ${data._id}</span>
    <span class="${data.status}">Status: ${data.status}</span>
    <span class="">Org: ${data.source}</span>
  </div>
  <div style="    height: 17px;">
    <span class="pull-right fa fa-arrow-up" onclick='syncProp("${data._id}")'></span>
    <span class="pull-right fa fa-refresh" onclick='resetProp("${data._id}")'></span>
    <span class="pull-right fa fa-trash red" onclick='deleteProp("${data._id}")'></span>
  </div>
  `;
  var newDiv = document.createElement("div");
  newDiv.classList.add('row');
  newDiv.classList.add('split');
  newDiv.innerHTML = html;
  document.querySelector('.hist').appendChild(newDiv);
};
function getHist() {
  $.post(
    '/1.5/import/history',
    {},
    function (data) {
      if (data.ok == 1) {
        document.querySelector('.hist').innerHTML = '';
        let histList = data.list;
        histList.forEach(d => {
          createTemp(d);
        });
      };
    });
};
function deleteProp(id) {
  $.post(
    '/1.5/import/delete',
    { id: id },
    function (data) {
      if (data.ok == 1) {
        alert(data.msg);
        getHist();
      } else {
        alert(data.err);
      }
    });
};
// 同步prop当前状态
function syncProp(id) {
  $.post(
    '/1.5/import/sync',
    { id: id },
    function (data) {
      if (data.ok == 1) {
        alert(data.msg);
        getHist();
      } else {
        alert(data.err);
      }
    });
}
// 导出prop的ids
function dumpIdsForImport() {
  $.post(
    '/1.5/import/dump',
    { limit: 20, skip: 0 },
    function (data) {
      if (data.ok == 1) {
        // alert(data.msg);
        renderTextInput(data.ids);
        getHist();
      } else {
        alert(data.err);
      }
    });
}
// 重置prop状态
function resetProp(id) {
  $.post(
    '/1.5/import/reset',
    { id: id },
    function (data) {
      if (data.ok == 1) {
        alert(JSON.stringify(data.msg));
        getHist();
      } else {
        alert(data.err);
      }
    });
}
// 渲染textarea，可用于复制
function renderTextInput(ids) {
  var html = `
    <textarea id="idInput" class="form-control" rows="5" placeholder="IDs here">${ids}</textarea>
    <button class="btn btn-positive" onclick="copyIds()">Copy</button>
  `;
  document.querySelector('#textCopyArea').innerHTML = html;
}
function copyIds() {
  var copyText = document.querySelector("#idInput");
  copyText.select();
  document.execCommand("copy");
  alert("Copied the text: " + copyText.value);
}

window.onload = function () {
  getHist();
};