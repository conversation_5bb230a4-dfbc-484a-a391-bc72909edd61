
var indexHeader = {
  data() {
    return {
      datas:[
        'sessionUser',
        'hasFollowedVipRealtor',
        'userCity',
        'isLoggedIn',
        'isRealtor',
        'isVipPlus',
        'hasAid',
        'lang',
        'isApp',
        'isCip',
        'coreVer',
        'projLastQuery',
        'languageObj',
        'isEmailVerified',
        'homePageABTest'
      ],
      dispVar: {
        lang:        'zh',
        userCity: {o:'Toronto',n:'多伦多'},
      },
      nativeSearchClicked:false,
      isNativeSearch:false,
      focused: false,
      userForm:{},
      signupTitle:'',
      feedurl: '/1.5/form/forminput',
      hasWechat:false,
      strings:{
        refreshAlert:{key:'Please refresh first.'},
      },
    };
  },

  mounted () {
    this.$getTranslate(this);
    function getAndroidVersion(ua) {
      ua = (ua || navigator.userAgent).toLowerCase();
      var match = ua.match(/android\s([0-9\.]*)/);
      return match ? match[1] : false;
    }
    var self = this, bus = window.bus;
    if (getAndroidVersion() && parseFloat(getAndroidVersion()) < 4.4) {
      self.oldVerBrowser = true;
    }
    if(/mls/.test(location.href)){
      self.datas = self.datas.concat(['savedSearches','favCmtys','propTags','edmDetail'])
    }
    if(/rm/.test(location.href)){
      self.datas = self.datas.concat(['forumAdmin','reqHost','shareHostNameCn','userGroups','isAdmin'])
    }
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.isNativeSearch = self.isNewerVer(self.dispVar.coreVer,'6.0.1');
      self.hasDeviceId = self.isNewerVer(self.dispVar.coreVer,'6.1.5');
      if (!self.updatedPN && d.updatePN && localStorage.pn) {
        self.postPN();
      }
      if (d.userCity) {
        self.rcmdCity = {o:d.userCity.o, n:d.userCity.n, lat:d.userCity.lat, lng:d.userCity.lng, p:d.userCity.p};
        if (d.userCity.cnt) {
          self.userCityCnt = d.userCity.cnt;
        }
      }
      if (self.dispVar.sessionUser && self.dispVar.sessionUser.eml) {
        for (let i of ['nm','eml','mbl','fnm']) {
          self.userForm[i] = self.dispVar.sessionUser[i];
        }
        if(!self.userForm.nm || self.userForm.fnm){
          self.userForm.nm = self.userForm.fnm || d.sessionUser.nm_en || d.sessionUser.nm_zh
        }
      }
      self.recordEmlAndTime();
      // NOTE: this register appstate change cb, will be invoked when back from desktop
      // RMSrv.onAppStateChange((opt)=>{
      //   alert(JSON.stringify(opt))
      // })
      if (d.lang && RMSrv.setAppLang && self.dispVar.lang) {
        if ('function' == typeof RMSrv.onReady) {
          RMSrv.onReady(()=>{
            if (self.hasDeviceId) {
              RMSrv.getUniqueId((id)=>{
                if (id && id.length > 5) {
                  self.logUserId(id)
                }
              })
            }
            if (RMSrv.hasWechat){
              RMSrv.hasWechat((has)=>{
                if (has!=null) {
                  self.logUserHasWechat(has)
                  self.hasWechat = has
                  if(!has){
                    self.hideElemById('#banners')
                    self.hideElemBySelectors('#indexAd > div.sp-holder  div.img > div')
                    self.hideDrawAndMove()
                    setTimeout(() => {
                      if(!self.dispVar.isVipPlus){
                        self.hideElemById('#realtorTodos')
                        self.hideElemById('#promotionsMarketing')
                      }
                    }, 100);
                  }
                }
              })
            }
            if(RMSrv.hasGoogleService){
              RMSrv.hasGoogleService((hasGoogle) => {
                var transit = '#drawers > div.swiper-wrapper  a.drawer.link-transit';
                var transitElem = document.querySelector(transit);
                if (!hasGoogle && transitElem) {
                  self.hideElemBySelectors(transit)
                  self.hideElemBySelectors('#drawers > div.swiper-wrapper  a.drawer.link-co-ophouse');
                  // var stig = '#drawers > div.swiper-wrapper  a.drawer.link-stigmatized';
                  // var stigElem = document.querySelector(stig);
                  // var parent = document.querySelector('#drawers > div.swiper-wrapper > div.drawers.swiper-slide > div:nth-child(2)');
                  // parent.append(stigElem);
                }
              });
            }
            var location = window.location.href;
            var domain = self.extractDomain(location);
            // RMSrv.setItemObj({key:'Cookie@'+domain,value:document.cookie,stringify:true,store:true});
            if(RMSrv.setCookie){
              RMSrv.setCookie();
            }
            RMSrv.action('pushToken');
            RMSrv.setAppLang(self.dispVar.lang);
            RMSrv.setItemObj({key:'lastLoadDomain',value:domain,stringify:false,store:false},()=>{
              // alert(`url=${ret.body.testUrl} \n---domain=${domain}`)
              // window.location.href = ret.body.testUrl;
            })
            if (RMSrv.refreshSystemValue) {
              RMSrv.refreshSystemValue();
            }
            if (RMSrv.removeItemObj && !window.localStorage.lastRMMapPosTs) {
              window.localStorage.lastRMMapPosTs = '202004230653';
              RMSrv.removeItemObj('lastMapPosition')
            }
          })
        }
      }
      if (!self.inited) {
        self.initIndexData();
      }
    });
    bus.$on('index-redirect-goto', function (dr) {
      self.goTo(dr);
    });
    bus.$on('prop-changed', function (prop) {
      // window.location = "/1.5/prop/detail/inapp?id="+prop._id;
      if (/^RM/.test(prop.id)) {
        propId = prop.id;
      } else {
        propId = prop._id;
      }
      if (!self.isNewerVer(self.dispVar.coreVer, '5.3.0')) {
        return window.location = "/1.5/search/prop?d=/1.5/index&id=" + propId;
      }
      var cfg = {hide:false,title:self.$_('RealMaster')};
      var propId;
      var url = "/1.5/prop/detail/inapp?lang="+self.dispVar.lang+"&id="+propId;//+'&mode='+self.appMapMode;
      url = self.appendDomain(url);
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        if (/^redirect/.test(val)) {
          return window.location = val.split('redirect:')[1]
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          if (/^cmd-redirect:/.test(val)) {
            // return alert(val);
            var url = val.split('cmd-redirect:')[1];
            return window.location = url;
          }
          // alert(val);
          window.location = '/1.5/mapSearch?d=/1.5/index&'+val;
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    });
    bus.$on('set-city', function (d) {
      var url = '',
          city = d.city,
          data = {city:city};
      if (self.mode == 'subscribe') {
        url = '/1.5/index/subscribe';
      } else {//set user city
        url = '/1.5/settings/city';
      }
      // post to server and get data for mode
      axios.post(url, data)
      .then((ret)=>{
        ret = ret.data;
        if (ret.ok) {
          // reset recommend
          toggleModal('citySelectModal');
          if (ret.msg) {
            window.bus.$emit('flash-message', ret.msg);
          }
          setLoaderVisibility('block');
          setTimeout(()=>{
            location.reload();
          },500)
        } else {
          window.bus.$emit('flash-message', ret.e);
        }
      })
      .catch( (ret) =>{
        console.error( "server-error" );
        // RMSrv.dialogAlert( "server-error" );
      });
    })
  },
  methods: {
    openCamera(){
      var p = -1,url,self = this;
      RMSrv.scanQR(function (r) {
        if (r && (p = r.indexOf('/s/')) > 0) {
          url = r.substr(p);
          return openPopup(url,self.$_('RealMaster'));
        }else if (r && /[1.5|qrcode]/.test(r)) {
          p = r.indexOf('/qrcode') > 0 ? r.indexOf('/qrcode') : r.indexOf('/1.5/');
          // 去除分享和没有返回标识，显示app内样式
          url = r.substr(p).replace('share=1','').replace('inFrame=1','');
        }else{
          // 未知的二维码路径
          url = '/qrcode/act404'
        }
        window.location.href = url;
      });
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    // showSignupOnclick(id){
    //   var elem = document.querySelector(id)
    //   if(elem && !this.dispVar.isRealtor){
    //     elem.onclick = ()=>{toggleModal('SignupModal')}
    //   }
    // },
    hideElemById(id){
      var banner = document.querySelector(id)
      if(banner){
        banner.remove()
      }
    },
    hideDrawAndMove(){
      var self = this;
      var agent = '#drawers > div.swiper-wrapper  a.drawer.link-agent';
      var agentElem = document.querySelector(agent)
      if(agentElem){
        self.hideElemBySelectors(agent)
        self.hideElemBySelectors('#drawers > div.swiper-wrapper  a.drawer.link-yellowpage')
      }
    },
    appendEmptyPlaceHolder(){
      let self = this;
      let stepParent = document.querySelector('#drawers > div.swiper-wrapper  .wrapper.newDrawers3')
      if(stepParent){
        for (let index = 0; index <= 1; index++) {
          let div = document.createElement("a")
          div.classList.add('drawer')
          stepParent.append(div)
        }
      }
    },
    hideElemBySelectors(sel){
      var avt = document.querySelectorAll(sel)
      if(avt && avt.length){
        for (let a of avt){
          a.remove()
        }
      }
    },
    logUserHasWechat(has){
      var self = this;
      if (!self.dispVar.isLoggedIn) {
        return;
      }
      if(self.loggedWechat){return}
      self.loggedWechat = true
      axios.post('/loguserwechat', {hasWechat:has})
      .then((ret)=> {
        ret = ret.data;
        if (ret.ok) {
          // console.log(ret);
        }
      }).catch((ret)=> {
        // console.error( "server-error" );
      });
    },
    logUserId(id){
      var self = this;
      if (!self.dispVar.isLoggedIn) {
        return;
      }
      axios.post('/matchuser', {id})
      .then((ret)=> {
        ret = ret.data;
        if (ret.ok) {
          console.log(ret);
        }
      }).catch((ret)=> {
        // console.error( "server-error" );
      });
    },
    onClickSearchBar() {
      if (!this.nativeSearchClicked) {
        //prevent user open multiple autocomplete
        var city = this.dispVar.userCity.o;
        var prov = this.dispVar.userCity.p;
        var lang = this.dispVar.lang || 'en';
        this.nativeSearchClicked = true;
        this.goTo({url:'/1.5/autocomplete?referer=index&city='+city+'&prov='+prov+'&lang='+lang});
        setTimeout(()=>{
          //set nativeSearchClicked back to false, so user can open autocomplete again
          // or set it using goback from autocomplete
          this.nativeSearchClicked = false;
        },1000);
      }
    },
    extractDomain(url){
      var hostname;
      //find & remove protocol (http, ftp, etc.) and get hostname
      if (url.indexOf("//") > -1) {
          hostname = url.split('/')[2];
      }
      else {
          hostname = url.split('/')[0];
      }
      //find & remove port number
      hostname = hostname.split(':')[0];
      //find & remove "?"
      hostname = hostname.split('?')[0];
      return hostname;
    },
    postPN(){
      if (!localStorage.pn || this.updatedPN) {
        return;
      }
      var self = this;
      self.updatedPN = true;
      axios.post('/1.5/user/updPn', {pn:localStorage.pn})
      .then((ret)=> {
        ret = ret.data;
        if (ret.r) {
        } else {
          console.error(ret.e);
          self.updatedPN = false;
        }
      }).catch((ret)=> {
        console.error( "server-error" );
      });
    },
    unsubscribe(){
      trackEventOnGoogle('homeSubscribedCities','unsubscribe');
      var url = '/1.5/index/subscribe', self = this;
      axios.post(url, {mode:'unsubscribe', idx:this.cityIdx})
      .then((ret) => {
        ret = ret.data;
        if (ret.ok) {
          if (ret.cnt != null) {
            self.userCityCnt = ret.cnt;
          } else {
            self.userCityCnt -= 1;
          }
          if (ret.msg) {
            window.bus.$emit('flash-message', ret.msg);
          }
          self.cityIdx = 0;
        } else {
          window.bus.$emit('flash-message', ret.e);
        }
      }).catch((ret) => {
        console.error( "server-error" );
        // RMSrv.dialogAlert( "server-error" );
      });
    },
    initIndexData(){
      this.inited = true;
      // this.getRcmd();
      this.hist = (typeof indexHistCtrl !== "undefined" && indexHistCtrl !== null ? indexHistCtrl.getHist() : void 0) || [];
    },
    // TODO:retire
    getCityList (mode) {
      let category = 'homeTopBar';
      if (mode == 'subscribe') {
        category = 'homeSubscribedCities';
        window.bus.$emit('select-city', {noloading:true});
        if (!this.dispVar.isLoggedIn) {
          window.location = '/1.5/user/login';
        }
      }
      this.mode = mode;
      window.bus.$emit('select-city', {noloading:true});
      trackEventOnGoogle(category,'selectCity');
    },
    recordEmlAndTime(){
      if(!this.dispVar.sessionUser) return;
      RMSrv.getItemObj('recordEml',true,(recordEml)=>{
        if(recordEml && recordEml.length){
          return;
        }
        fetchData('/1.5/user/encryptEml',{body:{}},function(err,ret) {
          if (ret.ok == 1 && ret.encryptedEml) {
            let objEml = {
              eml: ret.encryptedEml,
              ts: new Date()
            }
            RMSrv.setItemObj({key:'recordEml',value:objEml,stringify:true,store:true});
          }
        });
      });
    }
  },
  computed:{
  },
};
var app = Vue.createApp(indexHeader);

// app.component('sign-up-form', SignUpForm);
app.component('city-select-modal', CitySelectModal);
app.component('lang-select-cover', LangSelectCover);
app.component('flash-message', flashMessage);
app.mixin(rmsrvMixins);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#index-header');



function showVipInfo() {
  var url = 'https://www.realmaster.ca/membership';
  RMSrv.showInBrowser(url);
};

function changeTab(tab) {
  var promotionsTls = document.getElementsByClassName('promotionsTl');
  for (var i = 0; i < promotionsTls.length; i++) {
    if (promotionsTls[i].id == 'title-'+tab) {
      promotionsTls[i].classList.add('active');
    } else {
      promotionsTls[i].classList.remove('active');
    }
  }
  var promotionsLists = document.getElementsByClassName('promotionsList');
  for (var i = 0; i < promotionsLists.length; i++) {
    if (promotionsLists[i].id == tab) {
      promotionsLists[i].classList.add('active');
    } else {
      promotionsLists[i].classList.remove('active');
    }
  }
};
function hideAlert() {
  if(window.angular) {
    var appElement = document.querySelector('[ng-controller=ctrlNotifications]');
    var $scope = window.angular.element(appElement).scope();
  if ($scope){
    $scope['showAppUpgrade'] = false;
    $scope.$apply();
    } 
  }else {
    var alertWindow = document.getElementById('alert-window-bg');
    if(alertWindow) {
      alertWindow.style.display = 'none';
    }
  }
}

function closeApp() {
  if (RMSrv && RMSrv.exitApp) {
    RMSrv.exitApp();
  } else {
    hideAlert();
  }
}
function openDownloadLink(url) {
  var currentOS = document.getElementById('currentOS').value;
  if (currentOS === 'android' && RMSrv.hasGoogleService) {
    RMSrv.hasGoogleService(function(hasGoogle) {
      if (hasGoogle) {
        RMSrv.showInBrowser('https://play.google.com/store/apps/details?id=com.realmaster');
      }
    });
  }
  RMSrv.showInBrowser(url)
}
document.addEventListener("DOMContentLoaded", function() {
});

function swipeTodos() {
  var todoSwiper = new Swiper('.todoSwiper',{
    pagination: {
      el: '.todoSwiperPagination',
      type: 'fraction',
    }
  });
}
function swipeBanners() {
  var bannerSwiper = new Swiper('#banners',{
    autoplay: {
      delay: 5000,
      disableOnInteraction: false,
    },
    pagination:{
      el:'.bannerPagination',
      clickable:true
    }
  });
}
function swipeNews() {
  var newsSwiper = new Swiper('#news',{
    loop:true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination:{
      el:'.newsPagination',
      clickable:true
    }
  });
}
document.addEventListener("DOMContentLoaded", function(event) {
  setTimeout(function(){
    swipeBanners();
    if(document.querySelector('.newsPagination')){
      swipeNews();
    }
    // swipeDrawers();
    swipeTodos();
    calcSpWraper();
    // calcMktWraper()
    swipeAssigment();
    if(document.querySelector('.mls')){
      swipePreCon();
    }
    if(document.querySelector('#moreDrawers')){
      addDrewersEventListener(document.querySelector('#moreDrawers'))
    }
  },300);
});

function swipeDrawers() {
  var drawersSwiper = new Swiper('#drawers',{
    loop:false,
    pagination:{
      el:'.drawersPagination',
      clickable:true
    }
  });
}
function swipeAssigment() {
  var assignSwiper = new Swiper('#trustedAssign',{
    loop:true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination:{
      el:'.assignPagination',
      clickable:true
    },
    slidesPerView: 2,
    loopedSlides:2,
    spaceBetween: 10,
  });
};
function swipePreCon() {
  var preConSwiper = new Swiper('#preCon',{
    loop:true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    pagination:{
      el:'.preConPagination',
      clickable:true
    },
    slidesPerView: 2,
    loopedSlides:2,
    spaceBetween: 10,
  });
}

function toggleHome(target) {
  var url = '/home/'+target;
  gotoLink(url,{'key':'homeSwitch','val':target})
};
function calcSpWraper(){
  var spWraper = document.getElementsByClassName('sp-wrapper')
  if(spWraper.length){
    var calculatedSpWidth = parseInt((window.innerWidth-30)/2.05);
    var numOfProjs = document.querySelectorAll('.sp').length;
    var spWidth = calculatedSpWidth * numOfProjs;
    document.getElementsByClassName('sp-wrapper')[0].style.width = spWidth+'px';
    var sps = document.getElementsByClassName('sp');
    for (i = 0; i < sps.length; i++) {
      sps[i].style.width = calculatedSpWidth+'px';
    }
  }
}
// TODO: can use calc(42% - 30px) instead of js
// 已经修改为css计算，该函数目前没有被调用
function calcMktWraper(){
  var mktWraper = document.getElementsByClassName('mktrcmd')
  if(mktWraper.length){
    var calculatedMktWidth = parseInt((window.innerWidth-30)/2.05);
    var numOfRecos = document.querySelectorAll('.mkt').length;
    var mktWidth = calculatedMktWidth * numOfRecos;
    document.getElementsByClassName('mktrcmd')[0].style.width = mktWidth+'px';
    var mkts = document.getElementsByClassName('mkt');
    for (i = 0; i < mkts.length; i++) {
      mkts[i].style.width = calculatedMktWidth+'px';
    }
  }
}

function showMoreDrawers(){
  let wrapper = document.querySelector('#drawers-wrapper'),direction;
  direction = wrapper.classList.contains('limited-height') ? 'down' : 'up'
  wrapper.classList.toggle('limited-height')
  document.querySelector('#moreDrawers .fa-home-arrow-up').classList.toggle('hide')
  document.querySelector('#moreDrawers .fa-home-arrow-down').classList.toggle('hide')
  checkAndSendLogger(null,{sub:'mls',query:{direction},act:'toggle drawer'});
}
function addDrewersEventListener(elem){
  var moveEndY,startY;
  elem.ontouchstart = function (e) {
    e.preventDefault();
    startY = e.changedTouches[0].pageY;
  }
  elem.ontouchend = function (e) {
    e.preventDefault();
    moveEndY = e.changedTouches[0].pageY;
    showMoreDrawers()
  }
  elem.onclick = function (e) {
    e.preventDefault();
    showMoreDrawers()
  }
}

window.addEventListener('click', checkAndSendLogger);
