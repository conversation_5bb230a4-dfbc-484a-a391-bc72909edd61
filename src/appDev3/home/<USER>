ONE_DAY = 86400000; //1000 * 3600 * 24
initUrlVars();
var forum = {
  data() {
    return {
      showSearchbar:false,
      search:'',
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        forumAdmin: false,
        sessionUser: '',
        newsAdmin:false,
        isAdmin: false,
      },
      pageTag:'news',
      forumLangs:[],
      flagPost:null,
      newsList:[],
      newsPageNum:1,
      newsHasMore:true,
      newsTags:{},
      videoList:[],
      videoPageNum:1,
      videoHasMore:true,
      videoTags:{},
      posts_more: false,
      qna:[],
      qna_more: false,
      todayChoices: [],
      todayChoices_more: false,
      news:[],
      news_more: false,
      curCity: {},
      exCities: null,
      curPost: {},
      loading: false,
      displayPage: 'all',
      scrollElement: null,
      tag: null,
      src: null,
      gid: null,
      blkUids:{},
      blkCmnts:{},
      lastPropTag:'',
      showMask: false,
      title:'RealMaster',
      showReportForm:false,
      reportForm:{
        postId:null,
        title: 'Report',
        feedurl:'/1.5/form/forminput',
        userForm:{
          m:'',
          // ueml:'<EMAIL>',
          tp:'feedback',
          subjectTp:'Forum Complaint',
          formid:'system',
          id:'',
          violation:'',
          tl:''
        }
      },
      form:'homeForum',
    }
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    try {
      this.blkUids = JSON.parse(localStorage.getItem('blkUids')) || {};
      this.blkCmnts = JSON.parse(localStorage.getItem('blkCmnts')) || {};
    } catch (error) {
      return window.bus.$emit('flash-message', error.toString());
    }
    var isHomePage = false;
    if(/\/home\/marketplace/.test(document.URL)){
      isHomePage = true
    }
    if(!isHomePage){
      self.$nextTick(function () {
        $('#forum-containter').xpull({
         'callback':function(){
            self.reloadPosts();
         }
        });
      });
    }
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    window.bus.$on('pagedata-retrieved', function (d) {
      // 在indexHeader中发送datas请求  'isCip','isApp','isLoggedIn','lang','forumAdmin','reqHost','isAdmin',
      self.dispVar = Object.assign(self.dispVar, d);
      if(!d.isCip){
        clearTimeout(self.getForumTimeout)
        self.getForumTimeout = setTimeout(() => {
          self.getShowData()
        }, 500);
        self.scrollElement = document.getElementById('content');
        self.scrollElement.addEventListener('scroll', function() {
          self.handleLoadMore();
        });
      }
    });
  },
  methods: {
    calcDirection:function(k,idx){
      // this.propTags
      // idx > cur -> l2r
      // index < cur -> r2l
      let curIdx = this.pageTag == 'news'?0:1
      let lastIdx = this.lastPropTag == 'news'?0:1
      if(idx==curIdx){
        return lastIdx>idx?'r2l':'l2r';//last value
      }
      if(idx==lastIdx){
        return 'out'
      }
      return '';
    },
    clickMask() {
      this.showMask = false;
      this.toggleFlagModal(false);
    },
    selectPage(tag) {
      if (this.pageTag != tag) {
        this.lastPropTag = this.pageTag;
        this[this.computedTags]={
          tag:this.tag,
          src:this.src,
          curCity:this.curCity
        }
        this.pageTag = tag;
        this.tag = this[this.computedTags].tag || null;
        this.src = this[this.computedTags].src || null;
        this.curCity = this[this.computedTags].curCity || {};
        if(!this[this.computedListKey].length){
          this.getShowData()
        }
        trackEventOnGoogle(this.form,'tag',tag)
      }
      setTimeout(() => {
        document.getElementsByClassName("tabs-container")[0].click();
      }, 10);
    },
    getShowData(){
      if(this.pageTag != 'news'){
        // 数据库中保存的字段就是中文
        this.tag ='视频'
      }
      var params = this.getSearchParmas();
      params.page = this[this.computedPageNumber]
      this.getAllPost(params,{
        listKey:this.computedListKey,
        hasMoreKey:this.computedHasMoreKey
      });
    },
    clearTag(type) {
      switch (type) {
        case 'all':
          this.tag = null;
          this.src = null;
          this.curCity = {};
          break;
        case 'tag':
          this.tag = null;
          break;
        case 'gid':
          this.gid = null;
          break;
        case 'src':
          this.src = null;
          break;
        case 'city':
          this.curCity = {};
          break;
      }
      this[this.computedListKey]=[];
      this[this.computedPageNumber]=1;
      this.getShowData();
      setTimeout(() => {
        document.getElementsByClassName("tabs-container")[0].click();
      }, 10);
    },
    getSearchParmas() {
      var params = {};
      var self = this;
      if (self.search) {
        params.search = self.search;
      }
      if (this.curCity && this.curCity.o) {
        params.city = this.curCity.o;
      }
      if (this.gid) {
        params.gid = this.gid;
      }
      if (this.curCity && this.curCity.p) {
        params.prov = this.curCity.p;
      }
      if (this.curCity && this.curCity.cnty) {
        params.cnty = this.curCity.cnty;
      }
      if(this.tag)
        params.tag = this.tag;
      return params;
      },
    handleLoadMore:function(){
      var self = this, wait = 400;
      if (!self.waiting) {
        self.waiting = true;
        self.scrollLoading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if(self[self.computedHasMoreKey]){
              self[self.computedPageNumber] += 1;
              self.getShowData();
            }
          }
          self.checkScrollAndSendLogger(element)
        }, wait);
      }
    },
  },
  computed:{
    hasTag:function() {
      return (this.tag && this.tag != '视频')||this.gid||this.curCity.o||this.curCity.p||this.curCity.cnty
    },
    computedPageNumber:function(){
      return this.pageTag+'PageNum'
    },
    computedListKey:function(){
      return this.pageTag+'List'
    },
    computedHasMoreKey:function(){
      return this.pageTag+'HasMore'
    },
    computedTags:function(){
      return this.pageTag+'Tags'
    },
    noTag: function() {
      return {
        tag: this.tag,
        property: this.src=='property',
        psch: this.src=='psch',
        sch: this.src=='sch',
        topic: this.displayPage == 'topic',
        city: this.curCity.o,
        gid: this.gid
      }
    }
  },
};
var app = Vue.createApp(forum);
app.mixin(rmsrvMixins);
app.mixin(pageDataMixins);
app.mixin(forumMixins);
app.mixin(forumCommonMixins);
trans.install(app,{ref:Vue.ref})
app.component('forum-summary-card', ForumSummaryCard);
app.component('flash-message', flashMessage);
app.component('report-forum-form', ReportForumForm);
app.component('block-popup', BlockPopup);
app.config.globalProperties.$filters = {currency:filters.currency}
app.mount('#forum');