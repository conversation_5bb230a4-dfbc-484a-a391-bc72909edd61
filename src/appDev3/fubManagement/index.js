var fubManagem = {
  data(){
    return {
      emailList:[]
    }
  },
  mounted(){
    this.$getTranslate(this);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.initData();
  },
  methods:{
    initData(){
      setLoaderVisibility('block');
      fetchData('/fubManagement/getEmail',{body:{}},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok) {
          return bus.$emit('flash-message','Error');
        }
        this.emailList = ret.result
      });
    },
    saveEmail(id,value){
      if(value.length == 0){
        return bus.$emit('flash-message','Please input email!');
      }
      setLoaderVisibility('block');
      updataEml = {
        id,
        value,
      }
      fetchData('/fubManagement/updateEmail',{body:{updataEml}},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok) {
          return bus.$emit('flash-message','Error');
        }
        bus.$emit('flash-message',ret.result);
      });
    }
  },
}
var app = Vue.createApp(fubManagem);
trans.install(app,{ref:Vue.ref});
app.component('flash-message', flashMessage);
app.mount('#fubManagem');