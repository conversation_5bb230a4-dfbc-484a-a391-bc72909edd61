var searchProvider = {
  data() {
    return {
      datas:[
        'isCip',
        'isApp',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipAlliance',
        'isMerchant',
        'isAdmin',
        'userCity',
        'ownerData',
        'shareHost',
        'isProdSales',
        'forumAdmin',
        'isChatBlocked',
        'verifiedRole',
        'profileStars',
        'defaultEmail',
      ],
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        sessionUser: '',
        reqHost:'',
        shareHost:'',
        isMerchant:false,
        isVipAlliance: false,
        isAdmin: false,
        forumAdmin: false,
        userCity: {o:'Toronto',n:'多伦多'},
        ownerData:{},
        isProdSales:false,
        profileStars:0,
        verifiedRole:'realtor',
        defaultEmail:'',
      },
      tp:'mortgage',
      dots:[],
      dot:0,
      scrollStart:0,
      message:'',
      resultList:[],
      error:'',
      name:'',
      loading:false,
      curRealtor:{},
      page:0,
      waiting:false,
      scrollElement:'',
    };
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor()
    // }
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d)
    });
    bus.$on('update-click', function (opt) {
      self.updateClick(opt.tp, {}, opt.cb);
    });
    bus.$on('set-cur-realtor', function (r) {
      self.setCurRealtor(r);
    });
    bus.$on('set-fold', function ({u,idx,arr}) {
      self.setFold(u,idx,arr);
    });
    bus.$on('show-wecard', function (r) {
      self.showWecard(r);
    });
    bus.$on('toggleFav', function ({fav,uid}) {
      self.fav(fav,uid);
    });
    self.getFavAgents()
    self.scrollElement = document.getElementById('searchProvider')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  methods: {
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getFavAgents()
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    formatResult(resultList){
      var ret = [];
      // NOTE: set fold and recos
      for (var i = 0; i < resultList.length; i++) {
        let u = resultList[i];
        u.fold = false;
        if (u.itr && this.byteCount(u.itr)>50) {
          u.fold = true;
        }
        ret.push(u)
      }
      return ret;
    },
    byteCount(s) {
      return encodeURI(s).split(/%..|./).length - 1;
    },
    goBack() {
      if(vars.d) {
        document.location.href = vars.d;
      } else {
        window.history.back();
      }
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    search(){
      var self = this;
      if(self.name == ''){
        return;
      }
      self.waiting = true;
      self.scrollElement.scrollTop = 0
      var data = {'name': self.name, tp:self.tp,page:self.page};
      fetchData('/1.5/yellowpage/search', {body:data},function (err,ret) {
        self.waiting = false;
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          if (ret.ok) {
            var resultList= self.formatResult(ret.resultList)
            self.resultList = resultList;
          } else {
            self.message = ret.err;
          }
        }
      });
    },
    getFavAgents() {
      var self = this;
      var limit = 20
      var self = this, data = {page:self.page,limit};
      self.waiting = true;
      fetchData('/1.5/yellowpage/myfav', {body:data},function (err,ret) {
        self.waiting = false;
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          var resultList= self.formatResult(ret.resultList)
          if(self.page == 0){
            self.resultList = resultList;
          }else{
            self.resultList = self.resultList.concat(resultList)
          }
          if(resultList.length >= limit){
            self.hasMoreProps = true
          }else{
            self.hasMoreProps = false
          }
        }
      });
    },
    fav(fav, uid) {
      event.stopPropagation();
      var self = this;
      if (!this.dispVar.isLoggedIn) {
        if (!this.dispVar.isApp)  {
          this.goTo('/app-download?lang='+this.dispVar.lang);
        } else {
          RMSrv.closeAndRedirect('/1.5/user/login');
        }
      } else {
        fetchData('/1.5/yellowpage/fav', {body:{uid: uid, fav: fav}},function (err,ret) {
          if (err || ret.e) {
            return RMSrv.dialogAlert(err || ret.e);
          } else {
            if (ret.ok) {
              var user = self.resultList.find(function(data) {
                return data._id == uid;
              });
              if (user) {
                user.isfaved = fav;
              }
              window.bus.$emit('flash-message',ret.msg)
            }
          }
        });
      }
    },
    setCurRealtor(r) {
      var self = this;
      self.curRealtor = r;
      self.updateClick('contact');
      window.bus.$emit('toggle-contact-smb',r)
      // return self.toggleSMB();
    },
    setFold(u, idx, arr){
      // var u2 = Object.assign(u, {fold:false});
      // var u2 = {};
      // for (let key of Object.keys(u)) {
      //   u2[key] = u[key];
      // }
      // u2.fold = false;
      // this[arr].$set(idx, u2);
      // this.$set(this[arr],idx,u2);
      this.resultList[idx].fold = false;
      // this.$set(u, 'fold', value);
    },
    showWecard(u){
      this.curRealtor = u;
      this.updateClick('wesite');
      var url = '/1.5/wesite/' + u._id + '?inFrame=1';
      this.tbrowser(url);
    },
  },
  computed:{
  },
};
initUrlVars();
var app = Vue.createApp(searchProvider);
app.component('flash-message', flashMessage);
app.component('contact-realtor', contactRealtor);
app.component('agent', agent);
app.mixin(pageDataMixins);
app.mixin(userMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#searchProvider');