var yellowpage = {
  data() {
    return {
      datas:[
        'isCip',
        'isApp',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipAlliance',
        'isMerchant',
        'isAdmin',
        'userCity',
        'ownerData',
        'shareHost',
        'isProdSales',
        'forumAdmin',
        'isChatBlocked',
        'verifiedRole',
        'profileStars',
        'defaultEmail',
      ],
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        sessionUser: '',
        reqHost:'',
        shareHost:'',
        isMerchant:false,
        isVipAlliance: false,
        isAdmin: false,
        forumAdmin: false,
        userCity: {o:'Toronto',n:'多伦多'},
        ownerData:{},
        isProdSales:false,
        profileStars:0,
        verifiedRole:'realtor',
        defaultEmail:'',
      },
      categories:[],
      tp:'mortgage',
      role:'mortgage',
      dots:[],
      dot:0,
      scrollStart:0,
      sa:{o:'Toronto',n:'多伦多',p_ab:'ON'},
      message:'',
      resultList:[],
      error:'',
      name:'',
      loading:false,
      selectedSub:[],
      subcategory:[],
      allPosts:[],
      pgNum: 1,
      posts_more: false,
      currentPage:'person',
      checkedUser:[],
      share:false,
      currentCat: null,
      resultByCat:[],
      shareMode:false,
      curRealtor:{},
      tpName:vars.tpName,
      selectFilter:[],
      isSameRole:false,
      displayPage:'yellowpage',
      page:0,
      waiting:false,
      scrollElement:'',
      hasMoreProps:false,
      tagTop:0,
      categoriesBoxStyle:"position: relative; top:0;",
      placeholderHeight:'0'
    };
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.getPageData(self.datas, {}, true);
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor()
    // }
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d)
      self.showDots()
      if(d.userCity){
        self.sa = self.dispVar.userCity;
        window.bus.$emit('set-user-city', {city: self.dispVar.userCity});
        self.getCategories(function() {
          self.selectCategory(vars.tp,true)
          var navLinksContainer = document.querySelector('.categories-nav-container');
          var boundData = navLinksContainer.getBoundingClientRect();
          self.tagTop = boundData.top - 44
        });
      }
    });
    bus.$on('set-city', function (d) {
      var city = d.city;
      self.sa = city;
      toggleModal('citySelectModal', 'close');
      self.getProviders();
    });
    bus.$on('update-click', function (opt) {
      self.updateClick(opt.tp, {}, opt.cb);
    });
    bus.$on('set-cur-realtor', function (r) {
      self.setCurRealtor(r);
    });
    bus.$on('set-fold', function ({u,idx,arr}) {
      self.setFold(u,idx,arr);
    });
    bus.$on('show-wecard', function (r) {
      self.showWecard(r);
    });
    bus.$on('toggleFav', function ({fav,uid}) {
      self.fav(fav,uid);
    });
    self.scrollElement = document.getElementById('yellowpage')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  methods: {
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        // console.log('do loading more');
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page++;
              self.getProviders('more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
      if(self.scrollElement.scrollTop >= self.tagTop){
        self.categoriesBoxStyle = "position: fixed; top:44px;"
        self.placeholderHeight = '96px'
      }else if(self.scrollElement.scrollTop + 96 <= self.tagTop){
        self.categoriesBoxStyle = "position: relative; top:0;"
        self.placeholderHeight = '0'
      }else{
        self.categoriesBoxStyle = "position: relative; top:0;"
        self.placeholderHeight = '0'
      }
    },
    goSearch(){
      var url = "/1.5/yellowpage/searchProvider?d=/1.5/yellowpage&tp="+ this.tp;
      if (RMSrv.closeAndRedirectRoot) {
        RMSrv.closeAndRedirectRoot(this.appendDomain(url));
        return;
      }
      window.location = url;
    },
    // goToAgent(){
    //   var url = "/1.5/user/follow?d=/1.5/yellowpage";
    //   if(this.sa){
    //     url += '&city='+this.sa.o+'&prov='+this.sa.p+'&cityName='+this.sa.n
    //   }
    //   if (RMSrv.closeAndRedirectRoot) {
    //     RMSrv.closeAndRedirectRoot(this.appendDomain(url));
    //     return;
    //   }
    //   window.location = url;
    // },
    formatResult(resultList){
      var ret = [];
      // NOTE: set fold and recos
      for (var i = 0; i < resultList.length; i++) {
        let u = resultList[i];
        u.fold = false;
        if (u.itr && this.byteCount(u.itr)>50) {
          u.fold = true;
        }
        ret.push(u)
      }
      return ret;
    },
    byteCount(s) {
      return encodeURI(s).split(/%..|./).length - 1;
    },
    editProfile(){
      var url = "/1.5/settings/editProfile?d=/1.5/yellowpage";
      if (RMSrv.closeAndRedirectRoot) {
        RMSrv.closeAndRedirectRoot(this.appendDomain(url));
        return;
      }
      window.location = url;
    },
    showDots(){
      this.dots = []
      if(this.dispVar.isMerchant){
        if(!this.dispVar.isVipAlliance){
          this.dots.push({highlight:true})
        }
        if(this.dispVar.profileStars < 5){
          this.dots.push({highlight:false})
        }
      }
    },
    dotsScrollLeft(index){
      var box = document.getElementById('tips');
      var width = document.body.clientWidth
      box.scrollLeft = index * width;
      this.dots.forEach((ele,i)=>{
        if (i == index){
          ele.highlight = true
        }else{
          ele.highlight = false
        }
      })
    },
    tipsLogScrollLeft(){
      var self = this;
      self.scrollStart = document.getElementById('tips').scrollLeft
    },
    tipsOnScroll(){
      var self = this, wait = 400;
      var element = document.getElementById('tips');
      if (element.scrollLeft > self.scrollStart) {
          self.dot += 1;
          if (self.dot >self.dots.length){
            self.dot = self.dots.length
          }
          self.dotsScrollLeft(self.dot);
      } else if(element.scrollLeft < self.scrollStart){
          self.dot -= 1;
          if (self.dot < 0){
            self.dot = 0
          }
          self.dotsScrollLeft(self.dot);
      }
    },
    scrollBar(){
      var navLinksContainer = document.querySelector('.categories-nav-container');
      var navLinkSelected = document.querySelector('.selected');
      var navLinkActive = document.querySelector('.categories-nav-active');
      var windowWidth = window.innerWidth
      var boundData = navLinkSelected.getBoundingClientRect();
      var diffWidth = (windowWidth - boundData.width) / 2
      var targetOffset = navLinkSelected.offsetLeft - diffWidth;
      navLinkActive.style.left = targetOffset + windowWidth/2 - 15 +'px';
      if (targetOffset < 0) {
        navLinksContainer.scrollLeft = 0;
        return
      }
      navLinksContainer.scrollLeft = targetOffset
    },
    getCategories(cb) {
      var self = this;
      fetchData('/1.5/yellowpage/category.json', {},function (err,ret) {
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          if (ret.ok) {
            self.categories = ret.categories;
            // for (var i=0; i<self.categories.length; i++) {
            //   self.categories[i].img = self.imgs[i].default;
            // }
            if(cb) {
              return cb();
            }
          }
        }
      });
    },
    goBack() {
      var url = '/1.5/index';
      if(vars.d) {
        url = vars.d;
      }
      window.location = url;
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    selectFilter:function(name) {
      this.currentPage = name;
      if (name=='person') {
        this.getProviders();
      } else if(name=='posts') {
        this.allPosts = [];
        this.getAllPost(this.getSearchParmas());
      }
    },
    getSearchParmas() {
      var params = {};
      params.yellowpage = this.tp;
      params.subcate = this.selectedSub;
      return params;
    },
    isSelected:function(key) {
      if(this.selectedSub.indexOf(key) >=0 ){
        return 'active'
      } else {
        return '';
      }
    },
    contactRM(){
      var r = {
        eml:this.dispVar.defaultEmail || '<EMAIL>',
        mbl:**********,
        message:'Be featured on the TOP of agent list and more chances to attract clients. Contact us to learn more.',
        uid:'hide'
      }
      this.setCurRealtor(r);
    },
    selectCategory(tp,init){
      var self = this;
      if (['mortgage', 'lawyer', 'tax', 'insur', 'bas', 'maint','reno','secur'].indexOf(tp) > -1) {
        self.tp = self.role = tp;
      }
      this.selectedSub = [];
      var category = self.categories.find(function(cat) {
        return cat.id == self.tp;
      });
      if (category) {
        self.subcategory = category.sub;
      }
      self.getProviders();
      setTimeout(() => {
        self.scrollBar();
      }, 200);
      if(!init){
        self.scrollToTop()
      }
    },
    selectSubCategory(key) {
      var self = this;
      var index = self.selectedSub.indexOf(key)
      if (index >=0) {
        self.selectedSub.splice(index,1);
      } else {
        self.selectedSub.push(key);
      }
      if (self.currentPage=='person') {
        self.getProviders();
      } else if(self.currentPage=='posts') {
        self.allPosts = [];
        self.getAllPost(self.getSearchParmas());
      }
      self.scrollToTop()
    },
    scrollToTop(){
      var content = document.getElementById('yellowpage');
      content.scrollTop = this.tagTop
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },

    selectCity(){
      window.bus.$emit('select-city',{});
    },
    getProviders(more) {
      if(!more){
        this.page = 0;
        this.resultList = [];
      }
      var self = this, data = {tp:self.tp,page:self.page,limit:20};
      if (self.sa.o) {
        data.city = self.sa.o;
        if (self.sa.p_ab) {
          data.prov = self.sa.p_ab;
        }
      }
      data.sub = this.selectedSub;
      self.waiting = true;
      fetchData('/1.5/yellowpage/providers', {body:data},function (err,ret) {
        self.waiting = false;
        if(err || ret.e){
          self.message = err || ret.e;
        }else{
          if (ret.ok) {
            self.error = null;
            var resultList= self.formatResult(ret.resultList)
            if(self.page == 0){
              self.resultList = resultList;
            }else{
              self.resultList = self.resultList.concat(resultList)
            }
            if(self.page + 1 < ret.totalPages){//page从0开始
              self.hasMoreProps = true
            }else{
              self.hasMoreProps = false
            }
          } else {
            self.resultList = [];
            return self.message = ret.err;
          }
        }
      });
    },
    fav(fav, uid) {
      event.stopPropagation();
      var self = this;
      if (!this.dispVar.isLoggedIn) {
        if (!this.dispVar.isApp)  {
          RMSrv.openTBrowser('/app-download?lang='+this.dispVar.lang);
        } else {
          RMSrv.closeAndRedirect('/1.5/user/login');
        }
      } else {
        fetchData('/1.5/yellowpage/fav', {body:{uid: uid, fav: fav}},function (err,ret) {
          if (err || ret.e) {
            return RMSrv.dialogAlert(err || ret.e);
          } else {
            if (ret.ok) {
              var user = self.resultList.find(function(data) {
                return data._id == uid;
              });
              if (user) {
                user.isfaved = fav;
              }
              window.bus.$emit('flash-message',ret.msg)
            }
          }
        });
      }
    },
    setCurRealtor(r) {
      var self = this;
      self.curRealtor = r;
      self.updateClick('contact');
      window.bus.$emit('toggle-contact-smb',r)
      // return self.toggleSMB();
    },
    setFold(u, idx, arr){
      // var u2 = Object.assign(u, {fold:false});
      // var u2 = {};
      // for (let key of Object.keys(u)) {
      //   u2[key] = u[key];
      // }
      // u2.fold = false;
      // this[arr].$set(idx, u2);
      // this.$set(this[arr],idx,u2);
      this.resultList[idx].fold = false;
      // this.$set(u, 'fold', value);
    },
    showWecard(u){
      this.curRealtor = u;
      this.updateClick('wesite');
      var url = '/1.5/wesite/' + u._id + '?inFrame=1';
      this.tbrowser(url);
    },
    goToReno(url) {
      if(!url){
        url = 'https://www.realrenovation.ca?d=/1.5/yellowpage';
      }
      return RMSrv.showInBrowser(url);
    }
  },
  computed:{
  },
};
initUrlVars();
var app = Vue.createApp(yellowpage);
app.component('flash-message', flashMessage);
app.component('contact-realtor', contactRealtor);
app.component('city-select-modal', CitySelectModal);
app.component('agent', agent);
app.mixin(pageDataMixins);
app.mixin(userMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#yellowpage');