var similar = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: false,
      loading: false,
      rcmdHeight:0,
      propImgHeight:0,
      maxLog : 100,
      similarProps:[],
      simiLimit:50,
      buildLimit: 20,
      showFavGroups:false,
      prop:{fav:false,favGrp:[]},//待添加收藏的房源
      isBuilding:false,
      propId:'',
      propType:'',
      curSaleType:'Sold',
      curProp:{},
      lastSaleType:'',
      saleTypes:[
        {list:'SoldList',key:'Sold',val:'Sold'},
        {list:'LeasedList',key:'Leased',val:'Leased'},
        {list:'SaleList',key:'Sale',val:'For Sale'},
        {list:'RentList',key:'Rent',val:'For Rent'},
      ],
      fromPage:'similar',
      SoldList:[],
      LeasedList:[],
      SaleList:[],
      RentList:[],
      dispVar:{
        lang:'zh-cn',
        isCip:false,
        isLoggedIn:false,
        isDevGroup:false,
        isRealGroup:false,
        sessionUser: {},
        allowedShareSignProp:false,
        listShareMode:true,
        isApp: false,
      },
      datas:[
        'lang',
        'isCip',
        'isLoggedIn',
        'isVipRealtor',
        'isAdmin',
        'isRealtor',
        'isDevGroup',
        'isRealGroup',
        'allowedEditGrpName',
        'sessionUser',
        'shareUID',
        'allowedShareSignProp',
        'shareAvt',
        'isVipUser',
        'isApp',
      ],
      filterCondit:[],
      selectedCondit:'',
      strings:{
        ERR_RETRY:'You are visiting too frequently in a short time period. Wait and retry later.',
        SCROLL_BOTTOM: 'No more data'
      },
      showPropTable: false,
      action: '',
      actionIsCMA: false,
      favProps:[],
      currentGrp:{
        idx: "0",
        val: {v: "Default"}
      },
      grps:[],
      selectedProp:[],
      filterTime: [
        {k: 8,v: 8,},
        {k: 12,v: 12},
        {k: 18,v: 18},
        {k: 24,v: 24}
      ],
      wDl: true,
      wSign: true,
      shareList:[],
      SIM_OFFSET: 250
    }
  },
  beforeMounted(){
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    if(vars.building){
      this.isBuilding=true;
    }
    if(vars.id){
      this.propId=vars.id;
    }
    if(vars.type){
      this.propType=vars.type;
    }
    self.getSimilarProps();
    self.scrollElement = document.getElementById('similar')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.rcmdHeight = parseInt(window.innerWidth/1.6);
    self.propImgHeight = parseInt((window.innerWidth-48)/1.6/2);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('reset',()=>{
      this.reset()
    })
    bus.$on('prop-fav-changed',({prop})=>{
      this.prop = prop;
      if(self.actionIsCMA){
        self.actionIsCMA = false;
        self.action = 'cma';
        this.getFavPropList();
      }
    })
    bus.$on('commit-selected',(d) => {
      self.selectedProp = d;
      self.commitSelected();
    })
    if(window.loadLazyImg){
      loadLazyImg()
    };
    function genSlideMapping(tp) {
      const [nm, length] = tp;
      var ret = [{k:'', v:'Any'}];
      const start = (nm == 'filterCondit')?0:1;
      for (let i = start; i<length+1; i++) {
        ret.push({k:i+'',v:i+''});
        if (nm != 'filterCondit' || i != 0) {
          ret.push({k:i+'+n',v:i+'+•'});
        }
      }
      return ret;
    }
    function initSlideMapping() {
      for (let tp of [['filterCondit',5]]) {
        self[tp[0]] = genSlideMapping(tp);
      }
    }
    if(self.isBuilding){
      initSlideMapping();
    }else{
      self.filterCondit = self.filterTime;
      self.selectedCondit = self.filterTime[0].k
    }
  },
  computed:{
    sessionUserContact: function(){
      var ret = [], user;
      if (!this.wSign) {
        return '';
      }
      user = this.dispVar.sessionUser || {};
      var nm = '';
      if (this.dispVar.lang == 'en') {
        nm = user.nm_en;
      } else {
        nm = user.nm_zh;
      }
      // ret += user.fn?user.fn+user.ln:user.nm;
      if (nm){
        ret.push(nm);
      }else{
        ret.push(user.nm);
      }
      if(user.mbl){
        ret.push(user.mbl);
      }
      ret.push(user.eml);
      ret = ret.join(', ');
      return ret;
    },
    shareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : '/img/create_exlisting.png';
    },
    mShareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : this.dispVar.shareAvt;
    },
    mShareData: function () {
      var shareList = this.shareList, prop = this.prop, lang = this.dispVar.lang;
      var data_text = `id=${shareList.join(',')}&tp=listingList&lang=${lang}&share=1`
      if (this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        data_text += "&uid=" + this.dispVar.shareUID;
      }
      return data_text;
    },
    shareData: function () {
      function userListing(prop){
        var id = prop.adrltr?prop.adrltr['_id']:null;
        if (id == prop.uid) {
          return true;
        }
        return false;
      }
      var prop = this.prop, lang = this.dispVar.lang;
      var propID;
      if (this.isRMProp(prop)) {
        propID = prop.id;
      } else {
        propID = prop['_id'] || prop.sid;
      }
      var shareUID = this.dispVar.shareUID;
      var data_text = `id=${propID}&tp=listing&lang=${lang}`
      if (this.isRMProp(prop)) {
        if (!shareUID || userListing(prop)) {
          shareUID = prop.uid;
        }
      }
      if (this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        if (this.dispVar.isLoggedIn) {
          data_text += "&uid=" + shareUID;
        }
      }
      // force append download
      if (this.isRMProp(prop)) {
        if (!/wDl=1/.test(data_text)) {
          data_text += '&wDl=1';
        }
        data_text += "&wSign=1";
      }
      return data_text;
    }
  },
  methods: {
    isRMProp (prop) {
      return /^RM/.test(prop.id);
    },
    selecteBd(bd){
      this.selectedCondit = bd.k;
      this.page = 0;
      this.getSimilarProps();
      document.getElementById('similar').scrollTop = 0;
    },
    calcDirection:function(k,idx){
      let curK = this.curSaleType;
      let lastK = this.lastSaleType;
      let curIdx = this.saleTypes.findIndex((e)=>{return e.key==curK})
      let lastIdx = this.saleTypes.findIndex((e)=>{return e.key==lastK})
      if(idx==curIdx){
        return lastIdx>idx?'r2l':'l2r';//last value
      }
      if(idx==lastIdx){
        return 'out'
      }
      return '';
    },
    selectType(type){
      var list = 'SoldList'
      if(type){
        this.lastSaleType = this.curSaleType;
        this.curSaleType = type.key;
        list = type.list;
        document.getElementById('similar').scrollTop = 0;
      }
      if(this.lastSaleType == this.curSaleType) return ;

      this.page = 0;
      // if(!this.isBuilding && this[list].length > 0){
      //   return this.similarProps = this[list];
      // }
      this.getSimilarProps();
    },
    savePropList(propList){
      this.curProp = propList.origProp;
      let items = propList.items;
      if(this.isBuilding){
        if(propList.items.length >= this.buildLimit){
          this.hasMoreProps = true;
        }else{
          this.hasMoreProps = false;
        }
      } else {
        if(items.length>0){
          items.forEach(prop => {
            prop = this.compareDifference(prop);
          });
        }
        if(propList.items.length >= this.simiLimit){
          this.hasMoreProps = true;
        }else{
          this.hasMoreProps = false;
        }
      }
      this[this.curSaleType+'List'] = this[this.curSaleType+'List'].concat(items);
      this.similarProps = this[this.curSaleType+'List'];
    },
    reset:function(){
      var self = this;
      self.grpsOptions = false;
      self.showFavGroups = false;
      self.showPropTable = false;
      self.action='';
      self.selectedProp = [];
      window.bus.$emit('reset-sel-prop');
    },
    toggleFav(prop){
     this.showFavGroups = true;
     this.prop = prop;
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getSimilarProps();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getSimilarProps:function() {
      var self = this;
      var url = '/1.5/similar';
      var body = {
        page:this.page,
        limit:this.simiLimit,
        id:this.propId,
        type : this.propType,
        month: this.selectedCondit
      };
      body.saleDesc = this.curSaleType;
      if(/sold|sale/i.test(this.curSaleType)){
        body.saletp = 'Sale';
      }else{
        body.saletp = 'Lease';
      }
      if(this.isBuilding){ // 查找同楼房源
        url += '/building';
        body.bdrms = this.selectedCondit;
        body.limit = this.buildLimit;
        delete body.month;
      }else{ // 查找相似房源
        let offset = this.page * this.simiLimit;
        if(offset > this.SIM_OFFSET){
          return window.bus.$emit('flash-message',self.$_(self.strings.SCROLL_BOTTOM));
        }
        body.offset = offset;
      }
      if(this.page == 0) {
        this.SoldList=[];
        this.LeasedList=[];
        this.SaleList=[];
        this.RentList=[];
      }
      setLoaderVisibility('block');
      fetchData(url,{body},function(err,ret) {
        setLoaderVisibility('none');
        if (err && err.status == 403) {
          return RMSrv.dialogAlert(self.$_(self.strings.ERR_RETRY)) ;
        }else if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        }
        self.savePropList(ret);
      })
    },
    compareDifference(prop) {
      var self = this;
      if (typeof prop.bdrms == 'number' && typeof self.curProp.bdrms == 'number') {
        prop.bdrms_diff = prop.bdrms - self.curProp.bdrms
      }
      if (typeof prop.bthrms == 'number' && typeof self.curProp.bthrms == 'number') {
        prop.bthrms_diff = prop.bthrms - self.curProp.bthrms
      }
      if (typeof prop.gr == 'number' && typeof self.curProp.gr == 'number') {
         prop.gr_diff = prop.gr - self.curProp.gr
      }
      if (prop.lotsz_code == self.curProp.lotsz_code && typeof prop.depth == 'number' && typeof prop.front_ft == 'number' && typeof self.curProp.depth == 'number' && typeof self.curProp.front_ft == 'number') {
        prop.front_ft_diff = Math.round(prop.front_ft - self.curProp.front_ft);
        prop.depth_diff = Math.round(prop.depth - self.curProp.depth);
        prop.size_diff = parseInt(prop.front_ft * prop.depth - self.curProp.front_ft * self.curProp.depth);
      }
      if (typeof prop.sqft == 'number' && typeof self.curProp.sqft == 'number') {
        prop.sqft_diff = parseInt(prop.sqft - self.curProp.sqft)
        prop.sqft_diff_abs = Math.abs(parseInt(prop.sqft - self.curProp.sqft))
      } else if (/\-/.test(prop.sqft) && /\-/.test(self.curProp.sqft)) {
        prop.sqft_diff = parseInt(prop.sqft1+prop.sqft2 - self.curProp.sqft1-self.curProp.sqft2)/2
      } else if (typeof prop.sqft1 == 'number' && typeof prop.sqft2 == 'number' && typeof self.curProp.sqft == 'number') {
        prop.sqft_diff = parseInt(prop.sqft2 - self.curProp.sqft)
      } else if (typeof prop.sqft == 'number' && typeof self.curProp.sqft1 == 'number'&&typeof self.curProp.sqft2 == 'number') {
        prop.sqft_diff = parseInt(prop.sqft - self.curProp.sqft2)
      } else if (typeof prop.sqft1 == 'number' && typeof prop.sqft2 == 'number' && typeof self.curProp.sqft1 == 'number'&&typeof self.curProp.sqft2 == 'number') {
        prop.sqft_diff = (parseInt(prop.sqft1 - self.curProp.sqft1)+parseInt(prop.sqft2 - self.curProp.sqft2))/2
      }else if (typeof prop.rmSqft == 'number' && typeof self.curProp.rmSqft == 'number') {
        prop.rmSqft_diff = parseInt(prop.rmSqft - self.curProp.rmSqft)
        prop.rmSqft_diff_abs = Math.abs(parseInt(prop.rmSqft - self.curProp.rmSqft))
      } else if (/\-/.test(prop.rmSqft) && /\-/.test(self.curProp.rmSqft)) {
        prop.rmSqft_diff = parseInt(prop.rmSqft1+prop.rmSqft2 - self.curProp.rmSqft1-self.curProp.rmSqft2)/2
      } else if (typeof prop.rmSqft1 == 'number' && typeof prop.rmSqft2 == 'number' && typeof self.curProp.rmSqft == 'number') {
        prop.rmSqft_diff = parseInt(prop.rmSqft2 - self.curProp.rmSqft)
      } else if (typeof prop.rmSqft == 'number' && typeof self.curProp.rmSqft1 == 'number'&&typeof self.curProp.rmSqft2 == 'number') {
        prop.rmSqft_diff = parseInt(prop.rmSqft - self.curProp.rmSqft2)
      } else if (typeof prop.rmSqft1 == 'number' && typeof prop.rmSqft2 == 'number' && typeof self.curProp.rmSqft1 == 'number'&&typeof self.curProp.rmSqft2 == 'number') {
        prop.rmSqft_diff = (parseInt(prop.rmSqft1 - self.curProp.rmSqft1)+parseInt(prop.rmSqft2 - self.curProp.rmSqft2))/2
      }

      if (prop.st && prop.st == self.curProp.st && prop.prov == self.curProp.prov && prop.city == self.curProp.city) {
        prop.sameStreet = true;
      }
      if ((vars.fromMls || self.curProp.mlsid) && !prop.sameStreet && prop.cmty && prop.cmty == self.curProp.cmty && prop.prov == self.curProp.prov && prop.city == self.curProp.city) {
        prop.sameCmty = true;
      }
      return prop;
    },
    rmbdrmStr: function (prop) {
      if (prop.rmbdrm) {
        return prop.rmbdrm;
      }
      let tmp = prop.bdrms || prop.tbdrms;
      tmp += prop.br_plus?'+ '+prop.br_plus:''
      return tmp;
    },
    rmbthrmStr: function (prop) {
      return prop.rmbthrm || prop.tbthrms || prop.bthrms;
    },
    parseSqft(sqft){
      if (/\-/.test(sqft)) {
        return sqft;
      }
      if ('number' == typeof sqft) {
        sqft = ''+sqft;
      }
      if (/\./.test(sqft)) {
        return sqft.split('.')[0];
      }
      return sqft;
    },
    goBack() {
      if(vars.d) {
        document.location.href = vars.d;
      } else {
        window.history.back();
      }
    },
    getFavPropList(){
      let self = this;
      fetchData('/1.5/propFav/getGrpAndFavProps',{body:{id:self.propId}},function(err,ret) {
        if(ret?.ok){
          self.favProps = ret.items;
          self.currentGrp = ret.currentGrp;
          self.grps = ret.grps;
          self.showPropTable = true;
        } else if (ret?.msg == 'Not Found'){
          self.actionIsCMA = true;
          window.bus.$emit('toggleFav',self.curProp);
        } else{
          return RMSrv.dialogAlert(err || ret.e);
        }
      })
    },
    popupSelect(act){
      var self = this;
      if(self.action == act){
        self.reset();
      }else{
        self.action = act;
        self.getFavPropList();
      }
    },
    confirmVip: function (lang, optTip) {
      optTip = optTip?optTip:"Available only for Premium VIP user! Upgrade and get more advanced features.";
      var tip = this.$_(optTip);
      var later = this.$_('Later');
      var seemore = this.$_('SeeMore');
      function _doShowVip(idx) {
        var url = 'https://www.realmaster.ca/membership'
        if (idx+'' == '2') {
          RMSrv.showInBrowser(url);
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowVip, "VIP", [later, seemore]);
    },
    goCMA(){
      let url = `/1.5/htmltoimg/dottemplate?lang=${this.dispVar.lang}&showSign=true&&selModel=diff&nm=comparetable&ids=`
      var prop = this.selectedProp;
      if (prop) {
        if(typeof(prop) != "string") prop = prop.join(',')
        url += prop;
      }
      let cfg = {title:this.$_('RealMaster')};
      openContent(url,cfg)
    },
    commitSelected(){
      var self = this;
      self.showPropTable = false;
      if(self.action == 'cma'){
        this.goCMA()
      } else {
        if (self.selectedProp.length == 1 ){
          for (var p of self.favProps){
            if (p._id == self.selectedProp[0]){
              this.prop = p;
            }
          }
          RMSrv.showSMB('show');
        }else{
          RMSrv.showSMB('show','m-share-');
        }
      }
      self.shareList = self.selectedProp;
      window.bus.$emit('reset-sel-prop');
      self.selectedProp = [];
      self.action = '';
      self.favProps.forEach(p => {
        p.selected = false;
      })
    },
    closeSimilar(){
      window.rmCall(':ctx:'+JSON.stringify({fav:1}));
    },
    propCMAOrShareSel(act){
      this.action = act
      let choiceProp = []
      this.favProps = choiceProp.concat([this.curProp],this.similarProps);
      this.showPropTable = true;
    }
  },
};
initUrlVars();
var app = Vue.createApp(similar);
app.component('prop-item', propItem);
app.component('prop-fav-actions', propFavActions);
app.component('prop-sel-table', propSelTable);
app.component('flash-message', flashMessage);
app.component('share-dialog', shareDialog);
app.component('listing-share-desc', listingShareDesc);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#similar');