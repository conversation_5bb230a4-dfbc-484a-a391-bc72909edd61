;(function() {
  /*
  Usage: same as Vue2, bus.$on('sth',(val)=>{}); window.bus.$emit('sth',val)
  NOTE: only works at front end, don't work in node
  */
class EventEmitter{
  constructor(cfg) {
    this.cfg = cfg;
    this.obj = document.createElement("div");
  }
  // emit detail to channel
  $emit(channel=null,detail=null){
    if (!channel) {
      return console.warn('no channel');
    }
    channel += '';
    var event = new CustomEvent(channel,  {detail:detail});
    this.obj.dispatchEvent(event);
  }
  // add cb to channel when heard
  $on(channel,cb){
    if (!channel) {
      return console.warn('no channel');
    }
    if (!cb) {
      return console.warn('no cb');
    }
    channel += '';
    this.obj.addEventListener(channel, function(e) { cb(e.detail) });
  }
  $once(){
    console.error('Not suppoted $once!')
  }
  $off(channel,cb){
    if (!channel) {
      return console.warn('no channel');
    }
    this.obj.removeEventListener(channel,cb)
  }
}
if (!window.bus) {
  window.bus = new EventEmitter()
}
})();

