initUrlVars()
//get lat,lng from url, or use cached or real location
var lngLat = [-79.393086, 43.741119];
var mapbox= new Mapbox({
  zoom:15,
  navControl:true
});
    mapbox.init('map')

loc = []
if (vars.lat && vars.lng) {
  lngLat = [parseFloat(vars.lng),parseFloat(vars.lat)]
} else {
  //savedloc order is [lat,lng]
  if(loc = mapbox._getSavedGPSLoc()) {
    lngLat = [loc[1],loc[0]]
  }
}

var marker = new mapboxgl.Marker()
  .setLngLat(lngLat)
  .addTo(mapbox.map);
mapbox.map.flyTo({center:lngLat})

document.getElementById('switchToSatellite').addEventListener('click', function(){
  document.getElementById('switchToSatellite').style.display="none";
  document.getElementById('switchToMap').style.display="block";
  mapbox.map.setStyle(mapbox.views.satellite);
});

document.getElementById('switchToMap').addEventListener('click', function(){
  document.getElementById('switchToSatellite').style.display="block";
  document.getElementById('switchToMap').style.display="none";
  mapbox.map.setStyle(mapbox.views.street);
});
