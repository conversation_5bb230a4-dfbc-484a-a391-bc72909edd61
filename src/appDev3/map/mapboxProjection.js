function latlngToPoint(map, lnglat){
  var z = map.getZoom();
	var normalizedPoint = map.project(lnglat); // returns x,y normalized to 0~255
	// var scale = Math.pow(2, z);
	var scale = 1;
	var pixelCoordinate = new mapboxgl.Point(normalizedPoint.y * scale,normalizedPoint.x * scale);
	return pixelCoordinate; 
};
function pointToLatlng(map, point){
  var z = map.getZoom();
	// var scale = Math.pow(2, z);
	var scale = 1;
	var normalizedPoint = new mapboxgl.Point(point.y / scale,point.x / scale);
	var lnglat = map.unproject(normalizedPoint);
	return lnglat; 
};