if (window.key && key.mapboxKey) {
  mapboxgl.accessToken = key.mapboxKey
} else {
  console.error('No mapbox key found')
}
function Mapbox(mapParams) {
  mapParams = mapParams || {}
  this.map = null;
  this.geocoder = null
  this.Umarker = null;
  this.views = {
    street:'mapbox://styles/mapbox/streets-v11',
    satellite:'mapbox://styles/mapbox/satellite-v9'
  };
  this.init = (container) => {
    const center = mapParams.center||[-79.393086,43.741119];
    const zoom = 10;
    const opts = {
      style: mapParams.style||this.views.street,
      container:container,
      center: center,
      zoom: mapParams.zoom||zoom,
      zoomControl: mapParams.zoomControl||false,
      mapTypeControl: mapParams.mapTypeControl||false,
      scaleControl: mapParams.scaleControl||false,
      streetViewControl: mapParams.streetViewControl||false,
      rotateControl: false,
      gestureHandling: "greedy",
      disableDefaultUI: true,
      dragRotate:mapParams.dragRotate!=null?mapParams.dragRotate:true,
      touchZoomRotate:mapParams.touchZoomRotate!=null?mapParams.touchZoomRotate:true,
    };
    // console.log('+++++',opts);
    this.map = new mapboxgl.Map(opts);
    if(mapParams.locateMe) {
      this.map.addControl(
        new mapboxgl.GeolocateControl({
          positionOptions: {
            enableHighAccuracy: true
          },
          trackUserLocation: true
        })
      );
    };
    if (mapParams.navControl) {
      this.map.addControl(new mapboxgl.NavigationControl());
    }
  };
  this.onceResize = ()=>{
    this.map.once('idle', (e) => {
      // console.log(`once idle resize`);
      this.resize()
    });
  };
  // actions = ['resize']
  this.idle = (actions)=>{
    this.map.on('idle', () => {
      actions.forEach(element => {
        if(element == 'resize'){
          this.resize()
          // console.log('resized')
        }
      });
    });
  };
  this.resize = ()=> {
    this.map.resize()
  };
  this.updateStyle = () => {
    const curStyle = this.map.getStyle().sprite;
    if (curStyle.indexOf('street') > -1) {
      this.map.setStyle(this.views.satellite);
    } else {
      this.map.setStyle(this.views.street);
    }
  };
  this.updateZoom = (direction) => {
    if (direction == '+') {
      this.map.zoomIn();
    } else {
      this.map.zoomOut();
    }
  }
  this.markerDefaultClass = 'mapboxgl-marker mapboxgl-marker-anchor-center marker-label '

  this.getMarkerLabelClass = (p) => {
    const topLabelClass = p.isToplisting ? 'top' : '';
    var markerDefaultClass = this.markerDefaultClass;
    if (p.isCenter) {
      markerDefaultClass += ' selected ';
    }
    return markerDefaultClass + topLabelClass
  };
  this.renderBasicMarker = (params,onClicked) => {
    let position = params.position;
    if (!position) {
      return;
    }
    let className = params.className;
    let id = params.id;
    let innerHTML = params.innerHTML;
    let popupHTML = params.popupHTML
    let el = document.createElement('div');

    el.className = className? className : this.markerDefaultClass;
    if (id) {
      el.setAttribute('id', id);
    }
    if (innerHTML) {
      el.innerHTML = innerHTML;
    }
    if (onClicked) {
      el.addEventListener('click', (e) => {
        onClicked(e);
      });
    }
    let marker =  new mapboxgl.Marker({element:el, anchor:'bottom'})
    marker.setLngLat(position).addTo(this.map)

    if (popupHTML) {
      marker.setPopup(new mapboxgl.Popup({autoPan:true})
      .setHTML(popupHTML))
    }
    return marker
  };

  this.renderMarker = (p,popupHTML,select_cluster,select_listing,scope) => {
    let params = {
      position: [p.lng,p.lat],
      className: this.getMarkerLabelClass(p),
      id:p._id,
      innerHTML:p.marker_price,
      popupHTML
    }
    return this.renderBasicMarker(params,(e)=>{
      select_cluster();
      select_listing(e.target.getAttribute('id'))
      scope.$apply();
      e.stopPropagation();
    });
  };
  this.renderCondoMarker = (m,popupHTML,pos,select_cluster,select_listing,scope) => {
    const p = m[0]
    let params = {
      position: [p.lng,p.lat],
      className: this.getMarkerLabelClass(p),
      id:pos,
      innerHTML:'<span id="'+pos+'" class="marker-number">'+m.length+'</span>',
      popupHTML
    }
    return this.renderBasicMarker(params,(e)=>{
      select_cluster(e.target.getAttribute('id'));
      scope.$apply();
      e.stopPropagation();
    });
  };

  this._locating= function(doing) {
    if (mapParams.sendMsg) {
      return mapParams.sendMsg("locating", doing);
    }
  },
  this.setCenter = function(loc) {
    if(this.map) {
      this.map.flyTo({
        center: loc,
        zoom: 10
      });
    }
  },
  // loc = [lat,lng]
  this._showUmarker = function(loc) {
    loc = [loc[1],loc[0]]
    this._locating(false);
    //
    if (this.Umarker) {
      // NOTE: when map not loaded marker was created, set position not work
      this.Umarker.remove();
    }
    let params = {
      position: loc,
      className: 'mapboxgl-marker mapboxgl-marker-anchor-center marker-umarker ',
      id:'me'
    }
    this.Umarker = this.renderBasicMarker(params);
    this.setCenter(loc);
    return this._mapReady(loc)
  },
  this._mapReady=function(pos) {
    var pos1;
    pos1 = this.map.getCenter().wrap() || pos;
    if (mapParams.sendMsg) {
      return mapParams.sendMsg("locationReady", pos1);
    }
  },
  this.locateMe=(opt, cb)=> {
    var gpsLoc;
    this._locating(true);
    if (gpsLoc = this._getSavedGPSLoc()) {
      this._showUmarker(gpsLoc);
      if(cb && gpsLoc) {
        // 不能return 需要定位当前位置
        // 没有历史定位，定位当前位置并返回
        // 存在历史定位，先返回历史定位，再定位当前位置
        cb(gpsLoc)
      }
    }
    return this._realLocateMe(opt, cb);
  };
  this._realLocateMe=(opt, cb)=> {
    var failed, self, success;
    self = this;
    success = function(l) {
      var loc;
      loc = [];
      loc[0] = l.coords.latitude;
      loc[1] = l.coords.longitude;
      self._showUmarker(loc);
      // self.gmap.setZoom(14);
      localStorage.lastGPSLoc = '' + loc[0] + ',' + loc[1];
      if (cb) {
        return cb(loc)
      }
    };
    failed = function(e) {
      if (self.Umarker) {
        self.Umarker.remove();
      }
      self._locating(false);
      if (cb) {
        return cb()
      }
    };
    if (!opt || Object.keys(opt))  {
      opt = {
        enableHighAccuracy: true,
        timeout: 15000
      };
    }

    // pos = {coords:{latitude:43.8012491,longitude:-79.3359548}}
    // success(pos)
    RMSrv.getGeoPosition(opt,function (pos) {
      if (pos.err) {
        failed(pos.err);
      } else {
        success(pos)
      }
    });
  };
  this._getSavedGPSLoc = ()=> {
    var gpsLoc, s;
    if (gpsLoc = localStorage.lastGPSLoc) {
      return (function() {
        var i, len, ref, results;
        ref = gpsLoc.split(',');
        results = [];
        for (i = 0, len = ref.length; i < len; i++) {
          s = ref[i];
          results.push(parseFloat(s));
        }
        return results;
      })();
    }
    return null;
  };
  this.fitMarkers=function(markers) {
    var bounds = new mapboxgl.LngLatBounds();
    for (let marker of markers) {
      bounds.extend(marker.getLngLat());
    }
    // console.log(bounds)
    this.map.fitBounds(bounds,{padding:80});
  },
  this.parseGeoResult = function(responses) {
    if (!responses && !responses.result) {
      return {};
    }
    ret = {};
    r = responses.result
    ret.address = r.place_name;

    ret.lat = r.center[1]
    ret.lng = r.center[0]
    // console.log(responses[0]);
    ret.place_id = r.id;
    ret.addr = ''
    if(r.address) {
      ret.st_num = r.address
      ret.addr = ret.st_num
    }
    if(r.text) {
      ret.st = r.text
      ret.addr = ret.addr + ' ' + ret.st
    }
    if(r.context) {
      for(let cmp of r.context) {
        if (id = cmp.id) {
          if(id.indexOf('postcode') >= 0){
            ret.zip = cmp.text
          }
          if (id.indexOf('region') >= 0) {
            ret.prov = cmp.text
          }
          if (id.indexOf('place') >= 0) {
            ret.city = cmp.text
          }
          if (id.indexOf('country') >= 0) {
            ret.cnty = cmp.text
          }
        }

      }
    }
    return ret;
  };
  this.initAutocomplete = function(id,cb) {
    let zoom = 15
    if(localStorage.mapZoom) {
      zoom = parseInt(localStorage.mapZoom)
    }
    let geocoder = new MapboxGeocoder({
        accessToken: mapboxgl.accessToken,
        countries:'CA',
        limit:3,
        types:'address',
        marker: false,
        mapboxgl: mapboxgl,
        language: 'en',
        zoom:zoom
    });

    let el = document.getElementById(id)
    let self = this;
    // console.log(el)
    if (el) {
      el.appendChild(geocoder.onAdd(this.map));
      geocoder.on('result',function(ret){
        // console.log('onresult')
        let addrObj = self.parseGeoResult(ret);
        if(cb) {
          cb(addrObj);
        }
      });
      return geocoder;
    } else {
      console.error('element:'+id+'is not found');
      return null;
    }

  }
}
