var selectMap = {
  data() {
    return {
      prop:{},
      isPopup:false,
      d:'',
      showMapList:false,
      dispVar: {
        coreVer:'',
        exMapURL:'',
        isApp:true
      },
      datas: [
        'coreVer',
        'exMapURL',
        'isApp'
      ],
    }
  },
  mounted() {
    var bus = window.bus, self = this;
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.checkAndOpen()
    });
    this.prop = decodeJsonString2Obj('prop');
    this.isPopup = document.querySelector('#isPopup').innerHTML;
    this.d = document.querySelector('#d').innerHTML;
  },
  methods: {
    checkAndOpen(){
      var self = this;
      if(self.isNewerVer(self.dispVar.coreVer,'6.2.8')&& RMSrv.appInstalledChecker){
        RMSrv.appInstalled<PERSON>hecker('google maps',(ret)=>{
          if(ret){
            return self.chooseMap('google')
          }
        })
      }
      if(RMSrv.isIOS()){
        self.showMapList = true;
      }else{
        self.chooseMap()
      }
    },
    chooseMap(map=''){
      let self = this,url = self.dispVar.exMapURL;
      map = map.toLowerCase();
      if(map == 'google'){
        url = url.replace('apple','google');
      }
      self.exMap(self.prop,url);
      if(self.isPopup){
        setTimeout(() => {
          self.goBack();
        }, 1000);
      }
    },
    exMap (prop,url='') {
      if (!this.dispVar.isApp && this.isPopup) {
        document.location.href='/adPage/needAPP';
      }
      var addr;
      if (prop._id){
        if (prop.useMlatlng || (prop.daddr === "N" && prop.lat && prop.lng)) {
          addr = prop.lat + ',' + prop.lng;
        } else{
          addr = (prop.city_en || prop.city || '') + ', ' + (prop.prov_en || prop.prov || '') + ', ' + (prop.cnty_en || prop.cnty || '');
          if (prop.daddr !== "N") {
            addr = (prop.addr || '')+ ", " + addr;
          } else {
            addr = addr + ', ' + prop.zip;
          }
        }
      }else{
        addr = prop.lat + ',' + prop.lng;
      }
      url = url || this.dispVar.exMapURL;
      url = url + encodeURIComponent(addr);
      if (this.isPopup) {
        return RMSrv.showInBrowser(url);
      }
      let a = document.createElement('a');
      a.target = '_blank';
      a.href = url;
      a.click();
    },
    goBack(){
      if (this.isPopup) {
        return window.rmCall(':ctx::cancel');
      }
      if (this.d) {
        return window.location = this.d;
      }
      window.history.back();
    },
  }
};
var app = Vue.createApp(selectMap);
app.mixin(pageDataMixins);
app.mount('#selectMap');