/* used for commute. has two markers. from and to.
*/

var mapboxTransitService = {
  layerIds = [],
  removeRoute (map) {
   for (let layer of this.layerIds) {
     if (map.getLayer(layer)) {
       map.removeLayer(layer);
       map.removeSource(layer);
     }
   }
  },
  //legs is part of mapbox result
  showRouteOnMap(map, legs,colorSetp) {
   let legColor = 0;
   let self = this;
   // const colorSetp= (255-0)/this.showing.props.length

   for (const [index,leg] of legs.entries()) {
     const steps = leg.steps;
     legColor = legColor + colorSetp;

     let source = {
       'type': 'geojson',
       'data': {
         'type': 'FeatureCollection',
         'features': []
       },
     }
     for (let step of steps) {
       source.data.features.push(
         {
           type:'featue',
           geometry:step.geo
         });
     }
     map.addLayer({
       'id': leg.id,
       'type': 'line',
       'source': source,
       'layout': {
         'line-join': 'round',
         'line-cap': 'round'
       },
       'paint': {
         'line-color': 'rgb(0,0,'+legColor+')',
         'line-width': 3
         }
     });
     map.on('click', leg.summary, function(e) {
       let lineWidth = self.mapbox.map.getPaintProperty(leg.id, 'line-width');
       let weight = lineWidth==3? 8:3
       map.setPaintProperty(leg.summary, 'line-width', weight);
     });
   }
  },
  // pointStr=''
  getRoute(mapboxKey,pointStr,travelMode='driving',cb) {
    //f, t
    //if data changed, remove layer and compute again, else return.
    // index change or lat change or prop num change recompute.
    // let url = "https://api.mapbox.com/directions/v5/mapbox/driving-traffic/"
    travelMode = travelMode.toLowerCase()
    if (travelMode == 'bicycling') {
      travelMode='cycling'
    }
    let url = "https://api.mapbox.com/directions/v5/mapbox/"+travelMode+"/";
    //clear route on map
    this.removeRoute();
    url=url+encodeURIComponent(pointStr);
    // TODO:get token from server
    url = url +'.json?steps=true&access_token='+mapboxKey+'&geometries=geojson&overview=full'
    //different blue
    $.get(url,function (ret, status) {
      if (ret && ret.routes) {
        console.log(ret.routes)
        if(cb) {
          cb(ret.routes[0].legs)
        }
        this.showRouteOnMap(ret.routes[0].legs);
      }
      else {
        console.error( "server-error" );
      }
    });
  },
}
export default mixins;
