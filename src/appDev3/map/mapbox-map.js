initUrlVars()
//get lat,lng from url, or use cached or real location
var lngLat = [-79.393086, 43.741119];
var mapbox= new Mapbox({navControl:true});
    mapbox.init('map')
var hasGeoResult = false;
let autocomplete = mapbox.initAutocomplete('address-input',function(addr) {
  marker.setLngLat([parseFloat(addr.lng),parseFloat(addr.lat)])
  addrObj = addr
  hasGeoResult = true;
  showElementById('clearIcon')
  showElementById('confirmBtn')
  showElementById('editBtn')

});


var marker = new mapboxgl.Marker({
  draggable: true
})
  .setLngLat(lngLat)
  .addTo(mapbox.map);


  marker.on('dragend',onDragEnd);


if (vars.lat && vars.lng) {
  loc = [parseFloat(vars.lng),parseFloat(vars.lat)]
  marker.setLngLat(loc);
  mapbox.map.flyTo({center:loc})
  if (vars.addr) {
    autocomplete.setInput(vars.addr);
    document.getElementById('clearIcon').style.display='block';
    // document.getElementById('clearIcon').style.right='5px';
  }
} else {
  //savedloc order is [lat,lng]
  if(loc = mapbox._getSavedGPSLoc()) {
    loc = [loc[1],loc[0]]
    marker.setLngLat(loc)
    mapbox.map.flyTo({center:loc})
  }
}


function onDragEnd() {
  hideInputBox()
  lngLat = marker.getLngLat();
  mapbox.map.flyTo({ center: lngLat });
  autocomplete.query(lngLat.lng+','+lngLat.lat);
}

addrObj = {}
var cacher = new Cacher();

document.getElementById('confirmBtn').addEventListener('click', confirmLoc);
document.getElementById('sidePane').addEventListener('click', locateMe);
document.getElementById('editBtn').addEventListener('click', editLoc);


document.getElementById('body').addEventListener('click', function(target){
  if (!event.target.matches('.mapboxgl-ctrl-geocoder--input')) {
    clearHist();
  }
});

function locateMe() {
  mapbox.locateMe({}, function(loc){
    // alert(loc)
    autocomplete.query(loc[1]+','+loc[0]);
  });
}
function clearInput() {
  hasGeoResult = false;
  autocomplete.clear();
  hideElementById('clearIcon');
  hideElementById('confirmBtn');
  hideElementById('editBtn');
}
//getHist:get addrHist in localstorage, loop as html element, append in addrHist
function getHist() {
  // if (!document.getElementById('addrHist').innerHTML || !document.getElementById('addrHist').innerHTML.trim()) {
  document.getElementById('addrHist').innerHTML='';
  var addrHistArray =cacher.getCachedSet('addrHist');
  if (addrHistArray) {
    let html = '';
    let ul = document.getElementById('addrHist');
    for (let hist of addrHistArray) {
      if (hist) {
        faddr = getFullAddress(hist)
        html = html +"<li class='' onClick='selectAddr("+hist+")'>"+faddr+"</li>"
        var li = document.createElement('li');
        var text = document.createTextNode(faddr);
        // add the text node to the newly created div
        li.appendChild(text);
        li.class = "table-view-cell"
        li.addEventListener('click', function(){
            selectAddr(hist);
        });
        ul.appendChild(li)
        }
      }
    }
  // } else {
  //   clearHist();
  // }
}
function getInputVaule() {
  let ele = document.querySelector('.mapboxgl-ctrl-geocoder--input');
  if (ele) {
    return ele.value;
  } else {
    return '';
  }
}
function inputKeyUp() {
  hasGeoResult = false;
  if(getInputVaule) {
    showElementById('clearIcon');
    showElementById('confirmBtn');
    showElementById('editBtn');
  } else {
    hideElementById('clearIcon');
    hideElementById('confirmBtn');
    hideElementById('editBtn');

  }
  clearHist();
}
function showElementById(id) {
  document.getElementById(id).style.display='block';
}
function hideElementById(id) {
  document.getElementById(id).style.display='none';
}
function clearHist() {
  document.getElementById('addrHist').innerHTML = '';
}
function getFullAddress(hist) {
  if(hist.address){
    return hist.address
  } else {
    return hist.addr || (hist.st_num + ' ' + hist.st) + ',' + hist.city + ',' + hist.prov+ ',' + hist.cnty
  }
}
//hist should has addr and lat,lng.
function selectAddr(hist) {
  addrObj = hist;
  let loc = [parseFloat(hist.lng),parseFloat(hist.lat)]
  marker.setLngLat(loc);
  autocomplete.setInput(hist.address)
  mapbox.map.flyTo({center:loc})
  showElementById('clearIcon')
  showElementById('confirmBtn')
  showElementById('editBtn')

  clearHist();
}
function hideInputBox() {
  $('.addr-manual-box').removeClass("active");
}
addressFld = ['addr','cnty','prov','zip','city']
function confirmInput() {
  //read addr, city, prov, city, zip from input.
  for(fld of addressFld){
    addrObj[fld] = $('#'+fld+'Input').val().trim();
    $('#'+fld+'Input + .fa').removeClass("show");
    if (!addrObj[fld] && fld!='zip') {
      $('#'+fld+'Input + .fa').addClass("show");
      $('.flash-message-box').addClass('block')
      setTimeout(()=>{
        $('.flash-message-box').removeClass('block')
      },2000)
      return;
    }
  }
  addrObj.address = addrObj.addr +',' + addrObj.city + ',' +addrObj.prov+' '+addrObj.zip+','+addrObj.cnty
  saveLoc()
  $('.addr-manual-box').removeClass("active");
}
function saveLoc() {
  isSubCity = isSubCityAndGetParentCity(addrObj.city)
  if(isSubCity.result){
    addrObj.subCity = addrObj.city.trim() // 这两行需要按照这个顺序赋值
    addrObj.city = isSubCity.city
    addrArray = addrObj.address.split(',')
    addrArray[1] = ' ' + addrObj.city
    addrObj.address = addrArray.join(',')
  }
  var ret = JSON.stringify(addrObj);
  //save to localStorage,
  cacher.setPush('addrHist', addrObj, 100)
  window.rmCall(':ctx:' + ret);
  trackEventOnGoogle('showing','confirmStartLocation')
}
function confirmLoc() {
  // if no addrObj from autocomplet, get it from input and marker
  //提示使用的是手动地址，确认。如果不合格式要求重输入。
  if (hasGeoResult) {
    saveLoc()
  } else {
    editLoc()
  }
}
function editLoc() {
  addrObj = {}
  let address = getInputVaule()
  if (marker && address) {
    lngLat = marker.getLngLat();
    addrObj.address = address;
    addrObj.lat = lngLat.lat
    addrObj.lng = lngLat.lng
    addrArray = address.split(',')
    // addrObj.cnty = 'Canada'
    addrObj.addr = addrArray[0]
    isSubCity = isSubCityAndGetParentCity(addrArray[1])
    if(isSubCity.result){
      addrObj.city = ' ' + isSubCity.city
      addrObj.subCity = addrArray[1].trim()
    }else{
      addrObj.city = addrArray[1]
    }
    addrObj.prov = addrArray[2]
    if (addrObj.prov) {
      zipRegex = /[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/
      if (zipRegex.test(addrObj.prov)) {
        zip = addrObj.prov.substr(-7)
        //zip has space
        if (zipRegex.test(zip)) {
          addrObj.zip = zip;
          addrObj.prov = addrObj.prov.substr(0, addrObj.prov.length-7);
        } else {
          //zip no space
          zip = addrObj.prov.substr(-6);
          if (zipRegex.test(zip)) {
            addrObj.zip = zip;
            addrObj.prov = addrObj.prov.substr(0, addrObj.prov.length-6);
          }
        }
      }
    }

    addrObj.cnty = addrArray[3]

    for(fld of addressFld){
      $('#'+fld+'Input').val(addrObj[fld])
      $('#'+fld+'Input + .fa').removeClass("show");
      if (!addrObj[fld] && fld!='zip') {
        $('#'+fld+'Input + .fa').addClass("show");
      }
    }
    $('.addr-manual-box').addClass("active");
  } else {
    return;
  }
}
if(window.gtmjs){
  gtmjs({tags:{js:new Date()}})
}

document.querySelector('.mapboxgl-ctrl-geocoder--input').addEventListener('click', getHist);
// change not working, keydown works
document.querySelector('.mapboxgl-ctrl-geocoder--input').addEventListener('keyup', inputKeyUp);
document.querySelector('.mapboxgl-ctrl-geocoder--input').placeholder=strings.searchPlaceHolder;
