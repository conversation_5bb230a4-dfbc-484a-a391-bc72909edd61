var buyListModule = {
  data() {
    return {
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loading: false,
      maxLog : 100,
      buylist:[],
      limit:20,
      dispVar:{
        lang:'zh-cn',
        isCip:false,
        isApp:false,
      },
      crm:{},
      role:'client',
      scrollElement:'',
      datas:[
        'lang',
        'isCip',
        'allowedEditGrpName',
        'isRealtor',
        'isApp',
      ],
      rcmdHeight: 0
    }
  },
  beforeMounted(){
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.rcmdHeight = parseInt(window.innerWidth/1.6);
    self.role = document.querySelector('#role').innerHTML || 'client';
    self.getBuylist();
    self.scrollElement = document.querySelector('.prop-list-container')
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if(vars.id){
        self.openShowingDetail(vars.id)
      }
    });
    bus.$on('open-showing',(prop)=>{
      self.openShowingDetail(prop.showing._id)
    })
    bus.$on('remove',(prop)=>{
      self.remove(prop)
    })
  },
  methods: {
    goback(){
      if(this.role == 'owner'){
        return location.href = '/1.5/showing';
      }
      return location.href = '/1.5/index';
    },
    chooseClnt(){
      this.showCrm = true;
      var cfg = {hide:false,title:this.$_('Contacts','contactCrm')},self = this;
      var url = RMSrv.appendDomain('/1.5/crm?noBar=1&needEml=1&isPopup=1');
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          self.crm = JSON.parse(val);
          self.page = 0;
          self.getBuylist()
          document.querySelector('.prop-list').scrollIntoView(true);
        } catch (e) {
          console.error(e);
        }
      });
    },
    openShowingDetail(showingId){
      var url = '/1.5/showing/detail/share?showingId='+showingId+'&isPopup=1&d=/1.5/showing/buylist'+'&lang='+this.dispVar.lang
      if(this.role == 'owner'){
        url = '/1.5/showing/detail?showingId='+showingId+'&d=/1.5/showing/buylist'
      }
      if (this.dispVar && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', {toolbar:false}, function(val){
          if (val == ':cancel') {
            console.log('canceled');
            return;
          }
        });
      } else {
        window.location.href = url;
      }
    },
    deleteFilter(){
      this.crm = {};
      this.page = 0
      this.getBuylist();
      document.querySelector('.prop-list').scrollIntoView(true);
    },
    showDelete(prop){
      prop.del = true
    },
    remove (prop) {
      var self = this;
      var url = '/1.5/showing/toggleBuylist';
      var body = {id:prop.showing._id,add:false,pid:prop._id};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        }
        let i = -1,buylist = self.buylist;
        buylist.forEach((b,index)=>{
          b.del = false;
          if((b._id == prop._id) && (b.showing._id == prop.showing._id)){
            i = index
          }
        })
        self.buylist.splice(i,1)
      })
    },
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page += 1;
              self.getBuylist();
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getBuylist:function() {
      var self = this;
      var url = '/1.5/showing/buylist/'+this.role;
      var body = {page:this.page,limit:this.limit};
      if(this.crm._id){
        body.clnt = this.crm._id
      }
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        }
        if (ret.more) {
          self.hasMoreProps = true;
        } else {
          self.hasMoreProps = false;
        }
        if (self.page == 0) {
          self.buylist = ret.list
        } else {
          self.buylist = self.buylist.concat(ret.list);
        }
      })
    },
  },
};
initUrlVars();
var app = Vue.createApp(buyListModule);
app.component('prop-item', propItem);
app.component('flash-message', flashMessage);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#buylist');