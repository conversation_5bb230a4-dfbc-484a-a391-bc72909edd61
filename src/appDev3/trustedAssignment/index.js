var trustedAssigm = {
  data(){
    return{
      propList:[],
      rcmdHeight:0,
      page:0,
      scrollElement:'',
      waiting:false,
      hasMoreProps:false,
      loading:false,
      LIMIT:20,
      dispVar:{
        isLoggedIn:  false,
        lang:'zh-cn',
        isCip:false,
        userCity: {o:'Toronto',n:'多伦多'}
      },
      datas:[
        'lang',
        'isCip',
        'allowedEditGrpName',
        'isLoggedIn',
        'userCity'
      ],
      sortCondition: 'sortOrder-desc',
      selCity: 'City',
      cityList: [],
      selStyle: 'Style',
      ptype2s: [],
      showStyle: false,
      showCity: false
    }
  },
  mounted(){
    let self = this;
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    self.$getTranslate(self);
    self.getPtype2sAndCity();
    self.getPageData(self.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    self.getList();
    self.rcmdHeight = parseInt(window.innerWidth/1.6);
    self.scrollElement = document.getElementById('trustedAssigm');
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    if(vars.id){
      var propUrl = '/1.5/prop/detail/inapp?id='+vars.id;
      setTimeout(()=>{
        openPopup(propUrl,self.$_('RealMaster'));
      },100);
    }
  },
  methods:{
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page++;
              self.getList('more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    goBack(){
      window.rmCall(':ctx::cancel');
    },
    getList(more){
      if(!more){
        this.propList = [];
        this.page = 0;
      }
      setLoaderVisibility('block');
      let obj = {
        ltp: "assignment",
        page: this.page,
        ptype: "Assignment",
        sort: this.sortCondition,
        src: "rm",
        rmProp: true,
        limit:this.LIMIT
      }
      if(this.selCity != 'City'){
        obj.city = this.selCity;
      }
      if(this.selStyle != 'Style'){
        obj.ptype2 = this.selStyle;
      }
      fetchData('/1.5/props/search',{body:obj},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok) {
          return bus.$emit('flash-message','Error');
        }
        if(more){
          this.propList = this.propList.concat(ret.items);
        }else{
          this.propList = ret.items;
        }
        if(ret.cnt > this.propList.length){
          this.hasMoreProps = true;
        }else{
          this.hasMoreProps = false;
        }
      });
    },
    selectCity(city){
      this.selCity = city.isProv? 'City' : city.o
      this.page = 0;
      this.getList();
      this.showCity = false;
    },
    getPtype2sAndCity(){
      let self = this;
      let obj = {
        ptype: "Assignment"
      }
      fetchData('/truAssigm/cityAndPtype2List',{body:obj},(err,ret)=>{
        if (!ret.ok) {
          return
        }
        self.ptype2s = ret.result.ptype2s;
        self.cityList = ret.result.city;
      });
    },
    selectPypte2s(ptp2){
      this.selStyle = ptp2;
      this.page = 0;
      this.getList();
      this.showStyle = false;
    },
    showSelectOprion(name,stateOri){
      this.showStyle = false;
      this.showCity = false;
      if(name) this[name] = !stateOri;
    }
  }
}
initUrlVars();
var app = Vue.createApp(trustedAssigm);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('prop-item', propItem);
app.component('prop-fav-actions', propFavActions);
app.component('flash-message', flashMessage);
app.mount('#trustedAssigm');