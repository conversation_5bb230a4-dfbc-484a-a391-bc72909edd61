/***
 * select Language: 选择语言，返回选中语言
 * closeLangTemplate: 删除模块
 * createLangTemplate: 生成语言选择模块
 */
var LANGUAGE_OBJ = [
  {
    lang:'en',
    nm:'English'
  },
  {
    lang:'zh-cn',
    nm:'简体中文'
  },
  {
    lang:'zh',
    nm:'繁体中文'
  },
  {
    lang:'kr',
    nm:'한국어'
  },
]

function createLangTemplate(lang){
  var template = `
  <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">
    <div class="language-box"
      style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">
      ${LANGUAGE_OBJ.map(item=>{
        return `
        <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${lang == item.lang?'rgba(130,130,130,0.6)':'transparent'}" onClick="selectLang('${item.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">
          ${item.nm}
        </div>
        `
      }).join("")}
    </div>
  </div>
  `
  var div = document.createElement('div');
  div.innerHTML = template;
  div.id = 'langSelectBox';
  document.body.appendChild(div);
}

function closeLangTemplate(){
  var langSelectBox = document.getElementById('langSelectBox');
  document.body.removeChild(langSelectBox);
}
function selectLang(lang){
  closeLangTemplate()
  setLang(lang)
}