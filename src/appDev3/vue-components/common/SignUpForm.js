var SignUpForm = {
  components:{
    PageSpinner
  },
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {}
      }
    },
    target:{
      type:Object,
      default:function () {
        return {}
      }
    },
    needWxid:{
      type:<PERSON>olean,
      default:false,
    },
    cstyle:{
      type:Object,
      default:function () {
        return {}
      },
    },
    owner:{
      type:Object,
      default:function () {
        return {vip:false};
      }
    },
    userForm:{
      type:Object
    },
    isWeb:{
      type:Boolean,
      default:false
    },
    title:{
      type:String,
      default:''
    },
    feedurl:{
      type:String,
      default:'/chat/api/feedback'
    },
    mblNotRequired:{
      type:<PERSON><PERSON><PERSON>,
      default:false
    },
    mRequired:{
      type:Boolean,
      default:false
    }
  },
  data () {
    return {
      nmErr:    false,
      emlErr:   false,
      mblErr:   false,
      mErr:   false,
      sending:  false,
      message:  null,
      picUrls:  this.$parent.picUrls || []
    };
  },
  // watch:{
  //   'userForm.nm':function (val,oldVal) {
  //       console.log(val);
  //   }
  // },
  mounted(){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    bus.$on('reset-signup', function (d) {
      var msg;
      self.message = null;
      var msg = document.querySelector('#signUpSuccess');
      var form = document.querySelector('#signUpForm');
      if (form && self.owner.vip) {
        form.style.display = 'block';
      }
      if (msg) {
        msg.style.display = 'none';
      }
      if(/report/i.test(self.userForm.m) == false){
        var addr = self.formatedAddr || self.$parent.formatedAddr || '';
        self.userForm.m = 'I want to book an appointment to view: '+addr;
      }
    });
  },
  methods: {
    // updateForm(fld){
    //   console.log(this.userForm[fld]);
    // },
    signUp(){
      var regex_email, self = this;
      self.nmErr = false;
      self.emlErr = false;
      self.mblErr = false;
      self.mErr = false;
      self.message = null;
      let msgSuccess = document.querySelector('#signUpSuccess');
      let msgFail = document.querySelector('#signUpFail');
      let signUpForm = document.querySelector('#signUpForm');
      msgSuccess.style.display = 'none'
      msgFail.style.display = 'none'
      msgSuccess.style.height = signUpForm.clientHeight+'px';
      msgFail.style.height = signUpForm.clientHeight+'px';
      signUpForm.style.display = 'none'
      if (!self.userForm) {
        self.nmErr = true;
        self.emlErr = true;
        self.mblErr = true;
        return;
      }
      let requiedFld = ['nm','mbl'];
      if (this.mblNotRequired) {
        requiedFld = ['nm']
      }
      if (this.mRequired) {
        requiedFld.push('m')
      }
      for (let i of requiedFld) {
        if (!self.userForm[i]) {
          self[i+'Err'] = true;

        }
      }
      if (self.userForm.eml) {
        regex_email = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,26}$/i;
        if (!regex_email.test(self.userForm.eml)) {
          return self.emlErr = true;
        }
      }
      if (self.nmErr || self.emlErr || self.mblErr || self.mErr ) {
        return;
      }
      if (self.sending) {
        return;
      }
      self.userForm.img = self.userForm.img || self.$parent.shareImage || null;
      // return console.log(self.userForm)
      self.sending = true;
      fetchData(self.feedurl,{body:self.userForm},(err,ret)=>{
        self.sending = false;
        if (err || !ret.ok) {
          msgFail.style.display = 'flex';
          return;
        }
        signUpForm.style.display = 'none';
        if (ret.msg) {
          self.message = ret.msg;
        }
        msgSuccess.style.display = 'flex';
      });
    },
    closeForm() {
      toggleModal('SignupModal');
      window.bus.$emit('reset-signup',null);
      var backdrop = document.getElementById('backdrop');
      if (backdrop) {
        backdrop.style.backgroundColor = null;
        backdrop.style.display = 'none';
        backdrop.style.top = null;
        backdrop.style.height = null;
      }
      // this.$parent.toggleModal('SignupModal');
      let msgSuccess = document.querySelector('#signUpSuccess');
      let msgFail = document.querySelector('#signUpFail');
      msgSuccess.style.display = 'none';
      msgFail.style.display = 'none';
      // this.showContact=false;
      this.mErr = false;
    },
  },
  template: `
  <div>
    <div class="to-user" v-show="target.eml">
        <div class="color-bg">
            <div class="avt"><img :src="target.avt || '/img/noPic.png'" />
                <div class="fa fa-vip"></div>
            </div>
            <div class="nm">
                <div>{{target.fnm}}</div>
                <div class="cpny">
                  <span v-show="dispVar.lang == 'en'">{{target.cpny_en}}</span>
                  <span v-show="dispVar.lang !== 'en'">{{target.cpny_zh}}</span>
                </div>
            </div>
            <div class="contact">
                <a :href="'mailto:'+target.eml">
                  <span class="fa fa-envelope"></span>
                </a>
                <a :href="'tel:'+target.mbl">
                  <span class="fa fa-phone"></span>
                </a>
              </div>
        </div>
        <div class="itr">{{target.itr}}</div>
    </div>
    <div class="tl">
      <span v-if="title">{{title}}</span>
      <span v-else>{{$_('Contact Me')}}</span>
      <span class="icon icon-close" style="font-weight:normal;" @click="closeForm()"></span>
    </div>
    <div id="signUpSuccess" :style="cstyle">
      <i class="fa fa-check-circle"></i>
      <div class="thankyou">{{ $_('Thank You')}}</div>
      <div class="msg">{{message || $_('Your feedback has been submitted.')}}</div>
    </div>
    <div id="signUpFail" :style="cstyle">
      <i class="fa fa-exclamation-circle" style="color:#FFCD00"></i>
      <div class="thankyou">{{ $_('Submission Failed')}}</div>
      <div class="msg">{{message || $_('Please try again later.')}}</div>
    </div>
    <form id="signUpForm" :class="{'visible':owner.vip, 'web':isWeb}" :style="cstyle">
        <page-spinner :loading.sync="sending"></page-spinner>
        <div>
          <label>
            <span class="tp">{{$_('Name','signUpForm')}}</span>
            <span class="ast">*</span>
          </label>
          <input class="nm" type="text" placeholder="" v-model="userForm.nm" :class="{'error':nmErr}" />
        </div>
        <div>
          <label>
            <span class="tp">{{$_('Mobile','signUpForm')}}</span>
            <span class="ast" v-if="!mblNotRequired">*</span>
          </label>
          <input class="mbl" type="number" v-model="userForm.mbl" :class="{'error':mblErr}" />
        </div>
        <div>
          <label>
            <span class="tp">{{$_('Email','signUpForm')}}</span>
          </label>
          <input class="eml" type="email" v-model="userForm.eml" :class="{'error':emlErr}" />
        </div>
        <div v-if="needWxid">
          <label>
            <span class="tp">{{$_('WeChat ID','signUpForm')}}</span>
          </label>
          <input class="wxid" type="text" v-model="userForm.wxid" />
        </div>
        <div>
          <label>
            <span class="tp">{{$_('Message','signUpForm')}}</span>
            <span class="ast" v-if="mRequired">*</span>
          </label>
          <textarea class="m" rows="3" v-model="userForm.m" :class="{'error':mErr}"></textarea>
        </div>
        <div>
          <button class="btn btn-block btn-signup" type="button" @click="signUp()">{{$_('Submit','signUpForm')}}</button>
        </div>
    </form>
</div>
  `
};