var CitySelectModal = {
  components:{flashMessage},
  mixins:[pageDataMixins],
  props:{
    needLoc:{
      type:Boolean,
      default:false
    },
    showProvBar:{
      type:Boolean,
      default:false
    },
    curCity:{
      type:Object,
      default:function() {
        return {o:'Toronto',n:'多伦多'}
      }
    },
    nobar:{
      type:Boolean,
      default:false,
    },
    showSubscribe:{
      type:Boolean,
      default:true,
    },
    lang:{
      type:String,
    }
  },
  computed:{
    computedFavCities:function (){
      if (!this.filter) {
        return this.favCities;
      }
      var self = this;
      var favs = self.favCities;
      return favs.filter(this.filterFn);
      // return filterBy filter in 'o' 'n'
    }
  },
  data () {
    return {
      setCurCity:false,
      filter:'',
      prov:'CA',
      loading:false,
      provs:[],
      extCities:[],
      extCitiesCp:[],
      favCities:[],
      favCitiesCp:[],
      userCities:[],
      histCities:[],
      dispVar:{
        lang:'en'
      },
      datas:['lang']
    };
  },
  mounted () {
    this.getProvs();
    this.getUsercities();
    var bus = window.bus, self = this;
    if(!this.lang){
      this.getPageData(this.datas,{},true);
    }else{
      this.dispVar.lang = this.lang;
    }
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    bus.$on('select-city', function (opts={}) {
      self.doSearch = opts.doSearch?true:false;
      if (self.favCities && self.favCities.length) {
        return toggleModal('citySelectModal');
      }
      if (!opts.noloading) {
        self.loading = true;
      }
      fetchData('/1.5/props/cities.json', {body:{loc:self.needLoc}},function(err,data) {
        if (data.ok) {
          self.favCities = self.parseCityList(data.fc);
          if (data.cl) {
            self.extCitiesCp = data.cl.slice();
          }
          self.favCitiesCp = self.favCities.slice();
          self.loading = false;
          toggleModal('citySelectModal');
        }
      });
      var params =  {
        e: 'open',
        type:"CitySelect"
      };
      window.bus.$emit('track-log-event',params);
    });
    if (localStorage.histCities) {
      this.histCities = JSON.parse(localStorage.histCities);
    }
  },
  watch:{
    'filter':function (newVal, oldVal) {
      // console.log(newVal);
      if (newVal && newVal.length > 0 && this.extCitiesCp.length) {
        // var list = this.$eval("extCitiesCp | filterBy filter in 'o' 'n'");
        var list = this.extCitiesCp.filter(this.filterFn);
        this.extCities = this.formatCityList(list, {hide:false});
      } else if (newVal && newVal.length == 0) {
        this.extCities = this.formatCityList(this.extCitiesCp, {hide:false});
      }
    }
  },
  methods: {
    getCityidx(city) {
      if (!this.userCities || !this.userCities.length) {
        return -1;
      }
      let cityidx = this.userCities.findIndex(function(d) {
        return d.o==city.o;
      })
      return cityidx;
    },
    unSubscribeCity(city,cityIdx){
      if (!cityIdx){
        cityIdx= this.getCityidx(city);
      }
      trackEventOnGoogle('citySelectModal','unsubscribe');
      var url = '/1.5/index/subscribe', self = this;
      fetchData(url, {body:{mode:'unsubscribe', city:city}},function (err,ret) {
          console.log(ret,err)
          if (ret.ok) {
            self.userCities.splice(cityIdx,1);
            self.unSubscribe = true;
            // if (ret.cnt != null) {
            //   self.userCityCnt = ret.cnt;
            // } else {
            //   self.userCityCnt -= 1;
            // }
            if (ret.msg) {
               window.bus.$emit('flash-message', ret.msg);
            }
            // self.cityIdx = 0;
            // self.getNextCityRcmd();
          } else {
            if(ret.e=='Need login') {
              document.location.href='/1.5/user/login'
            } else {
              window.bus.$emit('flash-message', ret.e);
            }
          }
        }
      );
    },
    subscribeCity(d) {
      trackEventOnGoogle('citySelectModal','subscribe');
      var url = '/1.5/index/subscribe',
          self = this,
          data = {city:d};
      // post to server and get data for mode
      fetchData(url, {body:data}, function (err,ret) {
          // ret = ret.data;
          console.log(ret,err)
          if (ret.ok) {
            console.log('ok')
            window.bus.$emit('flash-message', self.$_('Subscribed'));
            self.userCities.push(d)
          } else if(ret.e){
            if(ret.e=='Need login') {
              document.location.href='/1.5/user/login'
            } else {
              window.bus.$emit('flash-message', ret.e);
            }
          }
        }
      );
    },
    filterFn(f){
      var self = this;
      var filter = self.filter;
      if (!filter) {
        return;
      }
      var reg = new RegExp(filter,'ig')
      // console.log(filter,f.o);
      // console.log(reg.test(f.o) || reg.test(f.n));
      return reg.test(f.o) || reg.test(f.n);
      // return (f.o.indexOf(filter) !== -1) || (f.n.indexOf(filter) !== -1)
    },
    parseCityList(l){
      var ret = [];
      for (var i = 0; i < l.length; i++) {
        let cur = l[i];
        cur.split = false;
        if (i==0) {
          ret.push({split:true, pn:cur.pn, p:cur.p, o:cur.o, n:cur.n});
        }
        ret.push(cur);
        let next = l[i+1] || {p:cur.p, pn:cur.pn};
        if (cur.p !== next.p) {
          ret.push({split:true, pn:next.pn, p:next.p, o:next.o, n:next.n});
        }
      }
      return ret;
    },
    closeCitySelect(){
      toggleModal('citySelectModal');
      window.bus.$emit('city-select-close');
    },
    addCityHistory(hist,cityNow){
      var hasCity = -1
      hist.forEach((elem,i)=>{
        if (elem.o == cityNow.o){
          hasCity = i
        }
      })
      if (hasCity >-1){
        hist.splice(hasCity,1)
      }
      delete cityNow.n
      delete cityNow.pn
      hist.unshift(cityNow)
      if(hist.length > 10){
        hist.length = 10
      }
      return hist
    },
    setCity(city){
      if (this.setCurCity) {
        this.curCity = city;
      }
      if (!city.cnty && !city.ncity)
        city.cnty = 'Canada';
      this.histCities = this.addCityHistory(this.histCities,city)
      localStorage.histCities = JSON.stringify(this.histCities)
      window.bus.$emit('set-city', {city:city, doSearch:this.doSearch});
    },
    getUsercities() {
      var self = this;
      fetchData('/1.5/index/userCities', {},function(err,data) {
          // data = data.body;
          if (data.ok) {
            self.userCities = data.cities;
          }
        }
      );
    },
    getProvs () {
      var self = this;
      fetchData('/1.5/props/provs.json', {},function(err,data) {
        if (data.ok) {
          self.provs = data.p;
          // var on=null,bc=null;
          // // move ontario first, bc second
          // var onindex = self.provs.findIndex(function(prov){
          //   return prov.o_ab == 'ON';
          // });
          // if (onindex >=0) {
          //   on = self.provs.splice(onindex,1)[0];
          // }
          // var bcindex = self.provs.findIndex(function(prov){
          //   return prov.o_ab == 'BC';
          // });
          // if (bcindex >=0) {
          //   bc = self.provs.splice(bcindex,1)[0];
          // }
          // if (bc)
          //   self.provs.unshift(bc);
          // if (on)
            // self.provs.unshift(on);
        }
      });
    },
    changeProv () {
      if (this.prov == 'CA') {
        this.favCities = this.favCitiesCp;
        this.extCities = [];
        return;
      }
      this.favCities = [];
      this.extCities = [];
      this.extCitiesCp = [];
      window.bus.$emit('clear-cache');
      this.getCitiesFromProv();
    },
    //@input [{o:'', n:'', k:''}]
    //@return [{i:'A', l:[{o:'', n:'', k:''}]}]
    formatCityList (l, opt={}) {
      var ret = [], m = {};
      for (var i = 0; i < l.length; i++) {
        var v = l[i]; // v is city
        // do not hide city for english
        if (opt.hide && this.dispVar.lang!=='en') {
          if (v.o == v.n) {
            continue;
          }
        }
        var firstChar = v.o.charAt(0);
        if (!m[firstChar]) {
          m[firstChar] = [];
        }
        m[firstChar].push(v);
      }
      for (let k of "ABCDEFGHIGKLMNOPQRSTUVWXYZ") {
        if (m[k]) {
          ret.push({i:k, l:m[k]});
        }
      }
      return ret;
    },
    getCitiesFromProv (p) {
      if (!p) {
        p = this.prov;
      }
      var self = this;
      self.loading = true;
      window.bus.$emit('clear-cache');
      var body = {p:p, loc:self.needLoc};
      fetchData('/1.5/props/cities.json',{body},function(err,data) {
        if (data.ok) {
          self.extCitiesCp = data.cl;
          self.extCities = self.formatCityList(data.cl, {hide:true});
          // TODO: deal with fc
          self.favCities = self.parseCityList(data.fc);
          self.loading = false;
          window.bus.$emit('clear-cache');
        }
      })
    }
  },
  template: `
  <div class="modal" id="citySelectModal">
  <header class="bar bar-nav" v-if="!nobar"><a class="icon icon-close pull-right" @click="closeCitySelect()" href="javascript:void 0"></a>
    <h1 class="title">{{$_('Select City')}}</h1>
  </header>
  <div class="content">
    <div class="table-view-cell provbar" v-if="showProvBar"><span class="blue" @click="setCity({o:null,p:null,cnty:'Canada'})">{{$_('Canada')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: 'United States'})">{{$_('United States')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: 'China'})">{{$_('China')}}</span>
        <span
            class="blue" v-for="p in provs" v-if="p.o_ab!='CA'" @click="setCity({o:null, p:p.o, cnty: 'Canada'})">{{p.n || p.o}}</span><span class="blue" @click="setCity({o:null,p:null,cnty:null, ncity: true})">{{$_('No City')}}</span></div>
    <div class="filter">
        <div class="prov"><select v-model="prov" v-on:change="changeProv()"><option v-for="p in provs" v-bind:value="p.o_ab">{{p.n || p.o}}</option></select></div>
        <div class="input"><span class="desc" v-show="prov == 'CA'"><i class="fa fa-long-arrow-left"></i>{{$_('Select Province to see All Cities')}}</span><input v-show="prov !== 'CA'" type="text" v-model="filter" :placeholder="$_('Input City')" /></div>
    </div>
    <ul class="table-view" v-show="curCity.o">
        <li class="table-view-divider">{{$_('Current City')}}</li>
        <li class="table-view-cell"><a href="javascript:void 0" @click="setCity(curCity)">{{curCity.o}}<span v-show="curCity.o !== curCity.n" :class="{'right-2':showSubscribe, 'right':!showSubscribe}">{{curCity.n}}</span></a></li>
    </ul>
    <ul class="table-view" v-show="histCities && histCities.length">
        <li class="table-view-divider">{{$_('History')}}</li>
        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in histCities"><span @click="setCity(city)">{{$_(city.o)}}</span></span>
        </li>
    </ul>
    <ul class="table-view" v-show="showSubscribe && userCities && userCities.length">
        <li class="table-view-divider">{{$_('Subscribed Cities')}}</li>
        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in userCities"><span @click="setCity(city)">{{city.n}}</span><span class="icon icon-close" @click="unSubscribeCity(city,index)"></span></span>
        </li>
    </ul>
    <ul class="table-view" v-show="favCities.length">
        <li class="table-view-divider">{{$_('Popular Cities')}}</li>
        <li v-for="city in computedFavCities" :class="{'table-view-cell':!city.split, 'table-view-divider cust':city.split}"><span v-if="city.split">{{city.pn}}</span><span v-if="!city.split"><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{'right-2':showSubscribe, 'right':!showSubscribe}">{{city.n}}</span></a>
          <span v-if="showSubscribe">
            <span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>
            <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span></span>
          </span>
        </li>
    </ul>
    <ul class="table-view" v-for="idx in extCities">
        <li class="table-view-divider">{{idx.i}}</li>
        <li class="table-view-cell" v-for="city in idx.l"><span><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{'right-2':showSubscribe, 'right':!showSubscribe}">{{city.n}}</span></a><span v-if="showSubscribe"><span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>
          <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span>
              </span>
            </span>
        </li>
    </ul>
  </div>
  <flash-message/>
</div>
  `
};