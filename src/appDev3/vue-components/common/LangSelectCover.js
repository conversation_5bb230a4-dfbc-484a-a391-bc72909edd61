var LangSelectCover = {
  props:{
    oldVer:{
      type:Boolean,
      default:false
    },
    dispVar:{
      type:Object,
      default:function(){
        return {
          userCity:    {o:'Toronto'},
          languageObj:[],
        }
      }
    }
  },
  mounted(){
    var self = this, bus = window.bus;
    bus.$on('pagedata-retrieved', function (d) {
      if (d.lang) {
        self.locale = d.lang;
      }
      var defaultHomeTab = '';
      if(d.sessionUser && d.sessionUser.defaultHomeTab){
        defaultHomeTab = d.sessionUser.defaultHomeTab
      }
      self.defaultHomeTab = defaultHomeTab || localStorage.defaultHomeTab || 'new';
      if(d.coreVer){
        self.is63NewerVer = self.$parent.isNewerVer(d.coreVer,'6.3.1');
        self.is65NewerVer = self.$parent.isNewerVer(d.coreVer,'6.4.0');
        if(self.is63NewerVer && RMSrv.onAppStateChange){
          RMSrv.onAppStateChange((opt)=>{
            self.doCheckSequence();
          })
        }else{
          self.canRefresh = true;
        }
        if(self.isAndroid){
          // 6.5以上的安卓版本初始设置不请求定位权限
          if(self.is65NewerVer){
            self.steps = 2;
          }
        }
      }
    })
    // 新版流程ios初始设置不请求定位权限
    if(RMSrv.isIOS()){
      self.steps = 2;
    }
    self.isAndroid = RMSrv.isAndroid()
    self.doCheckSequence();
    self.showCover();
  },
  data () {
    return {
      canContinue:true,
      coreVer: '5.2.0',
      location:false,
      locationState:'denied',
      notification:false,
      notificationState:'denied',
      clickedLocation:0,
      clickedNotification:0,
      wordings:[
        // 'To disable location usage you need to go to settings, however location is required to find nearby properties and schools.',
        'To find nearby properties and schools you need to enable location.',
        // 'To disable notification you need to go to settings, however you need to enable notification to get latest messages and listing alerts.',
        'To get latest messages and listing alerts you need to enable notification.'
      ],
      locale:'en',
      steps:3,
      curStep:0,
      homeTabs:[
        {k:'feed',v:'Feeds',ctx:'discover'},
        {k:'new',v:'New',ctx:'tag'}
      ],
      canRefresh:false,
      refresh:false,
      confirmStep:0,
      locationInit:false,
      notificationInit:false,
      is63NewerVer:false,
      is65NewerVer:false,
      isAndroid:false,
      isInitApp:false,
    };
  },
  methods: {
    showCover() {
      var self = this;
      if (/hideLang=1/.test(document.URL)){
        return ;
      } else if (/src=setting/.test(document.URL)) {
        self.curStep = 1;
        self.addToStorage('curStep',self.curStep)
        return self.openCover();
      }
      self.getFromStorage('initApp',(isInitApp)=>{
        var status = 0
        if(isInitApp == null){
          if(self.readCookie('locale') ){
          // 之前已经init过，添加参数
            status = 1
            self.addToStorage('notificationPermission',new Date())
          }else if(self.isAndroid){
            // 没有init过的安卓版本添加notification已验证，因为安卓默认会给通知权限
            self.addToStorage('notificationPermission',new Date())
          }
          self.addToStorage('initApp',status)
        }else{
          status = isInitApp;
        }
        self.isInitApp = status == 1?false:true;
        self.getFromStorage('curStep',(step)=>{
          if(self.isInitApp){
            if(step > 0){
              self.curStep = step;
            } else {
              self.curStep = 1;
              self.addToStorage('curStep',self.curStep)
            }
            self.openCover();
          }else if((step != null) && (step > 0)){
          self.curStep = step;
          self.openCover()
          }
        })
      })
    },
    openCover() {
      setTimeout(function () {
        toggleModal('coverPageModal','open');
      }, 0);
    },
    isAuthedRet(ret=''){
      return ['authorized','granted'].indexOf(ret) > -1;
    },
    doCheckSequence(){
      var self = this;
      self.refresh = false;
      RMSrv.onReady(()=>{
        self.checkPermissions('notification',()=>{
          self.checkPermissions('location')
        })
      });
    },
    setStatus(type,checkedResult,cb,init){
      this[type] = this.isAuthedRet(checkedResult);
      this[type+'State'] = checkedResult;
      this[type+'Init'] = init ? true : false;
      if(cb){
        cb()
      }
    },
    checkPermissions(type,cb){
      var self = this;
      if(!RMSrv.permissions){
        return
      }
      RMSrv.permissions(type,'check',-1,function(result){
        if(result != 'denied'){
          return self.setStatus(type,result,cb)
        }
        if(self.isAndroid && (type == 'notification')){
          if(self.isInitApp){
            self.setStatus(type,result,cb,'init')
          }else{
            self.setStatus(type,'block',cb)
          }
        }
        self.getFromStorage(type+'Permission',(savedStatus)=>{
          if(savedStatus == null){
            self.setStatus(type,result,cb,'init')
          }else{
            self.setStatus(type,'block',cb)
          }
        });
      })
    },
    setCity(){
      this.addToStorage('curStep',this.curStep)
      this.$parent.getCityList('setUserCity');
    },
    blockAlert(optTip,cb) {
      var self = this;
      optTip = optTip?optTip:"To find nearby houses and schools you need to enable location";
      var fn = this.$_?this.$_:this.$parent.$_;
      if('function' !== typeof fn){
        fn = (a)=>{
          return a
        }
      }
      var tip = fn(optTip);
      var later = fn('Skip');
      var gotoNext = fn('Cancel');
      var optTl = optTl?optTl:"";
      function _doShowGotoNext(idx) {
        if (idx+'' == '1') {
           self.next()
        } else {
          if ('function' == typeof cb) {
            cb()
          }
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowGotoNext, optTl, [later, gotoNext]);
    },
    confirmNext(tp,cb){
      var self = this;
      var wording = self.getWording(tp, self[tp+'State']);
      if (wording) {
        self.blockAlert(wording);
      } else {
        cb();
      }
    },
    getWording(tp, result){
      var wording;
      if (tp == 'location') {
        if (!this.isAuthedRet(result)) {
          wording = this.wordings[0];
        } //else {
        //   wording = this.wordings[1];
        // }
      } else if (tp == 'notification') {
        if (!this.isAuthedRet(result)) {
          wording = this.wordings[1];
        } //else {
        //   wording = this.wordings[3];
        // }
      }
      return wording;
    },
    setLang (lang) {
      var self = this;
      localStorage.lang = lang;
      this.locale = lang;
      self.addToStorage('curStep',self.curStep)
      setLoaderVisibility('block');
      var d = '/1.5/index?src=setting';
      var url = "/1.5/settings/lang?l=" + lang + "&d=";
      url = url+encodeURIComponent(d);
      window.location = url;
    },
    confirmNextStep(step){
      var self = this;
      self.confirmStep = step;
      var type = step == 2?'location':'notification';
      if(self[type+'Init']){
        return
      }
      if(self.showRefresh){
        return bus.$emit('flash-message',self.$_(self.$parent.strings.refreshAlert.key));
      }
      if(self[type]){
        return self.next(step);
      }else{
        self.confirmNext(type);
      }
    },
    setHomeTab(value){
      if(this.defaultHomeTab == value){
        return
      }
      this.defaultHomeTab = value;
      this.$forceUpdate();
      if(this.dispVar.isLoggedIn){
        fetchData('/1.5/settings/user', {body:{defaultHomeTab:value}},function (err,ret) {
          if (err || ret.e) {
            return bus.$emit('flash-message',err || ret.e);
          }
        });
      }else{
        localStorage.defaultHomeTab = value;
      }
    },
    requestPermissions(type){
      var self = this;
      if(self[type+'State'] =="denied"){
        if(RMSrv.permissions){
          RMSrv.permissions(type,'request',function(result){
            self.addToStorage(type+'Permission',new Date())
            // 安卓获取location第一次blocked的返回值的denied
            if(result == 'denied'){
              result = 'blocked'
            }
            self.setStatus(type,result);
          })
        }
      }else{
        self.openAppSettings()
      }
    },
    openAppSettings(){
      this.refresh = true;
      if(RMSrv.openSettings){
        RMSrv.openSettings();
      }
    },
    addToStorage(key,val){
      RMSrv.setItemObj({key:key,value:val,stringify:true,store:true});
      localStorage[key] = val;
    },
    getFromStorage(key,cb){
      var self = this;
      RMSrv.getItemObj(key,true,(status)=>{
        if(!status){
          return cb(null)
        }
        if(status.indexOf('null') > -1 && (localStorage[key] == undefined)){
          return cb(null)
        }
        var matchNum = status.match(/\d+/)
        asyncStatus = matchNum ? parseInt(matchNum[0], 10) : null;
        localStatus = localStorage[key]
        if(asyncStatus == localStatus){
          return cb(asyncStatus)
        }
        cb(self.is65NewerVer?asyncStatus:localStatus)
      });
    },
    next(step){
      step = step || this.confirmStep;
      if(step >=this.steps){
        return this.done()
      }
      setLoaderVisibility('block');
      setTimeout(() => {
        this.curStep = step+1 > this.steps ? this.steps : step+1;
        this.addToStorage('curStep',this.curStep);
        setLoaderVisibility('none');
      }, 500);
    },
    back(step){
      if(step > this.steps){
        step = step >= this.steps ? this.steps : step;// 只有两页的情况下，page3返回需要处理page
      }
      this.curStep = step-1 >= 1 ? step-1 : 1;
      this.addToStorage('curStep',this.curStep);
    },
    done () {
      var d, url, userLang;
      var self = this;
      userLang = this.locale;
      setLoaderVisibility('block');
      self.addToStorage('curStep',0)
      if(self.isInitApp){
        self.addToStorage('initApp',1)
      }
      setTimeout(() => {
        if (typeof localStorage !== 'undefined' && userLang) {
          localStorage.lastCoverHideDate = new Date();
          d = `&d=${encodeURIComponent('/1.5/user/login#index')}`;
          url = "/1.5/settings/lang?l=";
          if (!self.isInitApp) {
            return window.location = '/1.5/index';
          }
          var redirectUrl = url + userLang + d;
          if(self.dispVar.homePageABTest){
            return self.sendTestInfo(redirectUrl);
          }
          window.location = redirectUrl;
        } else {
          toggleModal('coverPageModal');
        }
      }, 500);
    },
    sendTestInfo(redirectUrl){
      var self = this,url='/abtest',groupName='home';
      let seed = getABTestSeed();
      self.setCookie(groupName+'seed',seed)
      axios.put(url, {groupName})
      .then((ret)=>{
        ret = ret.data;
        if(ret.ok){
          checkAndSendLogger(null,{sub:groupName,act:'abtest',tgp:'hp'+ret.group});
          if(ret.group == 'A'){
            window.location = redirectUrl
          }else{
            window.location = '/1.5/index'
          }
        }
      })
      .catch( (ret) => {
        console.error( "server-error" );
      })
    },
  },
  computed:{
    showRefresh:function(){
      return this.canRefresh && this.refresh
    }
  },
  template: `
  <div>
    <div style="display:none">
      <span v-for="w in wordings">{{ $_(w)}}</span>
    </div>
    <div class="modal" id="coverPageModal">
      <div class="content">
        <header class="bar bar-nav" v-if='!isInitApp'>
          <a class="icon icon-close pull-right" @click="done()"></a>
        </header>
        <div class="title-wrapper">
          <div class="tl">{{ $_("Setup")}}
            <span class="desc gray">{{curStep}}/{{steps}}</span>
          </div>
        </div>
        <div class="step-line-wapper">
          <span v-for='i in steps' :class="{active:i == curStep}"></span>
        </div>
        <div class="steps-wrapper">
          <div class="page" v-show='curStep == 1'>
            <div class="cata lang">
              <div class="tl">{{ $_("Language")}}</div>
              <div class="desc" v-for="lang in dispVar.languageObj" @click="setLang(lang.k)">
                {{lang.v}}
                <span class="fa pull-right" :class='{"fa-check-circle":locale == lang.k,"fa-circle-thin":locale != lang.k}'></span>
              </div>
            </div>
            <div class="cata city">
              <div class="tl">{{ $_("Default City")}}</div>
              <div class="opts">
                <div class="desc">
                  <div>{{dispVar.userCity.n || dispVar.userCity.o}}</div>
                </div>
                <div class="switch"><span class="positive" @click="setCity()">{{ $_("Change","lang select")}}</span>
                </div>
              </div>
            </div>
            <div class="cata home">
              <div class="tl">{{ $_("Default Homepage Tab")}}</div>
              <div class="desc" v-for="tab in homeTabs" @click="setHomeTab(tab.k)">
                {{ $_(tab.v,tab.ctx)}}
                <span class="fa pull-right" :class='{"fa-check-circle":defaultHomeTab == tab.k,"fa-circle-thin":defaultHomeTab != tab.k}'></span>
              </div>
            </div>
            <div class="btn-box login-wrapper">
              <span type="button" class="btn btn-block btn-positive full" @click='next(1)'> {{ $_("Continue")}}</span>
            </div>
          </div>

          <div class="page" v-show='(curStep == 2) && (steps == 3)'>
            <div class="cata location">
              <div class="tl">{{ $_("Turn On Location Permission")}}
              </div>
              <div class="desc" v-if='showRefresh' @click="doCheckSequence()">
                {{ $_("Permissions status updated.")}}
                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>
              </div>
              <div class="desc" @click="requestPermissions('location')">
                {{$_('ON')}}
                <span class="fa pull-right" v-show='!showRefresh' :class='{"fa-check-circle":(location && !locationInit),"fa-circle-thin":((!location) || locationInit)}'></span>
              </div>
              <div class="desc" @click="requestPermissions('location')">
                {{$_('OFF')}}
                <span class="fa pull-right" v-show='!showRefresh' :class='{"fa-check-circle":(!location && !locationInit),"fa-circle-thin":(location || locationInit)}'></span>
              </div>
              <div class="desc gray">{{ $_("We highly recommend you grant permission, as it will be used for a map-based property search.")}} {{ $_("We use GPS to zoom in/out on the user's current location and filter properties nearby.")}}</div>
              <div class="desc gray">{{ $_("The use of your location data is subject to our privacy policy.")}}</div>
            </div>
            <div class="btn-box login-wrapper">
              <span type="button" class="btn btn-block" @click='back(2)'> {{ $_("Back")}}</span>
              <span type="button" class="btn btn-block btn-positive" :class="{'disabled':locationInit}" @click='confirmNextStep(2)'> {{ $_("Continue")}}</span>
            </div>
          </div>

          <div class="page" v-show='curStep == steps'>
            <div class="cata notification">
              <div class="tl">{{ $_("Turn On Notification Permission")}}
              </div>
              <div class="desc" v-if='showRefresh' @click="doCheckSequence()">
                {{ $_("Permissions status updated.")}}
                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>
              </div>
              <div class="desc" @click="requestPermissions('notification')">
                {{$_('ON')}}
                <span class="fa pull-right" v-show='!showRefresh' :class='{"fa-check-circle":(notification && !notificationInit),"fa-circle-thin":((!notification) || notificationInit)}'></span>
              </div>
              <div class="desc" @click="requestPermissions('notification')">
                {{$_('OFF')}}
                <span class="fa pull-right" v-show='!showRefresh' :class='{"fa-check-circle":(!notification && !notificationInit),"fa-circle-thin":(notification || notificationInit)}'></span>
              </div>
              <div class="desc gray">{{ $_("Enable notification to get timely push notifications for property listings and relevant news.")}} {{ $_("Stay informed and never miss any new listings or status updates like price changes to your favorite listings.")}}</div>
            </div>
            <div class="btn-box login-wrapper">
              <span type="button" class="btn btn-block" @click='back(3)'> {{ $_("Back")}}</span>
              <span type="button" class="btn btn-block btn-positive" :class="{'disabled':notificationInit}" @click='confirmNextStep(3)'> {{ $_("Done")}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  `
};