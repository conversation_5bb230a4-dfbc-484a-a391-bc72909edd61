
/**
 * ForumSummaryCard 组件
 * 用于显示论坛帖子的摘要卡片，支持左右布局和上下布局两种显示模式
 *
 * @component
 * @example
 * <forum-summary-card
 *   :post="postData"
 *   :disp-var="displayVariables"
 *   cur-style="up-down"
 *   parent-page="forumList">
 * </forum-summary-card>
 */
var ForumSummaryCard = {
  mixins: [pageDataMixins,forumMixins,forumCommonMixins],
  props: {
    postType:{
      type:String,
      default:'forum'
    },
    parentPage:{
      type: String,
      default:''
    },
    post: {
      type: Object,
      default: function () {
        return {
          hasUpdate:false,
          src:'news',
        }
      }
    },
    dispVar: {
      type: Object,
      default: function () {
        return {}
      }
    },
    hideStickyIcon: {
      type:Boolean,
      default: false
    },
    noTag: {
      type: Object,
      default: function () {
        return {}
      }
    },
    noTagAction:{
      type:Boolean,
      default: false
    },
    isWeb: {
      type:<PERSON>olean,
      default: false
    },
    displayPage: {
      type:String,
      default: 'all'
    },
    curStyle: {
      type: String,
      default: 'left-right'  // 默认左右布局，'up-down'为上下布局
    }
  },
  components: {
    LazyImage
  },
  computed:{
    computedThumb:function() {
      if(this.post.thumb) {
        return this.post.thumb
      } else if(this.post.src=='sch') {
        return '/img/school/school_forum.png'
      } else if (this.post.src=='psch') {
        return '/img/school/school_forum_p.png'
      } else {
        return null;
      }
    },
    computedForumFas: function() {
      return this.isForumFas(this.dispVar, {city:this.post.city, prov:this.post.prov});
    },
    commentsHeight: function() {
      if ( (this.post.tags && this.post.tags[0]) || this.post.src=='property')
        return 40;
      else
        return 60;
    },
    computedVc:function() {
      return (this.post.vc || 0)  + (this.post.vcc ||0)
    },
    computedTp: function () {
      if (this.post.tpbl && this.post.tpbl[0])
        return this.post.tpbl[0];
      else if (this.post.tp)
          return this.$_('Topic')
      else
        return null;
    },
    // 获取图片样式
    computedImgStyle: function() {
      return this.layoutStyles[this.curStyle] ?
        this.layoutStyles[this.curStyle].image :
        this.layoutStyles['left-right'].image;
    },
    // 获取容器样式
    computedContainerStyle: function() {
      return this.layoutStyles[this.curStyle] ?
        this.layoutStyles[this.curStyle].container :
        this.layoutStyles['left-right'].container;
    },
    // 统一的图片渲染配置
    imageRenderConfig: function() {
      const isUpDown = this.curStyle === 'up-down';
      return {
        isUpDown: isUpDown,
        containerClass: isUpDown ? 'image-section' : 'image-section-lr',
        imageClass: isUpDown ? 'img post-img-ud' : 'img post-img',
        imageComponent: isUpDown ? 'div' : 'lazy-image',
        imageStyle: this.computedImgStyle,
        showVideoTags: true
      };
    },
    // 统一的视频标签配置
    videoTagsConfig: function() {
      const config = this.imageRenderConfig;
      // 统一判断逻辑，只返回一个字段
      const videoTagClass = config.isUpDown ? '' : (this.computedThumb ? '' : 'noPic');
      return {
        videoTagClass
      };
    }
  },
  data () {
    return {
      imageLoadError: false,
      // 预定义的布局样式配置
      layoutStyles: {
        'left-right': {
          container: 'height: 103px;',
          image: 'width: 120px;height: 90px;background-size: 100% 100%;'
        },
        'up-down': {
          container: 'height: auto;',
          image: 'width: 100%; height: 0; padding-bottom: 66.67%; background-size: 100%; background-position: center; background-repeat: no-repeat;'
        }
      }
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (!this.post.del)
      this.post.del = false;
  },
  methods: {
    /**
     * 渲染图片组件的统一方法
     * @returns {Object} 图片渲染所需的所有配置
     */
    renderImageComponent() {
      const config = this.imageRenderConfig;
      const videoConfig = this.videoTagsConfig;

      return {
        ...config,
        ...videoConfig,
        hasThumb: !!this.computedThumb,
        thumbUrl: this.computedThumb,
        altText: this.post.tl,
        firstLetter: this.post.tl ? this.post.tl.substr(0,1) : '',
        // 视频相关
        isVideo: this.post.src === 'video',
        hasVideoRecord: this.post.vidRecord || this.post.passedLive,
        isLiving: this.post.vidLive && this.post.isLiving,
        isScheduled: this.post.vidLive && !this.post.isLiving && !this.post.passedLive,
        scheduledTime: this.parseDate(this.post.ohv || '')
      };
    },

    toggleFlagModal(post) {
      this.$parent.toggleFlagModal(post);
      return;
    },
    parseDate(ohv=''){
      // 检查参数是否有效
      if (!ohv || typeof ohv !== 'string') {
        return '';
      }
      var l = ohv.split(' ');
      var p1 = l[0].split('-');
      var p2 = l[1].split('-');
      // + 'EST' 'EDT'?
      return p1[1]+'.'+p1[2]+' '+p2[0];
    },
    imageLoadError() {
      this.post.thumb = null;
    },
    openView(event) {
      if (event) event.stopPropagation();
      if (this.post.hasUpdate){
        this.post.hasUpdate = false;
      }
      if (this.isWeb)  {
        window.open('/1.5/forum/webedit?web=true&id='+this.post._id,'_blank');
      } else if (this.postType == 'wecard') {
        this.$parent.showWecard(this.post);
      } else {
        this.$parent.showPostView(this.post._id,this.post.src,this.post.gid);
      }
      return false;
    },
    showAd(event) {
      if (event) event.stopPropagation();
      this.$parent.showPostView(this.post._id,this.post.src,this.post.gid,this.post.adInlist,this.post.adTop,this.post.adTopPhoto)
    },
    addCityFilter(event) {
      if(this.noTagAction) {
        return;
      }
      if (event) event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&city='+this.post.city+'&prov='+this.post.prov, '_blank')
      } else {
        this.$parent.curCity = {o:this.post.city, p:this.post.prov, cnty:this.post.cnty}
        this.$parent.reloadPosts();
      }
      return false;
    },
    openTag(event) {
      if(this.noTagAction) {
        return;
      }
      if (event) event.stopPropagation();
      var tag = this.post.tags[0];
      if (this.isWeb)  {
        window.open(window.location.href+'&tag='+tag, '_blank')
      } else {
        this.$parent.tag = tag;
        this.$parent.reloadPosts();
      }
      return false;
    },
    openGroup(event) {
      if (event) event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&gid='+this.post.gid, '_blank')
      } else {
        this.$parent.gid = this.post.gid;
        this.$parent.gnm = this.post.gnm;
        this.$parent.reloadPosts();
      }
      return false;
    },
    openBySrcView(src, event) {
      if(this.noTagAction) {
        return;
      }
      if (event) event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&src='+src, '_blank')
      } else {
        this.$parent.src = src;
        this.$parent.reloadPosts();
      }
    },
    openTp(event) {
      if(this.noTagAction) {
        return;
      }
      if (event) event.stopPropagation();
      if (this.post.tp) {
        if (this.isWeb)
          window.open('http://'+this.dispVar.reqHost+'/forum/'+this.post._id + '/'+ formatUrlStr(this.post.tl), '_blank')
        else
          this.$parent.showPostView(this.post._id,this.post.src);
      } else {
        var self = this;
        axios.get('/1.5/forum/findPostByTp/' + self.computedTp)
        .then((ret)=>{
          ret = ret.data
          if (ret.ok) {
            if (this.isWeb)
              window.open('http://'+self.dispVar.reqHost+'/forum/'+ret.postid, '_blank')
            else
              this.$parent.showPostView(ret.postid,this.post.src);
          } else {
              return window.bus.$emit('flash-message', ret.e);
          }
        }).catch(()=> {
          console.error(err.status+':'+err.statusText);
        });
      }
      return false;
    },
  },
  template: `
  <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid, 'layout-up-down': curStyle == 'up-down','box-shadow': curStyle == 'up-down'}">
    <!-- 广告模式 -->
    <div class="post-top-div" style="position:relative; display:block!important;" v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle != 'up-down'">
        <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_('Edit')}}</span></div>
        <img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />
        <div class="post-top-text">{{$_('AD')}}</div>
    </div>
    <!-- 正常内容模式 -->
    <div :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">
        <!-- 上下布局：图片在上 -->
        <div v-if="curStyle == 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
            <div v-if="renderImageComponent().hasThumb" :style="renderImageComponent().imageStyle + 'background-image: url(' + renderImageComponent().thumbUrl + ');'" :class="renderImageComponent().imageClass">
                <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" style="position: absolute; top: 10px; right: 10px;"><i class="fa fa-video-camera"></i></span>
                <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" style="position: absolute; bottom: 10px; right: 10px;">{{$_('LIVE')}}</span>
                <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" style="position: absolute; bottom: 10px; right: 10px;">{{renderImageComponent().scheduledTime}}</span>
            </div>
            <div v-else :class="renderImageComponent().imageClass" :style="renderImageComponent().imageStyle">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 50px; color: #d2d5d8;">{{renderImageComponent().firstLetter}}</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div :class="curStyle == 'up-down' ? 'content-section-ud' : 'post-summary'">
            <!-- 标题区域 -->
            <div :class="curStyle == 'up-down' ? 'post-title-ud' : 'post-title'" v-bind:class="{deleted: post.del}" @click="openView()">
                <span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span>
                <span  class="red-button"  v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle == 'up-down'">{{$_('AD')}}</span>
                <span class="red-button" v-if="post.sticky && !noTag.top">{{$_('TOP')}}</span>
                <span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span>
                <span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic)">{{computedTp}}</span>
                <span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag">
                    <span v-if="post.tags[0]=='HOT'">{{$_('HOT')}}</span>
                    <span v-else>{{post.tags[0]}}</span>
                </span>
                <span class="red-button blue" @click="openBySrcView('property')" v-else-if="post.src=='property' && post.cc>0 && !noTag.property">{{$_('Home Review')}}</span>
                <span class="red-button blue" @click="openBySrcView('sch')" v-else-if="(post.src=='sch'||post.src=='psch') && !noTag.sch && !noTag.psch">{{$_('School Review')}}</span>
                <span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!=='No City'">{{$_(post.city||post.prov||post.cnty,'city')}}</span>
                <!-- 标题文本 -->
                <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-if="['property','psch','sch'].indexOf(post.src)>=0 && post.cmntl">{{post.cmntl + ' ' + post.tl}}</span>
                <span :class="curStyle == 'up-down' ? 'txt-ud' : 'txt'" v-else>{{post.tl}}</span>
            </div>

            <!-- 评论信息区域 -->
            <div class="post-comments" :style="curStyle == 'up-down' ? 'margin-top: 8px;' : ''">
                <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>
                <span class="post-bottom">
                    <span v-if="post.src!='sch' && post.src!=='psch'">
                        <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>
                        <span v-else>{{computedVc}}</span>
                        <span class="fa fa-eye" style="padding-left:'5px'"></span>
                    </span>
                    <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">
                        <span v-if="post.src!='sch' && post.src!=='psch'">|</span>
                        <span>{{post.cc}}</span>
                        <span class="fa fa-comments"></span>
                    </span>
                    <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>
                </span>
                <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>
                <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>
                <span class="realtor-only" v-if="post.realtorOnly">{{$_('Realtor Only')}}</span>
                <span class="icon icon-close reportForumIcon" v-if="parentPage == 'forum'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>
            </div>
        </div>

        <!-- 左右布局：图片在右 -->
        <div v-if="curStyle !== 'up-down'" :class="renderImageComponent().containerClass" @click="openView()">
            <lazy-image :class="renderImageComponent().imageClass" v-if="renderImageComponent().hasThumb" :alt="renderImageComponent().altText" :error="imageLoadError" :src="renderImageComponent().thumbUrl" :imgstyle="renderImageComponent().imageStyle"></lazy-image>
            <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" :class="renderImageComponent().videoTagClass"><i class="fa fa-video-camera"></i></span>
            <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" :class="renderImageComponent().videoTagClass">{{$_('LIVE')}}</span>
            <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" :class="renderImageComponent().videoTagClass">{{renderImageComponent().scheduledTime}}</span>
            <div :class="renderImageComponent().imageClass" v-if="!renderImageComponent().hasThumb">
                <div class="no-img">{{renderImageComponent().firstLetter}}</div>
            </div>
        </div>
    </div>
  </div>
  `
};