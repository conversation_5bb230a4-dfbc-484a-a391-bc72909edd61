
var ReportForumForm = {
  components:{
    PageSpinner
  },
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {}
      }
    },
    target:{
      type:Object,
      default:function () {
        return {}
      }
    },
    needWxid:{
      type:Boolean,
      default:false,
    },
    cstyle:{
      type:Object,
      default:function () {
        return {}
      },
    },
    owner:{
      type:Object,
      default:function () {
        return {vip:false};
      }
    },
    userForm:{
      type:Object
    },
    page:{
      type:String,
      default:'forumHome'
    },
    isWeb:{
      type:Boolean,
      default:false
    },
    title:{
      type:String,
      default:''
    },
    feedurl:{
      type:String,
      default:'/chat/api/feedback'
    },
    mblNotRequired:{
      type:Boolean,
      default:false
    },
    mRequired:{
      type:<PERSON>olean,
      default:false
    }
  },
  data () {
    return {
      closeCountdown:3,
      closeCountdownTimer:null,
      options: [
        'Uncivil or offensive',
        'Safety issue, fraud or illegal',
        'Misinformation',
        'Copyright violation',
        'Spam or annoying'
      ],
      nmErr:    false,
      emlErr:   false,
      mblErr:   false,
      mErr:   false,
      sending:  false,
      message:  null,
      picUrls:  this.$parent.picUrls || []
    };
  },
  // watch:{
  //   'userForm.nm':function (val,oldVal) {
  //       console.log(val);
  //   }
  // },
  mounted(){
    if (this.closeCountdownTimer) {
      clearInterval(this.closeCountdownTimer);
      this.closeCountdown = 3;
    }
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    bus.$on('reset-signup', function (d) {
      var msg;
      self.message = null;
      var msg = document.querySelector('#ForumSignUpSuccess');
      var form = document.querySelector('#signUpForm');
      if (form && self.owner.vip) {
        form.style.display = 'block';
      }
      if (msg) {
        msg.style.display = 'none';
      }
    });
  },
  methods: {
    closeReportForm() {
      this.$parent.closeReportForm();
    },
    signUp () {
      var error, ref, regex_email, self=this;
      self.nmErr = false;
      self.emlErr = false;
      self.mblErr = false;
      self.mErr = false;
      self.message = null;
      // return console.log(self.userForm);
      let msg = document.querySelector('#ForumSignUpSuccess');
      msg.style.display = 'none'
      if (self.sending) {
        return;
      }
      self.sending = true;
      // self.userForm.m = self.userForm.violation+'<br />'+self.userForm.m;
      axios.post(self.feedurl, self.userForm)
      .then((ret)=>{
          ret = ret.data;
          self.sending = false;
          if (ret.ok) {
            document.querySelector('#signUpForm').style.display = 'none';
            self.closeCountdownTimer = setInterval(()=>{
              if (self.closeCountdown > 1) {
                self.closeCountdown--;
              } else {
                clearInterval(self.closeCountdownTimer);
                self.$parent.closeReportForm();
              }
            },1000)
            if (ret.msg) {
              self.message = ret.msg;
            }
          } else {
            self.message = ret.err||ret.e;
          }
          return msg.style.display = 'block';
        }).catch(()=> {
          self.sending = false;
          // RMSrv.dialogAlert('Server Error');
          console.error('Server Error');
        });
    }
  },
  template: `
  <div id="reportForm">
    <div class="mask show"></div>
    <div id="reportFormContainer" :class="page">
        <div id="ForumSignUpSuccess" :style="cstyle">
          <i class="fa fa-check-circle" v-if="!message"></i>
          <span v-if="message">{{message}}</span>
          <span v-else>{{$_('Your feedback has been submitted.')}}
            <span>{{$_('This will close in ') + closeCountdown + 's'}}</span>
          </span>
        </div>
        <form id="signUpForm" :class="{'visible':owner.vip, 'web':isWeb}" :style="cstyle">
            <page-spinner :loading.sync="sending"></page-spinner>
            <div class="tl"><span v-if="title">{{title}}</span><span v-else>{{$_('Contact Me')}}</span></div>
            <div><label><span class="tp">{{$_('What is wrong')}}</span><span class="ast">*</span></label><select style="height: 35px;background-color:#fff" v-model="userForm.violation">
                    <option value="" selected="selected">{{$_('Please Select')}}</option>
                    <option v-for="(o,idx) in options" :value="o">{{$_(o)}}</option>
                </select></div>
            <div><label><span class="tp">{{$_('Message','signUpForm')}}</span><span class="ast" v-if="mRequired">*</span></label><textarea class="m" style="padding:10px;" rows="3" v-model="userForm.m" :class="{'error':mErr}"></textarea></div>
            <div><button class="btn btn-block btn-signup" :disabled="!userForm.violation" type="button" @click="signUp()">{{$_('Submit','signUpForm')}}</button></div>
        </form><img class="close" @click="closeReportForm()" src="/img/staging/close.png" />
    </div>
  </div>
`
};
