// 引用axios.js
var forumMixins = {
  created :  function () {
  },
  data() {
    return {
    }
  },
  computed:{
  },
  methods : {
    reloadPosts() {
      var self = this;
      self.allPosts = [];
      var opt={}
      if(self.computedListKey){
        self[self.computedListKey]=[];
        opt.listKey=this.computedListKey;
        opt.hasMoreKey=this.computedHasMoreKey
      }
      if(document.getElementsByClassName("tabs-container").length>0){
        document.getElementsByClassName("tabs-container")[0].click();
      }
      self.pgNum=1;
      var params = self.getSearchParmas();
      params.page = self.pgNum
      self.getAllPost(params,opt);
    },
    goTo(url) {
      window.location.href=url;
    },
    openTBrowser (url, cfg={}) {
      if (this.dispVar.isApp) {
        this.tbrowser(url);
      } else {
        window.location.href = url;
      }
    },
    changeStatus(status, postid, gid, cb) {
      var self = this;
      axios.post('/1.5/forum/changeStatus',{id: postid, status:status, gid:gid})
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
          self.post.readStatus = status;
          cb();
        }
      })
      .catch(err=>{
        console.error(err.status+':'+err.statusText);
      })
    },

    refreshPost(postid,gid) {
      var self = this;
      axios.post('/1.5/forum/detail/' + postid,{gid:gid, type:'summary'})
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
            window.bus.$emit('forum-view-close', ret.post);
        }
      })
      .catch(err=>{
        self.loading = false;
        console.error(err.status+':'+err.statusText);
      })
    },
    //used for open post view from forum list
    showPostView(postid,src,gid,adInlist,adTop,adTopPhoto) {
      var self = this;
      if(!postid || postid=='null'){
        return;
      }
      // NOTE: if we reload this page we shoulnt popup, so change url, remove postid
      // make sure this is called once after reload page
      if(window.vars && vars.postid && vars.postid == postid){
        // unset url vars.post id,
        vars.postid = null
        var url = new URL(window.location);
        url.searchParams.set('postid', null);
        url.search = url.searchParams;
        url        = url.toString();
        history.replaceState({}, null, url);
        // open instantly, checkalive is 2s when app came to foreground
        timeout = 0;
        // alert(window.location,vars.postid,timeout)
      }
      if(adInlist && adTop && adTopPhoto) {
        axios.post('/1.5/forum/adClick',{id: postid, index:0, gid: gid})
        .then(ret=>{
          ret = ret.data
          if (ret.ok) {
            RMSrv.showInBrowser(adTop);
          }
        })
        .catch(err=>{
          console.error(err.status+':'+err.statusText);
        })
      } else {
        let url = null;
        if(src =='psch') {
          url = '/1.5/school/private/detail/'+postid
        } else if (src =='sch') {
          url = '/1.5/school/public/detail?id='+postid+'&redirect=1';
        } else {
          url = '/1.5/forum/details?id=' + postid;
        }
        if (gid) {
          url += '&gid='+gid;
        }
        url = this.appendDomain(url);
        var cfg = {hide:false, title:this.$_('RealMaster')}
        if (this.isForumList) {
          url = url +"&from=forumList"
          return window.open(url, '_blank');
        }
        if (!this.dispVar.isApp) {
          url = url +"&iswebAdmin=1"
          return document.location.href = url;
        }
        if (url.indexOf('?')>0) {
          url = url + "&inFrame=1"
        } else {
          url = url + "?inFrame=1"
        }
        // list open detail, detail open property, if property need login, need callback to list, then redirect.

        RMSrv.getPageContent(url, '#callBackString', cfg, function(val) {
          // for close post and later
          try {
            if (/^cmd-redirect:/.test(val)) {
              var url = val.split('cmd-redirect:')[1];
              return window.location = url;
            }
          } catch (e) {
            console.error(e);
          }
          if (val ==':cancel' || val ==':later') {
            if (gid) {
              var status = val == ':cancel' ? 'read' : 'later';
              self.changeStatus(status, postid, gid, function() {
                self.refreshPost(postid,gid);
              });
            } else {
              self.refreshPost(postid,gid);
            }
          } else {
            // for click city, tags, related posts.
            console.log(val)
            try {
              var ret = JSON.parse(val);
              window.bus.$emit('forum-view-close', ret);
            } catch (e) {
              console.error(e);
            }
          }
        });
      }
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    getAllPost(params,opt={}) {
      var self = this;
      var posts_more = 'posts_more'
      if(opt.hasMoreKey){
        posts_more = opt.hasMoreKey
      }
      self[posts_more] = false;
      self.doSearch(params, function(result) {
        self.loading = false;
        self.refreshing = false;
        if (result.length >20)
          self[posts_more] = true;
        var listKey = 'allPosts'
        if(opt.listKey){
          listKey = opt.listKey
        }
        // console.log('listkey=',listKey)
        self[listKey] = self[listKey].concat(result.splice(0,20));
        self.updateForumsAfterBlocked(listKey);
        if (params.scroll){
          setTimeout(() => {
            self.scrollPostIntoView();
          }, 20);
        }
      });
    },
    isForumUserBlocked(post,{blkUids,blkCmnts}) {
      var uid = post.uid;
      return (blkCmnts[post._id] && blkCmnts[post._id].b) || (uid && blkUids[uid]);
    },
    updateForumsAfterBlocked(listKey='allPosts') {
      try {
        var blkUids = JSON.parse(localStorage.getItem('blkUids')) || {};
        var blkCmnts = JSON.parse(localStorage.getItem('blkCmnts')) || {};
      } catch (error) {
        alert(error);
      }
      var allPostsNum = this[listKey].length;
      var allPosts = []
      for (let i = 0; i < allPostsNum; i++) {
        const post = this[listKey][i];
        if (!this.isForumUserBlocked(post,{blkUids,blkCmnts})) {
          allPosts.push(post);
        }
      }
      this[listKey] = allPosts;
    },
    doSearch(params,cb) {
      var self = this;
      trackEventOnGoogle(this.form,'search')
      axios.post('/1.5/forum/query',params)
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
          cb(ret.forums)
        }
        else if (ret.url) {
          self.goTo(ret.url);
        }
      })
      .catch(err=>{
        console.error(err);
      });
    },
    setUpPreview (el) {
      var h, w, self=this;
      w = el.naturalWidth;
      h = el.naturalHeight;
      if ( w>0 && h>0 ) {
        self.sizes.push(w + 'x' + h);
        el.setAttribute('data-size', w + 'x' + h);
      }
  },
    listScrolled(){
      var self = this, wait = 400;
      self.scrollElement = document.getElementById('forum-containter');
      if (!self.waiting && self.posts_more) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.posts_more) {
              self.pgNum+=1;
              var params = self.getSearchParmas();
              params.page = self.pgNum
              self.getAllPost(params);
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    formatNews(isApp) {
      var nodes = document.querySelectorAll(".post-content *[style]");
      for (var node of nodes) {
        node.style.removeProperty('font-size');
        node.style.removeProperty('line-height');
        node.style.removeProperty('color');
      }
      var links = document.querySelectorAll('.post-content a') || [];
      for (var link of links) {
        if (link.getAttribute('data-src')!='realmaster') {
          link.setAttribute('href','javascript:void(0)');
        } else {
          var url = link.getAttribute('href');
          if(!/^tel:/.test(url) && !/^mailto:/.test(url)) {
            if (isApp) {
              link.setAttribute('href','javascript:void(0)');
              link.setAttribute('onclick',"window.RMSrv.showInBrowser('"+url+"')");
            } else {
              link.setAttribute('href',url);
              link.setAttribute('target', '_blank');
            }
          }
        }
      }
      var imgs = document.querySelectorAll('.post-content img') || []
      for (var img of imgs) {
        var dataSrc = img.getAttribute('data-src')
        var dataS = img.getAttribute('data-s')
        if (/^(http|https):\/\/mmbiz.qpic.cn/i.test(img.src))
          img.src = img.src.replace(/&tp=\w+&wxfrom=\d&wx_lazy=\d/gi, '')
        else if (dataSrc)
          img.src = dataSrc
        img.setAttribute('style','')
        img.style.height='auto'
        img.style.width='100%'
        var dataIgnore = img.getAttribute('data-ignore')
        if (!dataIgnore) {
          img.addEventListener("click", this.previewPic);
          this.setUpPreview(img);
        }
        if (img.parentElement) {
          img.parentElement.style.height='auto';
          img.parentElement.style.width='100%'
        }
      }
      var iframes = document.querySelectorAll('iframe') || []
      for (var iframe of iframes) {
        var style = iframe.getAttribute('style')
        var dataSrc = iframe.getAttribute('data-src')
        var url = iframe.getAttribute('src')
        if (dataSrc!='realmaster') {
          iframe.src = ''
        }
        var reg = /width=((\d|\.)+)&height=((\d|\.)+)/
        if(reg.test(url)) {
          var width = window.innerWidth - 30
          var owidth = parseFloat(url.match(reg)[1])
          var oheight = parseFloat(url.match(reg)[3])
          var height = oheight/owidth * width
          var nurl=url.replace(/width=((\d|\.)+)&/,'width='+width+'&')
          nurl=nurl.replace(/&height=((\d|\.)+)&/,'&height='+height+'&')
          iframe.src = nurl
        }
        if (style) {
          var height = iframe.style.height;
          var minHeight = iframe.style.minHeight;
          iframe.setAttribute('style','')
          iframe.style.height = height || 'auto';
          iframe.style.minHeight = minHeight || '240px';
          iframe.style.width = '100%';
        }
      }
    },
    getThumbUrl(picurl) {
      if (picurl){
        var replaceFrom = `img.${this.dispVar.shareHostNameCn || 'realmaster.cn'}`; //TODO: get this from server
        var replaceTo = 'img.realmaster.com';
        return "url(" + picurl+ "),url(" + picurl.replace(replaceFrom,replaceTo)+ "), url('/img/no-pic.png')";
      } else {
        return "url('/img/no-pic.png')";
      }
    },
    inValidNickName(name, isForumAdmin) {
      return false; //bypass frontend check, check on backend.
      if (!name || !name.trim()) {
        return "nickname needed"
      }
      var nickname = name.toLowerCase().replace(/\s/g, '');
      var forbiddenWords = ['realmaster', '房大师', 'wechat']
      for (var word of forbiddenWords) {
        if (nickname.indexOf(word)>= 0 && !isForumAdmin)
          return "Can not use preserved keywords";
      }
      if (nickname.length > 20)
        return "nickname too long";
      var specialstr = ['>','<','=']
      for (var str of specialstr) {
        if (nickname.indexOf(str)>= 0)
          return "illegal nickname";
      }
      nickname = nickname.replace(/[&\/\\#,+()$~%.'":*?<>{}!^_-]/g,'');
      if (/\d{8,}/.test(nickname))
        return "illegal nickname";
      return false;
    },
    saveForumName(cb) {
      var self = this;
      axios.post('/1.5/forum/setFornm',{fornm: self.nickname})
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
          cb();
        } else {
          cb(ret.e)
        }
      })
      .catch(err=>{
        console.error(err);
      });
    },
    handleMsgAfterBlock(msg) {
      var self = this;
      window.bus.$emit('flash-message', msg);
      setTimeout(function(){
        self.toggleFlagModal(false);
        self.updateForumsAfterBlocked();
      },1000);
    },
    blockAuthor(post) {
      var uid = post.uid;
      if (!this.dispVar.isLoggedIn) {
        this.blkUids[uid] = 1
        localStorage.setItem('blkUids',JSON.stringify(this.blkUids));
        this.handleMsgAfterBlock(this.$_('User was block'));
        return;
      }
      trackEventOnGoogle(this.form,'blockAuthor')
      var self = this;
      this.blockCmnt({type:'user',uid},function(err,ret) {
        if (err) {
          return window.bus.$emit('flash-message', err);
        }
        self.blkUids[uid] = 1
        localStorage.setItem('blkUids',JSON.stringify(self.blkUids));
        self.handleMsgAfterBlock(ret.msg);
      });
    },
    updateBlkCmnts(post) {
      if (this.blkCmnts[post._id]) {
        this.blkCmnts[post._id].b = 1;
      } else {
        this.blkCmnts[post._id] = {b:1};
      }
      localStorage.setItem('blkCmnts',JSON.stringify(this.blkCmnts));
    },
    blockPost(post) {
      var self = this;
      // var url = '/1.5/forum/block';
      // if (post.gid) {
      //   url += '?gid='+post.gid
      // }

      if (!this.dispVar.isLoggedIn) {
        this.updateBlkCmnts(post);
        window.bus.$emit('flash-message', this.$_('Post was blocked'));
        setTimeout(function(){
          self.toggleFlagModal(false);
          self.showMask = false;
          self.reloadPosts();
        },1000);
        return;
      }
      trackEventOnGoogle(this.form,'blockPost')
      var params = {type:'post',forumId:post._id}
      this.blockCmnt(params,function(err,ret) {
        if (err) {
          return window.bus.$emit('flash-message', err);
        }
        setTimeout(function(){
          self.toggleFlagModal(false);
          self.showMask = false;
          self.reloadPosts();
        },1000);
        self.updateBlkCmnts(post);
        self.handleMsgAfterBlock(ret.msg);
      });
      // this.$http.post('/1.5/forum/blockCmnts', params).then(
      //   function (ret) {
      //     if (ret.data.ok) {
      //       window.bus.$emit('flash-message', ret.data.message);
      //       setTimeout(function(){
      //         self.toggleFlagModal(false);
      //         self.showMask = false;
      //         self.reloadPosts();
      //       },1000);
      //     } else return window.bus.$emit('flash-message', ret.data.e);
      //   },
      //   function (ret) {
      //     console.log(ret.status+':'+ret.statusText);
      //   }
      // );
    },
    reportPost(post) {
      this.toggleFlagModal(false);
      this.showMask = false;
      this.showReportForm = true;
      var reportForm = this.reportForm;
      var {protocol,host} = location;
      if (this.dispVar.isLoggedIn) {
        var {eml,nm} = this.dispVar.sessionUser;
        reportForm.userForm.eml = eml;
        reportForm.userForm.nm = nm;
      }
      trackEventOnGoogle(this.form,'reportPost')
      reportForm.userForm.id = post._id;
      reportForm.userForm.url = protocol+'//'+host+'/1.5/forum/details?id='+post._id;
      reportForm.userForm.img = post.thumb;
      reportForm.userForm.tl = post.tl;
      this.reportForm = reportForm;
    },
    closeReportForm() {
      this.showReportForm = false;
      this.showMask = false;
      var reportForm = this.reportForm;
      reportForm.userForm.violation = '';
      reportForm.userForm.m = '';
      reportForm.userForm.url = '';
      reportForm.userForm.img = '';
      reportForm.userForm.id = '';
      this.reportForm = reportForm;
    },
    contactUs() {
      trackEventOnGoogle(this.form,'contactUs')
      RMSrv.showInBrowser('https://realmaster.ca/about-us');
    },
    toggleFlagModal(post) {
      var body = document.querySelector('body');
      if (post) {
        body.classList.add('smb-open');
        this.flagPost = post;
        this.showMask = true;
      } else {
        body.classList.remove('smb-open');
        this.flagPost = false;
        this.showMask = false;
      }
    },
  }
}
