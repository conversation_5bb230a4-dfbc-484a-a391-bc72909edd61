
var BlockPopup = {
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {}
      }
    },
    flagPost:{
      type:Object,
      default:function () {
        return {}
      }
    },
  },
  data () {
    return {
    };
  },
  mounted(){
    if (this.closeCountdownTimer) {
      clearInterval(this.closeCountdownTimer);
      this.closeCountdown = 3;
    }
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
  },
  methods: {
    blockPost() {
      this.$parent.blockPost(this.flagPost);
    },
    blockAuthor() {
      this.$parent.blockAuthor(this.flagPost);
    },
    reportPost() {
      this.$parent.reportPost(this.flagPost);
    },
    contactUs() {
      this.$parent.contactUs();
    },
    toggleFlagModal() {
      this.$parent.toggleFlagModal();
    },
  },
  template: `
<div>
  <nav class="menu slide-menu-bottom smb-auto">
    <ul class="table-view">
        <li class="table-view-cell"><a class="flagModalLink" data-sub="block post" :data-id="flagPost&&flagPost._id?flagPost._id:''" @click="blockPost(flagPost)"><i class="fa fa-ban"></i>{{$_('Block this post')}}</a></li>
        <li class="table-view-cell" v-if="flagPost && flagPost.author && (!flagPost.author.isAdmin)"><a class="flagModalLink" data-sub="block user" :data-id="flagPost&&flagPost.author&&flagPost.author._id?flagPost.author._id:''" @click="blockAuthor(flagPost)"><i class="fa fa-ban"></i>{{$_('Block this user')}}</a></li>
        <li class="table-view-cell"><a class="flagModalLink" data-sub="report info" :data-id="flagPost&&flagPost._id?flagPost._id:''" @click="reportPost(flagPost)"><i class="fa fa-flag"></i>{{$_('Report','forum')}}</a></li>
        <li class="table-view-cell"><a class="flagModalLink" data-sub="contact us" :data-id="flagPost&&flagPost._id?flagPost._id:''" @click="contactUs()"><i class="fa fa-fw fa-phone"></i>{{$_('Contact us')}}</a></li>
        <li class="cancel table-view-cell" @click="toggleFlagModal(false)" data-sub="cancel report" :data-id="flagPost&&flagPost._id?flagPost._id:''">{{$_('Cancel')}}</li>
    </ul>
  </nav>
</div>
`
};
