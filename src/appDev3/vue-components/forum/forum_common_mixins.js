var forumCommonMixins = {
  created :  function () {
  },
  data() {
    return {
      monthArray: ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'],
    }
  },
  computed:{
    computedAdmin: function() {
      var groupAdmin = false;
      var self = this;
      if(!this.dispVar) {
        return false;
      }
      if (this.post && this.dispVar.userGroups) {
        var grp = this.dispVar.userGroups.find(function(grp) {
          return grp._id == self.post.gid;
        });
        if (grp && (grp.isAdmin || grp.isOwner))
          groupAdmin = true;
      }
      return this.dispVar.forumAdmin || groupAdmin;
    }
  },
  methods : {
    showComments(index) {
      event.stopPropagation();
      window.bus.$emit('showComments',index);
    },
    getImageUrl(picurl) {
      if (picurl)
        return "url(" + picurl+ "), url('/img/user-icon-placeholder.png')";
      else
        return "url('/img/user-icon-placeholder.png')";
    },
    formatTs2: function (ts) {
      if (!ts) return '';
      var ts = new Date(ts);
      var diff = new Date() - ts
      if (diff < 60000)
        return this.$_('Just now')
      else if (diff > 24*3600*1000) {
        return this.$_(this.monthArray[ts.getMonth()]) + ' ' + ts.getDate();
        // return (ts.getMonth() + 1) + '-' + ts.getDate() + ' ' + ts.getHours() + ":" + ts.getMinutes() + ":" + ts.getSeconds();
        // return fn(this.monthArray[ts.getMonth()]) + ' ' + ts.getDate() + ' ' + ts.getHours() + ":" + ts.getMinutes();
      }
      else if (diff > 3600*1000)
        return parseInt(diff / (3600*1000)) + this.$_('Hrs')
      else
        return parseInt(diff/ (60 * 1000)) + this.$_('Mins')
    },
    trimStr(str, len) {
      if (!str || !len)  return '';
      var a = 0;
      var i = 0;
      var temp = '';

      for (i=0; i<str.length; i++) {
        if (str.charCodeAt(i)>255) {
          a += 2;
        } else {
          a ++;
        }
        if (a > len) return temp + '...';
        temp += str.charAt(i);
      }
      return str;
    },
    formatTs(ts) {
      if (ts) {
        ts = new Date(ts);
        var minute = ts.getMinutes();
        if (minute<10)
          minute = '0'+minute
        return ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate() + ' ' + ts.getHours() + ":" + minute;
      } else
        return '';
    },
    blockCmnt(postData,cb) {
      axios.post('/1.5/forum/blockCmnt',postData)
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
          return cb(null,ret);
        } else {
          return (ret.e,null);
        }
      })
      .catch(err=>{
      return cb(err.status+':'+err.statusText,null);
      });
    }
  }
}
