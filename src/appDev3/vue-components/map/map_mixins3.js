/***
 * getIconFunc => getIcon
 * get_map_obj => getMapObject
 * 删除 mgName == ？关键字 添加elementType
 * elementType：
 *    img marker为图片 例如学校marker
 *    div 传入label （showing地图起点、终点）
 *    backgroundImg  默认marker
 * getAllGMarkers = getAllGroupMarkers
 * ***/
var map_mixins = {
  created :  function () {
  },
  methods : {
    getMapObject: function (mapParams) {
      if (!mapParams) {
        mapParams = {};
      }
      var ret = {
        isIOS: function() {
          return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        },
        generalElementFromOption:function({icon,label,image,zIndex,elementType='backgroundImg',style,url}){
            if (elementType == 'img') {
          // (opt.mgName == 'mapSchools' || opt.mgName = 'pSch) {
              var el = document.createElement('img');
              el.style = "width:30px;height:30px;padding:2.5px";
              if(style){
                el.style = style
              }
              el.setAttribute('src', icon);
              return el;
            }
            //append from to label to default mapbox marker
            if (elementType == 'div') {
            // (opt.mgName == 'from' || opt.mgName == 'to' )
              let el = document.createElement('div')
              let text = document.createTextNode(label)
              el.appendChild(text)
              el.style = "position:absolute;top:30px;left:10px";
              // mkProf.mkr.getElement().append(div)
              return el;
            }

          // position: [mkProf.lng, mkProf.lat],
          // (ignore)map: this.gmap,
          // (ignore)optimized: this.isIOS()
          // label: {text:D369K, color:'xxx', fontSize:'10px'}
          // icon:{
              // url: '/img/mapmarkers/price.png',
              // size: [56, 31],
              // origin: [-5, -5.5],
              // anchor: 'bottom',
              // anchor: new google.maps.Point(0, 32)
              // scaledSize: [45, 25]}
          // https://docs.mapbox.com/mapbox-gl-js/api/#map: The specified element must have no children.
          var el = document.createElement('div');
          var size = [45, 25];
          var url = url || '/img/mapmarkers/price.png';
          // for (var i of ['icon','image']) {
          //   for (var j of ['url','scaledSize']) {
          //     if (i) {
          //       if (i[j]) {
          //         if (j == 'url') {
          //           url = i[j]
          //         } else {
          //           size = i[j]
          //         }
          //       }
          //     }
          //   }
          // }
          var style = '';
          var zIndex = 1;
          if (icon) {
            if (icon.url) {
              url = icon.url
            }
            if (icon.scaledSize) {
              size = icon.scaledSize
            }
            if (icon.addedStyle) {
              style = icon.addedStyle;
            }
            if (icon.zIndex) {
              zIndex = icon.zIndex;
            }
            // console.log('icon :-> ',icon);
          }
          // if (image) {
          //   if (image.scaledSize) {
          //     size = image.scaledSize
          //   }
          //   if (image.url) {
          //     url = image.url
          //   }
          // }
          var text = '';
          var color = 'black';
          var fontSize = '11px'
          if (label) {
            if (label.text) {
              text = label.text;
            }
            if (label.color) {
              color = label.color;
            }
            if (label.fontSize) {
              fontSize = label.fontSize
            }
          }
          if (text) {
            el.innerHTML = text;
          }
          el.style = `width:${size[0]+'px'};height:${size[1]+'px'};background-image:url('${url}');color:${color};font-size:${fontSize};background-size:cover; text-align: center; z-index:${zIndex};`+style;
          // console.log('xxxxxxx',`width:${size[0]+'px'};height:${size[1]+'px'};background-image:url('${url}');color:${color};font-size:${fontSize};background-size:cover; text-align: center; `+style)
          return el;
        },
        init: function(el) {
          var hfHeight, tHeight, theHeight;
          tHeight = window.innerHeight || screen.height;
          hfHeight = mapParams.hfHeight != null ? mapParams.hfHeight : (50 + 44 + 32);
          theHeight = tHeight - hfHeight;
          this.el = document.getElementById(el);
          this.el.style.height = theHeight + 'px';
          return this.initMap(el);
        },
        initMap: function(el) {
          var defaultZoom, e, error, error1, error2, loc, mapLoc, opts, ref, v, zoom;
          defaultZoom = 13;
          if (vars.loc) {
            if ('string' === typeof vars.loc) {
              loc = (function() {
                var i, len, ref, results;
                ref = vars.loc.split(',');
                results = [];
                for (i = 0, len = ref.length; i < len; i++) {
                  v = ref[i];
                  results.push(parseFloat(v));
                }
                return results;
              })();
            }
            this.mapCenter = [loc[1], loc[0]];
            if ('string' === typeof vars.zoom) {
              zoom = parseInt(vars.zoom) || defaultZoom;
            }
            this.mapZoom = zoom;
          } else {
            loc = [43.72199, -79.45175];
            if (mapLoc = ((ref = document.getElementById('loc')) != null ? ref.value : void 0) || localStorage.lastMapLocZoom || localStorage.mapLoc) {
              loc = (function() {
                var i, len, ref1, results;
                ref1 = mapLoc.split(',');
                results = [];
                for (i = 0, len = ref1.length; i < len; i++) {
                  v = ref1[i];
                  results.push(parseFloat(v));
                }
                return results;
              })();
              if (vars.zoom) {
                loc[2] = parseInt(vars.zoom) || null;
              }
              this.mapZoom = loc[2] || (localStorage.mapZoom ? parseInt(localStorage.mapZoom) : defaultZoom);
              this.mapCenter = [loc[1], loc[0]];
            }
            this.mapCenter = [loc[1], loc[0]];
            if (this.mapZoom == null) {
              this.mapZoom = defaultZoom;
            }
          }
          opts = {
            center: this.mapCenter,
            zoom: this.mapZoom,
            style: null,//google.maps.MapTypeId.ROADMAP,
            draggable: true,
            scaleControl: true,
            disableDoubleClickZoom: false,
            mapTypeControl: false,
            streetViewControl: false,
            zoomControl: false,
            sendMsg:mapParams.sendMsg,
            dragRotate:false,//mapParams.dragRotate!=null?mapParams.dragRotate:true,
            // touchZoomRotate:false,//mapParams.touchZoomRotate!=null?mapParams.touchZoomRotate:true,
          };
          if (mapParams.mapTypeControl != null) {
            opts.mapTypeControl = mapParams.mapTypeControl?true:false;
          }
          this.mapbox= new Mapbox(opts);
          this.mapbox.init(el);
          // map canvas 400x300, fix here
          // this.mapbox.idle(['resize'])
          this.mapbox.onceResize()
          this.gmap = this.mapbox.map;
          // this.gmap = new google.maps.Map(this.el, opts);
          this.cluster = {};

          if (vars.loc && vars.cMarker && !mapParams.noCmarker) {
            var image = {
              url: '/img/mapmarkers/none-selected.png',
              // url: '/img/mapmarkers/hmarker.png',
              size: [32,32],//new google.maps.Size(30, 35),
              // origin: new google.maps.Point(0, 0),
              // anchor: new google.maps.Point(0, 32)
              scaledSize: [22,22],//new google.maps.Size(28, 32)
            };
            var cMarker = {
              position: this.mapCenter,
              optimized: this.isIOS(),
              icon: image,//'/img/mapmarkers/umarker.png',
              map: this.gmap
            };
            if (mapParams.defaultCmarkerIcon) {
              delete cMarker.icon;
            }
            //
            var el = this.generalElementFromOption(cMarker);
            var marker = new mapboxgl.Marker({element:el, anchor:'bottom'});

            marker.setLngLat(this.mapCenter).addTo(this.gmap)
            // this.Cmarker = new google.maps.Marker(cMarker);
          }
          var bndsChanged = mapParams.bndsChanged || function(){};
          var dragStart = mapParams.dragStart || null;
          var tilesLoaded = mapParams.tilesLoaded || null;
          var zoomChanged = mapParams.zoomChanged || null;
          if (dragStart) {
            this.gmap.on('dragend', dragStart);
            // google.maps.event.addListener(this.gmap, "dragstart", dragStart);
          }
          if (tilesLoaded) {
            console.warn('Not implemented yet!')
            // google.maps.event.addListener(this.gmap, "tilesloaded", tilesLoaded);
          }
          // if (zoomChanged) {
            // google.maps.event.addListener(this.gmap, "zoom_changed", zoomChanged);
          this.gmap.on('zoomend', bndsChanged);
          // }
          // google.maps.event.addListener(this.gmap, "bounds_changed", bndsChanged);
          this.gmap.on('dragend', bndsChanged);
          this.gmap.on('load', bndsChanged);
          if (vars.gps === '1') {
            return this.locateMe();
          } else {
            return this.mapbox._mapReady(opts.center);
          }
        },
        locateMe: function(opt,cb) {
          this.mapbox.locateMe(opt,cb)
          // var gpsLoc;
          // this._locating(true);
          // if (gpsLoc = this._getSavedGPSLoc()) {
          //   this._showUmarker(gpsLoc);
          //   if(cb) {
          //     return cb(gpsLoc)
          //   }
          // }
          // return this.realLocateMe(opt,cb);
        },
        // realLocateMe: function(opt,cb) {
        //   this.mapbox.realLocateMe(opt,cb);
          // var failed, self, success;
          // self = this;
          // // alert(JSON.stringify(opt))
          // success = function(l) {
          //   var loc;
          //   loc = [];
          //   loc[0] = l.coords.latitude;
          //   loc[1] = l.coords.longitude;
          //   self._showUmarker(loc);
          //   self.gmap.setZoom(14);
          //   return localStorage.lastGPSLoc = '' + loc[0] + ',' + loc[1];
          //   if(cb) {
          //     return cb(loc)
          //   }
          // };
          // failed = function(e) {
          //   var ref;
          //   if ((ref = self.Umarker) != null) {
          //     ref.setMap(null);
          //   }
          //   return self._locating(false);
          // };
          // // var opt = {
          // //   enableHighAccuracy: true,
          // //   timeout: 15000
          // // };
          // RMSrv.getGeoPosition(opt,function (pos) {
          //   if (pos.err) {
          //     failed(pos.err);
          //   } else {
          //     success(pos)
          //   }
          // });
        // },
        _showUmarker: function(loc1) {
          this.mapbox._showUmarker(loc1);
          // var Upos, ref;
          // this._locating(false);
          // Upos = new google.maps.LatLng(loc1[0], loc1[1]);
          // if ((ref = this.gmap) != null) {
          //   ref.setCenter(Upos);
          // }
          // if (this.Umarker) {
          //   // NOTE: when map not loaded marker was created, set position not work
          //   // this.Umarker.setPosition(Upos);
          //   this.Umarker.setMap(null);
          // }
          // var image = {
          //   url: '/img/mapmarkers/umarker.png',
          //   size: new google.maps.Size(32, 32),
          //   // origin: new google.maps.Point(0, 0),
          //   // anchor: new google.maps.Point(0, 32)
          //   scaledSize: new google.maps.Size(25, 25)
          // };
          // this.Umarker = new google.maps.Marker({
          //   position: Upos,
          //   optimized: this.isIOS(),
          //   icon: image,//'/img/mapmarkers/umarker.png',
          //   map: this.gmap
          // });
          // return this._mapReady();
        },
        // _getSavedGPSLoc: function() {
        //   var gpsLoc, s;
        //   if (gpsLoc = localStorage.lastGPSLoc) {
        //     return (function() {
        //       var i, len, ref, results;
        //       ref = gpsLoc.split(',');
        //       results = [];
        //       for (i = 0, len = ref.length; i < len; i++) {
        //         s = ref[i];
        //         results.push(parseFloat(s));
        //       }
        //       return results;
        //     })();
        //   }
        //   return null;
        // },
        // _locating: function(doing) {
        //   return mapParams.sendMsg("locating", doing);
        // },
        // _mapReady: function(pos) {
        //   var pos1;
        //   pos1 = this.gmap.getCenter() || pos;
        //   return mapParams.sendMsg("locationReady", pos1);
        // },
        saveLocation: function() {
          var pos, zoom;
          if (this.gmap && this.gmap.getCenter) {
            pos = this.gmap.getCenter();
            zoom = this.gmap.getZoom();
            return localStorage.lastMapLocZoom = '' + pos.lat + ',' + pos.lng + ',' + zoom;
          }
        },
        resized: function() {
          this.gmap.resize()
          // return google.maps.event.trigger(this.gmap, 'resize');
        },
        getCenter: function() {
          var mapCenter = this.gmap.getBounds().getCenter();
          return mapCenter;
        },
        recenter: function(items) {
          this.fitBounds(items)
          var mapLat = this.gmap.getBounds().getCenter().lat;
          var mapLong = this.gmap.getBounds().getCenter().long;
          this.gmap.setCenter = (mapLat, mapLong);
        },
        fitBounds:function(items,center){
          // this.mapbox.fitMarkers(items)
          if (!items || items.length == 0) {
            return;
          }
          // console.log('fitbounds',items)
          var bounds = new mapboxgl.LngLatBounds();
          // var bounds = [];
          if(items.length <2){
            var item = items[0];
            var ne = [item.lng-0.002,item.lat+0.002];
            var sw = [item.lng+0.002,item.lat-0.002];
            bounds.extend([ne,sw]);
          }else{
            for (var item of items) {
              if (item.lat && item.lng) {
                // var latlng = new google.maps.LatLng(item.lat, item.lng);
                bounds.extend([item.lng,item.lat]);
              }
            }
          }
          if(center){
            bounds.extend([center.lng,center.lat]);
          }
          // console.log('fitBounds');
          this.gmap.fitBounds(bounds,{padding: 80,duration:300});//animate:false
          // if(center){
          //   this.gmap.panTo(center, {duration: 300});
          //   setTimeout(() => {
          //     if(this.gmap.getZoom()>13){
          //       this.gmap.setZoom(this.gmap.getZoom() - 1)
          //     }
          //   }, 600);
            // todo 判断是否有marker超出显示范围
            // var newbounds = this.gmap.getBounds(),contained=true;
            // for(var item of items)  {
            //   if (!newbounds.contains([item.lng , item.lat])) {
            //     contained = false;
            //   }
            // }
            // if (!contained) {
            //   this.gmap.setZoom(this.gmap.getZoom() - 1)
            //   console.log('setzoom');
            // };
          // }
          // TODO: enlarge bounds 10%
        },
        recenterWithZoom:function (loc, zoom) {
          zoom = zoom ||10;
          if ((!loc && !loc.lat && !loc.lng) ||!this.gmap) {
            return;
          }
          this.gmap.setZoom(zoom);
          // https://docs.mapbox.com/mapbox-gl-js/api/#lnglatlike
          // var v1 = new mapboxgl.LngLat(-122.420679, 37.772537);
          // var v2 = [-122.420679, 37.772537];
          // var v3 = {lon: -122.420679, lat: 37.772537};
          this.gmap.setCenter([loc.lng, loc.lat]);
        },
        setMapTypeId: function (id) {
          var ids = ['HYBRID', 'TERRAIN', 'SATELLITE', 'ROADMAP'];
          if (ids.indexOf(id) == -1) {
            return;
          }
          var style = 'streets-v11';
          if (id=='HYBRID') {
            style='satellite-streets-v11'
          } else if (id=='TERRAIN'){
            // https://docs.mapbox.com/mapbox-gl-js/example/hillshade/
            style='light-v10'
          } else if (id=='SATELLITE'){
            style='satellite-v9'
          } else if (id=='ROADMAP'){
            style='streets-v11'
          }
          style = 'mapbox://styles/mapbox/'+style
          // this.gmap.setMapTypeId(google.maps.MapTypeId[type]);
          this.gmap.setStyle(style)
        },
        zoomIn: function() {
          // var nz;
          // if ((nz = this.gmap.getZoom() + 1) <= 24) {
          //   return this.gmap.setZoom(nz);
          // }
          this.gmap.zoomIn()
        },
        zoomOut: function() {
          // var nz;
          // if ((nz = this.gmap.getZoom() - 1) >= 0) {
          //   return this.gmap.setZoom(nz);
          // }
          this.gmap.zoomOut()
        },
        getBounds: function() {
          if (this.gmap) {
            this.saveLocation();
            return this.gmap.getBounds();
          }
          return null;
        },
        getIcon: mapParams.getIcon,
        getPriceImg: function(objs,fnImg,selected){
          var tmp = {
            url: '/img/mapmarkers/price.png',
            size: [56, 31],
            origin: [-5, -5.5],
            anchor: 'bottom',
            // anchor: new google.maps.Point(0, 32)
            scaledSize: [45, 25]
          }
          if (!this.isIOS()) {
            tmp.origin = [-5, -5];
          }
          if ('function' == typeof fnImg) {
            var imgObj = fnImg(objs,selected);
            tmp.url = imgObj.url;
            var scaledSize, origin, size, zIndex, addedStyle;
            if (size = imgObj.size) {
              tmp.size = imgObj.size//new google.maps.Size(size[0], size[1]);
            }
            if (scaledSize = imgObj.scaledSize) {
              tmp.scaledSize = imgObj.scaledSize//new google.maps.Size(scaledSize[0], scaledSize[1]);
            }
            if (origin = imgObj.origin) {
              tmp.origin = imgObj.origin//new google.maps.Point(origin[0], origin[1]);
            }
            if (zIndex = imgObj.zIndex) {
              tmp.zIndex = zIndex;
            }
            if (addedStyle = imgObj.addedStyle) {
              tmp.addedStyle = addedStyle
            }
          }
          return tmp;
        },
        createMarker: function(mgName, mkProf, fnIcon,elementType,style) {
          var icon, iconSel;
          if (fnIcon) {
            icon = fnIcon(mkProf.objs,null,mapParams.vueSelf);
            iconSel = fnIcon(mkProf.objs, true,mapParams.vueSelf);
          } else if(this.getIcon) {
            icon = this.getIcon(mkProf.objs,null,mapParams.vueSelf);
            iconSel = this.getIcon(mkProf.objs, true,mapParams.vueSelf);
          }
          var iconOpt = {
            mgName,
            position: [mkProf.lng, mkProf.lat],
            icon: icon,
            map: this.gmap,
            optimized: this.isIOS()
          };
          if (mkProf.draggable) {
            iconOpt.draggable =  true
            // iconOpt.animation = google.maps.Animation.DROP
          }
          if (mkProf.label) {
            iconOpt.label = mkProf.label;
          }
          if (mkProf.url) {
            iconOpt.url = mkProf.url;
          }
          var label,image,imageSel;
          if (mapParams.getLabelFunc && elementType == 'img') {
            label = {
              text: mapParams.getLabelFunc(mkProf.objs,null,mapParams.vueSelf),
              color: (mapParams.labelColor || "white"),
              fontSize: '10px',
            }
            //image = {url:,anchor:,}
            image = this.getPriceImg(mkProf.objs, mapParams.getImgFunc, false);
            imageSel = this.getPriceImg(mkProf.objs, mapParams.getImgFunc, true);
            // console.log('image: ->',image);
            if (image.zIndex) {
              iconOpt.zIndex = image.zIndex;
            }
            iconOpt.label = label;
            iconOpt.icon = image;
          }
          if(elementType){
            iconOpt.elementType = elementType
          }
          if(style){
            iconOpt.style = style
          }
          // mkProf.mkr = new google.maps.Marker(iconOpt);
          mkProf.el = this.generalElementFromOption(iconOpt)
          let markerParams = {anchor:'bottom'}
          if(mkProf.el) {
            markerParams.element = mkProf.el;
          }
          if(mkProf.draggable) {
            markerParams.draggable = true;
          }
          mkProf.mkr = new mapboxgl.Marker(markerParams)
          mkProf.mkr.setLngLat(iconOpt.position).addTo(this.gmap)
          // if (elementType == 'img') {
          mkProf.mkr.setIcon = (icon)=>{
            if ('object' == typeof(icon)) {
              icon = icon.url
            }
            // console.log('set icon:',icon);
            if (mkProf && mkProf.mkr) {
              if (elementType == 'img') {
                mkProf.mkr.getElement().setAttribute('src',icon);
              } else {
                mkProf.mkr.getElement().style.backgroundImage=`url('${icon}')`;
              }
            }
          }
          // }
          if (elementType == 'img') {
            mkProf.mkr.setLabel = (lable)=>{
              if ('object' == typeof(lable)) {
                lable = lable.text
              }
              // console.log(mkProf.mkr, this, mkProf==this);
              // NOTE: curSelMarker moved out of view and freed;
              if (mkProf && mkProf.mkr) {
                mkProf.mkr.getElement().innerHTML = lable;
              }
            }
          }
          mkProf.mkr.iconNor = icon;
          mkProf.mkr.iconSel = iconSel;
          if (label) {
            mkProf.mkr.rmLabel = label;
            mkProf.mkr.rmIcon = image;
            mkProf.mkr.rmIconSel = imageSel;
          }
          var self = this;
          var dragMarkerStart = mapParams.dragMarkerStart || null;
          var dragMarkerEnd = mapParams.dragMarkerEnd || null;
          if(dragMarkerStart) {
            mkProf.mkr.on('drag',function () {
              dragMarkerStart();
            });
          }
          if(dragMarkerEnd) {
            mkProf.mkr.on('dragend',function () {
              dragMarkerEnd(mkProf.ids, mkProf.mkr.getLngLat());
            });
          }
          if(!mkProf.draggable) {
            mkProf.el.addEventListener('click', (e) =>{
              var ref;
              // if selected, change back to unsel state
              // console.log('mgName: ',mgName);
              if ((ref = mapParams.vueSelf.mapObj.curSelMarker) != null) {
                if (ref.rmLabel) {
                  ref.setLabel(ref.rmLabel);
                  ref.setIcon(ref.rmIcon);
                  // console.log('rmLabel', ref.rmLabel, ref.rmIcon);
                } else {
                  ref.setIcon(mapParams.vueSelf.mapObj.curSelMarker.iconNor);
                }
              }
              if (mkProf.mkr.rmLabel) {
                // console.log('rmLabel2', mkProf.mkr.rmLabel, mkProf.mkr.rmIconSel);
                mkProf.mkr.setIcon(mkProf.mkr.rmIconSel);
              } else {
                mkProf.mkr.setIcon(mkProf.mkr.iconSel);
              }
              mapParams.vueSelf.mapObj.curSelMarker = mkProf.mkr;
              if (mapParams.sendMsg) {
                mapParams.sendMsg(mgName + "MarkerClicked", mkProf.ids);
                return e.stopPropagation()
              }
            },false);
          }
          return mkProf.mkr
        },
        removeMarker: function(mkProf) {
          // google.maps.event.clearListeners(mkProf.mkr, 'click');
          // mkProf.mkr.setMap(null);
          // TODO: remove listener?
          // console.log(mkProf);
          // if (mkProf.el) {
          //   var old_element = mkProf.el
          //   var new_element = old_element.cloneNode(true);
          //   old_element.parentNode.replaceChild(new_element, old_element);
          // }
          mkProf.mkr.remove();
          return delete mkProf.mkr;
        },
        triggerClick: function (markerGroupName, id) {
          var mkrs = this.markerGroups[markerGroupName];
          for (var key in mkrs) {
            var v = mkrs[key];
            if (v.ids.indexOf(id) > -1) {
              // console.log(v);
              if (v.el) {
                v.el.click()
              }
              // google.maps.event.trigger(v.mkr, 'click');
              return;
            }
          }
        },
        cluster_key: function(l) {
          return this.round(l.lat) + "," + this.round(l.lng);
        },
        round:function(d, p) {
          if (p == null) {
            p = 5;
          }
          return Math.round(d * Math.pow(10, p)) / Math.pow(10, p);
        },
        setMarkers: function(markerGroupName, objs, fnIcon,elementType,style) {
          // console.log('fnIcon'+fnIcon);
          var i, k, key, len, mgL, mgR, mkObj, obj, v, defaultIDName = mapParams.defaultIDName || '_id';
          if (this.markerGroups == null) {
            this.markerGroups = {};
          }
          // function round(d, p) {
          //   if (p == null) {
          //     p = 5;
          //   }
          //   return Math.round(d * Math.pow(10, p)) / Math.pow(10, p);
          // };
          // function cluster_key (l) {
          //   return round(l.lat) + "," + round(l.lng);
          // };
          mgL = this.markerGroups[markerGroupName] || {};//before
          mgR = {};//after
          //add new list to after
          for (i = 0, len = objs.length; i < len; i++) {
            obj = objs[i];
            key = this.cluster_key(obj);
            mkObj = mgR[key] || {
              key: key,
              lat: obj.lat,
              lng: obj.lng,
              ids: [],
              objs: [],
              draggable: obj.draggable,
              label:obj.label,
              url:obj.url,
            };
            mkObj.ids.push(obj[defaultIDName]);
            mkObj.objs.push(obj);
            mgR[key] = mkObj;
          }
          //sort for comparsion
          for (k in mgR) {
            v = mgR[k];
            v.ids.sort(); // for compare
          }
          //remove dup
          for (k in mgL) {
            v = mgL[k];
            if ((mgR[k] == null) || mgR[k].ids.toString() !== v.ids.toString()) {
              this.removeMarker(v);
              delete mgL[k];
            } else {
              delete mgR[k];
            }
          }
          for (k in mgR) {
            v = mgR[k]; //v = mkProf  .mkr = google.Marker
            this.createMarker(markerGroupName, v, fnIcon,elementType,style);
            delete v.objs;
            mgL[k] = v;
          }
          this.markerGroups[markerGroupName] = mgL;
          // if (window.MarkerClusterer) {
          //   var markers = this.getAllGroupMarkers(markerGroupName);
          //   this.cluster = new MarkerClusterer(this.gmap, markers, {
          //     imagePath: '/img/mapmarkers/m',
          //     maxZoom:15
          //   });
          // }
        },
        getAllGroupMarkers: function (markerGroupName) {
          var allMkrs = this.markerGroups[markerGroupName] || {}, ret = [];
          for (let mkey in allMkrs) {
             let mv = allMkrs[mkey];
             if (mv.mkr) {
               ret.push(mv.mkr);
             }
          }
          return ret;
        },
        clearMarkers: function(markerGroupName, opt={}) {
          var k, mgL, v, sk_id = 'default_skip';
          if (this.markerGroups && (mgL = this.markerGroups[markerGroupName])) {
            if (opt.skip && opt.skip.length) {
              sk_id = opt.skip[0]+'';//TODO: support list
            }
            for (k in mgL) {
              v = mgL[k];
              if ((v.ids[0]+'') == sk_id) {
                continue;
              }
              this.removeMarker(v);
              delete mgL[k];
            }
            if (!(opt.skip && opt.skip.length)) {
              delete this.markerGroups[markerGroupName];
            }
          }
        },
        createOrUpdateMarker:function(markerGroupName,markerObj, loc) {
          if (markerObj.mkr) {
            markerObj.mkr.setLngLat([loc[1],loc[0]]);
          } else {
            markerObj.lat = loc[0];
            markerObj.lng = loc[1];
            this.setMarkers(markerGroupName, [markerObj]);
            let key = this.cluster_key(markerObj);
            markerObj.mkr = this.markerGroups[markerGroupName][key].mkr
          }
        },
        initAutocomplete:function(id, cb) {
          return this.mapbox.initAutocomplete(id,cb);
        },
        displayRoute:function(){
          mapboxTransitService.route()
        },
        setCenter:function({lat,lng}){
          this.mapbox.setCenter([lng, lat]);
        }
      };

      if (mapParams.canGeoCode) {
        ret = Object.assign(ret, this.get_map_geo_fn());
      }
      return ret;

    }
  }
}
