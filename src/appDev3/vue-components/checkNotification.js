
var checkNotification = {
  props: {
    className:{
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isOpenNotify:true,
      hasGoogleService:true,
      canRefresh:false,
      refresh:false,
      dispVar:{
        isEmailVerified:true
      }
    }
  },
  mounted() {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this,bus=window.bus;
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if(d.coreVer){
        var is63NewerVer = self.$parent.isNewerVer(d.coreVer,'6.3.1');
        if(RMSrv.hasGoogleService){
          RMSrv.hasGoogleService(function (ret) {
            self.hasGoogleService = ret;
            if(ret == true){
              self.checkNotify()
              if(is63NewerVer && RMSrv.onAppStateChange){
                RMSrv.onAppStateChange((opt) => {
                  return self.checkNotify();
                })
              }else{
                self.canRefresh = true;
              }
            }
          });
        }
      }
    })
  },
  computed:{
    showRefresh:function(){
      return this.canRefresh && this.refresh;
    },
    computedHasIssueReceivePN(){
      return !this.dispVar.isEmailVerified || !this.hasGoogleService || !this.isOpenNotify;
    }
  },
  methods:{
    isAuthedRet(ret=''){
      return ['authorized','granted'].indexOf(ret) > -1;
    },
    checkNotify(){
      var self = this;
      this.refresh = false;
      RMSrv.onReady(()=>{
        RMSrv.permissions('notification','check',-1,function(resultN){
          self.isOpenNotify = self.isAuthedRet(resultN);
        })
      });
    },
    openSetting(){
      this.refresh = true;
      if(RMSrv.openSettings){
        RMSrv.openSettings();
      }
    },
    openVerifyPopup(){
      var url = '/1.5/settings/verify?verify=v',self = this;
      RMSrv.getPageContentIframe(url,'#callBackString', {transparent:true}, function(val) {
        if (val == ':cancel') {
          return;
        }
        try {
          let ret = JSON.parse(val)
          if(ret.emlV){
            self.dispVar.isEmailVerified = true
          }
        } catch (e) {
        console.error(e)
        }
      });
    },
  },
  template:
  `
<div class="check-notification" v-if='computedHasIssueReceivePN' :class='className'>
  <span class="status fa fa-exclamation-circle" style="color: rgb(255, 205, 0); display: inline;"></span>
  <span class="field">
    <span class="field-name" v-if='!dispVar.isEmailVerified'> {{$_('Email is not verified.')}}
      <span class="explain" @click="openVerifyPopup()">{{$_('Verify')}}</span>
    </span>
    <span class="field-name" v-else-if='!hasGoogleService'> {{$_('Install Google Play to get listing updates.')}}
    </span>
    <span class="field-name" v-else> {{$_('Turn on notifications to get listing updates.')}}
      <span class="explain" @click="checkNotify()" v-if='showRefresh'>{{$_("Refresh")}}</span>
      <span class="explain" @click="openSetting()" v-else>{{$_('Notification Settings')}}</span>
    </span>
  </span>
</div>
  `
};