var flashMessage={data:()=>({hide:!0,block:!1,msg:"",msg1:"",style:null}),mounted(){if(window.bus){var s=window.bus,e=this;s.$on("flash-message",(function(s){s.msg&&s.msg1?(e.msg=s.msg,e.msg1=s.msg1):(e.msg=s,e.msg1="");let l=s.delay||2e3;e.flashMessage(l)}))}else console.error("global bus is required!")},methods:{flashMessage(s=2e3){var e=this;return e.block=!0,e.hide=!1,"close"===s?e.flashMessageClose():isNaN(s)?void 0:setTimeout((function(){return e.flashMessageClose()}),s)},flashMessageClose(s){var e=this;return e.hide=!0,setTimeout((function(){e.block=!1}),500)}},template:'\n  <div class="flash-message-box" :class="{\'hide\':hide, \'block\':block}" :style="style">\n    <div class="flash-message-inner">\n        <div>{{msg}}</div>\n        <div v-if="msg1" style="font-size:13px;margin: 10px -10% 0px -10%;">{{msg1}}</div>\n    </div>\n  </div>\n  '};
