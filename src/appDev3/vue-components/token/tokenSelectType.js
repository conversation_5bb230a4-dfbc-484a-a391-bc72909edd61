var tokenSelectType = {
  props:{
    isshowtoken:{
      type: Boolean,
      default: true
    },
    isshowtags:{
      type: Boolean,
      default: false
    },
  },
  data(){
    return{
      TokenType: [],
      dealTokenType: [],
      selectedType: '',
      billType: [{
        k: 'fullBill',
        v: 'Statement'
      },{
        k: 'summary',
        v: 'Summary'
      }],
      userInfo: {
        eml:'',
        uid: ''
      },
      billTypeSelected: 'fullBill',
      balance: '-',
      uids:[]
    }
  },
  mounted(){
    let self = this;
    this.TokenType = decodeJsonString2Obj('TOKEN_TYPES','array');
    this.userInfo = decodeJsonString2Obj('userInfo');
    if(this.userInfo.uid){
      this.uids.push(this.userInfo.uid);
    }
    this.TokenType.forEach((ele)=>{
      if(ele.type == 'Token'){
        this.dealTokenType.push({k:'',v:ele.type,t:'-'});
      }else{
        this.dealTokenType.push({k:ele.type,v:ele.type,t:'-'});
      }
    });
    this.selectedType = '';
    bus.$on('tokenType-preSelected',(val) => {
      self.selectedType = val.selectedType;
      if(val.token){
        let index = self.dealTokenType.findIndex((ele)=>{
          return ele.k == val.selectedType
        });
        if(index > -1){
          self.dealTokenType[index].t = val.token;
          self.balance = self.dealTokenType[index].t;
        }
      }
      this.$nextTick(()=>{
        self.scrollBar();
      });
    });
    bus.$on('member-changed',(val) => {
      if(val){
        self.uids = val;
        if(self.uids.length == 1){
          self.userInfo = self.uids[0];
        } else {
          self.userInfo = {eml:'',uid: ''}
        }
        self.getTokensBalance();
      }
    });
    if(this.isshowtoken){
      this.getTokensBalance();
    }
    window.addEventListener('load',()=>{
      this.scrollBar();
    })
  },
  methods:{
    selectType(type){
      if(this.selectedType == type.k) return;
      this.selectedType = type.k;
      this.$nextTick(()=>{
        this.scrollBar();
      });
      this.getShowBalanceValue();
    },
    getShowBalanceValue(){
      this.balance = '-';
      let index = this.dealTokenType.findIndex((ele)=>{
        return ele.k == this.selectedType;
      });
      if(index > -1){
        this.balance = this.dealTokenType[index].t;
      }
      bus.$emit('tokenType-select',{selectedType:this.selectedType,billTypeSelected: this.billTypeSelected,token:this.dealTokenType[index].t});
    },
    getTokensBalance(){
      let self = this;
      setLoaderVisibility('block');
      let body = {isHistory:true};
      if(this.uids.length){
        body.uids = this.uids;
      }
      fetchData('/token/getTokenBalance',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        if (!ret.result){
          return bus.$emit('flash-message','Not result');
        }
        if(!ret.result.hasRecord){
          self.dealTokenType.forEach((ele)=>{
            ele.t = '-';
          });
          this.balance = '-';
          bus.$emit('tokenType-select',{selectedType:this.selectedType,billTypeSelected: this.billTypeSelected,token:'-'});
          return;
        }
        let tokenDate = ret.result.tokens;
        self.dealTokenType.forEach((ele)=>{
          let index = tokenDate.findIndex((value)=>{
            return value.tp == ele.k
          });
          if(index != -1){
            ele.t = tokenDate[index].token;
          } else {
            ele.t = '-';
          }
        });
        this.getShowBalanceValue();
      });
    },
    selectBillType(type){
      this.billTypeSelected = type.k;
      bus.$emit('tokenType-select',{selectedType:this.selectedType, billTypeSelected: this.billTypeSelected,token:type.t});
    },
    scrollBar(){
      let navLinksContainer = document.querySelector('.list-nav-container');
      if (!navLinksContainer) {
        return;
      }
      let navLinkSelected = document.querySelector('.list-nav-link.selected');
      if(!navLinkSelected) return;
      let selectedWidth = navLinkSelected.offsetWidth;
      let navListWidth = window.innerWidth;
      let scrollPos = navLinkSelected.offsetLeft + selectedWidth / 2 - navListWidth / 2;
      navLinksContainer.scrollLeft = scrollPos;
      let navLinkActive = document.querySelector('.list-nav-active');
      navLinkActive.style.left = navLinkSelected.offsetLeft + selectedWidth /2 - 15 + 'px';
    }
  },
  template: `
  <div id="tokenSelectType">
    <nav class="tabs-list-nav">
      <div class="list-nav-container">
        <div class="list-nav-active"></div>
        <a v-for='type in dealTokenType' class="list-nav-link" :class="{'selected':type.k == selectedType}" href="javascript:;" @click="selectType(type)">{{ $_(type.v) }}</a>
      </div>
    </nav>
    <div class="tags" v-if='isshowtags'>
      <div>
        <span class="tag" v-for='type in billType' @click='selectBillType(type)' :class='{active:type.k == billTypeSelected}'>
          <span>{{ $_(type.v) }}</span>
        </span>
      </div>
      <div style="font-size:13px;" v-if='billTypeSelected !== "summary"'>{{ $_('Balance') }}:<span v-show="selectedType == 'Credit Report'">$&nbsp;</span>{{balance}}</div>
    </div>
  </div>
  `
}
