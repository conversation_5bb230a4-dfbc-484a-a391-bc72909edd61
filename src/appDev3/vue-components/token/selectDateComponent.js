var selectDateComponent = {
  props:{
    datelist:{
      type: Array,
      default: () => []
    }
  },
  data(){
    return{
      selectedDate:''
    }
  },
  mounted(){
    let self = this;
    bus.$on('pre-selected-date',function(d){
      self.selectedDate = d;
    });
  },
  methods:{
    emitSelectList(needDate,date){
      let param = {
        showDate:false
      }
      if(needDate){
        this.selectedDate = date
        param.selectedDate = this.selectedDate;
      }
      bus.$emit("selectDate-list",param);
    }
  },
  template: `
  <div id="selectDateComponent">
    <div class="backdrop" @click='emitSelectList()'></div>
    <div class="selectDateDiv">
      <div class="dateContentDiv">
        <div v-for="(time,row) in datelist" style="height:39px;border-radius:4px;" :class='{selectActive:selectedDate == time}' @click='emitSelectList(true, time)'>
          <div class="disPlayFlexDiv">{{time}}</div>
        </div>
      </div>
    </div>
  </div>
  `
}
