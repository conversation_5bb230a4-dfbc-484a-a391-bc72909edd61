var tokenUserInfo = {
  props:{
    userinfo:{
      type: Object,
      default:{
        eml:'',
        avt: '',
        fnm:'',
        token: '-',
        uid: ''
      }
    },
    isshowmodify:{
      type: Boolean,
      default: false
    },
    selectedtype:{
      type: String,
      default: ''
    }
  },
  data(){
    return{
    }
  },
  mounted(){
  },
  methods:{
    goModifyToken(){
      let self = this;
      let url = `/token/edit?uid=${this.userinfo.uid}&tokenType=${this.selectedtype}`;
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          let result = JSON.parse(val);
          self.userinfo.token = result.token;
          bus.$emit("token-changed",result);
        } catch (e) {
          console.error(e);
        }
      });
    },
    gotoHistory(){
      let self = this;
      let url = `/token/history?uid=${this.userinfo.uid}&type=admin&tokenType=${this.selectedtype}`;
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          let result = JSON.parse(val);
          if(result.selectedType !== self.selectedtype){
            self.selectedtype = result.selectedType;
            bus.$emit('tokenType-preSelected',{selectedType:result.selectedType,token:false});
          }
          bus.$emit('tokenType-select',{selectedType:result.selectedType});
        } catch (e) {
          console.error(e);
        }
      });
    }
  },
  template: `
  <div id="tokenUserInfo">
    <div class="userInfo">
      <img class="headPic" :src="userinfo.avt || '/img/user-icon-placeholder.png'" @error="e => { e.target.src = '/img/user-icon-placeholder.png' }">
      <div class="basicInfo">
        <div class="userNames">{{userinfo.fnm}}</div>
        <div>{{ $_('Token','token') }}: <span v-show="selectedtype == 'Credit Report' && userinfo.token !== '-'">$&nbsp;</span>{{ userinfo.token }}</div>
        <div class="textEllipsis">{{userinfo.eml}}</div>
      </div>
      <button v-if="isshowmodify" class="searchEmailBtn" style="height:30px;font-size:14px;margin-right:5px;" @click.stop="gotoHistory(list)">{{$_('Bill','token')}}</button>
      <button v-if="isshowmodify" class="searchEmailBtn" @click.stop="goModifyToken()" style="height:30px;font-size:14px;">{{$_('Modify','token')}}</button>
    </div>
  </div>
  `
}
