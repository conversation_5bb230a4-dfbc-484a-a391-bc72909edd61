/*
hist: {
  addr,city,cnty,hist,lat,lng,prov,st,tracked,_id
}
*/
var latestCard = {
  props:['hist'],
  mounted() {
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    getTime(ts) {
      return formatDate(ts);
    },
    openResultModal(e) {
      var result={
        e:e,
        hist:this.hist,
        fromhist:true,
      }
      window.bus.$emit('openResult',result);
    }
  },
  template: `
  <div class="sp-addr">{{hist.addr}}</div>
  <div class="bdrms"><span v-show="hist.hist.bdrms != null"><span>{{hist.hist.bdrms}}</span><span class="fa fa-rmbed"></span></span><span v-show="hist.hist.bthrms != null"><span>{{hist.hist.bthrms}}</span><span class="fa fa-rmbath"></span></span><span v-show="hist.hist.gr != null"><span>{{hist.hist.gr}}</span><span class="fa fa-rmcar"></span></span><span v-show="hist.hist.sqft != null"><span>{{hist.hist.sqft}} {{getTranslate('sqft')}}</span></span></div>
  <div class="price-wrapper">
      <div class="price">{{$filters.currency(hist.hist.result.p,'$',0)}}</div>
      <div class="text">{{getTime(hist.hist.ts)}}<span style="padding-left:10px;">{{getTranslate('Evaluated')}}</span></div>
  </div>
  <div class="btn btn-green btn-right" @click="openResultModal($event)">{{getTranslate('Detail')}}</div>
  `
};