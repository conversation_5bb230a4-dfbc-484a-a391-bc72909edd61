var pageDataMixins={created:function(){},data:()=>({cacheList:["propSortMethods","propPtypes","domFilterVals","bsmtFilterVals"]}),methods:{isNewerVer:(e="5.3.0",r)=>"appDebug"==e||(e=e.split("."),r=r.split("."),parseInt(e[0])>parseInt(r[0])||(parseInt(e[0])==parseInt(r[0])&&parseInt(e[1])>parseInt(r[1])||parseInt(e[0])==parseInt(r[0])&&parseInt(e[1])==parseInt(r[1])&&parseInt(e[2])>=parseInt(r[2]))),processPostError(e){if(e.needLogin){if(RMSrv.closeAndRedirectRoot)return void RMSrv.closeAndRedirectRoot("/1.5/user/login");document.location.href="/1.5/user/login"}else RMSrv.dialogAlert(e.e)},loadJsSerial(e,r){let a=this,t=function(o){(o=e.shift())?a.loadJs(o.path,o.id,(function(){t()})):r()};t()},loadJs(e,r,a){if(!this.hasLoadedJs(r)&&e&&r){var t=document.createElement("script");t.type="application/javascript",t.src=e,t.id=r,a&&(t.onload=a),document.body.appendChild(t)}},loadCss(e,r){if(e&&r){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",a.href=e,a.id=r,document.body.appendChild(a)}},loadJSString(e,r){if("string"==typeof e){var a=document.createElement("script"),t=document.createTextNode(e);a.id=r,a.appendChild(t),document.body.appendChild(a)}},setCookie(e,r,a){var t=new Date;t.setTime(t.getTime()+24*a*60*60*1e3);var o="expires="+t.toUTCString();document.cookie=e+"="+r+"; "+o+"; path=/"},readCookie(e){for(var r=e+"=",a=document.cookie.split(";"),t=0;t<a.length;t++){for(var o=a[t];" "==o.charAt(0);)o=o.substring(1,o.length);if(0==o.indexOf(r))return o.substring(r.length,o.length)}return null},getCachedDispVar(){if(!localStorage.dispVar)return{};try{return JSON.parse(localStorage.dispVar)}catch(e){return console.error(e),{}}},saveCachedDispVar(e){if(!e)return!1;var r=this.getCachedDispVar();try{var a=Object.assign(r,e),t={};for(let e in a)this.cacheList.indexOf(e)>-1&&(t[e]=a[e]);localStorage.dispVar=JSON.stringify(t)}catch(e){return console.error(e),!1}},hasLoadedJs:e=>document.querySelector("script#"+e),dynamicLoadJs(e){var r=this,a="jsGmapUrl",t="jsCordova",o="jsWechat",s="wxConfig";if(e[a]&&!r.hasLoadedJs(a)){var i=e[a]+(window.gMapsCallback?"&callback=gMapsCallback":"");r.loadJs(i,a)}if(e[t]&&!r.hasLoadedJs(t+"0")&&Array.isArray(e[t]))for(var n=0;n<e[t].length;n++){var d=e[t][n],l=t+n;r.loadJs(d,l)}if(e[o]&&!r.hasLoadedJs(o)){if(!Array.isArray(e[t]))return;if(r.loadJs(e[o][0],o),e[s]){var c=JSON.stringify(e[s]);r.loadJSString("var wxConfig = "+c+";","wxConfig"),setTimeout((function(){r.loadJs(e[o][1],o+"1")}),800)}}},filterDatasToPost(e,r){if(Object.keys(e).length)for(var a=r.length-1;a>-1;){var t=r[a];e.hasOwnProperty(t)&&r.splice(a,1),a--}},loadJsBeforeFilter(e,r=[]){var a={};for(let t of["jsGmapUrl","jsWeixinUrl","jsWeixinRMsrv"])r.indexOf(t)>-1&&(a[t]=e[t]);this.dynamicLoadJs(a)},emitSavedDataBeforeFilter(e,r){var a={},t=window.bus;for(let t of r)e.hasOwnProperty(t)&&this.cacheList.indexOf(t)>-1&&(a[t]=e[t]);t.$emit("pagedata-retrieved",a)},getPageData:function(e,r={},a=!1){var t,o=this;if(Array.isArray(e)){if(!(t=window.bus))return console.error("global bus required!");var s=o.getCachedDispVar();o.loadJsBeforeFilter(s,e),o.emitSavedDataBeforeFilter(s,e),a||o.filterDatasToPost(s,e);var i={datas:e},n=Object.assign(i,r);fetchData("/1.5/pageData",{body:n},(function(e,r){if(e||r.err)return RMSrv.dialogAlert(e||r.err);o.dynamicLoadJs(r.datas),o.saveCachedDispVar(r.datas),t.$emit("pagedata-retrieved",r.datas)}))}else console.error("datas not array")},isForumFas(e,r){var a=!1;if(e.sessionUser){var t=e.sessionUser.fas;t&&t.forEach((function(e){(e.city&&e.city==r.city||!e.city&&e.prov==r.prov)&&(a=!0)}))}return a},getCachedForumCity(){if(!localStorage.forumCity)return{};try{return JSON.parse(localStorage.forumCity)}catch(e){return console.error(e),{}}},saveCachedForumCity(e){if(!e)return!1;try{localStorage.forumCity=JSON.stringify(e)}catch(e){return console.error(e),!1}}}};
