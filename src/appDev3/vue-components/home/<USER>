
var MatchListPopup = {
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {}
      }
    },
    log:{
      type:Object,
      default:function () {
        return {
          src:[]
        }
      }
    },
  },
  data () {
    return {
      savesUrl:{
        Saved:'properties',
        'Saved Search':'searches',
        Community:'communities',
        Location:'locations',
      }
    };
  },
  mounted(){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
  },
  methods: {
    toggleModal(){
      this.$parent.clickMask()
    },
    goEdit(status) {
      var url = ''
      if(status == 'Building'){
        status = 'Location'
      }
      if(status == 'Showing'){
        url = '/1.5/showing'
      }else{
        url = `/1.5/saves/${this.savesUrl[status]}`
      }
      url += '?d=/home/<USER>'
      location.href = url
    },
  },
  template: `
<div id='MatchListPopup' class='modal modal-60pc'>
  <header class="bar bar-nav">
    {{$_('Watchlist')}}
    <span class="icon icon-close pull-right" @click='toggleModal()'></span>
  </header>
  <div class='content'>
    <ul class="table-view" v-for="(log,idx) in log.pnSrc">
      <div class='tag'>
        <span>{{log.transK}}</span>
        <span class='pull-right link' @click='goEdit(log.k)'>{{$_('EDIT')}}</span>
      </div>
      <li class='folder' v-if='typeof(log.v)=="string"'>{{log.v}}</li>
      <li v-for="(v,idx) in log.v" class='folder' v-else>
        <div v-if='v.nm'>
         {{v.nm}}
         <div class='clnt' v-show='v.cNm'>
          <span class='sprite16-18 sprite16-4-6'></span>
          {{v.cNm}}
         </div>
        </div>
        <div v-else>{{v}}</div>
      </li>
    </ul>
  </div>
</div>
`
};
