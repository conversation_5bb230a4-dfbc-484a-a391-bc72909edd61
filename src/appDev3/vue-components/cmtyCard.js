/*
cmty: {
  _id,nm,city,pr,cnty,isFav
}
define bus.$on event in parent vue to use emit function

bus.$on('favCmty',function(cmty) {
  
});
bus.$on('clickCmty',function(cmty) {
  
});
*/
var cmtyCard = {
  props:['cmty','idx'],
  mounted() {
  },
  methods:{
    fav() {
      this.cmty.idx = this.idx;
      window.bus.$emit('favCmty',this.cmty);
    },
    click() {
      window.bus.$emit('clickCmty',this.cmty);
    }
  },
  template: `
  <div class="cmty" @click="click()">
    <div class="cmty-name">
      <div class="nm">{{cmty.nm}}</div>
      <div class="loc">{{cmty.city}} {{cmty.pr}} {{cmty.cnty}}</div>
    </div>
    <div class="fa" :class="{'fa-heart-o':!cmty.isFav, 'fa-heart':cmty.isFav}" @click.stop="fav()"></div>
  </div>
  `
};