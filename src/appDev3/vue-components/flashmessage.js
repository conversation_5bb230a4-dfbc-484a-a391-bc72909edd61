var flashMessage ={
  data () {
    return {
      hide:true,
      block:false,
      msg:'',
      msg1:'',
      style:null
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    bus.$on('flash-message', function (m) {
      if(m.msg && m.msg1) {
        self.msg = m.msg;
        self.msg1 = m.msg1;
        // self.style = "width:250px;margin-left:-125px;";
      } else {
        self.msg = m;
        self.msg1='';
      }
      let delay = m.delay || 2000;
      self.flashMessage(delay);
    });
  },
  methods: {
    flashMessage (delay=2000) {
      var self = this;
      self.block = true;
      self.hide = false;
      if (delay === 'close') {
        return self.flashMessageClose();
      } else if (!isNaN(delay)) {
        return setTimeout((function() {
          return self.flashMessageClose();
        }), delay);
      }
    },
    flashMessageClose (ele) {
      var self = this;
      self.hide = true;
      return setTimeout((function() {
        self.block = false;
      }), 500);
    }
  },
  template:
  `
  <div class="flash-message-box" :class="{'hide':hide, 'block':block}" :style="style">
    <div class="flash-message-inner">
        <div>{{msg}}</div>
        <div v-if="msg1" style="font-size:13px;margin: 10px -10% 0px -10%;">{{msg1}}</div>
    </div>
  </div>
  `
};