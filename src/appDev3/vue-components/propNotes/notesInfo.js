var notesInfo = {
  props:{
    noteinfo:{
      type: Object,
      default:{
        memo:[],
        pic:[],
        avt: '',
        nm_zh: '',
        nm_en: ''
      }
    },
    isshowicon:{
      type: String
    },
    propid:{
      type: String,
      default: ''
    },
    dispvar:{
      type: Object,
      default:{
        isRealGroup: false,
        isApp:false,
        isNoteAdmin:false,
        lang:'en',
      }
    },
    isshowmore:{
      type: Boolean,
      default:false
    },
    noteindex:{
      type:Number,
      default:0
    }
  },
  data(){
    return{
      showDelete: false,
      showMore: false,
      sizes:[],
      EDIT:'edit',
      PRAISE:'praise',
    }
  },
  mounted(){
    var self = this;
    this.$nextTick(()=>{
      self.judgeShowMore();
    });
  },
  methods:{
    judgeShowMore(){
      if(this.isshowmore){
        let elementH = document.querySelector('.noteinfoH'+this.noteindex).clientHeight
        if(elementH >=76){ // 76:显示三行
          this.showMore = true;
          return;
        }
        this.showMore = false;
      }
    },
    deleteNote(){
      let self = this;
      setLoaderVisibility('block');
      fetchData('/1.5/notes/delete',{body:{id:this.noteinfo._id,propId: this.propid}},(err,ret)=>{
        setLoaderVisibility('none');
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        if(ret.tokenMsg && ret.tokenMsg.length){
          bus.$emit('flash-message',this.$_(ret.tokenMsg));
        }
        this.showDelete = false;
        bus.$emit("update-note",{model:'delete',id:this.noteinfo._id});
      });
    },
    editNote(){
      if(!this.noteinfo.uaddr || this.noteinfo.uaddr == 'undefined'){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
      }
      if(!this.propid || this.propid == 'undefined'){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
      }
      let self = this;
      fetchData('/1.5/notes/findListingByID',{body:{id:this.propid}},(err,ret)=>{
        if (!ret.ok) {
          return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
        }
        let getUaddr = ret.result.uaddr;
        if (!getUaddr || getUaddr == 'undefined'){
          return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
        }
        let url = `/1.5/notes/editNotes?propId=${this.propid}&uaddr=${encodeURIComponent(getUaddr)}&id=${this.noteinfo._id}`;
        if(!(self.dispvar.isRealGroup || self.dispvar.isNoteAdmin) && ret.result.unt){
          url += `&unt=${encodeURIComponent(ret.result.unt)}`
        }
        let cfg = {toolbar:false};
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
          try {
            let info = JSON.parse(val);
            if(info.type == 'delete'){
              return window.bus.$emit("update-note",{model:'delete',id:self.noteinfo._id});
            }
            let note = info.note;
            self.noteinfo = self.noteinfo ? Object.assign(self.noteinfo,note) : note
            if (!note.memo){
              self.noteinfo.memo = []
            }
            if (!note.pic){
              self.noteinfo.pic = []
            }
          } catch (e) {
            console.error(e);
          }
        });
      })
    },
    topArticle(){
      let info = {
        _id: this.noteinfo._id
      }
      info.topMt = this.noteinfo.topMt ? '': new Date();
      fetchData('/1.5/notes/update',{body:{noteDetail:info,type:this.PRAISE,propId:this.propid}},(err,ret)=>{
        if (!ret.ok) {
          return bus.$emit('flash-message','Error');
        }
        if(ret.tokenMsg && ret.tokenMsg.length){
          bus.$emit('flash-message',this.$_(ret.tokenMsg));
        }
        this.noteinfo = Object.assign(this.noteinfo,ret.result);
        if(!ret.result.topMt && this.noteinfo.topMt) delete this.noteinfo['topMt'];
        bus.$emit("update-note",{model:this.PRAISE,id:this.noteinfo._id,isPraise:this.noteinfo['topMt']});
      });
    },
    setUpPreview (evt) {
      var h, w, self=this, el=evt.target, idx;
      if (el) {
        if (/^data:image/.test(el.src)) {
          return;
        }
        w = el.naturalWidth;
        h = el.naturalHeight;
        el.setAttribute('data-size', w + 'x' + h);
        idx = parseInt(el.getAttribute('dataindex'));
        if (!self.sizes[idx]) {
          self.sizes[idx]=(w + 'x' + h);
        }
        if(el.classList.contains('detail-photo') && h > w){
          el.classList.add('vertical')
        }
      }
    },
    removePicSize(l=[]){
      let ret = [];
      l.forEach((i) => {
        ret.push(i.replace(/(\/(414|768|828)\.)/,'/'))
      });
      return ret;
    },
    previewPic (evt) {
      var compressed, index, text, url, self=this, el=evt.target||evt.srcElement;
      index = parseInt(el.getAttribute('dataindex'));
      text = JSON.stringify({
        urls: self.removePicSize(self.noteinfo.pic),
        index: index,
        sizes: self.sizes
      });
      if (!LZString) {
        return console.error('LZString is required!');
      }
      compressed = LZString.compressToEncodedURIComponent(text);
      url = '/1.5/SV/photoScroll?data=' + compressed;
      if (!window.RMSrv && !this.inFrame) {
        return;
      }
      RMSrv.openTBrowser(url,{nojump:true,title:self.$_('RealMaster')});
    },
    confirmDelete(){
      let self = this;
      let deleteTip = self.$_('Are you sure you want to delete this note?');
      let cancel = self.$_('Cancel');
      let yes = self.$_('Yes');
      function _doDelete(idx) {
        if (idx+'' == '2') {
          self.deleteNote();
        }
        if (idx+'' == '1') {
          self.showDelete = false;
        }
      }
      return RMSrv.dialogConfirm(deleteTip, _doDelete, '', [cancel, yes]);
    }
  },
  template: `
  <div id="notesInfo" v-if="noteinfo != null">
    <div style="margin-bottom:10px;" v-if="isshowicon">
      <img v-if="isshowicon == EDIT || dispvar.isNoteAdmin || isshowicon == 'complex'" class="headPic" :src="noteinfo.avt || '/img/user-icon-placeholder.png'" @error="e => { e.target.src = '/img/user-icon-placeholder.png' }">
      <img v-else class="headPic" src="/img/user-icon-placeholder.png">
      <div class="userInfo">
        <div class="userNames" v-if="(isshowicon == EDIT || dispvar.isNoteAdmin) && (noteinfo.nm_en || noteinfo.nm_zh || noteinfo.nm)">{{dispvar.lang == 'en'? (noteinfo.nm_en || noteinfo.nm) : (noteinfo.nm_zh || noteinfo.nm)}}</div>
        <div v-else style="height: 21px;"></div>
        <div class="updateTime">
          <span v-show="isshowicon == 'praise' && noteinfo.topMt" class="topTag">{{$_('TOP')}}</span>
          {{noteinfo.mt}}
        </div>
      </div>
      <div style="float: right;margin-top: 15px;">
        <div v-if="isshowicon == EDIT">
          <span class="btn btn-nooutline pull-right" style="padding:0px;margin-top:-3px;" v-show="showDelete">
            <span class="pull-right btn btn-nooutline" style="border:1px solid #fff;" @click.stop.prevent="showDelete = false">{{$_('Cancel')}}</span>
            <span class="pull-right btn btn-negative" @click.stop.prevent="confirmDelete()">{{$_('Delete')}}</span>
          </span>
          <div v-show="!showDelete">
            <a href="javascript:;" style="margin-right:20px;font-size: 15px;" @click="editNote">
              <span class="sprite16-18 sprite16-5-3"></span>
              <span style="margin-left: 5px;vertical-align: top;">{{$_('Edit')}}</span>
            </a>
            <span class="sprite16-18 sprite16-4-8" style="color:rgba(17,17,17,.6)" @click="showDelete = true"></span>
          </div>
        </div>
        <span v-if="isshowicon == PRAISE && dispvar.isNoteAdmin" :class="noteinfo.topMt? 'fa fa-rmliked':'fa fa-rmlike'" :style="noteinfo.topMt? 'color:#428bca' : 'color:#111111'" @click="topArticle()"></span>
      </div>
    </div>
    <div>
      <div :class="'noteinfoH'+ noteindex" :class="showMore && isshowmore? 'fold':''">
        <div v-for="(item,index) in noteinfo.memo" style="padding-bottom:10px;">
          <span v-show="item.tag !== 'noTag'" class="tagTitle fontSize15">[{{item.tag}}]&nbsp;</span>
          <span class="tagNotes fontSize15">{{item.m}}</span>
        </div>
      </div>
      <div v-if="isshowmore && showMore" style="color:#428bca;font-size:12px" @click="showMore = false">{{$_('More')}}</div>
    </div>
    <div class="pic-table marTop5" v-show="noteinfo.pic && noteinfo.pic.length>0">
      <img class="image" v-for="(src,$index) in noteinfo.pic" :src="src" :dataindex="$index" @click="previewPic($event)" @load="setUpPreview">
    </div>
    <div style="display:none">{{$_('Edit')}}{{$_('Delete')}}{{$_('TOP')}}{{$_('More')}}{{$_('Yes')}}</div>
  </div>
  `
}
