var tagInfo = {
  props:{
    notestag:{
      type: Array,
      default: () => []
    },
    editflag:{
      type: String,
      default:''
    },
    isapp:{
      type: Boolean,
      default: true
    }
  },
  data(){
    return{
      tagText: [],
    }
  },
  mounted(){
    this.tagText = decodeJsonString2Obj('TAGTEXT','array');
  },
  methods:{
    reset(){
      bus.$emit("noteTag-list",false);
    },
    // 编辑模式：点击非高亮tag后关闭窗口，点击高亮tag给出提示；
    // 列表模式：tag在高亮和非高亮之间切换
    // 地址选择模式：tag在高亮和非高亮之间转换，并且多选之后手动关闭
    chooseTag(value){
      if(this.editflag != 'uaddrTag'){
        if (this.editflag == 'editTag' && this.notestag.includes(value)) { // 笔记编辑模式下tag已被选中
          return window.bus.$emit('flash-message',this.$_('Tag already exists'));
        }
        bus.$emit("noteTag-list",value);
      } else { // 选择地址界面的tag可以一次选多个之后在关闭界面
        let index = this.notestag.indexOf(value)
        if(index !== -1) {
          this.notestag.splice(index,1);
        }else{
          this.notestag.push(value);
        }
      }
    },
  },
  template: `
  <div id="tagInfo">
    <flash-message></flash-message>
    <div :class="['backdrop', { webShow: !isapp }]" @click='reset()'></div>
    <div :class="['tagSelect', { webShow: !isapp }]">
      <div v-for="(tag,row) in tagText">
        <button v-for="(item,col) in tag" class="tagText" :class="notestag.includes(item)?'choose':'noChoose'" @click="chooseTag(item)">{{item}}</button>
        <hr>
      </div>
    </div>
  </div>
  `
}
