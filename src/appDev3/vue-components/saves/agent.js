
var agent = {
  props:{
    showFav:{
      type:Boolean,
      default:'true'
    },
    list:{
      type:String,
      default:'resultList'
    },
    u:{
      type:Object,
    },
    dispVar:{
      type:Object,
      default(){
        return {
          isProdSales:false,
        }
      }
    },
    index:{
      type:Number
    },
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;

  },
  computed:{
    computedSplang:function(){
      var u = this.u || {};
      // if (u && Array.isArray(u.splang)) {
      //   return u.splang.join(" ")
      // }
      return u.splang || '';
    },
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    toggleSMB () {
      var backdrop = document.getElementById('contactBackdrop'),
          body = document.querySelector('body');
      body.classList.toggle('smb-open');
      if (backdrop) {
        var disp = backdrop.style.display === 'none' ? 'block' : 'none';
        return backdrop.style.display = disp;
      }
    },
    computeSrc(u){
      if (u.avt) {
        return u.avt;
      }
      return '/img/icon_nophoto.png';
    },
    showWecard(r){
      window.bus.$emit('show-wecard', r)
    },
    setCurRealtor(r){
      window.bus.$emit('set-cur-realtor', r)
    },
    setFold(u,idx,arr){
      window.bus.$emit('set-fold', {u,idx,arr})
    },
    fav(u){
      window.bus.$emit('toggleFav', {fav:!u.isfaved,uid:u._id})
    }
  },
  watch:{
  },
  template: `
 <div>
  <div class='avt'>
    <img class="media-object pull-left" :src="computeSrc(u)" @click="showWecard(u)" />
    <span class="sprite16-24" :class="{'sprite16-1-3':!u.isfaved, 'sprite16-2-3':u.isfaved}" v-show='showFav'
    @click.stop.prevent="fav(u)"></span>
  </div>
  <div class="media-body">
    <div>
      <div class="detail-wrapper" @click="showWecard(u)">
        <span class="nm cut">{{ u.fnm }}</span>
        <span class="reco" v-show="u.vip">{{getTranslate("RECO")}}</span>
        <span class="cert" v-show="u.cert">{{getTranslate("CERT")}}</span>
        <p class="size13 cut">{{u.cpny_pstn}}</p>
        <p class="size13 cut">{{ u.cpny }}</p>
        <p class="size13 cut"><span v-if="computedSplang">{{computedSplang}} | &nbsp;</span><span
            v-show="u.lic">{{getTranslate('LIC')}} {{u.lic}}</span></p>
      </div>
      <div class="pull-right add-wrapper"><button class="btn btn-positive"
          @click="setCurRealtor(u);">{{getTranslate("Contact")}}</button><button class="btn btn-positive" v-if="dispVar.isProdSales"
          @click="showUserStats(u);">{{getTranslate("Stats")}}</button></div>
    </div>
    <div class="itrWrapper" v-if="u.itr">
      <p :class="{'fold':u.fold}">{{ u.itr }}</p>
      <p class="showMore" @click="setFold(u, index, list)" v-show="u.fold"><span
          class="pull-right fa fa-caret-down"></span></p>
      <p class="wx" v-show="u.wx">{{getTranslate("WeChat")}}: {{u.wx}}</p>
    </div>
  </div>
</div>
  `
};