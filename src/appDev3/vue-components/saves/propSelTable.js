var propSelTable = {
  props:{
    favProps:{
      type: Array,
      default: () => []
    },
    showPropTable:{
      type: Boolean,
      default:false
    },
    action:{
      type:String,
      default:''
    },
    propImgHeight:{
      type:Number,
      default:0
    },
    from:{
      type:String,
      default:''
    },
    currentGrp:{
      default:function(){
        return {
          idx: "0",
          val: {v: "Default"}
        }
      }
    },
    grps:{
      type: Array,
      default: () => []
    }
  },
  data(){
    return{
      selectedProp:[],//选择需要分享或收藏的prop
      strings:{
        searchID:{key:"Search",ctx:''},
        alreadyExists:{key:"The current property already exists",ctx:''},
        saveFailed:{key:"Failed",ctx:''},
        noResults:{key:"No Results",ctx:''},
        showing: {key:"+ Showing",ctx:''},
        share: {key:"Share",ctx:''},
        cma:{key:"CMA",ctx:''},
        rm:{key:"RealMaster",ctx:''},
        'NO_SELECTION':{key:"No Selection",ctx:''}
      }
    }
  },
  mounted(){
    if (!window.bus) {
      return console.error('global bus required!');
    }
    window.bus.$on('reset-sel-prop',()=>{
      this.selectedProp = []
    })
  },
  methods:{
    reset(){
      window.bus.$emit('reset');
    },
    selectProp(id){
      var self = this;
      var idx = self.selectedProp.indexOf(id);
      var prop = self.favProps.find(p => p._id == id)
      if(idx >= 0){
        self.selectedProp.splice(idx,1);
        prop.selected = false;
      }else{
        self.selectedProp.push(id);
        prop.selected = true;
      }
      self.$forceUpdate();
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    parseSqft(sqft){
      if (/\-/.test(sqft)) {
        return sqft;
      }
      if ('number' == typeof sqft) {
        sqft = ''+sqft;
      }
      if (/\./.test(sqft)) {
        return sqft.split('.')[0];
      }
      return sqft;
    },
    getPropdetail(grp,id){
      var self = this;
      var url = '/1.5/props/detail';
      fetchData(url,{body:{_id:id,useThumb:true}},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          if (ret.detail){
            let propDetail = ret.detail
            propDetail.fav = true;
            if(!propDetail.thumbUrl){
              propDetail.thumbUrl = propDetail.picUrls[0];
            }
            propDetail.selected = true;
            if(propDetail.favGrp){
              propDetail.favGrp.push(grp)
            }else{
              propDetail.favGrp=[grp]
            }
            self.favProps.unshift(propDetail)
          } else {
            window.bus.$emit('flash-message',self.$_(self.strings.noResults.key,self.strings.noResults.ctx));
          }
        }
      })
    },
    openAutoCompleteSearch(){
      var self = this;
      var url = '/1.5/autoCompleteSearch?isPopup=1&isMLS=1';
      // color close
      RMSrv.getPageContent(url,'#callBackString', {hide:false,title:self.$_(self.strings.searchID.key,self.strings.searchID.ctx)}, function(val) {
        if (val == ':cancel') {
          return;
        }else{
          var ret = JSON.parse(val);
          var id = ret.id;
          var idProp = self.favProps.find(p => p._id == id)
          if(idProp){
            window.bus.$emit('flash-message', self.$_(self.strings.alreadyExists.key,self.strings.alreadyExists.ctx));
          }else{
            self.selectedProp.push(id)
            window.bus.$emit('add-fav',{grp:self.currentGrp.idx,prop:{_id:id},grps:self.grps})
            self.getPropdetail(self.currentGrp.idx,id)
          }
        }
      });
    },
    commitSelected(){
      var self = this;
      if(self.selectedProp.length <1 ){
        return window.bus.$emit('flash-message',self.$_(self.strings['NO_SELECTION'].key,self.strings['NO_SELECTION'].ctx));
      }
      window.bus.$emit('commit-selected',self.selectedProp);
    },
    saveCMAForWeb(){
      if(this.selectedProp.length < 1 ){
        return window.bus.$emit('flash-message',this.$_(this.strings['NO_SELECTION'].key,this.strings['NO_SELECTION'].ctx));
      }
      fetchData('/1.5/saveCMA/saveCMAIds',{body:{ids:this.selectedProp}},(err,ret)=>{
        if (!ret.ok) {
          return bus.$emit('flash-message',this.$_(this.strings.saveFailed.key,this.strings.saveFailed.ctx));
        }
        bus.$emit('flash-message',ret.msg);
      });
    },

    dotdate (d, isCh, split='.') {
      // console.log(d);
      if (!d) {
        return '';
      }
      if ('number' == typeof d) {
        d += '';
        d = d.slice(0,4)+'/'+d.slice(4,6)+'/'+d.slice(6,8)
      }
      //not supported by ios
      // var isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream;
      if ('string' == typeof d && !/\d+Z/.test(d)) {
        d += ' EST';
      }
      var split1 = isCh?'年':split;
      var split2 = isCh?'月':split;
      var split3 = isCh?'日':'';
      if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) && !/\d+Z/.test(d)) {
        var d2 = d.split(' ')[0].split('-');
        return d2[0]+split1+d2[1]+split2+d2[2]+split3;
      }
      var t = new Date(d);
      if (!t || isNaN( t.getTime() )) {
        // console.log('isIos: '+isIOS+' ivalid date: '+d);
        return d;
      }
      return t.getFullYear() + split1 + (t.getMonth()+1) + split2 + t.getDate() + split3;
    }
  },
  template: `
    <div class="prop-table" v-if='showPropTable'>
      <div v-for="prop in favProps" :key="prop._id">
        <div class="prop-info" @click=selectProp(prop._id) :class='{selected:prop.selected}'>
          <span class="stp" :class="prop.tagColor">
            <span>{{prop.saleTpTag || prop.lstStr}}</span>
          </span>
          <span class="sort-number" v-if="selectedProp.indexOf(prop._id) >-1">
            {{selectedProp.indexOf(prop._id)+1}}
          </span>
          <span class='table-image'>
            <img
              :src="prop.thumbUrl || '/img/noPic.png'"
              style="background-image: url('/img/noPic.png');"
              :style="{'height':propImgHeight+'px'}"
              @error="e => { e.target.src = '/img/noPic.png'}">
            <span class='dom' v-if="!prop.login && prop.dom">{{prop.dom}} {{$_('days')}}</span>
            <span class="date stp" :class="prop.tagColor" v-show="(prop.tagColor == 'red') && (prop.spcts||prop.mt||prop.ts) ">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>
          </span>
          <div class="addr one-line" v-if="!prop.login">
            <p v-show="prop.addr && prop.addr">{{prop.unt}} {{prop.addr}}</p>
            <p v-show="(!prop.addr) && prop.cmty"> {{prop.cmty}}</p>
            <p>{{prop.city}}, {{prop.prov}}</p>
          </div>
          <div class="addr one-line" v-if="prop.login"><span v-show="prop.addr">{{prop.addr}}, {{prop.city}}</span><span
            v-show="!prop.addr">{{prop.city}}, {{prop.prov}}</span>
          </div>
          <p class="bdrms" v-if="!prop.login">
            <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?'+'+prop.br_plus:''}}</span>
            <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>
            <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>
          </p>
          <p class="price" v-if="!prop.login">
            <span>{{formatPrice(prop.sp || prop.lp || prop.lpr)}}</span>
            <span v-if="prop.sp" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>
          </p>
          <p v-if="!prop.login && (prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft)">
              <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>
              <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>
              <span v-if="prop.rmSqft && prop.login">
                <span v-if="prop.sqft && /\-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>
                <span v-if='!prop.sqft'>{{parseSqft(prop.rmSqft)}} ({{$_('Estimated')}})</span>
              </span>
              <span>&nbsp;{{$_('ft&sup2;')}}</span>
            </span>
          </p>
          <p v-if="!prop.login && prop.front_ft">
            {{prop.front_ft}}&nbsp;* {{prop.depth}}{{prop.lotsz_code}} {{prop.irreg}}
          </p>
        </div>
      </div>
      <div>
        <div class="prop-info add-new-prop" @click="openAutoCompleteSearch()">
          <span class="plus-icon" :style="{'height':propImgHeight+'px','line-height':propImgHeight+'px'}">
            <span class="fa fa-plus"></span>
          </span>
          <p class="addr">{{$_('Add prop by ID')}}</p>
        </div>
      </div>
    </div>
    <div class="btn-cell bar bar-tab" v-show="showPropTable" style="display:flex;align-items: center;">
      <a @click="commitSelected()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">
        <span v-if="action.length">{{$_(strings[action].key)}}&nbsp;</span>
        <span class="badge" style="color:white">{{selectedProp.length}}</span>
      </a>
      <a @click="saveCMAForWeb()" class="btn btn-tab btn-half btn-sharp btn-fill btn-primary" style="color:white" v-if="action == 'cma'">{{$_('Save for Web')}}</a>
      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_('Cancel')}}</a>
    </div>
  `
}
