var addToShowing={
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {
          lang:'zh-cn',
        }
      }
    },
    vars:{
      type:Object,
    }
  },
  mounted() {
    var bus = window.bus, self = this;
    bus.$on('toggle-showing', function (prop) {
      self.getShowingList();
      self.selectedProp=prop;
      self.showShowing=true;
    });
  },
  data(){
    return {
      vipReachedLimit:false,
      isntVipReachedLimit:false,
      isntVipReachedTotal:false,
      propsLimit:0,
      showingList:[],
      selectedProp:[],
      showShowing:false
    }
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    formatHM: function (ts) {
      if (ts) {
        return  (ts.getHours()<10?'0'+ts.getHours():ts.getHours()) + ":" + (ts.getMinutes()<10?'0'+ts.getMinutes():ts.getMinutes())
      } else
        return '';
    },
    date2Num: function (ts) {
      if (ts) {
        return ''+ts.getFullYear()+ ((ts.getMonth()+1)<10?'0'+(ts.getMonth()+1):(ts.getMonth()+1))  + (ts.getDate()<10?'0'+ts.getDate():ts.getDate())
      } else
        return '';
    },
    getShowingList () {
      var self = this;
      var url = '/1.5/showing/upcoming';
      fetchData(url, {body:{}}, function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          if (ret.ok) {
            self.showingList = ret.list
            self.propsLimit = ret.propsLimit
            self.vipReachedLimit = ret.vipReachedLimit
            self.isntVipReachedLimit = ret.isntVipReachedLimit
            self.isntVipReachedTotal = ret.isntVipReachedTotal
          } else {
            alert(ret);
          }
        }
      });
    },
    //添加到showing 如果存在不满足条件给出提示
    addToShowingList(showing) {
      let self = this,propLength;
      var prop =self.selectedProp;
      if(this.vipReachedLimit && !showing){
        // return window.bus.$emit('flash-message', self._(self.strings.maximumShowing.key,self.strings.maximumShowing.ctx));
      }
      if(this.isntVipReachedLimit && !showing){
        return self.confirmVip(self.dispVar.lang);
      }
      if(this.isntVipReachedTotal && !showing){
        return self.confirmVip(self.dispVar.lang);
      }
      if(typeof(prop) == "string") {
        propLength=1
      }else{
        propLength = prop.length;
      }
      if (showing && propLength+showing.props.length > this.propsLimit){
        // return window.bus.$emit('flash-message', self.sprintf(self._(self.strings.maximumListing.key,self.strings.maximumListing.ctx),self.propsLimit));
      }
      if(showing){
        this.addToShowing(prop,showing._id,vars.d);
      }else{
        this.addToShowing(prop,'',vars.d);
      }
    },
    addToShowing(prop, showingId, d,today, cb){
      // 不满足新建showing条件
      if((this.isntVipReachedLimit || this.vipReachedLimit || this.isntVipReachedTotal) && !showingId){
        return ;
      }
      if((!prop || !prop.length) && !showingId) {
        return ;
      }
      let url = "/1.5/showing/detail?inFrame=1"
      if (prop) {
        if(typeof(prop) != "string") prop = prop.join(',')
        url = url +"&propIds="+prop;
      }
      if(showingId) {
        url = url+'&showingId='+showingId;
      }
      if(d) {
        url = url+'&d='+d;
      }
      if(today) {
        url = url+'&today='+today;
      }
      openContent(url,{toolbar:false})
      // this.toggleSelectMode();
      // if (self.dispVar && self.dispVar.isApp) {
      //   RMSrv.getPageContent(url,'#callBackString', {toolbar:false}, function(val){
      //     if (cb) {
      //       cb(val)
      //     }
      //   });
      // } else {
      //   window.location.href = url;
      // }
      this.reset();
    },
    reset(){
      this.showShowing = false;
      window.bus.$emit('reset')
    },
  },
  template:
    `
    <div class="backdrop" :class='{show:showShowing}' @click='reset()'></div>
    <div class="modal modal-60pc" id='showingSelect' :class="{'active':showShowing}">
      <header class="bar bar-nav"> {{getTranslate('add')}}</header>
      <div class="content">
          <ul class="table-view">
              <li class="table-view-cell" id="createBtn" @click="addToShowingList()" :class="{cantAdd:vipReachedLimit}">
                <span>{{getTranslate('createNShowing')}}</span>
                <span class="icon icon-plus"></span>
              </li>
              <li class="table-view-cell" v-for="showing in showingList" @click="addToShowingList(showing)" :class="{full:showing.props.length == propsLimit}">
                <span>{{showing.dt}}</span>
                <span style="padding-left:5px">{{showing.cNm||getTranslate('noClnt')}}</span>
                <span class="pull-right" v-show="showing.props.length == propsLimit">{{getTranslate('full')}}</span>
              </li>
          </ul>
      </div>
    </div>
    `
};