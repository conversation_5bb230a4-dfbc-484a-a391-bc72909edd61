
var propItem = {
  props:{
    prop:{
      type:Object,
    },
    rcmdHeight:{
      type:Number,
    },
    index:{
      type:Number,
    },
    formPage:{
      type:String,
      default:''
    },
    showFav:{
      type:Boolean,
      default:true
    },
    create:{
      type:Boolean,
      default:false
    },
    dispVar:{
      default:function () {
        return {
          lang:'zh-cn',
          isNoteAdmin: false,
          isRealGroup: false
        }
      }
    }
  },
  data(){
    return {
      noteMemo:'',
    }
  },
  mounted() {
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
  },
  computed:{
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    computedBgImg:function(){
      var img = 'url(' + (this.prop.thumbUrl) + '), url("/img/noPic.png")';
      return img;
    },
    computedShowInfo(){
      if(this.formPage != "similar"){
        return false;
      }
      if(this.prop.dist){
        return true;
      }
      if(this.prop.sqft || (this.prop.sqft1&&this.prop.sqft2) || this.prop.rmSqft){
        return true;
      }
      if(this.prop.front_ft){
        return true;
      }
      if(this.prop.weight && this.dispVar.isAdmin){
        return true;
      }
      return false;
    }
  },
  watch:{
   prop:{
      handler(newValue){
        if(this.prop.noteMemo){
          if(this.prop.noteMemo[0].tag == 'noTag'){
            this.noteMemo = this.prop.noteMemo[0].m
          }else{
            this.noteMemo = `[${this.prop.noteMemo[0].tag}]${this.prop.noteMemo[0].m}`
          }
        }
      },
      immediate: true,
      deep:true,
    }
  },
  methods:{
    showAgentWesite(user){
      var url = `/1.5/wesite/${user._id}?inFrame=1`;
      RMSrv.openTBrowser(url);
    },
    getStatus:function(prop) {
      var status = prop.status == 'U' ? 'sold' : 'sale';
      return status;
    },
    openDetail:function(prop) {
      var id = (/^RM/.test(prop.id))?prop.id:prop._id;
      var propUrl = '/1.5/prop/detail/inapp?id='+id;
      if (this.formPage) {
        propUrl += `&formPage=${this.formPage}`;
      }
      openPopup(propUrl,this.$_('RealMaster'));//lang='+this.dispVar.lang+'
    },
    openShowing(prop){
      return window.bus.$emit('open-showing',prop);
    },
    getPropDate(prop) {
      if (prop.status_en == 'U') {
        return prop.sldd;
      } else {
        return prop.mt.substr(0,10);
      }
    },
    soldOrLeased: function (prop) {
      return prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(prop.lst);
    },
    saletpIsSale: function (prop) {
      if (!prop.saletp_en) {
        return true;
      }
      if (/sale/.test(prop.saletp_en.toString().toLowerCase())) {
        return true;
      }
      return false;
    },
    isTop:function(prop) {
      return (prop.status == 'A') && (new Date(prop.topTs) >= new Date());
    },
    propSid:function (prop) {
      if (prop.isProj) {
        return '';
      } else if (prop.sid) {
        return prop.sid;
      } else if (/^RM/.test(prop.id)) {
        return prop.id;
      } else {
        return prop._id?prop._id.substr(3):'';
      }
    },
    rmgrStr: function (prop) {
      return prop.rmgr || prop.tgr || prop.gr;
    },
    rmbdrmStr: function (prop) {
      if (prop.rmbdrm) {
        return prop.rmbdrm;
      }
      let tmp = prop.bdrms || prop.tbdrms;
      tmp += prop.br_plus?'+ '+prop.br_plus:''
      return tmp;
    },
    rmbthrmStr: function (prop) {
      return prop.rmbthrm || prop.tbthrms || prop.bthrms;
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    isInGrp(g){
      var grps = this.prop.favGrp || [];
      return grps.indexOf(parseInt(g)) > -1;
    },
    toggleFav(prop){
      window.bus.$emit('toggleFav',prop);
    },
    computedVideoUrl:function(){
      if (this.dispVar.isCip) {
        return this.prop.vurlcn;
      } else {
        if (this.prop.ytvid) {
          return "https://www.youtube.com/watch?v="+this.prop.ytvid;
        }
        return null;
      }
    },
    isPropUnavailable:function () {
      if (this.prop.id && this.prop.id.substr(0,2) == 'RM') {
        return false;
      }
      return this.prop.status_en !== 'A';
    },
    calcShowingName(prop){
      if(prop.showing){
        return prop.showing.dt;
      }
      return ''
    },
    remove(){
      window.bus.$emit('remove',this.prop);
    },
    clearNoteInfo(){
      delete this.prop.noteMemo;
      delete this.prop.noteId;
      this.noteMemo = '';
      this.prop.noteCount--;
    },
    goToEditNote(){
      if(!this.prop.uaddr || (this.prop.uaddr == 'undefined')){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
      }
      if(!this.prop._id || (this.prop._id == 'undefined')){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
      }
      let self = this;
      fetchData('/1.5/notes/findListingByID',{body:{id:this.prop._id}},(err,ret)=>{
        if (!ret.ok) {
          return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
        }
        let getUaddr = ret.result.uaddr;
        if (!getUaddr || (getUaddr == 'undefined')){
          return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
        }
        let url = `/1.5/notes/editNotes?propId=${this.prop._id}&uaddr=${encodeURIComponent(getUaddr)}`;
        if (this.prop.noteId) url += `&id=${this.prop.noteId}`;
        if(!(self.dispVar.isRealGroup || self.dispVar.isNoteAdmin) && ret.result.unt){
          url += `&unt=${encodeURIComponent(ret.result.unt)}`
        }
        let cfg = {toolbar:false};
        if (self.dispVar && self.dispVar.isApp) {
          RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
            if (val == ':cancel') {
              return;
            }
            try {
              let info = JSON.parse(val);
              if(info.type == 'delete'){
                self.clearNoteInfo();
                return;
              }
              let note = info.note;
              if(!self.prop.noteMemo) self.prop.noteCount += 1;
              if(note.memo){
                self.prop.noteMemo = note.memo;
              } else {
                self.prop.noteMemo = [{tag: 'noTag', m: ' '}];
              }
              self.prop.noteId = note._id;
            } catch (e) {
              console.error(e);
            }
          });
        }
      })
    },
    goToNoteList(){
      let self = this;
      if(!this.prop.uaddr){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOUADDR));
      }
      if(!this.prop._id){
        return bus.$emit('flash-message',this.$_(NOTE_TIPS.NOPROPID));
      }
      let url = `/1.5/notes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}&addr=${encodeURIComponent(this.prop.addr)}&lat=${this.prop.lat}&lng=${this.prop.lng}&isBuilding=${this.prop.isBuilding}`;
      let cfg = {toolbar:false};
      if (self.dispVar && self.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
          try {
            let note = JSON.parse(val);
            let noteDetail = note.noteDetail;
            if(noteDetail && self.prop.noteMemo){ // 只修改
              self.prop.noteMemo = noteDetail.memo? noteDetail.memo : [{tag: 'noTag', m: ' '}];
            } else if(noteDetail && !self.prop.noteMemo){ // 添加
              self.prop.noteMemo = noteDetail.memo? noteDetail.memo : [{tag: 'noTag', m: ' '}];
              self.prop.noteId = noteDetail._id;
              self.prop.noteCount += 1;
            } else if(!noteDetail && self.prop.noteMemo){ // 删除
              self.clearNoteInfo();
            }
          } catch (e) {
            console.error(e);
          }
        });
      }
    },
    parseSqft(sqft){
      if (/\-/.test(sqft)) {
        return sqft;
      }
      if ('number' == typeof sqft) {
        sqft = ''+sqft;
      }
      if (/\./.test(sqft)) {
        return sqft.split('.')[0];
      }
      return sqft;
    },
    dotdate (d, isCh, split='.') {
      // console.log(d);
      if (!d) {
        return '';
      }
      if ('number' == typeof d) {
        d += '';
        d = d.slice(0,4)+'/'+d.slice(4,6)+'/'+d.slice(6,8)
      }
      //not supported by ios
      // var isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream;
      if ('string' == typeof d && !/\d+Z/.test(d)) {
        d += ' EST';
      }
      var split1 = isCh?'年':split;
      var split2 = isCh?'月':split;
      var split3 = isCh?'日':'';
      if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) && !/\d+Z/.test(d)) {
        var d2 = d.split(' ')[0].split('-');
        return d2[0]+split1+d2[1]+split2+d2[2]+split3;
      }
      var t = new Date(d);
      if (!t || isNaN( t.getTime() )) {
        // console.log('isIos: '+isIOS+' ivalid date: '+d);
        return d;
      }
      return t.getFullYear() + split1 + (t.getMonth()+1) + split2 + t.getDate() + split3;
    }
  },
  // TODO: restrict field in backend instead of checking role in frontend
  template: `
<div class="prop" @click="openDetail(prop)">
  <div class="img" :style="{ 'background-image': computedBgImg, 'height':rcmdHeight+'px'}">
    <span class="top pull-left" v-if="prop.isTop && prop.marketRmProp">{{$_('TOP')}}</span>
    <span class="tp" v-show="prop.type">{{prop.type}}</span>
    <span class="pull-right fav fa" :class="{'fa-heart-o':!prop.fav, 'fa-heart':prop.fav}"
      @click.stop.prevent="toggleFav(prop)" v-show="!prop.login && !prop.isProj" v-if='showFav'></span>
  </div>
  <div class="price" :class="{'blur':prop.login}">
    <span class="val" v-if='prop.priceValStrRed'>{{prop.priceValStrRed}}</span>
    <span class="val" v-else-if='prop.askingPriceStr'>{{prop.askingPriceStr}}</span>
    <span class="desc" v-if="prop.priceValStrRedDesc" :class="{'through':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>
    <span class="desc" v-if="prop.lstStr && (prop.tagColor != 'red')">({{prop.lstStr}})</span>
    <div class="displayFlex maxWidth">
      <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !='Delisted'" :class="prop.tagColor">
        <span>{{prop.saleTpTag}}</span>
        <span v-if="(prop.tagColor == 'red'|| prop.tagColor == 'green') && (prop.spcts||prop.mt||prop.ts) && !prop.marketRmProp">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>
      </span>
      <span class="stp green" v-show="['exlisting','assignment','rent'].indexOf(prop.ltp) > -1 && !prop.marketRmProp">
        <span v-show="prop.ltp == 'exlisting'">{{$_('Exclusive')}}</span>
        <span v-show="prop.ltp == 'assignment'">{{$_('Assignment')}}</span>
        <span v-show="prop.ltp == 'rent' && !prop.cmstn">{{$_('Landlord Rental')}}</span>
        <span v-show="prop.ltp == 'rent' && prop.cmstn">{{$_('Exclusive Rental')}}</span>
      </span>
      <span class="dist" v-show="prop.dist && (formPage != 'similar')" >{{prop.dist}}</span>
      <span class="stp" v-show="prop.isAgreementVerified && !prop.marketRmProp">
        <span class="fa fa-check"></span>
      </span>
      <span class="stp vid" v-show="computedVideoUrl()">
        <span class="fa fa-youtube-play"></span>{{$_('Video')}}
      </span>
      <span class="stp oh" v-if="prop.hasOh">{{$_('Open House')}}</span>
      <div class="stp viewTrusted" v-if="prop.marketRmProp"><span class="fa fa-check-circle trustedCir"></span>{{$_(prop.marketRmProp)}}</div>
    </div>
  </div>
  <div class="addr one-line" v-if="!prop.login"><span v-show="prop.daddr !== 'N'"><span v-show="prop.addr">{{prop.unt}}
        {{prop.addr}},</span> {{prop.city}}, {{prop.prov}}</span><span v-show="prop.daddr == 'N'"><span
        v-show="prop.cmty">{{prop.cmty}},</span>{{prop.city}}, {{prop.prov}}</span>
  </div>
  <div class="addr one-line" v-if="prop.login"><span v-show="prop.daddr == 'Y'">{{prop.addr}}, {{prop.city}}</span><span
      v-show="prop.daddr !== 'Y'">{{prop.city}}, {{prop.prov}}</span></div>
  <div class="bdrms one-line" v-if="!prop.login">
    <span class="rmbed" v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null">
      <span class="fa fa-rmbed">
      </span>
      <b class="num">{{rmbdrmStr(prop)}}</b>
      <span v-if="prop.bdrms_diff!= null" class='diff'>
        <span :class="{red: prop.bdrms_diff < 0, green:prop.bdrms_diff > 0,blue:prop.bdrms_diff == 0}">
          <span class="nopadding" :class="{'fa-caret-up': prop.bdrms_diff > 0, 'fa-caret-down':prop.bdrms_diff < 0,'fa-check-circle':prop.bdrms_diff == 0 }"></span>
          <span v-show='prop.bdrms_diff != 0'>&nbsp;{{Math.abs(prop.bdrms_diff)}}</span>
        </span>
      </span>
    </span>
    <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null">
      <span class="fa fa-rmbath">
      </span>
      <b class="num">{{rmbthrmStr(prop)}}</b>
      <span v-if="prop.bthrms_diff!= null" class='diff'>
        <span :class="{red: prop.bthrms_diff < 0, green:prop.bthrms_diff > 0,blue:prop.bthrms_diff == 0}">
          <span class="nopadding" :class="{'fa-caret-up': prop.bthrms_diff > 0, 'fa-caret-down':prop.bthrms_diff < 0,'fa-check-circle':prop.bthrms_diff == 0}"></span>
          <span v-show='prop.bthrms_diff != 0'>&nbsp;{{Math.abs(prop.bthrms_diff)}}</span>
        </span>
      </span>
    </span>
    <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null">
      <span class="fa fa-rmcar"></span>
      <b class="num">{{rmgrStr(prop)}}</b>
      <span v-if="prop.gr_diff!= null" class='diff'>
        <span :class="{red: prop.gr_diff < 0, green:prop.gr_diff > 0,blue:prop.gr_diff == 0}">
          <span class="nopadding" :class="{'fa-caret-up': prop.gr_diff > 0, 'fa-caret-down':prop.gr_diff < 0,'fa-check-circle':prop.gr_diff == 0}"></span>
          <span v-show='prop.gr_diff != 0'>&nbsp;{{Math.abs(prop.gr_diff)}}</span>
        </span>
      </span>
    </span>
    <span v-if="prop.marketRmProp && prop.sqft && (formPage == 'truthAssignList')">
      <span class="fa fa-area"></span>
      <b class="num">{{prop.sqft}}</b>
    </span>
    <span v-show="prop.isAgreementVerified && !prop.marketRmProp">
      <span class="fa fa-check-circle"></span>
      <b class="num">{{$_('Verified')}}</b>
    </span>
    <span v-if="(prop.private && (formPage == 'truthAssignList'))">
      <span class="num" style="color:#e03131">{{$_('Hide to the public')}}</span>
    </span>
    <span class="sid">{{propSid(prop)}}</span>
  </div>
  <div class="bdrms" v-if="prop.login">{{$_('Please login to see this listing')}}</div>
  <div class='showing-info' v-if="formPage == 'buylist'">
    <span class="left">
      <span v-show='dispVar.isRealtor && create'>
        <span v-if='prop.clnt && prop.clnt.nm'>{{prop.clnt.nm}}</span>
      </span>
      <span v-if='!create && prop.realtor && prop.realtor.fnm' class='agent' @click.stop='showAgentWesite(prop.realtor)'>
        <img :src="prop.realtor.avt || prop.realtor.wxavt || '/img/logo.png'" @error="prop.realtor.avt = '/img/logo.png'">
        <span class="d">{{prop.realtor.fnm}}</span>
      </span>
      <span class="showing-name" @click.stop='openShowing(prop)' :class='{"pull-right":!create}'>
        <span class="link">{{$_('Showing')}}</span>
        <span>({{calcShowingName(prop)}})</span>
      </span>
    </span>
    <span class="right" v-show='create'>
      <span class="pull-right delbtns btn btn-nooutline" v-show="prop.del">
        <span class="cancle pull-right btn btn-nooutline" @click.stop.prevent="prop.del = false" >{{ $_('Cancel')}}</span>
        <span class="delete pull-right btn btn-negative" @click.stop.prevent="remove()" >{{ $_('Delete')}}</span>
      </span>
      <span class="pull-right sprite16-18 sprite16-4-8" v-show="!prop.del" @click.stop.prevent="prop.del = true"></span>
    </span>
  </div>
  <div v-if='computedShowInfo' class='size bdrms'>
    <span v-if="prop.dist">{{$_('Distance')}}
      <span class='val pull-right' >{{prop.dist}}{{$_('m')}}</span>
    </span>
    <div v-if="prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft">
      {{$_('Size')}}
      <span class='val pull-right'>
        <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>
        <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>
        <span v-if="prop.rmSqft && dispVar.isLoggedIn">
          <span v-if="prop.sqft && /\-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>
          <span v-if='!prop.sqft'>{{parseSqft(prop.rmSqft)}} ({{$_('Estimated')}})</span>
        </span>
        <span>&nbsp;{{$_('ft&sup2;')}}</span>
        <span v-if="prop.sqft_diff!= null" class='diff'>
          <span :class="{red: prop.sqft_diff < 0, green:prop.sqft_diff > 0,blue:prop.sqft_diff == 0}">
            <span class="nopadding" :class="{'fa-caret-up': prop.sqft_diff > 0, 'fa-caret-down':prop.sqft_diff < 0,'fa-check-circle':prop.sqft_diff == 0}"></span>
            <span v-if='prop.sqft_diff_abs && prop.sqft_diff_abs != 0'>&nbsp;{{prop.sqft_diff_abs}} {{$_('ft&sup2;')}}</span>
          </span>
        </span>
      </span>
    </div>
    <div v-if="prop.front_ft">
      {{$_('Lot')}}
      <span class='val pull-right'>
        {{prop.front_ft}}
        <span v-if="prop.front_ft_diff!= null">(
          <span :class="{red: prop.front_ft_diff < 0, green:prop.front_ft_diff > 0,blue:prop.front_ft_diff == 0}">
            <span class="nopadding" :class="{'fa-caret-up': prop.front_ft_diff > 0, 'fa-caret-down':prop.front_ft_diff < 0,'fa-check-circle':prop.front_ft_diff == 0}"></span>
            <span v-if='prop.front_ft_diff != 0'>&nbsp;{{Math.abs(prop.front_ft_diff)}}</span>
          </span>)
        </span>
        &nbsp;* {{prop.depth}}
        <span v-if="prop.depth_diff!= null">(
          <span :class="{red: prop.depth_diff < 0, green:prop.depth_diff > 0,blue:prop.depth_diff == 0}">
            <span class="nopadding" :class="{'fa-caret-up': prop.depth_diff > 0, 'fa-caret-down':prop.depth_diff < 0,'fa-check-circle':prop.depth_diff == 0}"></span>
            <span v-if='prop.depth_diff != 0'>&nbsp;{{Math.abs(prop.depth_diff)}}</span>
          </span>)
        </span>
        {{prop.lotsz_code}} {{prop.irreg}}
      </span>
    </div>
    <div v-if="prop.weight && dispVar.isDevGroup">
      {{$_('Weight')}}
      <span class='val pull-right' v-if='prop.weight.wTotal'>
        {{prop.weight.wTotal}}
      </span>
    </div>
  </div>
  <div class="bdrms" v-if="prop.login">{{$_('Please login to see this listing')}}</div>
  <div class="noteInsave" v-if="prop.noteMemo || prop.noteCount>=0">
    <div class="noteInfo" :style="(dispVar.isNoteAdmin || dispVar.isRealGroup)? 'justify-content:normal':'justify-content: space-between'">
      <input v-model="noteMemo" class="noteMemo" readonly="readonly" :placeholder="$_('Add a note')" @click.stop="goToEditNote()"></input>
      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" class="fa fa-rmmemo noteIcon" @click.stop="goToNoteList()"></span>
      <span v-else class="fa fa-rmmemo noteIcon" :style="prop.noteCount? 'color: #428bca':'color:#777'" @click.stop="goToEditNote()"></span>
      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" @click.stop="goToNoteList()">{{prop.noteMemo?1:0}}/{{prop.noteCount}}</span>
    </div>
  </div>
</div>
  `
};