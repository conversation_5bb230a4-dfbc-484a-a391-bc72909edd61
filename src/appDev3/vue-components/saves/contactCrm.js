
var contactCrm = {
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {
          isVipRealtor:false,
          allowedEditGrpName:false,
          lang:'zh-cn',
          languageAbbrObj:[]
        }
      }
    },
    edit:{
      type: Boolean,
      default: true
    },
    noBar:{
      type: Boolean,
      default: false
    },
    needEml:{
      type: Boolean,
      default: true
    },
    showCloseIcon:{
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      grp:null,
      grpName:'',
      showDrop:false,
      mode:'new',
      loading:false,
      ifInput:false,
      add:true,
      contact:{
        nm :'',
        mbl :'',
        eml :'',
        m :'' ,
        lang:'en'
      },
      crmList:[],
      rcnt:[],
      errnm:false,
      errmbl:false,
      erreml:false,
      errm:false,
      fromUrl:false,
      strings:{
        savedStr:{key:"Saved", ctx:'showing'},
        crmNoEmail:{key:'Conntact email not found!'},
        crmNoName:{key:'Conntact name not found!'},
      },
    };
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  mounted () {
    var self = this, bus = window.bus;
    bus.$on('showing-contacts', function () {
      self.getCrmList();
      self.showDrop = true;
    });
    // if(vars.fromUrl){
    //   self.getCrmList();
    //   self.showDrop = true;
    //   self.fromUrl=true
    // }
    bus.$on('close-contacts', function () {
      self.close();
    });
    this.createLangTemplate = createLangTemplate;
    window.setLang = this.setLang;
  },
  watch:{

  },
  computed:{

  },
  methods: {
    getCrmList () {
      var self = this;
      var d = {}
      self.loading = true;
      fetchData('/1.5/crm', {body:d},function(err,ret){
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          // ret = ret.body;
          // self.loading = false;
          if (ret.ok) {
            var crml={};
            var crmlAll = self.chineseLetter(ret.list.all,"nm")
            for(var i in crmlAll) {
              if(crmlAll[i].length>0){
                crml[i]=crmlAll[i]
              }
            }
            self.crmList = crml
            self.rcnt = ret.list.rcnt
          } 
        }
      }
      );
    },
    isActive (lang) {
      // check user.locale, not dispvar.lang
      return this.contact.lang === lang ? 'active' : '';
    },
    setLang (lang) {
      if (lang) {
        this.contact.lang=lang
      }
    },
    close(){
      this.showDrop=false;
      window.bus.$emit('close-crm');
    },
    closeInput(){
      this.ifInput = false;
    },
    addOrUp(val){
      if(val){
        this.add=false
        this.contact=Object.assign({lang:'en'},val)
      }else{
        this.add=true
        this.contact={nm :'', mbl :'',eml :'',m :'' ,lang:'en'}
      }
      this.ifInput = true;
    },
    selectClient(contact) {
      let self = this;
      if(self.fromUrl==true) return;// 从more contacts进来的，只能编辑，不能选择。
      if (self.needEml){
        if(contact.nm ==''){
          return window.bus.$emit('flash-message', self.$_(self.strings.crmNoName));
        }
        if(contact.eml ==''){
          return window.bus.$emit('flash-message', self.$_(self.strings.crmNoEmail));
        }
      }
      trackEventOnGoogle('crm', 'select');
      window.bus.$emit('choosed-crm',contact)
      self.showDrop = false;
    },
    editCrm(){
      let self = this;
      var params = {};
      params = this.contact
      trackEventOnGoogle('crm', 'edit')
      var mblPattern = /^[0-9 +-]{8,14}$/
      var emlPattern = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
      var textPattern = new RegExp(/[\`\@\#\$\^\&\*\=\|\{\}\[\]\<\>\/\~\@\#\￥\…\…\&\*\—\|\{\}\【\】]/) ;
      if(this.contact.nm == '' || this.contact.nm.length < 2) this.errnm=true;else this.errnm = false
      if(this.contact.mbl != ''&& mblPattern.test(this.contact.mbl) == false) this.errmbl=true;else this.errmbl = false
      if(this.contact.eml != '' && emlPattern.test(this.contact.eml) == false) this.erreml=true;else this.erreml = false
      if(this.contact.m != '' && textPattern.test(this.contact.m) == true) this.errm=true;else this.errm = false
      if(this.errnm || this.errmbl ||this.erreml || this.errm) return
      self.loading = true;
      fetchData('/1.5/crm/edit', {body:params},function (err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          self.loading = false;
          if (!ret.ok) {
            self.processPostError(ret);
          }
          self.getCrmList();
          self.closeInput();
          this.contact={nm :'', mbl :'',eml :'',m :'' }
        }
        }
      );
    },
    goBack(){
      document.location.href = vars.d || '/1.5/index'
    // <page-spinner :loading="loading"></page-spinner>
    },
  },
  template: `
  <div class="crm" v-show="showDrop" id='crmModule'>
    <header class="bar bar-nav" v-show="!noBar"><a class="icon fa fa-back pull-left" href="javascript:;" @click="goBack()"
        v-show="fromUrl"></a>
      <h1 class="title">{{$_('Contacts','contactCrm')}}</h1><a class="icon icon-close pull-right" @click="close()"
        v-show="!fromUrl || showCloseIcon"></a>
    </header>
    <flash-message></flash-message>
    <div class="content" :class="{'padding-top-0':noBar}">
      <div id="createBtn" v-show="edit" @click="addOrUp()" :class="{'padding-top-0':noBar}">
        <span>{{$_('New Contact')}}</span><span class="sprite16-18 sprite16-1-9 pull-right"></span></div>
      <ul class="table-view near" v-show="rcnt.length" :class="{btnPosition:edit}"><span>
          {{$_('Recent')}}</span>
        <li class="table-view-cell" v-for="crm in rcnt"><span class="name"
            @click.stop="selectClient(crm)">{{crm.nm}}</span><a class="phone"><span
              class="sprite16-18 sprite16-3-7 pull-right" :href="'tel:'+crm.mbl" v-show="crm.mbl"></span></a></li>
      </ul>
      <ul class="table-view" v-for="(val,key) in crmList"><span v-show="val.length&gt;0"> {{key}}</span>
        <li class="table-view-cell" v-for="crm in val">
            <div class='crmCard'>
                <div class="name" @click="selectClient(crm)">{{crm.nm}}
                  <span>
                    <a v-show="edit" @click.stop="addOrUp(crm)">
                      <span class="sprite16-18 sprite16-5-3 margin-right-3"></span>
                      <span>{{$_('Edit')}}</span>
                    </a>
                    <a>{{$_('Select')}}</a>
                  </span>
                </div>
                <div class="info" v-show="crm.eml">
                  <span class="sprite16-18 sprite16-5-1"></span>
                  <a :href="'mailto:'+crm.eml">{{crm.eml}}</a>
                  <b @click="selectClient(crm)">click</b>
                </div>
                <div class="info" v-show="crm.mbl">
                  <span class="sprite16-18 sprite16-5-7"></span>
                  <a :href="'tel:'+crm.mbl">{{crm.mbl}}</a>
                  <b @click="selectClient(crm)">click</b>
                </div>
                <div class="info" v-show="crm.m" @click="selectClient(crm)">
                  <span class="sprite16-18 sprite16-5-2"></span>
                  <em style="color:#747474">{{crm.m}}</em>
                </div>
                <div class="info" v-show="crm.lang" @click="selectClient(crm)">
                  <span class="fa fa-language"></span>
                  <em style="color:#747474">{{$_(crm.lang,'lang')}}</em>
                </div>
            </div>
        </li>
      </ul>
    </div>
    <div class="editBox" @click.stop="closeInput()" v-show="ifInput"></div>
    <div class="modal modal-60pc" :class="{'active':ifInput}" style="height: inherit;">
      <div class="editCell"><input :placeholder="$_('Name')" v-model="contact.nm" maxlength="20" /><span
          class="inline-error fa fa-exclamation-circle" v-show="errnm"></span></div>
      <div class="editCell"><input :placeholder="$_('Email')" v-model="contact.eml" /><span
          class="inline-error fa fa-exclamation-circle" v-show="erreml"></span></div>
      <div class="editCell"><input type="text" :placeholder="$_('Phone')" v-model="contact.mbl" /><span
          class="inline-error fa fa-exclamation-circle" v-show="errmbl"></span></div>
      <div class="editCell"><input :placeholder="$_('Memo')" v-model="contact.m" /><span
          class="inline-error fa fa-exclamation-circle" v-show="errm"></span></div>
      <div class="editCell">
        <div>{{$_('lang')}}</div><span class="btn-group pull-right" style="margin-left: auto;"><a
            class="btn btn-default" href="javascript:void 0" v-for="lang in dispVar.languageAbbrObj" @click="createLangTemplate(contact.lang)" v-show="isActive(lang.k)">{{lang.v}}</a></span>
      </div>
      <div class="btns">
        <div class="btn btn-half btn-sharp btn-fill add">
          <div v-show="add" @click="editCrm()">{{$_('Add')}}</div>
          <div v-show="!add" @click="editCrm()">{{$_('Update')}}</div>
        </div>
        <div class="btn btn-half btn-sharp btn-fill" @click="closeInput()">{{$_('Cancel')}}</div>
      </div>
    </div>
  </div>
  `
};