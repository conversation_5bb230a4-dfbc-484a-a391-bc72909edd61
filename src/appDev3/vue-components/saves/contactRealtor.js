
var contactRealtor = {
  props:{
    message:{
      type:String,
      default:'Please tell him/her you see the info on RealMaster APP'
    },
    nodrop: {
      type:Boolean,
      default:false,
    },
    isChatBlocked:{
      type: Boolean,
      default:false
    },
    // showChat:{
    //   type:Boolean,
    //   default:true,
    // },
  },
  data(){
    return {
      realtor:{}
    }
  },
  mounted() {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    bus.$on('toggle-contact-smb', function (r) {
      if (r && (r._id || r.uid)) {
        self.realtor = r;
        if (r.uid == 'hide') {
          self.showChat = false;
        } else {
          self.showChat = true;
        }
        // if (r.message) {
        //   self.message = r.message;
        // } else {
        //   self.message = 'infoOnApp'
        // }
      }
      self.toggleSMB();
    });
  },
  computed:{
    computedBgImg:function(){
      return 'url(' + this.proj.img.l[0] + '), url("/img/noPic.png")';
    }
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    toggleSMB () {
      var backdrop = document.getElementById('contactBackdrop'),
          body = document.querySelector('body');
      body.classList.toggle('smb-open');
      if (backdrop) {
        var disp = backdrop.style.display === 'none' ? 'block' : 'none';
        return backdrop.style.display = disp;
      }
    },
    chat(user) {
      var uid, url;
      if (!(user._id || user.uid)) {
        return;
      }
      uid = user._id || user.uid;
      url = '/chat/u/' + uid +"?d="+vars.d;
      var cb = function(){
        window.location.replace(url);
      };
      window.bus.$emit('update-click',{tp:'chat',cb:cb});
    },
    updateClick(tp){
      window.bus.$emit('update-click',{tp:tp});
    },
    showWecard(r){
      window.bus.$emit('show-wecard', r)
    },

  },
  watch:{
  },
  template: `
<div>
  <div class="backdrop" id="contactBackdrop" style="display:none; z-index:13" @click="toggleSMB()"></div>
  <nav class="menu slide-menu-bottom smb-auto" id="realtorContact">
      <ul class="table-view">
          <li class="table-view-cell"><a class="tip" href="javascript:;">{{getTranslate('infoOnApp')}}</a></li>
          <li class="table-view-cell"><a :href="'tel:'+realtor.mbl" @click="updateClick('mbl')"><i class="fa fa-fw fa-phone"></i>{{getTranslate('Call')}}</a></li>
          <li class="table-view-cell"><a :href="'mailto:'+realtor.eml" @click="updateClick('email')"><i class="fa fa-fw fa-envelope"></i>{{getTranslate('Email')}}</a></li>
          <li class="table-view-cell" @click="chat(realtor)" v-show="(!isChatBlocked) && showChat"><i class="fa fa-fw fa-comments-o" style="color:#e03131"></i>{{getTranslate('sendMessage')}}</li>
          <li class="table-view-cell" @click="showWecard(realtor)" v-show="showChat"><i class="sprite16-18 sprite16-3-6" style="vertical-align: text-top; margin-right: 10px; width: 17px;"></i>{{getTranslate('viewProfile')}}</li>
          <li class="cancel table-view-cell" @click="toggleSMB('close')">{{getTranslate('Cancel')}}</li>
      </ul>
  </nav>
</div>
  `
};