var crmMixin = {
  created :  function () {
  },
  data() {
    return {
    }
  },
  computed:{
  },
  methods : {
    isRMProp(prop){
      return /^RM/.test(prop.id);
    },
    
    goTo(url) {
      window.location.href=url;
    },
    openTBrowser (url, cfg={}) {
      if (this.dispVar.isApp) {
        this.tbrowser(url);
      } else {
        window.location.href = url;
      }
    },
    getCrmList () {
      var self = this;
      var d = {}
      if (self.curUser && self.curUser._id) {
        d.clnt = self.curUser._id;
      }
      self.loading = true;

      self.$http.post('/1.5/crm', d).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          if (ret.ok) {
            var crml={};
            var crmlAll = self.chineseLetter(ret.list.all,"nm")
            for(var i in crmlAll) {
              if(crmlAll[i].length>0){
                crml[i]=crmlAll[i]
              }
            }
            self.crmList = crml
            self.rcnt = ret.list.rcnt
          } else {
            self.processPostError(ret);
          }
        },
        function (ret) {
          // console.error( "server-error" );
          RMSrv.dialogAlert( "server-error get contact list" );
        }
      );
    },
    listScrolled(){
      var self = this, wait = 400;
      self.scrollElement = document.getElementById('forum-containter');
      if (!self.waiting && self.posts_more) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.posts_more) {
              self.pgNum+=1;
              var params = self.getSearchParmas();
              params.page = self.pgNum
              self.getAllPost(params);
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    formatTs(ts) {
      if (ts) {
        ts = new Date(ts);
        var minute = ts.getMinutes();
        if (minute<10)
          minute = '0'+minute
        return ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate() + ' ' + ts.getHours() + ":" + minute;
      } else
        return '';
    },
    trimStr(str, len) {
      if (!str || !len)  return '';
      var a = 0;
      var i = 0;
      var temp = '';

      for (i=0; i<str.length; i++) {
        if (str.charCodeAt(i)>255) {
          a += 2;
        } else {
          a ++;
        }
        if (a > len) return temp + '...';
        temp += str.charAt(i);
      }
      return str;
    },

    formatTs2: function (ts) {
      var fn = this._?this._:this.$parent._;
      if (!ts) return '';
      var ts = new Date(ts);
      var diff = new Date() - ts
      if (diff < 60000)
        return fn('Just now','forum')
      else if (diff > 24*3600*1000) {
        return fn(this.monthArray[ts.getMonth()]) + ' ' + ts.getDate();
        // return (ts.getMonth() + 1) + '-' + ts.getDate() + ' ' + ts.getHours() + ":" + ts.getMinutes() + ":" + ts.getSeconds();
        // return fn(this.monthArray[ts.getMonth()]) + ' ' + ts.getDate() + ' ' + ts.getHours() + ":" + ts.getMinutes();
      }
      else if (diff > 3600*1000)
        return parseInt(diff / (3600*1000)) + fn('Hrs','forum')
      else
        return parseInt(diff/ (60 * 1000)) + fn('Mins','forum')
    },
    formatYMD: function (ts) {
      if (ts) {
        if (ts.length == 8) {
          var tm=[]
          tm[0]=ts.slice(0,4)
          tm[1]=ts.slice(5,6)
          tm[2]=ts.slice(7,8)
          tm=tm.join('-')
          return new Date(ts)
        }else{
        ts = new Date(ts);
        return ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate();
        }
      } else
        return '';
    },
    chineseLetter :function(arr) {
      var self = this;
      var data = arr;
      var map = {};
      var c = 'A'.charCodeAt();
      for(; c <= 'Z'.charCodeAt(); c++ ){
        map[String.fromCharCode(c)] = [];
      }
      map['#'] = [];
      var firstCharUpper;
      data.forEach(function(item){
        firstCharUpper = self.getFirstUpperChar(item.nm);
        if (map.hasOwnProperty(firstCharUpper)) {
          map[firstCharUpper].push(item);
        } else {
          map['#'].push(item);
        }
      });

      //排序
      for(var key in map) {
        if( map.hasOwnProperty( key ) && (map[key].length != 0)) {
          map[key].sort(function(a, b){
            return a.nm.localeCompare(b.nm, 'zh-CN-u-co-pinyin');
          });
        }
      }

      this.dictMap = map;
      return map;
  
    },
    getFirstUpperChar: function(str){
      var string = String(str);
      var c = string[0];
      if (/[^\u4e00-\u9fa5]/.test(c)) {
        return c.toUpperCase();
      }
      else {
        return this.chineseToEnglish(c);
      }
    },
    chineseToEnglish: function(c){
      var idx = -1;
      var MAP = 'ABCDEFGHJKLMNOPQRSTWXYZ';
      var boundaryChar = '驁簿錯鵽樲鰒餜靃攟鬠纙鞪黁漚曝裠鶸蜶籜鶩鑂韻糳';
      if (!String.prototype.localeCompare) {
        throw Error('String.prototype.localeCompare not supported.');
      }
      if (/[^\u4e00-\u9fa5]/.test(c)) {
        return c;
      }
      for (var i = 0; i < boundaryChar.length; i++) {
        if (boundaryChar[i].localeCompare(c, 'zh-CN-u-co-pinyin') >= 0) {
          idx = i;
          break;
        }
      }
      return MAP[idx];
    },
  }
};
