var userMixins = {
  created :  function () {
  },
  data() {
    return {
    }
  },
  computed:{
  },
  methods : {
    updateClick(tp='chat',opt={}, cb){
      var self = this;
      var data = opt;
      if (self.sa) {
        data.o = self.sa.o;
        data.n = self.sa.n;
        data.p = self.sa.p_ab;
      }
      data.tp = tp;
      if ((!data.uid) && self.curRealtor) {
        data.uid = self.curRealtor._id;
      }
      if ((!data.role) && self.role) {
        data.role = self.role;
      }
      if (!data.uid) {
        return;
      }
      if((!data.saletp) && vars.saletp) {
        data.saletp = vars.saletp
      }
      // silent mode
      fetchData('/1.5/stat/realtorContact', {body:data},function (err,ret) {
        if (err || ret.err) {
          return console.error(ret.err);
        } else {
          ret = ret.data;
          if ('function' == typeof cb) {
            cb()
          }
        }
      });
    },
    showUserStats(u){
      var url = "/1.5/stat/realtorStats?id="+u._id;
      RMSrv.openTBrowser(url)
    },
    tbrowser : function (url, cfg2={}) {
      var cfg, ref, srv, self = this;
      cfg = {
        toolbar: {
          height: 44,
          color: '#E03131'
        },
        closeButton: {
          image: 'close',
          align: 'right',
          event: 'closePressed'
        },
        fullscreen: false
      };
      cfg = Object.assign(cfg, cfg2);
      // return alert(url);
      RMSrv.openTBrowser(url, cfg);
      // if(this.ready){
      //   ref = RMSrv.openTBrowser(url, cfg);
      //   return ref != null ? ref.addEventListener('closePressed', function() {
      //     if (ref != null) {
      //       ref.close();
      //     }
      //   }) : void 0;
      // }
    }
  }
};