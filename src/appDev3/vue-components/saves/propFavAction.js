
var propFavActions = {
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {
          lang:'zh-cn',
          allowedEditGrpName:false,
          isLoggedIn:false
        }
      }
    }
  },
  components:{},
  data () {
    return {
      grp:null,
      grpName:'',
      cntr:1,
      MAX_GROUPS:5,
      grps:[],
      isArchived:false,
      grpsSelect:false,
      grpsEdit:false,
      prop:{fav:false,favGrp:[]},
      inputGrpName:'',
      grpsOptions:false,
      mode:'new',
      showCrm:false,
      clnt:null,
      strings:{
        clearTip:{key:"Are you sure you want to clear this folder?",ctx:''},
        deleteTip:{key:"Are you sure you want to delete this folder?",ctx:''},
        cancel: {key:"Cancel",ctx:''},
        delete: {key:"Delete",ctx:''},
        clear: {key:"Clear",ctx:''},
      },
      folderSort:'time'
    }
  },
  mounted () {
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    var self = this, bus = window.bus;
    bus.$on('toggleFav', function (prop) {
      if (!self.dispVar.isLoggedIn) {
        location.href = '/1.5/user/login';
        return
      }
      self.grpsSelect =true;
      self.prop = prop;
      self.grpsEdit = false;
      if (!self.grps.length) {
        self.getGroups({mode:'get'});
      }
      self.toggleGrpSelect();
      // if (self.dispVar.isVipRealtor) {
      //   self.MAX_GROUPS = 50;
      // }
    });
    bus.$on('editGroups',function({grp,grpInfo}){
      self.grp = grp;
      self.grpName = grpInfo.v;
      if(grpInfo.clnt && grpInfo.clnt._id){
        self.clnt = grpInfo.clnt;
      }
      self.grpsOptions = !self.grpsOptions;
    })
    bus.$on('choosed-crm', function (crm) {
      // self.grpsEdit = true;
      self.showCrm = false;
      self.clnt = crm;
      self.inputGrpName = self.inputGrpName.length?self.inputGrpName:crm.nm
    });
    bus.$on('close-crm', function (crm) {
      self.showCrm = false;
    });
    bus.$on('add-fav', function (d) {
      if(d.grps){
        self.grps = d.grps
      }
      self.addFav(d);
    });
    bus.$on('is-archived', function (status) {
      self.isArchived = status;
    });
    self.sortByMtWithoutDefault = window.sortByMtWithoutDefault
  },
  methods: {
    clientName:function(){
      if(this.clnt && this.clnt.nm){
        return this.clnt.nm;
      } else {
        return ''
      }
    },
    showClientFn(){
      if (!this.dispVar.isVipRealtor){
        let confirmVip = this.confirmVip || this.$parent.confirmVip
        return confirmVip(this.dispVar.lang);
      }
      if(this.clnt && this.clnt.nm){
        return ;
      }
      // this.grpsEdit = false;
      this.showCrm = true;
      var cfg = {hide:false,title:this.$_('Contacts','contactCrm')};
      var url = RMSrv.appendDomain('/1.5/crm?noBar=1&isPopup=1&owner=1');
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          var crm = JSON.parse(val);
          // alert(JSON.stringify(crm));
          window.bus.$emit('choosed-crm',crm);
        } catch (e) {
          console.error(e);
        }
      });
    },
    isInGrp(g){
      var grps = this.prop.favGrp || [];
      return grps.indexOf(parseInt(g)) > -1;
    },
    editGrpName(){
      this.inputGrpName = this.grpName;
      this.addNewGrp();
      this.mode = 'edit';
    },
    confirmDelOrClear(type){
      let self = this;
      let stringKey = `${type}Tip`;
      let btnObj = this.strings[type];
      let infoObj = this.strings[stringKey]
      let tipInfo = this.$_(infoObj.key,infoObj.ctx);
      var cancel = this.$_(this.strings.cancel.key,this.strings.cancel.ctx);
      var yes = this.$_(btnObj.key,btnObj.ctx);
      function _doDelete(idx) {
        if (idx+'' == '2') {
          if (type == 'clear'){
            self.clearGrpFavs();
          } else if (type == 'delete') {
            self.removeGrp()
          }
        }
      }
      return RMSrv.dialogConfirm(tipInfo, _doDelete, '', [cancel, yes]);
    },
    clearGrpFavs(opts={}){
      if (this.grp == null) {
        return;
      }
      //post clear fav by grp
      this.getGroups({mode:'clear', grp:this.grp, noEmit:opts.noEmit});
    },
    removeGrp(){
      this.getGroups({mode:'delete', grp:this.grp});
    },
    addNewGrp(){
      var self = this;
      if (!this.dispVar.allowedEditGrpName) {
        // window.bus.$emit('flash-message', self.getTranslate('vip'));
        let confirmVip = self.confirmVip || self.$parent.confirmVip
        return confirmVip(self.dispVar.lang);
      }
      this.grpsEdit = true;
      this.grpsSelect = false;
      this.grpsOptions = false;
    },
    reset(){
      this.grpsEdit = false;
      this.grpsSelect = false;
      this.grpsOptions = false;
      window.bus.$emit('reset');
    },
    addGrpName(p){
      if (!this.inputGrpName) {
        return;
      }
      var post = {nm:this.inputGrpName}
      if(this.clnt && this.clnt._id){
        post.clnt = this.clnt._id
        post.cNm = this.clnt.nm
      }
      if (this.mode == 'edit') {
        post.mode = 'put'
        post.grp = this.grp
      } else {
        post.mode = 'set'
      }
      this.getGroups(post);
    },
    toggleGrpSelect(){
      var self = this;
      self.grpsSelect = true;
      self.grpsEdit = false;
    },
    parseGrps(grp={}){
      var l = [];
      for (let k in grp) {
        if (k !== 'cntr') {
          l.push({idx:k, val:grp[k],mt:(grp[k] ? grp[k].mt : -1)});
        }
      }
      return this.sortByMtWithoutDefault(l);
    },
    getGroups(d){
      var self = this;
      // self.loading = true;
      self.reset();
      trackEventOnGoogle('saves','properties',d.mode+'Group')
      var url = '/1.5/props/propGroups';
      fetchData(url, {body:d}, function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          self.inputGrpName = '';
          if (ret.grps) {
            self.grps = self.parseGrps(ret.grps);
            window.bus.$emit('prop-fav-retrieved', ret.grps);
          }
          if (d.mode == 'set' || d.mode == 'get') {
            self.toggleGrpSelect();
          }
          if(d.mode == 'set'){
            return self.selectGrp(self.grps[1])
          }
          if(ret.grps || d.mode == 'clear' || d.mode == 'delete'){
            d.grps = ret.grps
            window.bus.$emit('afterFavAction',d);
          }
          self.clnt = null;
        }
      });
    },
    selectGrp(g){
      var idx = parseInt(g.idx) || 0;
      this.addFav({prop:this.prop, grp:idx});
    },
    addFav(d){
      var self = this, mode = 'favour', prop=d.prop;
      // TODO: use prop.favGrp for mode
      if (prop.fav && this.isInGrp(d.grp)) {
        mode = 'unfavor';
      }
      if (self.loading) {
        // console.log('already setting fav!!')
        return;
      }
      // window.bus.$emit('set-loading',true)
      trackEventOnGoogle('saves','properties',mode)
      var url = '/1.5/props/favProp';
      var body = {grp:d.grp, id:prop._id,topTs:prop.topTs, mode:mode,src:prop.src};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          var tmpFav = (mode=='favour')?true:false;
          // TODO: Update favGrp
          self.prop.favGrp = self.prop.favGrp || [];
          if (mode == 'favour') {
            if (!self.prop.favGrp) {
              self.prop.favGrp = [];
            }
            self.prop.favGrp.push(d.grp)
            self.grps.forEach(e => {
              if(e && (e.idx == d.grp)){
                e.mt = new Date()
              }
            });
            self.grps = self.sortByMtWithoutDefault(self.grps)
          } else {
            self.prop.favGrp.splice(self.prop.favGrp.indexOf(d.grp),1)
            tmpFav = self.prop.favGrp.length;
          }
          // self.$set('prop.fav', tmpFav);
          prop.fav = tmpFav;
          self.grpsSelect = false;
          window.bus.$emit('prop-fav-changed',{prop,grps:self.grps})
          window.bus.$emit('flash-message', ret.msg);
          // window.bus.$emit('prop-favored', prop);
        }
        // window.bus.$emit('set-loading',false);
      }
      );
    },
    archive(flag){
      var self = this;
      if (self.loading) {
        return;
      }
      trackEventOnGoogle('archive','folder')
      var url = '/1.5/propFav/archive';
      var body = {grp:this.grp, flag};
      fetchData(url,{body},function(err,ret) {
        if (err || ret.e) {
          return RMSrv.dialogAlert(err || ret.e);
        } else {
          self.isArchived = !self.isArchived;
          window.bus.$emit('flash-message', ret.msg);
          self.reset();
          window.bus.$emit('refresh-archive-folder', {grp:self.grp,isArchived:self.isArchived});
        }
      });
    },
    resetClnt(){
      this.clnt=null;
      this.inputGrpName = this.inputGrpName.split(' ')[0]
    },
    gotoSaves(){
      var url = '/1.5/saves/properties?grp=0'
      if (RMSrv.closeAndRedirectRoot) {
        RMSrv.closeAndRedirectRoot(RMSrv.appendDomain(url));
        return;
      }
      window.location = url;
    },
    sortFolder(sortBy){
      this.grps = this.sortByMtWithoutDefault(this.grps,sortBy);
      this.folderSort = sortBy;
    }
  },
  template: `
  <div>
  <div class="backdrop" :class='{show:(grpsOptions || grpsSelect ||grpsEdit),lowIndex:showCrm}' @click='reset()'></div>
  <div class="modal modal-60pc" id="grpSelect" :class='{active:grpsSelect}'>
    <header class="bar bar-nav">{{$_('Save to')}}
      <span class="pull-right link" style="padding-right:0" @click="gotoSaves()">{{$_('All Saved')}}</span>
    </header>
    <div class="folderSort" style="top: 44px;">
      <span id="createBtn" @click="addNewGrp()">
        <span class="fa icon icon-plus"></span>
        <span>{{$_('Create New Folder')}}</span>
      </span>
      <span class="sort">
        <span :class="{select:folderSort == 'time'}" @click="sortFolder('time')">{{$_('Time')}}<span class="fa fa-long-arrow-down"></span></span>
        <span :class="{select:folderSort == 'name'}" @click="sortFolder('name')">{{$_('Name')}}<span class="fa fa-long-arrow-up"></span></span>
      </span>
    </div>
    <div class="content" style="padding-top: 97px;">
      <ul class="table-view">
        <li class="table-view-cell" v-for="g in grps" @click="selectGrp(g)">
          <span class="fa fa-star" v-show="g.idx == '0'"></span>
          <span class="fa" v-show="g.idx !== '0'"></span>
          <span class="group-name">{{g.val.v}}</span>
          <span class="pull-right fa" :class="{'fa-heart-o':!isInGrp(g.idx), 'fa-heart':isInGrp(g.idx)}"></span>
        </li>
      </ul>
    </div>
  </div>
  <div class="modal" id="grpEdit" :class="{active:grpsEdit,lowIndex:showCrm}">
      <div class='bar bar-nav'>{{$_('Edit Folder')}}
      </div>
      <div class="addClient" @click.stop="showClientFn()" v-if="dispVar.isRealtor && mode != 'new'"><span
          class="sprite16-18 sprite16-3-6"></span>
          <span class="editClientName">{{clientName() ||  $_("Choose a client")}}
            <span class="lang" v-if="clientName() && clnt.lang">({{ $_(clnt.lang,'lang')}})</span>
            <span class="lang" v-if="clientName() && !clnt.lang">(En)</span>
          </span>
        <span class="fa fa-rmclose" style="color:#aaa;padding:10px" v-show="clnt && clnt.nm " @click.stop="resetClnt()"></span>
      </div>
      <div class="bar bar-standard bar-header-secondary"><input v-model="inputGrpName" :placeholder="$_('Input folder name')"/></div>
      <div class="btn-cell bar bar-tab">
      <a @click="addGrpName()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{$_('Save')}}</a>
      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_('Cancel')}}</a>
    </div>
  </div>
  <div class="modal" id="grpOpts" :class="{active:grpsOptions}">
      <div class="content">
          <ul class="table-view">
              <li class="table-view-cell" @click="archive(true)" v-if='!isArchived && (grp != 0)'><span>{{$_('Archive Folder')}}</span></li>
              <li class="table-view-cell" @click="archive(false)" v-if='isArchived && (grp != 0)'><span>{{$_('Unarchive Folder')}}</span></li>
              <li class="table-view-cell" @click="editGrpName()"><span>{{$_('Edit Folder Name')}}</span></li>
              <li class="table-view-cell" @click="confirmDelOrClear('clear')"><span>{{$_('Clear Folder')}}</span></li>
              <li class="table-view-cell" @click="confirmDelOrClear('delete')"><span>{{$_('Delete Folder')}}</span></li>
          </ul>
      </div>
  </div>
  <div style="display:none"><span v-for="(v,k) of strings">{{ $_(v.key, v.ctx)}}</span></div>
</div>
  `
};