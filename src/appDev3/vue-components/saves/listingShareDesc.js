var listingShareDesc = {
  computed:{
    propSqft:function(){
      let prop = this.prop || {};
      if (!prop.sqft || /-/.test(prop.sqft)) {
        return prop.sqft
      }
      let int = parseInt(prop.sqft);
      if (!isNaN(int)) {
        return int
      } else {
        return prop.sqft;
      }
    }
  },
  props:{
    isApp:{
      type:Boolean,
      default:false
    },
    prop:{
      type:Object,
      default:function () {
        return {lp:0};
      }
    }
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    isArray (p) {
      return Array.isArray(p);
    },
    isRMProp (prop) {
      return /^RM/.test(prop.id);
    },
    getDesc (m) {
      if (!m) {
        return '';
      }
      return m.length > 70 ? m.substr(0,70)+'...' : m;
    }
  },
  template: `
  <div>
  <span id="share-title-en"><span v-if="!isApp">RealMaster •  </span><span v-if="prop.ltp=='assignment'">Assignment
      • </span><span v-if="prop.ltp=='exlisting'">Exclusive • </span><span
      v-if="prop.ltp=='rent' && !prop.cmstn">Landlord Rent • </span><span
      v-if="prop.ltp=='rent' && prop.cmstn">Rent • </span><span> {{(prop.lp || prop.lpr)}}
      • <span v-if="prop.daddr !== 'N'">{{prop.addr}} {{prop.unt||''}}, </span>{{prop.city_en || prop.city}}
      {{prop.prov_en || prop.prop}}</span>
  </span>
  <span id="share-desc-en">
    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}
    {{ isArray(prop.ptp) ? prop.ptp[0] : (prop.ptype2_en?prop.ptype2_en.join(' '):'') }}
    {{prop.pstyl_en}}
    {{propSqft?', '+propSqft+' Sqft, ':''}}<span v-if="prop.bcf != 'b'">Bedroom: {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, Kitchen:
      {{prop.kch}}, Bathroom: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}}, Parking:
      {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m)}}
  </span>
  <span id="share-title"><span v-if="!isApp">{{$_('RealMaster') }} • </span><span v-if="prop.ltp=='assignment'">
      {{$_('Assignment') }} • </span><span v-if="prop.ltp=='exlisting'"> {{$_('Exclusive','realtor sale')}} • </span><span
      v-if="prop.ltp=='rent' && !prop.cmstn"> {{$_('Landlord Rent')}} • </span><span
      v-if="prop.ltp=='rent' && prop.cmstn"> {{$_('Rent','share-title rent')}} • </span><span>
      {{(prop.lp || prop.lpr)}} • <span v-if="prop.daddr !== 'N'">{{prop.addr}}
        {{prop.unt||''}}, </span>{{prop.city}} {{prop.prov}}</span>
      </span>
  <span id="share-desc">
    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}
    {{ isArray(prop.ptp) ? prop.ptp[3] : (prop.ptype2?prop.ptype2.join(' '):'') }}
    {{propSqft ? ', '+propSqft+' '+$_('Sqf','property')+', ':''}}<span v-if="prop.bcf != 'b'">{{$_('Bedroom')}}:
      {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, {{$_('Kitchen')}}: {{prop.kch}}, {{$_('Bathroom')}}: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}},
      {{$_('Parking')}}: {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m || prop.m_zh)}}
    </span>
</div>
  `
};