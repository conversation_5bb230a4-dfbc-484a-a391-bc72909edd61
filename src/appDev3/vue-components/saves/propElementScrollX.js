var propElementScrollX = {
  props:{
    prop:{
      type:Object,
      default:{}
    },
    rcmdHeight:{
      type:Number,
    },
    formPage:{
      type:String,
      default:''
    },
    favMap:{
      type:Object,
      default:{}
    },
    dispVar:{
      default:function () {
        return {
          lang:'zh-cn',
        }
      }
    }
  },
  mounted() {
    if (!(bus = window.bus)) {
      return console.error('global bus required!');
    }
    this.calcMktWraper();
  },
  methods:{
    calcMktWraper(){
      var mktWraper = document.getElementsByClassName('mktrcmd')
      if(mktWraper.length){
        var calculatedMktWidth = parseInt((window.innerWidth-30)/2.05)*2;
        var numOfRecos = document.querySelectorAll('.mkt').length;
        var mktWidth = calculatedMktWidth * numOfRecos;
        document.getElementsByClassName('mktrcmd')[0].style.width = mktWidth+'px';
        var mkts = document.getElementsByClassName('mkt');
        for (i = 0; i < mkts.length; i++) {
          mkts[i].style.width = calculatedMktWidth+'px';
        }
      }
    },
    getStatus:function(prop) {
      var status = prop.status == 'U' ? 'sold' : 'sale';
      return status;
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    openDetail:function(prop) {
      var id = (/^RM/.test(prop.id))?prop.id:prop._id;
      var propUrl = '/1.5/prop/detail/inapp?id='+id;
      if (this.formPage) {
        propUrl += `&formPage=${this.formPage}`;
      }
      openPopup(propUrl,this.$_('RealMaster'));//lang='+this.dispVar.lang+'
    },
    getPropDate(prop) {
      if (prop.status_en == 'U') {
        return prop.sldd;
      } else {
        return prop.mt.substr(0,10);
      }
    },
    soldOrLeased: function (prop) {
      return prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(prop.lst);
    },
    saletpIsSale: function (prop) {
      if (!prop.saletp_en) {
        return true;
      }
      if (/sale/.test(prop.saletp_en.toString().toLowerCase())) {
        return true;
      }
      return false;
    },
    isTop:function(prop) {
      return (prop.status == 'A') && (new Date(prop.topTs) >= new Date());
    },
    propSid:function (prop) {
      if (prop.isProj) {
        return '';
      } else if (prop.sid) {
        return prop.sid;
      } else if (/^RM/.test(prop.id)) {
        return prop.id;
      } else {
        return prop._id?prop._id.substr(3):'';
      }
    },
    rmgrStr: function (prop) {
      return prop.rmgr || prop.tgr || prop.gr;
    },
    rmbdrmStr: function (prop) {
      if (prop.rmbdrm) {
        return prop.rmbdrm;
      }
      let tmp = prop.bdrms || prop.tbdrms;
      tmp += prop.br_plus?'+ '+prop.br_plus:''
      return tmp;
    },
    rmbthrmStr: function (prop) {
      return prop.rmbthrm || prop.tbthrms || prop.bthrms;
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    isInGrp(g){
      var grps = this.prop.favGrp || [];
      return grps.indexOf(parseInt(g)) > -1;
    },
    toggleFav(prop){
      window.bus.$emit('toggleFav',prop);
    },
    computedVideoUrl:function(){
      if (this.dispVar.isCip) {
        return this.prop.vurlcn;
      } else {
        if (this.prop.ytvid) {
          return "https://www.youtube.com/watch?v="+this.prop.ytvid;
        }
        return null;
      }
    },
    isPropUnavailable:function () {
      if (this.prop.id && this.prop.id.substr(0,2) == 'RM') {
        return false;
      }
      return this.prop.status_en !== 'A';
    },
    selectGroup:function(g){
      window.bus.$emit('selectGroup',g);
    },
    dotdate (d, isCh, split='.') {
      // console.log(d);
      if (!d) {
        return '';
      }
      if ('number' == typeof d) {
        d += '';
        d = d.slice(0,4)+'/'+d.slice(4,6)+'/'+d.slice(6,8)
      }
      //not supported by ios
      // var isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream;
      if ('string' == typeof d && !/\d+Z/.test(d)) {
        d += ' EST';
      }
      var split1 = isCh?'年':split;
      var split2 = isCh?'月':split;
      var split3 = isCh?'日':'';
      if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) && !/\d+Z/.test(d)) {
        var d2 = d.split(' ')[0].split('-');
        return d2[0]+split1+d2[1]+split2+d2[2]+split3;
      }
      var t = new Date(d);
      if (!t || isNaN( t.getTime() )) {
        // console.log('isIos: '+isIOS+' ivalid date: '+d);
        return d;
      }
      return t.getFullYear() + split1 + (t.getMonth()+1) + split2 + t.getDate() + split3;
    }
  },
  watch:{
  },
  template: `
  <div @click="openDetail(prop)" class="mkt rmProp rcmdProp">
  <div class="img">
    <img class="lazy" :src='prop.thumbUrl || "/img/noPic.png"' :style="{ 'height':rcmdHeight * 0.872+'px'}"/>
    <div class="tag" v-show='prop.type'>
      <span class="type">{{prop.type}}</span>
    </div>
    <span class="pull-right fav fa" :class="{'fa-heart-o':!prop.fav, 'fa-heart':prop.fav}"
      @click.stop.prevent="toggleFav(prop)"></span>
    <div class="showOnImg prop">
      <div class='price' style='overflow: auto;'>
        <span class="val" v-if='prop.priceValStrRed'>{{prop.priceValStrRed}}</span>
        <span class="val" v-else-if='prop.askingPriceStr'>{{prop.askingPriceStr}}</span>
        <span class="desc" v-if="prop.priceValStrRedDesc" :class="{'through':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>
        <span class="desc" v-if="prop.lstStr && (prop.tagColor != 'red')">({{prop.lstStr}})</span>
        <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !='Delisted'" :class="prop.tagColor" style="margin-left:10px;">
          <span>{{prop.saleTpTag}}</span>
          <span v-if="(prop.tagColor == 'red'|| prop.tagColor == 'green') && (prop.spcts||prop.mt||prop.ts) ">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>
        </span>
      </div>
      <div class="bdrms">
        <span v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null"><span class="fa fa-rmbed"></span> {{rmbdrmStr(prop)}}</span>
        <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null"><span class="fa fa-rmbath"></span> {{rmbthrmStr(prop)}}</span>
        <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null"><span class="fa fa-rmcar"></span> {{rmgrStr(prop)}}</span>
      </div>
    </div>
  </div>
  <div class="detail">
    <div class="prov" v-show='prop.addr'>
      <span class="fa fa-map-marker"></span> {{prop.city}}, {{prop.prov}}</div>
    <div v-if='favMap[0]'>
      <span v-for='(g,i) in prop.favGrp' v-show='favMap[g]'>
        <span v-show='i>0'> &#183; </span>
        <span class="exlinks" @click.stop='selectGroup(g)'>{{favMap[g]?favMap[g].v:''}}</span>
      </span>
    </div>
  </div>
</div>
  `
};