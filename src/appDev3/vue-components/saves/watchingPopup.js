
var watchingPopup = {
  props: {
    watchingType: {
      type: String,
      default: ''
    },
    dataFrom:{
      type: String,
      default: ''
    },
    dispVar:{
      default:function () {
        return {
          isEmailVerified:false,
        }
      }
    }
  },
  data () {
    return {
      ranges:[0,100,300,500],
      WATCH_TYPES:WATCH_TYPES,
      WATCH_EVENTS:WATCH_EVENTS,
      strings:{
        location:{key:'Location Notification'},
        cmty:{key:'Community Notification'},
      },
      watchingDefault:{
        range:0,
        propType:[],
        watchEvent:[]
      },
      watching:{},
      prop:{
        showAddr:'',
        cmty:'',
        watching:{}
      },
      idx:0,
      hasPropWatching:false,
      isNotOpenNotify:false,
      hasGoogleService:false,
    };
  },
  components:{
    checkNotification
  },
  mounted() {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this,bus=window.bus;
    bus.$on('watch-item-change',function ({item,idx}) {
      self.prop = item;
      self.idx = idx;
      self.initWatching(self.watchingType)
    });
  },
  methods:{
    close(){
      this.watching = {}
      bus.$emit('close-watch-popup', {});
    },
    getNotificationType(){
      if(this.watchingType == 'cmty'){
        return this.strings.cmty.key;
      }else{
        return this.strings.location.key;
      }
    },
    getAddress(){
      if(this.watchingType == 'cmty'){
        return `${this.prop.cmty || this.prop.nm}, ${this.prop.city}` ;
      }else{
        return this.prop.nm;
      }
    },
    setFeature(tp,val){
      if(tp == 'range'){
        this.watching.range = val;
      }else{
        var index = this.watching[tp].indexOf(val)
        if(index>-1){
          if(this.watching[tp].length == 1){
            return bus.$emit('flash-message',this.$_('Choose at least one of each'));
          }
          this.watching[tp].splice(index,1)
        }else{
          this.watching[tp].push(val);
        }
      }
    },
    getDisableOKBtn(){
      for(var k in this.watching){
        if(this.watching[k].length == 0){
          return true;
        }
      }
      return false;
    },
    getDisableUnwatchBtn(){
      if(this.hasPropWatching){
        return false
      }
      return true
    },
    watchProps(){
      var self = this,params;
      if(this.dataFrom == 'cmty' && this.watchingType == 'cmty'){
        params={
          uaddr:self.prop._id,
          tp:this.watchingType,
        }
        params.nm = params.cmty = self.prop.nm
        if(self.prop.pos){
          params.loc=self.prop.pos;
        }
      }else{
        params=this.prop;
      }
      params = Object.assign(params,self.watching);
      var savedSearchTxtArray = [];
      for (var filter in this.watching) {
        var v = this.watching[filter]
        if(filter == 'range'){
          continue
        }
        if(Array.isArray(v)){
          v = v.join(' • ')
        }
        savedSearchTxtArray.push(v);
      }
      this.prop.display = savedSearchTxtArray.join(' • ');
      this.prop.watching = this.watching;
      this.hasPropWatching = true;
      this.close()
      bus.$emit('watch-prop', {unwatch:false,prop:this.prop,idx:this.idx});
      this.postWatch(params);
    },
    postWatch(params){
      var self = this;
      if(!params.uaddr){
        return
      }
      getStandardFunctionCaller() ('watchLocation',params,(err,msg)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        bus.$emit('flash-message',msg);
        trackEventOnGoogle('saves',this.watchingType == 'cmty'?'cmty':'location','editWatch')
      })
    },
    unwatch(){
      var params={
        tp:this.watchingType,
      }
      if(this.prop._id.indexOf('CA')>-1){
        params.uaddr=this.prop._id;
      }else{
        params.uaddr=this.prop.uaddr;
      }
      params.unwatch = true;
      this.hasPropWatching = false;
      this.postWatch(params);
      this.close()
      this.initWatching(this.watchingType,true)
      bus.$emit('watch-prop', {unwatch:true,prop:this.prop,idx:this.idx});
    },
    initWatching (val,reset){
      this.watching = JSON.parse(JSON.stringify(this.watchingDefault));
      var watching = this.prop.watching;
      if(reset){
        this.hasPropWatching = false;
      }else{
        this.hasPropWatching = watching&&Object.keys(watching).length>0;
      }
      if(this.hasPropWatching){
        this.watching = Object.assign({},watching);
      }else{
        if(val == 'cmty'){
          delete this.watching.range;
        }else if(val == 'building'){
          delete this.watching.propType;
        }
      }
    },
  },
  template:
  `
<div class="modal" id="watchingModal" v-cloak="">
  <div class="bar bar-standard bar-footer back">
    <button class="btn btn-half btn-nooutline btn-fill btn-sharp red" :disabled='getDisableOKBtn()' @click="watchProps()">
      {{$_('Save')}}
    </button>
    <button class="btn btn-half btn-nooutline btn-fill btn-sharp gray" :disabled='getDisableUnwatchBtn()' @click="unwatch()">
      {{$_('Unwatch')}}
    </button>
  </div>
  <div style="background: #f1f1f1;">
    <div class="wrapper">
      <ul class="slideMenu table-view options">
        <li class="table-view-cell">
          <a>
            <div class="name" style='font-size: 17px;'>{{$_(getNotificationType())}}</div>
            <span class="saved-searches-desc">{{getAddress()}}</span>
          </a>
          <span class='close icon icon-close' @click='close()'></span>
        </li>
        <li class="table-view-cell has-btn-grp" v-if="watching.range != undefined">
          <div class="tl">
            <span class="name">{{$_('Distance')}}
              <span class="saved-searches-desc" v-if='watching.range != 0'>({{$_("Within")}} {{prop.cmty}})</span>
            </span>
            <span class="input-desc" v-show='watching.range == 0 && watchingType == "building"'>{{$_('Units in this building')}}</span>
            <span class="input-desc" v-show='watching.range == 0 && watchingType == "location"'>{{$_('Only this property')}}</span>
          </div>
          <div class="btn-sets">
            <a class="btn btn-default" v-for="range in ranges" href="javascript:void 0"
              @click="setFeature('range',range)"
              :class="{active:watching.range == range}">{{range}}{{$_('m','distance')}}</a>
          </div>
        </li>
        <li class="table-view-cell has-btn-grp" v-if="watching.propType">
          <div class="tl">
            <span class="name">{{$_('Prop Type')}}</span><span class="input-desc">{{$_('Multi-Select')}}</span>
          </div>
          <div class="btn-sets">
            <a class="btn btn-default" v-for="type in WATCH_TYPES" href="javascript:void 0"
              @click="setFeature('propType',type)"
              :class="{active:watching.propType.indexOf(type) > -1}">{{$_(type)}}</a>
          </div>
        </li>
        <li class="table-view-cell has-btn-grp" v-if="watching.watchEvent">
          <div class="tl">
            <span class="name">{{$_('Events')}}</span><span class="input-desc">{{$_('Multi-Select')}}</span>
          </div>
          <div class="btn-sets">
            <a class="btn btn-default" href="javascript:void 0" @click="setFeature('watchEvent',event)"
              v-for="event of WATCH_EVENTS" :class="{active:watching.watchEvent.indexOf(event) > -1}">{{$_(event)}}</a>
          </div>
        </li>
        <check-notification></check-notification>
      </ul>
    </div>
  </div>
  <div style="display:none">
    <span v-for="(v,k) of strings">{{$_(v.key)}}</span>
  </div>
</div>
  `
};