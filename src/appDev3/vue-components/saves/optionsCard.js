
var optionsCard = {
  props:{
    item:{
      type:Object
    },
    idx:{
      type:Number
    },
    noBar:{
      type:Boolean,
      default:true
    },
    web:{
      type:Boolean,
      default:false
    },
    dispVar:{
      default:function () {
        return {
          lang:'zh-cn',
        }
      }
    }
  },
  data(){
    return {
    }
  },
  mounted() {
  },
  methods:{
    goToList(sale){
      window.bus.$emit('goToList',{item:this.item,sale})
    },
    showEdit(){
      window.bus.$emit('showEdit',{item:this.item,idx:this.idx})
    },
    showDelete(){
      window.bus.$emit('showDelete',{item:this.item,idx:this.idx})
    },
    remove(){
      window.bus.$emit('remove',{item:this.item,idx:this.idx})
    }
  },
  template:
  `
<li class="table-view-cell">
  <div class="nm" v-show="item.nm">{{item.nm}}
    <span v-show="item.isBuilding" >({{$_('Building')}})</span>
  </div>
  <div class="other"><span>{{item.display}} </span></div>
  <div class="distance"><span v-if='typeof(item.range)=="number"'>{{item.range}}{{$_('m','distance')}}</span></div>
  <div class='btnBox'>
    <span class="pull-right delbtns btn btn-nooutline">
      <span class="cancle pull-right btn btn-nooutline" v-show="item.del" @click.stop.prevent="item.del = false" >{{$_('Cancel')}}</span>
      <span class="delete pull-right btn btn-negative" v-show="item.del" @click.stop.prevent="remove()" >{{$_('Delete')}}</span>
    </span>
    <span class="btn btn-nooutline editBtn searchBtn"  v-show="!item.del" @click.stop.prevent="goToList(true)">{{$_('SALE')}}</span>
    <span class="btn btn-nooutline editBtn searchBtn"  v-show="!item.del" @click.stop.prevent="goToList(false)">{{$_('RENT')}}</span>
    <span class="pull-right btn btn-nooutline fa fa-trash" style="font-size: 15px;color: rgb(181, 181, 181);padding: 5px 0 7px 10px;" v-if="!web" v-show="!item.del" @click.stop.prevent="showDelete()"></span>
    <span class="pull-right btn btn-nooutline editBtn" v-if="!web" v-show="!item.del" @click.stop.prevent="showEdit()" >{{$_('EDIT')}}</span>
  </div>
</li>
  `
};