
var searchOption = {
  props:{
    item:{
      type:Object
    },
    idx:{
      type:Number
    },
    noBar:{
      type:Boolean,
      default:true
    },
    web:{
      type:Boolean,
      default:false
    },
    dispVar:{
      default:function () {
        return {
          lang:'zh-cn',
        }
      }
    }
  },
  data(){
    return {
    }
  },
  mounted() {
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    // openDetail:function(proj) {
    //   var id = (/^RM/.test(proj.id))?proj.id:proj._id;
    //   openPopup('/1.5/prop/projects/detail?id='+this.proj._id+'&lang='+this.dispVar.lang);
    // },
    setAdvSlideVal(){
      window.bus.$emit('setAdvSlideVal',{item:this.item,idx:this.idx})
    },
    toggleHandleStyle(){
      if (this.item.sbscb){
        return "transform: translate3d(32px, 0px, 0px);"
      } else {
        return "transform: translate3d(0px, 0px, 0px);"
      }
    },
    parseArray(v){
      if (Array.isArray(v)) {
        return v.join(' ')
      }
      return v;
    },
    subscribe2client(){
      window.bus.$emit('subscribe2client',{item:this.item,idx:this.idx})
    },
    showEditSavedSearch(){
      window.bus.$emit('showEditSavedSearch',{item:this.item,idx:this.idx})
    },
    showDelSavedSearch(){
      window.bus.$emit('showDelSavedSearch',{item:this.item,idx:this.idx})
    },
    removeSavedSearch(){
      window.bus.$emit('removeSavedSearch',{item:this.item,idx:this.idx})
    }
  },
  template:
  `
<li class="table-view-cell" @click.stop="setAdvSlideVal()">{{item.v}}
  <div class="nm" v-show="item.nm">{{item.nm}}</div>
  <div class="other" :class='{hasBnds:item.mapBndsImg}'>
    <img v-if='item.mapBndsImg' :src='item.mapBndsImg'/>
    <span>{{item.display}} </span>
  </div>

  <div class="subscribe">
    <div class="notification-toggle">
      <span>{{$_('Notification')}}</span>
      <div class="toggle" :id="'sbscb-'+idx+'-toggle'" :class="{'active':item.sbscb}" @click.stop.prevent="subscribe2client()">
        <div class="toggle-handle" :id="'sbscb-'+idx+'-toggle-handle'" :style='toggleHandleStyle()'></div>
      </div>
    </div>
    <div class="forward-to-span" v-show="item.sbscb &amp;&amp; item.clnt &amp;&amp; item.clnt.nm">
      <span class="sprite16-18 sprite16-3-6 forward-to-icon"> </span>
      <span class="clntNm" v-show="item.clnt &amp;&amp; item.clnt.nm">{{item.clnt.nm}}</span>
    </div>
  </div>
  <div>
    <span class="delbtns btn btn-nooutline pull-right">
      <span class="cancle pull-right btn btn-nooutline" v-show="item.del" @click.stop.prevent="item.del = false" >{{$_('Cancel')}}</span>
      <span class="delete pull-right btn btn-negative" v-show="item.del" @click.stop.prevent="removeSavedSearch()" >{{$_('Delete')}}</span>
    </span>
    <span class="btn btn-nooutline editBtn searchBtn" v-if="!web" v-show="!item.del" @click.stop.prevent="setAdvSlideVal()">{{$_('SEARCH')}}</span>
    <span class="pull-right btn btn-nooutline fa fa-trash" style="font-size: 15px;color:#b5b5b5;padding: 5px 0 7px 10px;" v-if="!web" v-show="!item.del" @click.stop.prevent="showDelSavedSearch()"></span>
    <span class="pull-right btn btn-nooutline editBtn" v-if="!web" v-show="!item.del" @click.stop.prevent="showEditSavedSearch()" >{{$_('EDIT')}}</span>
  </div>
</li>
  `
};