var shareDialog={
  props: {
    option:{
      type: Object,
    },
    noAdvance:{
      type:Boolean,
    },
    height: {
      type: Number
    },
    wDl:{
      type: Boolean
    },
    wSign:{
      type: Boolean
    },
    wComment: {
      type: Boolean,
      default:true,
    },
    dispVar:{
      type: Object,
      default:function(){
        return {
          isRealtor:false,
          isVipRealtor:false,
          lang:'zh-cn'
        }
      }
    },
    noSign:{
      type:Boolean
    },
    noLang:{
      type:Boolean
    },
    prop:{
      type: Object,
      required: true,
      default:function () {
        return {};
      }
    },
    showComment: {
      type:Boolean,
      default: function() {
        return false;
      }
    },
    noFlyer: {
      type:Boolean,
      default: function() {
        return false;
      }
    },
  },
  data () {
    return {
      wSign2:true,
      wDl2:true,
      wCommentCheck: true,
    };
  },
  watch:{
    // wSign2:function(val){
    //   window.bus.$emit('valute-modify-from-child',{fld:'wSign',v:val})
    //   this.$emit('update:wSign',val)
    // },
    // wDl2:function(val){
    //   window.bus.$emit('valute-modify-from-child',{fld:'wDl',v:val})
    //   this.$emit('update:wDl',val)
    // },
    // wCommentCheck:function() {
    //   window.bus.$emit('wCommentChange',this.wCommentCheck);
    // }
  },
  mounted () {
    var self = this;
    this.wCommentCheck = this.wComment;
    // bus.$on('pagedata-retrieved', function (d) {
    //   setTimeout(function () {
    //     if (self.dispVar.height) {
    //       self.height = self.dispVar.height;
    //     }
    //     if (self.dispVar.publishNews) {
    //       self.height = 450;
    //     }
    //   }, 10);
    // });
    // if (!window.bus) {
    //   console.error('global bus is required!');
    //   return;
    // }
    if (this.wSign == false) {
      this.wSign2 = false;
    }
  },
  computed:{
    isProj:function(){
      var prop = this.prop;
      if (prop.deposit_m || prop.closingDate){
        return true;
      }
      return false;
    },
    computedVersion:function(){
      return this.$parent.isNewerVer(this.dispVar.coreVer,'5.6.0')
      // return this.dispVar.appVerNum >= 5.6;
    },
    showPromo:{
      cache:false,
      get(){
        return this.dispVar.isRealtor && !/^RM/.test(this.prop.id);
      }
    },
    showSignature:{
      cache:false,
      get(){
        return this.dispVar.isRealtor;
      }
    },
    wDlDisable:{
      cache:false,
      get(){
        if (!this.dispVar) {
          return true;
        }
        if (this.dispVar.isRealtor && !this.dispVar.isVipRealtor) {
          return true;
        }
        return false;
      }
    },
    wSignDisable:function () {
      if (!this.dispVar) {
        return true;
      }
      return false;
    }
  },
  methods: {
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    checkIsAllowed(tp){
      if (!this.dispVar.shareLinks) {
        return false;
      }
      var l = this.dispVar.shareLinks.l || [];
      var v = this.dispVar.shareLinks.v || [];
      var idx = l.indexOf(tp);
      var allowed = this.dispVar.isVipRealtor || v[idx] === 0;
      return (idx > -1) && allowed;
    },
    rmShare(tp, doc){
      RMSrv.share(tp, doc);
    },
    rmCustWechatShare(){
      // if (!this.dispVar.isVipUser) {
      //   return this.confirmVip(this.dispVar.lang);
      // }
      // console.log(this.computedVersion,this.$parent.isNewerVer(this.dispVar.coreVer,'6.0.2'))
      if (!this.computedVersion) {
        return this.confirmUpgrade(this.dispVar.lang);
      }
      var url = '/1.5/htmltoimg/templatelist?id=';
      var id = this.prop._id;
      if (/^RM/.test(this.prop.id)) {
        id = this.prop.id;
      }
      url += id;
      RMSrv.openTBrowser(url,{title:this._('Choose Template')});
      // RMSrv.share('wechat-cust',null,{type:'imageUrl'})
    },
    rmsrvDownloadImage(){ // TODO: @Rain seems not used
      var url = 'http://img.realmaster.com/mls/1/668/W4069668.jpg',$el;
      if ($el = document.querySelector('#share-image')) {
        url = $el.textContent;
      }
      RMSrv.downloadImage(url,{},function(err,ret){
        let msg = err?err:ret;
        RMSrv.dialogAlert(msg);
      })
    },
    toggleDrop(cmd){
      window.bus.$emit('toggle-drop',cmd);
    },
    cancelPromoteModal () {
      RMSrv.share('hide');
      // this.toggleDrop('hide');
    },
    promote (to) {
      if (!this.checkIsAllowed(to)) {
        // return window.bus.$emit('flash-message',this.$parent._('VIP Only'));
        return this.confirmVip(this.dispVar.lang);
      }
      var url = "/1.5/promote/mylisting?ml_num=" + this.prop.sid + '&to=' + to;
      if (this.$parent && this.$parent.toggleDrop) {
        this.$parent.toggleDrop();
      }
      RMSrv.share('hide');
      if (this.$parent.closeAndRedirect) {
        this.$parent.closeAndRedirect(url);
      } else {
        window.location = url;
      }
    },
    createWePage(type){
      if (!this.checkIsAllowed(type)) {
        // return window.bus.$emit('flash-message',this.$parent._('VIP Only'));
        return this.confirmVip(this.dispVar.lang);
      }
      var url = '/1.5/wecard/edit/'+(this.prop._id || this.prop.sid)+'?shSty=';
      if (type == 'vt' || type == 'blog') {
         url += type;
      } else if (type == 'mylisting') {
        url = '/1.5/promote/mylisting?ml_num='+this.prop._id;
      }
      if (this.$parent.closeAndRedirect) {
        this.$parent.closeAndRedirect(url);
      } else {
        window.location = url;
      }
    },
    // addToShowing(){
    //   RMSrv.share('hide');
    //   window.bus.$emit('prop-showing-add', this.prop._id);
    // }
  },
  template:
    `
<div id="shareDialog">
  <div class="backdrop" id="backdrop" style="display:none"></div>
  <nav class="menu slide-menu-bottom smb-md" :style="{height:height}">
      <div class="first-row" :class="{visitor:!dispVar.isRealtor}">
          <div @click="rmCustWechatShare()" v-show="dispVar.isRealtor && !isProj && !noFlyer"><span class="sprite50-45 sprite50-5-1"></span>
              <div class="inline">{{$_('Wechat Flyer')}}</div>
          </div>
          <div @click="rmShare('wechat-moment')"><span class="sprite50-45 sprite50-5-3"></span>
              <div class="inline">{{$_('Wechat Moment')}}</div>
          </div>
          <div @click="rmShare('wechat-friend')"><span class="sprite50-45 sprite50-5-2"></span>
              <div class="inline">{{$_('Wechat Friend')}}</div>
          </div>
          <div @click="rmShare('facebook-feed')"><span class="sprite50-45 sprite50-5-4"></span>
              <div class="inline">{{$_('Facebook')}}</div>
          </div>
          <div @click="rmShare('qr-code')"><span class="sprite50-45 sprite50-6-1"></span>
              <div class="inline">{{$_('QR-Code')}}</div>
          </div>
          <div @click="rmShare('other')"><span class="sprite50-45 sprite50-5-5"></span>
              <div class="inline">{{$_('More')}}</div>
          </div>
      </div>
      <div class="split" v-show="dispVar.isRealtor && !dispVar.listShareMode">
          <div class="left inline"></div>
          <div class="text inline"><span>{{$_('Advanced Features')}}</span></div>
          <div class="right inline"></div>
      </div>
      <div class="second-row" v-show="(dispVar.isRealtor || dispVar.isDevGroup) && !dispVar.listShareMode && !noAdvance">
          <div id="shareToNews" v-show="dispVar.publishNews && dispVar.shareLinks.l.indexOf('news')>-1"><span class="sprite50-45 sprite50-6-3"></span>
              <div class="inline">{{$_('News')}}</div>
          </div>
          <div @click="promote('58')" ngClick="promote('58', formData); toggleModal('savePromoteModal')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf('58')>-1 && prop.pcls !== 'b' && prop.src == 'TRB'"><span class="sprite50-45 sprite50-4-4"></span>
              <div class="inline">58.com</div>
          </div>
          <div @click="promote('market')" ngClick="promote('market', formData); toggleModal('savePromoteModal')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf('market')>-1 && prop.pcls !== 'b' && prop.src == 'TRB'"><span class="sprite50-45 sprite50-4-2"></span>
              <div class="inline">{{$_('Listing Market')}}</div>
          </div>
          <div @click="createWePage('vt')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf('vt')>-1 && prop.src == 'TRB'"><span class="sprite50-45 sprite50-4-3"></span>
              <div class="inline">{{$_('WePage Flyer')}}</div>
          </div>
          <div @click="createWePage('blog')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf('blog')>-1 && prop.src == 'TRB'"><span class="sprite50-45 sprite50-4-5"></span>
              <div class="inline">{{$_('WePage Blog')}}</div>
          </div>
      </div>
      <div class="cancel">
          <div class="promoWrapper" v-show="!noSign">
              <div class="inline" id="id_with_sign_wrapper" v-show="showSignature"><label id="id_with_sign"><input type="checkbox" v-model="wSign2" checked="true" :class="{disabled:wSignDisable}"/> {{$_('Signature')}}</label></div>
              <div class="inline" id="id_with_dl_wrapper" v-show="showPromo"><label id="id_with_dl" v-show="dispVar.isVipUser"><input type="checkbox" v-model="wDl2" checked="true" :class="{disabled:wDlDisable}" :disabled="wDlDisable"/>{{$_('Promo')}}</label></div>
              <div class="inline" v-show="showComment"><label id="id_with_cm"><input type="checkbox" v-model="wCommentCheck"/>{{$_('With Comments')}}</label></div>
          </div>
          <div class="lang-selectors-wrapper">
              <div class="segmented-control lang-selectors">
              <a class="control-item lang-selector" id="id_share_lang_en" onclick="RMSrv.share('lang-en');" href="javascript:;" v-if="dispVar.lang != 'en'">En</a>
              <a class="control-item lang-selector" id="id_share_lang_zh" onclick="RMSrv.share('lang-zh-cn');" href="javascript:;" v-if="!(dispVar.lang == 'zh'||dispVar.lang == 'zh-cn')">Zh</a>
              <a class="control-item lang-selector" id="id_share_lang_kr" onclick="RMSrv.share('lang-kr');" href="javascript:;" v-if="dispVar.lang != 'kr'">Kr</a>
              <a class="control-item lang-selector active" id="id_share_lang_cur" onclick="RMSrv.share('lang-cur');" href="javascript:;">
                <span v-show="dispVar.lang == 'zh'">繁</span>
                <span v-show="dispVar.lang == 'zh-cn'">中</span>
                <span v-show="dispVar.lang == 'kr'">한</span>
                <span v-show="dispVar.lang !== 'zh' && dispVar.lang !== 'zh-cn' && dispVar.lang !== 'kr'">En</span>
              </a></div>
          </div><a class="cancel-btn" href="javascript:;" @click="cancelPromoteModal()">{{$_('Cancel')}}</a></div>
  </nav>
  <div class="pic" id="id_share_qrcode">
      <div id="id_share_qrcode_holder"></div><br/>
      <div style="border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;"></div><button class="btn btn-block btn-long" onclick="RMSrv.share('qr-code-close');" style="border:1px none;">{{$_('Close')}}</button></div>
    <div class="hide" style="display:none">{{$_('Available only for Premium VIP user! Upgrade and get more advanced features.')}} {{$_('See More')}} {{$_('Later')}}
  </div>
</div>
    `
};