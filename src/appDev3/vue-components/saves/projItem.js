
var projItem = {
  props:{
    proj:{
      type:Object
    },
    rcmdHeight:{
      type:Number,
    },
    hasWechat:{
      type:Boolean,
    },
    dispVar:{
      default:function () {
        return {
          lang:'zh-cn',
        }
      }
    }
  },
  mounted() {
  },
  computed:{
    computedBgImg:function(){
      return 'url(' + this.proj.img.l[0] + '), url("/img/noPic.png")';
    }
  },
  methods:{
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    openDetail:function(proj) {
      var id = (/^RM/.test(proj.id))?proj.id:proj._id;
      openPopup('/1.5/prop/projects/detail?id='+id+'&lang='+this.dispVar.lang+"&d="+vars.d,'RealMaster');
    },
    remove(p){
      var self = this;
      var optTip = optTip?optTip:"deleteFromDb";
      var tip = self.getTranslate(optTip);
      var later = self.getTranslate('cancel');
      var seemore = self.getTranslate('delete');
      var optTl = optTl?optTl:"";
      function _doShowEditProfile(idx) {
        var url = '/1.5/settings/editProfile';
        if (idx+'' == '2') {
          fetchData('/1.5/prop/projects/remove', {body:{_id:p._id}},function(err,ret) {
            if (err || ret.err) {
              return RMSrv.dialogAlert(err || ret.err);
            } else {
              if (ret.ok) {
                self.getProjList();
                window.bus.$emit('remove-proj')
                window.bus.$emit('flash-message',self.getTranslate('removed'));
              } else {
                RMSrv.dialogAlert(ret.err);
              }
            }
          })
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowEditProfile, optTl, [later, seemore]);
    },
    edit(p, isCopy){
      var url = '/1.5/prop/projects/edit?id='+p._id+'&d=/1.5/saves/projects';
      if (isCopy) {
        url += '&copy=true';
      }
      window.location = url;
    },
    settop(b, rcmdMode){
      var self = this;
      var top = b.top?true:false;
      var rcmd = b.rcmd?true:false;
      var params = {_id:b._id,top:top,rcmd:rcmd}
      if (rcmdMode) {
        delete params.top;
      } else {
        delete params.rcmd;
      }
      function success(ret) {
        ret = ret.data;
        if (ret.ok) {
          // window.bus.$emit('flash-message',ret.msg)
        } else {
          RMSrv.dialogAlert(ret.err);
        }
      }
      function failed(ret) {
        console.error('server-error:'+ret);
        RMSrv.dialogAlert('server-error');
      }
      fetchData('/1.5/prop/projects/top', {body:params},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          if (rcmdMode) {
            b.rcmd = !rcmd;
          } else {
            b.top = !top;
          }
        }
      })
    },
    toggleFav(){
      var hasFaved = this.proj.fav?true:false;
      var self = this;
      var url = '/1.5/prop/projects/fav';
      var d = {fav:hasFaved, _id:self.proj._id};
      fetchData(url, {body:d},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          self.proj.fav= !self.proj.fav;
          window.bus.$emit('flash-message',ret.msg)
        }
      })
    }
  },
  watch:{
  },
  template: `
  <div class="list-element" @click="openDetail(proj)">
    <div class="img-wrapper" :style="{ 'background-image': computedBgImg, 'height':rcmdHeight+'px'}">
      <div class="on-top">
        <div class="avt" v-if="hasWechat && proj.spuAvt && !dispVar.hasFollowedRealtor">
        <img :src="proj.spuAvt"  src="/img/noPic.png"/><span class="vvip"><span v-show="dispVar.lang == 'en'"> {{proj.spuNm_en}}</span><span
              v-show="dispVar.lang !== 'en'">{{proj.spuNm}}</span></span>
        </div>
        <!-- <div class="top" v-show="proj.top">{{getTranslate('top')}}</div>
        <div class="top" v-show="proj.rcmd">{{getTranslate('rcmd')}}</div> -->
        <span class="pull-right fav fa" :class="{'fa-heart-o':!proj.fav, 'fa-heart':proj.fav}"
          @click.stop.prevent="toggleFav(proj)"></span>
      </div>
    </div>
    <div class="on-image"><span v-if="dispVar.lang == 'en'">{{proj.desc_en}}</span><span v-else>{{proj.desc}}</span>
    </div>
    <div class="tl">
      <div class="left">
        <div class="nm">
          <span v-if="dispVar.lang == 'en'">{{proj.nm_en || proj.nm}}</span><span v-else>{{proj.nm}}</span>
        </div>
        <div class="addr">
          <div>{{proj.city}}, {{proj.prov}}</div>
        </div>
        <div v-if="dispVar.isProjAdmin" style="text-align:right"><a class="edit" href="javascript:;"
            @click.stop.prevent="remove(proj)">Remove</a><a class="edit" href="javascript:;"
            @click.stop.prevent="edit(proj)">Edit</a><a class="edit" href="javascript:;"
            @click.stop.prevent="edit(proj, true)">Copy</a><a class="edit" href="javascript:;"
            @click.stop.prevent="settop(proj)">Top</a><a href="javascript:;"
            @click.stop.prevent="settop(proj,true)">Rcmd</a>
        </div>
        <div class="admin" v-show="dispVar.isProdSales">
          <div class="views"><span v-show="proj.vcapp">APP:{{proj.vcapp}}</span><span v-show="proj.vcappc">&nbsp;
              C:{{proj.vcappc}}</span><span v-show="proj.vcweb">&nbsp; WEB:{{proj.vcweb}}</span><span
              v-show="proj.favR">&nbsp;
              FavR:{{proj.favR}}</span><span v-show="proj.favU">&nbsp; FavU:{{proj.favU}}</span></div>
          <span class="views" v-show="proj.vc">{{proj.vc}} {{getTranslate('views')}}</span>
        </div>
      </div>
    </div>
  </div>
  `
};