var propItem = {
  props:{
    prop:{
      type:Object,
      default:function () {
        return {
          pnSrc:[]
        }
      }
    },
    loading:{
      type:Boolean
    },
    lang:{
      type:String
    },
    tag:{
      type:String,
      default:''
    },
    cantClick:{
      type:Boolean,
      default:false
    }
  },
  mounted() {
    
  },
  computed:{
    showBlur:  function() {
      if((this.tag == 'sold' || this.tag == 'fast') && this.prop.login) return true;
      else return false;
    }
  },
  methods:{
    openDetail:function(prop) {
      if(this.cantClick){
        return
      }
      var id = (/^RM/.test(prop.id))?prop.id:prop._id;
      openPopup(`/1.5/prop/detail/inapp?id=${id}`,this.$_('RealMaster'));//lang=${this.lang}
    },
    getPropDate(prop) {
      if (prop.status_en == 'U') {
        return prop.sldd;
      } else {
        if (prop.mt) {
          return prop.mt.substr(0,10);
        } else {
          return '';
        }
      }
    },
    formatPrice(price) {
      if (typeof price == "number") {
        price = Math.ceil(price);
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    popupSrcList(){
      window.bus.$emit('popup-match-list',this.prop)
    },
    cntSrc(src){
      if(!src){
        return 0;
      }
      var cnt = 0;
      src.forEach(element => {
        cnt += element.v.length
      });
      return cnt
    },
    formateSrc(v){
      if(!v || typeof(v) == 'string'){
        return v;
      }
      var arr = []
      v.forEach(e => {
        if (e.nm){
          arr.push(e.nm)
        }else{
          arr.push(e)
        }
      });
      return arr.join(',')
    },
    showLpInDelisted(prop){
      if (prop.saleTpTag_en == 'Delisted'){
        return this.formatPrice(prop.lp || prop.lpr)
      } else {
        return this.formatPrice(prop.sp || prop.lp || prop.lpr)
      }
    }
  },
  template: `
    <div class="prop" :class="loading?'loading':''" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">
      <div class="detail">
        <div class="addr one-line" :class='{blur:showBlur}'>
          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>
        </div>
        <div class="prov">{{prop.city}}, {{prop.prov}}</div>
        <div class="bdrms" :class='{blur:showBlur}'>
          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?'+'+prop.br_plus:''}}</span>
          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>
          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>
        </div>
        <div v-if="showBlur" class='prov'>{{$_('Please login to see this listing!')}}</div>
        <div v-else>
          <div v-if="prop.lp == '$0'" class="price">{{$_('To Be Negotiated')}}</div>
          <div v-else-if="tag == 'loss'" class="price">
            <span class='black'>-{{formatPrice(prop.lsp - prop.sp)}}</span>
            <span v-if="prop.lspDifPct && (tag == 'loss')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>
          </div>
          <div v-else class="price">
            <span>{{showLpInDelisted(prop)}}</span>
            <span v-if="prop.pc && (prop.status == 'A') && prop.pcts" class="price-change">
              <span :class="prop.pc > 0?'plus':'mins'">{{prop.pc>0?'+':'-'}}</span>
              <span>{{formatPrice(Math.abs(prop.pc))}}</span>
            </span>
            <span v-if="prop.lstStr && prop.lst == 'Sc'" class="price-change">{{prop.lstStr}}</span>
            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != 'loss')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>
          </div>
        </div>
        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_('Open House')}}: {{prop.ohdate}}</div>
      </div>
      <div class="img" :class='{blur:showBlur}'>
        <img class="lazy" :src="prop.thumbUrl || '/img/no-photo.png'" onerror="this.src='/img/no-photo.png'"  />
        <div class="tag" :class="prop.tagColor">
          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>
          <span v-if="prop.ltp == 'exlisting'" class="ltp">{{$_('Exclusive')}}</span>
          <span v-if="prop.ltp == 'assignment'" class="ltp">{{$_('Assignment')}}</span>
          <span v-if="prop.ltp == 'rent'" class="ltp">{{$_('Rental')}}</span>
          <span v-if="prop.type" class="type">{{prop.type}}</span>
          <span>{{prop.saleTpTag || prop.lstStr}}</span>
        </div>
        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_('days')}}</div>
        <!--div v-else class="dom">{{getPropDate(prop)}}</div-->
      </div>
      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class='match' @click.stop='popupSrcList(prop)' data-sub="feeds pnsrc" :data-id="prop._id">
        <span class="fa fa-radar"></span>
        <span class="pnSrc" v-if="src = prop.pnSrc[0]">
        <span class='name'>{{src.transK}} </span>
          {{formateSrc(src.v)}}
        </span>
        <span class="count">{{cntSrc(prop.pnSrc)}}</span>
        <span class="fa fa-caret-down"></span>
      </div>
    </div>
  `
};