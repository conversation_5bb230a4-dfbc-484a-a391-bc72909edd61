var slider = {
  props: ['props'],
  beforeMount() {
    
  },
  data() {
    return {
      width:''
    }
  },
  mounted() {
    this.setRangeColor();
  },
  beforeUpdate() {
    var self = this;
    this.$nextTick(function () {
      self.setRangeColor();
    });
  },
  methods:{
    setRangeColor:function() {
      var {field,val} = this.props;
      var range = document.getElementById(field);
      var rangeWidth = range.getBoundingClientRect().width;
      var unit = rangeWidth/3;
      this.width = (val * unit) + 'px';
    },
    changeSlide:function(e) {
      var value = e.target.value;
      this.props.method(this.props.field,value);
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
  },
  template: `
    <div class="range-container">
      <div class="range-red" :style="{width:width}"></div>
      <input class="range" :id="props.field" type="range" step="1" max="3" min="0" :value="props.val" @input="changeSlide"/>
      <div class="range-values">
        <div class="range-value">{{getTranslate('any')}}</div>
        <div class="range-value second-in-four">{{getTranslate('low')}}</div>
        <div class="range-value third-in-four">{{getTranslate('medium')}}</div>
        <div class="range-value">{{getTranslate('high')}}</div>
      </div>
    </div>
  `
};