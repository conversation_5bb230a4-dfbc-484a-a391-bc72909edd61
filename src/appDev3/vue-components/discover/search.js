var Search = {
  props:['ispopup'],
  data() {
    return {
      schoolDisplay:{
        0:'low',
        1:'medium',
        2:'good',
        3:'top'
      },
      MAX_PRICE:0,
      showPopup:false,
      ptype2s:[],
      ethnics:{},
      search:{},
      showCityModal:false,
      key:0,
      userCity:{}
    }
  },
  components:{
    slider,
    slider2,
    CitySelectModal//: Vue.defineAsyncComponent(() => loadModule(CITY_SELECT_MODAL, options))
  },
  beforeMount() {
    var cmtySearch = localStorage.getItem('cmtySearch');
    try {
      cmtySearch = JSON.parse(cmtySearch);
    } catch (error) {
      console.log(error);
    }
    if (!cmtySearch || !Object.keys(cmtySearch).length) {
      this.setBasicSearch();
      this.setSearch2LocalStorage();
    } else {
      this.setCmtySearch(cmtySearch);
    }
  },
  mounted() {
    var self = this;
    self.userCity = decodeJsonString2Obj('userCity');
    self.ethnics = decodeJsonString2Obj('ethnics');
    self.ptype2s = decodeJsonString2Obj('ptype2s','array');
    self.MAX_PRICE = document.querySelector('#MAX_PRICE').innerHTML;
    bus.$on('set-city', function (d) {
      var city = d.city;
      var key = `${city.o}-${city.p_ab}`;
      if (!self.search.cities[key]) {
        self.search.cities[key] = city.n;
        self.setSearch2LocalStorage();
      }
    });
    bus.$on('city-select-close', function (d) {
      self.showCityModal = false;
    });
    bus.$on('toggle-filter', function (opts={}) {
      self.showPopup = opts.showFilter;
    });
  },
  methods:{
    getTranslate(key) {
      return TRANSLATES[key];
    },
    setBasicSearch() {
      var basicSearch = {
        ptype:'',
        cities:{},
        income:0,
        education:0,
        ethnicKey:'',
        ethnic:0,
        trnst:0,
        school:{min:0,max:3},
        price:{min:0,max:MAX_PRICE}
      };
      var key = `${this.userCity.o}-${this.userCity.p_ab}`;
      basicSearch.cities[key] = this.userCity.n;
      this.search = basicSearch;
    },
    reset() {
      this.setBasicSearch()
      this.setSearch2LocalStorage();
      this.key += 1;//rerender slide2 component
    },
    openSaves() {
      openPopup('/1.5/saves/communities?d=/1.5/community/map&isPopup=1','',true);
    },
    closePopup() {
      this.showPopup = false;
    },
    disabledFilter(field) {
      return field?'':'disabled';
    },
    activeFilter(field) {
      var val = '';
      if (this.search[field]) {
        // this.search[field] = '';
      } else {
        if ('ptype' == field) {
          val = 'House';
        } else if ('ethnicKey' == field) {
          val = 'cs16_1326';
        } else {
          val = 1;
        }
      }
      this.search[field] = val;
      this.setSearch2LocalStorage();
    },
    displayRange(rangeType) {
      if (rangeType == 'price') {
        // var {min,max} = this.search.price;
        var min = this.search.price.min;
        var max = this.search.price.max;
        if (min == max) {
          return this.formatPrice(max);
        } else {
          return `${this.formatPrice(min)} - ${this.formatPrice(max)}`
        }
      } else if (rangeType == 'school') {
        // var {min,max} = this.search.school;
        var min = this.search.school.min;
        var max = this.search.school.max;
        if (min == max) {
          return this.getTranslate(this.schoolDisplay[max]);
        } else {
          return this.getTranslate(this.schoolDisplay[min])+' - '+this.getTranslate(this.schoolDisplay[max]);
        }
      }
    },
    searchCmty() {
      if (this.ispopup) {
        bus.$emit('search-cmty');
        this.showPopup = false;
      } else {
        window.location.href="/1.5/community/map?mode=search&d=/1.5/community/filter";
      }
    },
    removeCity(k) {
      delete this.search.cities[k];
      this.setSearch2LocalStorage();
    },
    openCityModal:function() {
      this.showCityModal = true;
      bus.$emit('select-city', {noloading:true});
    },
    formatPrice: function(p) {
      if (p == this.MAX_PRICE) {
        return this.getTranslate('MAX');
      }
      return currencyFormat(p, '$', 0);
    },
    selectEthnic: function(ethnic) {
      if (this.search.ethnicKey == ethnic) {
        this.search.ethnicKey = '';
        this.search.ethnic = 0;
      } else {
        this.search.ethnicKey = ethnic;
      }
      this.setSearch2LocalStorage();
    },
    isPtype2Selected: function(ptype) {
      return (this.search.ptype === ptype)?'active':'';
    },
    selectPtype2: function(ptype) {
      if (this.search.ptype == ptype) {
        this.search.ptype = '';
      } else {
        this.search.ptype = ptype;
      }
      this.setSearch2LocalStorage();
    },
    setCmtySearch:function(cmtySearch) {
      this.search = cmtySearch;
    },
    setSearch2LocalStorage:function() {
      var cmtySearch = JSON.stringify(this.search);
      localStorage.setItem('cmtySearch',cmtySearch);
    },
    handleChange:function(field,val) {
      this.search[field] = parseInt(val);
      this.setSearch2LocalStorage();
    },
    handleChange2:function(field,obj) {
      // clearTimeout(this.debounceTimeout);
      // var self = this;
      this.search[field] = obj;
      this.setSearch2LocalStorage();
      // this.debounceTimeout = setTimeout(function () {
      //   self.debounceTimeout = null;
      // }, 700);
    }
  },
  template: `
    <div id="community" :class="ispopup?'modal ' + (showPopup?'active':''):''">
    <header id="header-bar" class="bar bar-nav" v-if="ispopup">
      <a class="icon fa fa-back pull-left" @click="closePopup()"></a>
      <h1 class="title">{{getTranslate('compass')}}</h1>
      <a class="icon header-bar-link" @click="openSaves()">{{getTranslate('saved')}}</a>
    </header>
    <div class="filter-container">
      <div class="filter" style="display:none">
        <h6 class="tl" @click="openCityModal()">
          <span class="name">{{getTranslate('city')}}</span>
          <span class="icon fa fa-angle-right"></span>
        </h6>
        <div class="options" :class="showCityModal?'active':''">
          <span v-for="(v,k) in search.cities" class="option active">
            <span>{{v}}</span>
            <span class="fa fa-times-circle" @click="removeCity(k)"></span>
          </span>
        </div>
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('type')}}</span>
        </h6>
        <div class="options">
          <span v-for="p in ptype2s" class="option" :class="isPtype2Selected(p.k)" @click="selectPtype2(p.k)">{{p.v}}</span>
        </div>
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('price')}}</span>
          <span class="price-range">{{displayRange('price')}}</span>
        </h6>
        <slider2 :key="'price'+key" v-bind:props="{field:'price',val:search.price,method:handleChange2,minVal:0,step:50000,maxVal:MAX_PRICE}" :type="'price'" />
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('schoolRank')}}</span>
          <span class="price-range">{{displayRange('school')}}</span>
        </h6>
        <slider2 :key="'school'+key" v-bind:props="{field:'school',val:search.school,method:handleChange2,minVal:0,maxVal:3}" />
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('railTransitRank')}}</span>
        </h6>
        <slider v-bind:props="{field:'trnst',val:search.trnst,method:handleChange}" />
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('ethnic')}}</span>
        </h6>
        <div class="options">
          <div v-for="(v,k) in ethnics" class="option" :class="search.ethnicKey == k?'active':''" @click="selectEthnic(k)">{{v}}</div>
        </div>
        <slider :class="disabledFilter(search.ethnicKey)" v-bind:props="{field:'ethnic',val:search.ethnic,method:handleChange}" />
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('educationLevel')}}</span>
        </h6>
        <slider v-bind:props="{field:'education',val:search.education,method:handleChange}" />
      </div>
      <div class="filter">
        <h6 class="tl">
          <span class="name">{{getTranslate('houseIncome')}} (HHI)</span>
        </h6>
        <slider v-bind:props="{field:'income',val:search.income,method:handleChange}" />
      </div>
    </div>
      <div class="search-btns">
        <span class="search-btn" @click="searchCmty()">{{getTranslate('search')}}</span>
        <span class="search-btn reset" @click="reset()" >{{getTranslate('reset')}}</span>
      </div>
      <city-select-modal />
    </div>
  `
};