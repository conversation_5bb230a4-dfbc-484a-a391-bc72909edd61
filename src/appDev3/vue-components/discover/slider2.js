var slider2 = {
  props: ['props','type'],
  data() {
    return {
      left:'',
      right:'',
      width:'',
      min:'',
      max:''
    }
  },
  beforeMount() {
    var {min,max} = this.props.val;
    min = parseInt(min);
    max = parseInt(max);
    if (min > max) {
      this.min = max;
      this.max = min;
    } else {
      this.min = min;
      this.max = max;
    }
    this.props.method(this.props.field,{min:this.min,max:this.max});
    if (!this.props.step) {
      this.props.step = 1;
    }
  },
  mounted() {
    this.setRangeColor();
  },
  beforeUpdate() {
    var self = this;
    this.$nextTick(function () {
      self.setRangeColor();
    });
  },
  methods:{
    setRangeColor:function() {
      var range = document.getElementById('range1');
      var rangeWidth = range.getBoundingClientRect().width;
      var unit = rangeWidth/this.props.maxVal;
      var {min,max} = this;
      this.left = (min * unit) + 'px';
      this.width = (Math.abs(max - min) * unit) +'px';
    },
    changeSlide:function(key,e) {
      var value = e.target.value;
      var {min,max} = this;
      if (key == 'min') {
        if (value > parseInt(max)) {
          this.min = max;
          this.max = value;
        } else {
          this.min = value;
        }
      }
      if (key == 'max') {
        if (value < parseInt(min)) {
          this.max = min;
          this.min = value;
        } else {
          this.max = value;
        }
      }
      this.props.method(this.props.field,{min:this.min,max:this.max});
      this.setRangeColor();
    },
    changeMin:function(e) {
      this.changeSlide('min',e);
    },
    changeMax:function(e) {
      this.changeSlide('max',e);
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
  },
  template: `
    <div class="range-container">
      <div class="range-red" :style="{left:left,width:width}"></div>
      <input class="range" id="range1" type="range" :step="props.step" :max="props.maxVal" :min="props.minVal" v-model="min" @input="changeMin" />
      <input class="range range2" id="range2" type="range" :step="props.step" :max="props.maxVal" :min="props.minVal" v-model="max" @input="changeMax" />
      <div class="range-values">
        <template v-if="type == 'price'">
          <div class="range-value">0</div>
          <div class="range-value second-in-five">600K</div>
          <div class="range-value third-in-five">1.5M</div>
          <div class="range-value forth-in-five">2.2M</div>
          <div class="range-value">{{getTranslate('MAX')}}</div>
        </template>
        <template v-else>
          <div class="range-value">{{getTranslate('low')}}</div>
          <div class="range-value second-in-four">{{getTranslate('medium')}}</div>
          <div class="range-value third-in-four">{{getTranslate('good')}}</div>
          <div class="range-value">{{getTranslate('top')}}</div>
        </template>
      </div>
    </div>
  `
};