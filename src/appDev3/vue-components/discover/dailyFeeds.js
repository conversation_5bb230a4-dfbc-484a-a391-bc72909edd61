var Caret = {
  props:['changeval'],
  data() {
    return {
      tp:null
    }
  },
  mounted() {
    
  },
  methods:{
    getIconClass:function() {
      if (typeof this.changeval != 'number') {
        return;
      }
      if (this.changeval > 0) {
        var tp = 'up';
      } else if (this.changeval < 0) {
        var tp = 'down';
      }
      return `fa-caret-${tp}`;
    }
  },
  template: `
  <span class="caret fa" :class="getIconClass()"></span>
  `
};

var keyFacts = {
  props: ['cmty'],
  beforeMount() {
    
  },
  mounted() {
  },
  components:{
    Caret
  },
  methods:{
    displayMomYoY(val) {
      if (typeof val != 'number') {
        return 'N/A';
      }
      return `${Math.abs(val.toFixed(2))}%`;
    },
    displayPrice() {
      if (this.cmty.avgP) {
        return `$${this.cmty.avgP}`;
      }
      return 'N/A';
    },
  },
  template: `
  <div class="row">
    <div class="data">
      <div class="val price">{{displayPrice()}}</div>
      <div class="nm">{{$_('Avg. Price')}}</div>
    </div>
    <div class="data">
      <Caret :changeval="cmty.mom"></Caret>
      <div class="val">{{displayMomYoY(cmty.mom)}}</div>
      <div class="nm">{{$_('MoM')}}</div>
    </div>
    <div class="data">
      <Caret :changeval="cmty.yoy"></Caret>
      <div class="val">{{displayMomYoY(cmty.yoy)}}</div>
      <div class="nm">{{$_('YoY')}}</div>
    </div>
  </div>
  `
};
var dailyFeeds={
  props: ['cmty','form'],
  mounted() {
  },
  components:{
    keyFacts
  },
  methods:{
    gotoStatPage() {
      var {city_en,pr_en,nm} = this.cmty;
      var statsUrl = `/1.5/prop/stats?city=${city_en}&prov=${pr_en}&cmty=${nm}&d=/1.5/landlord/owners&isPopup=1&itvl=M`;
      openContent(statsUrl,{toolbar:false});
    },
  },
  template:`
    <div class="cmty-stats">
      <div class="tl">{{cmty.nm}}</div>
      <div class="sub">{{$_('Based on your subscription')}}</div>
      <key-facts :cmty="cmty"></key-facts>
    </div>
    <div class="monthly-stats" data-sub="daily feeds go trends" @click="gotoStatPage()">
      <span>{{cmty.tsf}} - {{cmty.tst}}</span>
      <span>{{$_('Trends')}}<span class="icon icon-right-nav"></span></span>
    </div>
  `
};