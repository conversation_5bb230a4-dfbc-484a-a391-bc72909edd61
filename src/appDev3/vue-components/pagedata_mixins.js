var pageDataMixins = {
  created :  function () {
    // this.loadedJS = {};
  },
  data () {
    return {
      // noCacheList:['lang','locale','langCover','ownerData','shareLinks','noFnLn','popup','hasPn','appVer']
      cacheList:['propSortMethods','propPtypes','domFilterVals','bsmtFilterVals'],
      lastScrolledTop:0,
      scrollUp:false,
      // pageDataDebounce:null,
    }
  },
  methods : {
    isNewerVer(cur='5.3.0',req){
      if (cur=='appDebug') {
        return true;
      }
      cur = cur.split('.');
      req = req.split('.');
      if (parseInt(cur[0]) > parseInt(req[0])) {
        return true;
      }
      if (parseInt(cur[0]) == parseInt(req[0]) && parseInt(cur[1]) > parseInt(req[1])) {
        return true;
      }
      if (parseInt(cur[0]) == parseInt(req[0]) &&
          parseInt(cur[1]) == parseInt(req[1])  &&
          parseInt(cur[2]) >= parseInt(req[2])) {
        return true;
      }
      return false;
    },
    // only used for app now. if for web, need to change and specify the url
    processPostError(ret) {
      if (ret.needLogin) {
        if(RMSrv.closeAndRedirectRoot) {
          RMSrv.closeAndRedirectRoot('/1.5/user/login');
          return;
        } else {
          document.location.href='/1.5/user/login';
        }
      } else {
        RMSrv.dialogAlert(ret.e)
      }
    },
    loadJsSerial(jsArray, onload) {
      let js = null
      let self = this;
      let doOne = function(js) {
        if(js = jsArray.shift()) {
          self.loadJs(js.path, js.id, function(){
            doOne()
          });
        } else {
          onload()
        }
      }
      doOne();
    },
    loadJs (path, id, onload) {
      let self = this;
      if (self.hasLoadedJs(id)) {
        return;
      }
      if (!path || !id) {
        return;
      }
      var jsElm = document.createElement("script");
      jsElm.type = "application/javascript";
      jsElm.src = path;
      jsElm.id = id;
      if (onload) {
        jsElm.onload = onload
      }
      // self.loadedJS[id] = path;
      document.body.appendChild(jsElm);
    },
    loadCss (path, id) {
      if (!path || !id) {
        return;
      }
      var jsElm = document.createElement("link");
      jsElm.rel = "stylesheet";
      jsElm.type = "text/css";
      jsElm.href = path;
      jsElm.id = id;
      document.body.appendChild(jsElm);
    },
    loadJSString (str, id) {
      if (typeof str !== 'string') {
        return;
      }
      var jsElm = document.createElement("script");
      var jsElmText = document.createTextNode(str);
      jsElm.id = id;
      jsElm.appendChild(jsElmText);
      document.body.appendChild(jsElm);
    },
    setCookie(cname, cvalue, exdays) {
      var d = new Date();
      d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
      var expires = "expires="+d.toUTCString();
      document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
    },
    //julie,tested under chrome,won't get expired cookies
    readCookie (name) {
      var nameEQ = name + "=";
      var ca = document.cookie.split(';');
      for(var i=0;i < ca.length;i++) {
          var c = ca[i];
          while (c.charAt(0)==' ') c = c.substring(1,c.length);
          if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
      }
      return null;
    },
    //datas=['isLoggined']
    //datasObj={ownerId:'e13891uaiudjkaj'} supplement for datas
    getCachedDispVar(){
      if (!localStorage.dispVar) {
        return {};
      }
      try {
        return JSON.parse(localStorage.dispVar);
      } catch (e) {
        console.error(e);
        return {};
      }
    },
    saveCachedDispVar(dispVar){
      if (!dispVar) {
        return false;
      }
      var cached = this.getCachedDispVar();
      try {
        var ret = Object.assign(cached, dispVar);
        var tmp = {};
        // for (let item of this.noCacheList) {
        //   delete ret[item];
        // }
        for (let i in ret) {
          if (this.cacheList.indexOf(i)>-1) {
            tmp[i] = ret[i];
          }
        }
        localStorage.dispVar = JSON.stringify(tmp);
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    hasLoadedJs(id){
      return document.querySelector('script#'+id);
    },
    dynamicLoadJs(dispVar){
      var self = this,
          jsGmapUrl = 'jsGmapUrl',
          jsCordova = 'jsCordova',
          jsWechat = 'jsWechat',
          wxConfig = 'wxConfig';
      if (dispVar[jsGmapUrl] && !self.hasLoadedJs(jsGmapUrl)) {
        var gmapUrl = dispVar[jsGmapUrl]+(window.gMapsCallback?'&callback=gMapsCallback':'');
        self.loadJs(gmapUrl, jsGmapUrl);
      }
      if (dispVar[jsCordova] && !self.hasLoadedJs(jsCordova+'0')) {
        if (Array.isArray(dispVar[jsCordova])) {
          for (var i = 0; i < dispVar[jsCordova].length; i++) {
            var url = dispVar[jsCordova][i];
            var id = jsCordova+i;
            self.loadJs(url, id);
          }
        }
      }
      if (dispVar[jsWechat] && !self.hasLoadedJs(jsWechat)) {
        if (!Array.isArray(dispVar[jsCordova])) {
          return;
        }
        self.loadJs(dispVar[jsWechat][0], jsWechat);
        if (dispVar[wxConfig]) {
          var strWxConfig = JSON.stringify(dispVar[wxConfig]);
          self.loadJSString('var wxConfig = '+strWxConfig+';','wxConfig');
          setTimeout(function () {
            self.loadJs(dispVar[jsWechat][1], jsWechat+'1');
          }, 800);
        }
      }
      // if (ret.datas.jsGmapCluster) {
      // }
      //TODO: if wxcfg and wxjs
    },
    filterDatasToPost(cached, datas){
      if (Object.keys(cached).length) {
        var i = datas.length-1;
        while (i > -1) {
          var d = datas[i];
          if (cached.hasOwnProperty(d)) {
            datas.splice(i,1);
          }
          i--;
        }
      }
    },
    loadJsBeforeFilter(cached, datas=[]){
      var dispVar = {}, jss=['jsGmapUrl', 'jsWeixinUrl', 'jsWeixinRMsrv'];
      for (let js of jss) {
        if (datas.indexOf(js)>-1) {
          dispVar[js]=cached[js];
        }
      }
      this.dynamicLoadJs(dispVar);
    },
    emitSavedDataBeforeFilter(cached, datas){
      var dispVar = {}, bus = window.bus;
      for (let d of datas) {
        // ownerData will flash show previous user
        if (cached.hasOwnProperty(d) && this.cacheList.indexOf(d)>-1) {
          dispVar[d]=cached[d];
        }
      }
      bus.$emit('pagedata-retrieved', dispVar);
    },
    getPageData : function (datas, datasObj={}, forced=false) {
      var bus, self = this;
      if (!Array.isArray(datas)) {
        console.error('datas not array');
        return;
      }
      if (!(bus = window.bus)) {
        return console.error('global bus required!');
      }
      var cached = self.getCachedDispVar();//simple k,v object
      self.loadJsBeforeFilter(cached, datas);
      self.emitSavedDataBeforeFilter(cached, datas);
      if (!forced) {
        self.filterDatasToPost(cached, datas);
      }
      var dataObj = {datas:datas};
      // if (window.localStorage.translateCache) {
      //   var tlmt;
      //   try {
      //     tlmt = JSON.parse().tlmt;
      //   } catch (e) {
      //     tlmt = new Date();
      //   }
      //   dataObj.tlmt = tlmt;
      // }
      var postObj = Object.assign(dataObj, datasObj);
      fetchData('/1.5/pageData', {body:postObj},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          // if (ret.clearCache && window.localStorage) {
          //   delete window.localStorage.translateCache;
          // }
          self.dynamicLoadJs(ret.datas);
          self.saveCachedDispVar(ret.datas); //depend on datas
          bus.$emit('pagedata-retrieved',ret.datas);
        }
      })
      // clearTimeout(this.pageDataDebounce);
      // this.pageDataDebounce = setTimeout(function () {
      //   self.pageDataDebounce = null;
      // }, 200);
    },
    isForumFas(dispVar,city) {
      var isForumFas = false;
      if (dispVar.sessionUser) {
        var fas = dispVar.sessionUser.fas;
        var self = this;
        if(fas) {
          fas.forEach(function(el) {
            if (el.city && el.city == city.city || !el.city && el.prov == city.prov ) {
              isForumFas = true;
              return;
            }
          });
        }
      }
      return isForumFas;
    },
    getCachedForumCity(){
      if (!localStorage.forumCity) {
        return {};
      }
      try {
        return JSON.parse(localStorage.forumCity);
      } catch (e) {
        console.error(e);
        return {};
      }
    },
    saveCachedForumCity(forumCity){
      if (!forumCity) {
        return false;
      }
      try {
        localStorage.forumCity = JSON.stringify(forumCity);
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    checkScrollAndSendLogger(element){
      if (element.scrollTop > this.lastScrolledTop) {
        this.lastScrolledTop = element.scrollTop
      if(this.scrollUp){
        checkAndSendLogger(null,{sub:'scroll up',act:'scroll'});
        this.scrollUp = false;
      }
    }else{
      this.scrollUp = true;
    }
    }
  }
};
