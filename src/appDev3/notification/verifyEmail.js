var  emailChecker = emailMisspelled({ domains: top100 })
var timer = null,isSendingVCode = false,mode='';
function showModel(model){
  $(".backdrop").addClass('active');
  $("#"+model).addClass('active');
};
function closeModel(info){
  $(".flash-message-inner").html('');
  $(".backdrop").removeClass('active');
  $("#changeemail").removeClass('active');
  $("#verifyemail").removeClass('active');
  $("#verifyphone").removeClass('active');
  if(info){
    return window.iframRmCall(':ctx:'+JSON.stringify(info));
  }
  window.iframRmCall(':ctx::cancel');
};
function checkVcode(btnId,codeId,valId) {
  var vcode = $("#"+codeId).val();
  var email = $("#"+valId).val();
  if( vcode.length == 7 && (!email || (email.length > 0)) ){
    $("#"+btnId).removeClass('disable');
  } else{
    $("#"+btnId).addClass('disable');
  }
};
function clear(){
  $("div", ".possibleEmail").remove();
  $(".possibleEmail").hide();
  $(".emailError").hide();
};
var emailReg = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
function checkEmail() {
  var email = $("#newEmail").val();
  if(!email){
    return ;
  }
  clear();
  var possibleEmls = emailChecker(email)
  if(possibleEmls.length){
    possibleEmls.forEach((eml)=>{
      $(".possibleEmail").append(`<div onClick='changeCurEmail("${eml.corrected}")'>${eml.corrected}</div>`);
    })
    $(".possibleEmail").show();
  }
  if (emailReg.test(email)){
    $(".onErr").removeClass('disable');
  } else{
    $(".onErr").addClass('disable');
  }
};
function changeUserEmail(){
  var email = $("#newEmail").val();
  var vCode = $("#changeVCode").val();
  if (!emailReg.test(email) || vCode.length != 7){
    return ;
  }
  $.post(
  '/1.5/settings/changeSelfEmail', //新邮件替换并验证，
  {emlvcd:vCode,neml:email.toLowerCase()},
  function(data){
    if (data.e){
      if (data.e == 'Need Login') {
        document.location.href="/1.5/user/login"
      }else{
        flashMessage(data.e);
      }
    }else{
      flashMessage(data.msg,data.ok)
      setTimeout(function(){
        closeModel({emlV:true,eml:email});
      },500);
    }
  }
  )
};
function sendCode(neml){
  var post = {};
  if (isSendingVCode == true){
    return;
  }
  isSendingVCode = true
  if(neml){
    var email = $("#newEmail").val();
    if (!emailReg.test(email)){
      return $(".emailError").show();
    }
    post.neml = email.toLowerCase()
  }
  $("#"+mode+" #loader").show();
  $.post(
    '/verifyEmail/emailVCode',
    post,
    function(data){
      isSendingVCode = false;
      $("#"+mode+" #loader").hide();
      if (data.err){
        if (data.err == 'Need Login') {
          document.location.href="/1.5/user/login"
        }else{
          flashMessage(data.err);
          $(".emailError").show()
        }
      }else{
        flashMessage(data.ret);
        timeCounter();
      }
    }
  )
};
function timeCounter(){
  var time = 120;
  $("#"+mode+" .onErr").hide();
  $("#"+mode+" .count").show();
  timer = setInterval(function(){
    if (time > 0){
      time --;
      $(".second").html(time);
    }else{
      clearInterval(timer);
      $("#"+mode+" .count").hide();
      $("#"+mode+" .resend").show();
    }
  },1000);
};
function verifyEmailVCode(){
  var vcode = $("#vcode").val();
  if (vcode.length != 7){
    return ;
  }
  $.post(
  '/verifyEmail/verifyEmailVCode',
  {emlvcd:vcode},
  function(data){
    if (data.err){
      if (data.err == 'Need Login') {
        document.location.href="/1.5/user/login"
      }else{
        flashMessage(data.err);
      }
    }else{
      flashMessage(data.msg,data.ok)
      setTimeout(function(){
        closeModel({emlV:true});
      },500);
    }
  }
  )
};
function flashMessage(msg,ok){
  $(".flash-message-inner").html(msg);
  $(".flash-message-box").show();
  setTimeout(function(){
    $(".flash-message-box").hide();
  },2000);
};
var phoneReg = /^\(?(\d{3})\)?[-\s]?(\d{3})[-\s]?(\d{4})$/;
function checkPhone(){
  var phone = $("#mbl2v").val();
  if(!phone){
    return;
  }
  if (phoneReg.test(phone)){
    $(".phoneError").hide()
    $(".onErr").removeClass('disable');
  } else{
    $(".phoneError").show()
    $(".onErr").addClass('disable');
  }
};
function verifyPhoneVCode(){
  mbl2v = $("#mbl2v").val();
  mblvcd = $("#mblvcd").val();
  if (!phoneReg.test(mbl2v) || mblvcd.length != 7){
    return $(".phoneError").show()
  }
  $.post(
  '/1.5/user/verify',
  {mbl2v,mblvcd,tp:'visitor'},
  function(data){
    if (data.err){
      if (data.err == 'Need Login') {
        document.location.href="/1.5/user/login"
      }else{
        flashMessage(data.err);
      }
    }else if(data.success){
      flashMessage(data.msg,data.ok)
      setTimeout(function(){
        closeModel({mblV:true,mbl:mbl2v});
      },500);
    }else{
      flashMessage(data.message,data.ok)
    }
  }
  )
};
function sendSMSCode(){
  var post = {}
  post['mbl2v'] = $("#mbl2v").val();
  post.tp = 'sms';
  if (!phoneReg.test(post.mbl2v)){
    return $(".phoneError").show()
  }
  $("#"+mode+" #loader").show();
  $.post(
    '/1.5/user/verifyCell',
    post,
    function(data){
      isSendingVCode = false;
      $("#"+mode+" #loader").hide();
      if (!data.success){
        $(".phoneError").show()
      }else{
        timeCounter();
      }
      flashMessage(data.message);
    }
  )
};
function confirmDeletePhone(mbl) {
  if(!mbl){return;}
  var tip = $('#confirmDelete').text();
  var cancel = $('#cancel').text();
  var confirm = $('#confirm').text();
  return window.confirm(tip, deletePhoneNumber, '', [cancel, confirm]);
};
function deletePhoneNumber(idx) {
  if (idx+'' != '2') {
     return;
  }
  $.post(
    '/1.5/user/deletePhone',
    {},
    function(data){
      if (data.e){
        if (data.e == 'In use') {
          return window.alert($('#inUse').text(),$('#ok').text());
        }else{
         flashMessage(data.e);
        }
      }else{
        flashMessage(data.msg,data.ok)
        setTimeout(function(){
          closeModel({delete:'phone'});
        },500);
      }
    }
  )
};

window.onload = function(){
  checkPhone();
  checkEmail();
  mode = $('#mode').text().toLowerCase()
  showModel(mode);
};
$(document).ready(function() {
  window.confirm = function(message, callback,title,btns) {
    var confirmBox = $('<div class="confirm-box">' + message + '</div>');
    var FstButton = $('<div>'+ btns[0] +'</div>');
    var SecButton = $('<div>'+ btns[1] +'</div>');
    var btns =  $('<div class="btn-box"></div>');
    btns.append(FstButton);
    btns.append(SecButton);
    confirmBox.append(btns);
    $('body').append(confirmBox);

    FstButton.click(function() {
      confirmBox.remove();
      callback(1);
    });

    SecButton.click(function() {
      confirmBox.remove();
      callback(2);
    });
  }
  window.alert = function(message,ok) {
    var confirmBox = $('<div class="confirm-box">' + message + '</div>');
    var FstButton = $('<div>'+ ok || 'OK' +'</div>');
    var btns =  $('<div class="btn-box"></div>');
    btns.append(FstButton);
    confirmBox.append(btns);
    $('body').append(confirmBox);

    FstButton.click(function() {
      confirmBox.remove();
    });
  }
});
