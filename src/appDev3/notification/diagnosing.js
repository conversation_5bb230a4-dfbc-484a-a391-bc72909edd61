var result = {},errProblem='';
  /* todo l10n;
  NOTE dot engine has bug
  one line comment does not work
  */
  var pnWaitMessage = 'Push message test';
  var rmpntestTimer = null;
  function onErrClick (mode,url){
    if(mode == 'notification'){
      trackEventOnGoogle('diagnosing','setNotification');
      return RMSrv.openSettings();
    }else if(mode == 'language'){
      trackEventOnGoogle('diagnosing','setLanguage');
      return window.location = url;
    }else if(mode == 'message'){
      var ver=result.ua.match(/cpu iphone os (.*?) like mac os/);
      if(ver){
        result.iosVersion = ver[1].replace(/_/g,".");
      }
      result.errorProblem = 'Notification push notify message test error. user cannot receive pn message.';
      trackEventOnGoogle('diagnosing','sendEmail');
      $.post(
      '/1.5/user/sendErrReport',result,function(data){
        if(data.err == 'Need Login') {
          document.location.href = '/1.5/user/login'
        }
        $("#diagnosingModel .flash-message-box").show();
        setTimeout(function(){
          $("#diagnosingModel .flash-message-box").hide();
          closeModel('showDiagnosing');
        },1000);
      }
      )
    }
  }

  function checkNotify(){
    RMSrv.permissions('notification','check',-1,function(data){
      result.notification=data;
      if(data == 'granted'){
        changeHTML(1,true);
        $("#diagnoseOptionmessage").show();
      }else{
        changeHTML(1,false);
        $("#diagnoseOptionmessage").hide();
      }
    });
  }

  function checkLanguage(){
    $.post(
    '/1.5/user/language',
    {},
    function(data){
      if(data.ok == 1) {
        if (data.lang) {
          changeHTML(2,true,data.lang);
        } else {
          changeHTML(2,false);
        }
      }else if(data.ok == 0){
        changeHTML(2,false);
      }
    });
  }
  /* pushnotify 前台收到会有一个event，rmsrv里的event会调用pnFunction;
    如果一直没有收到30s后调用pnFunction 设为fail
  */
  function checkPn() {
    /*if do not have pn permission, don't do test*/
    RMSrv.permissions('notification','check',-1,function(data) {
      if(data == 'granted') {
        RMSrv.action('pushToken');
        // RMSrv.setApplicationIconBadgeNumber(0) if ios
        setTimeout(function() {
          $.post(
          '/sys/pntest',
          {url:':rmpntest',m:pnWaitMessage, pn:localStorage.pn},
          function(data){
            result.pn=data.pn;
            result.ts=data.ts;
            rmpntestTimer = setTimeout(function(){
              pnFunction({ok:0})
            },30*1000);
          });
        },1000)
      }
      else {
        changeHTML(3,false);
      }
    })
  }

  function pnFunction(data){
    clearTimeout(rmpntestTimer);
    if(data.ok==1){
      changeHTML(3,true);
    }else{
      changeHTML(3,false);
    }
  }

  function checkAll (){
    $(".row").eq(0).siblings().show();
    checkNotify();
    checkLanguage();
    checkPn();
  }

  function changeHTML (index,success,text){
    $("#diagnosingModel .row").eq(index).find('img').hide();
    if (success){
      $("#diagnosingModel .row").eq(index).find('.fa-check-circle').show();
      $("#diagnosingModel .row").eq(index).find('.success').show();
      if(text == 'hide'){
        $("#diagnosingModel .row").eq(index).find('.success').html('');
      }else if(text){
        $("#diagnosingModel .row").eq(index).find('.success').html(text);
      }
    }else{
      $("#diagnosingModel .row").eq(index).find('.fa-exclamation-circle').show();
      $("#diagnosingModel .row").eq(index).find('.failure').show();
      $("#diagnosingModel .row").eq(index).find('.onErr').show();
    }
  }
  function resetHtml(){
    $("#diagnosingModel .row").find('img').show();
      $("#diagnosingModel .row").find('.fa-check-circle').hide();
      $("#diagnosingModel .row").find('.success').hide();
      $("#diagnosingModel .row").find('.fa-exclamation-circle').hide();
      $("#diagnosingModel .row").find('.failure').hide();
      $("#diagnosingModel .row").find('.onErr').hide();
  }
  function closeModel(model){
    var appElement = document.querySelector('[ng-controller=ctrlNotifications]');
    var $scope = angular.element(appElement).scope();
    if ($scope){
      $scope[model] = false;
      $scope.$apply();
    }
  }
  function checkHasGooglePlay(){
    resetHtml();
    $(".row").eq(0).siblings().hide();
    result.ua = navigator.userAgent.toLowerCase();
    if (/(iPhone|iPad|iPod|iOS)/i.test(result.ua) ){
      changeHTML(0,true,'hide');
      checkAll();
    }else{
      RMSrv.hasGoogleService(function (ret) {
        if(ret == true){ 
          changeHTML(0,true);
          checkAll();
        }else{
          changeHTML(0,false);
          $('.modal-40pc').addClass('min')
        }
      });
    }
  }