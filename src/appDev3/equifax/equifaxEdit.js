var consentAgremnt = false, acurtInfo = false, tokenInfo = false,editRecordId = null;
var editInfo = {}, consumerInfo = {}, initInfo={};
var InfoField = ['lastName', 'firstName', 'socialInsuranceNumber', 'dateOfBirth', 'occupation']
var AddressField = ['unit', 'civicNumber', 'streetName', 'city', 'provinceCode', 'postalCode']
function getInitbaseInfo(){
  initInfo = getInputText();
}
$(document).ready(function() {
  $("img").error(function () {
    $(this).attr("src", "/img/no-photo.png");
  });
  $.validator.addMethod("alphanumericSpace", function(value,element,params){
    if(/^[a-zA-Z0-9 ']+$/.test(value)){
      return true;
    }
    return false;
  });
  $.validator.addMethod("specialChar", function(value,element,params){
    if(/^[a-zA-Z0-9- ]+$/.test(value)){
      return true;
    }
    return false;
  });
  $.validator.addMethod("capitalNum", function(value,element,params){
    if(/^[A-Z0-9a-z]+$/.test(value)){
      return true;
    }
    return false;
  });
  $.validator.addMethod("civicType", function(value,element,params){
    if (!value || !value.length){
      return true;
    }
    if(/^[A-Z0-9a-z /]+$/.test(value)){
      return true;
    }
    return false;
  });
  $.validator.addMethod("streetNameType", function(value,element,params){
    if(/^[A-Z0-9a-z /#-'.,]+$/.test(value)){
      return true;
    }
    return false;
  });
  $("#editBaseInfo").validate({
    rules: {
      lastName: {
        required: true,
        minlength: 2,
      },
      firstName: {
        required: true,
        minlength: 2,
      },
      dateOfBirth:{
        required: true,
      },
      civicNumber:{
        required: false,
        maxlength: 10,
        civicType: true,
      },
      streetName:{
        required: true,
        minlength: 2,
        maxlength: 25,
        streetNameType: true
      },
      city:{
        required: true,
        maxlength: 20,
        specialChar: true
      },
      provinceCode:{
        required: true,
        alphanumericSpace: true
      },
      postalCode:{
        required: true,
        capitalNum: true,
        rangelength: [6, 6]
      }
    },
    messages: {
      lastName: {
        required: "Please enter last name",
        minlength: "Please enter at least 2 characters",
      },
      firstName: {
        required: "Please enter first name",
        minlength: "Please enter at least 2 characters",
      },
      dateOfBirth: {
        required: "Please enter birthday"
      },
      civicNumber:{
        maxlength: "The Maximum length is 10 characters",
        civicType: "Alphanumeric whitespace and slash are allowed"
      },
      streetName:{
        required: "Please enter street name",
        minlength: "Please enter at least 2 characters",
        maxlength: "The Maximum length is 25 characters",
        streetNameType: "Alphanumeric, whitespace, slash, # and - are allowed"
      },
      city:{
        required: "Please enter city",
        maxlength: "The Maximum length is 20 characters",
        specialChar: "Alphanumeric, whitespace and - are allowed"
      },
      provinceCode:{
        required: "Please enter province",
        alphanumericSpace: "Cannot contain special characters"
      },
      postalCode:{
        required: "Please enter postal code",
        capitalNum: "Alphanumeric only",
        rangelength: "The length of the input must be equal to 6"
      }
    }
  });
  getInitbaseInfo();
  $('#dateOfBirth').change(function(){
    $("#editBaseInfo").valid();
  });
});
// 获取用户输入的信息
function getInputText(){
  let info = {};
  InfoField.forEach((elem) => {
    let value = $('input[name="'+elem+'"]').val();
    info[elem] = value || '';
  });
  AddressField.forEach((elem) => {
    let value = $('input[name="'+elem+'"]').val();
    info[elem] = value || '';
  });
  let memo = $('textarea[name="memo"]').val();
  info.memo = memo || '';
  let imgSrc = $("#poaImg").attr("alt");
  info.pic = imgSrc;
  return info;
}
function showFlashMessage(text){
  $(".flash-message-inner").html(text);
  $('.flash-message-box').show();
  setTimeout(()=>{
    $('.flash-message-box').hide();
  },2000)
}
function changeStepLineActive(){
  $('#allContent').animate({scrollTop: 0}, 0);
  $("#stp1Hr").toggleClass("active");
  $("#stp2Hr").toggleClass("active");
}
// 信息编辑页面点击下一步
$("#nextBtn").click(function() {
  if(!$("#editBaseInfo").valid()){
    return
  }
  let imgSrc = $("#poaImg").attr("alt");
  if(!imgSrc.length){
    return showFlashMessage('Please upload tenant authorization form');
  }
  editInfo.pic = imgSrc;
  InfoField.forEach((elem) => {
    let value = $('input[name="'+elem+'"]').val();
    $('#'+elem+'Text').text(value);
    editInfo[elem] = value;
    consumerInfo[elem] = value;
  });
  let memo = $('textarea[name="memo"]').val();
  if (memo.length){
    editInfo.memo = memo;
  }
  let address = {};
  let zip = $('input[name=postalCode]').val();
  let zipToUpCase = zip.toLocaleUpperCase();
  $('input[name=postalCode]').val(zipToUpCase);
  AddressField.forEach((elem) => {
    let value = $('input[name="'+elem+'"]').val();
    $('#'+elem+'Text').text(value);
    address[elem] = value;
    editInfo[elem] = value;
  });
  consumerInfo.address = address;
  changeStepLineActive();
  $('#memoText').text(memo);
  $("#stp1Title").hide();
  $("#stp2Title").show();
  $("#curentStp").text("2");
  $("#stp1Content").hide();
  $("#stp2Content").show();
});
function goBackEditPage(){
  changeStepLineActive();
  $("#stp1Title").show();
  $("#stp2Title").hide();
  $("#curentStp").text("1");
  $("#stp1Content").show();
  $("#stp2Content").hide();
}
// 信息编辑页面点击上一步
$("#btnBack").click(function() {
  goBackEditPage();
});
// 点击地址输入input
$("#address").click(function() {
  let titleText = $('#selAddrTitle').text();
  let cfg = {hide:false,title: titleText}
  let url = "/1.5/map/searchLocation?1=1"
  url = RMSrv.appendDomain(url)
  let addrField = {
    st_num: 'civicNumber',
    st: 'streetName',
    city: 'city',
    prov: 'provinceCode',
    zip: 'postalCode'
  }
  RMSrv.getPageContent(url, '#callBackString', cfg, (val)=>{
    if(val == ':cancel') return
    try {
      let ret = JSON.parse(val);
      for(let key in addrField){
        $('input[name="'+addrField[key]+'"]').val('');
      }
      $('input[name=unit]').val('');
      if(ret.address){
        $('input[name="address"]').val(ret.address);
      }
      for(let key in addrField){
        if(ret[key]){
          $('input[name="'+addrField[key]+'"]').val(ret[key]);
        }
        if(key == 'zip'){
          ret[key] = ret[key].replace(/ /,'');
          $('input[name="'+addrField[key]+'"]').val(ret[key]);
        }
      }
      if(!(ret.st_num && ret.st) && ret.addr){
        let matchVal = ret.addr.match(/^\S+/);
        if(matchVal){
          let strNum = matchVal[0];
          if(/\d+/.test(strNum)){
            $('input[name=civicNumber]').val(strNum);
            streetName = ret.addr.slice(strNum.length);
            $('input[name=streetName]').val(streetName);
          } else {
            $('input[name=streetName]').val(ret.addr);
          }
        }
      }
      $("#editBaseInfo").valid();
    }catch(e){
      console.log(e)
    }
  })
})
// 编辑信息点击选择图片
$("#selectPicModel").click(function() {
  let opt = {
    url: '/1.5/img/insert'
  }
  insertImage(opt,(val)=>{
    if (val == ':cancel') {
      return;
    }
    try {
      let ret = JSON.parse(val);
      let picUrls = ret.picUrls;
      if(picUrls && picUrls.length){
        $(".new-img").hide();
        $("#poaImg").show();
        $("#poaImg").attr("src", picUrls[0]);
        $("#poaImg").attr("alt", picUrls[0]);
      }
    } catch (e) {
    }
  })
});
// 编辑信息点击图片查看大图
$("#poaImg").click(function(){
  let imgSrc = $("#poaImg").attr("alt");
  $(".imageDisplay").show();
  $("#bigImg").attr("src", imgSrc);
});
// 点击取消大图显示
$(".imageDisplay").click(function(){
  $(".deleteSpan").hide();
  $(".trashIcon").show();
  $('.imageDisplay').hide();
});
// 大图点击删除按钮
$(".trashIcon").click(function(event){
  event.stopPropagation();
  $(this).hide();
  $(".deleteSpan").show();
});
// 大图点击取消删除按钮
$(".cancelImgBtn").click(function(event){
  event.stopPropagation();
  $(".deleteSpan").hide();
  $(".trashIcon").show();
})
// 大图点击确认删除按钮
$(".deleteImgBtn").click(function(event){
  event.stopPropagation();
  $("#bigImg").attr("src", '');
  $("#poaImg").attr("src", '');
  $("#poaImg").attr("alt", '');
  $(".new-img").show();
  $("#poaImg").hide();
  $(".imageDisplay").hide();
  $(".deleteSpan").hide();
  $(".trashIcon").show();
})
// 点击确认Consent and Agreement
$("#agrmntThin").click(function() {
  $("#agrmntThin").hide();
  $("#agrmntCircle").show();
  consentAgremnt = true;
});
// 点击取消确认Consent and Agreement
$("#agrmntCircle").click(function() {
  $("#agrmntCircle").hide();
  $("#agrmntThin").show();
  consentAgremnt = false;
});
// 点击确认Accurate Information
$("#acurtThin").click(function() {
  $("#acurtThin").hide();
  $("#acurtCircle").show();
  acurtInfo = true;
});
// 点击取消确认Accurate Information
$("#acurtCircle").click(function() {
  $("#acurtCircle").hide();
  $("#acurtThin").show();
  acurtInfo = false;
});
// 点击确认Payment
$("#tokenThin").click(function() {
  $("#tokenThin").hide();
  $("#tokenCircle").show();
  tokenInfo = true;
});
// 点击取消确认Payment
$("#tokenCircle").click(function() {
  $("#tokenCircle").hide();
  $("#tokenThin").show();
  tokenInfo = false;
});
// 跳转报告页
function gotoReportPage(id){
  let screenWidth = $(window).width();
  let url = '/equifax/report?recordId=' + id;
  if(screenWidth < 500){
    url += `&isApp=true&lineLength=${screenWidth-30}`;
    window.location = url;
  } else {
    window.open(url, '_blank');
    window.location = '/equifax/history';
  }
}
// 退出编辑
$("#exitEdit").click(function() {
  let currInfo = getInputText();
  let showSaveModel = false;
  for(let k in currInfo){
    if(currInfo[k] !== initInfo[k]){
      showSaveModel = true;
      break;
    }
  }
  if(showSaveModel){
    $("#backAlert").show();
  } else {
    window.location = '/equifax/history';
  }
});
$("#editBackDrop").click(function() {
  $("#backAlert").hide();
});
$("#cancelEditBtn").click(function() {
  window.location = '/equifax/history';
});
// 发送生成报告请求
function pullCreditReport(){
  setLoaderVisibility('block');
  let editRecordId = $("[data-role=module-args]#recordId").text();
  let body = Object.assign({}, editInfo, {consumer: consumerInfo});
  if(editRecordId.length){
    body.recordId = editRecordId;
  }
  $("#btnBack").attr("disabled", true);
  $("#btnPull").attr("disabled", true);
  fetchData('/equifax/report',{body},(err,ret)=>{
    setLoaderVisibility('none');
    initInfo = getInputText();
    if (err || !ret.ok) {
      let msg = err? 'Error': ret.e;
      $("#btnPull").attr("disabled", false);
      $("#btnBack").attr("disabled", false);
      if(/contact the administrator/.test(msg)){
        $("#btnPull").attr("disabled", true);
        $("#nextBtn").attr("disabled", true);
        $("#saveDraftBtn").attr("disabled", true);
      }
      $('#message').text(msg);
      $('.errorMessage').show();
      goBackEditPage();
      return
    }
    gotoReportPage(ret.recordId);
  });
}
function confirmTokenTakeOut(){
  let tkCreditRpt = $("[data-role=module-args]#tkCreditRpt").text();
  let tokenOutTip = `You will be charged ${tkCreditRpt} credit report tokens for this report.`;
  let cancel = 'Cancel';
  let yes = 'Confirm';
  function _pullCreditReport(idx) {
    if (idx+'' == '2') {
      pullCreditReport();
    }
  }
  return RMSrv.dialogConfirm(tokenOutTip, _pullCreditReport, '', [cancel, yes]);
}
$("#btnPull").click(function() {
  if(!$("#editBaseInfo").valid()){
    return
  }
  if(!consentAgremnt){
    return showFlashMessage('Please check consent and agreement');
  }
  if(!acurtInfo){
    return showFlashMessage('Please check accurate information');
  }
  if(!tokenInfo){
    return showFlashMessage('Please check Payment');
  }
  let screenWidth = $(window).width();
  if(screenWidth >= 500){
    $('html, body').animate({scrollTop: 0}, 0);
  }
  confirmTokenTakeOut();
});
// 编辑页面点击保存草稿
$("#saveDraftBtn, #saveExitBtn").click(function() {
  let editRecordId = $("[data-role=module-args]#recordId").text();
  let body = {status: 'Draft'};
  let canSave = false;
  if(editRecordId.length){
    body.recordId = editRecordId;
    canSave = true;
  }
  let inputInfo = getInputText();
  body = Object.assign({},body,inputInfo);
  for(let k in inputInfo){
    if(inputInfo[k].length){
      canSave = true;
      break;
    }
  }
  if(!canSave){
    return showFlashMessage('Please enter something before saving.');
  }
  setLoaderVisibility('block');
  fetchData('/equifax/update',{body},(err,ret)=>{
    setLoaderVisibility('none');
    if (err || !ret.ok) {
      let msg = err? 'Error': ret.e;
      return showFlashMessage(msg);
    }
    showFlashMessage(ret.msg);
    window.location = '/equifax/history';
  });
});