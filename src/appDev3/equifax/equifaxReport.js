// app报告点击时间切换显示内容
$(".timeTag").click(function(){
  const SCROLL_OFFSET = 135;
  const STICKY_ADDITIONAL_OFFSET = 40;
  $(this).addClass('active').siblings().removeClass('active');
  let dateText = $(this).text();
  let dealDateText = dateText.replace(/\(\d+\)/g,"").trim();
  let $parentElm = $(this).parent().parent();
  let $elm = $(this).parent();
  let offset = SCROLL_OFFSET;
  let eleClasses = $($elm['0']).attr('class');
  if(/posStickSquire/.test(eleClasses)){
    offset += STICKY_ADDITIONAL_OFFSET;
  }
  $($parentElm['0']).find('.appContent').each(function(){
    let classes = $(this).attr('class');
    let regexp = new RegExp(dealDateText);
    if(regexp.test(classes)){
      $(this).show();
      let offsetTop = $(this).offset().top;
      $('html, body').animate({scrollTop: offsetTop - offset}, 500);
    }else{
      $(this).hide();
    }
  })
})
$(".squareDiv").click(function(){
  let $parentElm =  $(this).parent();
  let $parentSibling = $($parentElm[0]).siblings();
  let $siblings = $(this).siblings();
  $siblings.attr('class', 'displayDiv haveSeenTitle padding15');
  $siblings.find('.cntCircle').css("background-color", "#555");
  if ($parentSibling.is(':hidden')) {
    $(this).find('span').attr('class', 'fa fa-rmminus');
    $parentSibling.show();
  } else {
    $(this).find('span').attr('class', 'icon icon-plus');
    $parentSibling.hide();
  }
})
function toggleAllInfo(shouldFold = false){
  let $squareDivElms = $(".squareDiv");
  if (!$squareDivElms || !$squareDivElms.length) return;
  $squareDivElms.each(function(){
    let $parentElm =  $(this).parent();
    let $sibling = $($parentElm[0]).siblings();
    if(shouldFold){
      $(this).find('span').attr('class', 'fa fa-rmminus');
      $sibling.show();
    } else {
      $(this).find('span').attr('class', 'icon icon-plus');
      $sibling.hide();
    }
  })
}
function openPrint(){
  $('.score-bar-container div:first').addClass('printBar');
  $('.printBtn').hide();
  window.print();
}
function afterPrint() {
  $('.score-bar-container div:first').removeClass('printBar');
  $('.printBtn').show();
}
// 在用户关闭打印预览后，会触发afterprint事件
window.addEventListener("afterprint", afterPrint);
$(document).ready(function() {
  toggleAllInfo();
});