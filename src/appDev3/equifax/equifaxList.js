var consentAgremnt = false, acurtInfo = false, tokenInfo = false,hisRecordId = null,editRecordId = null;

var InfoField = ['lastName', 'firstName', 'socialInsuranceNumber', 'dateOfBirth', 'occupation']

var AddressField = ['unit', 'civicNumber', 'streetName', 'city', 'provinceCode', 'postalCode']

function showFlashMessage(text){
  $(".flash-message-inner").html(text);
  $('.flash-message-box').show();
  setTimeout(()=>{
    $('.flash-message-box').hide();
  },2000)
}
// 历史记录memo编辑模块关闭
function closeMemoEditModel(){
  hisRecordId = null;
  $('textarea[name="memoEdit"]').val('');
  $("#memoModel").hide();
}
// 历史记录memo编辑取消
$("#cancelMemoBtn,#memoModel .backdrop").click(function(){
  closeMemoEditModel();
})
// 历史记录memo编辑保存
$("#saveMemoBtn").click(function(){
  if(!hisRecordId){
    showFlashMessage('Record Id Not Found,Please cancel and try again!');
    return
  }
  memoValue = $('textarea[name="memoEdit"]').val();
  let body = {
    recordId: hisRecordId,
    memo: memoValue
  }
  setLoaderVisibility('block');
  fetchData('/equifax/update',{body},(err,ret)=>{
    setLoaderVisibility('none');
    if (err || !ret.ok) {
      let msg = err? 'Error': ret.e;
      return showFlashMessage(msg);
    }
    showFlashMessage(ret.msg);
    $(`input[name=${hisRecordId}]`).val(memoValue);
    closeMemoEditModel();
  });
})
// 历史记录页面点击memo
$(".historyMemo").click(function(event){
  event.stopPropagation();
  let value = $(this).val();
  hisRecordId = $(this).attr('name');
  $('textarea[name="memoEdit"]').val(value);
  $("#memoModel").show();
})
function deleteHistory(_this){
  let classes = _this.attr('class');
  let id = classes.replace(/confirmDetele/, '').trim();
  setLoaderVisibility('block');
  fetchData('/equifax/delete',{body:{recordId: id}},(err,ret)=>{
    setLoaderVisibility('none');
    if (err || !ret.ok) {
      let msg = err? 'Error': ret.e;
      return showFlashMessage(msg);
    }
    showFlashMessage(ret.msg);
    let $parentElm = _this.parent().parent().parent();
    let childLeng = $($parentElm['0']).find('.historyCard').length;
    if(childLeng == 1){
      $parentElm.remove();
    } else {
      _this.parent().parent().remove();
    }
  });
}
function confirmDeleteHistory(_this){
  let deleteTip = 'Are you sure you want to delete it?';
  let cancel = 'Cancel';
  let yes = 'Yes';
  function _pullCreditReport(idx) {
    if (idx+'' == '2') {
      deleteHistory(_this);
    }
  }
  return RMSrv.dialogConfirm(deleteTip, _pullCreditReport, '', [cancel, yes]);
}
// 删除历史记录
$(".deleteMemo").click(function(event){
  event.stopPropagation();
  hisRecordId = $(this).attr('id');
  $parent = $(this).parent();
  $($parent[0]).find(`div.${hisRecordId}`).show();
  $(this).hide();
})
$(".cancelBtn").click(function(event){
  event.stopPropagation();
  $(".confirmDetele").hide();
  $(".deleteMemo").show();
})
$(".deleteBtn").click(function(event){
  event.stopPropagation();
  let _this = $(this).parent().parent();
  confirmDeleteHistory(_this);
})
// 跳转报告页
function gotoReportPage(id){
  let url = '/equifax/report?recordId=' + id;
  let screenWidth = $(window).width();
  if(screenWidth < 500){
    url += `&isApp=true&lineLength=${screenWidth - 30}`
    window.location = url;
  } else {
    window.open(url, '_blank');
  }
}
// 点击历史记录卡片跳转编辑/查看页面
$(".historyCard").click(function(){
  let classes = $(this).find('span.resultTag').attr('class');
  let id = $(this).find('input.historyMemo').attr('name');
  if(/successTag/.test(classes)){
    gotoReportPage(id);
  } else if(/faildedTag/.test(classes) || /draftTag/.test(classes)){
    let url = '/equifax/edit?recordId=' + id;
    window.location = url;
  } else {
    showFlashMessage('Contact administrator');
  }
})
// 前往新编辑页面按钮点击
$("#screTenantBtn").click(function(){
  let url = '/equifax/edit';
  window.location = url;
})