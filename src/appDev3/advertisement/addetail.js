var adDetail = {
  computed:{
    calculatedWith:function(){
      return Math.max(400,this.currentAnalysis.length*20)
    },
    activeTime:function(){
      if(!(this.curBanner.st && this.curBanner.et)){
        return ''
      }
      var ts = new Date(this.curBanner.et).getTime() - new Date(this.curBanner.st).getTime()
      return ts/(1000 * 3600 * 24)
    },
    isAdActive:function(){
      if(!(this.curBanner.st && this.curBanner.et)){
        return false
      }
      var edTs = new Date(this.curBanner.et).getTime();
      var stTs = new Date(this.curBanner.st).getTime();
      return (edTs > this.todayts) && (this.todayts > stTs)
    }
  },
  data () {
    return {
      todayts:new Date().getTime(),
      loading: true,
      curBanner:{},
      currentAnalysis:[],
      mode:'edit',
      provs:[],
      bannerTypes:[
        {k:'banner',v:'banner'},
        {k:'project',v:'project(index ad)'},
        // {k:'event',v:'event'},
        // {k:'popup',v:'popup'},
        {k:'realtor',v:'realtor'},
        {k:'mortgage',v:'mortgage'}
        // {k:'projectl',v:'project(list)'}
      ],
      dispVar:{
        edmAdmin:false,
        edmApprove:false,
        splashAdmin:false,
        languageList:[],
      },
      datas:[
        "edmAdmin",
        "edmApprove",
        'splashAdmin',
        'adFieldsRequired',
        'languageList',
      ],
      batch:['weekly','daily','saved'],
      batchRunTime:{
        weekly:'Sent at 1am on Saturday',
        daily:'Sent every afternoon at 5pm',
        saved:'Sent every afternoon at 6pm'},
      position:['top','middle','bottom'],
      baseTemp:{
        prov: ['CA'],
        pauseHist:[],
        zh:true,
        en:true,
      },
      adFieldsRequired:{},
      imageSize:{
        splash:{width:1080,height:2340},
        banner:{width:640,height:180},
        edm:'width:height=4:3',
      },
      area:['china','nochina'],
      language:[],
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.$getTranslate(this);
    var bus = window.bus, self = this;
    // bus.$on('some-event', function (rltr) {
    //   self.rltr = rltr;
    //   self.getBrkgs(rltr);
    // });
    this.getPageData(this.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.adFieldsRequired = d.adFieldsRequired;
      
      if(d.edmAdmin || d.edmApprove){
        self.bannerTypes.push({k:'edm',v:'edm'})
      }
      if(d.splashAdmin){
        self.bannerTypes.push({k:'splash',v:'splash'})
      }
      self.language = d.languageList
    });
    bus.$on('image-selected',({images})=>{
      if(!(Array.isArray(images.picUrls))) return
      const fullUrl = images.picUrls[0]
      this.curBanner.src = fullUrl
    })
    bus.$on('set-city',(d)=>{
      var {o,p_ab} = d.city;
      if (self.curBanner.prov[0] !== p_ab) {
        RMSrv.dialogAlert(`The selected city ${o} is not in the current province`);
        return;
      }
      if (self.curBanner.cities.indexOf(o) == -1) {
        self.curBanner.cities.push(o);
      }
    });
    self.getProvs();
    if(ADDetail._id){
      this.curBanner = ADDetail;
      // self.edit(ADDetail)
      if(!this.curBanner.prov){
        this.curBanner.prov= ['CA'];
      }
    }else{
      this.curBanner = this.baseTemp
      this.curBanner.type = vars.type
    }
    if(vars.create){
      this.mode = 'create'
    }
  },
  methods: {
    deleteFieldsByType(){
      var deleteFields = this.adFieldsRequired[this.curBanner.type],self=this;
      deleteFields.forEach(f => {
        delete self.curBanner[f]
      });
      self.curBanner.prov = ['CA']
    },
    pause(status){
      var self = this;
      var params = {type:this.curBanner.type,id:this.curBanner._id,pause:status};
      fetchData('/1.5/ads/pause', {body:params},function (err,ret) {
        if(err || ret.e){
          window.bus.$emit('flash-message',err || ret.e)
        }else{
          window.bus.$emit('flash-message','OK')
        }
      });
    },
    checkInappUrl(e){
      if(e.target.checked === true && !/^\//.test(this.curBanner.tgt)){
        RMSrv.dialogAlert('url should start with /')
        setTimeout(() => {
          this.curBanner.inapp = false
        }, 1000);
      }
    },
    selectCity(){
      var cfg = {hide:false};
      var url = '/1.5/city/select?search=1';
      var curProv = this.curBanner.prov[0];
      if (curProv) {
        url += `&prov=${curProv}`;
      }
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          var city = JSON.parse(val);
          window.bus.$emit('set-city',city);
        } catch (e) {
          console.error(e);
        }
      });
    },
    selectProv(prov) {
      // this.curBanner.cities = [];
      var idx = this.curBanner.prov.indexOf(prov);
      if (prov == 'CA') {
        this.curBanner.prov = [prov];
        return;
      } else {
        var idxCanada = this.curBanner.prov.indexOf('CA');
        if (idxCanada > -1) {
          this.curBanner.prov.splice(idxCanada,1);
        }
      }
      if (idx > -1) {
        this.curBanner.prov.splice(idx,1);
      } else {
        this.curBanner.prov.push(prov);
      }
    },
    isProvSelected(prov) {
      return this.curBanner.prov && (this.curBanner.prov.indexOf(prov)>-1);
    },
    getProvs () {
      var self = this;
      fetchData('/1.5/props/provs.json', {body:{}},function (err,ret) {
        if(err || ret.e){
          window.bus.$emit('flash-message',err || ret.e)
        }else{
          if (ret.ok) {
            self.provs = ret.p;
          }
        }
      });
    },
    save(){
      if (!Object.keys(this.curBanner)) {
        return;
      }
      if (this.mode !== 'create' && !this.curBanner._id){
        return;
      }
      var self = this;
      var params = Object.assign(self.curBanner,{mode:self.mode});
      // 必要字段检查
      let necessarySingleKey = ['st','et','tgt','type','grp'];
      necessarySingleKey.forEach(fld => {
        if(!params[fld] || params[fld].length == 0){
          return RMSrv.dialogAlert('广告需要'+fld);
        }
      });
      let necessaryMultipleKey = ['language','area'];
      necessaryMultipleKey.forEach(fld => {
        let hasFld = this[fld].some(value => params.hasOwnProperty(value) && (params[value] == true));
        if(!hasFld){
          return RMSrv.dialogAlert('广告需要'+fld);
        }
      });
      // edm需要检查posn和batch
      if (params.type == 'edm') {
        ['position','batch'].forEach(fld => {
          let hasFld = this[fld].some(value => params.hasOwnProperty(value) && (params[value] == true));
          if(!hasFld){
            return RMSrv.dialogAlert('edm广告需要'+fld);
          }
        });
      }
      // NOTE: dont show project type to agents
      if (params.type == 'project'){
        if (params.grp == 'all' || params.grp == 'realtor'){
          return RMSrv.dialogAlert('不要展示project类型的广告给agent')
        }
      }
      // 对于有图片要求的检查图片尺寸
      if(params.src && self.imageSize[params.type]){
        let imageSize = self.imageSize[params.type]
        let img = new Image();
        img.src = params.src;
        if(params.type == 'edm'){
          var ratio = img.width / img.height;
          if(ratio != 4/3){
            return RMSrv.dialogAlert('Image size is not qualified')
          }
        }else{
          if((img.width != imageSize.width) || (img.height != imageSize.height)){
            return RMSrv.dialogAlert('Image size is not qualified')
          }
        }
      }
      if (!params.prov || params.prov.length == 0 || params.prov.indexOf('CA') > -1) {
        delete params.prov;
      }
      var self = this;
      var url = '/1.5/ads/detail';
      fetchData(url,{body:params},function(err,ret) {
        if (err || ret.e) {
          return window.bus.$emit('flash-message',err || ret.e)
        }
        window.bus.$emit('flash-message','Saved')
        trackEventOnGoogle('new ad model','create or edit')
      })
    },
    openImageModel () {
      RMSrv.getPageContent('/1.5/img/insert', '#callBackString',{}, (val)=>{
      try {
        const json = JSON.parse(val);
        if(!json.picUrls || !Array.isArray(json.picUrls)){
          return
        }
        window.bus.$emit('image-selected',{images:json})
      } catch (e) {
        console.error(e);
      }
    })
    }
  }
};
initUrlVars();
var app = Vue.createApp(adDetail);
app.component('flash-message', flashMessage);
trans.install(app,{ref:Vue.ref})
app.mixin(pageDataMixins);
app.mount('#adDetail');