var adList = {
  computed:{
    calculatedWith:function(){
      return Math.max(400,this.currentAnalysis.length*20)
    }
  },
  data () {
    return {
      loading: true,
      bannerList:ADLIST,
      currentAnalysis:[],
      curBanner:{prov:[]},
      dispVar:{
        edmAdmin:false,
        edmApprove:false,
        splashAdmin:false,
        marketAdmin:false,
      },
      web:false,
      datas:[
        "edmAdmin",
        "edmApprove",
        'splashAdmin',
        'marketAdmin',
      ]
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.$getTranslate(this);
    var bus = window.bus, self = this;
    // bus.$on('some-event', function (rltr) {
    //   self.rltr = rltr;
    //   self.getBrkgs(rltr);
    // });
    this.getPageData(this.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('on-banner-menu-click', (type)=>{
      this.createNew(type)
    })
    if(vars.web){
      self.web = true
    }
    // self.initChart();
  },
  methods: {
    generateLabels(v){
      // chart x轴上显示的数据，需要匹配字段名
      let ret = []
      for (let i of v){
        if(/^\d.*\d(cc|)$/.test(i.n)){
          ret.push(i.n.match(/\d.*\d/)[0])
        }
      }
      return ret
    },
    generateDatas(v, tp) {
      // chart上显示的数据，需要匹配字段名
      let ret = new Array(this.generateLabels(v).length).fill(0); // 初始化数组,默认值为0
      let dateMap = new Map(); // 用于存储日期和索引的映射

      // 先生成日期标签并建立映射
      this.generateLabels(v).forEach((label, index) => {
        dateMap.set(label, index);
      });

      // 遍历数据并根据类型匹配填充对应位置
      for (let i of v) {
        let dateMatch = i.n.match(/^\d.*\d/);
        if (!dateMatch) continue;
        
        let dateKey = dateMatch[0];
        let index = dateMap.get(dateKey);
        if (index === undefined) continue;

        switch(tp) {
          case 'vc':
            if (/vc$/.test(i.n)) {
              ret[index] = i.v;
            }
            break;
          case 'send':
            if (/^\d.*\dSendc$/.test(i.n)) {
              ret[index] = i.v;
            }
            break;
          case 'show':
            if (/^\d.*\dShowc$/.test(i.n)) {
              ret[index] = i.v;
            }
            break;
          default:
            if (/^\d.*\d(cc|)$/.test(i.n)) {
              ret[index] = i.v;
            }
        }
      }

      return ret;
    },
    initChart(v=[],isEdm){
      var data = {
        labels: this.generateLabels(v),//array of dates
        datasets: [{
          label: 'view',
          backgroundColor: '#ffeb3b',
          borderColor: '#ffeb3b',
          data: this.generateDatas(v,'vc'),
        },{
          label: 'click',
          backgroundColor: '#e03131',
          borderColor: '#e03131',
          data: this.generateDatas(v,'click'),
        },
        ]
      }
      if(isEdm){
        data.datasets.push({
          label: 'send',
          backgroundColor: '#228bef',
          borderColor: '#228bef',
          data: this.generateDatas(v,'send'),
        })
        data.datasets[0].data = this.generateDatas(v,'show')
      }
      var config ={
        type: 'line',
        data: data,
        options: {}
      }
      var ctx = document.getElementById('myChart')
      if(this.myChart){
        this.myChart.destroy();
      }
      this.myChart = new Chart(
        ctx,
        config
      )
    },
    openDetail(id,type){
      var url = `/1.5/ads/detail?type=${type}&isPopup=1`;
      if(id){
        url += '&id='+id
      }else{
        url += '&create=1'
      }
      // let data = {
      //   type: 'creat', // 类型：创建或删除
      //   targetUrl: url, // 目标url
      //   model: 1, // 0不固定 1固定
      //   currentUrl: location.search, // 当前url
      //   elementId: 'adList', // 在一个页面下创建多个iframe，主页面的元素id，eg:<div id="dealtransaction">
      //   contentId: 'list', // 主页面创建的iframe内容的位置id，eg:<div id="iscontent">
      // }
      // creatIframes(data);
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: 'Edit detail'}, function(val) {
        if (val == ':cancel') {
          return;
        }
      });
      return;
    },
    createNew (type) {
      this.openDetail('',type)
    },
    edit(b){
      this.openDetail(b._id,b.type)
    },
    stripCurrentAnalysisRecords(){
      this.currentAnalysis = this.currentAnalysis.slice(-30)
      this.initChart(this.currentAnalysis,this.curBanner.type === 'edm')
    },
    openAnalysis (b) {
      if(b.analysis){
        this.currentAnalysis = b.analysis;
      }
      this.curBanner = Object.assign({},b);
      toggleModal('analysis','open')
      this.getDetailAnalysis(b)
    },
    getDetailAnalysis(b){
      var self = this;
      self.curBanner = b
      var params = {id:b._id,type:b.type,isNew:true};
      fetchData('/1.5/banner/manage/stats', {body:params},function (err,ret) {
        if(err || ret.e){
          window.bus.$emit('flash-message',err || ret.e)
        }else{
          let ad = ret.ad
          // console.log(ad)
          self.initChart(ad.v ,self.curBanner.type === 'edm')
          self.currentAnalysis = ad.v;
        }
      });
    },
    closeAnalysis () {
      this.curBanner = {prov:[]};
      this.currentAnalysis = [];
      toggleModal('analysis','close')
    },
    toggleCreateMenu () {
      this.toggleSMB()
    },
    toggleSMB () {
      var backdrop = document.getElementById('bannerMenuBackdrop'),
          body = document.querySelector('body');
      body.classList.toggle('smb-open-map-menu');
      if (backdrop) {
        var disp = backdrop.style.display === 'none' ? 'block' : 'none';
        return backdrop.style.display = disp;
      }
    },
    handleClick(type){
      if(!(this.dispVar.marketAdmin || (type == 'edm' && (this.dispVar.edmAdmin || this.dispVar.edmApprove)) || (type == 'splash' && this.dispVar.splashAdmin))){
        return
      }
      window.bus.$emit('on-banner-menu-click', type)
      this.toggleSMB()
    },
    showInBrowser(tgt){
      if (!tgt) {
        return;
      }
      RMSrv.showInBrowser(tgt)
    },
  }
};
initUrlVars();
var app = Vue.createApp(adList);
app.component('flash-message', flashMessage);
trans.install(app,{ref:Vue.ref})
app.mixin(pageDataMixins);
app.mount('#adList');