var sellhome = {
  data() {
    return {
      isBusy:false,
      hists:[],
      calculatedSpWidth:144,
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        sessionUser: {},
        reqHost: '',
        isAdmin: false,
        userCity: {o:'Toronto',n:'多伦多',p:'Ontario'},
      },
      datas: [
        'isApp',
        'isCip',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'userCity',
        'isAdmin'
      ],
    };
  },
  mounted () {
    this.$getTranslate(this);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor()
    // }
    this.calculatedSpWidth = parseInt((window.innerWidth-30)/1.2);
    var bus = window.bus, self = this;
    // if open from url, can init from url params.
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });

    bus.$on('add-hist', function(hist){
      self.prop.uaddr = hist.uaddr;
      self.histcnt++;
    });
    bus.$on('remove', function(hist){
      self.remove(hist.e,hist.hist);
    });
    bus.$on('openResult', function(hist){
      self.openResultModal(hist.e,hist.hist,hist.fromhist);
    });
    this.getUserHist();
    // this.getLatestHist();
    if (!window.openEvaluatePage){
      window.openEvaluatePage = this.openEvaluatePage;
    }
    loadLazyImg()
  },
  methods: {
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    showSignup(){
      toggleModal('signupModal','opens');
      // console.log('show signupform');
    },
    toggleModal(a,b){
      toggleModal(a,b);
    },
    remove:function(e, hist) {
      var msg = this.getTranslate('afterDelete');
      var self = this;
      this.removeHist(e, hist.hist.uid, hist._id, null, msg, function() {
        var index = self.hists.findIndex(function(el) {
          return el._id == hist._id;
        })
        self.hists.splice(index,1);
      });
    },
    addTrack:function(e,tracked,hist) {
      e.preventDefault();
      e.stopPropagation();
      var self = this;
      hist.tracked = tracked;
      hist = Object.assign({}, hist);
      fetchData('/1.5/evaluation/addTrack', {body:{uaddr:hist._id, tracked: tracked}},function(err,ret) {
        if (err){
          console.log(err)
          self.msg = "error";
        }else{
          if (ret.ok == 1) {
          } else {
            console.log(ret.e)
            self.msg = "error";
          }
        }
      })
    },
    getTime(ts) {
      return formatDate(ts);
    },
    goStat() {
      var url = '/1.5/prop/stats';
      url = this.appendCityToUrl(url, this.dispVar.userCity);
      return window.location = url;
    },
    getUserHist() {
      var self = this;
      fetchData('/1.5/evaluation/userHist', {body:{limit: 30}},function(err,ret) {
        if (err){
          console.log(err)
          self.msg = "error";
        }else{
          if (ret.ok == 1) {
            self.hists = ret.hist || [];
          } else {
            console.log(ret.e)
            self.msg = "error";
          }
        }
      })
    },
    openResultModal(e, hist, fromhist) {
      e.preventDefault();
      e.stopPropagation();
      var url = '/1.5/evaluation/result.html?uaddr=' + encodeURIComponent(hist._id) + '&id=' + hist.hist._id +'&lang='+this.dispVar.lang+'&d=/1.5/landlord/sellhome';
      if (fromhist) {
        url+='&hist=1';
      }
      url = RMSrv.appendDomain(url);
      window.location.href = url;
    },
    goTo(url) {
      window.location.href=url;
    },
    goToReno(url) {
      if(!url){
        url = 'https://www.realrenovation.ca?d=/1.5/landlord/sellhome';
      }
      return RMSrv.showInBrowser(url);
    },
    openEvaluatePage () {
      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1'
      url = RMSrv.appendDomain(url);
      RMSrv.openTBrowser(url, {nojump:true, title:this.getTranslate('Conditions')});
    },
  },
  computed: {
    spWrapperWidth() {
      return this.calculatedSpWidth * (this.hists.length) + (this.hists.length-1) * 10;
    },
  },
};
var app = Vue.createApp(sellhome);
app.component('hist-card', histCard);
trans.install(app,{ref:Vue.ref})
app.config.globalProperties.$filters = {currency:filters.currency}
app.mixin(rmsrvMixins);
app.mixin(pageDataMixins);
app.mixin(evaluateMixins);
app.mount('#sellhome');