function scrollBar(){
  var navLinksContainer = document.querySelector('.list-nav-container');
  var navLinkSelected = document.querySelector('.list-nav-link.selected');
  var navLinkActive = document.querySelector('.list-nav-active');
  var boundData = navLinkSelected.getBoundingClientRect();
  var scroll = 0;
  scroll = boundData.left + (boundData.width)/2 - window.innerWidth/2;
  navLinksContainer.scrollLeft = scroll;
  navLinkActive.style.left = boundData.left + (boundData.width)/2 - 15 +'px';
};
function goBack(d){
  d != ('undefined' || null) ? document.location.href = d : window.history.back();
};
window.addEventListener('load',()=>{
  scrollBar();
});