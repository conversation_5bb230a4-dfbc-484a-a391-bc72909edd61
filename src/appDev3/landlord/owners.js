var owners = {
  data() {
    return {
      isBusy:false,
      hists:[],
      cmtys:[],
      postsLength:0,
      calculatedSpWidth:144,
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        sessionUser: {},
        reqHost: '',
        isAdmin: false,
        userCity: {o:'Toronto',n:'多伦多',p:'Ontario'},
      },
      hasWechat:true,
      datas: [
        'isApp',
        'isCip',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'userCity',
        'isAdmin',
        'defaultEmail',
      ],
      latests:[],
      totalHist:0,
      totalTrackedProp:0,
      totalTrackingUsers:0,
      yellowpageList:[
        {
          url:'/1.5/yellowpage?tp=mortgage&d=/1.5/landlord/owners',
          img:'/img/staging/reno_contract.png',
          name:'Mortgage'
        },
        {
          url:'/1.5/yellowpage?tp=insur&d=/1.5/landlord/owners',
          img:'/img/staging/reno_solution.png',
          name:'Insurance'
        },
        {
          url:'/1.5/yellowpage?tp=bas&d=/1.5/landlord/owners',
          img:'/img/staging/reno_booking.png',
          name:'B&A Sales'
        },
        {
          url:'/1.5/yellowpage?tp=maint&d=/1.5/landlord/owners',
          img:'/img/staging/reno_construction.png',
          name:'Maintenance'
        }
        ]
    };
  },
  mounted () {
    this.$getTranslate(this);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor()
    // }
    this.postsLength = document.querySelector('#postsLength').innerHTML;
    this.calculatedSpWidth = parseInt((window.innerWidth-30)/1.2);
    var bus = window.bus, self = this;
    // if open from url, can init from url params.
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (RMSrv.hasWechat){
        RMSrv.hasWechat((has)=>{
          if (has!=null) {
            self.hasWechat = has
          }
        })
      }
    });

    if (!window.showSignup){
      window.showSignup = this.showSignup;
    }
    bus.$on('add-hist', function(hist){
      self.prop.uaddr = hist.uaddr;
      self.histcnt++;
    });
    bus.$on('remove', function(hist){
      self.remove(hist.e,hist.hist);
    });
    bus.$on('openResult', function(hist){
      self.openResultModal(hist.e,hist.hist,hist.fromhist);
    });
    this.getUserHist();
    this.getFavCmty();
    if (!window.openEvaluatePage){
      window.openEvaluatePage = this.openEvaluatePage;
    }
    if (!window.goTo){
      window.goTo = this.goTo;
    }
    loadLazyImg()
  },
  methods: {
    topUp(){
      var url = '/1.5/prop/topup/charge';
      RMSrv.openTBrowser(this.appendDomain(url), {nojump:true, title:this.getTranslate('topListing')});
    },
    goToReno(url) {
      if(!url){
        url = 'https://www.realrenovation.ca?d=/1.5/landlord/owners';
      }
      return RMSrv.showInBrowser(url);
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    showSignup(){
      toggleModal('signupModal','opens');
      // console.log('show signupform');
    },
    toggleModal(a,b){
      toggleModal(a,b);
    },
    getFavCmty:function() {
      var self = this;
      fetchData('/1.5/community/favList',{body:{full:1}},function(err,ret) {
        if (err || ret.err) {
          return RMSrv.dialogAlert(err || ret.err);
        } else {
          self.cmtys = ret.cmtys;
        }
      })
    },
    remove:function(e, hist) {
      var msg = this.getTranslate('afterDelete');
      var self = this;
      this.removeHist(e, hist.hist.uid, hist._id, null, msg, function() {
        var index = self.hists.findIndex(function(el) {
          return el._id == hist._id;
        })
        self.hists.splice(index,1);
      });
    },
    goCommunity() {
      var url = '/1.5/community/filter';
      url = this.appendCityToUrl(url, this.dispVar.userCity);
      return window.location = url;
    },
    getUserHist() {
      var self = this;
      fetchData('/1.5/evaluation/userHist', {body:{limit: 30,tracked:true}},function(err,ret) {
        if (err){
          console.log(err)
          self.msg = "error";
        }else{
          if (ret.ok == 1) {
            self.hists = ret.hist || [];
          } else {
            console.log(ret.e)
            self.msg = "error";
          }
        }
      })
    },
    openResultModal(e, hist, fromhist) {
      e.preventDefault();
      e.stopPropagation();
      var url = '/1.5/evaluation/result.html?uaddr=' + encodeURIComponent(hist._id) + '&id=' + hist.hist._id +'&lang='+this.dispVar.lang+'&d=/1.5/landlord/owners';
      if (fromhist) {
        url+='&hist=1';
      }
      url = RMSrv.appendDomain(url);
      window.location.href = url;
    },
    openEvaluatePage () {
      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1'
      url = RMSrv.appendDomain(url);
      RMSrv.openTBrowser(url, {nojump:true, title:this._('Evaluation Conditions','evaluation')});
    },
    goTo(url) {
      window.location.href=url;
    },
    openEvaluatePage () {
      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1'
      url = RMSrv.appendDomain(url);
      RMSrv.openTBrowser(url, {nojump:true, title:this.getTranslate('Conditions')});
    },
  },
  computed: {
    spWrapperWidth() {
      return this.calculatedSpWidth * (this.hists.length) + (this.hists.length-1) * 10;
    },
  },
};
var app = Vue.createApp(owners);
trans.install(app,{ref:Vue.ref})
app.component('hist-card', histCard);
app.component('flash-message', flashMessage);
app.component('daily-feeds', dailyFeeds);
app.config.globalProperties.$filters = {currency:filters.currency}
app.mixin(rmsrvMixins);
app.mixin(pageDataMixins);
app.mixin(evaluateMixins);
app.mount('#owners');