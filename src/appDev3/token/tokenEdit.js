var tokenEdit = {
  data(){
    return{
      tokenScore: '',
      tokenReason: '',
      userInfo: {
        eml: '',
        fnm:'',
        uid: '',
        avt: '',
        token:'-'
      },
      isFocusInput: false,
      isFocusText: false,
      ERRORINFO: 'Input value is incorrect',
      isShowResult: false,
      TokenType: [],
      selectedType: '',
      reasonTags: [],
      regexScore: /^(\-)?\d+(\.\d{1,2})?$/
    }
  },
  computed: {
    showModifyBtn:function(){
      if(this.newTokenValue == this.ERRORINFO){
        return true
      }
      if(this.regexScore.test(this.tokenScore) && this.tokenReason.length){
        return false
      }
      return true
    },
    newTokenValue:function(){
      if(!this.regexScore.test(this.tokenScore)){
        return 'Input integer or up to two decimals';
      }
      let parseT = Number((this.tokenScore * 100).toFixed(0));
      let canBeNegative = false;
      for(let i=0;i<this.TokenType.length;i++){
        if(this.TokenType[i].type == this.selectedType){
          canBeNegative = this.TokenType[i].canBeNegative;
          break;
        }
      }
      if (Number.isNaN(parseT)){
        return this.ERRORINFO;
      }
      if ((this.userInfo.token == '-') && canBeNegative){
        return (parseT / 100).toFixed(2);
      }
      if ((this.userInfo.token == '-') && (parseT < 0)){
        return this.ERRORINFO;
      }
      if ((this.userInfo.token == '-') && (parseT >= 0)){
        return (parseT / 100).toFixed(2);
      }
      let parseOldToken = Number((this.userInfo.token * 100).toFixed(0));
      let newToken = parseOldToken + parseT;
      if ((newToken < 0) && (!canBeNegative)) return this.ERRORINFO;
      return (newToken / 100).toFixed(2);
    }
  },
  mounted(){
    let self = this;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    self.$getTranslate(self);
    this.TokenType = decodeJsonString2Obj('TOKEN_TYPES','array');
    this.userInfo = decodeJsonString2Obj('userInfo');
    if(vars.tokenType){
      this.selectedType = vars.tokenType;
      bus.$emit('tokenType-preSelected',{selectedType:this.selectedType,token:false});
    }else{
      this.selectedType = '';
    }
    this.getReasontags();
    bus.$on('tokenType-select',(val) => {
      if(val){
        self.selectedType = val.selectedType;
        self.userInfo.token = val.token;
      }
    });
  },
  methods:{
    modifyToken(){
      let self = this;
      setLoaderVisibility('block');
      let body = {
        uid: self.userInfo.uid,
        token: self.tokenScore,
        reason: self.tokenReason,
        type: this.selectedType
      }
      fetchData('/token/adminModify',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        self.userInfo.token = ret.result.token;
        self.goBack();
      });
    },
    handleInput(event) {
      if(event.target.value){
        this.isShowResult = true;
      } else {
        this.isShowResult = false;
      }
    },
    goBack(){
      let result = {
        token:this.userInfo.token,
        selectedType:this.selectedType
      }
      window.rmCall(':ctx:'+JSON.stringify(result));
    },
    getReasontags(){
      let self = this;
      setLoaderVisibility('block');
      fetchData('/token/getReasonTags',{body:{}},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        this.reasonTags = ret.result;
      });
    },
    chooseTag(info,index){
      this.reasonTags.forEach(element => {
        element.isChoose = false
      });
      this.reasonTags[index].isChoose = true;
      this.tokenReason = info.reason;
    }
  },
};
initUrlVars();
var app = Vue.createApp(tokenEdit);
trans.install(app,{ref:Vue.ref});
app.component('flash-message', flashMessage);
app.component('token-user-info', tokenUserInfo);
app.component('token-select-type', tokenSelectType);
app.mount('#tokenEdit');