var systemKey = {
  data(){
    return{
      systemKeyList:[],
      groupSystemKeys: {},
      newTokenScore: -1,
      newEntryName: '',
      TokenType: [],
      selectedType: ''
    }
  },
  mounted(){
    let self = this;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    self.$getTranslate(self);
    this.TokenType = decodeJsonString2Obj('TOKEN_TYPES','array');
    self.getTokenKeyList();
    bus.$on('tokenType-select',(val) => { // type tag页面选择type后
      if(val){
        self.selectedType = val.selectedType;
        if(val.selectedType == ''){
          self.systemKeyList = self.groupSystemKeys[self.TokenType[0].type];
        }else{
          self.systemKeyList = self.groupSystemKeys[val.selectedType];
        }
      }
    });
  },
  methods:{
    getTokenKeyList(){
      let self = this;
      setLoaderVisibility('block');
      fetchData('/token/getKeyList',{body:{}},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        self.groupSystemKeys = ret.result;
        self.systemKeyList = self.groupSystemKeys[self.TokenType[0].type];
      });
    },
    clearSystemKeyStatus(index){
      this.systemKeyList[index].isEdit = false;
      this.newTokenScore = -1;
      this.newEntryName = '';
    },
    updateKeyScore(keyInfo,index){
      let self = this;
      if(self.newTokenScore == keyInfo.t && self.newEntryName == keyInfo.nm){
        self.clearSystemKeyStatus(index);
        return
      }
      const regex = /^(\-)?\d+(\.\d{1,2})?$/;
      if(!self.newEntryName.length){
        return bus.$emit('flash-message','Input Entry Name');
      }
      if(!regex.test(self.newTokenScore)){
        return bus.$emit('flash-message','Input integer or up to two decimals');
      }
      setLoaderVisibility('block');
      let body = {
        key: keyInfo.key,
        token: self.newTokenScore,
        name: self.newEntryName
      }
      fetchData('/token/updateSystemKey',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        self.systemKeyList[index].t = Number(self.newTokenScore).toFixed(2);
        self.systemKeyList[index].nm = self.newEntryName;
        self.clearSystemKeyStatus(index);
      });
    },
    initEditStatus(keyInfo,index){
      this.systemKeyList.forEach(ele => {
        ele.isEdit = false;
      });
      this.systemKeyList[index].isEdit = true;
      this.newTokenScore = keyInfo.t;
      this.newEntryName = keyInfo.nm;
    }
  }
};
var app = Vue.createApp(systemKey);
trans.install(app,{ref:Vue.ref});
app.component('flash-message', flashMessage);
app.component('token-select-type', tokenSelectType);
app.mount('#systemKey');