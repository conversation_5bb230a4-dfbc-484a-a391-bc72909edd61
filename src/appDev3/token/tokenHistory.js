var tokenHistory = {
  data(){
    return{
      historyList: [],
      page: 0,
      waiting:false,
      hasMoreProps:false,
      loading:false,
      scrollElement:'',
      type: vars.type,
      userInfo: {
        eml: '',
        fnm:'',
        uid: '',
        avt: ''
      },
      datas:[
        'isTokenAdmin'
      ],
      dispVar: {
        isTokenAdmin: false
      },
      TokenType: [],
      selectedType: '',
      billTypeSelected: 'fullBill',
      userList: [],
      showUserList: [],
      showUsers: false,
      selectDate: '',
      selectAll: false,
      searchText: '',
      searchInputTimeout: null,
      startDate: new Date('2024-12-01T00:00:00'),
      dateList:[],
      showDateSelect: false,
      selectedDate: '',
      uids: [],
    }
  },
  mounted(){
    let self = this;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    self.$getTranslate(self);
    this.userInfo = decodeJsonString2Obj('userInfo');
    this.uids.push(self.userInfo.uid);
    this.TokenType = decodeJsonString2Obj('TOKEN_TYPES','array');
    if(vars.tokenType){
      this.selectedType = vars.tokenType;
      bus.$emit('tokenType-preSelected',{selectedType:self.selectedType,token:false});
    }else{
      this.selectedType = '';
    }
    self.getPageData(this.datas,{},true);
    this.getDateList();
    bus.$on('pagedata-retrieved',function(d){
      self.dispVar = Object.assign(self.dispVar, d);
    });
    bus.$on('token-changed',(val) => { // 编辑页面修改了token之后
      if(val.selectedType !== self.selectedType){
        self.selectedType = val.selectedType;
      }
      bus.$emit('tokenType-preSelected',{selectedType:self.selectedType,token:val.token});
      self.initData();
    });
    bus.$on('tokenType-select',(val) => {
      if(val){
        self.selectedType = val.selectedType;
        self.billTypeSelected = val.billTypeSelected;
        self.initData();
      }
    });
    bus.$on('selectDate-list',(val) => {
      if(val){
        self.showDateSelect = val.showDate;
        if(val.selectedDate){
          self.selectedDate = val.selectedDate;
          self.initData();
        }
      }
    });
    self.scrollElement = document.getElementById('tokenHistory');
    self.scrollElement.addEventListener("scroll", self.listScrolled);
  },
  watch:{
    searchText:function(val){
      var self = this;
      if(val.length < 2){
        this.showUserList = this.userList;
        return 
      }
      clearTimeout(self.searchInputTimeout);
      self.searchInputTimeout = setTimeout(function () {
        self.searchInputTimeout = null;
        self.searchUserInfo();
      }, 500);
    }
  },
  computed: {
    showConfirmBtn:function(){
      for(let i=0;i<this.userList.length;i++){
        if(this.userList[i].isSel){
          return false;
        }
      }
      return true;
    }
  },
  methods:{
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page++;
              self.getTokenHistory('more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getPostParams(){
      let body = {
        type: this.selectedType
      }
      if(this.uids.length){
        body.uids = this.uids;
      }
      if(this.selectedDate.length){
        body.date = this.selectedDate;
      }
      return body;
    },
    getTokenHistory(more){
      let self = this;
      setLoaderVisibility('block');
      let body = this.getPostParams();
      body.page = self.page;
      fetchData('/token/getHistoryDetail',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        if(more){
          data.history.forEach((element) => {
            let index = self.historyList.findIndex((item)=>{
              return item.date == element.date
            })
            if(index == -1){
              self.historyList.push(element);
            } else {
              self.historyList[index].his = self.historyList[index].his.concat(element.his);
            }
          });
        } else {
          self.historyList = data.history;
        }
        let hisLength = 0;
        data.history.forEach((ele) => {
          hisLength += ele.his.length;
        });
        if(hisLength < data.limit){
          self.hasMoreProps = false;
        }else{
          self.hasMoreProps = true;
        }
      });
    },
    getTokenSummer(){
      let self = this;
      setLoaderVisibility('block');
      let body = this.getPostParams();
      fetchData('/token/getSummaryDetail',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        self.historyList = data.history;
      });
    },
    getUserList(){
      let self = this;
      if(self.userList.length) return;
      setLoaderVisibility('block');
      fetchData('/token/getInrealMember',{body:{}},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        self.userList = ret.result;
        self.showUserList = ret.result;
      });
    },
    goBack(){
      if(this.type == 'admin'){
        let result = {
          selectedType:this.selectedType
        }
        window.rmCall(':ctx:'+JSON.stringify(result));
      } else {
        window.location.href = "/1.5/settings";
      }
    },
    gotoWeCard(uid){
      let url = `/1.5/wesite/${uid}`;
      window.location.href = url;
    },
    getYearAndMonth(date){
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}/${month}`;
    },
    initData(){
      this.page = 0;
      this.historyList = [];
      if(this.billTypeSelected == 'summary'){
        if(!this.selectedDate.length){
          let yearMonth = this.getYearAndMonth(new Date())
          this.selectedDate = yearMonth;
        }
        this.getTokenSummer();
      } else {
        this.getTokenHistory();
      }
    },
    openUserSelectPage(){
      this.userList.forEach(element => {
        element.isSel = false;
      });
      this.showUsers = true;
      this.selectAll = false;
      this.getUserList();
    },
    selectUsers(user){
      user.isSel = !user.isSel;
      this.selectAll = true;
      for(let i = 0; i < this.userList.length; i++){
        if(!this.userList[i].isSel){
          this.selectAll = false;
          break;
        }
      }
    },
    selectAllUsers(){
      this.showUserList.forEach(element => {
        element.isSel = !this.selectAll;
      });
      this.userList.forEach(element => {
        element.isSel = !this.selectAll;
      });
    },
    searchUserInfo(){
      this.showUserList = [];
      let regExp = new RegExp(this.searchText,'ig');
      this.userList.forEach(element => {
        if(regExp.test(element.eml) == true ||  regExp.test(element.fnm) == true){
          this.showUserList.push(element);
        }
      });
    },
    confirmSelectUsers(){
      this.showUsers = false;
      this.uids = [];
      this.userList.forEach((uid) => {
        if(uid.isSel){
          this.uids.push(uid.uid);
          this.userInfo = uid;
        }
      });
      if (this.uids.length > 1) {
        this.userInfo = {eml: '',fnm:'',uid: '',avt: ''}
      }
      bus.$emit('member-changed',this.uids);
    },
    getDateList(){
      let currentDate = new Date();
      while (currentDate >= this.startDate) {
        let yearMonth = this.getYearAndMonth(currentDate)
        this.dateList.push(yearMonth);
        currentDate.setMonth(currentDate.getMonth() - 1);
      }
    },
    selectTokenDate(date){
      bus.$emit('pre-selected-date',date);
      this.showDateSelect = true;
    },
    goModifyToken(){
      let self = this;
      let url = `/token/edit?uid=${this.userInfo.uid}&tokenType=${this.selectedType}`;
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          let result = JSON.parse(val);
          bus.$emit('tokenType-preSelected',{selectedType:result.selectedType,token:result.token});
          self.selectedType = result.selectedType;
          self.initData();
        } catch (e) {
          console.error(e);
        }
      });
    },
    gotoDetail(propId,isOpen){
      if(!isOpen) return;
      openPopup(`/1.5/prop/detail/inapp?id=${propId}`,this.$_('RealMaster'));
    }
  }
};
initUrlVars();
var app = Vue.createApp(tokenHistory);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('flash-message', flashMessage);
app.component('token-user-info', tokenUserInfo);
app.component('token-select-type', tokenSelectType);
app.component('select-date-component', selectDateComponent);
app.mount('#tokenHistory');