var tokenList = {
  data(){
    return{
      userList: [],
      searchEmail: '',
      page: 0,
      waiting:false,
      hasMoreProps:false,
      loading:false,
      scrollElement:'',
      selectedType: '',
      TokenType: []
    }
  },
  mounted(){
    let self = this;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    self.$getTranslate(self);
    this.TokenType = decodeJsonString2Obj('TOKEN_TYPES','array');
    this.selectedType = '';
    self.scrollElement = document.getElementById('tokenList');
    self.scrollElement.addEventListener("scroll", self.listScrolled);
    bus.$on('tokenType-select',(val) => { // type tag页面选择type后
      if(val){
        self.selectedType = val.selectedType;
        self.getUserListByEml();
      }
    });
    bus.$on('token-changed',(val) => { // 编辑页面修改了token之后
      if(val.selectedType !== self.selectedType){
        self.selectedType = val.selectedType;
        bus.$emit('tokenType-preSelected',{selectedType:self.selectedType,token:false});
        self.getUserListByEml();
      }
    });
    this.getTokenList();
  },
  methods:{
    listScrolled:function(){
      var self = this, wait = 400;
      if (!self.waiting && self.hasMoreProps) {
        self.waiting = true;
        self.loading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.hasMoreProps) {
              self.page++;
              self.getTokenList('more');
            } else {
              self.loading = false;
            }
          } else {
            self.loading = false;
          }
        }, wait);
      }
    },
    getTokenList(more){
      let self = this;
      setLoaderVisibility('block');
      let body = {
        page: this.page,
        type: this.selectedType
      }
      if (this.searchEmail.length){
        body.eml = this.searchEmail;
      }
      fetchData('/token/getUserList',{body},(err,ret)=>{
        setLoaderVisibility('none');
        if (!ret.ok || err) {
          let msg = err? 'Error' : ret.e;
          return bus.$emit('flash-message',msg);
        }
        let data = ret.result;
        if(more){
          self.userList = self.userList.concat(data.userList);
        } else {
          self.userList = data.userList;
        }
        if(data.userList.length < data.limit){
          self.hasMoreProps = false;
        }else{
          self.hasMoreProps = true;
        }
      });
    },
    getUserListByEml(){
      this.page = 0;
      this.userList = [];
      this.getTokenList();
    }
  },
};
var app = Vue.createApp(tokenList);
trans.install(app,{ref:Vue.ref});
app.mixin(pageDataMixins);
app.component('flash-message', flashMessage);
app.component('token-user-info', tokenUserInfo);
app.component('token-select-type', tokenSelectType);
app.mount('#tokenList');