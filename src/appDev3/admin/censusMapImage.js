var census = decodeJsonString2Obj('census');
var censusIdContainer = document.getElementById('censusId');
var mapbox = new Mapbox();
var skip = 0;
var doAll = false;
mapbox.init('mapbox-container');
mapbox.map.on('load', function() {
  var bnds = census.bnds;
  if (bnds) {
    drawBoundaryOnMap(bnds);
  }
});

document.getElementById('updateAllCensus').addEventListener('click',()=>{
  doAll = true;
  updateCensus(census);
});

document.getElementById('pauseAllCenusu').addEventListener('click',()=>{
  doAll = false;
});

document.getElementById('updateOneCensus').addEventListener('click',()=>{
  updateCensus(census);
});


function updateCensus(census) {
  var newBbox = convertBounds2Bbox(mapbox.map.getBounds());
  skip++;
  var body = {skip,censusId:census._id,bbox:newBbox};
  fetchData('/census/generateMapImage',{body},(err,res)=>{
    if (!doAll) {
      return;
    }
    var nextCensus = res.census;
    setTimeout(() => {
      censusIdContainer.innerText = nextCensus._id;
      updateCensus(nextCensus);
      drawBoundaryOnMap(nextCensus.bnds);
    }, 500);
  });
}

function convertBounds2Bbox(bbox) {
  var sw = bbox.getSouthWest().toArray();
  var ne = bbox.getNorthEast().toArray();
  return [sw[0],sw[1],ne[0],ne[1]];
}

function drawBoundaryOnMap(bnds) {
  if (mapbox.map.getLayer('route')) {
    mapbox.map.removeLayer('route');
  }
  if (mapbox.map.getSource('route')) {
    mapbox.map.removeSource('route');
  }
  var bounds = new mapboxgl.LngLatBounds();
  var latlngs = [];
  bnds.features.forEach(f => {
    f.geometry.coordinates.forEach(c => {
      c.forEach(loc => {
        if (f.geometry.type == 'MultiPolygon') {
          loc.forEach(l=>{
            latlngs.push([l[0],l[1]]);
            bounds.extend(l);
          });
        } else {
          latlngs.push([loc[0],loc[1]]);
          bounds.extend(loc);
        }
      });
    });
  });
  mapbox.map.fitBounds(bounds,{duration: 0,padding: 20});
  mapbox.map.addSource('route', {
    'type': 'geojson',
    'data': {
      'type': 'Feature',
      'properties': {},
      'geometry': {
        'type': 'LineString',
        'coordinates': latlngs
      }
    }
  });
  mapbox.map.addLayer({
    'id': 'route',
    'type': 'line',
    'source': 'route',
    'layout': {
      'line-join': 'round',
      'line-cap': 'round'
    },
    'paint': {
      'line-color': '#ff2600',
      'line-width': 3
    }
  });
}