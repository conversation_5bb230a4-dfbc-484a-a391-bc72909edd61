var userFollowup = {
  data() {
    return {
      eml:'',
      user:{},
      users:[],
      editableFields: [
        'fubEml',
        'fubMbl',
        'fubAgentId'
      ],
      syncRet:{ret:{},ok:0},
      tp:'agent',
      userFubInfo:null,
      title:"Manage Agent in Followup CRM",
      fubResolved:false
    };
  },
  mounted() {
    this.$getTranslate(this);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (vars.tp) {
      this.tp = vars.tp
      if (this.tp =='people') {
        this.editableFields = ['fubId']
        this.title = "Manage People in Followup CRM"
      }
    }
    this.getUserByEmail();
    var bus = window.bus;
    // bus.$on('set-city',(d)=>{
    //   var {o,p_ab} = d.city;
    //   var user = this.user;
    //   if (user.serviceCity && (user.serviceCity.indexOf(o) == -1)) {
    //     user.serviceCity.push(o);
    //   }
    //   this.user = user;
    // });
   
  },
  methods:{
    openSysUser() {
      url = '/sys/user?eml='+this.user.eml
      RMSrv.openTBrowser(url)
    },
    goToRealtorFollow() {
      url = '/1.5/realtorfollow?eml='+this.user.eml
      RMSrv.openTBrowser(url)
    },
    goToUserFollow() {
      url = '/1.5/userfollow?eml='+this.user.eml
      RMSrv.openTBrowser(url)
    },
    editUser(u) {
      if (!u.serviceCity) {
        u.serviceCity = [];
      }
      this.user = u;
    },
    clearEml() {
      this.eml = '';
      this.getUserByEmail();
    },
    getUserByEmail() {
      var body = {eml:this.eml,tp:this.tp};
      this.user = {};
      fetchData('/1.5/getFollowUpCRMUsers',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        if (ret.ok && ret.users) {
          this.users = ret.users;
        }
      });
    },
    // get all agents from fub, match to app with email
    // display sync result
    syncFubUsers() {
      var body = {eml:this.eml,tp:this.tp};
      this.user = {};
      var self = this
      fetchData('/1.5/fub/syncFubUsers',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        self.syncRet = ret
        self.getUserByEmail()
      });
    },

    getFubPeopleByFubId() {
      var body = {fubId: this.user.fubId};
      fetchData('/1.5/fub/getFubPeopleByFubId',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.userFubInfo = ret.user
        } else { //not found
          RMSrv.dialogAlert('no user found in fub for fubId ' + this.user.fubId);
        }
      });
    },
    getUserFromFub(){
      if (this.tp =='agent') {
        this.getFubAgentByFubEml()
      } else {
        this.getFubPeopleByFubId()
      }
    },

    // get all people from fub, match to app with email
    // display sync result， not finished
    // syncPeopleFromFub() {
    //   var body = {eml:this.eml};
    //   this.user = {};
    //   body.tp = 'people'
    //   fetchData('/1.5/fub/syncFubPeople',{body},(err,ret)=>{
    //     if (err) {
    //       RMSrv.dialogAlert(err.toString());
    //     }
    //     if (ret.ok && ret.users) {
    //       this.users = ret.users;
    //     }
    //   });
    // },
    getFubAgentByFubEml() {
      var body = this.user;
      fetchData('/1.5/fub/getFubAgentByFubEml',{body},(err,ret)=>{
        if (err = (err ||ret.err)){
          RMSrv.dialogAlert(err.toString());
        }
        if (ret.ok && (agents = ret.agents)) {
          if (agents.length > 1) {
            RMSrv.dialogAlert('more than one fub user for email ' + this.user.fubEml);
          }
          if (agents.length == 1) {
            user = agents[0];
            this.user.fubMbl = user.phone;
            this.user.fubAgentId = user.id;
            RMSrv.dialogAlert('Updated');
          } else { //not found
            RMSrv.dialogAlert('no agent found in fub for email ' + this.user.fubEml);
          }
        }
      });
    },
    backToList() {
      this.user = {};
      this.userFubInfo=null
      this.fubResolved = false
    },

    selectServiceCity() {
      var cfg = {hide:false};
      var url = '/1.5/city/select?search=1';
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          var city = JSON.parse(val);
          window.bus.$emit('set-city',city);
        } catch (e) {
          console.error(e);
        }
      });
    },
    removeCity(idx) {
      var user = this.user;
      user.serviceCity.splice(idx,1);
      this.user = user;
    },
    updateUserFollowup() {
      var body = this.user
      body.tp = this.tp
      if(this.fubResolved && this.user.fubToFix) {
        body.fubResolved = true
      }
      fetchData('/1.5/updateUserFollowup',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        if (ret.ok) {
          RMSrv.dialogAlert('Updated');
        } else {
          RMSrv.dialogAlert(ret.e);
        }
      });
    },
  }
}
var app = Vue.createApp(userFollowup);
trans.install(app,{ref:Vue.ref})
app.mount('#user-followup');