var userFollow = {
  data() {
    return {
      userEml:'',
      realtorEml: '',
      user:{},
      userFubInfo:''
    };
  },
  mounted(){
    this.$getTranslate(this);
    if (vars.eml) {
      this.userEml = vars.eml
      this.getUserByEmail(this.userEml)
    }
  },
  methods:{
    getUserByEmail() {
      var body = {eml:this.userEml};
      fetchData('/1.5/getUserRolesFlwng',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.user = ret.user;
        } else {
          RMSrv.dialogConfirm(ret.e)
        }
      });
    },
    removeUserFlwngRm(realtorEml) {
      var body = {eml:this.userEml,realtorEml};
      fetchData('/1.5/removeUserFlwngRm',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.getUserByEmail();
        } else{
          RMSrv.dialogConfirm(ret.e)
        }
       
      });
    },
    assignRealtor() {
      var body = {eml:this.userEml,realtorEml:this.realtorEml,op:'addR'};
      fetchData('/1.5/addRMRealtor2User',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.getUserByEmail();
        } else {
          RMSrv.dialogConfirm(ret.e)
        }
       
      });
    },
    removeRealtorFollowers() {
      var body = {eml:this.userEml};
      fetchData('/1.5/removeRealtorFollowers',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.getUserByEmail();
        } else {
          RMSrv.dialogConfirm(ret.e)
        }
       
      });
    },
    assign2NewRealtor() {
      var body = {eml:this.userEml,realtorEml:this.realtorEml};
      fetchData('/1.5/assignFollowers2NewRealtor',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogConfirm(err.toString());
        }
        if (ret.ok) {
          this.getUserByEmail();
        } else {
          RMSrv.dialogConfirm(ret.e)
        }
      });
    }
  }
}
var app = Vue.createApp(userFollow);
trans.install(app,{ref:Vue.ref})
app.mount('#user-follow');