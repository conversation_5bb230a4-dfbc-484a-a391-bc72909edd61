var proxyEmail = {
  data() {
    return {
      proxyEmails: [],
      count: 0,
      newItem: "",
    };
  },
  mounted() {
    let self = this;
    let list = decodeJsonString2Obj('list','array');
    if(list.length){
      self.proxyEmails = list;
      self.count = list.length;
    }
    self.$getTranslate(this);
  },
  methods: {
    addItem: function () {
      if (this.proxyEmails.includes(this.newItem)) {
        RMSrv.dialogAlert("Existed email domain");
      } else {
        this.proxyEmails.push(this.newItem);
        this.newItem = "";
        this.save();
      }
    },
    saveItem: function (index) {
      this.save();
    },
    deleteItem: function (index) {
      this.proxyEmails.splice(index, 1);
      this.save();
    },
    reload() {
      fetchData(
        "/sys/proxyemail",
        { body: { action: "reload" } },
        (err, ret) => {
          if (err) {
            RMSrv.dialogAlert(err.toString());
          }
          if (ret.ok) {
            this.proxyEmails = ret.list || [];
            this.count = ret.list.length || 0;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        }
      );
    },
    save() {
      var body = {
        action: "edit",
        list: this.proxyEmails,
      };
      fetchData("/sys/proxyemail", { body }, (err, ret) => {
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        if (ret.ok) {
          this.proxyEmails = ret.list || [];
          this.count = ret.list.length || 0;
        } else {
          RMSrv.dialogAlert(ret.err);
        }
      });
    },
  },
};
var app = Vue.createApp(proxyEmail);
// app.mixin(pageDataMixins);
trans.install(app, { ref: Vue.ref });
app.mount("#proxy-email");
