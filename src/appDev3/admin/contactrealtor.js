var userFollowup = {
  data() {
    return {
      isWorking:isWorking || false,
      status: status || '',
      eml:'',
      users:[],
      picker:null
    };
  },
  mounted() {
    this.$getTranslate(this);
    this.getUserByEmail();
  },
  methods:{
    toggleWorking() {
      this.isWorking = !this.isWorking;
      var body = {isWorking:this.isWorking};
      fetchData('/1.5/contactRealtor/updateSysdata',{body},(err,ret)=>{
        if (ret.ok) {
          this.status = ret.status;
        }
      });
    },
    setFormInputAction(formInput,crStat) {
      var body = {id:formInput._id,crStat}
      fetchData('/1.5/contactRealtor/updateCrmUserStatus',{body},(err,ret)=>{
        var errMsg = err || ret.e;
        if (errMsg) {
          RMSrv.dialogAlert(errMsg.toString());
          return;
        }
        if (ret.ok) {
          this.getUserByEmail();
        }
      });
    },
    setFormInputActive(formInput){
      var users = this.users;
      users.forEach(formInput => {
        formInput.active = 0;
      });
      formInput.active = 1;
    },
    getUserByEmail() {
      var body = {};
      if (this.eml) {
        body.eml = this.eml;
      }
      fetchData('/1.5/contactRealtor/getCrmUsers',{body},(err,ret)=>{
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        if (ret.ok && ret.users) {
          this.users = ret.users;
        }
      });
    }
  }
}

var app = Vue.createApp(userFollowup);
trans.install(app,{ref:Vue.ref})
app.mount('#user-followup');