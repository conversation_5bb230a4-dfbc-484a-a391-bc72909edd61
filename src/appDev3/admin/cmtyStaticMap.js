var bboxContainer = document.getElementById('bbox');
var zoomContainer = document.getElementById('zoom');
var gAllImagesContainer = document.getElementById('gAllImages');
var gCurrentImageContainer = document.getElementById('gCurrentImage');
var cityInput = document.getElementById('city');
var provInput = document.getElementById('prov');
var statUname = document.getElementById('stat_uname');
var boundary = document.getElementById('boundary');
var isGenAllCmtyImages = false;
var targetCol = 'stat_uname';
gAllImagesContainer.addEventListener('click',()=>{
  isGenAllCmtyImages = true;
  genCmtyImage();
});
statUname.addEventListener('click',()=>{
  targetCol = 'stat_uname';
  statUname.className = 'selected';
  boundary.className = '';
});
boundary.addEventListener('click',()=>{
  targetCol = 'boundary';
  boundary.className = 'selected';
  statUname.className = '';
});
gCurrentImageContainer.addEventListener('click',()=>{
  genCmtyImage();
});
var newBbox,zoom,skip = null;
var cmty = decodeJsonString2Obj('cmty');
var mapbox = new Mapbox();
mapbox.init('mapbox-container');
renderCmtyOnMap(cmty);

mapbox.map.on('zoom',function() {
  newBbox = convertBounds2Bbox(mapbox.map.getBounds());
  bboxContainer.innerText = `Bbox: ${newBbox}`;
});

function renderCmtyOnMap(cmty) {
  if (cmty) {
    mapbox.map.on('load', function() {
      var bnds = cmty.bnds;
      if (bnds) {
        drawBoundaryOnMap(bnds);
      }
    });
    if (isGenAllCmtyImages) {
      var bnds = cmty.bnds;
      if (bnds) {
        drawBoundaryOnMap(bnds);
      }
    }
    var bbox = cmty.bbox;
    if (bbox) {
      var cmtyBounds = new mapboxgl.LngLatBounds(new mapboxgl.LngLat(bbox[0], bbox[1]), new mapboxgl.LngLat(bbox[2], bbox[3]));
      mapbox.map.fitBounds(cmtyBounds,{duration: 0,padding: 20});
      newBbox = convertBounds2Bbox(mapbox.map.getBounds());
      bboxContainer.innerText = `Bbox: ${newBbox}`;
      zoom = mapbox.map.getZoom();
      zoomContainer.innerText = `Zoom: ${zoom}`;
    }
    if (isGenAllCmtyImages) {
      genCmtyImage();
    }
  }
}

function drawBoundaryOnMap(bnds) {
  var multi_latlngs = [];
  bnds.features.forEach(f => {
    f.geometry.coordinates.forEach(c => {
      // Start fresh coordinates array for each feature
      var latlngs = [];

      c.forEach(loc => {
        if (f.geometry.type == 'MultiPolygon') {
          loc.forEach(l=>{
            latlngs.push([l[0],l[1]]);
          });
        } else {
          latlngs.push([loc[0],loc[1]]);
        }
      });
      multi_latlngs.push(latlngs)
    });
  });
  if (mapbox.map.getLayer('route')) {
    mapbox.map.removeLayer('route');
  }
  if (mapbox.map.getSource('route')) {
    mapbox.map.removeSource('route');
  }
  mapbox.map.addSource('route', {
    'type': 'geojson',
    'data': {
      'type': 'Feature',
      'properties': {},
      'geometry': {
        'type': 'MultiLineString',
        'coordinates': multi_latlngs
      }
    }
  });
  mapbox.map.addLayer({
    'id': 'route',
    'type': 'line',
    'source': 'route',
    'layout': {
      'line-join': 'round',
      'line-cap': 'round'
    },
    'paint': {
      'line-color': '#ff2600',
      'line-width': 3
    }
  });
}

function convertBounds2Bbox(bbox) {
  var sw = bbox.getSouthWest().toArray();
  var ne = bbox.getNorthEast().toArray();
  return [sw[0],sw[1],ne[0],ne[1]];
}

function genCmtyImage() {
  if (skip == null) {
    skip = 0;
  }
  if (isGenAllCmtyImages) {
    skip += 1;
  }
  body = {id:cmty._id,skip,targetCol};
  if (newBbox) {
    body.bbox = newBbox;
  }
  if (zoom) {
    body.zoom = zoom;
  }
  if (!isGenAllCmtyImages) {
    if (cityInput.value) {
      body.city = cityInput.value;
    }
    if (provInput.value) {
      body.prov = provInput.value;
    }
  }
  fetchData('/cmty/cmtyStaticMap',{body},(error,res) => {
    if (error) {
      alert(error);
      return
    }
    if (isGenAllCmtyImages && res && res.cmty) {
      cmty = res.cmty;
      setTimeout(() => {
        renderCmtyOnMap(cmty);
      }, 500);
    }
  });
}