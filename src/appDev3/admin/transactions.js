var page = 0
function renderList(l) {
  for (var idx = 0; idx < l.length; idx++) {
    var i = l[idx];
    var className = i.passed ? 'warn' : '';
    var elem = `<li class='table-view-cell record ${className}'><div class='idx'>${idx}</div>`;
    for (var j = 0; j < Object.keys(i).length; j++) {
      var key = Object.keys(i)[j];
      var val = i[key];
      if (curType == 'billing' && key == 'charge') {
        val = i[key].amount || i[key].amount_total;
      }
      if (curType == 'bookkeeping' && key == 'charge' && typeof(i[key]) == 'object') {
        val = i[key].amount_total;
      }
      elem += `<div><label>${key}</label><span>${val}</span></div>`;
    }
    elem += '</li>'
    $('.table-view').append(elem);
  }
}
function getTransactions() {
  data = {page,type:curType}
  var eml = $('#eml').val().trim();
  if (eml) {
    data.eml = eml;
  }
  if (page === 0) {
    $('.table-view').html('');
  }
  $.post('/sys/tatransactions',data,function(ret) {
    if (ret.err) {
      alert(ret.err);
      return
    }
    page += 1;
    renderList(ret.l);
  });
}
getTransactions();
$('.btn-primary').on('click',function(e) {
  getTransactions();
});
$('.control-item').on('click',function(e) {
  $('.note').hide();
  curType = $(this).attr('id');
  $('.'+curType).show();
  page = 0;
  getTransactions();
});
$('#eml-search').on('click',function(e) {
  page = 0;
  getTransactions();
});
$('#eml-clear').on('click',function(e) {
  $('#eml').val('');
});
$('.icon-search').on('click',function(e) {
  $('.input-container').toggleClass('active');
});
$('#go-to-top').on('click',function(e) {
  $('html,body').animate({scrollTop: 0}, 1000);
})
$(window).scroll(function() {
  var btn = $('#go-to-top');
  if ($(window).scrollTop() > 300) {
    btn.addClass('show');
  } else {
    btn.removeClass('show');
  }
});