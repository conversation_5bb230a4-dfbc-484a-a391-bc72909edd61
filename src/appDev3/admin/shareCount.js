var shareCount = {
  data() {
    return {
      types:['all'],
      shareCount:0,
      shareInfos:[],
      selectedType:'all',
      selectedEmail:null,
      hasMore:false,
      // page:0,
      limit:20,
      dispVar:{
        isAdmin:false
      },
      datas:[
        'isAdmin'
      ]
    };
  },
  mounted() {
    let self = this,bus = window.bus;
    var types = decodeJsonString2Obj('types','array');
    if(types){
      self.types = self.types.concat(types)
    }
    var docs = decodeJsonString2Obj('docs','array');
    if(docs.length){
      self.shareInfos = docs
    }
    self.shareCount = document.querySelector('#count').innerHTML;
    self.hasMore = document.querySelector('#hasMore').innerHTML === 'true';
    self.$getTranslate(this);
    self.getPageData(self.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    })
  },
  methods:{
    getShareInfos(val) {
      let self = this;
      fetchData('/s/share',{body:val},(err,ret)=>{
        if (err) {
          RMSrv.dialogAlert(err.toString());
        }
        if(ret.ok){
          if(val.needCount){
            self.shareCount = ret.result.count
          }
          self.shareInfos = self.shareInfos.concat(ret.result.docs)
          self.hasMore = ret.result.hasMore
        }else{
          RMSrv.dialogAlert(ret.err);
        }
      });
    },
    filterByType() {
      this.page = 0;
      this.shareInfos = []
      var body = {page:this.page,limit:this.limit,needCount:true}
      if(this.selectedType !== 'all'){
        body.type = this.selectedType
      }
      if(this.selectedEmail){
        body.eml = this.selectedEmail
      }
      this.getShareInfos(body)
    },
    getNextPageData() {
      // this.page += 1;
      var lastItem = this.shareInfos.slice(-1)[0]
      var body = {
        // page:this.page,
        limit:this.limit,
        ts:lastItem.ts,
        needCount:false
      }
      if(this.selectedType !== 'all'){
        body.type = this.selectedType
      }
      if(this.selectedEmail){
        body.eml = this.selectedEmail
      }
      this.getShareInfos(body)
    },
    openDetail:function(data) {
      var url = ''
      if(data.tp === 'wecard'){
        url = '/1.5/wecard/prop/'+data.uid+'/'+data.id
      }else{
        url = '/1.5/prop/detail/inapp?id='+data.id;
      }
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: this.$_('RealMaster')}, function(val) {
        if (val == ':cancel') {
          return;
        }
      });
    },
    openUserInfo:function(user) {
      const url = '/1.5/wesite/'+user._id+'?inFrame=1';
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: this.$_('RealMaster')}, function(val) {
        if (val == ':cancel') {
          return;
        }
      });
    },
  }
}
var app = Vue.createApp(shareCount);
app.mixin(pageDataMixins);
trans.install(app,{ref:Vue.ref})
app.mount('#share-count');