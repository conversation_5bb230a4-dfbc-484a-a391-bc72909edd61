{"version": "1.5.0", "private": true, "description": "see website", "scripts": {"build:dev": "export NODE_OPTIONS=--openssl-legacy-provider && webpack --config ./webpack/dev.config.js --mode development", "build": "export NODE_OPTIONS=--openssl-legacy-provider && webpack --config ./webpack/prod.config.js --mode production", "build:server": "export NODE_OPTIONS=--openssl-legacy-provider && webpack --config ./webpack/server.config.js --mode development", "sass:watch:web": "sass -w style/sass/web/pages:webroot/public/css/web/", "sass:build:web": "sass --no-source-map --style=compressed style/sass/web/pages:webroot/public/css/web/", "sass:watch:app": "sass -w style/sass/apps/pages:webroot/public/css/apps/", "sass:build:app": "sass --no-source-map --style=compressed style/sass/apps/pages:webroot/public/css/apps/", "react:watch": "npx babel --watch react_components --out-dir webroot/public/js/react_components", "test": "mocha", "coverage": "npx nyc npm run test"}, "author": {"name": "Rain", "email": "<EMAIL>", "url": "http://www.openshift.com/"}, "homepage": "http://www.openshift.com/", "repository": {"type": "git", "url": "https://bitbucket.org/fredxiang/realmaster-appweb.git"}, "engines": {"node": ">= 8.12.0", "npm": ">= 1.0.0"}, "dependencies": {"@aternus/csv-to-xlsx": "^1.0.17", "@aws-sdk/client-s3": "^3.456.0", "@aws-sdk/client-ses": "^3.775.0", "@azure-rest/ai-translation-text": "^1.0.0-beta.1", "@devmehq/email-validator-js": "^1.0.19", "@elastic/elasticsearch": "^8.1.0", "@google/generative-ai": "^0.17.0", "apn": "^2.2.0", "apple-signin-auth": "^1.4.1", "arangojs": "^5.8.0", "arcgis-pbf-parser": "^0.0.4", "async": "^2.6.3", "aws-sdk": "^2.858.0", "axios": "^1.6.2", "coffeekup": "^0.3.1", "colors": "^1.4.0", "crypto": "^1.0.1", "csv": "^5.3.1", "csvtojson": "^1.1.12", "date-utils": "*", "dot": "^1.1.3", "emoji-regex": "^10.3.0", "event-stream": "^4.0.1", "fast-csv": "^4.3.6", "fast-stats": "0.0.3", "fcm-v1-http2": "^1.0.12", "formidable": "^1.2.2", "geetest": "^4.0.0", "geolib": "^3.3.4", "geolite2-redist": "^3.0.2", "gm": "^1.23.1", "googleapis": "^51.0.0", "html-minifier": "^4.0.0", "html-to-text": "^7.0.0", "jade": "~1.6.0", "js-yaml": "^3.14.1", "jsdom": "^9.9.1", "jsonwebtoken": "^8.5.1", "jwt-simple": "^0.5.6", "kafkajs": "^2.1.0", "libphonenumber-js": "^1.9.12", "line-by-line": "^0.1.6", "lz-string": "^1.4.4", "maxmind": "^4.3.6", "mime": "^2.5.2", "mocha": "^10.3.0", "moment-timezone": "^0.5.33", "mongodb": "^6.16.0", "mustache": "^4.2.0", "mysql": "^2.17.1", "nanoid": "^3.1.20", "nimble": "*", "node-rets": "github:fxfred/node-rets#master", "node-uuid": "*", "nodemailer": "^2.3.2", "nodemailer-html-to-text": "^3.1.0", "nodemailer-sendmail-transport": "^1.0.0", "nyc": "^15.1.0", "object-path": "^0.11.8", "openai": "^4.56.0", "openid": "^2.0.7", "p-limit": "^3.1.0", "pg": "^8.5.1", "proj4": "^2.11.0", "promise": "^8.1.0", "prompt": "^1.1.0", "ps-node": "^0.1.6", "redis-sessions": "^2.1.0", "regression": "^1.4.0", "sass-loader": "^10.1.1", "serialize-javascript": "^5.0.1", "should": "^13.2.3", "simplify-geojson": "^1.0.4", "sinon": "^18.0.0", "sns-validator": "^0.3.4", "socket.io": "^2.4.1", "sprintf-js": "^1.0.3", "stripe": "^8.222.0", "stylus": "^0.54.8", "superagent": "^6.1.0", "supertest": "^6.3.3", "terser-webpack-plugin": "^2.3.8", "text-to-svg": "^3.1.5", "toml": "github:fxfred/toml#latest", "twilio": "^5.2.2", "typescript": "^4.2.3", "uglify-js": "^2.8.29", "uglifycss": "^0.0.29", "uuid": "^8.3.2", "validator": "^13.7.0", "vee-validate": "^2.2.15", "vue-resource": "^1.5.3", "vue-server-renderer": "2.6.14", "vuedraggable": "^2.24.3", "wind-dom": "0.0.3", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "yargs": "^15.4.1"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.16.7", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^8.2.2", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-runtime": "^6.26.0", "cheerio": "^1.0.0-rc.10", "coffee-loader": "^0.9.0", "coffeescript": "2.7.0", "css-loader": "^1.0.1", "email-misspelled": "^3.4.2", "eslint": "^3.2", "file-loader": "^0.9.0", "html-webpack-plugin": "^3.2.0", "JSONStream": "^1.3.5", "less": "^3.13.1", "less-loader": "^4.1.0", "parse5": "^2.2.3", "postcss-loader": "^3.0.0", "pug": "^3.0.2", "pug-plain-loader": "^1.1.0", "sass": "^1.55.0", "sharp": "^0.29.3", "terser": "^5.20.0", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^3.0.0", "vue": "2.6.14", "vue-hot-reload-api": "^1.3.3", "vue-html-loader": "^1.2.4", "vue-loader": "^15.9.6", "vue-style-loader": "^1.0.0", "vue-template-compiler": "2.6.14", "vueify": "9.4.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "webpack-merge": "^4.2.2"}, "main": "none.js"}