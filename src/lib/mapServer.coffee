
# Usage:
# mapServer = INCLUDE 'lib.mapServer'

debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()

GOOGLE_API_LINKS = {
  # staticMapCn:'https://ditu.google.cn/maps/api/staticmap'
  staticMap:'https://maps.googleapis.com/maps/api/staticmap'
  # geocodeCn:'https://ditu.google.cn/maps/api/geocode/json'
  geocode:'https://maps.googleapis.com/maps/api/geocode/json'
  # mapsApiCn:'https://ditu.google.cn/maps/api/js'
  mapsApi:'https://maps.googleapis.com/maps/api/js'
  autoComplete:'https://maps.googleapis.com/maps/api/place/autocomplete/json'
  # autoCompleteCn:'http://ditu.googleapis.cn/maps/api/place/autocomplete/json'
  # placeApi:'https://maps.googleapis.com/maps/api/place/details/json'
  streetViewMeta:'https://maps.googleapis.com/maps/api/streetview/metadata?size=600x352&heading=270',
  streetView:'https://maps.googleapis.com/maps/api/streetview?size=600x352&heading=270'
}

# GOOGLE_API_KEY = 'AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA'
# GOOGLE_API_KEY_HOUSEMAX = 'AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU'
# GOOGLE_API_KEYS = []

appendKeyValueToUrl=(url,k,v)->
  unless v?
    return url
  url += if url.indexOf('?') > 0 then '&' else '?'
  url += k+'='+v
  return url

# exports.getGoogleAPIKey = getGoogleAPIKey=({})->
#   unless GOOGLE_API_KEYS.length
#     return GOOGLE_API_KEY
#   googleApiKeyStat.vc += 1
#   isApp = req.getDevType() is 'app'
#   if opts.src is 'map'
#     googleApiKeyStat.vcmap += 1
#   else if opts.src is 'mapmix'
#     googleApiKeyStat.vcmix += 1
#     googleApiKeyStat.vcmixapp += 1 if isApp
#   else
#     googleApiKeyStat['vc'+opts.src] ?= 0
#     googleApiKeyStat['vc'+opts.src] += 1
#   if isApp
#     googleApiKeyStat.vcapp += 1
#   return GOOGLE_API_KEYS[googleApiKeyStat.vc%2]
DEFATULT_APP_CONFIG={google:null,mapbox:null,azureGeocoder:null}

# _config is auto called in coffeemate3 and loader
# do not need to setup every time.
module.exports._config_sections = ['google','mapbox','here','mapquest','azureGeocoder','serverBase']
exports.init = exports._config = (appConfig)->
  # do not log server config into logs
  # debug.info 'set mapserver config', appConfig
  DEFATULT_APP_CONFIG=appConfig

exports.getGoogleAPILink = getGoogleAPILink=({
  googleConfig=DEFATULT_APP_CONFIG.google,
  tp='error',
  isChina=false,
  isKey=false,
  lib='geometry',
  lang='en',
  sensor=false,
},cb
)->
#   if src = req.param 'src'
#     opts.src = src
#   tp = opts.tp or 'error'
#   isCn = opts.cn #bool
#   isKey = opts.key #bool
#   cb = opts.cb
#   lib = opts.lib or 'geometry'
#   lang = opts.lang #en/zh
#   sensor = if opts.sensor then true else false
  # console.log 'googleConfig',DEFATULT_APP_CONFIG,googleConfig
  return null unless googleConfig
  return null unless tp in ['staticMap','geocode','googleapi','autocomplete','placeApi','streetViewMeta','streetView']
  url = ''
  #TODO:@rain confirm static map is used?
  if tp is 'staticMap'
  #TODO: add key?
    if isChina
      url = GOOGLE_API_LINKS.staticMapCn
    else
      url = GOOGLE_API_LINKS.staticMap
    url = appendKeyValueToUrl(url, 'key', googleConfig.browser)
    return url

  else if tp is 'geocode'
    url = GOOGLE_API_LINKS.geocode
    url = appendKeyValueToUrl(url,'language',lang)
    url = appendKeyValueToUrl(url, 'key', googleConfig.geocoderServer)
    return url
  else if tp is 'autocomplete'
    url = GOOGLE_API_LINKS.autoComplete
    url = appendKeyValueToUrl(url, 'key', googleConfig.serverAutocomplete)
    url = appendKeyValueToUrl(url,'language',lang)
    url = appendKeyValueToUrl(url, 'components', 'country:ca')
    return url
  # else if tp is 'placeApi'
  #   url = GOOGLE_API_LINKS.placeApi
  #   url = appendKeyValueToUrl(url, 'key', googleConfig.autoComplete)
  #   fields = 'address_component,formatted_address,geometry,icon,id,name,photo,place_id'
  #   url = appendKeyValueToUrl(url, 'fields', fields)
  #   return url
  else if tp is 'streetViewMeta'
    url = GOOGLE_API_LINKS.streetViewMeta
    # url = appendKeyValueToUrl(url, 'key', googleConfig.browser)
    # return url
  else if tp is 'streetView'
    url = GOOGLE_API_LINKS.streetView
    # url = appendKeyValueToUrl(url, 'key', googleConfig.browser)
    # return url
  else
    # if isChina
    #   url = GOOGLE_API_LINKS.mapsApiCn
    # else
    url = GOOGLE_API_LINKS.mapsApi
    # if opts.src isnt 'map'
    # if key is 'housemax' # housemax is already changed to mapbox
    #   url = appendKeyValueToUrl(url, 'key', googleConfig.housemax)
    # else
    url = appendKeyValueToUrl(url, 'key', googleConfig.browser)
    url = appendKeyValueToUrl(url,'language',lang)
    url = appendKeyValueToUrl(url,'libraries',lib)
    url = appendKeyValueToUrl(url,'sensor',sensor)
    url = appendKeyValueToUrl(url,'callback',cb) if cb
    # debug.debug 'mapServer getGoogleAPILink',url
    return url

exports.getMapboxStaticImageUrl = (body,isApp)->
  accessTokenPrefix = if '?' in body then '&' else '?'

  mapboxBaseUrl = 'https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/'
  "#{mapboxBaseUrl}#{body}#{accessTokenPrefix}access_token=#{getMapboxAPIKey({isApp})}"

exports.getMapboxAPIKey = getMapboxAPIKey = ({isHousemax,isApp,isAddress})->
  mapboxkeys = DEFATULT_APP_CONFIG.mapbox
  unless mapboxkeys
    debug.error 'Missing mapbox key in app_config'
    return
  {app,web,housemax,address} = mapboxkeys
  return address if isAddress
  return housemax if isHousemax
  return app if isApp
  return web

axios = require('axios')

# {
#   id: 'address.6286428040258044',
#   type: 'Feature',
#   place_type: [ 'address' ],
#   relevance: 1,
#   properties: { accuracy: 'point' },
#   text: 'Duignan Crescent',
#   place_name: '1234 Duignan Crescent, Milton, Ontario L9E 1C5, Canada',
#   center: [ -79.852416, 43.488284 ],
#   geometry: { type: 'Point', coordinates: [ -79.852416, 43.488284 ] },
#   address: '1234',
#   context: [
#     { id: 'postcode.7964166350176440', text: 'L9E 1C5' },
#     {
#       id: 'place.16919034735198620',
#       wikidata: 'Q425057',
#       text: 'Milton'
#     },
#     {
#       id: 'district.8547420609092360',
#       wikidata: 'Q14875161',
#       text: 'Halton County'
#     },
#     {
#       id: 'region.10598799396263190',
#       short_code: 'CA-ON',
#       wikidata: 'Q1904',
#       text: 'Ontario'
#     },
#     {
#       id: 'country.2057072853587150',
#       wikidata: 'Q16',
#       short_code: 'ca',
#       text: 'Canada'
#     }
#   ]
# }
# NOTE: lib/mapServer.coffee exports.getAddressSearchByMapbox 261 request mapbox url error,url:https://api.mapbox.com/geocoding/v5/mapbox.places/.json?country=CA&limit=10&language=en&proximity=-79.3503,43.7806&access_token=pk.XXXX,error: Request failed with status code 400
isValidAddr = (addr='')->
  return false unless addr
  addr = addr+''
  addr = addr.trim()
  return false if addr.length < 2
  return false if /^[^a-z0-9]/i.test(addr)
  return true
#https://docs.mapbox.com/api/search/geocoding/?utm_source=mapboxcom&utm_medium=pricing&utm_content=temporary-api#geocoding-response-object
exports.getAddressSearchByMapbox = ({addr,ip,lat,lng})->
  if not isValidAddr(addr)
    return []
  getAddr = (addr={})->
    if addr?.addr and not /\d+&/.test addr.addr
      return addr.addr
    return addr.place_name.split(',')[0]
  formatResult = (addr={})->
    ret = {
      fullAddr:addr.place_name,
      loc:addr.center,
      addr:getAddr(addr)
    }
    # TODO: more data
    if addr?.context?.length
      for i in addr.context
        if /postcode/.test i.id
          ret.zip =  i.text
        else if /place/.test i.id
          ret.city = i.text
        else if /district/.test i.id
          ret.area = i.text
        else if /region/.test i.id
          ret.prov = i.text
        else if /country/.test i.id
          ret.cnty = i.text
    ret
  limit = 10
  # bbox = [minLon,minLat,maxLon,maxLat]
  language = 'en'
  # proximity = longitude,latitude order, or the string ip to bias based on reverse IP lookup.
  proximity = 'ip'
  if lat? and lng?
    proximity = "#{lng},#{lat}"
  url = "https://api.mapbox.com/geocoding/v5/mapbox.places/#{encodeURIComponent(addr)}.json?country=CA&limit=#{limit}&language=#{language}&proximity=#{proximity}&access_token=#{getMapboxAPIKey({isAddress:true})}"
  # console.log '++++',url
  try
    response = await axios.get(url)
  catch err
    debug.error "request mapbox url error,url:#{url},error:",err.message
    throw err
  features = response?.data?.features or []
  suggestAddresses = []
  for addr in features
    # console.log '+++++',addr
    suggestAddresses.push formatResult(addr)
  return suggestAddresses
  
exports.getHereAPIKey = getHereAPIKey = ()->
  hereApiKey = DEFATULT_APP_CONFIG.here?.key
  debug.debug 'hereApiKey',hereApiKey
  unless hereApiKey
    debug.error 'Missing here key in app_config'
    return
  return hereApiKey

exports.getMapquestAPIKey = getMapquestAPIKey = ()->
  mapquestKey = DEFATULT_APP_CONFIG.mapquest?.key
  unless mapquestKey
    debug.error 'Missing mapquest key in app_config'
    return
  return mapquestKey

exports.getAzureAPIKey = getAzureAPIKey = ()->
  azureApiKey = DEFATULT_APP_CONFIG.azureGeocoder?.primaryKey or DEFATULT_APP_CONFIG.azureGeocoder?.secondaryKey
  debug.debug 'azureApiKey',azureApiKey
  unless azureApiKey
    debug.error 'Missing azure key in app_config'
    return
  return azureApiKey