### Session Handler
###
verbose = 3
crypto = require 'crypto'
util = require 'util'
debug = require('../lib/debug').getDebugger()
{ getPreDefinedColl } = require './mongo4'
{ genNanoId, isSearchEngine } = require './helpers'
uuidTimeout = 1000*60*60*24*30*3 #3个月
UUIDCol = null
###
  MemoryStore
    parameter: seconds for session to keep
###

class MemoryStore
  constructor: (@lasting)->
    @store = {}
    @_plus = @lasting * 1000
    setInterval @timeout , 60000
  set: (sid,val,cb)->
    @store[sid] = {v:val,'t': (+new Date() + @_plus) }
    if cb?
      cb()
  get: (sid,cb)->
    vv = @store[sid]
    t = +new Date()
    if vv? and vv.t < t
      delete @store[sid]
      vv = undefined
    if cb?
      cb(null,vv)
  save: (cb)->
    cb()
  destory: (sid,cb)->
    delete @store[sid]
    if cb?
      cb()
  timeout: =>
    t = +new Date()
    for k,v of @store
      if v.t < t
        delete @store[k]


###
  Session handler setup
    options:
      key: default to 'cmate.sid'
      store: session store instance ( must provide set(key,val,cb), get(key,cb), destory(cb))
      fn_id: identification generation function
      lasting: seconds for auto timeout.
      secret: secret string for hash
      ignore: ignored path, default '/favicon.ico' only
    methods:
      regenerate(cb(err)):
        to re-generate the ID
      destory(cb(err)):
        to destory contents for this session.
        And a new session will be created at next request.
      set(key,val,cb):
        set new value to key. if cb is provide, save will be called as well.
      get(key,default):
        get value from key
      save(cb):
        save value to stores
      id():
        get session id
      options:
        cookie option: domain, path, httpOnly, secure, maxAge

###
module.exports = (opt)->
  opt or= {}
  verbose = opt.verbose if opt.verbose?
  sessionKey = opt.key or 'cmate.sid'
  uuidKey = 'uuid'
  lasting = opt.lasting or opt.maxAge or (3 * 3600)
  store = opt.store or new MemoryStore(lasting)
  #if not store? or not store.set? or not store.get? or not store.destory? or ('function' isnt typeof store.get) or ('function' isnt typeof store.set) or ('function' isnt typeof store.destory)
  #	throw new Error "Session Store must provide 'set','get' and 'destory' function."
  fn_id = opt.fn_id or (req)-> (req.headers['user-agent'] or '')
  ignore = opt.ignore or ['favicon.ico']
  if store.createSid
    create_sid = (req,cb)->
      store.createSid req,(err,sid)->
        debug.debug "++++++ Create New Session: #{sid} #{req.url}" if verbose > 1
        cb err,sid
  else
    secret = opt.secret or 'cmate.secret_hash'
    hash = (base)->
      return crypto.createHmac('sha1',secret).update(base).digest('base64').replace(/[\+\=\*\$]/,'_')
    create_sid = (req,cb)->
      base = (+new Date).toString()
      return cb null,(base + hash(base + fn_id(req)))
  isValidSid = if store.isValidSid then store.isValidSid else (sid)-> sid?
  # dirtyList = {}
  # addDirty = (sess)->
  #   dirtyList[sess._sid] = sess
  #   process.nextTick ->
  #     for k,v of dirtyList
  #       v?.save ->
  # removeDirty = (sess)->
  #   delete dirtyList[sess._sid]
  class Session
    constructor: (req,@_sid,@_vals={})->
      @req = req
      @options = {maxAge:(lasting*1000),path:'/'}
      try
        req.resp?.cookie sessionKey,@_sid,@options
      catch err
        if req.logger
          req.logger.error err
        else
          debug.error err

    destroy: (sid,createNew,cb)=>
      if not cb?
        cb = createNew
        createNew = false
      if not cb?
        cb = sid
        sid = @_sid
      self = @
      debug.debug "+++++ Destory #{@_sid} #{@req.url}" if verbose > 1
      store.destroy sid,(err)->
        if err or (not createNew)
          cb err if cb
          return
        self._vals = {}
        create_sid self.req,(err,newSid)->
          self._sid = newSid
          self.save cb

    set: (key,val,cb)=> # session data is saved if callback fn is provided
      ret = @_vals[key]
      @_vals[key] = val
      @save (err,retv)->
        # BUG: 先return了再cb err 
        cb err,retv if cb
      return ret

    get: (key,default_val)=>
      return @_vals[key] or default_val

    save: (cb)=> # save to center storage
      if verbose > 2
        debug.debug "+++++ Session Save:"
        debug.debug @_vals
      store.set @_sid,@_vals,(err)->
        debug.debug err if err
        cb err if cb

    id: ()-> @_sid

    maxAge: (req,maxAge)->
      opt = {maxAge:maxAge,path:'/'}
      try
        req.resp?.cookie sessionKey,@_sid,opt
        @set 'maxAge',maxAge
        debug.debug "MaxAge #{maxAge}" if verbose > 2
      catch err
        if req.logger
          req.logger.error err
        else
          debug.error err

  class SessionWithUUID extends Session
    constructor:(req,sid,vals)->
      super req,sid,vals

    ###
     * Updates the UUID associated with the current session.
     * If a UUID is not provided, a new one is generated using `genNanoId(12)`. The UUID is then stored in a cookie with the key `uuidKey` and a max age of `uuidTimeout`.
     * If the request has a `user-agent` header, additional metadata is stored in the `update` object, including the `ts` (timestamp) and the `RMuserID` extracted from the `user-agent` string.
     * If the `UUIDCol` collection is defined, the `update` object is used to update or insert a document in the collection with the `_id` set to the UUID.
     * @param {Object} req - The HTTP request object.
     * @param {string} [uuid] - The UUID to use, if provided.
     * @returns {void}
     ###
    updateUUID:(req,uuid)->
      hasUUid = true
      try
        uuid ?= req.cookies?[uuidKey]
        # 验证UUID格式：应该是12位的nanoid（只包含0-9a-zA-Z）
        if uuid
          # 先解码UUID，处理URL编码的情况
          try
            uuid = decodeURIComponent(uuid)
          catch decodeErr
            debug.warn "Failed to decode UUID: #{uuid}"
          # 清理UUID，只保留有效字符（0-9a-zA-Z），并限制长度为12
          uuid = uuid.replace(/[^0-9a-zA-Z]/g, '').slice(0, 12)
          # 验证清理后的UUID是否符合格式要求
          if not /^[0-9a-zA-Z]{12}$/.test(uuid)
            debug.warn "Invalid UUID format detected after cleanup: #{uuid}, generating new one"
            uuid = null
        if not uuid
          uuid = genNanoId(12)
          hasUUid = false
          UUIDCol = getPreDefinedColl uuidKey
        req.resp?.cookie uuidKey,uuid,{maxAge:uuidTimeout,path:'/'}
      catch err
        if req.logger
          req.logger.error err
        else
          debug.error err
      return if hasUUid
      update = {}
      ua = req.headers['user-agent']
      if ua
        update.ua = ua
      if (ret = /RMuserID\/([0-9a-zA-Z\-])+/.exec ua) and (RMuserID = ret[0]?.split('/')?[1])
        update.RMuserID = RMuserID
      if UUIDCol and not isSearchEngine(req)
        try
          await UUIDCol.updateOne {_id:uuid},{$set:update,$setOnInsert:{ts:new Date()}},{upsert:true}
        catch err
          if req.logger
            req.logger.error err
          else
            debug.error err

    ###
     * Sets a new UUID for the current session.
     * This method removes the existing UUID cookie and then calls `updateUUID` to generate a new UUID and store it in a cookie.
     * @param {Object} req - The HTTP request object.
     * @returns {void}
    ###
    clearOldUUID:(req)->
      try
        req.resp?.cookie uuidKey, '', {path:'/',maxAge:0}
      catch err
        if req.logger
          req.logger.error err
        else
          debug.error err

  return (req,resp,next)->
    # 更新uuid过期时间
    try
      await req.session?.updateUUID(req)
    catch err
      debug.error err
    return next() if req.session? or (req.pathname? and req.pathname in ignore)
    debug.debug "+++++ Session pathname:#{req.pathname} not in #{ignore}" if verbose > 2
    createNewSession = ->
      create_sid req,(err,sid)->
        return next err if err
        req.session = new SessionWithUUID(req,sid)
        try
          await req.session?.updateUUID(req)
        catch err
          debug.error err
        return req.session.save next

    sid = req.cookies?[sessionKey]
    # util.debug "session++++++: " + util.inspect req.cookies
    if isValidSid sid # retrive session
      debug.debug "++++++ Valid Session ID: #{sid}" if verbose > 1
      return store.get sid,(err,val)->
        if err? or not val? # not found sessionKey
          debug.debug err if err
          if verbose and val
            debug.debug val
          return createNewSession()
        else
          req.session = new SessionWithUUID(req,sid,val)
          try
            await req.session?.updateUUID(req)
          catch err
            debug.error err
          return next()
    createNewSession()
