### Coffee Mate Module
  NOTE: TODO: tobe deprecated use coffeemate4

  Provide following interface
    include(module.name) :
      like require, but can include coffeescript file directly.
      also support 1 level namespace like separation.
      "lib.logger" will include 'logger' module from 'lib' library, whos path
      can be preset by configuration.

    site(name[,[parent,parent2...]])  :
      create, switch or extends Site object
      the existing one will become current Site, and Site object returned.
      Otherwise a new one is defined, becomes current and returned.
      name must be a string like 'Realor Buyer' or 'Sign-In Sign-Up'.
      return a Site object
    app(name[,app])     :
      application definition within site. return defined app object.
      if specified named app exists, the existing one will become current app
      and be returned. Otherwise a new one is defined, becomes current and returned.
      An app could belong to another app but not itself or its decendents.
      the url part will be matched with [upper-app.name + '/' + app.name].
      name should be a simple string like 'user','profile'.
      return an App object.
    get/post/put/del(param-helper,function)  :
      method processing definition.
      the url part will be matched with [app.regexp + '/' + method.param-helper].
      function accept 3 parameters as in express framework.
      return a RequestHandler object.
    def (key-name[,object-or-function]) :
      define a object or function for the key-name (string).
      The key-name-ed object/function is defined within the current Site/App or Global object.
      A tree structure storage will be searched from leaves to root to find the key-name-ed object/function,
       if object/function is not supplied.
      Returned the object/function.
    defa (key-name[,object-or-function]) :
      define an array member for the key-name (string).
      The key-name-ed array is defined within the current Site/App or Global object.
      A tree structure storage will be searched from leaves to root to find the key-name-ed array,
       if a member object/function is not supplied.
      Returned the array, which include the object/function as a member.
    defm (key-name[,map-key[,value]]) :
      define a map value for the key-name (string).
      The key-name-ed map(object) is defined within the current Site/App or Global object.
      A tree structure storage will be searched from leaves to root to find the key-name-ed object,
       if the value is not supplied.
      Returned the object, which include the value.

    layout([Site-or-app,]layout-name,function):
      layout definition. layout belongs to specified site or app or global if null.
      default to current app.
    view([site-or-app,]view-name,function)  :
      view definition. Used to define ckup template.
      view belongs to specified site or app or global if null.
      default to current app.
    css/less/stylus/js/coffee([site-or-app,] [name,],string/function):
      define a named css/less/javascript/coffee-script for site or app.
      default to current app/site.
      default name is '_', which will accumulate all definitions into one.
      Named definition will replace old one with new defintion.
    view/layout:
      in either view or layout, 'ckup view-name,context' can be used to draw a sub view in place
    response.ckup(view-name,context[,layout]) :
      render the view with specified context. Context will become 'this' to the view.
      site,app,_() will automatically be added to context if not exists.
      _() is for i18n purpose.
    layout,view:
      used by ckup(render) method, if specified layout/view is not found,upper level object will be inquired.
      ckup(render) function is provided as locals to view function, so one view can include/call another view to draw details.
      Object chain: App -> Other App -> Domain -> Global.
    db(dbname):
      get db object
    collection(dbname,collectioname):
      get collection object
    Site object:
      path(req): a string for the public directry for this hostname or default global.
      lang(req): default language for this hostname or default global.
    App object:
      get_site():
        get belonging site object.
      get_prefix():
        get upper level app object or undefined if no upper app.
    Global object is not directly accessable.

    filter is global scope object in coffeemate. Name must be unique.
      1. name, App/Site, option-object, fn    : register to App/Site a filter with options
      2. name, App/Site, fn                   : register to App/Site a filter without options
      3. name, App/Site, option-object        : register to App/Site a existing filter with options
      4. name, option-object, fn                : register to current App/Site a filter with options
      5. name, fn                               : register to current App/Site a filter or just add a filter as prototyp
      6. name, App/Site                       : register to App/Site a existing filter without options
      7. name, option-object                    : register to current App/Site a existing filter with options
      8. name                                   : register to current App/Site a existing filter without options
      name: an identification for the filter. filter is a globel object, cross sites.
      belong: a Site/App. Or current App or Site Object if not specified. Or Global if '_all' is specified.
      option: an option object for specified circumstance. Can not be Site/App or function or "_all"
      function accept following parameters:
        host name in lowercase
        parts: url path parts in array
        pathname: url path in a string without first '/'
        request
        response
        next function: call this one if filter pass, or return without calling this one.
        options: the option object
      if function not supplied, attach existing filter to Site/App/Handler

    Following instance will be added to request object.
      logger object. provide log,warn,error function

  Options:
    server: the Express server instance
    base: the base folder. last slash is not needed, but will be removed if has.
          default undefined, will throw error when relative path is ever used.
    lib:  library path defintion. An object to define all libraries, key:[path-to-lib].
          path-to-lib is string or string array.
    apps: application folders to be included and parsed. A string or string array.
          Absolute path start with "/" or relative path from base.

  TODO:
    Theme support:
      widget(name,(param,req,callback(err,string-result))): define widget
      req.widget / resp.widget (widget-name,param,callback(err,string-result))
      req.render / resp.render (view-name,param,callback(err,string-result))
      req.l10n(string)

    socket.io support

    auto reload apps
      site 1->n files
      app 1->n files
      handler 1->1 file
      root views,filters 1->1 file
      override old views,filters,handlers,app and site; gc to recycle them; auto rollback; keep 2 version
###

#require './nodepatch' if process.version.node < '0.12'
require 'date-utils'
fs = require 'fs'
cluster = require 'cluster'
EventEmitter = require('events').EventEmitter
#coffeekup = require 'coffeekup'
Url = require 'url'
childprocess = require 'child_process'
spawn = childprocess.spawn
querystring = require 'querystring'
os = require 'os'
vm = require 'vm'
Path = require 'path'
util = require 'util'
mime = require 'mime'
toml = require 'toml'
coffee = require 'coffeescript'
coffee.register() if coffee.register?
DModule = require './dmodule'
# InterNode = require '../inter.node/internode' # not used any more
MongoDb = null
ArangoDb = require './arangodb'
mate = exports
logger = console
compressor = require './compress'
coffeeless = require './coffeeless'
helpers = require './helpers'
nimble = require 'nimble'
Themes = require './Themes'
jade = require 'jade'
doT = require './dot'
# mustache = require 'mustache';
libDebug = require './debug'
msgStrings = require './msgStrings'
methodMap = null
verbose = 1
puts = debug = console.log
putsObj = (obj) -> console.log util.inspect obj
sprintf = null
vsprintf = null
hfilename = '_header.coffee'
ckup = null
i18n = require './i18n'
staticProvider = null
chinaip = null
proxyEmail = null
chinaMode = null
satelliteMode = null
developerMode = null
abbreviator = null
alertNotified = 0
gLibGeoip = null

_start_date = new Date()
_start_time = _start_date.getTime()
total_requests = 0
total_processed = 0
g_before_exit = null

# multifill
unless typeof(mime.lookup) is 'function'
  mime.lookup = (ext,defType)-> mime.getType(ext) or defType

###
  Emit Events:
    - ready_geoip
    - ready_base
    - ready_db
    - ready_apps
    - ready
    - exit
###
isCommonLanguage = (lang)->
  return lang in ['en','zh','zh-cn','jp','kr']

class CoffeeMate extends EventEmitter
  constructor: ()->
    super()
    @root =
      _defs: {supportMobile:true,supportDesktop:true}
      _filters: []
      _layouts: {}
      _views: {}
      fullname: -> '[Global]'
      get_view: (vname,is_l)->
        v = if is_l then @_layouts[vname] else @_views[vname]
        return null if not v?
        vt = typeof v
        return v if 'function' is vt or 'string' is vt or helpers.isArray(v)
        return v.tpl if v.pname is vname
        pv = @get_view v.pname,is_l
        nv = if helpers.isArray(pv) then pv.slice 0 else [pv]
        nv.push v.tpl
        if is_l then @_layouts[vname] = nv else @_views[vname] = nv
        nv
      add_filter: (fn,name,opt)->
        if verbose then puts "Filter Whole Site with '#{name}'"
        @_filters.push {fn:fn,name:name,opt:opt}
      def: (name,obj)->
        methodMap.add ('_DEF:'+name),(if obj then '*' else 'U'),'Root'
        if obj?
          @_defs[name] = obj
        @_defs[name]
    # dummy filter holder, to hold a filter for future use/assigned to a site/app or root.
    @filter_holder =
      _filters: {}
      add_filter: (fn,name,opt)->
        @_filters[name] = {fn:fn,name:name,opt:opt}
    @filters = {}

    @pre_processors = [] # to support express type of program and components
    @fallbacks = [] # to support fallback process. Only support global for now.
    @error_handlers = [] # to support express type of error handling
    @sites = {}

    @cur_site = null
    @cur_app = null

    @libcache = {_:{}}
    @locals =
      require: require
      global: global
      process: process
      module: module
    @context = {}
    @app_config = {}
    # NOTE: options -> rootConfig, 更清楚，变量也自说明了。support HEAD/OPTIONS/PATCH
    @_rootConfig = {}
    @_load_time = {}
    @_standard_functions = {}
    @msg_strings = msgStrings.newInstance()
    @l10n_ctx = msgStrings.newInstance()
    #@debug = require('./debug').debug()
    @puts = puts
    @putsObj = putsObj

  locals_setup: ->
    self = @
    # |inget|inreg|incall
    for name in '''site|dmname|app|themes|widget|get|options|post|put|del|
    delete|inner|route|error|fallback|layout|view|include|monitor|
    filter|mongodb|collection|transaction|getTransaction|arangodb|acollection|
    config|fullpath|model|def|defa|defm|sitedef|depend|provide|
    plswait|waitme|on|service|todo|css|less|stylus|js|coffee|
    hostrefresh|jade|dot|coffeejs|abbr|addplugin|socket|
    callBeforeExit|addStdFn|getStdFn|debug'''.replace(/\n|\s+/g,'').split '|'
      do (name)->
        self.locals[name.toUpperCase()] = ->
          self[name].apply self, arguments
    for name in 'msg_strings|debug|puts|putsObj|l10n_ctx'.split('|')
      do (name)->
        self.locals[name.toUpperCase()] = self[name]
    self.locals.DEBUG = libDebug.getDebugger
    @basic_parts_setup()
    null

  basic_parts_setup: ->
    @context.locals = @locals
    @context.setInterval = setInterval
    @context.setTimeout = setTimeout
    @context.clearTimeout = clearTimeout
    @context.console = console
    @context.Buffer = Buffer
    @context.Date = Date

  # socket.io setup
  socket: (namespace,fn)-> # TODO: this is low lever interface, shall provide higher level one
    if (not fn?) and ('function' is typeof namespace)
      fn = namespace
      namespace = '/'
    if 'function' isnt typeof fn
      throw new Error("Bad socket handler type #{typeof fn}")
    namespace = namespace?.toString() or '/'
    @_socketFuns ?= {}
    @_socketFuns[namespace] ?= []
    @_socketFuns[namespace].push fn

  socketConnected: (ns,socket)->
    self = @
    if not @_cookieParser?
      for p in @pre_processors
        if p._cmate_name is 'cookieParser'
          @_cookieParser = p
        else if p._cmate_name is 'session'
          @_sessionParser = p
        else if p._cmate_name is 'vhost'
          @_vhostHandler = p
    req = socket.request
    self._cookieParser req,null, ->
      self._sessionParser req,null,->
        url = Url.parse req.url,true
        req.host = url.host or req.headers['host']
        req.hostname = url.hostname or req.host.split(':')[0].toLowerCase()
        req.port = url.port
        self._vhostHandler req,null,->
          # setup l10n, ab, _, __, _ab
          req.l10n = ExtendedRequest.l10n
          req.isChinaIP = ExtendedRequest.isChinaIP
          req.locale = ExtendedRequest.locale
          req.getDevType = ExtendedRequest.getDevType
          setupL10n req
          if (fns = self._socketFuns[ns])?.length > 0
            for fn in fns
              fn socket,self._socket

  # # InterNode service: refer InterNode Documents
  # inget: ->
  #   ind = (@internode_client ?= InterNode.connect())
  #   #debug ind
  #   ind.get.apply ind,arguments
  # inreg: ->
  #   ind = (@internode_server ?= InterNode.service()) #connect())
  #   ind.reg.apply ind,arguments
  # # Shorthand function for calling function with only one parameter
  # #           and must callback
  # incall: (funame,param,cb)->
  #   @inget funame,(err,fn)->
  #     if err then return cb err
  #     fn param,cb
  # incal: (funame,timelimit,param,cb)->
  #   # TODO:

  debug: libDebug.getDebugger


  getConfig:(name)->
    configDic =
      'pushNotify': ['apn','serverBase','fcmv1_config']
      'sendSMS': ['twilio']
      'sendMail': ['contact', 'mailEngine', 'mailEngineList']
      'sendCall': ['twilio']
      'translator': ['i18n','serverBase']
      'promoTo3pty': ['p58','sqldbs']
    
    combinedConfig = {}
    for section in configDic[name]
      if @_rootConfig[section]
        combinedConfig[section] = @_rootConfig[section]
    return combinedConfig


  service: (name)->
    console.log '--service',name
    return fn if fn = @libFns?[name]
    # use pushNotifyV2 lib in pushNotify model
    if 0 <= ['sendSMS','sendMail','sendCall','translator','promoTo3pty'].indexOf name
      lib = @include "lib.#{name}"
      name2 = name.substr(0,1).toUpperCase() + name.substr(1)
      @libFns ?= {}
      # return @libFns[name] = lib['get' + name2] @_rootConfig,@
      return @libFns[name] = lib['get' + name2] @getConfig(name),@
    null

  alertHandler: (msg,restart,doNotCount)=>
    @_needReload msg if restart
    msg = os.hostname() + ':' + msg
    console.error msg
    return if developerMode and (alertNotified > 0)
    unless doNotCount
      if ++alertNotified is 10
        msg = 'Too many notifiction. Last one:' + msg
      return if alertNotified > 10 # do not send too many for the same instance
    if (not developerMode) and (not doNotCount) and (sendSMS = @service 'sendSMS')
      # Get phone numbers from config
      alertSMS = @app_config.alertSMS or '6472223733'
      
      # Convert to array if it's a comma-separated string
      phoneNumbers = if typeof alertSMS is 'string' then alertSMS.split(',').map((phone) -> phone.trim()).filter((phone) -> phone.length > 0) else alertSMS
      
      sms =
        to: phoneNumbers
        from: '+19056142609'
        body: msg
      sendSMS sms,(err)->
        if err then console.error err
    if (not developerMode) and (sendMail = @service 'sendMail')
      mail =
        _trans: 'smtp'
        engine: @_rootConfig.mailEngine.mailEngine or 'sendmail' # 'gmail'
        # NOTE: no from email will auto gen 'from' by engine
        from: "RM Alert <#{@app_config.defaultEmail or '<EMAIL>'}>"
        replyTo: "RM <#{@app_config.defaultRTEmail or '<EMAIL>'}>"
        to: @app_config.alertEmail or '<EMAIL>'
        subject: 'Service Alert'
        text: msg
        html: msg
        eventType: 'ServiceAlertEmail'
      sendMail.sendMail mail,(err,response)->
        if err then console.error err

  callBeforeExit: (fn)-> regExit fn

  # extending Request Object
  addplugin: (fnName,fn)->
    unless ('string' is typeof fnName) and ('function' is typeof fn)
      throw new Error('Bad Parameter')
    if ExtendedRequest[fnName]
      throw new Error("function already exists [#{fnName}]")
    ExtendedRequest[fnName] = fn


  # Standard function Purpose:
  #   1. decoupling sequential dependencies, like Provider must defined before consumers.
  #   2. provide statistics and monitoring services: TODO
  #   3. provide exception/error dealing services: TODO
  #   4. provide data cache services: TODO
  # standard function register
  # name: can use prefix:fnName as name string
  # opt: an object to specify needLogin, allowed(isAllow), needReq authentications
  #      needLogin: will fill input with 'user' object if not set
  #      needReq:   will fill input with 'req' object if not set
  #      allowed: string or string array. Will check if the request has permitted authorization
  #      noHTTP?:  do not allow to be called from Web directly.
  addStdFn: (fnName,opt,fn)->
    unless fn
      fn = opt
      opt = null
    unless ('string' is typeof fnName) and ('function' is typeof fn)
      throw new Error('Bad Parameter')
    @_standard_functions[fnName] = {fn:fn,opt:opt}
  # to search for specified functions
  # search for prefix:fnName first, then fnName only
  getStdFn: (fnName, prefix)->
    if prefix
      fnObj = @_standard_functions[prefix+':'+fnName]
    unless fnObj
      fnObj = @_standard_functions[fnName]
    if fnObj
      return Object.assign {},fnObj
    null

  # dependency commands
  clearReady: (name)-> DModule.clear name
  depend: -> DModule.depend.apply @,arguments
  provide: -> DModule.provide.apply @,arguments
  waitme: (id)->
    id ?= '_WaitMe_' + (+new Date())
    @plswait id
    id
  plswait: (resource_name)->
    waitlist = (@waitfor_list ?= {})
    waitlist[resource_name] = resource_name
    self = @
    if verbose > 1 then debug "Require resource #{resource_name}"
    DModule.depend resource_name,(err,handler)->
      if err
        puts "Startup Error: Depend on #{resource_name}, #{err}"
        throw err
      delete waitlist[resource_name]
      self._check_no_waiting_resource()
      null
  _check_no_waiting_resource: ->
    if not @waitfor_list? or Object.keys(@waitfor_list).length is 0
      delete @waitfor_list
      @provide '__app_ready','__app_ready'
    else if @waitfor_list?
      for n,cb of @waitfor_list
        puts "Waiting for #{n} before start up."
    null

  show_load_time: ->
    tt = 0
    ts = []
    for f,t of @_load_time
      tt += t
      ts.push [t,f]
    ts.sort (a,b)-> b[0] - a[0]
    for i in [0..10]
      if ts[i]
        debug "#{ts[i][1]} => #{ts[i][0]}"


  config: (sections) ->
    if not sections
      return Object.assign {}, @_rootConfig

    sections = [sections] unless Array.isArray(sections)
    combinedConfig = {}
    for section in sections
      if @_rootConfig[section]
        combinedConfig[section] = @_rootConfig[section]
    return Object.assign {},combinedConfig


  # database
  mongodb: (dbname)->
    db = new MongoDb.Database()
    db.db(dbname)

  collection: (dbname,collectionname)->
    db = new MongoDb.Database()
    if not collectionname?
      throw new Error('Need DB name for ' + dbname)
    db.db(dbname).coll(collectionname)
  
  startSession: (dbname,options)->
    db = new MongoDb.Database(dbname)
    db.startSession options
  transaction: (dbname,transactionOptions, fnTransaction)->
    db = new MongoDb.Database()
    db.db(dbname).transaction(transactionOptions, fnTransaction)
  getTransaction:(dbname,transactionOptions)->
    db = new MongoDb.Database()
    return db.db(dbname).getTransaction(transactionOptions)

  getConfigColl: ->
    @collection 'chome', 'sysdata'

  arangodb: (dbname)->
    ArangoDb.getDatabase dbname

  acollection: (dbname,collectioname)->
    ArangoDb.getCollection dbname,collectioname

  _set_lib: (lib)->
    if not @libcache['lib']? # add this dir as lib, if lib is not defined
      @libcache['lib'] = {_path: __dirname}
    if not @libcache['libapp']? # libapp dir for app specified libs
      @libcache['libapp'] = {_path: Path.join(Path.dirname(__dirname),'libapp')}
    if not @libcache['model']? # model dir for app specified libs
      @libcache['model'] = {_path: Path.join(Path.dirname(__dirname),'model')}

    self = @
    if @_rootConfig.geoCoderService?.geoip isnt false
      gLibGeoip = require './geoip'
      @plswait '__geoip'
      gLibGeoip.init (err)->
        if err
          self.alertHandler 'Geoip init Error, continue. '+err
        else
          # getIpCity是我们自己用geoip的lib，得到ip的geoinfo。
          # getIpCity那个现在基本用不了。lib安装和更新经常有问题
          ExtendedRequest.getIpCity = (ip)-> # may return null
            gLibGeoip.getIpCity ip or @remoteIP()
          self.emit 'ready_geoip',self
        self.provide '__geoip','__geoip'
        self.emit 'ready_base',self
    else
      self.emit 'ready_base',self

  _load_models: (path)->
    models = []
    fd = fs.openSync path,'r'
    stat = fs.fstatSync fd
    fs.closeSync fd
    if stat.isDirectory()
      files = fs.readdirSync path
      for f in files
        if (not /^\.|(unused|~)$/i.test f) and (not /\.(txt|md|swp|tmp|json)$/i.test f)
          f = path + '/' + f
          m = @_load_models f
          models = models.concat m if m?
    else if stat.isFile()
      if verbose > 1 then puts "Loading model file: #{path}"
      models = @include(path,false).models
    models

  _set_db: (opts)->
    _READY_DB = 'ready_db'
    self = @
    if opts?
      sqlDbSetup = (cb)->
        if opts.usePostgresql and (sqldbCfg = opts.sqldbs)
          console.log 'SQL DB Setup'
          sqlDBs = require './sqlDBs'
          console.log sqldbCfg if verbose
          sqlDBs.init sqldbCfg,(err)->
            if err
              console.error err
              throw err
            puts 'SQL DB Ready'
            cb()
        else
          cb()
      arangoSetup = (cb)->
        if opts.arango
          puts 'Arango DB Setup'
          ArangoDb.setup opts.arango,(err)->
            throw err if err
            puts 'Arango Ready'
            sqlDbSetup cb
        else
          sqlDbSetup cb
      mongodbSetup = (cb)->
        if opts.dbs
          for k,v of opts.dbs
            if v? and v.models? and 'string' is typeof v.models
              fm = @fullpath(v.models)
              if verbose then puts "Load Models from '#{fm}' for database: '#{k}'"
              v.models = @_load_models fm
              if verbose > 1 then puts " -- Models #{v.models.length}"
          # if opts.dbs.mongo4
          MongoDb = require './mongo4'
          # else
          #   MongoDb = require './mongo_db'
          MongoDb.setup opts, (err,db)->
            if err
              puts "MongoDb setup error: #{err}"
              return cb(err)
            MongoDb.setAlertHandler self.alertHandler
            if opts.preDefColls?
              MongoDb.set_predef_colls opts.preDefColls,(err)->
                if err
                  puts "set predefine colls error: #{err}"
                  return cb(err)
                self.provide 'mongodb',db
                arangoSetup cb
            else
              self.provide 'mongodb',db
              arangoSetup cb
        else
          arangoSetup cb
      mongodbSetup (err)->
        return self.exit 1 if err
        self.emit _READY_DB
    else
      puts 'No DB configuration. Running on no DB mode.'
      self.emit _READY_DB
    _READY_DB

  _set_i18n: (cfg,cb)->
    _load_i18n_resource = '_load_i18n_resource'
    self = @
    @plswait _load_i18n_resource
    @on 'ready_db',->
      console.log cfg
      for k,v of cfg
        if k in ['coll','cityColl']
          coll = new MongoDb.Database()
          if v.db then coll.db v.db
          coll.coll v.coll
          cfg[k] = coll
      i18n.init cfg,(err)->
        if err then throw err
        cb() if 'function' is typeof cb
        self.provide _load_i18n_resource
    _load_i18n_resource

  _set_chinaip: (cb)->
    _load_chinaip_resource = '_load_china_ip'
    self = @
    @plswait _load_chinaip_resource
    cfgColl = @getConfigColl()
    @on 'ready_db',->
      chinaip = self.include 'lib.chinaip'
      chinaip.init cfgColl,satelliteMode,(err)->
        if err then throw err
        cb() if cb?
        self.provide _load_chinaip_resource
    _load_chinaip_resource

  # NOTE: need to have db to use this
  _set_abbr: (options,cb)->
    cfg = options.i18n.abbr
    _load_abbr_resource = '_load_abbreviations'
    self = @
    @plswait _load_abbr_resource
    abbrfile = @fullpath cfg.file
    setupAbbr = ->
      abbr = self.include 'lib.abbreviate'
      abbr.init cfgColl,abbrfile,(err)->
        if err then throw err
        abbreviator = abbr
        cb() if cb?
        self.provide _load_abbr_resource
    if options.dbs
      cfgColl = @getConfigColl()
      @on 'ready_db',setupAbbr
    else
      setupAbbr()
    _load_abbr_resource

  _set_proxy_email: (cb)->
    _load_proxy_email = '_load_proxy_email'
    self = @
    @plswait _load_proxy_email
    cfgColl = @getConfigColl()
    @on 'ready_db',->
      proxyEmail = self.include 'lib.proxyEmail'
      try
        await proxyEmail.init cfgColl
      catch err
        logger.error err
        throw err
      cb() if cb
      self.provide _load_proxy_email
    _load_proxy_email

  abbr: -> abbreviator

  base_setup: (options,cb)->
    if options.serverBase?.verbose?
      verbose = options.serverBase.verbose

    @_rootConfig = options
    chinaMode = options.serverBase.chinaMode
    satelliteMode = options.serverBase.satelliteMode or chinaMode # chinaMode always imply satelliteMode
    developerMode = options.serverBase.developer_mode
    # NOTE: debugThreshhold and developerMode should not change log level!!
    libDebug.setDefaultThreshhold options.serverBase?.debugThreshhold or \
      (if developerMode then libDebug.DEBUG else libDebug.WARNING)
    # setup base, normalize it
    options.base = @_set_base options.serverBase.base # normalize base path

    # get http server
    @http = require 'http'

    @_set_lib options.lib
    toWaitFor = []
    if cb
      if toWaitFor?.length > 0
        puts "Waiting for #{toWaitFor.join(' ,')}"
        @depend toWaitFor,(err)->
          if err
            console.error err
            throw err
          cb()
      else
        @on 'ready_db',-> # db may ready fast, so call "on" before setup_db
          puts 'DB Ready'
          cb()
    @_set_db options
    if options.dbs
      toWaitFor.push @_set_chinaip()
      # really run after DB setup, add developer_mode to options.i18n
      toWaitFor.push @_set_i18n(Object.assign({},options.i18n,{developer_mode:options.serverBase?.developer_mode}))
      toWaitFor.push @_set_abbr options
      toWaitFor.push @_set_proxy_email()
    @locals_setup()
    {sprintf,vsprintf} = @include 'lib.sprintf'
    ckup = @include 'lib.ckup'

    # setup default 500 & 404
    # TODO: seperate product & development server
    @view 'E500',->
      text "Error 500: #{@error or @err}" # development error
    @view 'E404',->
      text "'#{@url}' is not found on the server #{@host}"
    @view 'E503',->
      text "Error 503: #{@error or @err}" # 503 error


    null

  final_setup: ->
    # extend ServerRequest & ServerResponse
    #util.debug util.inspect @http
    extend_prototype ExtendedRequest,@http.IncomingMessage #ServerRequest
    extend_prototype ExtendedResponse,@http.ServerResponse #OutgoingMessage
    extend_prototype ExtendedRequest,DummyRequest
    extend_prototype ExtendedResponse,DummyResponse

  session_setup: (opts)->
    self = @
    opt = if opts.session? then opts.session else opts
    sessionStore = @include 'lib.' + if opt.store then opt.store else 'kv_store_client'
    session = @include 'lib.session'
    store = new sessionStore(opt)
    store.error_handler = -> self.alertHandler 'Session Error'
    sessionOpt = helpers.shallowExtendObject {store:store},opt
    @use 'session',session sessionOpt
    #@use session {secret:opt.secret}
    _wait_for_session_ = 'session_store' # real store to provide this service
    @plswait _wait_for_session_
    # store.set _wait_for_session_ ,{},(err)->
    #   if err then throw err
    #   store.destroy _wait_for_session_,(err)->
    #     if err then throw err
    #     self.provide _wait_for_session_

  migrate: (options,cb)->
    if not (mig_path = options?.serverBase?.migration)?
      process.nextTick cb
      console.log 'No migrations'
      cb()
      return null
    self = @
    SysData = @getConfigColl()
    key = 'migrateDone'
    torun = null
    records = null

    # get all files
    mig_path = self.fullpath mig_path
    console.log "Migration Path: #{mig_path}"
    runone = ->
      if f = torun.shift()
        return process.nextTick runone if f.match(/^\.|^\_|~$/) or (records.indexOf(f) >= 0)
        # check not system files and not run yet files
        ff = Path.join mig_path,f
        fs.stat ff,(err,stat)->
          if stat?.isFile()
            console.log "Migration: #{f}"
            migrateProcess = self.run_file ff
            console.log migrateProcess
            doneOne = ->
              SysData.updateOne {_id:key},{$addToSet:{done:f}},{safe:true,upsert:true},(err)->
                if err then throw err
                process.nextTick runone
            return doneOne() if not migrateProcess?.run?
            migrateProcess.run (err)->
              if err
                console.error 'Migration Error:'
                console.error err
                throw err
              doneOne()
          else
            process.nextTick runone
      else
        return cb()
    fs.readdir mig_path,(err,files)->
      if err
        console.error 'Migration readdir Error:'
        throw err
      if not files then return cb()
      torun = files.sort()
      # get runned files
      SysData.findOne {_id:key},(err,runned)->
        if err
          console.error 'Migration findOne Error:'
          throw err
        records = runned?.done or []
        runone()

  apps_setup: (options,cb)->
    # vhost
    # move to pre_process
    # vhost = @include "lib.vhost"
    # @filter '_VHOST','_all',vhost(options)

    # application configuration
    @app_config = options or {}
    @app_config.verbose ?= options.serverBase?.verbose
    @app_config.developer_mode ?= (options.serverBase.developer_mode or false)
    @app_config.chinaMode = chinaMode
    @app_config.satelliteMode = satelliteMode
    @app_config.createRNIIndex = options.serverBase?.createRNIIndex #TODO: doesn't in config
    @app_config.masterMode = if satelliteMode then false else (options.serverBase?.masterMode or false)
    @app_config.base = options.base
    @app_config.mailEngine ?= options.mailEngine.mailEngine
    @app_config.alertEmail ?= options.contact.alertEmail or '<EMAIL>'
    @app_config.alertSMS   ?= options.contact.alertSMS or '6472223733'

    # load applications
    @_app_paths = options.apps.folders
    @_app_excludes = options.exclude_apps
    if verbose > 1 then puts "App Path: #{util.inspect @_app_paths}"
    if options.serverBase.headerfile? and 'string' is typeof options.serverBase.headerfile
      @headerfile = options.serverBase.headerfile
    if verbose > 1 then puts "App Header File: #{@headerfile}"
    self = @
    @load cb

  themes: (group)-> @_themes.getMetas group

  themes_setup: (options,cb)->
    if p = options?.serverBase?.themes
      self = @
      p = @fullpath p
      if verbose > 1 then puts "Theme loading from: #{p}"
      @_themes = Themes.load p,options.themesAutoReload,(err,t)->
        if err then return cb err
        self._themes = t
        if verbose > 1 then puts 'Theme loaded'
        puts t
        cb()
    else
      @_themes = {
        getTheme: ->
        getNames: ->
        getMetas: ->
        }
      cb()

  inExit: false
  exit: (e)->
    #process.kill process.pid,'SIGQUIT'
    if process.send?
      process.send {cmd:'exit',data:process.pid}
    process.exit e

  finishAll: (sig = 'Exit')->
    self = @
    finish_all = (err)->
      try
        if process.send?
          process.send {cmd:'exit',data:process.pid}
      catch e
        unless /^Error(\s\[ERR_IPC_CHANNEL_CLOSED\])?:\schannel\sclosed/i.test e.toString()
          console.log e
      if err? then process.exit 1
      process.exit 0
      puts 'Kill Self'
      process.kill process.pid,'SIGKILL'
    if self.inExit then return null
    self.inExit = true
    if cluster.isWorker
      puts "Exit worker #{cluster.worker.id}"
    else if self._rootConfig.server?.pid
      try
        fs.unlinkSync self._rootConfig.server.pid
      catch e
        puts e
    puts "Exit by #{sig}"
    try
      logger.log "Coffeemate Exit by #{sig}"
      self.emit 'exit'
    catch e
      console.error 'When exit error:'
      console.error e
    if g_before_exit?
      helpers.map g_before_exit,((fn,i,cb)->
        fn cb
        ),finish_all
    else
      finish_all()

  before_exit: (sig)->
    self = @
    process.on sig, -> self.finishAll()

  _restarting: 0
  _needReload: (err)->
    return if @_restarting
    @_restarting = 1
    self = @
    setTimeout (-> self._restarting = 0), 600000 * 3  # reset in 3 min, when restart failed
    process.send {cmd:'restart',err: err?.toString()}
    puts 'Restarting coffeemate'

  ready: ->
    self = @
    @before_exit 'SIGINT'
    @before_exit 'SIGQUIT'
    @before_exit 'SIGTERM'
    @before_exit 'exit'
    process.on 'message',(msg)->
      debug "Master message #{util.inspect msg}"
      switch msg?.cmd
        when 'stop'
          debug "Stopping #{process.pid}"
          stopped = false
          self._server.close ->
            if process.send? and not stopped
              process.send {cmd:'stopping'}
            debug "Stopped #{process.pid}"
          if @_reqlog?
            @_reqlog?.whenfinished ->
              debug "Finished #{process.pid}"
              self._server.destroy()
              if process.send?
                process.send {cmd:'stopped'}
              stopped = true
              self.finishAll()
          else
            setTimeout (->
              if process.send?
                process.send {cmd:'stopped'}
              stopped = true
              self.finishAll()
              ),1000
    # TODO close databases, sessions connections, files...
    process.on 'uncaughtException',(e)->
      if /IPC\schannel\sis\salready\sdisconnected/.test e.toString()
      # if e.toString() is 'Error: IPC channel is already disconnected'
        puts 'IPC disconnected'
        return
      logger.critical e
      logger.error e.stack
      logger.error "Uncaught Exception: #{e} url:#{e.url}\n Service Continue"
      return if e.code is 'ERR_STREAM_WRITE_AFTER_END'
      self.alertHandler 'Uncaught Exception:' + e.toString()

    @is_ready = true
    if verbose then @show_load_time()
    if verbose
      puts "Server starts to service requests. (Total starting time: #{Date.now() - _start_time}ms)"
    @emit 'ready',@
    if @waiting_requests
      for reqs in @waiting_requests
        @exec reqs.req, reqs.resp
      delete @waiting_requests
    null

  _set_base: (base)->
    if base?
      if typeof base is 'string'
        # remove trailing /
        base = base.substr(0,base.lentgh()-1) while base.charAt(base.length - 1) is '/'
        @base = base
        if verbose then puts "Base Dir: #{base}"
        return base
      else
        throw new Error("'base' needs to be a string or undefined.")

  load: (cb)->
    @load_apps @_app_paths,cb
  is_excluded_app: (path)->
    if @_app_excludes
      for excl in @_app_excludes
        return true if path is excl # path.indexOf(excl) is 0
    return false

  load_apps: (apps,cb)->
    # copy of orig data
    apps = apps.slice()
    if apps?
      switch typeof apps
        when 'string' then @load_app_from_path apps,'',cb
        when 'object' # really an array
          if helpers.isArray apps
            self = @
            doone = ->
              # NOTE: this modifies orig data
              if p = apps.shift()
                self.load_app_from_path self.fullpath(p),'',doone
              else
                cb()
            doone()
          else
            throw new Error('App path(s) needs to be a string or string array.')
        else
          if verbose > 1 then puts util.inspect apps
          throw new Error("App path(s) needs to be a string or string array. Got '#{typeof apps}'.")
    else
      cb()
    null
  # NOTE: loading files under /src/apps recursively
  load_app_from_path: (path, _header = '',cb)->
    if typeof path isnt 'string'
      throw new Error("path needs to be a string, but get '#{typeof path}'")
    # NOTE: ignore folder end with unused
    if /unused$/.test path
      return cb()
    # get _header file
    try
      hfilename = Path.join(path,@headerfile)
      puts "Try HEADER FILE #{hfilename}" if verbose > 1
      hfile = fs.readFileSync hfilename,'utf8'
      if hfile?
        _header += "\n#{hfile}"
    catch e
      if e.code isnt 'ENOENT' #errno isnt 9 and e.errno isnt 33 # v0.6.0 33, v0.5.10 32, v0.4.12 9
        puts e
    self = @
    fs.readdir path,(err,files)->
      if err
        console.error 'Error when reading folder: ' + path
        throw err
      if not files then return cb()
      files = files.sort()
      folders = []
      loadfolder = ->
        if f = folders.shift()
          self.load_app_from_path f, _header,(err)->
            if err then throw err
            process.nextTick loadfolder
        else
          cb()
      loadone = ->
        if f = files.shift()
          if (f.match /^\.|^\_|~$/) or (f is self.headerfile) or (self.is_excluded_app(f))
            process.nextTick loadone
          else
            f = path + '/' + f
            if self.is_excluded_app f
              return process.nextTick loadone
            fs.stat f,(err,stat)->
              if stat.isDirectory()
                folders.push f
              else if stat.isFile()
                self.run_file f, _header
              process.nextTick loadone
        else
          process.nextTick loadfolder
      loadone()
    null

  include: (module,in_module=true)->
    if 'string' isnt typeof module
      throw new Error("Module name shall be a string. Got #{typeof module}.")
    if in_module and (0 < (p = module.indexOf '.'))
      try
        k = module.substr 0,p
        module = module.substr p+1
      catch e
        if verbose > 1 then puts e
    k ?= '_'
    # NOTE: already verbose in _include
    # if verbose > 1 then puts "Loading #{k} #{module}"
    repo = @libcache[k]

    # return existing one
    return repo[module] if repo[module]?

    # load new module
    path = null
    if k isnt '_'
      path = repo['_path']
    m = @_include module,path
    if m?._config
      if m?._config_sections
        m._config @config(m._config_sections)
      else
        m._config @config()
    repo[module] = m

  _include: (fn,path)->
    stat = null
    fn = path + '/' + fn if path?
    try
      stat = fs.statSync(fn + '.coffee')
    catch e
    if stat? and stat.isFile() is true
      if verbose > 1 then puts "include: #{fn}.coffee"
      #console.log 'Including coffee ' + fn
      return require fn
      #vm = require 'vm'
      #f = fn + '.coffee'
      #code = @read_and_compile f
      #code = "r={exports:{}};with(r){#{code}};r.exports;"
      #return vm.runInThisContext(code,f)
    else
      if verbose > 1 then puts "include: #{fn}.js" #"include: #{Path.normalize (__dirname+'/'+fn)}"
      #console.log 'Including js ' + fn
      return require fn

  def: (key,obj)->
    if key? and 'string' is typeof key
      if obj isnt undefined
        belong = @cur_app or @cur_site or @root
        if verbose > 3 then puts "DEF #{key} for #{belong.fullname()}"
        return belong.def key,obj
      else
        unless ret = @cur_app?.def(key) or @cur_site?.def(key) or @root.def(key)
          throw new Error("UNDEFINED DEF: #{key}, check load sequence")
        return ret
    else
      throw new Error('key-name must be a string')
  ###*
  * get/set model. implementation is same as def.
  ###
  model: (key,obj)->
    @def key,obj

  defa: (key,obj)->
    if key? and 'string' is typeof key
      belong = @cur_app or @cur_site or @root
      ary = belong._defs?[key] or [] # use its own _defs
      if ary and not helpers.isArray ary
        ary = [ary]
      if obj?
        if verbose > 2 then puts "DEFA add #{key} for #{belong.classtype}:#{belong.name}"
        ary.push obj
        return belong.def key,ary
      else
        return ary[ary.length - 1]
    else
      throw new Error('key-name must be a string')

  defm: (key,name,val)->
    if key? and 'string' is typeof key
      belong = @cur_app or @cur_site or @root
      map = belong._defs?[key] or {} # use its own _defs
      if map and 'object' isnt typeof map
        throw new Error("Original value in DEF is not an object, but #{typeof map}")
      if name? and 'string' is typeof name
        if val?
          map[name] = val
          return belong.def key,map
        else
          return map[name]
      else
        throw new Error("key name must be a string. Got #{typeof name}")
    else
      throw new Error("object name must be a string. Got #{typeof key}")

  site: (name,parents)->
    if name?
      if not @cur_site? or @cur_site.name isnt name
        @cur_app = null
      if site = @sites[name]
        @cur_site = site
      else
        @cur_site = @sites[name] = new Site(name,@)
      if parents
        @cur_site.extend parents
    else if name is null # clear current site
      @cur_site = null
    # if *undefined*, just return current site
    @cur_site

  sitedef: (sitename,key)-> # Get site def
    return @sites[sitename]?.def(key)

  dmname: (name)->
    console.warn "DMNAME(#{name}) is deprecated"
    null
    ###
    if name?
      name = name.toLowerCase()
      @cur_app = null
      if verbose > 1 then puts "Use domain '#{name}'"
      if not @cur_site? then @site ''
      @cur_domain = @cur_site.domain name
    @cur_domain
    ###

  app: (name,prefix)->
    # prefix can be another app, false/null/undefined(top) or true(under current app)
    if not name?
      if prefix? then throw new Error("Can't create an APP without a name.")
      if name is null then @cur_app = null # set current to app to null
      # when called without parameters
      return @cur_app
    if name instanceof App
      @cur_app = name
      @cur_site = @cur_app.site
      return @cur_app
    name = name.toLowerCase()
    if not @cur_site?
      debug "APP(#{name}) has no SITE"
      @site '' # create/set current site, if not set yet.
    switch prefix
      when true
        prefix = @cur_app
      when false,null,undefined
        prefix = null
    @cur_app = @cur_site.ensureApp name,prefix

  widget: (name,fn)-> # define widget
    if not @cur_site?
      throw new Error('No site defined yet.')
    @cur_site.addWidget name,fn

  get:  (param,handler)-> @route 'GET', arguments
  post: (param,handler)-> @route 'POST', arguments
  options: (param,handler)-> @route 'OPTIONS', arguments
  put:  (param,handler)-> @route 'PUT', arguments
  del:  (param,handler)-> @route 'DELETE', arguments
  'delete':  (param,handler)-> @route 'DELETE', arguments
  inner: (param,handler)-> @route 'INNER', arguments # internal call only method

  coffeejs: (name,opt,fn)->
    if not fn?
      fn = opt
      opt = {}
    js = coffeeless.compileCoffee fn,opt
    now = new Date()
    bjs = new Buffer(js)
    size = bjs.length
    resp_headers =
      'Content-Type': 'text/javascript'
      'Content-Length': size
      'Cache-Control': 'public max-age=' + (opt?.session?.maxage or 3600000)
      'Last-Modified': now.toUTCString()
      'ETag': now.getTime() + '-' + size
    @get name,(req,resp)->
      for k,v of headers
        resp.setHeader k,v
      return resp.send bjs

  route: (verb, args) ->
    paramType = typeof(p0 = args[0])
    #puts "Route: #{verb} #{args[0]} #{paramType} #{p0 instanceof RegExp}"
    if (paramType is 'string') or helpers.isRegExp(p0)
      # route 'get','/path',(req,resp)->
      return @register_route verb.toUpperCase(), p0, args[1]
    else if paramType is 'function'  # route 'get',(req,resp)->
      return @register_route verb.toUpperCase(), p0
    else if paramType is 'object'  # route 'get',{path:(req,resp)-> , path2:(req,resp)->...}
      return (@register_route verb.toUpperCase(), k, v) for k, v of p0
    else
      puts verb
      putsObj args[0]
      throw new Error("Unknown route parameter type: #{paramType}")
    null

  register_route: (verb, path, handler) ->
    if handler is null or handler is undefined
      handler = path
      path = ''
    if not @cur_app? then @app ''
    #puts "reg route: #{verb} #{path}"
    return @cur_app.add_handler verb, path, handler

  # lightweight Monitor for site
  monitor: (fn)->  @cur_site.setMonitor fn

  ### filter is global scope object in coffeemate. Name must be unique.
    1. name, App/Site, option-object, fn    : register to App/Site a filter with options
    2. name, App/Site, fn                   : register to App/Site a filter without options
    3. name, App/Site, option-object        : register to App/Site a existing filter with options
    4. name, option-object, fn                : register to current App/Site a filter with options
    5. name, fn                               : register to current App/Site a filter or just add a filter as prototyp
    6. name, App/Site                       : register to App/Site a existing filter without options
    7. name, option-object                    : register to current App/Site a existing filter with options
    8. name                                   : register to current App/Site a existing filter without options
  ###
  filter: (name,belong,opt,fn)->
    __belong = @cur_app or @cur_site or @filter_holder
    if not fn? # 2345678
      if opt? # 234
        if belong instanceof WebLayer or belong is '_all' # 23
          if 'function' is typeof opt # 2
            fn = opt
            opt = null
          else # 3
            fn = null # @filters[name]
        else # 4
          fn = opt
          opt = belong # opt should not be a function. but no need to check
          belong = __belong
      else # 5678
        if belong? #567
          if 'function' is typeof belong #5
            fn = belong
            opt = null
            belong = __belong
          else # 67
            if belong instanceof WebLayer or belong is '_all' # 6
              fn = opt = null
            else # 7
              opt = belong
              belong = __belong
        else #8
          belong = __belong
          fn = opt = null

    # check name is string
    if 'string' isnt typeof name
      throw new Error("First parameter for 'filter' must be a string.")
    # check if global filter
    if belong is '_all'
      belong = @root
      @_deal_shortcut = @_deal_filters # to speed up filter process
    # check fn
    if fn?
      if 'function' isnt typeof fn
        throw new Error("filter expects a function parameter but get #{typeof fn}")
      if @filters[name]?
        puts "WARN: Filter Overwrite '#{name}'"
      @filters[name] = fn
    else
      fn = @filters[name]
      if not fn?
        throw new Error("Filter '#{name}' is not defined yet.")
    # always have opt for filter to use for themselves, and no need to check the existence
    if not opt?
      opt = {}
    # register filter to belong
    belong.add_filter fn,name,opt

  # the same as express use function.
  # register a pre-process filter for whole framework
  use: (name,pre_processor)->
    if not pre_processor
      throw new Error('No name use pre processor')
    else
      pre_processor._cmate_name = name
    if pre_processor? and 'function' is typeof pre_processor
      #if verbose then puts "USE pre_processor: #{pre_processor}"
      @pre_processors.push pre_processor
    else
      throw new Error("'use' can only accept a handler function")

  # the same as express error function.
  # register an error handler for whole framework
  error: (error_fn)->
    if error_fn? and 'function' is typeof error_fn
      @error_handlers.push error_fn
    else
      throw new Error("'error' can only accept a handler function")

  fallback: (fn)->
    @fallbacks.push fn


  _css_js: (target,belong,name,pview,arg)->
    if target is '_coffee'
      if ((t = typeof arg) isnt 'function') and ('string' isnt t)
        throw new Error("COFFEE needs a function, but got #{typeof arg}")
    else if 'string' isnt typeof arg
      throw new Error "CSS/LESS/JS need string, but got #{typeof arg}"
    if 'string' isnt typeof name
      throw new Error "name is not a string. #{typeof name}"
    if verbose > 1 then puts "new #{target} '#{name}' for " + (if belong instanceof Site then "Site '#{belong.name}'" else if belong instanceof App then "App '#{belong.url()}'" else 'Globel') + (if pview? then " extends #{pview}" else "")
    if pview then throw new Error "extention for #{target} is not supported yet." # TODO
    b = (belong[target] ?= {})
    if (name is '_') and (t = b[name]?)
      if Array.isArray t
        t.push arg
      else
        b[name] = [arg]
    else
      b[name] = arg

  _layout_view: (target,belong,name,pview,arg)->
    if typeof arg is 'object'
      for k, v of arg
        if verbose > 1 then puts "new #{target} '#{k}' for " + (if belong instanceof Site then "Site '#{belong.name}'" else if belong instanceof App then "App '#{belong.fullname()}'" else 'Globel') + (if name? and name isnt 'none' then " extends #{name}" else "")
        if 'string' is typeof k
          belong[target] ?= {}
          if name? and name isnt 'none'
            belong[target][k] = {pname:name,tpl:arg} # ptpl is resolved when rendering
          else
            belong[target][k] = v
        else
          throw new Error "View or Layout name must be a string, but #{typeof k}"
    else if 'function' is typeof arg # a function
      if verbose > 1 then puts "new #{target} '#{name}' for " + (if belong instanceof Site then "Site '#{belong.name}'" else if belong instanceof App then "App '#{belong.fullname()}'" else 'Globel') + (if pview? then " extends #{pview}" else "")
      if 'string' is typeof name
        belong[target] ?= {}
        if pview?
          belong[target][name] = {pname:pview,tpl:arg} # ptpl is resolved when rendering
        else
          #TODO: debug
          belong[target][name] = arg
      else
        throw new Error "View or Layout name must be a string, but #{typeof name}"
    else
      throw new Error "View or Layout should be a function, but #{typeof arg}"

  _layout_view_css_js: (target,belong,name,arg)->
    if not arg? # belong or name maybe omitted
      if not name? # both belong and name are omitted
        arg = belong
        name = '_'
        belong = null
      else # only one is omitted
        arg = name
        if 'string' is typeof belong # belong is omitted
          name = belong
          belong = null
        else if helpers.isArray belong # inherits views, belong is omitted
          name = belong.shift()
          if typeof name isnt 'string' then throw new Error "View/Layout/Css/Js name must be a string or string array for 2. Get the 1st one gets #{typeof name}."
          pview = belong.shift()
          pview = '_' if pview is null
          if typeof pview isnt 'string' then throw new Error "View/Layout/Css/Js name should be a string array for 2. But the 2nd one gets #{typeof pview}."
          if belong.length then throw new Error "View/Layout name should be a string array for 2. But get extra #{belong.length}."
          belong = null
        else if belong instanceof WebLayer
          name = '_'
        else
          throw new Error "Un-recognized parameter. Should be a string, string array of 2, or a Site/App. Got #{typeof belong}."
    if (not belong?)
      if (not @cur_app?) and @cur_site?
        @cur_app = @cur_site._app
      belong = @cur_app or @cur_site or @root
    else if not (belong instanceof WebLayer)
      throw new Error "Un-recognized View/Layout container. #{belong}"
    if target is '_views' or target is '_layouts'
      @_layout_view target,belong,name,pview,arg
    else
      @_css_js target,belong,name,pview,arg

  layout: (belong,name,arg)-> @_layout_view_css_js '_layouts',belong,name,arg
  view:   (belong,name,arg)-> @_layout_view_css_js '_views',belong,name,arg
  css:    (belong,name,arg)-> @_layout_view_css_js '_css',belong,name,arg
  less:   (belong,name,arg)-> @_layout_view_css_js '_less',belong,name,arg
  stylus: (belong,name,arg)-> @_layout_view_css_js '_stylus',belong,name,arg
  js:     (belong,name,arg)-> @_layout_view_css_js '_js',belong,name,arg
  coffee: (belong,name,arg)-> @_layout_view_css_js '_coffee',belong,name,arg

  # WebLayer.compile_css_js(cb)
  compile_css_js: (cb)-> # pre-compile all css to APP.__css
    # TODO: use helpers.map
    nimble.map @sites,((s,callback)-> s.compile_css_js callback),cb

  fullpath: (file)->
    if typeof file is 'string'
      if file.charAt(0) isnt '/'
        if @base?
          if @base.charAt(@base.length - 1) isnt '/'
            file = @base + '/' + file
          else
            file = @base + file
        else throw new Error "relative path #{file} can not be resolved. 'base' path is undefined."
      return Path.normalize file
    else
      throw new Error "File name needs to be a string, but get a '#{typeof file}'"

  todo: (req,resp)-> resp.todo()

  get_root_handler: ()->
    self = this
    @waiting_requests = []
    return (req,resp)->
      if self.is_ready
        self.exec.call self,req,resp
      else
        self.waiting_requests.push {req:req,resp:resp}
      null

  _deal_error: (req,resp,err)->
    try
      if verbose > 3 then puts "NEXT Internal Error Procedure Invoked. err:#{err}"
      if (err? and not (err instanceof NotFound))
        if not err.doNotLog
          if not (err instanceof Error)
            console.error err
            err = new Error "err #{err} is not Error type." + util.inspect err
          req.logger.error err if req.logger?
        resp.statusCode = err.statusCode or 500
        err = @_rootConfig.production?.error_message or err
        resp.ckup 'E500', {error: err}
      else
        req.logger.notfound req
        resp.statusCode = 404
        resp.ckup 'E404', {host:req.hostname, url:req.url} # applications to define ERROR400 layout
      return null
    catch new_err
      try
        if verbose
          puts "Error when invoking internal Error Procedure. #{new_err}"
          puts new_err.stack
        req.logger.error new_err if req.logger?
        if resp._header
          req.logger.warn "Don't send 500 error to client, because headers are sent already." if req.logger?
        else
          resp.send "Internal Error.",500
      catch new_new_err
        puts new_new_err
        puts "Continue to run..."
    return null

  _deal_error_handler: (req,resp,err)->
    if err? and (l = @error_handlers.length) > 0
      i = 0
      self = this
      next_error_handler = (err)->
        if i < l
          return self.error_handlers[i++] err,req,resp,next_error_handler
        return self._deal_error req,resp,err
      return next_error_handler err
    return @_deal_error req,resp,err

  _deal_fallbacks: (req,resp,err)->
    if err then @_deal_error_handler req,resp,err
    if (l = @fallbacks.length) > 0
      i = 0
      self = this
      next_fallback = (err)->
        return self._deal_error_handler(req,resp,err) if err
        if i < l
          return self.fallbacks[i++] req,resp,next_fallback
        return self._deal_error_handler req,resp
      return next_fallback()
    return @_deal_error_handler req,resp

  _deal_sites: (parts,pathname,req,resp)->
    # putsObj @sites
    # putsObj req.setting
    # get site by setting from vhost, or use the default
    site = @sites[req.setting?.site or '']
    # if no default site, and there is only one site, use that one.
    if not site? and Object.keys(@sites).length = 1
      site = @sites[Object.keys(@sites)[0]]
    if not site? # TODO: error for no site
      return @_deal_error_handler req,resp, new Error 'Site: '+site+"Can't find approper Site handler."+(req.setting?.site or '')
      
    self = this
    req._next_site = {} # for sites to work
    total_processed++
    site.exec parts, pathname, req,resp,(err)-> self._deal_fallbacks req,resp,err

  _deal_filters: (parts,pathname,req,resp)->
    if (l = @root._filters.length) > 0
      flts = @root._filters
      i = 0
      self = this
      next_filter = (err)->
        if err then self._deal_error_handler req,resp,err
        if i < l
          f = flts[i++]
          if verbose > 1 then req.logger.log "run filter [#{f.name}]"
          try
            return f.fn parts,pathname,req,resp,next_filter,f.opt
          catch err
            return self._deal_error_handler req,resp,err
        else
          return self._deal_sites parts,pathname,req,resp
      return next_filter()
    else
      return self._deal_sites parts,pathname,req,resp

  _deal_shortcut: @prototype._deal_sites

  _deal_pre_procedures: (parts,pathname,req,resp)->
    if (l = @pre_processors.length) > 0
      i = 0
      self = this
      next_pre_processor = (err)->
        if err then return self._deal_error_handler req,resp,err
        if i < l
          try
            if verbose > 3 then puts "Pre - processer #{i} #{self.pre_processors[i]._cmate_name}"
            self.pre_processors[i++] req,resp,next_pre_processor
          catch err
            if /URI\smalformed/.test err.message
              return resp.send 'Error: URI malformed',500
            return self._deal_error_handler req,resp,err
        else
          if verbose > 3 then puts "Pre - processer done"
          return self._deal_shortcut parts,pathname,req,resp
      return next_pre_processor()
    else
      return @_deal_shortcut parts,pathname,req,resp


  # get request directly from node server
  exec: (req,resp)->
    total_requests++
    self = this
    req.resp = resp
    resp.req = req
    cfg_proxy_header = @_rootConfig.serverBase.proxy_header
    req.ip = if cfg_proxy_header then req.headers[cfg_proxy_header] or req.socket.remoteAddress or req.connection.remoteAddress else req.socket.remoteAddress or req.connection.remoteAddress
    url = Url.parse req.url,true
    req.search = url.search
    req.query = url.query
    req.pathname = pathname = url.pathname.substr(1) # keep pathname as is
    req.lpathname = lpathname = pathname.toLowerCase()
    req.href = url.href
    parts = lpathname.split '/'
    req.params ?= {} # setup params for express framework.

    if (host = (url.host or req.headers['host']))?
      # using URL parse first then header?
      req.host = host
      req.hostname = url.hostname or host.split(':')[0].toLowerCase()
      if url.port
        req.port = url.port
    else
      err = new Error "Host header is not received."
      return @_deal_error req,resp,err

    # run pre-processors
    @_deal_pre_procedures parts,pathname,req,resp

  read: (file) -> # TODO: support header for JS files
    @cur_app = null
    @cur_site = null
    if verbose > 1 then puts "load file #{file}"
    fs.readFileSync file, 'utf8'

  processCode: (code,filePath)->
    mReg = /DEBUG([\.\s\(]+)(.*)$/
    lines = code.split /\r?\n/
    for l,i in lines
      if m = l.match mReg
        if m[1] is '.' # DEBUG.info/error ...
          unless mm = m[2].match /^([^\s\(]+)([\s\(]+)(.*)$/
            throw new Error "Bad line(#{filePath}:#{i+1}): #{l}"
          newLine = "DEBUG.#{mm[1]}#{mm[2]}#{filePath},#{i+1},#{mm[3]}"
        else # space or (
          newLine = "DEBUG.info#{m[1]}#{filePath},#{i+1},#{mm[2]}"
        lines[i] = newLine
    lines.join '\n'

  read_and_compile: (file, _header = '') ->
    _st = Date.now()
    code = @read file
    # code = @processCode code,file
    code = _header + '\n' + code
    try
      #puts 'CoffeeCode',code
      code = coffee.compile code,{inlineMap:true,bare:true}
    catch e
      puts e
      puts ">>>======================"
      puts code
    @_load_time[file] = Date.now() - _st
    code

  run_file: (file, _header = '') ->
    code = if file.match /\.coffee$/ then @read_and_compile file,_header else @read file
    @run code,file

  run: (code,file) ->
    #scoped(code)(@context, @locals)
    context = Object.assign {},@context,@locals
    run_in_context code,context,file

  _startup_step2: (cfg)->
    self = this

    # other services & filters
    parser = @include 'lib.http_parser'
    @use 'cookieParser',parser.cookieParser()
    @use 'multipartParser',parser.multipartParser cfg.multipart
    @use 'bodyParser',parser.bodyParser()

    # vhost,
    # since this one may need access db to get site,
    #  Delay can cause bodyParser not working correctly, so make this one after bodyParser
    vhost_rid = self.waitme 'vhost_handler'
    vhost = @include "lib.vhost_db"
    vhostcfg = cfg.serverBase.vhost
    if 'string' is typeof vhostcfg
      vhostcfg = @fullpath vhostcfg
      if vhostcfg.endsWith('.ini')
        vhostcfg= toml.parse fs.readFileSync(vhostcfg,'utf8')
    if not cfg.dbs
      @use 'vhost', vhost vhostcfg,->
        self.provide vhost_rid,vhost
    else
      site_db = @collection 'chome','site'
      @use 'vhost', vhost vhostcfg,site_db,i18n,->
        self.provide vhost_rid,vhost
    @hostrefresh = vhost.clearCache

    #@use parser.cookieParser()
    @use 'methodOverride',parser.methodOverride()
    #@use 'compressor',compressor(cfg.compress) # TODO

    # coffeemate session setup
    @session_setup cfg if cfg.session

    if cfg.limitAccessRate
      puts "Use rateLimiter"
      # rateLimiter = @include 'lib.rateLimiter'
      # @use 'rateLimiter',rateLimiter cfg.limitAccessRate
      # rateLimiter.setAlertHandler @alertHandler
      rateLimiter = @include 'lib.rateLimiterV2'
      options = Object.assign {source:'coffeemate3'},cfg.limitAccessRate
      rateLimiterInstance = rateLimiter.createLimiter options,@alertHandler
      @use 'rateLimiter',rateLimiter.createFilter rateLimiterInstance

    # Themes setup
    @plswait "process_load_themes"
    @themes_setup cfg, (err)->
      if err?
        console.error 'Error when loading themes:'
        console.error err
        console.error cfg
        throw err
      self.provide "process_load_themes"

    # Migration when not satelliteMode
    if (not satelliteMode) and (cfg.dbs)
      @plswait "process_migration"
      @migrate cfg,->
        self.provide "process_migration"

    # coffeemate apps setup
    @plswait "process_compile_apps"
    @apps_setup cfg, ->
      self.provide "process_compile_apps"
      self.plswait "process_compile_css_js"
      self.compile_css_js (err)->
        if err?
          console.error 'Error when compiling css/js:'
          console.error err
          throw err
        self.provide "process_compile_css_js"

    # common static provider for vhost and public files
    staticProvider = @include 'lib.static_provider'
    @fallback staticProvider cfg

    # register router
    puts "Create Server..."
    @_server = @http.createServer @get_root_handler()
    @clearReady '__app_ready'
    @depend '__app_ready',(err,ok)->
      puts "APP_READY"
      if err
        puts "Abort start-up due to error: #{err}"
        throw err
      self.final_setup()
      # if socket
      if cfg.socket
        self._socket = require('socket.io')(self._server,(if 'object' is typeof cfg.socket then cfg.socket else null))
        if verbose then puts "With Socket.IO at #{cfg.socket.path or '/socket.io'}"
        self._sockets = {}
        bindNamespace = (k)->
          self._sockets[k] = self._socket.of k
          self._sockets[k].on 'connection',(socket)->
            self.socketConnected k,socket
        for k,v of self._socketFuns
          bindNamespace k
      # output methodMap structure
      methodMap.done()
      # startup server
      port = cfg.server?.port or 3000
      host = cfg.server?.host or '127.0.0.1'
      self._server.on 'error',(err)->
        puts "Server error: #{err}. Exit"
        self.exit 1
      self._server.listen port,host,(err)-> # port
        if err
          puts "Server start up error #{err}"
          self.exit(1)
        else
          puts "Server bounded to #{host}:#{port}"
          self.profile cfg,->
            self.secure(cfg)
            puts "Server starting on #{host}:#{port}"
            if MongoDb?.setReady
              MongoDb.setReady()
            if process.send?
              process.send {cmd:'online',data:process.pid}


    helpers.nextTask -> self._check_no_waiting_resource()
    null

  profile: (cfg,cb)->
    if cfg.profiler
      require('./profiler').start cfg.profiler,mate,cb
    else
      cb()
  secure: (cfg)->

    if cfg.server?.title
      process.title = cfg.server.title

    if cfg.server?.root
      @chroot cfg.server.root,cfg.server.noch
    else
      @_rootConfig.base = @base

    if cfg.server?.gid
      @setgid cfg.server.gid
    if cfg.server?.uid
      @setuid cfg.server.uid

    @emit 'ready_apps',this
    @ready()
    self = this
    process.nextTick -> self._check_no_waiting_resource()
    null


  chroot: (path,noch)->
    path = @fullpath path
    if noch
      if verbose then puts "set root to: " + path
      @_rootConfig.base = @base = path
    else
      if verbose then puts "chroot to : " + path
      posix = @include 'posix' # may fail
      posix.chroot path
      @_rootConfig.base = @base = '/'
    if verbose
      puts "current path: " + process.cwd()

  setgid: (gid)->
    cgid = process.getgid()
    if verbose
      puts "Current gid: " + cgid
    if cgid isnt gid
      try
        process.setgid gid
        puts "New gid: " + process.getgid()
      catch err
        puts "Failed to set uid: " + gid
        throw err

  setuid: (uid)->
    cuid = process.getuid()
    if verbose
      puts "Current uid: " + cuid
    if cuid isnt uid
      try
        process.setuid uid
        puts "New uid: " + process.getuid()
      catch err
        puts "Failed to set uid: " + uid
        throw err


  check_running: (cfg,next)->
    if cluster.isWorker
      if f = cfg.server.pid
        fs.writeFileSync f, (""+process.pid) # always write out pid, when has pid set
      return process.nextTick next
    if cfg.server?.pid
      puts "Check PID file: #{f} "
      try
        pid = fs.readFileSync f
        if pid
          childprocess.exec 'ps -p #{pid} -o pid=', (err,stdout, stderr)->
            if stdout.length is 0
              puts "Remove old PID file: #{f}"
              fs.unlinkSync f
              fs.writeFileSync f, process.pid
              puts "Write PID file: #{f} "
              process.nextTick next
            else
              throw new Error "Process #{pid} is running."
      catch e
        if e.code isnt "ENOENT" #errno isnt 9 and e.errno isnt 33 # v0.6.0 33, v0.5.10 32, v0.4.12 9
          puts e
          throw e
        fs.writeFileSync f, process.pid
        puts "Write PID file: #{f} "
        process.nextTick next
    else
      process.nextTick next
    null

  startup: (cfg)->
    self = this
    @check_running cfg, ->
      puts "Base Setup"
      self.base_setup cfg, ->
        # Request Logger
        if verbose > 2 then puts "Setup Logger"
        self._reqlog = self.include "lib.log"
        self._reqlog.timeout_handler (req)-> self._needReload "Timeout @ #{req?.url}"
        log_cfg = cfg.log
        self.use "log",self._reqlog(log_cfg,self)
        logger = self._reqlog.logger
        # appMethodMap
        methodMap = self.include('lib.appMethodMap')
        methodMap.init log_cfg,self
        self._startup_step2 cfg

  # Templating engines
  dot: (def,src)->
    if not src
      src = def
      def = null
    doT.compile src,def
  jade: (opt,src)->
    if not opt
      src = opt
      opt = null
    @dot jade.render src,opt
  # mustache: (opt,src)->
  #   if not opt
  #     src = opt
  #     opt = null
  #   mustache.render src,opt

class WebLayer
  constructor: (@name,@upper,@classtype='WebLayer')->

  add_filter: (fn,name,opt)->
    if verbose > 1 then puts "Add Filter '#{name}'"
    if not @_filters?
      @_filters = []
      @_filters.push {fn:fn,name:name,opt:opt}

  def: (key,obj)->
    methodMap.add ('_DEF:'+key),(if obj then '*' else 'U'),@fullname()
    if obj?
      @_defs ?= {}
      @_defs[key] = obj
    else
      @_defs?[key] or @upper?.def key

  get_view: (vname,is_layout)->
    v = if is_layout then @_layouts?[vname] else @_views?[vname]
    if v?
      vt = typeof v
      return v if 'function' is vt or 'string' is vt or Array.isArray v
      pv = if v.pname is vname then @upper?.get_view v.pname,is_layout else @get_view v.pname,is_layout
      nv = if Array.isArray pv then pv.slice 0 else [pv]
      nv.push v.tpl
      if is_layout then @_layouts[vname] = nv else @_views[vname] = nv
      return nv
    return @upper?.get_view vname,is_layout

  exec_it: (parts,pathname,req,resp,next)->
    next() # To Be Overrided

  filter_it: (parts,pathname,req,resp,next)->
    if (l = @_filters.length) > 0
      if verbose > 2 then req.logger.log "#{@fullname()} Filters: #{@_filters.length}"
      i = 0
      self = this
      next_filter = (err)->
        if err? then return next err
        if i < l
          f = self._filters[i++]
          if verbose > 1 then req.logger.log "#{self.fullname()} run filter: #{f.name}"
          try
            return f.fn parts,pathname,req,resp,next_filter,f.opt
          catch err
            return next err
        else
          return self.exec_it parts,pathname,req,resp,next
      return next_filter()
    return @exec_it parts,pathname,req,resp,next

  exec: (parts,pathname,req,resp,next)->
    if @_filters then return @filter_it parts,pathname,req,resp,next
    @exec_it parts,pathname,req,resp,next

  fullname: -> "TO BE OVERRIDED #{@classtype} #{@name}"

class Site extends WebLayer
  constructor: (name,cmate)->
    super name,cmate.root,'Site'
    @cmate = cmate
    # @_dm_srh_hd = undefined
    @app = new App '',null,@
    @widgets = {}

  fullname: -> @name

  extend: (parents)->
    if 'string' is typeof parents
      @_add_parent parents
    else if Array.isArray parents
      for p in parents
        @_add_parent p
    else
      throw new Error "Site parents must be a name or name array. Got #{typeof p} #{p}"

  addWidget: (name,fn)-> @widgets[name] = fn

  setMonitor: (fn)-> @monitor = fn

  _add_parent: (p)->
    if pp = @cmate.sites[p]
      @_ps ?= []
      if @_ps.indexOf(pp) < 0
        if verbose then puts "Extended Site(#{@name}) from Site(#{pp.name})"
        @_ps.push pp
        # copy apps
        @app.mergeClone pp.app
        # copy widgets
        for k,v of pp.widgets
          @widgets[k] ?= v
    else
      throw new Error "Undefined parent site #{p}"

  compile_css_js: (cb)->  @app.compile_css_js cb

  def: (key,obj)->
    methodMap.add '_DEF:'+key,(if obj then '*' else 'U'),@fullname()
    if obj?
      @_defs ?= {}
      @_defs[key] = obj
    else
      return v if (v = @_defs?[key])?
      if @_ps
        for pp in @_ps
          if (v = pp._defs?[key])?
            return v
      return @upper?.def key

  sitedef: (key,length = 1000)-> # get def array from current and all parents site
    ret = []
    if v = @_defs?[key]
      if Array.isArray v
        ret = ret.concat v
      else
        ret.push v
    if ret.length >= length then return ret
    if @_ps
      for pp in @_ps
        if (v = pp._defs?[key])?
          if Array.isArray v
            ret = ret.concat v
          else
            ret.push v
          if ret.length >= length then return ret
    if ret.length > 0
      return ret
    else
      return null

  get_view: (vname,is_layout)->
    v = super vname,is_layout
    if not v? and @_ps?
      for p in @_ps
        if v = p.get_view vname,is_layout
          return v
      if not v?
        v = @cmate.root.get_view vname,is_layout
    v

  ensureApp: (name,prefix)->
    # under another app
    if prefix?
      if prefix instanceof App
        if prefix.site isnt @ then throw new Error "Parent App isn't belonging to this Site #{@name}"
      else
        throw new Error "App can only be under another App, not #{typeof prefix}"
    else
      prefix = @app
    if name is '' then return prefix
    return prefix.subapp name

  filter_it: (parts,pathname,req,resp,next)->
    if not req.site?
      if verbose > 2 then req.logger.log "Set Site #{@name}"
      req.site = this # the endpoint site only, not parent sites
    super parts,pathname,req,resp,next

  exec_it: (parts,pathname,req,resp,next)->
    if verbose > 1 then req.logger.log "Hostname: #{req.hostname}"
    if not req.site?
      if verbose > 2 then req.logger.log "Set Site #{@name}"
      req.site = this # the endpoint site only, not parent sites
    if @monitor
      self = @
      @monitor req,->
        self.app.exec parts,pathname,req,resp,next unless req.renderFile pathname,{cfg:req.setting}
      return
    @app.exec parts,pathname,req,resp,next unless req.renderFile pathname,{cfg:req.setting}

  exec: (parts,pathname,req,resp,next)->
    # TODO: if is app do nothing
    if req.setting
      if (prefix = req.setting.langPrefix) and (parts.length > 0) and isCommonLanguage(parts[0])
        lang = prefix[parts[0]]
        if not lang
          lang = req.setting.locale or 'en'
        req.setLocale lang
        cutOff = parts[0].length + 1
        pathname = pathname.substr cutOff
        # req.pathname = req.pathname.substr cutOff
        # req.lpathname = req.lpathname.substr cutOff
        req.removedPrefix = parts.shift()
      else if req.setting.locale
        req.setLocale req.setting.locale
    if @_filters then return @filter_it parts,pathname,req,resp,next
    @exec_it parts,pathname,req,resp,next

class App extends WebLayer
  constructor: (path,upper,site)-> # upper is parent App
    super path,upper,'App'
    @path = path
    @site = site
    if upper and @path is '' then throw new Error "Empty name App can't under antoher App."
    if not upper and @path isnt '' then throw new Error "Top lavel App shall has not name, got #{@path}"
    @_methods = {}
    @_layouts = {}
    @_views = {}
    # TODO: consider write to file, not helpful to be in logs that only displays when restart
    # if verbose > 1 then puts "new App '#{@url()}' for Site '#{@site.name}'"

  _clone_methods: (_methods,_toBeClonedMethods)->
    _methods ?= {}
    for m,fna of _toBeClonedMethods
      efna = (_methods[m] ?= [])
      for fn in fna
        notequal = true
        for efn in efna
          if efn.regexp.toString() is fn.regexp.toString()
            notequal = false
            break
        if notequal
          efna.push fn
    _methods

  clone: (forSite,upper=null)->
    obj = new App @path,upper,forSite
    if @_methods
      obj._methods = @_clone_methods obj._methods,@_methods
    for n in ['_views','_layouts','_css','_js','_less','_stylus','_coffee','_defs']
      if @[n] then obj[n] = helpers.shallowExtendObject {}, @[n]
    if @_subs?
      obj._subs = {}
      for k,a of @_subs
        obj._subs[k] = a.clone forSite,obj
    obj

  mergeClone: (toBeCloned)->
    if toBeCloned._subs?
      @_subs ?= {}
      for k,a of toBeCloned._subs
        if @_subs[k]
          @_subs[k].mergeClone a
        else
          @_subs[k] = a.clone @site,@
    if toBeCloned._methods
      @_methods = @_clone_methods @_methods,toBeCloned._methods
    for n in ['_views','_layouts','_css','_js','_less','_stylus','_coffee','_defs']
      if toBeCloned[n] then @[n] = helpers.shallowExtendObject (@[n] or {}), toBeCloned[n]

  get_css: (name)->  @__css?[name]
  get_js: (name)-> @__js?[name]
  compile_css_js: (cb)-> #TODO
    self = @
    do_subs = (err)->
      if not err? and self._subs?
        helpers.map self._subs,((a,i,callback)-> a.compile_css_js callback),cb
      else
        cb(err)
    compile_less = (err)->
      if not err? and self._less
        helpers.map self._less,((c,k,callback)->
          coffeeless.compileLess c,(err,cc)->
            if not err
              helpers.flattenAddToObject self.__css,k,cc
            callback err
          ),do_subs
      else
        do_subs(err)
    compile_stylus = (err)->
      if verbose then debug self.fullname() + " compile stylus"
      if not err? and self._stylus
        helpers.map self._stylus,((c,k,callback)->
          coffeeless.compileStylus c,(err,cc)->
            if not err
              helpers.flattenAddToObject self.__css,k,cc,'\n'
            callback err
          ),compile_less
      else
        compile_less(err)

    # compile coffee
    if @_js or @_coffee
      @__js = {}
      if @_js
        for k,j of @_js
          @__js[k] = if Array.isArray j then j.join '\n' else j
      if @_coffee?
        for k,c of @_coffee
          j = coffeeless.compileCoffee c,{} # add {} to uglify
          helpers.flattenAddToObject @__js, k, j,'\n'
    # compile css/less/stylus
    if @_css or @_less or @_stylus
      @__css = {}
      if @_css
        for k,c of @_css
          @__css[k] = if Array.isArray c then c.join '\n' else c
      compile_stylus()
    else
      do_subs()

  get_view: (vname,is_layout)->
    (super vname,is_layout) ? (@site?.get_view vname,is_layout)

  url: ->
    if @upper? and @upper instanceof App
      return @upper.url() + @path + '/'
    return @path + '/'

  sitepath:->
    "#{@site.fullname()}#{@url()}"

  fullname: -> @sitepath()

  subapp: (name)->
    while name.slice(0,1) is '/'
      name = name.slice(1)
    name = name.toLowerCase()
    if (p = name.indexOf '/') > 0
      left = name.slice(p+1)
      name = name.slice(0,p)
    # check '..' and illegal chars (not a-z0-9-_.:
    if /[^0-9a-z.\-\_:]/.test name
      throw new Error "Unsupported characters in App name (#{name})"
    # if parent App
    if name is '..'
      if @upper? and @upper instanceof App
        prefix = @upper
      else
        throw new Error "No upper App for #{@fullname()}"
    # if current has this sub
    if not (prefix ?= @_subs?[name])
      # not in upper or sub, create new sub
      s = (@_subs ?= {})
      prefix = s[name] = new App name,this,@site
    if left and left.length > 0
      return prefix.subapp left
    return prefix

  add_filter: (fn,name,opt)->
    if verbose > 1 then puts "Filter App(#{@url()}) for Site(#{@site.name}): '#{name}'"
    super fn,name,opt

  add_handler: (method,name,handler)->
    #puts "Add handler: #{method} #{name}"
    methodMap.add (@fullname()+''+name),method
    h = new RequestHandler(method, name, @, handler) #(
        #if typeof handler is 'string' then ((req, resp) -> resp.send String(handler)) else handler)
    m = (@_methods[method] ?= [])
    m.push h
    return h

  exec_me: (parts,pathname,req,resp,next)->
    if verbose > 3
      req.logger.log "looking for method #{req.method} parts #{parts} pathname #{pathname}"
      if verbose > 4 then req.logger.log util.inspect @_methods
    req.app = self = this # not found in subapps
    go_next = ->
      if pathname.length >= 4
        if (pathname.slice(-3) is '.js') and (js = self.__js?[pathname.slice(0,-3)])
          return resp.sendJs js
        else if (pathname.slice(-4) is '.css') and (css = self.__css?[pathname.slice(0,-4)])
          if pathname is 'editpage.css' then puts css
          return resp.sendCss css
      return next()
    m = @_methods[method = req.method] # method always upper case
    if m?
      if verbose > 3 then req.logger.log "method #{method} #{util.inspect m}"
      i = m.length - 1
      self = @
      exec_one = (err)->
        if err? then return next(err)
        if i >= 0
          handler = m[i--] # user reverse order, so child site can override parants' method
          if verbose > 3 then req.logger.log "try handler #{handler.regexp} for #{pathname} of #{parts}"
          if (cap = (handler.regexp.exec pathname))
            if verbose > 3 then req.logger.log "run handler #{handler.regexp} for #{pathname} of #{parts}"
            return handler.exec cap,pathname,req,resp,exec_one
          else
            return exec_one() # next one
        else
          # check css/js #TODO
          if req.method is 'GET' then return go_next()
          return next()
      return exec_one()
    else
      if req.method is 'GET' then return go_next()
      return next()


  exec_it: (parts,pathname,req,resp,next)->
    if verbose > 3 then req.logger.log pathname
    if parts.length > 0 and @_subs?
      n = parts[0]
      if verbose > 3
        req.logger.log "search subapp #{n}"
        if verbose > 4 then req.logger.log " in #{util.inspect @_subs}"
      if @_subs[n]?
        if verbose > 3 then req.logger.log "subapp find #{n}"
        self = this
        return self._subs[n].exec parts.slice(1),pathname.substr(n.length+1),req,resp,(err)->
          if err then return next(err)
          return self.exec_me parts,pathname,req,resp,next
    return @exec_me parts,pathname,req,resp,next

  exec: (parts,pathname,req,resp,next)->
    req.app = @
    if @_filters then return @filter_it parts,pathname,req,resp,next
    @exec_it parts,pathname,req,resp,next

class RequestHandler
  constructor: (@method,@pattern,@app,@fn)->
    @keys = []
    # puts "Method:" + @method
    # putsObj @pattern
    if helpers.isRegExp @pattern
      @regexp = @pattern
    else
      @regexp = regexp_convert @pattern, @keys
    @locals = {}
    if 'string' is typeof @fn
      str = @fn
      @fn = (req,resp,next)-> resp.ckup str
    # TODO: consider write to file, not helpful to be in logs that only displays when restart
    # NOTE: appMethods.txt/
    # if verbose > 1 then puts "new Route #{@method} for '#{@sitepath()}'"

  url: (f)->
    f ?= @app.url()
    if f.charAt(f.length-1) isnt '/'
      return f + '/' + @pattern
    else
      return f + @pattern

  sitepath: ->
    return @url @app.sitepath()



  exec: (cap,pname,req,resp,next)->
    p = (req.query ?= {})
    i = 0
    while i < cap.length
      k = @keys[i++]
      if k?
        v = cap[i]
        if ('string' == typeof v) and (not /[^\u0000-\u00ff]/.test v)
          v = querystring.unescape v
        p[k] = v
    req.query['all'] = querystring.unescape pname

    # call the real handler
    # with db as context version
    #@fn.call {db:new Database()},req,resp,next

    # call the real handler, no db version
    if verbose > 1 then req.logger.log "request handler #{@method} #{@pattern}"
    @fn req,resp,next


# NotFound error after all route were searched.
class NotFound extends Error
  constructor: (@path) ->
    super()
    if (@path)
      Error.call this, ('Cannot find ' + path)
    else
      Error.call this, 'Not Found'
    Error.captureStackTrace this, arguments.callee

class DummyResponse
  constructor: (@cb,@headers={})->
    @chunks = []
    @strings = []
    @encoding = 'utf8'
  writeContinue: ()->
  setHeader: (name,value)-> @headers[name] = value
  getHeader: (name)-> @headers[name]
  removeHeader: (name)-> delete @headers[name]
  addTrailers: ()->
  writeHead: (@statusCode,reasonPhrase,headers)->
    if 'object' is typeof reasonPhrase and not headers?
      headers = reasonPhrase
    @headers = headers
  write: (chunk,@encoding)->
    if 'string' is typeof chunk
      @strings.push chunk
    else
      buf = new Buffer(chunk.length)
      chunk.copy buf
      @chunks.push buf
  end: (data, @encoding)->
    if data
      @write data,@encoding
    if @strings.length > 0
      @cb @statusCode, @headers, @strings.join(''), @encoding
    else
      if @chunks.length is 1
        buf = @chunks[0]
      else if @chunks.length < 1
        buf = ''
      else
        buf = Buffer.concat @chunks
      @cb @statusCode,@headers,buf, @encoding # an array of Buffer

class DummyRequest
  constructor: (@realReq,@method,@url,@cfgParam={})->
    @connection = @realReq.connection
    @httpVersion = @realReq.httpVersion
    @host = @realReq.host
    @hostname = @realReq.hostname
    @app = @realReq.app
    @site = @realReq.site
    @logger = @realReq.logger
    @session = @realReq.session
    @headers = helpers.shallowMergeObjects @realReq.headers, @cfgParam.headers
    @params = helpers.shallowMergeObjects @realReq.params, @cfgParam.params
    @query = helpers.shallowMergeObjects @realReq.query, @cfgParam.query
    @body = helpers.shallowMergeObjects @realReq.body, @cfgParam.body
    @cookies = @realReq.cookies
    @_locale = @realReq._locale
    @setting = @realReq.setting
    @fullUrl = @realReq.fullUrl
    @isSecure = @realReq.isSecure


ExtendedRequest =
  setupL10n: -> setupL10n @
  redirectHTTPWhenCnDitu: (cb)->
    if @isIOS() and @isChinaIP() and @getProtocol() is 'https'
      @resp.redirect @fullUrl().replace /^https/,'http'
      return true
    return cb() if cb
    return false
  redirectHTTP: (cb)->
    # after fileupload change, no need to redirect http
    # if @getProtocol() is 'https'
    #   @resp.redirect @fullUrl().replace /^https/,'http'
    #   return true
    return cb() if cb
    return false
  getProtocol: ->
    if proto = @protocol or @headers['x-forwarded-proto']
      return proto
    else if (@connection.constructor.name is 'CleartextStream') or (@connection.encrypted)
      return 'https'
    else
      return 'http'

  fullUrl: (uri = @url, noDomain)->
    return uri if /^http/.test uri
    if @removedPrefix and (l = @removedPrefix.length) and uri.substr(0,l+2) isnt "/#{@removedPrefix}/"
      if uri[0] is '/'
        uri = "/#{@removedPrefix}" + uri
      else
        uri = "/#{@removedPrefix}/" + uri
    return uri if noDomain
    @getProtocol() + "://" + @host + uri

  isSecure: ->
    if (@protocol or @headers['x-forwarded-proto']) is 'https'
      return true
    else if (@connection.constructor.name is 'CleartextStream') or (@connection.encrypted)
      return true
    false

  remoteIP: ->
    @ip or @connection?.remoteAddress or @connection?.socket?.remoteAddress or @socket?.remoteAddress

  isChinaIP: ->
    return @_isChinaIP if @_isChinaIP?
    @_isChinaIP = chinaMode or chinaip.isChinaIP @remoteIP()

  isRealChinaIP: -> chinaip.isChinaIP @remoteIP()

  isChinaMode: -> chinaMode

  isDeveloperMode: -> developerMode

  param: (name,val)->
    return @params[name] if @params?[name]?
    return @query[name]  if @query?[name]?
    return @body[name]   if @body?[name]?
    return val

  get_def: (name)->
    @app?.def(name) or @site?.def(name)

  get_view: (view, is_l)-> (@app or @site or mate.coffeemate.root).get_view view,is_l

  referer: ->
    @headers['referer'] or @headers['referrer']

  accepts: (type)->
    accept = @headers['accept']

    # when Accept does not exist, or is '*/*' return true
    return true unless accept and '*/*' isnt accept

    # normalize extensions ".json" -> "json"
    type = type.substr(1) if type? and '.' is type[0]

    if type?
      # allow "html" vs "text/html" etc
      if type.indexOf('/') < 0
        type = mime.lookup(type)

      # check if we have a direct match
      return true if accept.indexOf(type) >= 0

      # check if we have type/*
      type = type.split('/')[0] + '/*'
      return accept.indexOf(type) >= 0
    else
      return false

  locale: (allowed,dlang)->
    return @_locale if @_locale
    if (lang = @cookies?['locale']) and (not @cookies['lang__a']?) # not an array
      return lang
    if lang = @session?.get 'locale'
      return lang
    if not dlang
      dlang = allowed
      allowed = null
    if langs = @headers?['accept-language']
      langs = langs.split(',')
      for lang in langs
        l =  lang.split(';',1)[0].toLowerCase()
        unless allowed and allowed.indexOf(l) < 0
          l = (i18n?.locale l) or l
          @session?.set 'locale',l
          return @_locale = l
    l = (dlang or @lang) # lang from site database or default setting
    l = (i18n?.locale l) or l or 'en'
    if 'string' isnt typeof l
      console.error "Locale type #{typeof l}:#{l}"
      l = '' + l
    @session?.set 'locale',l
    @_locale = l

  setLocale: (locale,cb)->
    locale = i18n?.locale locale
    @_locale = locale
    delete @_l10n
    @resp?.cookie 'locale',locale, {maxAge:(3600000 * 24 * 365),path:'/'}
    @session?.set 'locale',locale,cb

  _internal_loop_callback: (statusCode, headers,body)->
    if headers
      for k,v of headers
        if k is 'Location' and v is "http://#{@host}#{@url}"
          @logger?.warn "Detected Redirect Loop at #{v}"
          continue
        @resp.setHeader k,v
    @resp.send body,statusCode

  _internal_loop_call: (method, to_url, options, callback)->
    self = this
    if callback # has callback
      if 'function' isnt typeof callback
        throw new Error "Internal Loop Call expect a callback function as last parameter. Got #{typeof callback}."
    else if 'function' is typeof options
      callback = options
      options = null
      if 'function' isnt typeof callback
        throw new Error "Internal Loop Call expect a callback function as last parameter. Got #{typeof callback}."
    else
      #throw new Error "Internal Loop Call expect a callback function. Got #{typeof options}."
      callback = -> self._internal_loop_callback.apply self,arguments
    req = new DummyRequest self,method,to_url,options
    resp = new DummyResponse callback
    req.resp = resp
    resp.req = req
    if 'string' is typeof to_url
      to_url = encodeURI to_url
      url = Url.parse to_url,true
      req.query = url.query
      req.hostname = self.hostname
      if to_url.charAt(0) is '/'
        base = self.site
        req.url = to_url
        req.pathname = pathname = url.pathname.substr(1)
      else
        base = self.app
        req.url = base.url() + '/' + to_url
        req.pathname = pathname = (url.pathname ?= '')
      req.lpathname = pathname.toLowerCase()
      parts = req.lpathname.split '/'
    else
      throw new Error "Internal Loop Call expect a URL as parameter."
    base.exec parts, pathname, req, resp, ->
      self.logger.error new Error "Internal Loop Call #{method} #{self.hostname}:#{to_url} got no result. #{parts} #{pathname}"
      callback 0 # no header & body for not executed operation

  GET: (url,options,callback)->
    @_internal_loop_call 'GET',url,options,callback
  POST: (url,options,callback)->
    @_internal_loop_call 'POST',url,options,callback
  DELETE: (url,options,callback)->
    @_internal_loop_call 'DELETE',url,options,callback
  PUT: (url,options,callback)->
    @_internal_loop_call 'PUT',url,options,callback
  # OPTIONS: (url,options,callback)->
  #   @_internal_loop_call 'OPTIONS',url,options,callback

  UA: -> @headers?['user-agent']
  getUA: ->
    if ua = @headers?['user-agent']
      return 'iphone' if /iPhone/i.test ua
      return 'ipad' if /iPad/i.test ua
      return 'android' if /Android/i.test ua
      return 'iephone' if /Windows\sPhone|IEMobile/i.test ua
    null

  # get remote ip's geo country and region string. i.e. CA:ON ,or longformat Canada:Ontario
  # 这个getGeoInfo/LatLng只能得到request的geoInfo，而且是通过nginx的配置实现的。
  getGeoInfo: (longFormat=false)->
    if not @headers?
      return null
    if longFormat
      country = @headers['x-geocountry']
      region = @headers['x-georegion']
    else
      country = @headers['x-geocode']
      region = @headers['x-georegioncode']
    geoInfo = ''
    if country
      geoInfo = country
    if region
      geoInfo += ':' + region
    return geoInfo

  # return the [lat,lng] of the remote ip
  getGeoLatLng: ->
    if (lat = @headers['x-geolat']) and (lng = @headers['x-geolng'])
      return [lat,lng]
    null

  isMiniProgram: ->
    if ua = @headers?['user-agent']
      return true if /miniProgram/i.test ua
    false

  isWeChat: ->
    if ua = @headers?['user-agent']
      return true if /MicroMessenger/i.test ua
    false

  isChrome: ->
    if ua = @headers?['user-agent']
      return /Chrome|CriOS/i.test(ua)
    null

  # NOTE: android device is safari too. navigator.vendor is a better option
  # check browser is safari.
  #Mozilla/5.0 (iPhone; CPU iPhone OS 13_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1
  isSafari: ->
    if ua = @headers?['user-agent']
      return (/Safari/i.test(ua)) and (not @isChrome())
    null

# Mozilla/5.0 (iPhone; CPU iPhone OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
# Mozilla/5.0 (iPad; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1
# Safari: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36
# NOTE: should be isAppleDevice
  isIOSDevice:->
    if ua = @headers?['user-agent']
      return @isIOS() or /Macintosh/i.test(ua)
    null
  
  isIOS: ->
    if ua = @headers?['user-agent']
      return /iPhone|iPad/i.test ua
    null

  isAndroid: ->
    if ua = @headers?['user-agent']
      return /Android/i.test ua
    null

  isMobile: ->
    return ['iphone','android','iephone'].indexOf(@getUA()) >= 0

  isIE: ->
    if ua = @headers?['user-agent']
      return (/MSIE|Trident/.test(ua) and /Windows/.test(ua))
    null

  getDevType: ->
    if @cookies['apsv'] or (@headers?['user-agent']?.indexOf('RMuserID') > 0)
      return 'app'
    #else if @cookies['k'] # not used
    #  return 'oldapp'
    else if @isWeChat()
      return 'wechat'
    else if @isMobile()
      return 'mobile'
    else
      return 'browser'

  getAppVer: -> @cookies['apsv']

  l10n: (s,ctx)->
    if not @_l10n
      # if @setting?.i18n
      #   @_l10n = @setting.i18n
      # else
      #   # get lang dynamically
      @_l10n = i18n.getFun @locale()
    @_l10n s,ctx

  getTheme: (name)-> # get current theme
    return @theme if @theme
    return null unless themes = @site?.cmate?._themes
    return @theme = themes.getTheme(name) if name
    # 1. setting, 2. site named, 3. baseTheme
    return @theme = themes.getTheme(@setting.theme or @site.name) or themes.getTheme(@site.name) or themes.getTheme()

  renderFile: (name,param)-> # direct return
    # puts "renderFile theme:"
    # puts @param '_theme'
    # putsObj @setting
    # putsObj @getTheme()
    if (th = @getTheme(@param '_theme'))? and (v = th.getFile name,@)
      req = @
      done = (err,data,mime)->
        if err then return req.resp.E500 err
        req.resp.contentMime mime
        req.resp.send data
      # putsObj v # TODO
      if (fn = v.fn)
        if fn.l10n and not @l10n # delayed initialization
          @l10n = @setting.i18n or i18n.getFun @locale()
        try
          checkTimeoutId = helpers.checkTimeoutCheckin 500,->
            console.error "Theme[#{th.name}] view #{name} timeout!"
            console.error v.fn.toString()
          v.fn param,@,(err,str)->
            helpers.checkTimeoutCheckout checkTimeoutId
            done err,str,v.mime
        catch e
          console.error v.fn.toString()
          done e
      else if v.str or v.buf
        done null,(v.str or v.buf),v.mime
      else if v.file and v.stat
        staticProvider.provide_file req,req.resp,v.file,v.stat
      else
        throw new Error "renderFile #{name}: has no str/buf/file"
      return true
    # console.log 'No theme defined an no name!'
    return false

  # get Theme view object
  getThemeView: (viewname)->
    return @getTheme()?.getViewOrFile(viewname,@)

  # render Theme view object
  #TODO: some bug here: u4:Uncaught Exception:Error [ERR_STREAM_WRITE_AFTER_END]: write after end
  renderThemeView: (v,param,callback)->
    if (fn = v.fn)
      if fn.l10n and not @l10n # delayed initialization
        @l10n = @setting.i18n or i18n.getFun @locale()
      try
        html = v.fn param,@,callback
        return html if callback or param.returnString
        @resp.contentMime v.mime
        @resp.send html
      catch e
        console.error v.fn.toString()
        if callback
          callback e
        else
          @resp.E500 e
    else
      if callback
        callback null,(v.str or ''),v.mime
      else if not param.returnString
        @resp.contentMime v.mime
        @resp.send v.str or ''
      return v.str or ''
    return true

  # Theme View only
  render: (viewname,param,callback)->
    if v = @getThemeView viewname
      return @renderThemeView v,param,callback
    callback new Error "Unknown view #{viewname} for theme #{@getTheme()?.name}"
    return false

  widget: (name,param,callback)-> # Widget first, then theme view
    if (w = @site?.widgets[name])
      try
        self = @
        w param,@,(callback or (err,str)->
          if err
            self.resp.E500 err
          else
            self.resp.send str
          )
      catch e
        if callback
          callback e
        else
          @resp.E500 e
      return true
    @render name,param,callback # try render

  ckup: (view,param,callback)-> # ckup for rendering in Theme layout
    @resp.ckup view,param,'none',callback


# TODO: add file downloads support
ExtendedResponse =
  widget: (name,param,callback)-> @req.widget name,param,callback
  render: (name,param,callback)-> @req.render name,param,callback
  renderFile: (name,param)-> @req.renderFile name,param
  # NOTE: 如果clearCookie 时name的path不是/（或者path和浏览器里的不一样）会无法reset name（在浏览器里），可以logout后观察cookie的wk
  clearCookie: (name, path='/')->
    @cookie name, '', { expires: new Date(1) , path}

  cookie: (name, val, options)->
    @cookies ?= {} # cookies go with response object
    options ?= {}
    if options?.maxAge?
      options.expires = new Date(Date.now() + options.maxAge)
    # TODO: hostname,path,httpOnly,secure
    cookie = helpers.serializeCookie(name, val, options)
    @cookies[name] = {value:val,options:options,cookie:cookie}
    cookies = []
    for k,v of @cookies
      cookies.push v.cookie
    if @finished
      console.log "resp.cookie(#{name}:#{val}): Header Sent."
      return
    @setHeader 'Set-Cookie', cookies

  noCache: ->
    if @finished
      console.log "resp.noCache: Header Sent."
      return
    @setHeader 'Cache-Control','no-cache, no-store, must-revalidate'
    @setHeader 'Pragma','no-cache'
    @setHeader 'Expires','0'

  contentType: (type,fallback)->
    if @finished
      console.log "resp.contentType: Header Sent."
      return
    ct = mime.lookup(type,fallback)
    if /^text/.test(ct)
      ct += ("; charset=" + (@charset ?= 'utf-8'))
    @setHeader 'Content-Type',ct

  contentMime: (mime = 'text/html')->
    if @finished
      console.log "resp.contentMime: Header Sent."
      return
    if /^text/.test(mime)
      mime += ("; charset=" + (@charset ?= 'utf-8'))
    @setHeader 'Content-Type',mime

  attachment: (filename)->
    if @finished
      console.log "resp.attachment: Header Sent."
      return
    disposition = 'attachment'
    if filename?
      disposition += ('; filename="' + path.basename(filename) + '"')
    @setHeader 'Content-Disposition', disposition

  redirect: (uri = '/', status = 302, checkPrefix = true)->
    if verbose > 2 then @req.logger.log "Redirect to #{uri}"
    # Relative
    if uri.indexOf('://') < 0
      # Deal with removedPrefix
      if checkPrefix and @req.removedPrefix
        uri = "/" + @req.removedPrefix + uri
      host = @req.host # TODO: Support reverse proxy
      uri = @req.getProtocol() + '://' + host + uri


    ajax = if @req.headers['x-requested-with'] is 'XMLHttpRequest' then true else false
    # Support text/{plain,html} by default
    if ajax
      body = JSON.stringify {redirect: uri}
      status ?= 278
      @setHeader 'Content-Type','application/json'
    else if @req.accepts('html')
      body = '<h3> Redirecting to <a href="' + uri + '">' + uri + '</a></h3>'
      @setHeader 'Content-Type','text/html'
    else
      body = 'Redirecting to ' + uri
      @setHeader 'Content-Type','text/plain'

    # Respond
    @statusCode = status
    @setHeader 'Location', uri
    @end body
    if verbose > 1 then @req.logger.log "Redirect to #{uri} done."
    null

  reply: (err,body)-> # short hand function to reply a request with error or contents
    if err then return @E500 err
    @send body

  send: (body,code)->
    if verbose > 2 then @req.logger.info "Response.send #{code}"
    body_buf = null
    if code
      if 'number' is typeof(code)
        @statusCode = code
      else
        throw new Error "statusCode needs to be a number. Got #{typeof code}"
    if body?
      switch typeof body
        when 'string' #contents
          if not @getHeader('Content-Type')
            @contentType '.html'
        when 'object' #json
          if Buffer.isBuffer body
            body_buf = body
          else if Array.isArray(body) and body[0]? and Buffer.isBuffer(body[0])
            body_buf = Buffer.concat body
          else
            if not @getHeader('Content-Type')
              @contentType '.json'
            body = JSON.stringify body
        else
          throw new Error "Unsupport body type #{typeof body}"
      @charset ?= 'UTF-8'
      if body_buf is null
        body_buf = new Buffer body,@charset
      if not @getHeader('Content-Length')
        @setHeader 'Content-Length',body_buf.length #Buffer.byteLength(body,@charset)
      try
        @end body_buf
      catch e
        # Error [ERR_STREAM_WRITE_AFTER_END] [ERR_STREAM_WRITE_AFTER_END]: write after end
        # some time we get this error, do not throw. but need to log and see what caused this
        console.error @req.url,e
        e.url = @req.url
        if e.code is 'ERR_STREAM_WRITE_AFTER_END'
          console.error e
        else
          throw e
    else
      @statusCode = 204
      @end()
    null

  sendL10n: (str,params...,code)->
    if @req.setting?.i18n
      myi18n = @req.setting.i18n
    else
      # get lang dynamically
      myi18n = i18n.getFun @req.locale()
    s = myi18n str
    s = sprintf s,params... if params?
    @send s,code

  sendData: (data,type)->
    @charset ?= "UTF-8"
    bjs = new Buffer data,@charset
    size = bjs.length
    resp_headers =
      'Content-Type': type + "; charset=" + @charset
      'Content-Length': size
      'Cache-Control': "public max-age=" + 518400 # 6 days
      'Last-Modified': _start_date.toUTCString()
      'ETag': _start_time + '-' + size
    for k,v of resp_headers
      @setHeader k,v
    if not helpers.isModified @req,resp_headers
      return helpers.notModified @,resp_headers
    @end data

  sendJs: (js)-> @sendData js,"text/javascript"
  sendCss: (css)-> @sendData css,"text/css"


  ajax: (view,ctx)->
    @ckup view,ctx,'none'

  ###
  _renderBITheme: (view,ctx,l_ctx,cb)->
    setting = @req.setting
    ctheme = setting.ctheme
    vars = ctheme.vars
    html = ctheme.html.slice 0
    @ckup 'menubar',setting,'none',l_ctx,(err,txt)-> html[vars.menu] = txt or ''
    @ckup 'footer',setting,'none',l_ctx,(err,txt)-> html[vars.footer] = txt or ''
    if vars.comment?
      console.log "SHOW COMMENT"
      @ckup 'comment',setting,'none',l_ctx,(err,txt)-> html[vars.comment] = txt or ''
    @ckup view,ctx,'none',l_ctx,(err,txt)-> html[vars.content] = txt or ''
    html[vars.header] = setting.cheadbar.html
  ###
  builtInTheme: (biTheme,extra_head,view,ctx,l_ctx,cb)->
    resp = @
    req = @req
    setting = req.setting
    vars = biTheme.vars
    html = biTheme.html.slice 0
    return resp.ckup 'menubar',setting,'none',l_ctx,(err,txt)->
      html[vars.menu] = txt or ''
      resp.ckup 'footer',setting,'none',l_ctx,(err,txt)->
        html[vars.footer] = txt or ''
        resp.ckup 'comment',setting,'none',l_ctx,(err,txt)->
          html[vars.comment] = (txt or '') if vars.comment
          resp.ckup view,ctx,'none',l_ctx,(err,txt)->
            html[vars.content] = txt or ''
            resp.ckup 'htmlheader',ctx,'none',l_ctx,(err,txt)->
              html[vars.header] = setting.cheadbar?.html or ''
              css = (txt or '' ) + setting.cheadbar?.css or ''
              if Object.keys(extra_head.css).length > 0
                for k,v of extra_head.css
                  if not v? or v.length > 1024 or v.length is 0
                    css += "<link rel=\"stylesheet\" type=\"text/css\" href=\"#{k}\" />"
                  else
                    css += '<style type="text/css">' + v + '</style>'
              html[vars.css] = css
              if Object.keys(extra_head.js).length > 0
                js = ''
                for k,v of extra_head.js
                  if not v? or v.length > 1024 or v.length is 0
                    js += "<script src=\"#{k}\"></script>"
                  else
                    js += '<script language="javascript" type="text/javascript">' + v + '</script>'
                html[vars.js] = js
              ret = html.join ''
              delete ctx.req
              delete ctx.ckup
              if cb
                return cb null,ret
              else
                resp.send ret

  ckup: (view,ctx,layout,l_ctx,cb)->
    biTheme = null # Built-In Theme
    if not cb?
      if (not l_ctx?) and ('function' is typeof layout)
        cb = layout
        layout = null
      else if 'function' is typeof l_ctx
        cb = l_ctx
        l_ctx = {}
    if verbose > 2 then @req.logger.info "Response.ckup #{view},#{layout}"
    resp = this # for response object
    req = @req
    ckup_error = (e)->
      e.extra = "template error in view/widget '#{view_name or view}' in layout '#{layout_name or layout}' for URL #{req.url}"
      req.logger?.error e
      puts e.stack
      if not resp._header?
        resp.send "Error when rendering the page. Please contact site admin to correct the problem."
    try
      # get view template
      if 'string' is typeof view
        view_name = view
        if layout is 'none' and req.widget view_name,ctx,cb
          return true # found widget or view for the view name
        if (v = req.get_view view_name)?
          view = v # VIEW coffeecup object
        else if (req.site?.widgets) and (v = req.site.widgets[view_name])? # widget
          return req.widget view_name,ctx,(err,ret)->
            if err then return ckup_error err
            resp.ckup ret,null,layout,l_ctx,cb
        else # try Theme View
          themeViewObj = req.getThemeView view_name

      # get layout
      layout ?= '_'
      # site can provide another way to provide default layout
      if 'string' is typeof layout
        layout_name = layout
        if layout is 'none'
          layout = null
        else
          # render theme view/file as layout, if no callback
          if (theme = req.getTheme()) and (m = if theme.getView layout_name then 'render' else if (not cb? and theme.getFile layout_name) then 'renderFile')
            l_ctx = helpers.shallowExtendObject {ckupView:view,ckupParam:ctx},l_ctx
            return req[m] layout_name,l_ctx,cb
          # no layout specified, using build-in theme, no default layout or default layout is from Site.
          if layout_name is '_' and (setting = req.setting) and setting.ctheme and ((not (layout = req.app.get_view('_',true))?) or (layout is req.site?.get_view('_',true)))
            #puts "Use B/I Theme #{req.url}"
            layout = null
            biTheme = setting.ctheme
          else
            layout = req.get_view layout_name,true

      # inital view locals
      # NOTE: abbreviator = null if no dbs, eg. img server
      locals = {ab:abbreviator?.ab,abobj:abbreviator?.abobj} # get full name or object from abbreviation
      setupL10n @req,locals

      # extra head css/js support. For automatic same named css/js
      extra_head = {css:{},js:{}}
      if app = req.app
        appurl = app.url()
        if c = app.get_css('_') then extra_head.css[appurl + '_.css'] = c
        if c1 = app.get_css(view_name) then extra_head.css[appurl + view_name + '.css'] = c1
        if j = app.get_js('_') then extra_head.js[appurl + '_.js'] = j
        if j1 = app.get_js(view_name) then extra_head.js[appurl + view_name + '.js'] = j1
      if c or c1 or j or j1
        locals.extra_html_head = extra_head

      #
      _ckup = (vname,p_ctx,cb)->
        # view render. Called like ckup 'view-name',context-obj
        # dynamic_locals use function instead of the source
        if (part = req.get_view vname)?
          remove_afterfinish = true
          p_ctx ?= {}
          if p_ctx.req
            remove_afterfinish = false
          else
            p_ctx.req = req
            p_ctx.ckup = _ckup
          r = ckup.render part,{context:p_ctx,locals:locals,dynamic_locals:on}
          if remove_afterfinish
            delete p_ctx.req # clear req object, prevent memory leakage
            delete p_ctx.ckup
          cb null,r if cb
          return r
        else if _themeSubView = req.getThemeView vname
          # TODO: if need remove after finish?
          p_ctx.req ?= req
          p_ctx.dynamic_locals = true
          return req.renderThemeView _themeSubView,p_ctx,(err,ret)->
            if err then throw err
            return ret
        else if req.logger?
          req.logger.error "subview '#{vname}' not found"
      locals.ckup = _ckup
      req._ckup ?= _ckup

      # inital view context
      ctx ?= {}
      ctx.req = req # re-assign request every time
      ctx.ckup = _ckup
      ctx.dynamic_locals = true
      view_ctx = ctx
      renderViewContent = ->
        if themeViewObj
          # renderThemeView need callback to prevent resp.send
          return req.renderThemeView themeViewObj,ctx,(err,ret)->
            if err then throw err
            return ret
        else if 'string' is typeof view
          return view
        else
          return ckup.render view,{context:view_ctx,locals:locals,dynamic_locals:on}

      # has layout
      if layout?
        l_ctx ?= {}
        #l_ctx = helpers.shallowExtendObject l_ctx,view_ctx
        l_ctx.vctx = view_ctx
        l_ctx.req = req
        l_ctx.content = renderViewContent
        #resp.send
        ret = ckup.render(layout,{context:l_ctx,locals:locals,dynamic_locals:on})
        delete l_ctx.req
        delete l_ctx.ckup
      else if biTheme
        resp.builtInTheme biTheme,extra_head,view,ctx,l_ctx,cb
      else # no layout
        ret = renderViewContent()
      delete ctx.req
      delete ctx.ckup
      if ctx.returnString
        return ret
      else if cb # return callback
        return cb null,ret
      else
        resp.send ret
    catch e
      ckup_error e
    null
  E500: (error)-> #TODO: send message to admin
    @req.logger.error error
    err = (@ckup "E500",{error:error,returnString:true})
    @send err,500

  E503: (error)->
    @req.logger.error error
    err = (@ckup "E503",{error:error,returnString:true})
    @send err,503

  E404: (error)->
    @req.logger.notfound(@req)
    @send (@ckup "E404",{error:error,returnString:true}),404

  todo: (s)->
    @send "TODO: #{s}",404

# Global functions
setupL10n = (req,target)->
  _i18n = req.setting?.i18n or i18n.getFun req.locale()
  target ?= req
  target._ = _i18n #@req.setting.i18n
  target.__ = (s,a...)->
    s = _i18n s #@req.setting.i18n s
    sprintf s,a...
  target._a = (s,a)->
    s = _i18n s #@req.setting.i18n s
    vsprintf s,a
  target._ab = (id,ctx,noTranslate)->
    if noTranslate
      return abbreviator.ab(id,ctx)
    _i18n abbreviator.ab(id,ctx),ctx # get translated full name from abbreviation

extend_prototype = (methods,object)->
  for n,v of methods
    object.prototype[n] = v
  null

regexp_convert = `function (path, keys) {
  // from connect middleware
    path = path
        .concat('/?')
        .replace(/\/\(/g, '(?:/')
        .replace(/(\/)?(\.)?:(\w+)(?:(\(.*?\)))?(\?)?/g, function(_, slash, format, key, capture, optional){
            keys.push(key);
            slash = slash || '';
            return ''
                + (optional ? '' : slash)
                + '(?:'
                + (optional ? slash : '')
                + (format || '') + (capture || (format && '([^/.]+?)' || '([^/]+?)')) + ')'
                + (optional || '');
        })
        .replace(/([\/.])/g, '\\$1')
        .replace(/\*/g, '(.+)');
    return new RegExp('^' + path + '$', 'i');
}
`

coffeescript_support = ''
_unused = """
  var __slice = Array.prototype.slice;
  var __hasProp = Object.prototype.hasOwnProperty;
  var __bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; };
  var __extends = function(child, parent) {
    for (var key in parent) { if (__hasProp.call(parent, key)) child[key] = parent[key]; }
    function ctor() { this.constructor = child; }
    ctor.prototype = parent.prototype; child.prototype = new ctor; child.__super__ = parent.prototype;
    return child;
  };
  var __indexOf = Array.prototype.indexOf || function(item) {
    for (var i = 0, l = @length; i < l; i++) {
      if (this[i] === item) return i;
    }
    return -1;
  };
"""


scoped = (code) ->
  code = String(code)
  p = code.indexOf('function')
  if p is 1
    code = code.substr 1,(code.length-15)
  else if p isnt 0
    code = "function () {#{code}}"
  code = "#{coffeescript_support} with(locals) {return (#{code}).apply(context, args);}"
  new Function('context', 'locals', 'args', code)

run_in_context = (code,context,fname = 'noname.file') ->
  code = String(code)
  #code = "#{coffeescript_support} with(locals) {#{code};}"
  if verbose > 1 then puts "Run #{fname}"
  #puts code
  # s = vm.createScript code,fname
  s = new vm.Script(code,{filename:fname})
  s.runInNewContext context

# check config parameters
check_config = (cfg) ->
  # check mapbox.address, see:getAddressSearchByMapbox
  if not cfg?.mapbox?.address
    logger.warn 'Warning: No mapbox.address!'
  return

# check if folders exists
check_folder = ()->
  # check folder -> imgstocks
  # cmty * census images location
  imgstocks_path = Path.join(__dirname,'../webroot/public/img/imgstocks')
  if not fs.existsSync(imgstocks_path)
    logger.warn 'Warning: The imgstocks folder does not exists!'
  return



mate.version = '0.1.3'
mate.run = -> coffeemate.run.apply coffeemate, arguments
mate.run_file = -> coffeemate.run_file.apply coffeemate, arguments
mate.parse_config = (cfgfile)->
  if '/' isnt cfgfile.charAt 0
    cfgfile = process.cwd() + '/' + cfgfile
  cfgfile = Path.normalize cfgfile
  if verbose then puts "parse_config Configure File: #{cfgfile}"
  #cfg = require cfgfile
  src = fs.readFileSync cfgfile, 'utf8'
  # puts src
  try
    #puts "Parse config file"
    if cfgfile.endsWith('.coffee')
      code = coffee.compile src,{bare:on}
      vm = require 'vm'
      cfg = vm.runInThisContext code
    else # toml
      cfg = toml.parse(src)
  catch e
    puts "======= Config File Error ======="
    puts e
    # TODO: throw error, sendSMS
  if not cfg.base? or cfg.base is '.' or cfg.base is './'
    cfg.base = Path.dirname fs.realpathSync require.resolve cfgfile
  if cfg.base is 'cwd'
    cfg.base = process.cwd()
  if cfg.verbose > 3 then puts util.inspect cfg
  if cfg.ofe then require('ofe').call()
  cfg

mate.coffeemate = new CoffeeMate()
mate.profile = -> [total_requests,total_processed]
mate.regExit = regExit = (fn)->
  g_before_exit ?= []
  g_before_exit.push fn
mate.run_config = (cfgfile)->
  cfg = @parse_config cfgfile
  check_config(cfg)
  check_folder()
  mate.coffeemate.startup cfg

if require.main is module
  if process.argv.length > 2 and process.argv[0] is 'coffee'
    cfgfile = process.argv[2]
  else
    if process.argv.length > 0
      cfgfile = process.argv[0]
  if cfgfile?
    mate.run_config cfgfile
  else
    puts "Please specify configuration file."
