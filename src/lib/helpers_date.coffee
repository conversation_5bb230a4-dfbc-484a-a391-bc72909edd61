require 'date-utils' # date utils
DEFAULT_TIMEZONE = 'America/Toronto'
VANCOUVER_TIMEZONE = 'America/Vancouver'
moment = require('moment-timezone')
debugHelper = require './debug'
debug = debugHelper.getDebugger()

###
#moment.tz(dt,tz),  if there's zone in dt string, use the zone, and then conver to the tz
#if no zone in dt string, use zone of tz
obj = {
  year: 1995,
  month: 11,
  day: 18,
}
console.log moment.tz(obj,DEFAULT_TIMEZONE).format()
#1995-12-18T00:00:00-05:00

console.log moment.tz(obj,'America/Los_Angeles').format()
#1995-12-18T00:00:00-08:00


console.log  moment.tz('Mon, 18 Dec 1995 17:28:35 GMT',DEFAULT_TIMEZONE).format()
#1995-12-18T12:28:35-05:00
####

###
##################### Boolean ####################
###
Check if input string is a vaild data and in yyyy-mm-dd format
@param {string} string
@return {boolean}
###
module.exports.isDate = (string)->
  # check format and is number
  if not /^\d{4}\-\d{1,2}\-\d{1,2}$/.test(string)
    return false
  dateArray = string.split('-')
  dateArray.forEach (number)->parseInt(number,10)
  [year,month,day] = dateArray
  # check year and month
  if (year < 1000) or (year > 3000) or (month > 12)
    return false
  monthLength = [ 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 ]
  # leap years
  if(year % 400 is 0 ) or (year % 100 isnt 0 and year % 4 is 0)
    monthLength[1] = 29
  # check day
  return day > 0 and day <= monthLength[month - 1]

module.exports.dateFormat = dateFormat = (dt,format)->
  if 'number' is typeof dt
    dt = new Date(dt)
  if (not format?)
    if 'string' is typeof dt
      format = dt
      dt = null
  dt ?= new Date()
  format ?= 'YYYY-MM-DD HH24:MI:SS'
  if 'string' is typeof dt
    dt = new Date(dt)
  # if format error, retrun format,
  # dateFormat('1/22/2018')  = 1/22/2018
  # eg: dateFormat('1/22/2018','YYMMDD')  = 20180122
  #eg: dateFormat('1/22/2018','YY-MM-DD')  = 2018-01-22
  #console.log dt.toFormat format
  return dt.toFormat format

module.exports.dateAdd = (dt,add)-> dt.add add


###
@param {number, string} dStart
@param {number, string} dEnd
return day diff of input start and end
###
module.exports.dayDiff = dayDiff = (dStart,dEnd)->
  dStart = inputToDate dStart
  dEnd = inputToDate dEnd
  # inputToDate may return null or non-date object
  if (not dStart?.getTime) or (not dEnd?.getTime)
    return null
  timeDiff = Math.abs(dStart.getTime() - dEnd.getTime())
  return Math.floor(timeDiff / (1000 * 3600 * 24))

#input is date, converto number
module.exports.date2num = date2num = (date)->
  return null unless date
  (date.getFullYear() * 10000) + ((date.getMonth() + 1) * 100) + date.getDate()

#20200829-> 2020-08-29
module.exports.dateNum2str = (num)->
  d = num.toString()
  year = d.slice 0, 4
  month = d.slice 4, 6
  day = d.slice 6, 8
  "#{year}-#{month}-#{day}"


#08/28/20 -> new Date('2020-08-28')
#["08/28/20", "08", "28", "20"]
module.exports.mdy2date = mdy2date = (str)->
  if m = str?.match /^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/
    year = if m[3].length > 2 then m[3] else '19' + m[3]
    month = if m[1].length > 1 then m[1] else '0' + m[1]
    day = if m[2].length > 1 then m[2] else '0' + m[2]
    return new Date("#{year}-#{month}-#{day}T05:00:00Z")
  null

# 5-4-2020 10:24:20 AM ->
# 5/4/2020 10:24:20 AM
module.exports.ymd2date = ymd2date = (str)->
  if (m = str.match /(\d+)\-(\d+)\-(\d+)\s+(\d+)\:(\d+)\:(\d+)\s*(AM|PM)?/i)
    h = if m[7]?.toUpperCase() is 'PM' then parseInt(m[4],10) + 12 else m[4]
    r = "#{m[1]}-#{m[2]}-#{m[3]}T#{h}:#{m[5]}:#{m[6]}"
    debug.debug r
    return new Date(r)
  null

module.exports.showTs = (oldTs,str = 'MS')->
  newTs = Date.now()
  debug.debug str + (newTs - oldTs) + 'ms'
  newTs

module.exports.formatDateTime = (date) ->
  year = date.getUTCFullYear()
  month = pad date.getUTCMonth() + 1
  day = pad date.getUTCDate()
  hour = pad date.getUTCHours()
  minute = pad date.getUTCMinutes()
  second = pad date.getUTCSeconds()
  return year+month+day+'T'+hour+minute+second+'Z'

module.exports.getTheCurrentDate = (date) ->
  date = new Date(date)
  year = date.getFullYear()
  month = pad date.getMonth() + 1
  day = pad date.getDate()
  return parseInt year+month+day

module.exports.getTheCurrentTime = (date) ->
  date = new Date(date)
  hour = pad date.getHours()
  minute = pad date.getMinutes()
  return hour + ':' + minute

pad = (i) ->
  if i < 10 then i='0'+i else i=''+i
  return i

monthNames = [
  ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  ['January', 'February', 'March', 'April', 'May', 'June',\
  'July','August', 'September', 'October', 'November', 'December']
  ]
monthNamesToNum = {}
for  arr in monthNames
  for i of arr
    value = arr[i]
    monthNamesToNum[value] = parseInt(i)+1

###
get number from string
input: 'Jan','January',return 1
input 'Aug', return 8
input 'November', return 11
###
module.exports.getMonthNamesToNum = getMonthNamesToNum = (monthStr)->
  return monthNamesToNum[monthStr]

module.exports.num2month = (n,full)-> monthNames[if full then 1 else 0][n]

module.exports.monthNameAndDate = (t) ->
  # eg: '2021-02-01T05:00:00.000Z' -> Feb.1
  return '' unless t
  d = new Date(t)
  return monthNames[0][d.getMonth()]+'.'+d.getDate()

module.exports.dotdate = dotdate = (d,isCh) ->
  # return 2021.5.27
  return '' unless d
  if ('number' is typeof d)
    d += ''
    d = "#{d.slice(0,4)}/#{d.slice(4,6)}/#{d.slice(6,8)}"
  #not supported by ios
  #isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream
  if (('string' is typeof d) and !/\d+Z/.test(d))
    d += ' EST'
  split1 = if isCh then '年' else '.'
  split2 = if isCh then '月' else '.'
  split3 = if isCh then '日' else ''
  
  if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) and !/\d+Z/.test(d))
    d2 = d.split(' ')[0].split('-')
    if isCh
      return d2[0]+split1+d2[1]+split2+d2[2]+split3
    return "#{d2[0]}#{split1}#{pad(parseInt(d2[1]))}#{split2}#{pad(parseInt(d2[2]))}#{split3}"
  t = new Date(d)
  return d if (!t or isNaN( t.getTime() ))
  if isCh
    return "#{t.getFullYear()}#{split1}#{t.getMonth()+1}#{split2}#{t.getDate()}#{split3}"
  return "#{t.getFullYear()}#{split1}#{pad(t.getMonth()+1)}#{split2}#{pad(t.getDate())}#{split3}"

module.exports.formatCmntTs = formatCmntTs = (ts,l10n) ->
  #return 3s ago, 5 mins ago, 5hrs ago, Feb 27
  return '' unless ts
  monthArray = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec']
  ts = new Date(ts)
  diff = new Date() - ts
  if (diff < 60000)
    return l10n('Just now','forum')
  else if (diff >= 24*3600*1000)
    return l10n(monthArray[ts.getMonth()]) + ' ' + ts.getDate()
  else if (diff > 3600*1000)
    return parseInt(diff / (3600*1000)) + l10n('Hrs','forum')
  else
    return parseInt(diff/ (60 * 1000)) + l10n('Mins','forum')

module.exports.formatUpdatedMt = (ts, l10n) ->
  if dayDiff(ts, new Date()) > 24
    return dotdate ts
  return formatCmntTs ts, l10n

module.exports.getAppendedDate = getAppendedDate = ({date,updatedTime})->
  updatedTime ?= 2592000000 #1000*30*24*3600 one month
  date = if date then new Date(date) else new Date()
  return new Date(date.getTime()+updatedTime)

setObjectFromMatchYMD = (m)->
  obj = {}
  return obj unless m
  obj.year = parseInt m[1]
  obj.month = parseInt m[2]
  obj.day = parseInt m[3]
  if m[4]
    if m[7]?.toUpperCase() is 'PM'
      obj.hour =  parseInt(m[4],10) + 12
    else
      obj.hour = parseInt m[4]
  obj.minute = parseInt m[5] if m[5]
  obj.second = parseInt m[6] if m[6]
  obj

gYmdReg5 = /(\d{4})\-(\d{2})\-(\d{2})\T(\d+)\:(\d+)\:(\d+)$/i

# @input utcDate '2025-01-04T19:00:00Z'
# @output date as format '2025-01-04 14:00'
module.exports.formatUTC2EST = (utcDate,format='YYYY-MM-DD HH:mm')->
  return moment(utcDate).tz(DEFAULT_TIMEZONE).format(format)

###
# 将UTC时间转换为温哥华时区
# @param {String} utcDate - UTC时间字符串，格式如 '2025-01-04T19:00:00Z'
# @param {String} format - 输出格式，默认为 'YYYY-MM-DD HH:mm'
# @return {String} 温哥华时区的时间字符串
###
module.exports.formatUTC2Vancouver = (utcDate,format='YYYY-MM-DD HH:mm')->
  return moment(utcDate).tz(VANCOUVER_TIMEZONE).format(format)

parseStringDateInput = (dt,dayFirst,tz=DEFAULT_TIMEZONE)->
  obj = {}
  #eg: 2022/01/2019 13:35 PM
  ymdReg1 = /(\d{4})\/(\d+)\/(\d+)\s+(\d+)\:(\d+)\:(\d+)\s*(AM|PM)$/i

  #eg: 2020-12-20 : 13:35 AM
  ymdReg2 = /(\d+)\-(\d+)\-(\d+)\s+(\d+)\:(\d+)\:(\d+)\s*(AM|PM)?$/i

  #eg 2020-12-20
  ymdReg3 = /^(\d{4})-(\d{2})-(\d{2})$/

  # 20201002
  ymdReg4 = /^(\d{4})(\d{2})(\d{2})$/

  ###
  #eg: 20/12/20 : AM|PM
  const str = new Date('2021-1-31').toLocaleString('en-US', {day:'numeric',month:'numeric',year:'numeric',
  hour: 'numeric',
  minute: 'numeric',
  hour12: true
  })
  console.log(str);
  VM255:6 1/31/2021, 12:00 AM
  ###
  mdyAndHour = /(\d{1,2})\/(\d{1,2})\/(\d{4})\:(\d+)\:(\d+)\s*(AM|PM)$/i

  #Bcre SoldDate 5/11/2018  MM/DD/YYYY
  #DDF ListingContractDate: 14/02/2012 DD/MM/YYYY
  dmyReg = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/#
  

  #/Date(1604954914000-0500)/ from treb manual
  trebReg = /\/Date\((\d+)\-(\d+)\)\//
  #str = '/Date(12-345)/'
  #"/Date(12-345)/"
  #str.match(/\/Date\((\d+)\-(\d+)\)\//)  ["/Date(200902-345123)/", "12", "345"

  # 日月，有一个大于12 的是date，
  # monthFirst or dataFirst.
  # todo:Thu, 17 Sep 2015 02:04:13
  #月大于12，日大于31，错误的返回errors

  if m = dt.match(ymdReg1) \
    or dt.match(ymdReg2) \
    or dt.match(ymdReg3) \
    or dt.match(ymdReg4) \
    or dt.match(gYmdReg5)
      obj = setObjectFromMatchYMD m
  else if m = dt.match(mdyAndHour) or dt.match(dmyReg)
    if m[1]>12
      obj.day = parseInt m[1]
      obj.month = parseInt m[2]
    else if m[2]>12
      obj.day = parseInt m[2]
      obj.month = parseInt m[1]
    else if dayFirst
      obj.day = parseInt m[1]
      obj.month = parseInt m[2]
    else # default month first
      obj.day = m[2]
      obj.month = parseInt m[1]
    obj.year = parseInt m[3]
    if m[4]
      obj.hour = if m[7]?.toUpperCase() is 'PM' then parseInt(m[4],10) + 12 else parseInt m[4]
    obj.minute = parseInt m[5] if m[5]
    obj.second = parseInt m[6] if m[6]
  ##/Date(1604954914000-0500)/ from treb manull
  else if m = dt.match(trebReg) # to confirm.
    obj = parseDateWithMoment (new Date(parseInt m[1])),tz
  else
    ##'2013-06-01T00:00:00+00:00'
    #'2013-06-01T00:00:00-04:00'
    #"May 12th 2014 8PM"
    obj = parseDateWithMoment dt,tz
  return obj

parseDateWithMoment = (dt,tz)->
  obj = {}
  try
    dtFormatStr = moment.tz(dt,tz).format()
  catch error
    debug.error 'catch error,dt is invalid format',error, dt
    return null
  if dtFormatStr
    dtFormatStr = dtFormatStr.substring(0,dtFormatStr.length-6)
    if m = dtFormatStr.match(gYmdReg5)
      obj = setObjectFromMatchYMD m
    else
      debug.error 'dt is invalid format',dt,'dtFormatStr',dtFormatStr
  else
    debug.error 'dt is invalid format, no dtFormatStr',dt
  obj

###
  @param  {number,date,string} input date in any format
  @param  {boolean} monthFirst is Month First before day
  @param {string} tz - timezone, default is toronto
  return {year:,month:up to 12, day: , hour: min:}
###
exports.parseDateInput = parseDateInput = (dt,dayFirst,tz=DEFAULT_TIMEZONE)->
  # debug.debug 'parseDateInput dt,monthFirst',dt,monthFirst,
  obj = {}
  if 'string' is typeof dt
    obj = parseStringDateInput dt,dayFirst,tz
  else if ('number' is typeof dt) and ((''+dt).length is 8)
    obj.day = dt % 100
    obj.month = (Math.floor(dt/100))%100
    obj.year = Math.floor(dt/10000)
  else
    obj = parseDateWithMoment dt, tz
  return unless obj
  if (obj.month > 12) or (obj.day > 31) or (obj.year < 1900)
    debug.error 'bad date', dt,obj
    return null
  return obj


###
@param {date,number,string,long} dt - date time in any format
@param {boolean} dayFirst- true if day is before month
@param {string} tz - timezone
###
exports.inputToDate = inputToDate = (dt,dayFirst=false,tz=DEFAULT_TIMEZONE)->
  return dt if dt instanceof Date
  obj = parseDateInput(dt,dayFirst,tz)
  return dt unless obj?.year
  # in object, month is up to 12, subtract one when pass to moment
  obj.month = obj.month - 1
  return new Date(moment.tz(obj,tz))

###
@param {date,number,string,long} dt - date time in any format
@param {boolean} dayFirst- true if day is before month
@param {string} tz - timezone
return input to 8 number 20210828
###
exports.inputToDateNum = inputToDateNum = \
(dt,dayFirst=false,tz=DEFAULT_TIMEZONE)->
  return dt if /(d{8})/.test(''+dt)
  obj = parseDateInput(dt,dayFirst,tz)
  return dt unless obj?.year
  return (parseInt(obj.year) * 10000) + \
    (parseInt(obj.month) * 100) + parseInt(obj.day)
###
#2023/06/01 13:35 PM-> 2023-06-21
# @param {string} date
###
module.exports.date2str = (date)->
  if date
    d = new Date(date)
  else
    d = new Date()
  year = d.getFullYear()
  month = pad d.getMonth() + 1
  day = pad d.getDate()
  "#{year}-#{month}-#{day}"

# 将24小时制的时间转换成12小时制(包含处理24:60:60这种数据)
# eg:2024-12-23(日期格式不管,也可以不存在) 13:00:00 -> 2024-12-23 1:00:00 PM
# eg:00:00:00 -> 0:00:00
# eg:2024-12-23 12:00:00 -> 2024-12-23 12:00:00 PM
# eg:2024-12-23 06:00:00 -> 2024-12-23 6:00:00 AM
# eg:2024-12-23( 24:60:60 -> 2024-12-23 12:59:59 AM
# 如果给入的不是时间格式，则如果有日期返回日期，否则返回null
exports.convert24HoursTo12Hours = convert24HoursTo12Hours = (origTime)->
  unless origTime
    return null
  separDate = origTime.toLocaleString().split(' ')
  date = ''
  if separDate.length > 1 #如果前面有日期的话去掉日期部分
    time = separDate[1]
    date = separDate[0]
  else
    time = separDate[0]
  # 判断时间格式是否正确，并分组
  # NOTE: 判断时间的正则表达式原本应该是/^([01]\d|2[0-3])(:[0-5]\d)(:[0-5]\d)?$/,但是因为之前的数据解析错误，可能存在24:60:60的情况
  time = time.match(/^([01]\d|2[0-4])(:[0-6]\d)(:[0-6]\d)?$/) or [time]
  if (time.length > 1)
    time = time.slice(1)
    if time[0] and (time[0] is '24')
      time[0] = '00'
    if time[1] and (time[1] is ':60')
      time[1] = ':59'
    if time[2] and (time[2] is ':60')
      time[2] = ':59'
    time[0] = Number(time[0])
    tp = 'AM'
    if time[0] > 12
      tp = 'PM'
      time[0] = time[0] - 12
    else if time[0] is 12
      tp = 'PM'
    else if ((time[0] is 0) or (time[0] is 24))
      tp = ''
      time[0] = 0
    joinTime = "#{date} #{time.join('')} #{tp}"
    return joinTime.trim()
  ymdReg = /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/ # 2024/3/29
  ymdReg1 = /^(\d{4})-(\d{2})-(\d{2})$/ #2024-01-02
  ymdReg2 = /^(\d{4})(\d{2})(\d{2})$/ # 20201002
  ymdReg3 = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/ # 3/11/2024
  if time[0] and (ymdReg.test(time[0]) or ymdReg1.test(time[0]) or ymdReg2.test(time[0]) or ymdReg3.test(time[0]))
    return origTime
  return null # 不是时间格式

# 特殊处理ohz时间，如果小于6:00AM，可能数据不对，后面不要加AMPM，让用户自己判断
exports.specialDealOhzTime = specialDealOhzTime = (origTime)->
  origTime = convert24HoursTo12Hours(origTime)
  unless origTime
    return null
  separTime = origTime.split(' ')
  date = ''
  time = null
  regex = /^([0-9]|1[0-2])(:[0-6]\d)(:[0-6]\d)?$/g
  for item,i in separTime
    if regex.test(item) # 是12进制时间格式
      time = item
      date = separTime[i-1] if separTime[i-1]
      tp = separTime[i+1] if separTime[i+1]
      break
  unless time
    return origTime
  timeSplit = time.split(':')
  if timeSplit[0] and (tp is 'AM') and (Number(timeSplit[0]) < 6)
    return "#{date} #{time}"
  return origTime

exports.OhzArrConve24HoursTo12Hours = (ohzArr)->
  showOhz = []
  for item in ohzArr
    ohzObj = Object.assign({},item)
    if ohzObj.f
      f = specialDealOhzTime(ohzObj.f)
      ohzObj.f = if f then f else ''
    if ohzObj.t
      t = specialDealOhzTime(ohzObj.t)
      ohzObj.t = if t then t else ''
    showOhz.push ohzObj
  return showOhz

###
# 如果是今年的年份就按 formatCmntTs 函数的格式显示，否则显示年.月.日
# @param {string} date - new Date()可以转换的日期
# @param {function} l10n - 翻译
###
module.exports.formatMtBasedOnYear = (ts, l10n) ->
  unless ts
    return null
  tsDate = new Date(ts)
  nowDate = new Date()
  if tsDate.getFullYear() isnt nowDate.getFullYear()
    return dotdate ts
  return formatCmntTs ts, l10n

###
# 将unix时间戳转换成
# @param {} date - unix时间戳
# @return UTC 时间
###
module.exports.unixTimestampToUTC = (unixTime) ->
  unless unixTime
    return null
  date = new Date(unixTime * 1000)
  if (isNaN(date.getTime()))
    return null
  return date.toISOString()

###
# 将给定的日期时间转换成整点数。eg: 2024-06-17 14:30:59 -> 2024-06-17 14:00:00
# @param {} origTime - new Date 可以通过的时间
# @return 年-月-日 小时:00:00
###
module.exports.convertToAnHourTime = (origTime) ->
  unless origTime
    return null
  origTime = new Date(origTime)
  if (isNaN(origTime.getTime()))
    return null
  year = origTime.getFullYear()
  hours = origTime.getHours()
  data= origTime.getDate()
  month = origTime.getMonth()+1
  return "#{year}-#{month}-#{data} #{hours}:00:00"

# 获取当前EST时间，并根据传入的转换格式返回,eg: format('YYYY-MM-DD HH:mm:ss'), 返回'2024-12-29T08:57:40-05:00'
module.exports.getESTDate = (formatStr) ->
  if formatStr
    return moment.tz('America/New_York').format(formatStr)
  return moment.tz('America/New_York').format()
###
# 根据当前月份生成近12个月+近5年的数组
# @return [this month,202408,202407...,202309,this year,2023,2022,2021,2020,2019]
###
module.exports.getMonthAndYearArrayForClaim = ()->
  monthArray = []
  monthArray.push 'This month'
  monthArray = monthArray.concat(getMonthYearArray(12))
  monthArray.push 'This year'
  monthArray = monthArray.concat(getYearsArray(5))
  return monthArray
###
  根据传入的月份数，生成近几个月的数组
  @param {number} monthsCount - 近几个月的数组
  @return [202408,202407...,202309].length = monthsCount
###
module.exports.getMonthYearArray = getMonthYearArray = (monthsCount) ->
  nowDate = new Date()
  nowYear = nowDate.getFullYear()
  nowMonth = nowDate.getMonth()+1
  monthArray = []
  for i in [0..monthsCount-1]
    nowMonth--
    if nowMonth is 0
      nowMonth = 12
      nowYear--
    monthArray.push "#{nowYear}#{pad nowMonth}"
  return monthArray
###
  根据传入的年份数，生成近几年的数组
  @param {number} yearsCount - 近几年的数组
  @return [2024,...,2019].length = yearsCount
###
module.exports.getYearsArray = getYearsArray = (yearsCount) ->
  nowDate = new Date()
  nowYear = nowDate.getFullYear()
  yearsArray = []
  for i in [0..yearsCount-1]
    yearsArray.push (nowYear-i).toString()
  return yearsArray
