path = require 'path'
APN = require 'apn'
libAsync = require 'async'
helpers = require './helpers'
debugHelper = require './debug'
fcmV1Http2 = require('fcm-v1-http2')
debug = debugHelper.getDebugger('pushNotify')

CHUNKSIZE = 100

# this does not set badge number and send all pn in batch
sendToIOSBatch = ()->

###*
 * 发送 iOS 推送通知 - 批量发送版本
 * @param {Object} options - 发送选项
 * @param {Object} options.note - 单个通知对象
 * @param {Array} options.anTokens - 设备令牌数组
 * @param {Object} options.apnProvider - APN 提供者实例
 * @param {string} options.prefix - token 前缀
 * @returns {Object} 发送结果，包含 failed 和 success 数组
###
sendToIOS = ({note, anTokens, apnProvider, prefix})->
  unless apnProvider
    return {errMsg:'No apn provider'}
  
  try
    res = await apnProvider.send(note, anTokens)
  catch error
    debug.error 'sendToIOS error:', error
    return {errMsg: error}
  
  failed = []
  success = []
  
  if res?.sent?.length
    for ret in res.sent
      if token = ret.device
        token = prefix + token
        success.push token

  if res?.failed?.length
    for ret in res.failed
      if token = ret.device
        err =
          timestamp: new Date()
          token: token
          reason: ret.response?.reason
          status: ret.status
        token = prefix + token
        failed.push {token, err}
  
  return {failed, success}

# invoke fcm with fcmV1http2
# FCM format: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages?hl=zh-cn#Message
# type note {
#   url:string
#   title:string
#   message:string
#   body:string
#   image:string
#   priority:string
# }
convertToFcmV1Message = (note)->
  unless note
    return null
  note ?= {}
  {url,title,message,body,image,priority} = note
  priority ?= 'high'
  ret =  {
    # fcm data field, customizable
    data:{
      url:url
      title:title
      message:message
      body:body
    }
    # fcm base notification format, uncustomizable
    notification:{
      title:title
      body:body
      # image:image
    }
    android:{
      priority:priority
    }
    # TODO: fcm_options
  }
  # TODO: support token/topic/condition
  return ret
# invoke fcm using fcm-v1-http2
# anTokens is an array of token, token format without prefix(android:fcm:)
sendToAndroidFcmAsync = ({fcmNote,anTokens,fcmSender,prefix})->
  note = convertToFcmV1Message fcmNote
  unless fcmSender
    return throw new Error('No fcmSender v1')
  # console.log '+++++note',note,anTokens
  failed = []
  success = []
  try
    unregisteredTokens = await fcmSender.sendMulticast(note, anTokens)
  catch err
    debug.error('Sending fcmv1 failed:', err)
  # Sending successful
  # console.log('fcm Message sent successfully', unregisteredTokens)
  # Remove unregistered tokens from your database
  unregisteredTokens ?= []
  success = anTokens.filter((token) -> token not in unregisteredTokens)
  # add android:fcm: to each token, for remove error token in pn_token db
  success = success.map((token) ->
    return prefix+token
  )
  if unregisteredTokens.length > 0
    for token in unregisteredTokens
      failed.push {
        timestamp:new Date()
        token:prefix+token
        reason:'unregistered'#ret.erro
      }
  return({failed, success})
  # console.log('Unregistered device token(s): ', unregisteredTokens.join(', '))
  

class PushNotify
  constructor:(config)->
    @gcmSender = null
    @fcmSender = null
    @fcmV1Sender = null
    @apnProvider = null
    @noticeID = 1
    @setupIOSPnService(config)
    @setupAndroidPnService(config)
  # apn config for apple push notification service
  setupIOSPnService:(config)->
    if not config.apn
      debug.error 'No APN Configuration'
      if config.serverBase?.developer_mode
        return console.log 'Developer mode no push confg!'
    apn_config = config.apn
    apn_config.key = path.resolve config.serverBase?.base,apn_config.key if apn_config.key
    apn_config.cert = path.resolve config.serverBase?.base,apn_config.cert if apn_config.cert

    @topic = config.apn.topic if config.apn?.topic
    if APN.Provider
      @apnProvider = new APN.Provider(apn_config) if apn_config.cert
    else
      debug.error 'APN connection depreciated, please use new apn lib'
  # fcm config for firebase cloud messaging
  setupAndroidPnService:(config)->
    # use this
    if config.fcmv1_config
      @fcmV1Sender = new fcmV1Http2(
        serviceAccount: require(config.fcmv1_config.json)
        maxConcurrentConnections: 10
        maxConcurrentStreamsAllowed: 100
      )
    else
      debug.error 'No GCM/FCM Configuration'
  # invoke push notification, use apn or fcm automatically
  ###*
   * 主推送通知方法
   * @param {Object} notice - 通知对象
   * @returns {Object} 推送结果，包含错误列表、失败列表、成功列表
  ###
  pushNotify: (notice)->
    # 参数验证
    unless notice?.to and notice.from and (notice.sbj or notice.msg)
      return {errMsg:'Bad Parameters'}
    unless notice.id
      notice.id = @noticeID++
    unless notice.cnt
      notice.cnt = 1
    if /^http/.test notice.url
      loc = helpers.getLocation(notice.url)
      notice.url = loc.pathname+loc.search
    
    # 处理设备数据
    deviceData = @processDevices(notice)
    if deviceData.errMsg
      return deviceData
    
    # 初始化结果列表
    failedList = []
    successList = []
    errorList = []
    
    # 发送到 iOS 设备
    if Object.keys(deviceData.iosBadgeGroups).length > 0
      iosResult = await @sendToIOSDevices(notice, deviceData.iosBadgeGroups)
      if iosResult.errorList?.length
        errorList = errorList.concat(iosResult.errorList)
      if iosResult.failedList?.length
        failedList = failedList.concat(iosResult.failedList)
      if iosResult.successList?.length
        successList = successList.concat(iosResult.successList)
    
    # 发送到 Android FCM 设备
    if deviceData.androidFcmDevices.length > 0
      fcmResult = await @sendToAndroidFcmDevices(notice, deviceData.androidFcmDevices)
      if fcmResult.errorList?.length
        errorList = errorList.concat(fcmResult.errorList)
      if fcmResult.failedList?.length
        failedList = failedList.concat(fcmResult.failedList)
      if fcmResult.successList?.length
        successList = successList.concat(fcmResult.successList)
    
    # 处理传统 Android 设备（已不支持）
    if deviceData.androidDevices.length > 0
      debug.warn 'androidDevices with gcm no longer supported', deviceData.androidDevices
    
    return {errorList, failedList, successList}

  ###*
   * 处理设备分类和数据准备
   * @param {Object} notice - 通知对象
   * @returns {Object} 设备数据，包含分类后的设备列表和相关映射
  ###
  processDevices: (notice)->
    # 处理 tokens
    if 'string' is typeof notice.to
      tokens = [notice.to]
    else if Array.isArray notice.to
      tokens = notice.to
    else
      tokens = []
      return {errMsg:"Unsupported to type: #{typeof notice.to}"}
    
    # 按设备类型分组，iOS设备按badge进一步分组优化
    iosBadgeGroups = {}  # 直接按badge分组iOS设备
    androidDevices = []
    androidFcmDevices = []
    
    for apu in tokens
      # 检查设备标识格式
      if not apu or typeof apu isnt 'string'
        debug.error "Invalid device token: #{apu}"
        continue
        
      # 处理 iOS 设备，直接按badge分组
      if apu.startsWith('ios:')
        deviceToken = apu.substring(4)
        if deviceToken.length > 0
          badge = notice.pnBadgeMap?[apu] ? 1
          iosBadgeGroups[badge] ?= []
          iosBadgeGroups[badge].push deviceToken
      # 处理 Android FCM 设备
      else if apu.startsWith('android:fcm:')
        deviceToken = apu.substring(12)
        if deviceToken.length > 0
          androidFcmDevices.push deviceToken
        else
          debug.error "Empty Android FCM device token: #{apu}"
      
      # 处理传统 Android 设备
      else if apu.startsWith('android:')
        deviceToken = apu.substring(8)
        if deviceToken.length > 0 and not /BLACKLISTED/.test(apu)
          androidDevices.push deviceToken
        else if /BLACKLISTED/.test(apu)
          debug.warn "Blacklisted Android device: #{apu}"
        else
          debug.error "Empty Android device token: #{apu}"
      
      else
        debug.error "Unknown device type: #{apu}"
    
    return {
      iosBadgeGroups,
      androidDevices,
      androidFcmDevices
    }

  ###*
   * 发送推送通知到 iOS 设备
   * @param {Object} notice - 通知对象
   * @param {Object} iosBadgeGroups - iOS 设备按 badge 分组的对象
   * @param {number} limit - 每批发送的最大设备数
   * @returns {Object} 发送结果
  ###
  sendToIOSDevices: (notice, iosBadgeGroups, limit) ->
    returnData = {
      errorList: [],
      failedList: [],
      successList: []
    }
    return returnData unless Object.keys(iosBadgeGroups).length
    
    topic = @topic
    # 为每个badge组创建通知并批量发送
    for badge, tokens of iosBadgeGroups
      # 复制数组避免修改原数组
      deviceTokens = tokens.slice()
      
      # 按CHUNKSIZE分批处理每个badge组
      while deviceTokens.length > 0
        chunkTokens = deviceTokens.splice(0, limit or CHUNKSIZE)
        
        # 为这个badge组创建一个通知对象
        payload = messageFrom: notice.from, id: notice.id
        if notice.url then payload.url = notice.url
        
        apnNote = new APN.Notification()
        apnNote.expiry = Math.floor(Date.now() / 1000) + 3600 # 1H
        apnNote.badge = parseInt(badge) or notice.cnt or 1
        apnNote.sound = 'al1'
        apnNote.alert = notice.msg
        apnNote.payload = payload
        apnNote.topic = topic if topic
        if notice.title or notice.from
          apnNote.title = notice.title or notice.from
        
        # 使用批量发送
        iosResult = await sendToIOS {
          note: apnNote,
          anTokens: chunkTokens,
          prefix: 'ios:',
          apnProvider: @apnProvider
        }
        
        if iosResult?.errMsg
          returnData.errorList.push iosResult?.errMsg
          break
        returnData.failedList = returnData.failedList.concat(iosResult?.failed) if iosResult?.failed?.length
        returnData.successList = returnData.successList.concat(iosResult?.success) if iosResult?.success?.length
    
    return returnData

  ###*
   * 发送推送通知到 Android FCM 设备
   * @param {Object} notice - 通知对象
   * @param {Array} androidFcmDevices - Android FCM 设备令牌列表
   * @param {number} limit - 每批发送的最大设备数
   * @returns {Object} 发送结果
  ###
  sendToAndroidFcmDevices: (notice, androidFcmDevices, limit) ->
    returnData = {
      errorList: [],
      failedList: [],
      successList: []
    }
    return returnData unless androidFcmDevices.length
    
    # 复制设备列表以避免修改原数组
    devices = androidFcmDevices.slice()
    
    # 创建 FCM 通知对象
    fcmNote =
      sound: 'al1'
      title: notice.from
      message: notice.msg
      msgcnt: notice.cnt
      notId: notice.id
      url: notice.url
      color: '#E03131'
      tag: notice.id
      body: notice.msg
      priority: 'high'
    
    # 使用 while 循环处理所有设备，包括超过 limit 的部分
    while devices.length > 0
      anTokens = devices.splice(0, limit or CHUNKSIZE)
      # 过滤空token，确保只处理有效的字符串token
      validTokens = anTokens.filter((token) -> token and typeof token is 'string')
      
      try
        fcmResult = await sendToAndroidFcmAsync {
          fcmNote: fcmNote,
          prefix:'android:fcm:',
          fcmSender:@fcmV1Sender,
          anTokens: validTokens
        }
      catch error
        debug.error 'sendToAndroidFcm error:',error
        returnData.errorList.push error
        break
      
      returnData.failedList = returnData.failedList.concat(fcmResult?.failed) if fcmResult?.failed?.length
      returnData.successList = returnData.successList.concat(fcmResult?.success) if fcmResult?.success?.length
    
    return returnData


exports.createInstance = createInstance = (config)->
  pushNotify = new PushNotify(config)
  return pushNotify
