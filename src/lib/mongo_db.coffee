###
supports up to npm mongodb 3.7.0
for mongodb 4.0.0 MongoDriverError: The callback form of this helper has been removed
> mongodb 4.10.0 use mongo4.js
###
Promise = require 'promise'
util = require 'util'
fs = require 'fs'
mongodb = require 'mongodb'
ObjectID = mongodb.ObjectID
MongoClient = require('mongodb').MongoClient
helpersString = require './helpers_string'
helpersFunction = require './helpers_function'

DModule = require './dmodule'
RESOURCE_NAME = '__mongo_db_resource'
puts = console.log
verbose = 0
servers = {}
dbClients = {}
all_ready = false
expected_connections = 0
alertHandler = null
bServiceStarted = false
debugHelper = require './debug'
debug = debugHelper.getDebugger()

MAX_DB_ACCESS_TIME = 6000


INSERT_METHODS = 'insertOne|insert|insertAll|insertMany|save'.split('|')
UPDATE_METHODS = 'update|updateOne|updateMany|findOneAndUpdate'.split '|'
REPLACE_METHODS = 'replaceOne|findOneAndReplace'.split '|'

_restarting = false
restartSevice = (e)->
  console.error e
  if alertHandler
    setTimeout (-> alertHandler e,true),0 # raise alert in seperate thread or later
    # only restart when alertHandler is ready
    if _restarting
      console.error "Already In Restarting"
      return
    _restarting = true
  console.error "DB:Restarting"

accessLogAry = []
lastAccessObj = null
resetAccessObj = ->
  lastAccessObj =
    n:0
    tsMax: 0
    ts: 0
    en:0
resetAccessObj()
updateLog = ->
  lastAccessObj.tsAvg = lastAccessObj.ts / (lastAccessObj.n or 1)
  if bServiceStarted and (lastAccessObj.tsAvg > MAX_DB_ACCESS_TIME) # db access average time more than 2s
  #if accessLogAry.length > 1 # test
    # restart server
    restartSevice "DB Access Time Too Long #{lastAccessObj.tsAvg}ms. Restart."
  accessLogAry.push lastAccessObj
  if accessLogAry.length > 10
    accessLogAry.shift()
  profileObj.n += lastAccessObj.n
  profileObj.en += lastAccessObj.en
  profileObj.ts += lastAccessObj.ts
  if lastAccessObj.tsMax > profileObj.tsMax
    profileObj.tsMax = lastAccessObj.tsMax
  resetAccessObj()
setInterval updateLog, 60000

profileObj =
  n:0
  tsMax: 0
  ts: 0
  en:0
profile = ->
  ret = profileObj
  profileObj =
    n:0
    tsMax: 0
    ts: 0
    en:0
  ret

appendParameter = (conn,val="") ->
  if conn.indexOf('?')<0
    return conn+'?'+val
  return conn+'&'+val

util = require 'util'
MongoDb =
  setMaxDbAccessTime: (ts)->MAX_DB_ACCESS_TIME = ts
    
  setup: (opts, cb)->
    if opts?.dbs?
      for key,opt of opts.dbs
        if opt.rsName
          rsName = opt.rsName
    if opts?.dbs?.oldDb
      this.setup_old opts,cb
    else
      this.setup_new opts,cb
  setAlertHandler: (handler)-> alertHandler = handler
  setReady: ()->
    resetAccessObj()
    bServiceStarted = true
  setup_new: (opts,cb) ->
    verbose = opts?.verbose? or verbose
    DModule.depend RESOURCE_NAME,(err,handler)->
      if cb? then cb err,handler
      null
    if opts?.dbs?
      n = 0
      if opts.dbs.verbose? then verbose = opts.dbs.verbose
      if verbose > 2 then console.log opts.dbs
      for key,opt of opts.dbs
        continue if 'object' isnt typeof opt
        expected_connections++
        conn = opt.host+':'+opt.port+'/'+opt.name
        if opt.replSet?
          repl = [opt.host+':'+opt.port]
          for rsKey,rs of opt.replSet
            repl.push ((rs?.host or opt.host or '127.0.0.1')+':'+(rs?.port or opt.port or 27017))
          conn = repl.join(',')+'/'+opt.name
          if opt.rsName
            conn = appendParameter conn,('replicaSet='+opt.rsName)
        if opt.user
          conn = 'mongodb://'+encodeURIComponent(opt.user)+\
            ':'+encodeURIComponent(opt.password)+'@'+conn
          if opt.authdb
            # all need authSource now
            # unless opts.dbs.v36 # 3.6 do not allow authSource
            conn = appendParameter conn, "authSource=#{opt.authdb}"
          else
            puts 'no opt.authdb: authSource'
            conn = appendParameter conn, 'authSource=admin'
        else
          conn = 'mongodb://'+conn
        # TODO: load ssl Files by async means
        if opt?.ssl
          conn = appendParameter conn,'ssl=true'
          srvOpt = {ssl:opt.ssl,sslValidate:opt.sslValidate}
          # http://mongodb.github.io/node-mongodb-native/2.0/api/Server.html
          for oKey in ['sslCA','sslCert','sslKey']
            if oVal = opt[oKey]
              srvOpt[oKey] = fs.readFileSync oVal
        else
          srvOpt = {}
        srvOpt.poolSize = opt?.poolSize or 20
        if opt?.socketOptions
          srvOpt = Object.assign srvOpt, opt.socketOptions
        connectTimeoutMS = opt?.socketOptions?.connectTimeoutMS or 300000
        socketTimeoutMS = opt?.socketOptions?.socketTimeoutMS or 0
        conn = appendParameter conn,"connectTimeoutMS=#{connectTimeoutMS}&socketTimeoutMS=#{socketTimeoutMS}"
        # srvOpt.version = opt?.version or 3.6
        # if opt?.authdb
        #   srvOpt.authSource = opt.authdb
        if opt.readPrimary
          puts "Read Primary #{key}"
          srvOpt.readPreference = mongodb.ReadPreference.PRIMARY
          conn = appendParameter conn,"readPreference=primary"
        else
          srvOpt.readPreference = mongodb.ReadPreference.NEAREST
          conn = appendParameter conn,"readPreference=nearest"
          if opt.tag2ReadFrom
            readTags = []
            for k,v of opt.tag2ReadFrom
              readTags.push "#{k}:#{v}"
            if readTags.length
              conn = appendParameter conn,"readPreferenceTags=#{readTags.join(',')}"
        sname = key
        do (sname,conn, srvOpt, opt)->
          process.nextTick ->
            if verbose then console.log "Mongo Openning #{sname}"
            console.log conn
            console.log srvOpt
            MongoClient.connect conn, srvOpt, (err,dbClient)->
              console.error "Mongo DB open error #{err}" if err
              if not err?
                expected_connections--
                if dbClient.db
                  servers[sname] = dbClient.db opt.name # mongodb3.0 use client.db
                  dbClients[sname] = dbClient
                else
                  servers[sname] = dbClient
                if opt.isDefault
                  servers["_default"] = servers[sname]
                  console.log "*Default DB:" + sname
                dbClient.version = srvOpt.version
                # db.executeDbAdminCommand { getParameter: 1, featureCompatibilityVersion: 1 } ,(err,ret)->
                #   if err
                #     console.error "featureCompatibilityVersion Error : " + err
                #   else
                #     db.version = ret.featureCompatibilityVersion.version
                #     console.log db
                if verbose then console.log "Mongo Ready: #{sname} => #{opt.name}"
                check_ready null
              else
                console.error "Mongo DB open error #{err}"
                throw err unless opt.ignoreError

  setup_old: (opts,cb)->
    verbose = opts?.verbose? or verbose
    DModule.depend RESOURCE_NAME,(err,handler)->
      if cb? then cb err,handler
      null
    if opts?.dbs?
      n = 0
      if opts.dbs.verbose? then verbose = opts.dbs.verbose
      if verbose > 2 then console.log opts.dbs
      for key,opt of opts.dbs
        if key isnt 'verbose'
          expected_connections++
          # TODO: load ssl Files by async means
          if opt?.ssl
            srvOpt = {ssl:opt.ssl,sslValidate:opt.sslValidate}
            # http://mongodb.github.io/node-mongodb-native/2.0/api/Server.html
            for oKey in ['sslCA','sslCert','sslKey']
              if oVal = opt[oKey]
                srvOpt[oKey] = fs.readFileSync oVal
          else
            srvOpt = {}
          if opt?.socketOptions
            srvOpt.socketOptions = opt.socketOptions
          srvOpt.socketOptions ?= {connectTimeoutMS:300000,socketTimeoutMS:0}
          console.log srvOpt.socketOptions
          #console.log mongodb.Server
          s = new mongodb.Server (opt?.host or '127.0.0.1'),(opt?.port or 27017),srvOpt

          #support replica servers
          #to determine if replica server exists
          if opt.replSet?
          #add the main server to replica servers, if there is no replica server, it's fine too
            replServers = [s]
            #add replServers
            for rsKey,rs of opt.replSet
              replServers.push = new mongodb.Server (rs?.host or opt.host or '127.0.0.1'),(rs?.port or opt.port or 27017),srvOpt
            #create new replSet
            replSet = new mongodb.ReplSet replServers
            if opt.readPrimary
              rp = new mongodb.ReadPreference mongodb.ReadPreference.PRIMARY #, (if opt.tag2ReadFrom then opt.tag2ReadFrom else {})
              puts "Read Primary #{key}"
            else
              rp = new mongodb.ReadPreference mongodb.ReadPreference.NEAREST, (if opt.tag2ReadFrom then opt.tag2ReadFrom else {})
              puts "Read Preference #{key}:" + util.inspect(opt.tag2ReadFrom or {})
            servers[key] = new mongodb.Db opt.name, replSet, {safe:true,authSource:opt?.authdb,readPreference:rp,slaveOk:true} #key = db name , add readPreference
          else
            servers[key] = new mongodb.Db opt.name, s, {safe:true,authSource:opt?.authdb} #key = db name , add readPreference

          #console.log mongodb.Db
          #servers[key] = new mongodb.Db opt.name, s, {safe:true,authSource:opt?.authdb}
          if verbose then console.log "Mongo: #{key} => #{opt.name}"
          sname = key
          client = servers[key]
          do (sname,client,opt)->
            process.nextTick ->
              if verbose then console.log "Mongo Openning #{sname}"
              getVersion = ->
                client.version = opt.version or 3.6
                # client.executeDbAdminCommand { getParameter: 1, featureCompatibilityVersion: 1 } ,(err,ret)->
                #   if err
                #     console.error "featureCompatibilityVersion Error : " + err
                #   else
                #     client.version = ret.featureCompatibilityVersion.version
                #     console.log client
              client.open (err, p_client)->
                if not err?
                  if opt?.user
                    if verbose then console.log "Mongo Authenticating #{sname}"
                    client.authenticate opt.user, opt.password, (err,result)->
                      expected_connections--
                      if (not err?) and result
                        servers[sname] = p_client
                        if verbose then console.log "Mongo Ready: #{sname} => #{p_client.databaseName}"
                      else
                        console.error "Mongo #{sname} Auth Error: #{err}"
                        throw err
                      getVersion()
                      check_ready err
                  else
                    expected_connections--
                    servers[sname] = p_client
                    if verbose then console.log "Mongo Ready: #{sname} => #{p_client.databaseName}"
                    getVersion()
                    check_ready err
                else
                  console.error "Mongo DB open error #{err}"
                  throw err unless opt.ignoreError

  get_db: (sname,cb)->
    if not sname?
      sname = '_default'
    db = servers[sname]
    if cb?
      if db
        cb null,db
      else
        cb new Error("DB not found #{sname}")
    db
  get_db_client: (sname)->
    if not sname?
      sname = '_default'
    dbClients[sname]
  ObjectID: ObjectID
  ObjectId: ObjectID

setup_default_db = ->
  if not servers['_default']
    key = Object.keys servers
    servers['_default'] = servers[key[0]]
    console.log "Default DB:#{key[0]}"

check_ready = (err)->
  if all_ready or all_ready is 0 then return null
  if err
    console.log "MongoDB Setup Error #{err}"
    DModule.provide RESOURCE_NAME,err
    all_ready = 0 # never ready
  if expected_connections <= 0
    all_ready = true
    if verbose then console.log 'MongoDB Setup Done'
    setup_default_db()
    DModule.provide RESOURCE_NAME,null,MongoDb
  null

setup_server = (sname,opt)->
  expected_connections++
  s = new Server (opt.host or '127.0.0.1'),(opt.port or 27017),{}
  servers[sname] = new mongodb.DB opt.name, s

module.exports = MongoDb

class Database
  constructor: (@dbname,@collname)->
    @ObjectID = ObjectID
    @ReadPreferencePrimary = new mongodb.ReadPreference mongodb.ReadPreference.PRIMARY
    @ReadPreferenceNearest = new mongodb.ReadPreference mongodb.ReadPreference.NEAREST
  db: (@dbname)-> @
  coll: (@collname)-> @
  toString: -> if @collname then "[#{@dbname}:#{@collname}]" else "[#{@dbname}]"
  admin: ()-> MongoDb.get_db().admin()
  getlogArray: -> accessLogAry
  newDatabase: (db,coll)-> new Database db,coll
  get_db: (dbname,cb)-> MongoDb.get_db dbname,cb
  get_coll: (dbname,collname,callback)->
    if not callback?
      callback = collname
      collname = dbname
      dbname = @dbname
    if not callback?
      callback = collname
      collname = @collname
    console.log "MSG: get_coll DB:#{dbname}\tColl:#{collname}"
    MongoDb.get_db dbname,(err,db)->
      if err then return callback err
      db.collection collname,callback

  isObjectIDString: (fid)->
    return helpersString.isObjectIDString fid

  # convert to ObjectID, if the string is in ObjectID format
  toObjectID: (id)->
    return new ObjectID(id) if @isObjectIDString id
    id

  q_normalize_id: (f)->
    if f
      f = f.$query if f.$query
      if fid = f._id
        if 'object' is typeof fid and fid.$in
          fid.$in = (@toObjectID(id) for id in fid.$in)
        else
          f._id = @toObjectID fid
      f.uid = @toObjectID(f.uid) if f.uid
    f

  execute: (method,arg)->
    self = this
    tsStart = Date.now()
    _cb = (err)->
      if err
        console.error err,"Mongo Db Access"
    cb = (e,v,cur)->
      tsDiff = Date.now() - tsStart
      lastAccessObj.n++
      lastAccessObj.ts += tsDiff
      if tsDiff > lastAccessObj.tsMax
        lastAccessObj.tsMax = tsDiff
      if tsDiff > 10000
        console.error "***Slow Query[#{new Date()}:#{tsDiff/1000}s][#{self.collname}.#{method}]: #{util.inspect(arg[0])} #{if 'function' isnt typeof arg[1] then util.inspect(arg[1]) else 'function'}"
        if curInfo = cur?.server?.s
          console.error '***Cursor','description:',curInfo.description,\
            'options:',curInfo.options,'pool:',curInfo.pool
      if e
        lastAccessObj.en++
        lastAccessObj.e = e
        if e.code is 96
          console.error "DB Operation Failed.",self.collname,arg
      _cb e,v
    if (arg?.length > 1) and ('function' is typeof arg[arg.length - 1])
      _cb = arg.pop()
      arg.push cb
    self.get_db self.dbname,(err,db)->
      if err? then return cb err
      db.collection self.collname, (err,coll)->
        if err? then return cb err
        if not (arg[1]?.noQueryNormalize or arg[2]?.noQueryNormalize)
          self.q_normalize_id arg[0]
        if verbose > 2 then puts "DB: #{self.dbname or 'DefaultDB'} #{self.collname} #{method} #{util.inspect(arg[0])}"
        if method in INSERT_METHODS
          if Array.isArray arg[0] #
            objAry = arg[0].map (obj)->
              Object.assign({},obj,{_mt:new Date()}) # shall not change original object and array
            arg[0] = objAry
          else
            obj = Object.assign({},arg[0],{_mt:new Date()}) # Shall not change original object
            arg[0] = obj
        else if method in UPDATE_METHODS
          ###
            db.collection.updateMany(
            <query>,
            [
                { $set: { status: "Modified", comments: [ "$misc1", "$misc2" ] } },
                { $unset: [ "misc1", "misc2" ] }
            ]
          )
          ###

          if Array.isArray arg[1] # check set in array
            for obj in arg[1]
              if obj.$set
                # shall not change original object and array
                obj.$set = Object.assign({},obj.$set,_mt:new Date())
                setMt = true
            if not setMt
              arg[1].push {$set:{_mt:new Date()}}
          else
            if arg[1].$set?
              # shall not change original object and array
              arg[1].$set = Object.assign({},arg[1].$set,_mt:new Date())
            else
              arg[1].$set = {_mt:new Date()}
        else if method in REPLACE_METHODS
          # shall not change original object and array
          arg[1] = Object.assign({},arg[1],_mt:new Date())

        # switch method # schema check is not used.
        #   when 'insert','update','save'
        #     if method is 'update'
        #       if not arg[2]?.noQueryNormalize
        #         obj = self.q_normalize_id arg[1]
        #     else
        #       obj = arg[0]
        #     if sch = db.schemas?[self.collname] # schema not supported in this one yet
        #       sch.validate obj,(err,newObj)->
        #         if err
        #           return cb new Error "Invalide Record:#{self.collname}:#{method}: #{err}"
        #         else
        #           if method is 'update' then arg[1] = newObj else arg[0] = newObj
        #           if verbose then puts "Valid:#{self.collname}:#{method}"
        #   when 'insertAll'
        #     if sch = db.schemas?[self.collname] # schema not supported in this one yet
        #       all = arg[0]
        #       i = 0
        #       for a in all
        #         sch.validate o,(err,newObj)->
        #           if err
        #             return cb new "Invalide Record:#{self.collname}:#{method}:#{i}: #{err}"
        #           else
        #             all[i] = newObj
        #             if verbose then puts "Valid:#{self.collname}:#{method}:#{i}"
        #         i++
          
        if verbose > 2 then puts "DB: #{self.dbname} #{self.collname} #{method}: #{util.inspect arg}"
        if (['find','watch'].indexOf(method) >= 0) and (arg.length > 0) and ('function' is typeof arg[arg.length - 1])
          cb = arg.pop()
          cur = coll[method].apply coll,arg
          cb(null,cur,cur)
        else if method is 'aggregate' and ((not db.version?) or (db.version >= 3.6))
          # mongodb 3.6, will return cursor instead of callback
          cb = arg.pop()
          cur = coll[method].apply coll,arg
          cur.toArray (err,ret)-> cb err,ret,cur
        else
          coll[method].apply coll,arg

  # query [,fields [,sort [,limit [,skip]]]],callback
  findToArrayWithCb: ()->
    self = @
    tsStart = Date.now()
    arg = [].slice.call(arguments)
    old_callback = arg.pop()
    query = null
    #var f = function(err,list=[]){}, f.length = 1
    if 'function' isnt typeof old_callback or old_callback.length isnt 2
      throw new Error('Last parameter for findToArray must be a function(err,data)')
    cb = (e,v,cur)->
      tsDiff = Date.now() - tsStart
      lastAccessObj.n++
      lastAccessObj.ts += tsDiff
      if tsDiff > lastAccessObj.tsMax
        lastAccessObj.tsMax = tsDiff
      if tsDiff > 10000
        console.error "***Slow Query[#{new Date()}:#{tsDiff/1000}s][#{self.collname}.findToArray]: #{util.inspect(query)} #{util.inspect(arg[0])} #{if 'function' isnt typeof arg[1] then util.inspect(arg[1]) else 'function'}"
        if curInfo = cur?.server?.s
          console.error '***Cursor','description:',curInfo.description,\
            'options:',curInfo.options,'pool:',curInfo.pool
      if e
        lastAccessObj.en++
        lastAccessObj.e = e
        if e.code is 96
          console.error "DB Operation Failed.",self.collname,query,arg?[0]
      old_callback e,v
    self.get_db self.dbname,(err,db)->
      if err then return cb err
      db.collection self.collname, (err,coll)->
        if err then return cb err
        #console.dir arg
        if arg.length is 1 and 'object' isnt typeof arg[0]
          return cb(new Error 'Bad Request',null)
        arg[0] = self.q_normalize_id(arg[0]) if not arg[1]?.noQueryNormalize
        #if verbose then puts "find: #{util.inspect arg}"
        if verbose > 2 then puts "DB: #{self.dbname or 'DefaultDB'} #{self.collname} findToArray #{util.inspect(arg)}"
        query = arg.shift()
        #if (arg.length > 0) and (fields = arg.shift()) # when has fields, and fields is not null
        #  res = coll['find'].apply(coll,[query,fields])
        #else
        #  res = coll['find'].call(coll,query)
        #res = coll.find query
        #res = res.sort arg.shift() if arg.length > 0
        #res = res.limit arg.shift() if arg.length > 0
        #res = res.skip arg.shift() if arg.length > 0
        if arg.length > 0 # mongo drive 2.0 take options for skip limit fields
          res = coll.find query,arg[0]
        else
          res = coll.find query
        res.toArray (err,a)->
          cb(err,a,res)

  objectIdWithTimestamp: (timestamp)->
    # Convert string date to Date object (otherwise assume timestamp is a date)
    if 'string' is typeof timestamp
      timestamp = new Date timestamp

    # Convert date object to hex seconds since Unix epoch
    hexSeconds = Math.floor(timestamp/1000).toString(16)

    # Create an ObjectId with that hex timestamp
    new ObjectID hexSeconds + "0000000000000000"

  # start a new session for transactions.
  # parameters can be (dbname) or (options) or (dbname,options) or nothing()
  # dbname @param: database name
  # opt @param: session options
  # usage: https://www.mongodb.com/blog/post/quick-start-nodejs--mongodb--how-to-implement-transactions
  startSession: (opt)->
    if not @dbname
      throw new Exception('Need a database name')
    db = @get_db @dbname
    if not db
      throw new Exception('no database of ' + @dbname)
    db.startSession opt


  # usage:
  #  transaction dbname, options, (dbClient, session)->
  #     await dbClient.get_coll('coll').updateOne(xxx,{session})
  #     await dbClient.get_coll('coll2').updateOne(xxx,{session})
  transaction: (
    transactionOptions,
    fnTransaction,
    fnFinish
  )->
    if not fnFinish
      fnFinish = (err,result)->
        if err
          console.error 'Transaction Error:',err
        else
          console.log 'Transaction Success:',result
        return result
    if not fnTransaction
      fnTransaction = transactionOptions
      transactionOptions =
        readPreference: 'primary'
        readConcern: { level: 'local' }
        writeConcern: { w: 'majority' }
    client = MongoDb.get_db_client @dbname
    db = @get_db @dbname
    session = client.startSession()
    transactionError = null
    try
      transactionResult = await session.withTransaction( (()->
        # debug.debug 'start transaction',@dbname
        await fnTransaction db, session
        # debug.debug 'end transaction',@dbname
      ), transactionOptions)
      # has for success, null for aborted
    catch err
      transactionError = err
    finally
      await session.endSession()
    fnFinish transactionError,transactionResult

Database_method_setup = ->
  #ensureIndex|removeOne|removeMany
  for name in 'insertOne|insert|insertAll|insertMany|\
  replaceOne|save|update|updateOne|updateMany|\
  use|distinct|count|countDocuments|estimatedDocumentCount|createIndex|createIndexes|dropIndex|\
  remove|drop|deleteOne|deleteMany|\
  findOneAndReplace|findOneAndUpdate|findOneAndDelete|\
  find|findOne|sort|toArray|aggregate|group|watch|rename'.split '|'
    do (name)->
      Database.prototype[name] = ->
        arg = [].slice.call(arguments)
        if 'function' is typeof (lastParam = arg[arg.length-1])
          return @execute name,arg
        return new Promise((resolve,reject)=>
          arg.push (err,res)->
            if err
              reject err
            else
              resolve res
          @execute name,arg
        )
  Database.prototype.findToArray = ->
    arg = [].slice.call(arguments)
    if 'function' is typeof (lastParam = arg[arg.length-1])
      return @findToArrayWithCb arg...
    return new Promise((resolve,reject)=>
      arg.push (err,res)->
        if err
          reject err
        else
          resolve res
      @findToArrayWithCb arg...
    )

  Database.prototype.ensureIndex = (fields,opt,cb)->
    if (not cb?) and ('function' is typeof opt)
      cb = opt
      opt = null
    self = @
    @get_db self.dbname,(err,db)->
      if err? then return cb err
      db.collection self.collname, (err,coll)->
        if err? then return cb err
        mycb = (err)->
          if err
            console.error "ensureIndex error for #{self.collname}"
            console.error fields
            console.error opt
            cb err
          else
            cb()
        if opt?
          coll.ensureIndex fields,opt,mycb
        else
          coll.ensureIndex fields,mycb

  # watchTarget a collection for change
  # usage:
  # 1. default options, watch all change and deal with error automatically.
  #    db.watchTarget function(fullDoc,cb)
  # 2. customized options.
  #    db.watchTarget options,cb as in watchHelper.watchTarget
  Database.prototype.watchTarget = (opt,cb)->
    # TODO:

  null

Database_method_setup()

module.exports.Database = Database

module.exports.profile = profile

# monitor database events
# listener = require('mongodb').instrument({
#   operationIdGenerator:
#     operationId: 1
#     next: ->
#       @operationId++
#   timestampGenerator:
#     current: ->
#       (new Date).getTime()
#     duration: (start, end) ->
#       end - start

# }, (err, instrumentations) ->
#   # Instrument the driver
#   return
# )
# listener.on 'started', (event) ->
#   # command start event (see https://github.com/mongodb/specifications/blob/master/source/command-monitoring/command-monitoring.rst)
#   console.log 'started',event
# listener.on 'succeeded', (event) ->
#   # command success event (see https://github.com/mongodb/specifications/blob/master/source/command-monitoring/command-monitoring.rst)
#   console.log 'succeeded',event
# listener.on 'failed', (event) ->
#   # command failure event (see https://github.com/mongodb/specifications/blob/master/source/command-monitoring/command-monitoring.rst)
#   console.log 'failed',event
