Events = ACOLLECTION 'events'
# Events = COLLECTION 'events'

buf1px = new Buffer([
    0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00,
    0x80, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x2c,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02,
    0x02, 0x44, 0x01, 0x00, 0x3b])

PARAMMAP =
  u: 'uid'
  r: 'url'
  h: 'host'
  e: 'event'
  f: 'feature'
  t: 'type'
  d: 'timeDifferenceMs'

parseParam = (str)->
  parts = str.split /;;/
  kv = {}
  for p in parts
    pp = p.indexOf ':'
    kv[p.substr(0,pp)] = p.substr(pp+1)
  kv

sendImage = (resp)->
  resp.setHeader 'Content-Type','image/gif'
  resp.send buf1px # https://stackoverflow.com/questions/6638504/why-serve-1x1-pixel-gif-web-bugs-data-at-all

logEvent = (req,resp,p)->
  unless p
    return sendImage resp
  p = parseParam p
  p.rip = req.remoteIP()
  p.ts = new Date()
  Events.insertOne(p,
    ()-> sendImage resp
    (err)->
      console.error err
      sendImage resp
    )
  # Events.save p,(err)->
  #   if err
  #     console.error err
  #   sendImage resp


GET 'a.gif',(req,resp)->
  if (pp = req.url.indexOf('?')) >= 0
    p = req.url.substr(pp+1)
  logEvent req,resp,p
POST 'a',(req,resp)->
  logEvent req,resp,req.param('p')


LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      meta name:"apple-mobile-web-app-capable", content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"
      meta name:"format-detection", content:"telephone=no"
      meta name:"referrer", content:"no-referrer" if @noref
    block 'body', ->
      body ->
        text @content()
VIEW 'E404', ->
  header class: 'bar bar-nav', ->
    #a class: 'icon icon-left pull-left', href: '/1.5/index', style:'color:white;',dataTransition: 'slide-out'
    h1 class: 'title', -> @title or 'Error'
  div class: 'content', ->#style:'margin:10px;',->
    div class: '', style: 'margin:10px;', ->
      p style:"text-align:center;color:red;",->
        text "No contents for #{@req.host} #{@req.url}"

# AppLogs = COLLECTION 'app_logs'
# MAX_LOGIN_LENGTH = 100
#
# conf = CONFIG()
#
# DEF 'appLog',(req,cb)->
#   cb() if cb
#   return
  # Not used
  # push = {his:{$each:[{ts:new Date(),ua:req.UA(),sv:req.param('sv'),dbg:req.param('dbg')}],$slice:-MAX_LOGIN_LENGTH}}
  # AppLogs.updateOne {_id:req.remoteIP()},{$push:push},{safe:true,upsert:true},(err)->
  #   if err then console.error err
  #   cb() if cb
