###
Transit Model

###
debugHelper = INCLUDE 'lib.debug'
stringHelper = INCLUDE 'lib.helpers_string'

debug = debugHelper.getDebugger()
TransitRouteShapeCol = COLLECTION 'vow', 'transit_route_shape'
TransitStopCol = COLLECTION 'vow', 'transit_stop'
geolib = require 'geolib'
ProvAndCity = MODEL 'ProvAndCity'
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  TransitRouteShapeCol.createIndex {'bnds.features.geometry':'2dsphere',prov:1,route_tp:1,agency:1}
  TransitRouteShapeCol.createIndex {region:1,route_tp:1,prov:1}
  TransitStopCol.createIndex { prov:1, geometry:'2dsphere',route_tp:1, agency:1}

getBboxCenterAndDist = (bbox)->
  #opt = {bbox:[-79.79194235508669, 43.24743127940957, -78.84463820273068, 44.2347580249027]}
  centerLat = (bbox[1] + bbox[3])/2
  centerLng = (bbox[0] + bbox[2])/2

  center = {
    type: 'Point' ,
    coordinates: [ centerLng ,centerLat ]
  }

  distX = geolib.getDistance(
    {latitude: centerLat  , longitude: centerLng},
    {latitude: centerLat , longitude: bbox[0]}
  )

  distY = geolib.getDistance(
    {latitude: centerLat, longitude: centerLng},
    {latitude: bbox[1], longitude: centerLng}
  )
  return { center, distX, distY}

# COLOR_MAP = {0:'#e03131',1:'#007aff'}
getRandomColor = (route={})->
  # use route id hash to 0-10
  '#'+stringHelper.md5(route._id or route.nm).slice(0, 6)

class Transit
  @findList: ({type},cb)->
    type ?= 'route'
    query = {}
    # query = {route_tp:'Bus'}
    # query = {urlBase:'Canada_Ontario_Toronto_TTC_Sub'}
    # query = {_id:'CANADA:BRITISH COLUMBIA:REGION VANCOUVER#Bus'}
    # query = {_id:'CANADA:ONTARIO:REGION TORONTO#GO:Bus'}
    Col = if type is 'route' then TransitRouteCol else TransitRouteRegionCol
    Col.findToArray query,(err,routes)->
      if err
        debug.error 'Transit findList', err
        return cb(err)
      cb null, routes


  ###
    New:
    ON: [{nm:'Sub-TTC',transit_tp:'Sub',agency:'TTC'},{nm:'Rail-Go',...}]
    BC: [{nm:'Sub-TL',},{nm:'Bus',noAgency:1}]

    Old:
    TRANSIT_ROUTE_TP :{
      QC: { Sub: [], Bus: [], Rail: [] },
      ON: {
        Sub: [ 'TTC' ],
        Bus: [ 'TTC', 'GO', 'YRT' ],
        Tram: [ 'TTC' ],
        Rail: [ 'GO', 'UP' ]
      },
      BC: { Sub: [], Rail: [], Ferry: [], Bus: [] },
      AB: { Tram: [], Bus: [] }
      NS: {}
    }
  ###

  @getTransitRouteType:(cb)->
    
    # removeDup = (arr)->
    #   arr.filter (v,idx,array)->
    #     return array.indexOf(v) is idx
    TransitRouteShapeCol.aggregate [
      # 'agency':'$agency'
      {
        $group: {
          _id: {'region':'$region','route_tp':'$route_tp','prov':'$prov'}
          }
      },
      ], (err,transitRouteTP)->
        # transitRouteType = {} # {'region':[]}
        # provRouteType = {} # {'prov':[]}
        # for obj in transitRouteTP
        #   id = obj._id
        #   transitRouteType[id.region]?=[]
        #   provRouteType[id.prov]?=[]
        #   # NOTE: which agentcy not matter to user
        #   # agency = id.agency
        #   # if agency.indexOf('_')
        #   #   agency = agency.split('_')[0]
        #   tmp = {
        #     # nm:"#{id.route_tp}",#-#{agency}
        #     route_tp:id.route_tp,
        #     prov:id.prov,
        #     region:id.region,
        #     # agency:agency
        #   }
        #   transitRouteType[id.region].push tmp
        #   provRouteType[id.prov].push tmp
        # for k, v of transitRouteType
        #   # removeDup(v)
        #   v.sort sortByTransitType
        # for k, v of provRouteType
        #   # removeDup(v)
        #   v.sort sortByTransitType
        return cb(transitRouteTP)

  ###
    db.getCollection('transit_route').find({
        'bnds.features.geometry':{
            $near: {
              $geometry: {
                  type: "Point" ,
                  coordinates: [ -73.448202 ,45.360279 ]
              },
              $maxDistance: 1000,
            }
            }
      })
  ###
  @getTransitRoutes:({route_tp,prov,agency,bbox,region},cb)->
    return cb 'No type',null unless (route_tp)
    return cb 'Invalid route_tp' unless Array.isArray(route_tp)
    if region and (not  Array.isArray(region))
      region = [region]
    query = {}
    # if tp and bbox both, use both lookup
    #optional(which company), if no this must have bbox
    query.agency = {$in:agency} if agency?.length > 0
    query.route_tp = {$in:route_tp} if route_tp?.length > 0 #required*
    isBusOrTram = route_tp.indexOf('Bus')>=0 or route_tp.indexOf('Tram')>=0
    if (region?.length > 0) and (not isBusOrTram)
      query.region = {$in:region}
    else if prov and (not isBusOrTram)#user select prov, map not moved
      query.prov = ProvAndCity.getProvAbbrName prov
    else if bbox
      { center, distX, distY} = getBboxCenterAndDist(bbox)
      query['bnds.features.geometry'] =
        $near:
          $geometry: center,
          $maxDistance: Math.min(distX, distY),
    
    #TODO @rain, which bnd?, depend on zoom level, should send both if all tps selected
    # bnds17:1,bnds14:1, return when Sub/Rail/Ferry
    fields = {
      snm:1,agency:1,prov:1,region:1,fnm:1,nm:1,_id:1,tp:1,
      route_tp:1,route_id:1,color:1,
      bnds14:1,bnds17:1}
    debug.debug 'query',JSON.stringify query
    ###
      query {"route_tp":{"$in":["Sub"]},"bnds.features.geometry":{"$near":{"$geometry":{"type":"Point","coordinates":[-79.31829027891447,43.74222371678978]},"$maxDistance":31642}},"prov":"ON"}
    ###
    # limit400 when Sub5/Rail6/Ferry1, else limit 200,
    option = {fields:fields,sort:{fnm:1,route_tp:-1},limit:400}
    if isBusOrTram
      option.limit = 200
      delete fields.bnds14
      delete fields.bnds17

    debug.debug 'getTransitRoutes TransitRouteShapeCol findToArray',query,option

    TransitRouteShapeCol.findToArray query,option,(err,routes)->
      if err
        debug.error err
        return cb err
      routes ?= []
      # TODO: return when Sub/Rail/Ferry
      for route in routes
        # route.stops = route.bnds14?.features[0]?.properties?.stops
        route.num = route.snm
        # route.agency = route.agc_fnm
        if route.bnds14#route_tp.indexOf('Bus') < 0
          route.coords14 = route.bnds14?.features[0]?.geometry?.coordinates
          route.coords17 = route.bnds17?.features[0]?.geometry?.coordinates
        delete route.bnds14
        delete route.bnds17
      return cb null,routes



  @getTransitRoutesById:(id,cb)->
    unless id
      debug.error 'No id'
      return cb MSG_STRINGS.BAD_PARAMETER
    query = {_id:id}
    fields = {
      bnds17:1,
      bnds14:1,
      prov:1,
      region:1,
      fnm:1,
      nm:1,
      _id:1,
      agency:1,
      route_tp:1,
      color:1,
      route_id:1,
      agc_url:1,
      agc_phone:1
    }
    TransitRouteShapeCol.findOne query,{fields:fields},(err,route)->
      if err
        debug.error err
        return cb err
      unless route
        return cb()
      route.coords14 = route.bnds14?.features[0]?.geometry?.coordinates
      route.coords17 = route.bnds17?.features[0]?.geometry?.coordinates
      route.stops = route.bnds14?.features[0]?.properties?.stops
      delete route.bnds14
      delete route.bnds17
      if not route.color
        route.color = getRandomColor(route)
      return cb null,route

  # get stop info, append routes coords
  @getTransitStopById: (id,cb)->
    unless id
      debug.error 'No id'
      return cb MSG_STRINGS.BAD_PARAMETER
    query = {
      $or:[
        {_id: id},
        {ids: id}
      ]
    }
    TransitStopCol.findToArray query,(err,stops)->
      if err
        console.error err
        return cb MSG_STRINGS.DB_ERROR
      stops ?= []
      # stop is parent stop
      stop = stops[0] or {}
      origStop = stops[0] or {}
      for s in stops
        if s._id is id
          origStop = s
        if s.ids
          stop = s
          # break
      stop._id = id
      if not stop?.routes
        return cb null, stop
      origStop.routes ?= []
      preferredRouteId = origStop.routes[0]?._id
      # console.log('++++',preferredRouteId,origStop)
      #append routes info use route_shape_id?
      routeShapeIds = stop.routes.map (s)-> s._id
      query = {_id:{$in:routeShapeIds}}
      fields = {
        # bnds14:1,
        # bnds17:1,
        prov:1,region:1,
        fnm:1,nm:1,snm:1,_id:1,agency:1,route_tp:1,color:1,
        route_id:1}
      TransitRouteShapeCol.findToArray query,
        {fields,limit:25,sort:{route_tp:-1}},
        (err,routes)->
          if err
            console.error err
            return cb MSG_STRINGS.DB_ERROR
          routes ?= []
          # TODO: do we really need coords? or when we click some route
          for route in routes
            if route._id is preferredRouteId
              route.preferred = true
            route.num = route.snm
            # route.coords14 = route.bnds14?.features[0]?.geometry?.coordinates
            # route.coords17 = route.bnds17?.features[0]?.geometry?.coordinates
            delete route.bnds17
            delete route.bnds14
            # if r?.bnds?.features
            #   for feat in r.bnds.features
            #     # tmp = generateFeat(feat)
            #     r.coords14 = feat?..coords14
            #     r.coords17 = tmp.coords17
            if not route.color
              route.color = getRandomColor(route)
          stop.routes = routes
          return cb null, stop

  @getTransitStopsByBbox: ({
    bbox,
    agency,
    route_tp,
    prov,
    route_shape_id,
    zoom=14,
    limit,
    region,
    except,
  })->
    resultError = {err: MSG_STRINGS.BAD_PARAMETER, mapStops: [], cnt: 0}
    if not bbox
      # debug.error 'No bbox'
      resultError.err = null
      return resultError
    if agency and (not Array.isArray(agency))
      debug.error 'agency is not array,', agency
      return resultError
    if route_tp and (not Array.isArray(route_tp))
      debug.error 'route_tp is not array,', route_tp
      return resultError
    if limit and (not parseInt(limit))
      debug.error 'limit is not int,', limit
      return resultError
    if region and (not Array.isArray(region))
      region = [region]
    #zoom<=14按照weight排序，显示merged以及非merge的点。
    query = {
      geometry:{
        $geoWithin:
        # [ <bottom left coordinates> ], [<longitude>, <latitude> ]
        # [ <upper right coordinates> ]
        # 0: -79.46788854897022
        # 1: 43.68919727500817
        # 2: -79.36335816979408
        # 3: 43.81886693432363
          {$box:[[bbox[0],bbox[1]],[bbox[2],bbox[3]]]}
      }
    }
    #zoom大于14之后，不显示merged的stop
    #2022-02-01不显示merged的stop
    # zoom = parseInt zoom
    # if zoom > 16.5
    #   zoom = 17
    # else
    #   zoom = 14
    zoom = 17
    query.$or = [{zoom:zoom},{zoom:null}]
    query.agency = {$in:agency} if agency?.length > 0
    query.route_tp = {$in:route_tp} if route_tp?.length > 0
    query.region = {$in:region} if region?.length > 0
    query.prov = prov if prov
    query._id = {$nin:[except]} if except
    # map stop for certain route, in routes->stops, are all unmerged stops, but map need merged stops?
    # query['routes._id'] = route_shape_id if route_shape_id
    fields = {lat:1,lng:1,nm:1,_id:1,agency:1,route_tp:1} #routes
    # console.log('xxxxxq',query)
    mapStops = await TransitStopCol.findToArray query,{fields,limit,sort:{parentWeight:-1,weight:-1}}
    cnt = await TransitStopCol.countDocuments query
    return {err: null, mapStops, cnt}
  ###
  "stops" : [
              {
                  "stop_id" : "14404",
                  "_id" : "ON_TTC_14404",
                  "fnm" : "FINCH STATION - SOUTHBOUND PLATFORM",
                  "nm" : "Finch"
              },
              {
                  "stop_id" : "14405",
                  "_id" : "ON_TTC_14405",
                  "fnm" : "NORTH YORK CENTRE STATION - SOUTHBOUND PLATFORM",
                  "nm" : "North York Centre"
              },
  ]
  ###
  @getTransitStopsByRouteShapeId: (route_shape_id)->
    result = {err: null, stops:[]}
    if not route_shape_id
      # debug.error 'No route_shape_id'
      return result
    ret = await TransitRouteShapeCol.findOne {_id:route_shape_id}, {fields:{'bnds.features.properties.stops':1}}
    if not ret
      return result
    stops = ret.bnds.features[0]?.properties?.stops
    # TODO: find lat,lng? for this route stops?
    queryIds = []
    for s in stops
      queryIds.push s._id
    q = {_id:{$in:queryIds}}
    retStops = await TransitStopCol.findToArray q,{fields:{nm:1,fnm:1,snm:1,ids:1,lat:1,lng:1,route_tp:1}}
    return {err:null, stops:retStops}


MODEL 'Transit',Transit

