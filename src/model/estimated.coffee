EstimateCol = COLLECTION 'vow', 'prop_estimate'
{dateFormat} = INCLUDE 'lib.helpers_date'
{isMlsPrefix} = INCLUDE 'libapp.properties'
{saletpIsSale} = INCLUDE 'libapp.propertiesTranslate'

# 估价相关的字段定义
ESTIMATE_FIELDS =
  dom_m1_days: 1

ESTIMATE_PRICE =
  # n标识组
  mt_predict_n: 1,
  sp_mn_result: 1,
  sp_mn_min: 1,
  sp_mn_max: 1,
  # d标识组
  mt_predict_d: 1,
  sp_md_result: 1,
  sp_md_min: 1,
  sp_md_max: 1

ESTIMATE_FIELDS = Object.assign(ESTIMATE_FIELDS,ESTIMATE_PRICE)
for num in [1..10]
  ESTIMATE_FIELDS["sqft_mn#{num}"] = 1
  ESTIMATE_FIELDS["sqft_mn#{num}_acu"] = 1
  ESTIMATE_FIELDS["bltYr1_mn#{num}"] = 1
  ESTIMATE_FIELDS["bltYr1_mn#{num}_acu"] = 1
  ESTIMATE_FIELDS["bltYr2_mn#{num}"] = 1
  ESTIMATE_FIELDS["bltYr2_mn#{num}_acu"] = 1
  ESTIMATE_FIELDS["value_mn#{num}"] = 1
  ESTIMATE_FIELDS["value_mn#{num}_acu"] = 1
  ESTIMATE_FIELDS["rent_mn#{num}"] = 1
  ESTIMATE_FIELDS["rent_mn#{num}_acu"] = 1
for num in [1..30]
  ESTIMATE_FIELDS["sp_md#{num}"] = 1
  ESTIMATE_FIELDS["sp_md#{num}_acu"] = 1
  ESTIMATE_FIELDS["sp_mn#{num}"] = 1
  ESTIMATE_FIELDS["sp_mn#{num}_acu"] = 1

# 定义估价数据组给前端使用的字段
EstimateGroups = [
  {
    mt: 'mt_predict_d'
    result: 'sp_md_result'
    min: 'sp_md_min'
    max: 'sp_md_max'
  }
  {
    mt: 'mt_predict_n'
    result: 'sp_mn_result'
    min: 'sp_mn_min'
    max: 'sp_mn_max'
  }
]
# 检查房子类型
TYPE_PATTERN = /^Det|^Detached|Bungalow|Cottage|^Det Condo|Det W\/Com Elements|Link|Rural Resid|^Mobile|^Single|Residential Detached|Single Family Residence|^Semi|^Duplex|Semi-Detached|Semi-Det Condo|^Att|^Multi|Triplex|Townhouse|Row\/Townhouse|Townhouse Condo|Att\/Row\/Twnhouse|^Condo|Apartment|Co-Op Apt|Co-Ownership Apt|Comm Element Condo|Leasehold Condo/i

# 检查省份是否在允许范围内
VALID_PROVINCES = ['ON', 'BC', 'AB']
MIN_DATE = 20230404
###
# @description 判断mt_n时间是否比mt_d时间更接近当前时间
# @param {string} mt_n - 预测时间n
# @param {string} mt_d - 预测时间d
# @return {boolean} 返回true表示mt_n更近，false表示mt_d更近
###
isNTimeNearest = (mt_n, mt_d)->
  # 两个时间都存在，比较哪个更接近当前时间
  nTime = new Date(mt_n)
  dTime = new Date(mt_d)
  if nTime >= dTime
    return true
  return false

class Estimated
  @ESTIMATE_PROJECTION:
    mt_predict_n: 1
    sp_mn_result: 1
    mt_predict_d: 1
    sp_md_result: 1
    rent_mn1: 1
  ###
  # @description 根据_id获取房产估价数据
  # @param {string} id - 房产ID
  # @return {object} 返回格式化后的估价数据 {estVal, estMin, estMax}
  ###
  @getEstimateById: (id) ->
    unless isMlsPrefix(id)
      return null
    query = {_id: id}
    result = await EstimateCol.findOne query, {projection: ESTIMATE_PRICE}
    return null unless result

    # 检查每组数据是否完整
    validGroups = EstimateGroups.filter (group) ->
      result[group.mt]? and result[group.result]? and result[group.min]? and result[group.max]?

    return null if validGroups.length is 0

    # 如果有多组数据
    # selectedGroup = if validGroups.length > 1
    #   isNTimeNear = isNTimeNearest(result[validGroups[0].mt], result[validGroups[1].mt])
    #   validGroups[if isNTimeNear then 0 else 1]
    selectedGroup = validGroups[0] # 优先选择 mt_predict_d

    # 返回格式化后的估价数据
    return {
      estVal: result[selectedGroup.result]
      estMin: result[selectedGroup.min]
      estMax: result[selectedGroup.max]
      estMt: result[selectedGroup.mt]
    }

  ###
  # @description 获取房产的预估所有值
  # @param {string} id - 房产ID
  # @param {object} [projection] - 可选参数，指定要返回的字段
  # @return {object} 返回预测值
  ###
  @getAllEstimateInfo: (id, projection) ->
    unless isMlsPrefix(id)
      return null
    query = {_id: id}
    result = await EstimateCol.findOne query, {projection: projection or ESTIMATE_FIELDS}
    return null unless result
    if result.mt_predict_n or result.mt_predict_d
      result.ml_mt = result.mt_predict_d or result.mt_predict_n
      result.estVal = result.sp_md_result or result.sp_mn_result
      result.ml_mt = dateFormat(result.ml_mt,'YY-MM-DD HH:MI')
    return result
  
  ###
  # @description 判断该房源是否可以估价
  # @param {object} body - 请求参数对象，包含以下字段：
  #   @param {string} body.prov - 省份代码，必须是 'ON', 'BC', 'AB' 之一
  #   @param {string} body.ptype - 房产类型，必须是 'r' 或 'Residential'
  #   @param {string|array} body.ptype2 - 房产子类型，可以是字符串或字符串数组，必须匹配以下模式之一：
  #     - 独立屋: Detached, Bungalow, Cottage, Det Condo 等
  #     - 半独立屋: Semi-Detached, Duplex 等
  #     - 联排别墅: Townhouse, Row/Townhouse 等
  #     - 公寓: Condo, Apartment, Co-Op Apt 等
  #   @param {boolean} body.onD - 上市日期
  #   @param {boolean} body.merged - 是否是merged房源
  # @return {boolean} 返回true表示可以估价，false表示不可以估价
  ###
  @canEstimate: ({prov, ptype, ptype2, onD, merged, saletp}) ->
    if merged
      return false
    if saletp
      isSale = saletpIsSale(saletp)
    unless isSale
      return false
    return false unless prov in VALID_PROVINCES
    # 检查上市日期
    if isNaN(Number(onD))
      return false
    if Number(onD) < MIN_DATE
      return false
    # 检查房产类型
    return false unless ptype?.toString().toLowerCase() in ['r', 'residential']
    unless ptype2
      return false
    # 判断 ptype2 是否为数组
    if Array.isArray(ptype2)
      for type in ptype2
        typeStr = type?.toString().toLowerCase() or ''
        if TYPE_PATTERN.test(typeStr)
          return true
      return false
    else
      ptype2Str = ptype2.toString().toLowerCase() or ''
      return TYPE_PATTERN.test(ptype2Str)

MODEL 'Estimated', Estimated
