debug = DEBUG()
UserCol = COLLECTION 'chome','user'
PnErrorCol = COLLECTION 'chome','pn_error'
PnTokenCol = COLLECTION 'chome','pn_token'
config = CONFIG(['apn','serverBase','fcmv1_config'])

helpersFunction = INCLUDE 'lib.helpers_function'
pushNotify = INCLUDE 'lib.pushNotifyV2'
notifyInstance = pushNotify.createInstance config

THREE_DAYS_AGO = new Date(Date.now()-3*24*60*60*1000)

MSG_STRINGS.def {
  PN_REQUIRED: 'pn is required'
  PN_INVALID_TYPE: 'Unsupported pn type: {type}'
  PN_NO_VALID_TOKENS: 'No valid tokens found'
  PN_INVALID_COUNT: 'Invalid count value'
  PN_COUNT_OUT_OF_RANGE: 'Count value out of range'
}
BATCH_INTERVAL = 5000
BATCH_INTERVAL_FIND = 1000
###
验证并转换推送通知标识
@param {string|string[]} pn - 推送通知标识，可以是单个字符串或字符串数组
@returns {Object} 处理结果
@returns {boolean} returns.valid - 是否有效
@returns {string} [returns.errMsg] - 错误信息
@returns {string[]} [returns.tokens] - 处理后的有效 token 数组
###
validateAndConvertPn = (pn) ->
  # 参数类型检查
  if not pn?
    return {valid: false, errMsg: MSG_STRINGS.PN_REQUIRED}
    
  # 字符串转数组
  if typeof pn is 'string'
    if pn.indexOf(',') > -1
      # 如果字符串中包含逗号，按逗号分割成数组
      tokens = pn.split(',').map((item) -> item.trim())
    else
      # 否则将单个字符串转换为数组
      tokens = [pn]
  else if Array.isArray(pn)
    tokens = pn
  else
    return {valid: false, errMsg: MSG_STRINGS.PN_INVALID_TYPE.replace('{type}', typeof pn)}
  
  # 过滤掉无效的pn
  validTokens = tokens.filter (item)->
    item?.length > 10 and ((0 is item.indexOf 'ios') or (0 is item.indexOf 'android'))
  
  if validTokens.length is 0
    return {valid: false, errMsg: MSG_STRINGS.PN_NO_VALID_TOKENS}
    
  return {valid: true, tokens: validTokens}

# send只做发送操作与push_error
# send返回增加success数据并从pn_error中删掉
# send考虑大数据量的逻辑，以及调用第三方的可能
# remove不考虑错误类型，仅考虑时间因素
# 减少多次db操作

class PushNotify
  
  #find的结果直接+1
  @getBadgeCountMap:(pn)->
    # 验证并转换 pn
    debug.info 'Push Notify getBadgeCountMap', new Date()
    validation = validateAndConvertPn(pn)
    unless validation.valid
      throw validation.errMsg
    
    pn = validation.tokens
    result = {}
    
    # 处理单个批次的pn tokens
    _processBatch = (pnList)->
      # 从 user 表获取数据
      userDocs = await UserCol.findToArray(
        {pn: {$in: pnList}},
        {projection: {pn: 1, pnBadge: 1}}
      )
      
      batchResult = {}
      for doc in userDocs
        if parseInt doc.pnBadge
          doc.pnBadge += 1
        batchResult[doc.pn] = doc.pnBadge ? 1
      
      # 找出未在 user 表中找到的 pn
      foundPn = Object.keys(batchResult)
      remainingPn = pnList.filter (p) -> not (p in foundPn)
      
      if remainingPn.length > 0
        # 从 pn_token 表获取剩余数据
        tokenDocs = await PnTokenCol.findToArray(
          {_id: {$in: remainingPn}},
          {projection: {_id: 1, pnBadge: 1}}
        )
        
        for doc in tokenDocs
          if parseInt doc.pnBadge
            doc.pnBadge += 1
          batchResult[doc._id] = doc.pnBadge ? 1
      
      return batchResult

    # 将pn列表分成每组1000个
    chunks = []
    while pn.length
      chunks.push pn.splice(0,1000)
    
    # 每次处理10个chunks
    while chunks.length
      # 获取当前批次的chunks（最多10个）
      currentChunks = chunks.splice(0,10)
      
      # 并行处理当前批次的chunks
      parallelTasks = currentChunks.map (chunk) -> _processBatch(chunk)
      batchResults = await Promise.all(parallelTasks)
      
      # 合并当前批次的结果
      for batchResult in batchResults
        Object.assign(result, batchResult)
      
      # 如果还有剩余chunks，等待一小段时间再处理下一批
      if chunks.length
        await helpersFunction.sleep(BATCH_INTERVAL_FIND)
    
    debug.info 'Push Notify getBadgeCountMap done', new Date(),result
    return result

  ###
  更新推送通知计数
  @param {string|string[]} pn - 推送通知标识，可以是单个字符串或字符串数组
  @param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
  @returns {Promise<void>} 更新操作完成
  @throws {Error} 当数据库操作出错时抛出异常
  ###
  @updateBadgeCount: (pn, count) ->
    # 验证并转换 pn
    
    validation = validateAndConvertPn(pn)
    unless validation.valid
      throw validation.errMsg
    
    pn = validation.tokens
    
    # 验证count参数
    if count?
      if typeof count isnt 'number' or isNaN(count)
        throw MSG_STRINGS.PN_INVALID_COUNT
      if count < 0 or count > Number.MAX_SAFE_INTEGER
        throw MSG_STRINGS.PN_COUNT_OUT_OF_RANGE
    
    # 构建更新操作
    updateOperation = if count? then {$set: {pnBadge: count}} else {$inc: {pnBadge: 1}}
    
    # 分批处理
    _updateInBatch = (pnList)->
      # 首先尝试更新 user 表中的所有匹配文档
      userUpdates = await UserCol.updateMany(
        {pn: {$in: pnList}},
        updateOperation
      )
      debug.info 'updateBadgeCount: ',pnList.length if pnList.length > 500
      
      # 如果 user 表中没有更新所有 pn，则更新 pn_token 表
      if userUpdates.modifiedCount < pnList.length
        # 找出未在 user 表中更新的 pn
        updatedPn = await UserCol.findToArray(
          {pn: {$in: pnList}},
          {projection: {pn: 1}}
        ).then (docs) -> docs.map (doc) -> doc.pn
        
        remainingPn = pnList.filter (p) -> not (p in updatedPn)
        
        # 更新 pn_token 表中的剩余文档
        await PnTokenCol.updateMany(
          {_id: {$in: remainingPn}},
          updateOperation,
          {upsert: true}
        )

    if pn.length < 1000
      await _updateInBatch pn
      return
    debug.info 'Push Notify updateBadgeCount', new Date()
    while pn.length
      pnToUpdate = pn.splice(0,1000)
      await _updateInBatch pnToUpdate
      # prevent update 250000 users all at once that causes db to halt
      await helpersFunction.sleep(BATCH_INTERVAL)
    debug.info 'Push Notify updateBadgeCount done', new Date()

  ###
    @param {object} notice - notify info
    @param {string} notice.from - notify from user
    @param {string/array} notice.to - notify to user/users
    @param {string} notice.msg - notify content
    @param {string} notice.url
    @param {number} notice.id
    notice =
      to:''/[]
      from:''
      msg:''
      url:''
      id:''
  ###
  @send: (notice)->
    if not notice
      return {errMsg: 'notice is required'}
      
    # 发送前先更新计数，并获取最新 badge
    if notice.to
      # 验证并转换 pn
      validation = validateAndConvertPn(notice.to)
      unless validation.valid
        return {errMsg: validation.errMsg}
      debug.info 'Push Notify before update badge',new Date(),notice.to?.length
      notice.to = validation.tokens
      try
        pnBadgeMap = await PushNotify.getBadgeCountMap(notice.to)
        PushNotify.updateBadgeCount(notice.to)
      catch err
        debug.error 'Error updating badge count',err
        # NOTE: 更新badge计数失败，不返回错误，继续发送
      # 将 badge 计数保存到 notice.cnt 字段
      notice.pnBadgeMap = pnBadgeMap if pnBadgeMap?
    debug.info 'Push Notify send updated badge',new Date()
    try
      ret = await notifyInstance.pushNotify notice
      if ret?.errMsg
        throw new Error(ret.errMsg)
      {errorList, failedList, successList} = ret
    catch error
      debug.error 'pushNotify error: ',error
      return {errMsg:error.message}
    # debug.info 'Push Notify send after pushNotify'
    if successList.length
      await PushNotify.removeErrorPn successList
    # debug.info 'Push Notify send after success list'
    if failedList.length
      debug.info 'pushNotify.send failedList=',failedList
      await PushNotify.removeFailedPn failedList
    # debug.info 'Push Notify send after failedList'
    if errorList.length
      debug.info 'pushNotify.send errorList=',errorList
      return {errMsg: errorList.join(';')}
    return {cnt:notice.to?.length}

  ###
    @param {array} failedList - notify failed records
    failedList = [{
      token:'string' #ios:20503ea3a5d4899b
      err:{
        device:'string' #20503ea3a5d4899b
        stauts:'string'
        response:{
          reason:'string'
          timestamp:'number'
        }
      }
    }]
  ###
  @removeFailedPn: (failedList)->
    tokens = []
    for failed in failedList
      if failed.token
        tokens.push failed.token
    # no need to check for existance
    await PnTokenCol.deleteMany {_id:{$in:tokens}}

    query =
      _id:{$in:tokens}
      ts:{$lte:THREE_DAYS_AGO}
    results = await PnErrorCol.findToArray query
    if results.length
      removeTokens = []
      for pnError in results
        removeTokens.push pnError._id
      failedList = failedList.filter (item)-> (removeTokens.includes item.token) is false
      await UserCol.updateMany {pn:{$in:removeTokens}},{$unset:{pn:1}}
      await PnErrorCol.deleteMany {_id:{$in:removeTokens}}

    for errInfo in failedList
      update = {
        $setOnInsert:{ts:new Date()}
        $unset:{handled:1}
        $push:{
          err:{$each:[errInfo.err], $sort:{timestamp:-1}, $slice:20}
        }
      }
      await PnErrorCol.updateOne {_id:errInfo.token},update,{upsert:true}

    return true

  ###
    @param {array} tokens - notify success records
    tokens = ['']
  ###
  @removeErrorPn:(tokens)->
    # MongoError: pn_error.deleteMany RangeError [ERR_OUT_OF_RANGE]: The value of "offset" is out of range. It must be >= 0 && <= 17825792. Received 17825794
    # NOTE: split by 1000 for tokens and remove
    if tokens.length < 1000
      await PnErrorCol.deleteMany {_id:{$in:tokens}}
      return true
    while tokens.length
      tokensToRemove = tokens.splice(0,1000)
      await PnErrorCol.deleteMany {_id:{$in:tokensToRemove}}
    return true


MODEL 'PushNotify',PushNotify