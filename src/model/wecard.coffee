###
#        This file contains all property realated methods
#  0. to use model, add '<%this.srcPath%>/model' to tpls/config.coffee->apps
#  1. BLACK BOX API, if Switching from mongo -> SQL, do i need to change orig code?
#  2. DRY
#
###
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
wecardLib = INCLUDE 'libapp.wecard'
libPropertyImage = INCLUDE 'libapp.propertyImage'
WecardCol = COLLECTION 'chome','wecard'
UserCol = COLLECTION 'chome','user'

filterHtml = (html)->
  unless html
    return ''
  #find a tag replece href
  re = new RegExp("<a([^>]* )href=\"([^\"]+)\"", "g")
  result = html.replace(re, "<a$1href=\"#\" onclick=\"showInBrowser('$2')\"")
  result

class Wecard
  @injectWecardDetail:(locale, wepage)->
    unless wepage
      return
    if /^\w{24}/.test wepage
      q = {_id:wepage}
    # NOTE: wecard has no jp/kr version, use en instead
    if locale not in ['zh','zh-cn','en']
      locale = 'en'
    if /^:/.test wepage
      editor = wepage
      editorLocale = editor+':'+locale
      q = {
        tp:{$in:['evtad','topic']}
        'meta.editor':{$in:[editor, editorLocale]}
      }
    unless q
      return
    option = {projection:{seq:1,meta:1,sid:1}}
    list = await WecardCol.findToArray q,option
    if list
      for i in list
        ret = i if i.meta.editor is editor
        if i.meta.editor is editorLocale
          ret = i
          break
    if ret
      seq = libPropertyImage.buildWecardSeqContentWithImageUrl {s:ret.seq?[0],sid:ret.sid}
    return filterHtml(seq?.m)

  ###
  # @description get wecard data and user info for share
  # @param {string} id - wecard id
  # @param {string} uid - user id
  # @param {string} to - share to where
  # @return
    type result {
      wecard?: object #wecard info
      user?: object #user info
    }
  ###
  @getWecardDataForShareAsync:({id,uid,to})->
    update = {$inc:{'meta.vc': 1, 'meta.vcd': 1}}
    if to
      update.$inc['meta.vc' + to] = 1
    wecard = await WecardCol.findOneAndUpdate {_id:id }, update, {upsert:false, returnDocument:'after', projection:{meta:1,del:1,del_rsn:1}}
    if uid
      user = await UserCol.findOne {_id:uid},{projection:{id:1,nm:1,nm_zh:1,nm_en:1,avt:1}}
    return {wecard:wecard?.value,user}

 # 是否启用访问限制
  @ACCESS_ENABLED: true,

  # 检查访问权限
  @checkAccessPermission: ({host, isWeChat, isChinaIP}) ->
    # 如果未启用访问限制，或者其他域名，直接返回true
    return true unless @ACCESS_ENABLED and (host.indexOf('.cn') > -1 )
    
    # 如果在微信中打开，允许访问
    return true if isWeChat
    
    # 如果不在微信中打开，国内ip禁止访问
    return false if isChinaIP
    
    # 其他情况允许访问
    return true

MODEL 'Wecard',Wecard
