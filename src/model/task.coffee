###
#        This file contains all task related methods
#  Task Model for push notification management
#  任务管理模型，用于推送通知任务的创建、调度和状态管理
###

debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
# NOTE: tasks already used by others
TaskCol = COLLECTION 'chome','pntasks'
ObjectId = INCLUDE('lib.mongo4').ObjectId
config = CONFIG(['serverBase'])
g365Days = 365 * 24 * 60 * 60 * 1000

# 创建索引
if config.serverBase?.masterMode
  TaskCol.createIndex {'exp':1},{expireAfterSeconds:0}
  TaskCol.createIndex {'priority':-1,'status':1}
  TaskCol.createIndex {'status':1}

{isObjectIDString} = INCLUDE 'lib.helpers_string'

class Task
  ###
  # 创建推送任务
  # @param {Object} taskData - 任务数据对象
  # @param {string} taskData.title - 任务标题
  # @param {string} taskData.msg - 推送消息内容
  # @param {string} taskData.url - 跳转链接
  # @param {string} taskData.from - 发送人
  # @param {Object} taskData.targetUser - 目标用户配置对象
  # @param {string} taskData.targetUser.gid - 群组ID
  # @param {Array} taskData.targetUser.uids - 用户ID列表
  # @param {boolean} taskData.targetUser.realtorOnly - 是否只推送给房产经纪人
  # @param {boolean} taskData.targetUser.groupUnread - 是否只推送给群组未读用户
  # @param {boolean} taskData.targetUser.isDev - 是否为开发模式
  # @param {number} taskData.priority - 任务优先级
  # @returns {void} 成功时无返回值，失败时抛出错误
  ###
  @createTask: (taskData) ->
    now = new Date()
    expDate = new Date(now.getTime() + g365Days) # 365天后过期
    
    taskDoc = 
      _id: new ObjectId()
      priority: taskData.priority or 100
      status: 'pending'
      ts: now
      exp: expDate
    taskDoc.title = taskData.title if taskData.title
    taskDoc.msg = taskData.msg if taskData.msg
    taskDoc.url = taskData.url if taskData.url
    taskDoc.from = taskData.from if taskData.from
    taskDoc.targetUser = taskData.targetUser if taskData.targetUser
    
    await TaskCol.insertOne taskDoc
    debug.info "Task created successfully: #{taskDoc._id}"
    return

  ###
  # 查询并更新优先级最高的任务状态
  # @returns {Object|null} 优先级最高的任务对象，如果没有任务则返回null
  ###
  @findOneAndUpdateHighestPriority: ->
    query = 
      status: 'pending'
      exp: {$gt: new Date()}
    
    update = 
      $set: 
        status: 'processing'
    
    options = 
      sort: {priority: -1, ts: 1}
      returnDocument: 'after'
    
    result = await TaskCol.findOneAndUpdate query, update, options
    return result?.value

  ###
  # 根据任务ID将任务状态标记为 'done'
  # @param {string|ObjectId} taskId - 要标记为完成的任务ID
  # @param {Object} pushedStat - 推送统计信息对象
  # @param {number} pushedStat.totalPushSuccess - 成功推送的总数
  # @param {number} pushedStat.totalPushFailed - 失败推送的总数
  # @param {number} pushedStat.iosDeviceCount - iOS设备数量
  # @param {number} pushedStat.androidDeviceCount - Android设备数量
  # @param {number} pushedStat.queryUserCount - 查询到的用户数量
  # @returns {void} 成功时无返回值，失败时抛出错误
  ###
  @markTaskAsDone: (taskId, pushedStat) ->
    if typeof taskId is 'string'
      taskId = new ObjectId(taskId)
    
    query = 
      _id: taskId
    
    update = 
      $set: 
        status: 'done'
        pushedStat: pushedStat or {}
    
    await TaskCol.updateOne query, update
    return

MODEL 'Task', Task 