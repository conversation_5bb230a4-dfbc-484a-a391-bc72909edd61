GeoCoderLocal = MODEL 'GeoCoderLocal'
GeoCoderRemote = MODEL 'GeoCoderRemote'
GeoCache = COLLECTION 'vow', 'geo_cache'
helpers = INCLUDE 'lib.helpers'
propAddress = INCLUDE 'lib.propAddress'
config = CONFIG(['geoCoderService'])

dateDaysBeforeNow = (validDays=180)->
  # 86400000 = 24*3600*1000 = 1 day
  return new Date(Date.now() - validDays * 86400000)

class GeoCoder
  @noGeocoding: (v)->
    return GeoCoderLocal.noGeocoding v

  # type data {
  #   uaddrAfterFormat: aUaddr,
  #   geoCodingResult: set
  # }
  @saveGeoCache: (data, cb) ->
    if config.geoCoderService?.useRemoteService
      return GeoCoderRemote.saveGeoCache data, cb
    return GeoCoderLocal.saveGeoCache data, cb

  # @input object could be prop or param object from user input from /1.5/geocoding
  # type obj {
  #   addr/lat/lng
  #   ptype2?: string[]
  # }
  @geocoding: (obj, cb) ->
    if config.geoCoderService?.useRemoteService
      # before use remote service, check local geoCache first
      propAddress.formatProvAndCity obj
      uaddr = helpers.unifyAddress obj
      return GeoCache.findOne { _id: uaddr }, (err, result) ->
        return cb err if err
        return GeoCoderRemote.geocoding obj, cb if not result

        # 去掉半年以前的质量低的数据
        halfYearAgo = dateDaysBeforeNow 180
        tooOld = (not result.mt) or (new Date(result.mt) < halfYearAgo)
        if (result.q < 100) and tooOld
          return GeoCoderRemote.geocoding obj, cb

        result.cached = true
        if result.rlat and result.rlng and result.rq
          result.lat = result.rlat
          result.lng = result.rlng
          result.q = result.rq

        return cb null, result
    return GeoCoderLocal.geocoding obj, cb

  # reverse geocoding, do not check cache and do not save
  @reverseGeocoding: (data, cb) ->
    if config.geoCoderService?.useRemoteService
      return GeoCoderRemote.reverseGeocoding data, cb
    return GeoCoderLocal.reverseGeocoding data, cb

  # 根据uaddr查找geoCache记录
  @getGeoCacheByUaddr: (uaddr, uaddrZip)->
    return unless uaddr
    
    # 如果提供了uaddrZip，优先查找带uaddrZip的记录
    if uaddrZip
      result = await GeoCache.findOne { aUaddr: uaddrZip }
      return result if result
    
    # 如果没有找到带uaddrZip的记录，查找仅匹配uaddr的记录
    return await GeoCache.findOne { aUaddr: uaddr }

MODEL 'GeoCoder', GeoCoder
