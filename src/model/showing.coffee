###
#        This file contains all property realated methods
#  0. to use model, add '<%this.srcPath%>/model' to tpls/config.coffee->apps
#  1. BLACK BOX API, if Switching from mongo -> SQL, do i need to change orig code?
#  2. DRY
20231115 coop agent
  由经纪在contact中添加，与经纪共享当前客户的showing历史，可以添加新的showing，但是owner为创建客户的经纪

showing:数据格式
 "_id" : ObjectId("64cbdd7370eb02404c9ba47e"),
    "_mt" : ISODate("2023-08-03T17:03:05.170+0000"),

    clnt:[_id] crm表里面的id
    cNm: 'name' crm表客户名称
    dt: "2023-08-19" 计划参观时间
    "dtNum" : NumberInt(20230819),
    "m" : memo 分享后客户可见
    pm:private memo 私密信息 客户不可见
    cid: [_id] 如果有clnt并且是app的用户，保存app用户在user里的id
    "clsId" : "E29483B2-64AD-4D54-B2F1-EE69DF9D18A9",添加日历的id
    "props" : [当前showing添加的房源
        {
            "_id" : "TRBW4565898",房源id
            "stT" : "01:30",start time当前房源参观开始时间
            "endT" : "02:00",end time当前房源参观结束时间
            "durtn" : "30",参观整体时间
            "m" : "nice",分享后可见信息
            "pm" : "嗯嗯",私密信息
            tags: ['strings',...],
            'drvMin': number/ min 规划路线后距下一个地点的开车时间
            'drvDis' :number/ m 规划路线后距下一个地点的开车距离
            "addr" : "2787 Eglinton Ave E",
            "src" : "TRB",
            "lat" : 43.7366542,
            "lng" : -79.2455555,
            "city" : "Toronto",
            "prov" : "Ontario",
            "failed" : false,是否booking failed
            "inBuylist" : true,是否在buylist里
            "bTs" : "2023-08-03T17:01:23.751Z",加入buylist的时间
            "uaddrp" : "CA:ON:TORONTO:2787 EGLINTON AVE E#3216"
        },
    ],
    "stPt" : {
      可以添加地点，比如去某个地点接客户或者与客户碰面，结束后将客户送回去
      addr，faddr，lat，lng，city，prov，_id：地点信息
      drvMin，drvDis：记录开始地点和第一个房源之间的开车距离和时间
    },
    "totlT" : NumberInt(229),总时间 min
    "ts" : ISODate("2023-08-03T17:01:39.053+0000"),
    "uid" : ObjectId("5e9484bede02c0622cdf74dd"),
      20231115 objectid -> array 增加协作经纪
    "mUid" : ObjectId("5e9484bede02c0622cdf74dd"),
    "endT" : "04:00",预计结束时间
    "stT" : "01:00",预计开始时间
    "allDist" : "38.5",总开车时间
    # BUG: TODO: allDurn 和 allDist 内容反了
    "allDurn" : NumberInt(61), 总开车距离
    "legs": 路线描点
    "propSeq" : "CA:ONTARIO:NORTH YORK:227 LORD SEATON ROAD;TRBE5991012;TRBE5990936;CA:ONTARIO:NORTH YORK:227 LORD SEATON ROAD",当前路线规划的顺序
    "ptStr" : "-79.3945475,43.758338;-79.2455555,43.7366542;-79.2759722,43.7059626;-79.3945475,43.758338"，当前路线规划顺序的坐标
    "exp" : 删除操作后，90天之后从数据库删除
}
###
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger() #3,3

ShowingCol = COLLECTION 'chome','showing'
CrmCol = COLLECTION 'chome','crm'
ObjectId = INCLUDE('lib.mongo4').ObjectId
{isPropObjectId} = INCLUDE 'libapp.properties'
config = CONFIG(['serverBase'])
# use which 0/90days?
if config.serverBase?.masterMode
  ShowingCol.createIndex {'exp':1},{expireAfterSeconds:0} # 90*24*60*60
  ShowingCol.createIndex {'uid':1,'dtNum':-1,clnt:1,cid:1}
  ShowingCol.createIndex {'props._id':1}
  ShowingCol.createIndex {'dtNum':1,'props.uaddrp':1}
  # NOTE: in future vers, uid = [], props=[], cant create parallel array index
  ShowingCol.createIndex {'props.inBuylist':1}
  ShowingCol.createIndex {uid:1}
{date2num} = INCLUDE 'lib.helpers_date'
{isObjectIDString} = INCLUDE 'lib.helpers_string'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'

# TODO: use uuid
uuid = INCLUDE 'node-uuid'
uuid = uuid.v4

aggregateGroup = (match,group,cb)->
  ShowingCol.aggregate [
    {$match:match},
    {$group:group}
    ],{cursor:{batchSize:0}},(err,results)->
      if err then debug.error err
      if results
        for n in results
          n.n = n._id
          #n.v = '#{n.ios}/#{n.v-n.ios}/#{n.v}'
          delete n._id
        results.sort (a,b)-> if a.n < b.n then 1 else -1
        cb null,results
      else
        cb null,[]

class Showing
  @CALENDAR_FIELDS :
    props:1
    myClsId:1
    clsId:1
    uid:1
    stT:1
    endT:1
    cNm:1
    dt:1
    stPt:1

  @LIST_PAGE_FIELDS:
    _id: 1
    dt:1
    dtNum:1
    stT:1
    endT:1
    allDist:1
    allDurn:1
    cNm:1
    totlT:1
    uid:1
    'props._id':1
    'props.stT':1
    'props.endT':1
    # 'props.failed':1

  @isValidObjectId: (id) ->
    return id and isObjectIDString(id?.toString())

  @getStat : (cb)->
    match = {_id:$gt: ShowingCol.objectIdWithTimestamp(new Date(Date.now() - 15 * 24 * 3600000))}
    group =
      _id: {$dateToString:{format:'%Y/%m/%d',date:'$_id',timezone:'America/Toronto'}}, # {year:{$year:'$_id'},month:{$month:'$_id'},day:{$dayOfMonth:'$_id'}},
      v:{$sum:1}
    aggregateGroup match,group,cb

  @getDate : (cb)->
    match = {_id:$gt: ShowingCol.objectIdWithTimestamp(new Date(Date.now() - 15 * 24 * 3600000))}
    group =
      _id: '$dt' # {year:{$year:'$_id'},month:{$month:'$_id'},day:{$dayOfMonth:'$_id'}},
      v:{$sum:1}
    aggregateGroup match,group,cb

  @countTotal : (id)->
    return await ShowingCol.countDocuments {uid:id,exp:null}

  @distictCstmers : (id)->
    group = { '_id': { _id: '$clnt', nm: '$cNm' }}
    query = [{$match: {'uid': id}}, {'$group': group},  {$sort: {'cNm': 1}}]
    return await ShowingCol.aggregate (query),{cursor:{batchSize:0}}

  @findById : (id,{projection,ownerUid})->
    param = {}
    param.fields = projection if projection
    ret = await ShowingCol.findOne {_id:id},param
    unless ret
      return null
    if ret?.clnt
      crm = await CrmCol.findOne {_id:ret.clnt},{projection:{nm:1,mbl:1,eml:1}}
      if crm
        ret.crm = crm
    if ownerUid and Array.isArray(ret.uid)
      ret.uid = ret.uid[0]
    return ret

  @delete : (id,{isAdmin,uid})->
    query = {_id:id,uid}
    #admin可以删除任何showing， owner可以删除自己创建的
    delete query.uid if isAdmin
    #for vip: dtNum>=now can delete
    return await ShowingCol.updateOne query,{$set:{'exp':new Date(Date.now()+7776000*1000)}}

  @getUpdatedItems : (body)->
    set = {}
    unSet ={}
    #clsId:calendar id
    for fld in ['myClsId','clsId','dt','m','pm','cNm']
      set[fld]= body[fld] if (body[fld])?
    for fld in ['clnt','cid']#crm._id,user._id，需要保存为objectid格式
      if @isValidObjectId body[fld]
        set[fld]= new ObjectId(body[fld])
      else
        unSet[fld]  = 1
    if not set.clsId
      set.clsId = uuid().toUpperCase()
    if Array.isArray body.props
      set.props=[]
      for prop in body.props
        obj = {}
        for fld in ['_id','stT','durtn','m','pm','tags','drvMin','drvDis','endT','addr','src','lat','lng','city','prov','inBuylist','bTs'] #'failed'
          obj[fld] = prop[fld] if (prop[fld])?
        unless obj.inBuylist
          delete obj.bTs #加入buylist的时间
        if prop.uaddr
          obj.uaddrp = prop.uaddr
          if prop.unt
            obj.uaddrp = "#{obj.uaddrp}##{prop.unt}"
        set.props.push obj
    # start position
    if body.stPt
      for fld in ['addr','faddr','lat','lng','city','prov','drvMin','drvDis','_id']
        set.stPt ?= {}
        set.stPt[fld] = body.stPt[fld] if body.stPt[fld]
    #unset if route is changed but not recalculated
    for fld in ['stT','endT','allDist','allDurn','totlT','ptStr','propSeq','legs']
      if (body[fld])?
        set[fld]= body[fld]
      else
        unSet[fld]  = 1
    if Array.isArray body.legs
      set.legs=[]
      for leg in body.legs
        obj = {}
        if Array.isArray leg.steps
          for step in leg.steps
            obj.steps?=[]
            obj.steps.push {geo:step.geo}
        for fld in ['smry','dist','dur','f','t']
          obj[fld] = leg[fld] if (leg[fld])?
        set.legs.push obj
    update = {$set:set}
    if Object.keys(unSet).length
      update.$unset = unSet
    return update

  @updateOne : (id,{uid,body})->
    updateStatus = @getUpdatedItems body
    showing = updateStatus.$set
    showing.mt = new Date()
    query = {_id:id}
    unless id
      showing.ts = new Date()
      showing.uid = [uid]
      showing.mUid = uid
      query = {_id:new ObjectId()}
    # 做为协同经纪为客户创建的showing应当也是辅助身份
    # coopid为crm表中uid的值，会保留顺序，已确认经济身份,有修改联系人且联系人为两个经纪共同提供服务的状态下才会有这个字段
    if body.coopid
      coopid = []
      for id in body.coopid
        coopid.push new ObjectId(id)
      if uid.toString() in body.coopid
        showing.uid = coopid
        showing.mUid = coopid[0]#coopid 第一位为owner
    showing.dtNum = parseInt showing.dt.replace(/-/g,'') if showing.dt
    await ShowingCol.updateOne query, updateStatus , {upsert:true}
    return {showingId:query._id, clsId: showing.clsId}

  ###
    经济获取某个日期之后的showing列表,可以在showing页面过滤客户
    @param clnt showing list过滤的用户
    @param afterDate:"20231205" 查询指定日期以后的showing
    @param afterTime:"12:00" 查询指定日期，当前时间之后的showing，与afterDate同时存在时才生效
  ###
  @getList :(uid,{clnt,afterDate,afterTime,projection})->
    query = {uid,exp:null}
    query.clnt = new ObjectId(clnt) if @isValidObjectId clnt
    query.dtNum = {$gte:afterDate}
    list = await ShowingCol.findToArray query,{projection,sort:{'dtNum':1,'stT':1}}
    return list unless afterTime
    # 通过对于当前日期和时间的判断过滤upcoming的showing
    return list.filter (v)->
      # 如果日期大于今天，保留
      if v.dtNum > afterDate
        return true
      # 如果showing没有设置开始时间，保留
      if not v.endT
        return true
      endTime = v.endT
        # 如果结束时间小于开始时间，则在小时部分添加24
      if v.endT < v.stT
        endTime = Number(v.endT.slice(0,2))+24+v.endT.slice(2)
      # 如果结束时间超过现在时间，认为已经过期，不返回当前showing
      return endTime > afterTime

  ###
    客户获取showing列表，在首页可以看到关联的经济给自己创建的showing
    查找自己id所在的showing(showing:cid) 只查180天以内的
  ###
  @getListByCid :(uid,{projection})->
    afterDate = new Date()
    time = afterDate.setDate( afterDate.getDate()- 180)
    afterDate = date2num(new Date(time))
    query = {cid:uid,exp:null}
    query.dtNum = {$gte:afterDate}
    return await ShowingCol.findToArray query,{projection,sort:{'dtNum':1,'stT':1}}

  @getUserIDsAsync: (propId)->
    # TODO：shared showing
    query = {'props._id':propId,exp:null}
    projection = {uid:1,dt:1,cNm:1}
    showings = await ShowingCol.findToArray query,{projection}
    for showing in showings
      showing.uid = showing.uid[0]
    return showings

  ###
    经纪获取buylist
    经纪可以看到自己给客户添加过的buylist（可以过滤用户）
  ###
  @getBuylist:({uid,clnt})->
    query = {uid:uid,'props.inBuylist':true}
    query.clnt = new ObjectId(clnt) if @isValidObjectId clnt
    return await ShowingCol.findToArray query,{sort:{'dtNum':1,'stT':1}}

  ###
    客户获取buylist
    客户可以看到不同经纪给自己添加的buylist
  ###
  @getBuylistByCid:(uid)->
    # 查找自己是客户状态下的showing
    query = {cid:uid,'props.inBuylist':true}
    return await ShowingCol.findToArray query,{sort:{'dtNum':1,'stT':1}}
  ###
    添加/删除 buylist 列表
  ###
  @toggleBuylist : ({id,add,pid,uid})->
    query = {_id:id,props:{$elemMatch:{_id : pid}},uid}
    update = {$set:{'props.$.inBuylist':add}}
    if add
      update.$set['props.$.bTs'] = new Date()
    else
      update.$unset = {'props.$.bTs':1}
    return ShowingCol.updateOne query,update

  ###
    获取某个房源被添加到showing的记录，返回添加过的用户uid
    @param {objectId} uid
    @param [array] uids uid范围
    @param 'string' propId 查找的房源id
    @param 'string' uaddr 查找房源的uaddr
    @param 'string' unit 查找房源的unit
    propId和uaddr最少有其中的一项
  ###
  @getInrealGroupAddShowingLog:({uid,propId,uids,uaddr,unit})->
    # 查找当前房源除本人之外，在限制的uids中是否有被加到showing的记录
    logs = []
    if isPropObjectId propId
      propId = new ObjectId(propId)
    query =
      $and:[
        {uid:{$in:uids}}
      ]
    orArr = []
    if propId
      orArr.push 'props._id':propId
    if uaddr
      uaddrp = uaddr
      if unit
        uaddrp = "#{uaddrp}##{unit}"
      orArr.push 'props.uaddrp':uaddrp
    query.$and.push {$or:orArr}
    showings = await ShowingCol.findToArray query,{fields:{uid:1}}
    uidMap = {}
    strUids = uids.map((id)->
      return id.toString()
    )
    for showing in showings
      for id in showing.uid
        strId = id.toString()
        if uidMap[strId]
          continue
        if strId in strUids
          uidMap[strId] = id
    delete uidMap[uid.toString()] #删除自己的uid
    return Object.values(uidMap)

  ###
    检查是否是showing admin，并判断待查看的用户是否在inreal group中，满足条件返回待查看用户信息
    @param {user} uid
    @param 'string' viewedUid 待查看的用户id
    @return {viewedUser} 权限通过返回待查看的用户信息
  ###
  @checkShowingAdmin:({viewedUid,user})->
    return {err:MSG_STRINGS.NO_AUTHORIZED} unless UserModel.accessAllowed('showingAdmin',user)
    viewedUser = await UserModel.findByIdAsync viewedUid,{projection:UserModel.AGENT_FIELDS}
    return {err:MSG_STRINGS.USER_NOT_FOUND} unless viewedUser
    isGroup = await GroupModel.isInGroup {uid:viewedUser._id,groupName:':inReal'}
    return {err:MSG_STRINGS.NO_AUTHORIZED} unless isGroup
    return {viewedUser}

  ###
  # @description replace prop._id and src of ShowingCol
  # @param {string} idOld - old prop._id
  # @param {string} idNew - new prop._id
  # @param {boolean} dryRun - dryRun
  # @return {number} updatedNum - updated number
  ###
  @updatePropID:({
  idOld, 
  idNew, 
  dryRun=false
  })->
    updatedNum = 0
    if not ( idOld and idNew)
      throw new Error('Missing required parameters')
    src = idNew[0..2] #TRB or CAR
    records = await ShowingCol.findToArray {'props._id':idOld},{projection:{props:1}}
    for item in records
      debug.info 'updateShowingID old:',item._id, "#{idOld} to #{idNew}, #{src}", item.props
      for prop in item.props
        if (prop._id) and (prop._id is idOld)
          prop._id = idNew
          prop.src = src
      debug.info 'updateShowingID new:',item._id, item.props
      if not dryRun
        await ShowingCol.updateOne( 
          {_id:item._id},
          {noModifyMt: true, $set:{props: item.props}}
        )
      updatedNum++
    return updatedNum


MODEL 'Showing',Showing
