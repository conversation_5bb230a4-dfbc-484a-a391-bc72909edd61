###

User
  g: [] group names, indexed
  pn/mbl/eml: for notification
  pnn/mbln: 0-1000, notification threshold, 0:all, 500: personal only, 1000: none

UserMsg
  _id: uid
  msg: []
    ts
    m
    f: from
    l/url: l-internal link, url-external link

###
config = CONFIG(['contact'])
debug = DEBUG()
# puts = console.log
# User = COLLECTION 'chome', 'user'
PnToken = COLLECTION 'chome','pn_token'
ErrColl    = COLLECTION 'chome', 'pn_error'

pushNotifyModel = MODEL 'PushNotify'
sendSMS = SERVICE 'sendSMS'
sendMail = SERVICE 'sendMail'
UserModel = MODEL 'User'
msgIdCntr = 1
# sendMailLib = INCLUDE 'lib.sendMail'
helpers = INCLUDE 'lib.helpers'
{JT2FT,FT2JT} = INCLUDE 'lib.i18n'
htmlToText = sendMail.htmlToText
# user: user obj
# opt:
#   pn/sms/email: true/false
#   one: only use one method then return once success
#   url: clickable url for push notification
#   from: from name for push notification
#   subject: for mail title
# body: message
#
class SysNotify
  @sendToUser:(user,opt,body,cb)->
    if not cb?
      cb = body
      body = opt
      opt = {mail:true,subject:'Message from RealMaster'}
    unless user
      return cb new Error('No User')
    textbody = htmlToText.fromString(body, {})

    shortBody = "#{opt.shortBody or textbody}"
    if opt.subject
      shortBody = "#{opt.subject}:#{shortBody}"
    shortBody = shortBody.substr(0,14)
    sent = []
    errs = []
    _done = ->
      if cb
        if errs?.length is 0
          errs = null
        cb errs,sent
    _sendPN = (next)->
      unless opt.pn and user.pn
        return next()
      pn =
        to: user.pn
        from: opt.from or 'RealMaster'
        msg: opt.subject or textbody
        id: msgIdCntr++
      pn.url = opt.url if opt.url
      try
        result = await pushNotifyModel.send pn
      catch err
        errs.push err
        console.error err
      if opt.one and not err?
        return _done()
      else
        sent.push 'pn'
        next()
    _sendSMS = (next)->
      unless opt.sms and user.mbl
        return next()
      sms =
        to: user.mbl
        from: '+19056142609'
        body: "#{opt.subject}: #{opt.shortBody or textbody}"
      sendSMS sms,(err)->
        if opt.one and not err?
          return _done()
        if err
          console.error err
          errs.push err
        else
          sent.push 'sms'
        next()
    _sendMail = (next)->
      unless opt.mail and user.eml
        return next()
      engine = opt.engine or 'sendmail' # 'gmail'
      mail =
        _trans: 'smtp'
        engine: engine
        from: "#{opt.from or ''}<#{sendMail.getFromEmailByEngine(engine)}>"
        replyTo: opt.replyTo or "RealMaster Technology Inc.<#{config.contact?.defaultRTEmail}>"
        to: UserModel.getEmail user
        subject: opt.subject or 'New Message From RealMaster APP'
        text: textbody
        html: body
        isNeedValidate: true
        eventType: 'SysNotify'
      #moved to sendmail
      sendMail.sendMail mail,(err,response)->
        if opt.one and not err?
          return _done()
        if err
          console.error err
          errs.push err
        else
          sent.push 'mail'
        next()
    _sendPN ->
      _sendSMS ->
        _sendMail _done

  @notifyAdmin:(msg,sms = false)->
    if sms
      sms =
        to: '6472223733'
        from: '+19056142609'
        body: msg
      sendSMS sms,(err)->
        if err
          console.error err
    sendMail.notifyAdmin msg

MODEL 'SysNotify',SysNotify
