import SavedProperties from"./SavedProperties.js";import SavedCma from"./SavedCma.js";const{useEffect,useState}=React;const SavesIndex=()=>{const[tab,setTab]=useState(0);const[tabNav,setTabs]=useState([{name:TRANSLATES.homes,index:0}]);const[ids,setIds]=useState([]);useEffect(()=>{getCMAIds();window.bus.$on("update-cma-list",handleUpdateCmaList);return()=>{window.bus.$off("update-cma-list",handleUpdateCmaList)}},[]);const handleUpdateCmaList=updatedList=>{setIds(prevIds=>{return prevIds.map(item=>{const updatedItem=updatedList.find(newItem=>newItem._id===item._id);return updatedItem||item})})};const getCMAIds=()=>{fetchData("/1.5/saveCMA/getCMAIds",{method:"post",body:{}},(error,res)=>{if(error||!res){window.bus.$emit("flash-message",error.toString())}else if(res.ok==1&&res.result.length>0){setTabs([{name:TRANSLATES.homes,index:0},{name:"CMA",index:1}]);setIds(res.result)}})};return React.createElement(React.Fragment,null,React.createElement("div",{className:"prop-list-top"},React.createElement("div",{className:"container"},React.createElement("div",{className:"title-line "},React.createElement("h1",{className:"content-title"},TRANSLATES.savedTitle)),React.createElement("nav",{className:"prop-list-nav"},React.createElement("div",{className:"prop-list-nav-container"},React.createElement("div",{className:"prop-list-nav-active"}),tabNav.map(t=>React.createElement("a",{key:t.index,className:tab===t.index?"prop-list-nav-link selected":"prop-list-nav-link ",onClick:()=>setTab(t.index),role:"button"},t.name)))))),tab===0&&React.createElement(SavedProperties,null),tab===1&&React.createElement(SavedCma,{cmalist:ids,firstid:ids[0]._id}))};export default SavesIndex;