var notesInfo={props:{noteinfo:{type:Object,default:{memo:[],pic:[],avt:"",nm_zh:"",nm_en:""}},isshowicon:{type:String},propid:{type:String,default:""},dispvar:{type:Object,default:{isRealGroup:!1,isApp:!1,isNoteAdmin:!1,lang:"en"}},isshowmore:{type:Boolean,default:!1},noteindex:{type:Number,default:0}},data:()=>({showDelete:!1,showMore:!1,sizes:[],EDIT:"edit",PRAISE:"praise"}),mounted(){var e=this;this.$nextTick((()=>{e.judgeShowMore()}))},methods:{judgeShowMore(){if(this.isshowmore){if(document.querySelector(".noteinfoH"+this.noteindex).clientHeight>=76)return void(this.showMore=!0);this.showMore=!1}},deleteNote(){let e=this;setLoaderVisibility("block"),fetchData("/1.5/notes/delete",{body:{id:this.noteinfo._id,propId:this.propid}},((t,s)=>{if(setLoaderVisibility("none"),t||!s.ok){let i=t?e.$_("Error"):e.$_(s.e);return bus.$emit("flash-message",i)}s.tokenMsg&&s.tokenMsg.length&&bus.$emit("flash-message",this.$_(s.tokenMsg)),this.showDelete=!1,bus.$emit("update-note",{model:"delete",id:this.noteinfo._id})}))},editNote(){if(!this.noteinfo.uaddr||"undefined"==this.noteinfo.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.propid||"undefined"==this.propid)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let e=this;fetchData("/1.5/notes/findListingByID",{body:{id:this.propid}},((t,s)=>{if(!s.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let i=s.result.uaddr;if(!i||"undefined"==i)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));let n=`/1.5/notes/editNotes?propId=${this.propid}&uaddr=${encodeURIComponent(i)}&id=${this.noteinfo._id}`;e.dispvar.isRealGroup||e.dispvar.isNoteAdmin||!s.result.unt||(n+=`&unt=${encodeURIComponent(s.result.unt)}`);RMSrv.getPageContent(n,"#callBackString",{toolbar:!1},(function(t){if(":cancel"!=t)try{let s=JSON.parse(t);if("delete"==s.type)return window.bus.$emit("update-note",{model:"delete",id:e.noteinfo._id});let i=s.note;e.noteinfo=e.noteinfo?Object.assign(e.noteinfo,i):i,i.memo||(e.noteinfo.memo=[]),i.pic||(e.noteinfo.pic=[])}catch(e){console.error(e)}}))}))},topArticle(){let e={_id:this.noteinfo._id};e.topMt=this.noteinfo.topMt?"":new Date,fetchData("/1.5/notes/update",{body:{noteDetail:e,type:this.PRAISE,propId:this.propid}},((e,t)=>{if(!t.ok)return bus.$emit("flash-message","Error");t.tokenMsg&&t.tokenMsg.length&&bus.$emit("flash-message",this.$_(t.tokenMsg)),this.noteinfo=Object.assign(this.noteinfo,t.result),!t.result.topMt&&this.noteinfo.topMt&&delete this.noteinfo.topMt,bus.$emit("update-note",{model:this.PRAISE,id:this.noteinfo._id,isPraise:this.noteinfo.topMt})}))},setUpPreview(e){var t,s,i,n=e.target;if(n){if(/^data:image/.test(n.src))return;s=n.naturalWidth,t=n.naturalHeight,n.setAttribute("data-size",s+"x"+t),i=parseInt(n.getAttribute("dataindex")),this.sizes[i]||(this.sizes[i]=s+"x"+t),n.classList.contains("detail-photo")&&t>s&&n.classList.add("vertical")}},removePicSize(e=[]){let t=[];return e.forEach((e=>{t.push(e.replace(/(\/(414|768|828)\.)/,"/"))})),t},previewPic(e){var t,s,i,n=this,o=e.target||e.srcElement;if(t=parseInt(o.getAttribute("dataindex")),s=JSON.stringify({urls:n.removePicSize(n.noteinfo.pic),index:t,sizes:n.sizes}),!LZString)return console.error("LZString is required!");i="/1.5/SV/photoScroll?data="+LZString.compressToEncodedURIComponent(s),(window.RMSrv||this.inFrame)&&RMSrv.openTBrowser(i,{nojump:!0,title:n.$_("RealMaster")})},confirmDelete(){let e=this,t=e.$_("Are you sure you want to delete this note?"),s=e.$_("Cancel"),i=e.$_("Yes");return RMSrv.dialogConfirm(t,(function(t){t+""=="2"&&e.deleteNote(),t+""=="1"&&(e.showDelete=!1)}),"",[s,i])}},template:'\n  <div id="notesInfo" v-if="noteinfo != null">\n    <div style="margin-bottom:10px;" v-if="isshowicon">\n      <img v-if="isshowicon == EDIT || dispvar.isNoteAdmin || isshowicon == \'complex\'" class="headPic" :src="noteinfo.avt || \'/img/user-icon-placeholder.png\'" @error="e => { e.target.src = \'/img/user-icon-placeholder.png\' }">\n      <img v-else class="headPic" src="/img/user-icon-placeholder.png">\n      <div class="userInfo">\n        <div class="userNames" v-if="(isshowicon == EDIT || dispvar.isNoteAdmin) && (noteinfo.nm_en || noteinfo.nm_zh || noteinfo.nm)">{{dispvar.lang == \'en\'? (noteinfo.nm_en || noteinfo.nm) : (noteinfo.nm_zh || noteinfo.nm)}}</div>\n        <div v-else style="height: 21px;"></div>\n        <div class="updateTime">\n          <span v-show="isshowicon == \'praise\' && noteinfo.topMt" class="topTag">{{$_(\'TOP\')}}</span>\n          {{noteinfo.mt}}\n        </div>\n      </div>\n      <div style="float: right;margin-top: 15px;">\n        <div v-if="isshowicon == EDIT">\n          <span class="btn btn-nooutline pull-right" style="padding:0px;margin-top:-3px;" v-show="showDelete">\n            <span class="pull-right btn btn-nooutline" style="border:1px solid #fff;" @click.stop.prevent="showDelete = false">{{$_(\'Cancel\')}}</span>\n            <span class="pull-right btn btn-negative" @click.stop.prevent="confirmDelete()">{{$_(\'Delete\')}}</span>\n          </span>\n          <div v-show="!showDelete">\n            <a href="javascript:;" style="margin-right:20px;font-size: 15px;" @click="editNote">\n              <span class="sprite16-18 sprite16-5-3"></span>\n              <span style="margin-left: 5px;vertical-align: top;">{{$_(\'Edit\')}}</span>\n            </a>\n            <span class="sprite16-18 sprite16-4-8" style="color:rgba(17,17,17,.6)" @click="showDelete = true"></span>\n          </div>\n        </div>\n        <span v-if="isshowicon == PRAISE && dispvar.isNoteAdmin" :class="noteinfo.topMt? \'fa fa-rmliked\':\'fa fa-rmlike\'" :style="noteinfo.topMt? \'color:#428bca\' : \'color:#111111\'" @click="topArticle()"></span>\n      </div>\n    </div>\n    <div>\n      <div :class="\'noteinfoH\'+ noteindex" :class="showMore && isshowmore? \'fold\':\'\'">\n        <div v-for="(item,index) in noteinfo.memo" style="padding-bottom:10px;">\n          <span v-show="item.tag !== \'noTag\'" class="tagTitle fontSize15">[{{item.tag}}]&nbsp;</span>\n          <span class="tagNotes fontSize15">{{item.m}}</span>\n        </div>\n      </div>\n      <div v-if="isshowmore && showMore" style="color:#428bca;font-size:12px" @click="showMore = false">{{$_(\'More\')}}</div>\n    </div>\n    <div class="pic-table marTop5" v-show="noteinfo.pic && noteinfo.pic.length>0">\n      <img class="image" v-for="(src,$index) in noteinfo.pic" :src="src" :dataindex="$index" @click="previewPic($event)" @load="setUpPreview">\n    </div>\n    <div style="display:none">{{$_(\'Edit\')}}{{$_(\'Delete\')}}{{$_(\'TOP\')}}{{$_(\'More\')}}{{$_(\'Yes\')}}</div>\n  </div>\n  '},propItem={props:{prop:{type:Object,default:function(){return{pnSrc:[]}}},loading:{type:Boolean},lang:{type:String},tag:{type:String,default:""},cantClick:{type:Boolean,default:!1}},mounted(){},computed:{showBlur:function(){return!("sold"!=this.tag&&"fast"!=this.tag||!this.prop.login)}},methods:{openDetail:function(e){if(!this.cantClick){var t=/^RM/.test(e.id)?e.id:e._id;openPopup(`/1.5/prop/detail/inapp?id=${t}`,this.$_("RealMaster"))}},getPropDate:e=>"U"==e.status_en?e.sldd:e.mt?e.mt.substr(0,10):"",formatPrice:e=>"number"==typeof e?"$"+(e=Math.ceil(e)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):e,popupSrcList(){window.bus.$emit("popup-match-list",this.prop)},cntSrc(e){if(!e)return 0;var t=0;return e.forEach((e=>{t+=e.v.length})),t},formateSrc(e){if(!e||"string"==typeof e)return e;var t=[];return e.forEach((e=>{e.nm?t.push(e.nm):t.push(e)})),t.join(",")},showLpInDelisted(e){return"Delisted"==e.saleTpTag_en?this.formatPrice(e.lp||e.lpr):this.formatPrice(e.sp||e.lp||e.lpr)}},template:'\n    <div class="prop" :class="loading?\'loading\':\'\'" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">\n      <div class="detail">\n        <div class="addr one-line" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>\n        </div>\n        <div class="prov">{{prop.city}}, {{prop.prov}}</div>\n        <div class="bdrms" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n        </div>\n        <div v-if="showBlur" class=\'prov\'>{{$_(\'Please login to see this listing!\')}}</div>\n        <div v-else>\n          <div v-if="prop.lp == \'$0\'" class="price">{{$_(\'To Be Negotiated\')}}</div>\n          <div v-else-if="tag == \'loss\'" class="price">\n            <span class=\'black\'>-{{formatPrice(prop.lsp - prop.sp)}}</span>\n            <span v-if="prop.lspDifPct && (tag == \'loss\')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>\n          </div>\n          <div v-else class="price">\n            <span>{{showLpInDelisted(prop)}}</span>\n            <span v-if="prop.pc && (prop.status == \'A\') && prop.pcts" class="price-change">\n              <span :class="prop.pc > 0?\'plus\':\'mins\'">{{prop.pc>0?\'+\':\'-\'}}</span>\n              <span>{{formatPrice(Math.abs(prop.pc))}}</span>\n            </span>\n            <span v-if="prop.lstStr && prop.lst == \'Sc\'" class="price-change">{{prop.lstStr}}</span>\n            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != \'loss\')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </div>\n        </div>\n        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_(\'Open House\')}}: {{prop.ohdate}}</div>\n      </div>\n      <div class="img" :class=\'{blur:showBlur}\'>\n        <img class="lazy" :src="prop.thumbUrl || \'/img/no-photo.png\'" onerror="this.src=\'/img/no-photo.png\'"  />\n        <div class="tag" :class="prop.tagColor">\n          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>\n          <span v-if="prop.ltp == \'exlisting\'" class="ltp">{{$_(\'Exclusive\')}}</span>\n          <span v-if="prop.ltp == \'assignment\'" class="ltp">{{$_(\'Assignment\')}}</span>\n          <span v-if="prop.ltp == \'rent\'" class="ltp">{{$_(\'Rental\')}}</span>\n          <span v-if="prop.type" class="type">{{prop.type}}</span>\n          <span>{{prop.saleTpTag || prop.lstStr}}</span>\n        </div>\n        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_(\'days\')}}</div>\n        \x3c!--div v-else class="dom">{{getPropDate(prop)}}</div--\x3e\n      </div>\n      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class=\'match\' @click.stop=\'popupSrcList(prop)\' data-sub="feeds pnsrc" :data-id="prop._id">\n        <span class="fa fa-radar"></span>\n        <span class="pnSrc" v-if="src = prop.pnSrc[0]">\n        <span class=\'name\'>{{src.transK}} </span>\n          {{formateSrc(src.v)}}\n        </span>\n        <span class="count">{{cntSrc(prop.pnSrc)}}</span>\n        <span class="fa fa-caret-down"></span>\n      </div>\n    </div>\n  '},notes={data:()=>({noteList:[],page:0,scrollElement:"",waiting:!1,hasMoreProps:!1,loading:!1,iconModel:"no",datas:["isRealGroup","isApp","isNoteAdmin","isAdmin","lang"],dispVar:{isRealGroup:!1,isApp:!1,isNoteAdmin:!1,isAdmin:!1,lang:"en"},filterOnlyPic:!1,uidInfo:[],showUidInfo:!1,filterUid:[]}),mounted(){var e=this;e.$getTranslate(e),e.getPageData(this.datas,{},!0),bus.$on("pagedata-retrieved",(function(t){e.dispVar=Object.assign(e.dispVar,t),e.iconModel="edit",e.getNotesList(),e.dispVar.isNoteAdmin&&(e.iconModel="praise",e.getNoteUserInfo())})),bus.$on("update-note",(t=>{if(t&&"delete"==t.model){let s=this.noteList.findIndex((e=>e._id==t.id));e.noteList.splice(s,1)}e.tagShow=!1})),e.scrollElement=document.getElementById("notes"),e.scrollElement.addEventListener("scroll",e.listScrolled),window.showMap||(window.showMap=this.goMap)},methods:{goMap:function(){RMSrv.getPageContent("/1.5/map/notesMap","#callBackString",{toolbar:!1},(function(e){}))},listScrolled:function(){var e=this;!e.waiting&&e.hasMoreProps&&(e.waiting=!0,e.loading=!0,setTimeout((function(){e.waiting=!1;var t=e.scrollElement;t.scrollHeight-t.scrollTop<=t.clientHeight+260&&e.hasMoreProps?(e.page++,e.getNotesList("more")):e.loading=!1}),400))},getNotesList(e){let t=this;setLoaderVisibility("block");let s={page:this.page};this.filterOnlyPic&&(s.filterOnlyPic=this.filterOnlyPic),this.filterUid.length&&(s.filterUid=this.filterUid),fetchData("/1.5/notes/savedList",{body:s},((s,i)=>{if(setLoaderVisibility("none"),s||!i.ok){let e=s?t.$_("Error"):t.$_(i.e);return bus.$emit("flash-message",e)}let n=i.result;t.noteList=e?t.noteList.concat(n.noteList):n.noteList,n.noteList.length<n.limit?t.hasMoreProps=!1:t.hasMoreProps=!0}))},getNoteUserInfo(){let e=this;fetchData("/1.5/notes/allNoteUserInfor",{body:{}},((t,s)=>{if(!s.ok||t)return bus.$emit("flash-message","Error");let i=s.result;e.uidInfo=i.uidInfo}))},filterNotesList(){this.filterUid=[],this.page=0,this.noteList=[];for(let e=0;e<this.uidInfo.length;e++)this.uidInfo[e].isSel&&this.filterUid.push(this.uidInfo[e].uid);this.showUidInfo=!1,this.getNotesList()},filterOnlyPicNotes(){this.page=0,this.noteList=[],this.filterOnlyPic=!this.filterOnlyPic,this.getNotesList()}}},app=Vue.createApp(notes);trans.install(app,{ref:Vue.ref}),app.mixin(pageDataMixins),app.component("notes-info",notesInfo),app.component("prop-item",propItem),app.component("flash-message",flashMessage),app.mount("#notes");
