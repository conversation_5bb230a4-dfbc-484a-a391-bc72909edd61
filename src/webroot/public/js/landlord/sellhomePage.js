function time(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function day(t){if(t)return(t=new Date(t)).getUTCDate()}function yearMonth(t){if(t)return(t=new Date(t)).getFullYear()+"."+(t.getUTCMonth()+1)}function number(t,e){return null!=t?(e=parseInt(e),isNaN(t)?0:parseFloat(t.toFixed(e))):t}function propPrice(t,e){return null!=t?(t=parseInt(t),isNaN(t)||0==t?"":(t<1e3?t+="":t=t<1e4?(t/1e3).toFixed(1)+"K":t<999500?Math.round(t/1e3).toFixed(0)+"K":(t/1e6).toFixed(e=e||1)+"M",t)):""}function percentage(t,e){return null!=t?(t=parseFloat(t),isNaN(t)?0:(100*t).toFixed(2)):t}function dotdate(t,e,r="."){if(!t)return"";"number"==typeof t&&(t=(t+="").slice(0,4)+"/"+t.slice(4,6)+"/"+t.slice(6,8)),"string"!=typeof t||/\d+Z/.test(t)||(t+=" EST");var n=e?"年":r,o=e?"月":r,i=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(t)&&!/\d+Z/.test(t)){var a=t.split(" ")[0].split("-");return a[0]+n+a[1]+o+a[2]+i}var s=new Date(t);return!s||isNaN(s.getTime())?t:s.getFullYear()+n+(s.getMonth()+1)+o+s.getDate()+i}function datetime(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+"/"+t.getFullYear()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function monthNameAndDate(t){if(!t)return"";var e=new Date(t);return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"][e.getMonth()]+"."+e.getDate()}function currency(t,e="$",r){try{if("string"==typeof t&&-1!=t.indexOf(e))return t;var n=parseInt(t);if(isNaN(n))return null;n<100&&r<2&&(r=2);var o=t.toString().split(".");return o[0]=o[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),0==r?o[1]=void 0:r>0&&o[1]&&(o[1]=o[1].substr(0,r)),e+o.filter((function(t){return t})).join(".")}catch(t){return console.error(t),null}}function arrayValue(t){return Array.isArray(t)?t.join(" "):t}var filters={time:time,day:day,number:number,dotdate:dotdate,datetime:datetime,propPrice:propPrice,percentage:percentage,yearMonth:yearMonth,monthNameAndDate:monthNameAndDate,currency:currency,arrayValue:arrayValue},rmsrvMixins={created:function(){"undefined"!=typeof RMSrv&&null!==RMSrv&&(this.ready=!0)},methods:{trackEventOnGoogle(t,e,r,n){trackEventOnGoogle(t,e,r,n)},exMap(t,e){let r;return this.dispVar.isApp||(document.location.href="/adPage/needAPP"),t.useMlatlng||"N"===t.daddr&&t.lat&&t.lng?r=t.lat+","+t.lng:(r=(t.city_en||t.city||"")+", "+(t.prov_en||t.prov||"")+", "+(t.cnty_en||t.cnty||""),r="N"!==t.daddr?(t.addr||"")+", "+r:r+", "+t.zip),e=(e||this.dispVar.exMapURL)+encodeURIComponent(r),RMSrv.showInBrowser(e)},goBack(){if("nativeMap"==vars.src||"nativeAutocomplete"==vars.src)return window.rmCall(":ctx::cancel");vars.d?document.location.href=vars.d:window.history.back()},sprintf(){var t=arguments,e=1;return t[0].replace(/%((%)|s|d)/g,(function(r){var n=null;if(r[2])n=r[2];else{if(n=t[e],"%d"===r)n=parseFloat(n),isNaN(n)&&(n=0);e++}return n}))},appendLocToUrl(t,e,r){if(null!=e.lat&&null!=e.lng){var n=t.indexOf("?")>0?"&":"?";return t+=n+"loc="+e.lat+","+e.lng}return t},appendCityToUrl(t,e,r={}){if(!e.o)return t;var n=t.indexOf("?")>0?"&":"?";return t+=n+"city="+e.o,e.p&&(t+="&prov="+e.p),e.n&&(t+="&cityName="+e.n),e.pn&&(t+="&provName="+e.pn),e.lat&&(t+="&lat="+e.lat),e.lng&&(t+="&lng="+e.lng),r.saletp&&(t+="&saletp="+r.saletp),null!=r.dom&&(t+="&dom="+r.dom),null!=r.oh&&(t+="&oh="+!0),r.ptype&&(t+="&ptype="+r.ptype),t},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},clickedAd(t,e,r,n){var o=t._id;if(t.inapp)return window.location=t.tgt;e&&trackEventOnGoogle(e,"clickPos"+r),o=this.appendDomain("/adJump/"+o),RMSrv.showInBrowser(o)},goTo(t,e={}){if(t.googleCat&&t.googleAction&&trackEventOnGoogle(t.googleCat,t.googleAction),t.t){let e=t.t;"For Rent"==t.t&&(e="Lease");var r=t.cat||"homeTopDrawer";trackEventOnGoogle(r,"open"+e)}var n=t.url,o=t.ipb,i=this;if(n){if(t.login&&!this.dispVar.isLoggedIn)return window.location="/1.5/user/login";if(!this.jumping){if(t.vipplus&&!this.dispVar.isVipPlus)return this.confirmVip();if(t.trebuser&&!this.dispVar.hasAid)return this.confirmTreb();if(t.t,"Agent"==t.t&&!t.direct)return this.dispVar.isLoggedIn?this.dispVar.isProdSales||this.dispVar.marketAdmin?window.location=t.url:null:window.location="/1.5/user/login";if("Services"==t.t)return window.location=n;if(1==o){return t.jumpUrl&&(n=t.jumpUrl+"?url="+encodeURIComponent(t.url)),this.tbrowser(n,{backButton:{image:"back",imagePressed:"back_pressed",align:"left",event:"backPressed"}})}if(3==o)return RMSrv.scanQR("/1.5/iframe?u=");if(4==o)return console.log(n),RMSrv.showInBrowser(n);if(1==t.loc){var a=this.dispVar.userCity;n=this.appendCityToUrl(n,a)}if(t.projQuery){var s=this.dispVar.projLastQuery||{};n+="?";for(let t of["city","prov","mode","tp1"])s[t]&&(n+=t+"="+s[t],n+="&"+t+"Name="+s[t+"Name"],n+="&")}if(1==t.gps){a=this.dispVar.userCity;n=this.appendLocToUrl(n,a)}1==t.loccmty&&(n=this.appendCityToUrl(n,e)),t.tpName&&(n+="&tpName="+this.$_(t.t,t.ctx)),this.jumping=!1,i.isNewerVer(i.dispVar.coreVer,"5.8.0")&&/mapSearch|autocomplete/.test(n)&&!/mode=list/.test(n)||(i.jumping=!0),setTimeout((function(){window.location=n}),10)}}},clearCache(){"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache&&RMSrv.clearCache()},confirmNoFnLn:function(t,e,r,n){e=e||"To be presented here, please complete your personal profile.";var o=this.$_?this.$_:this.$parent.$_,i=o(e),a=o("Later"),s=o("Do it Now");r=r||"";return RMSrv.dialogConfirm(i,(function(t){t+""=="2"?window.location="/1.5/settings/editProfile":n&&(window.location=n)}),r,[a,s])},confirmSettings:function(t,e){t=t||"To find nearby houses and schools you need to enable location";var r=this.$_?this.$_:this.$parent.$_;"function"!=typeof r&&(r=t=>t);var n=r(t),o=r("Later"),i=r("Go to settings"),a=a||"";return RMSrv.dialogConfirm(n,(function(t){t+""=="2"?RMSrv.openSettings():"function"==typeof e&&e()}),a,[o,i])},confirmNotAvailable(t){t=t||"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";var e=this.$_?this.$_:this.$parent.$_,r=e(t),n=e("I Know"),o=o||"";return RMSrv.dialogConfirm(r,(function(t){}),o,[n])},confirmUpgrade:function(t,e){e=e||"Only available in new version! Upgrade and get more advanced features.";var r=this.$_?this.$_:this.$parent.$_,n=r(e),o=r("Later"),i=r("Upgrade"),a=this.appendDomain("/app-download");return RMSrv.dialogConfirm(n,(function(e){t&&(a+="?lang="+t),e+""=="2"&&RMSrv.closeAndRedirect(a)}),"Upgrade",[o,i])},confirmVip:function(t,e){e=e||"Available only for Premium VIP user! Upgrade and get more advanced features.";var r=this.$_?this.$_:this.$parent.$_,n=r(e),o=r("Later"),i=r("See More");return RMSrv.dialogConfirm(n,(function(t){t+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[o,i])},tbrowser:function(t,e={}){var r;r={toolbar:{height:44,color:"#E03131"},closeButton:{image:"close",align:"right",event:"closePressed"},fullscreen:!1},r=Object.assign(r,e),RMSrv.openTBrowser(t,r)}}},evaluateMixins={created:function(){},data:function(){return{numFields:["range","range_r","last","last_r","st_num","bdrms","bthrms","br_plus","reno","gr","tax","sqft","sqft1","sqft2","depth","front_ft"],fields:["range","lp","range_r","last","last_r","city","cnty","lng","lat","prov","addr","reno","st","st_num","cmty","mlsid","bdrms","bthrms","tp","br_plus","gr","tax","sqft","thumbUrl","unt","sqft1","sqft2","depth","front_ft","lotsz_code","irreg"]}},mounted(){},methods:{getFormatAddr:(t,e)=>t||(e.lat&&e.lng?e.lat.toString().substr(0,7)+","+e.lng.toString().substr(0,7):""),goBack(){window.history.back()},getGoolgeStreeViewImg(t,e,r){var n=t.streetView+"&location="+e.lat+","+e.lng,o=t.streetViewMeta+"&location="+e.lat+","+e.lng;this.$http.get(o).then((function(t){console.log(t),"OK"==t.body.status?r(n):r(null)}),(function(){r(null)}))},removeHist(t,e,r,n,o,i){t.stopPropagation();var a=this;RMSrv.dialogConfirm(o,(function(t){if(t+""=="2"){var o={uid:e,uaddr:r};n&&(o.id=n),fetchData("/1.5/evaluation/delete",{body:o},(function(t,e){t?(console.log(t),a.msg="error"):1==e.ok?i():(console.log(e.e),a.msg="error")}))}}),this.getTranslate("Message"),[this.getTranslate("Cancel"),this.getTranslate("Confirm")])},formatTs:t=>formatDate(t),getIds(t){var e=[];for(let r of t)e.push(r._id);return e},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},openHistPage(t,e){var r="/1.5/evaluation/histPage.html?uaddr="+t+"&inframe=1";if(this.fromMls&&(r+="&fromMls=1"),this.share?r+="&share=1":r+="&nobar=1",r=this.appendDomain(r),this.dispVar.isApp){var n=e.trim(25);RMSrv.openTBrowser(r,{nojump:!0,title:n})}else window.document.location.href=r},getMaxDist(){var t=this;t.$http.get("/1.5/evaluation/maxDistRange?type="+t.prop.tp).then((function(e){e&&(e=e.data,t.max_dist_range=e.max_dist_range)}),(function(){}))},compareDifference(t){var e=this;return"number"==typeof t.bdrms&&"number"==typeof e.prop.bdrms&&(t.bdrms_diff=t.bdrms-e.prop.bdrms),"number"==typeof t.bthrms&&"number"==typeof e.prop.bthrms&&(t.bthrms_diff=t.bthrms-e.prop.bthrms),"number"==typeof t.gr&&"number"==typeof e.prop.gr&&(t.gr_diff=t.gr-e.prop.gr),t.lotsz_code==e.prop.lotsz_code&&"number"==typeof t.depth&&"number"==typeof t.front_ft&&"number"==typeof e.prop.depth&&"number"==typeof e.prop.front_ft&&(t.front_ft_diff=Math.round(t.front_ft-e.prop.front_ft),t.size_diff=parseInt(t.front_ft*t.depth-e.prop.front_ft*e.prop.depth)),"number"==typeof t.sqft&&"number"==typeof e.prop.sqft?t.sqft_diff=parseInt(t.sqft-e.prop.sqft):"number"==typeof t.sqft1&&"number"==typeof t.sqft2&&"number"==typeof e.prop.sqft?(t.sqft1_diff=parseInt(t.sqft1-e.prop.sqft),t.sqft2_diff=parseInt(t.sqft2-e.prop.sqft)):"number"==typeof t.sqft1&&"number"==typeof t.sqft2&&"number"==typeof e.prop.sqft1&&"number"==typeof e.prop.sqft2&&(t.sqft1_diff=parseInt(t.sqft1-e.prop.sqft1),t.sqft2_diff=parseInt(t.sqft2-e.prop.sqft2)),t.st&&t.st==e.prop.st&&t.prov==e.prop.prov&&t.city==e.prop.city&&(t.sameStreet=!0),(vars.fromMls||e.prop.mlsid)&&!t.sameStreet&&t.cmty&&t.cmty==e.prop.cmty&&t.prov==e.prop.prov&&t.city==e.prop.city&&(t.sameCmty=!0),t},openPropModal(t){var e="/1.5/evaluation/listing.html?lat="+t.lat+"&lng="+t.lng+"&inframe=1";if(vars.share||(e+="&nobar=1"),e=RMSrv.appendDomain(e),this.dispVar.isApp){t.addr.trim(25);RMSrv.openTBrowser(e,{nojump:!0,title:t.addr})}else window.location.href=e},getPropCnt:function(t,e){var r=this;(t.uaddr||t.lat&&t.lng)&&fetchData("/1.5/evaluation/propcnt",{body:t},(function(t,n){t?(console.log(t),r.msg="error"):e(n)}))},getPropFromVars(t){t||(t=window.vars);var e={};for(let r of this.fields)t[r]&&("thumbUrl"==r?e[r]=decodeURIComponent(t[r]):this.numFields.indexOf(r)>=0?e[r]=Number(t[r]):e[r]=t[r]);return e},buildUrlFromProp(){var t=[];for(let e of this.fields)this.prop[e]&&("thumbUrl"==e?t.push(e+"="+encodeURIComponent(this.prop.thumbUrl)):t.push(e+"="+this.prop[e]));return t.join("&")}}},histCard={props:["hist"],mounted(){},methods:{getTranslate:function(t){return TRANSLATES[t]||t},remove(t){var e={e:t,hist:this.hist};window.bus.$emit("remove",e)},getTime:t=>formatDate(t),openResultModal(t){var e={e:t,hist:this.hist,fromhist:!1};window.bus.$emit("openResult",e)}},template:'\n  <div class="sp-addr">{{hist.addr}}</div>\n  <div class="bdrms"><span v-show="hist.hist.bdrms != null"><span>{{hist.hist.bdrms}}</span><span class="fa fa-rmbed"></span></span><span v-show="hist.hist.bthrms != null"><span>{{hist.hist.bthrms}}</span><span class="fa fa-rmbath"></span></span><span v-show="hist.hist.gr != null"><span>{{hist.hist.gr}}</span><span class="fa fa-rmcar"></span></span><span v-show="hist.hist.sqft != null"><span>{{hist.hist.sqft}} {{getTranslate(\'sqft\')}}</span></span></div>\n  <div class="close"><span class="icon fa fa-rmclose" @click="remove($event)"></span></div>\n  <div class="price-wrapper">\n      <div class="price">{{$filters.currency(hist.hist.result.p,\'$\',0)}}</div>\n      <div class="text">{{getTime(hist.hist.ts)}}<span v-if="hist.tracked" style="padding-left:10px;">{{getTranslate(\'Saved\')}}</span></div>\n  </div>\n  <div class="btn btn-green btn-right" @click="openResultModal($event)">{{getTranslate(\'ReEstimate\')}}</div>\n  '},sellhome={data:()=>({isBusy:!1,hists:[],calculatedSpWidth:144,dispVar:{isLoggedIn:!1,lang:"zh",isApp:!0,isCip:!1,sessionUser:{},reqHost:"",isAdmin:!1,userCity:{o:"Toronto",n:"多伦多",p:"Ontario"}},datas:["isApp","isCip","isLoggedIn","lang","sessionUser","reqHost","userCity","isAdmin"]}),mounted(){if(this.$getTranslate(this),window.bus){this.calculatedSpWidth=parseInt((window.innerWidth-30)/1.2);var t=window.bus,e=this;this.getPageData(this.datas,{},!0),t.$on("pagedata-retrieved",(function(t){e.dispVar=Object.assign(e.dispVar,t)})),t.$on("add-hist",(function(t){e.prop.uaddr=t.uaddr,e.histcnt++})),t.$on("remove",(function(t){e.remove(t.e,t.hist)})),t.$on("openResult",(function(t){e.openResultModal(t.e,t.hist,t.fromhist)})),this.getUserHist(),window.openEvaluatePage||(window.openEvaluatePage=this.openEvaluatePage),loadLazyImg()}else console.error("global bus is required!")},methods:{getTranslate:function(t){return TRANSLATES[t]||t},showSignup(){toggleModal("signupModal","opens")},toggleModal(t,e){toggleModal(t,e)},remove:function(t,e){var r=this.getTranslate("afterDelete"),n=this;this.removeHist(t,e.hist.uid,e._id,null,r,(function(){var t=n.hists.findIndex((function(t){return t._id==e._id}));n.hists.splice(t,1)}))},addTrack:function(t,e,r){t.preventDefault(),t.stopPropagation();var n=this;r.tracked=e,r=Object.assign({},r),fetchData("/1.5/evaluation/addTrack",{body:{uaddr:r._id,tracked:e}},(function(t,e){t?(console.log(t),n.msg="error"):1==e.ok||(console.log(e.e),n.msg="error")}))},getTime:t=>formatDate(t),goStat(){var t="/1.5/prop/stats";return t=this.appendCityToUrl(t,this.dispVar.userCity),window.location=t},getUserHist(){var t=this;fetchData("/1.5/evaluation/userHist",{body:{limit:30}},(function(e,r){e?(console.log(e),t.msg="error"):1==r.ok?t.hists=r.hist||[]:(console.log(r.e),t.msg="error")}))},openResultModal(t,e,r){t.preventDefault(),t.stopPropagation();var n="/1.5/evaluation/result.html?uaddr="+encodeURIComponent(e._id)+"&id="+e.hist._id+"&lang="+this.dispVar.lang+"&d=/1.5/landlord/sellhome";r&&(n+="&hist=1"),n=RMSrv.appendDomain(n),window.location.href=n},goTo(t){window.location.href=t},goToReno:t=>(t||(t="https://www.realrenovation.ca?d=/1.5/landlord/sellhome"),RMSrv.showInBrowser(t)),openEvaluatePage(){var t="/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1";t=RMSrv.appendDomain(t),RMSrv.openTBrowser(t,{nojump:!0,title:this.getTranslate("Conditions")})}},computed:{spWrapperWidth(){return this.calculatedSpWidth*this.hists.length+10*(this.hists.length-1)}}},app=Vue.createApp(sellhome);function scrollBar(){var t,e=document.querySelector(".list-nav-container"),r=document.querySelector(".list-nav-link.selected"),n=document.querySelector(".list-nav-active"),o=r.getBoundingClientRect();t=o.left+o.width/2-window.innerWidth/2,e.scrollLeft=t,n.style.left=o.left+o.width/2-15+"px"}function goBack(t){"undefined"!=t?document.location.href=t:window.history.back()}app.component("hist-card",histCard),trans.install(app,{ref:Vue.ref}),app.config.globalProperties.$filters={currency:filters.currency},app.mixin(rmsrvMixins),app.mixin(pageDataMixins),app.mixin(evaluateMixins),app.mount("#sellhome"),window.addEventListener("load",(()=>{scrollBar()}));
