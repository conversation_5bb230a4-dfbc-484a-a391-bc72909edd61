<template lang="pug">
div#forumViewModal(:class="[rendered ? 'noopcity' : 'opcity-0'  ]")
  red-navbar(:disp-var="dispVar")
  div.signup(v-if="post.showRequestInfo")
    button.btn.btn-block.btn-short(type="button", @click="showSignupForm()")
      | {{_('Request Info')}}
  div.container.detail-container
    div.detail-left.col-sm-8.col-xs-12
      confirm-with-message(:on-confirm='deleteMethod',:parent-name="parentName")
      flash-message
      div.pointer.mask(v-show="showMask", @click="close()")
      div.pointer.mask.nickname-mask(v-show="showNickNameMask", @click="closeNickName()")
      create-nickname(:on-confirm="saveNickName", :on-cancel="cancelNickName", :is-admin="isForumAdmin")
      div#postViewContainer.content
        div.post-top-container(v-if="post.adTopPhoto")
          img(:src="post.adTopPhoto", :alt="post.adTop", @error="setDefaultPhoto",@click="openAd(post.adTop,0)")
          div.post-top-text {{_('AD','forum')}}
          span(v-if="dispVar.forumAdmin") {{post.vcad0}}
        div(v-if="post.src!='news'")
          h3.post-title(v-bind:class="{cross: post.del}")
            a(@click="goToProperty()")
              span(v-if="post.src=='property' && post.cmntl") {{post.cmntl + ' ' + post.tl}}
              span(v-else) {{post.tl}}
          div.post-author(v-if="post.fornm")
            div.img.post-img(@click="showAuthorInfo(post.uid)",:src="post.avt",:style="{ 'background-image': getImageUrl(post.avt)}" )
            div(style="float:left;")
              span.name
                | {{post.fornm}}
              div.ts {{formatTs2(post.ts)}}
          pre(v-if="post.m")
            div.post-content(v-html="post.m")
        div(v-if="post.src=='news'")
          div.post-content(v-html="post.m || post.tl")
        div.post-photo(v-if="post.photos && post.photos.length && post.src!=='news'")
          div.img-wrapper(v-for="photo in post.photos", v-bind:key="photo", track-by="$index")
            img(:src="photo", @error="setDefaultPhoto",@click='goToProperty()')
            div.pointer.search(v-if="showSearchIcon", @click='goToProperty()')
              span.details {{_('Details','forum')}}
              span.fa.fa-search-plus
        div.post-topics(v-if="relatedPosts && relatedPosts.length")
          div.tp-title {{_("Topic","forum")}}: {{ post.tp ||relatedPosts[0].tp}}
          div(v-for="p in relatedPosts", v-bind:key="p._id" )
            div.pointer.topic-content(v-bind:class="{mainTopic: p.tp}", @click="openRelatedPost(p._id)")
              img(v-if="p.thumb",:src="p.thumb", @error="setDefaultPhoto")
              div.line-wrap.line-wrap-2.content-right(v-if="p.thumb") {{p.tl}}
              div.line-wrap.line-wrap-2.full-width(v-else) {{p.tl}}
        div.post-top-container(v-if="post.adBottomPhoto" style="padding-top:10px;")
          img(:src="post.adBottomPhoto",:alt="post.adBottom", @error="setDefaultPhoto",@click="openAd(post.adBottom,1)")
          div.post-top-text {{_('AD','forum')}}
          span(v-if="dispVar.forumAdmin") {{post.vcad0}}
        div.comments-header(v-show="!post.discmnt && recent_cmnts.length")
          span(style="color:#E03131") {{_('Comments','forum')}}
          span.pointer.pull-right.font-size-15(@click="reverseCmnt()") {{_('Time','forum')}}
            span.padding-left-5.pull-right.fa.fa-long-arrow-down(v-if="cmntSortOrder=='desc'")
            span.padding-left-5.pull-right.fa.fa-long-arrow-up(v-if="cmntSortOrder=='asc'")
        div#recent_cmnts_container(v-if="!post.discmnt")
          forum-cmnt-card(v-for="cmnt in display_cmnts",v-bind:key="cmnt._id", track-by="$index",:cmnt="cmnt", :can-edit="isForumAdmin")
          div.pointer(style="text-align: center;", v-if="recent_cmnts.length>20 && display_cmnts.length < recent_cmnts.length",@click="showAllComments()")
            div
              span {{_('show More')}}
              span(class='fa fa-arrow-down')
          div(style="text-align: center;", v-if="recent_cmnts.length>20 && display_cmnts.length == recent_cmnts.length",@click="CollapseComments()")
            div
              span {{_('Collapse')}}
              span(class='fa fa-arrow-up')
        user-brief-info(v-if="wId && aId", :owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")
      div.comment-dialog-footer
        textarea.pointer#commentTextarea(v-model="comment", :placeholder="_('Write Comments...')+ _('No self promoting, political related, or personal attacking articles allowed. Will be removed promptly.')",@click="showComments()")
        span.pull-left(v-if="dispVar.sessionUser")
          div.img.post-img.no-margin-right(:src="dispVar.sessionUser.avt",:style="{ 'background-image': getImageUrl(dispVar.sessionUser.avt)}" )
        span.pull-left
          | {{dispVar.sessionUser.fornm}}
        span.pointer.pull-right(:disabled="publishing", @click="publishCmnt()")
          | {{_('Publish')}}
        span.pointer.pull-right(v-if="editable()", style="color:grey",@click="goToEdit()")
          | {{_('Edit')}}
      div.authorInfo(v-if="showAuthorDialog")
        span.pointer.icon.icon-close.pull-right(@click="closeAuthorInfo()")
        h3 {{_('Author Information','forum')}}
        div {{_('EMAIL','forum')}}: {{authorInfo.eml}}
        div {{_('Phone','forum')}}: {{authorInfo.mbl}}
        div {{_('Full Name','forum')}}: {{authorInfo.fn}}
        div {{_('Forum NickName','forum')}}: {{authorInfo.fornm}}
    div.col-sm-3.col-xs-12
      div
        a.btn.btn-primary(:href="'/1.5/forum/webedit?web=true'", target="_blank")
            | {{_('Post New','forum')}}
        a.pointer.btn.btn-primary(@click="showComments()")
            | {{_('Comment','forum')}}
      div(v-if="post.showRequestInfo")
        sign-up-form(:user-form.sync="userForm", :title="post.requestInfotl", :owner="owner", :is-web="true", :need-wxid="true",:feedurl="feedurl")
      div.margin-top-20.margin-bottom-20.pull-left
        a(:href="'/app-download?lang='+dispVar.lang", target="_blank")
          img(src="/web/imgs/download.png", width="100%", height="100%")
  footer-multiline(:disp-var="dispVar")
</template>

<script>
import pagedata_mixins from '../../../coffee4client/components/pagedata_mixins'
import ConfirmWithMessage from '../frac/ConfirmWithMessage.vue'
import ForumCmntCard from '../frac/ForumCmntCard.vue'
import forum_mixins from '../../../coffee4client/components/forum/forum_mixins'
import forum_common_mixins from '../../../coffee4client/components/forum/forum_common_mixins'
import CreateNickname from '../frac/CreateNickname.vue'
import rmsrv_mixins from '../../../coffee4client/components/rmsrv_mixins'
import UserBriefInfo from '../frac/UserBriefInfo.vue'
import RedNavbar from '../common/redNavbar.vue'
import FooterMultiline from '../common/footerMultiline.vue'
import FlashMessage from '../frac/FlashMessage.vue'
import SignUpForm from '../common/signUpForm.vue'

export default {
  mixins: [pagedata_mixins,forum_mixins, forum_common_mixins,rmsrv_mixins],
  data () {
    return {
      rendered: false,
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       false,
        isCip:       false,
        forumAdmin: false,
        sessionUser: {
          fas:[]
        },
        canWebComment: false,
        globalTags:[],
        ownerData:{},
        isVipRealtor: false,
        isAdmin:false,
        isRealtor: false,
        webBackEnd: false,
        reqHost:'www.realmaster.com'
      },
      qrcd: false,
      post:{
        del:null,
        tl:'',
        m:'',
        sticky:false,
        cmnts:[],
        fav:[],
        city:null,
        prov:null,
        avt:null,
        photos:[]
      },
      comment:"",
      showCommentDialog:false,
      referIdx: undefined,
      parentName: 'forumView',
      recent_cmnts: [],
      display_cmnts:[],
      relatedPosts: [],
      post_sticky: null,
      post_fav: null,
      authorIsVip:false,
      wating:false,
      cmntPageCnt: 20,
      cmntTotalPageCnt: 100,
      wDl: false,
      wSign: true,
      wComment: true,
      wId: false,
      loading: false,
      waiting:false,
      showMask: false,
      deleteMethod: null,
      noBack: true,
      prop:{},
      cmntsWidth:40,
      cmntsHeight:30,
      cmntsMarginTop:10,
      cmntsBorderRadius:50,
      cmntsBarFooterHeight:50,
      cmntSortOrder:'desc',
      cmntSortKey: 'ts',
      cmntFontSize:10,
      section:null,
      city:null,
      aId: false,
      showNickNameMask: false,
      nobar:0,
      cmntsBottomHeight:0,
      cmntsPublishBottomHeight:0,
      showAuthorDialog: false,
      authorInfo:{},
      publishing: false,
      post_pn: 0,
      wct: true,
      userForm : {forumid:null, forumtl: null},
      owner:{vip:1},
      feedurl: '/1.5/user/contact',
    };
  },
  computed:{
    showSearchIcon() {
      return this.post.src=='property';
    },
    computedCmntNum() {
      if (!this.post.cmnts)
        return 0;
      var cmnts = this.post.cmnts.filter(function(cmnt){
        return !cmnt.del;
      });
      return cmnts.length > 99 ? '99+' :  cmnts.length;
    },
    isForumAdmin: function() {
      return this.dispVar.forumAdmin || this.isForumFas(this.dispVar, {city:this.post.city, prov:this.post.prov});
    },
    shareUrl: function () {
      if (this.wId)
        return document.location.href;
      var url = 'http://' + this.dispVar.reqHost + '/1.5/forum/details?id=' + this.post._id;
      if (this.post.gid)
        url +="&gid=" + gid;
      if (!this.wComment)
        url += '&wct=0';
      if (this.dispVar.isRealtor && this.wSign) {
        url += '&aid=' + this.dispVar.sessionUser._id;
      }
      return url;
    },
    canComment: function() {
      return !this.post.discmnt && !this.post.del && (!this.post.cc || this.post.cc < 1000)
    }
  },
  methods: {
    openAd(url, index) {
      this.$http.post('/1.5/forum/adClick',{id: this.post._id, index:index}).then(
        function(ret) {
          if (ret.data.ok)
            window.open(url,"_blank");
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    showSignupForm() {
      document.getElementById('signUpForm').scrollIntoView()
    },
    showAuthorInfo(uid) {
      if(!this.dispVar.isAdmin) return;
      var self = this;
      self.$http.get('/1.5/forum/authorInfo?uid='+uid).then(
        function(ret) {
          if (ret.data.ok) {
            self.authorInfo = ret.data.user;
            self.showAuthorDialog = true;
            self.showMask = true;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    closeAuthorInfo() {
      this.showMask = false;
      this.showAuthorDialog = false;
    },
    closeNickName() {
      window.bus.$emit('close-edit-nickname');
    },
    onCommentsIconClick() {
      if(this.post.cmnts.length) {
        document.getElementById('recent_cmnts_container').scrollIntoView()
      } else {
        this.showComments();
      }
    },
    openRelatedPost(id) {
      this.goTo(this.appendDomain('/forum/details/'+id));
    },
    goToEdit() {
      var url = '/1.5/forum/webedit?web=true&id='+this.post._id;
      if (this.post.gid)
        url = url +'&gid='+this.post.gid;
      window.open(url,'_blank');
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    goToProperty() {
      if (vars.id && vars.src === 'property')
        return;
      if(this.post.src==='property') {
        var url = '/prop/details?id='+ this.post._id;
        window.open(url, '_blank');
      }
    },
    sortCmnt(cmnt1, cmnt2) {
      if (cmnt1.sticky && !cmnt2.sticky) return -1;
      if (!cmnt1.sticky && cmnt2.sticky) return 1;
      if (cmnt1.sticky && cmnt2.sticky)
        return (new Date(cmnt2.mt) - new Date(cmnt1.mt))
      if (this.cmntSortKey === 'ts') {
        if (this.cmntSortOrder === 'desc')
          return (new Date(cmnt2.ts) - new Date(cmnt1.ts))
        if (this.cmntSortOrder === 'asc')
          return (new Date(cmnt1.ts) - new Date(cmnt2.ts))
      }
    },
    reverseCmnt() {
      if (this.cmntSortOrder =='desc')
        this.cmntSortOrder ='asc'
      else
        this.cmntSortOrder ='desc'
      this.recent_cmnts.sort(this.sortCmnt);
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },
    showAllComments() {
      this.display_cmnts  = this.recent_cmnts;
    },
    CollapseComments() {
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },
    disableCmnt(disable) {
      var self = this;
      self.$http.post('/1.5/forum/disableCmnt/' + self.post._id, {discmnt:disable, city: self.post.city, prov: self.post.prov}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.discmnt = disable;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    pushPost() {
      var self = this;
      self.$http.post('/1.5/forum/pushNotify/', {id: self.post._id, tl: self.post.tl}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post_pn = 1;
            return window.bus.$emit('flash-message', ret.data.msg);
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    goTo(url) {
      window.location.href=url;
    },
    setDefaultPhoto(event) {
      if(/m.i.realmaster.com|img.realmaster.com/i.test(event.srcElement.src)) {
        event.onerror=null;
        event.srcElement.src='/img/no-pic.png';
        return true;
      } else if(/img.realmaster.cn/i.test(event.srcElement.src)) {
        event.srcElement.src=event.srcElement.src.replace('img.realmaster.cn','img.realmaster.com')
      } else {
        event.srcElement.src='/img/no-pic.png';
      }
    },
    onTagClicked(tag) {
      if (!this.wId) {
        var element = this.dispVar.globalTags.find(function(element){return element.key == tag;});
        if(this.displayPage === 'all' && !element) {
          this.closeForumView(null, tag)
        } else {
          window.location.href = '/1.5/forum/list?tag='+tag;
        }
      }
    },
    onCityTagClicked() {
      if (!this.wId)
        window.location.href = '/1.5/forum/list?src=post&city='+this.post.city + '&prov='+this.post.prov;
    },
    computeFav: function () {
      var self = this;
      if (!this.post.fav || !this.post.fav.length)
        return false;
      var fav = this.post.fav.find(function(_fav){
        return _fav._id == self.dispVar.sessionUser._id;
      })
      if (fav)
        return true;
      else return false;
    },
    editable() {
      return this.isForumAdmin || this.post.uid == this.dispVar.sessionUser._id + '';
    },

    closeForumView(newPostId, tag) {
      if(vars.src=='property') {
        window.location.href="/1.5/forum?city="+this.post.city;
      }
      toggleModal('forumViewModal');
      this.close();
      this.closeView();
      return window.bus.$emit('forum-view-close', {cmntl: this.post.cmntl, _id: this.post._id, vc: this.post.vc, cc: this.post.cc, newPostId: newPostId, tag:tag});
    },

    initPostDetail() {
      var self = this;
      self.post_sticky = self.post.sticky || false;
      self.post_fav = self.computeFav()|| false;
      self.post_pn = self.post.pn;
      self.recent_cmnts = [];
      self.display_cmnts = [];
      if (self.post.src=='news') {
        self.$nextTick(function () {
          self.formatNews(self.dispVar.isApp);
        });
      }

      if (!self.post.cmnts) return;

      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];
        element.idx = i-1;
      }

      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];

        if (element.ref>=0) {
          var refcmnt = self.post.cmnts.find(function(cmnt) {
            return cmnt.idx == element.ref;
          });
          if (refcmnt && refcmnt.m) {
            var m = refcmnt.m
            element.refContent  = m.length > 70 ? m.substr(0,100)+'...' : m;
            element.refUser  = refcmnt.fornm;
            element.refTs  = this.formatTs(refcmnt.ts);
          }
        }
        self.recent_cmnts.push(element);
      }
      self.recent_cmnts.sort(self.sortCmnt);
      self.display_cmnts = self.recent_cmnts.slice(0,19);
    },
    sticky(sticky) {
      var self = this;
      self.$http.post('/1.5/forum/sticky/' + self.post._id, {sticky:sticky, city: self.post.city, prov: self.post.prov}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.sticky = sticky;
            self.post_sticky = sticky;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    favourite(fav) {
      var self = this;
      self.$http.post('/1.5/forum/fav/' + self.post._id, {fav:fav}).then(
        function(ret) {
          if (!ret.data.ok)
            return window.bus.$emit('flash-message', ret.data.e);
          if (fav)
            self.post.fav.push(ret.data.fav);
          else {
            var favindex = self.post.fav.findIndex(function(_fav) {
              return _fav._id == self.dispVar.sessionUser._id;
            })
            self.post.fav.splice(favindex, 1);
          }

          self.post_fav = self.computeFav();
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    showComments(index) {
      if(!this.dispVar.isLoggedIn)
        return window.location.href = '/www/login';
      document.getElementById('commentTextarea').scrollIntoView();
      document.getElementById('commentTextarea').focus();
      if (index>=0) {
        this.referIdx = index;
      }
    },

    manageComment(params, cb) {
      var self = this;
      self.$http.post('/1.5/forum/cmnts/' + self.post._id, params).then(
        function(ret) {
          if (ret.data.ok)
            return cb(ret.data);
          else
            return window.bus.$emit('flash-message', ret.data.e);
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    deleteComment(des, index, action) {
      var self = this, delFlag;
      var params = {action:'del', index: index, city: self.post.city, prov: self.post.prov};
      if (action == 'delete') {
        params.del = des;
        delFlag = true;
      } else if (action == 'recover') {
        params.del = false;
        delFlag = false;
      }
      var des = des;
      self.manageComment(params, function(ret) {
        self.post.cmnts[index].del = des || false;
        self.post.cmntl = ret.cmntl;
        // self.initPostDetail();
        var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
        if (recent_cmnt_index>=0) self.recent_cmnts[recent_cmnt_index].del = des || false;
      });
    },
    deleteCmntConfirm(action, index) {
      event.stopPropagation();
      this.deleteMethod = this.deleteComment;
      var msg = this._(action + ' cmnt #') + (index + 1);
      var placeholder = this._('Enter delete reason');
      //add parentName for a workaround to not active the dialog in forumList.
      return window.bus.$emit('show-confirm-dialog', {id: index, parentName:'forumView', action: action, msg:msg, placeholder: placeholder});
    },

    stickyComment(index, sticky) {
      var self = this;
      var params = {action:'sticky', index: index, sticky: sticky, city: self.post.city, prov: self.post.prov};
      self.manageComment(params, function(ret) {
        self.post.cmnts[index].sticky = sticky;
        self.post.cmntl = ret.cmntl;
        var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
        if (recent_cmnt_index>=0) {
          self.recent_cmnts[recent_cmnt_index].sticky = sticky;
        }
      });
    },
    savePost(cb) {
      var self = this;
      self.$http.get('/1.5/forum/publish/'+this.post._id +'?src=property&tl='+this.post.tl+'&city='+this.post.city+'&prov='+this.post.prov+'&img='+vars.img).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            cb();
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function (err) {
          ajaxError(err);
        }
      );
    },
    saveNickName(nickname) {
      var self = this;
      self.dispVar.sessionUser.fornm = nickname;
      this.showNickNameMask = false;
      self.publishCmntConfirm();
    },
    cancelNickName() {
      this.showNickNameMask = false;
    },
    publishCmnt() {
      //set forum nickname
      var self = this;
      if (!self.comment || !self.comment.trim())
        return window.bus.$emit('flash-message', self._('Content needed'));
      if(self.dispVar.sessionUser.fornm) {
        self.publishCmntConfirm();
      } else {
        this.showNickNameMask = true;
        return window.bus.$emit('show-edit-nickname');
      }
    },
    addCmnt() {
      var self = this;
      if (!self.post.cmnts)
        self.post.cmnts=[];
      var cmnt_len = self.post.cmnts.length;
      if (cmnt_len > 1 && self.post.cmnts[cmnt_len-1].uid == self.dispVar.sessionUser._id && self.post.cmnts[cmnt_len-1].m == self.comment )
        return window.bus.$emit('flash-message', self._('Duplicated comments not allowed', 'forum'));
      var params = {action:'add', comment:self.comment, src: self.post.src};
      if (self.referIdx >= 0)
        params.ref = self.referIdx
      self.manageComment(params, function(ret) {
        self.post.cmnts = ret.cmnts;
        self.post.cmntl = ret.cmntl;
        self.initPostDetail();
        self.publishing = false;
        self.close();
      });
    },
    publishCmntConfirm() {
      var self = this;
      self.publishing = true;
      if (self.post.status === 'new') {
        this.savePost(self.addCmnt);
      } else {
        self.addCmnt();
      }
    },
    closeView() {
      this.relatedPosts = [];
      this.recent_cmnts = [];
      this.display_cmnts = [];
    },
    close() {
      this.showCommentDialog = false;
      this.showAuthorDialog = false;
      this.showMask = false;
      this.comment = '';
      this.referIdx = undefined;
      this.cmntsBottomHeight = 0;
      this.cmntsPublishBottomHeight = 0;
      this.cmntsMarginTop = 10;
      this.cmntsWidth=40;
      this.cmntsHeight=30;
      this.cmntsBorderRadius = 50;
      this.cmntsBarFooterHeight = 50;
      this.cmntFontSize = 10;
    },
    deletePostConfirm(action) {
      event.stopPropagation();
      this.showMask = true;
      this.deleteMethod = this.deletePost;
      var title = this.post.tl? this.post.tl.substring(0,10): '';
      var msg = this._(+ action + ' post?');
      if (action=='db_delete')
        msg= this._('delete post from db?');
      var placeholder = this._('Enter delete reason');
      return window.bus.$emit('show-confirm-dialog', {id: this.post._id, action: action, msg:msg, placeholder: placeholder});
    },
    deletePost(des, id, action) {
      var self = this;
      if (!id) return;
      var params={id:id, city: self.post.city, prov: self.post.prov}, delFlag;
      if (action == 'delete') {
        params.del = 'delete';
        params.del_des = des;
        delFlag = true;
      } else if (action == 'recover') {
        params.del = 'recover';
        delFlag = false;
      } else if (action == 'db_delete') {
        params.del = 'db_delete';
        delFlag = true;
      }
      self.$http.post('/1.5/forum/edit', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.post.del = delFlag;
            if (params.del == 'db_delete') {
              self.closeForumView();
            }
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function (err) {
          ajaxError(err);
        }
      );
    },
  },
  mounted () {
    // if link from property, open exist or create a new one.
    var self = this;
    self.$getTranslate(self);
    self.initPostDetail();
    window.bus.$on('wSignChange', function (d) {
      self.wSign = d;
    });
    window.bus.$on('wCommentChange', function (d) {
      self.wComment = d;
    });
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // window.bus.$on('pagedata-retrieved', function (d) {
    //   self.dispVar = Object.assign(self.dispVar, d);
    // });
    bus.$on('close-confirm-dialog', function(d) {
      self.showMask = false;
    });
    setTimeout(function() {
      self.$http.post('/1.5/forum/updateForumhist', {id: self.post._id}).then(
        function (ret) {},
        function (err) {
          ajaxError(err);
        }
      );
    }, 5000);
    this.userForm.src = window.location.href;
    this.rendered = true;
  },
  components: {
    FlashMessage,
    ConfirmWithMessage,
    ForumCmntCard,
    CreateNickname,
    UserBriefInfo,
    RedNavbar,
    FooterMultiline,
    SignUpForm
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
[v-cloak] {
  display: none;
}
.post-top-container {
  margin-bottom: 20px;
  position: relative;
}
.post-top-container img {
  width: 100%;
}
.post-top-container span {
  float: right;
}
.sign-form-container {
  position: absolute;
  z-index: 20;
  top: 200px;
  left: 400px;
}
pre {
  background-color: white;
  border:none;
}
.mainTopic {
  color:#E03131
}
.authorInfo {
  position: fixed;
  top: 100px;
  padding: 15px;
  z-index: 200;
  background-color: white;
  color: black;
  font-size: 15px;
  width: 90%;
  left: 5%;
}
.nickname-mask {
  z-index: 30px;
}
.download-button i {
    font-size: 22px;
    vertical-align: middle;
}
.download-button {
  margin: 30px 0;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  color: #fff!important;
  text-decoration: none;
  border-radius: 4px;
  background-color: #42c02e;
  height: 40px;
  display: block;
}
.tp-title {
  background-color: #E03131;
  padding: 10px;
  color: #fff;
}
.topic-content img {
  width: 50px;
  height: 50px;
}
.topic-content .content-right{
  width: calc(100% - 70px);
  float:right;
  height: 45px;
  padding-top:5px;
}
.topic-content {
  height: 70px;
  padding: 10px;
  border-bottom: 1px solid #dedada;
  width: 100%;
  background-color: #f2f2f2;
}
.post-topics{
  margin-top: 20px;
  margin-bottom: 20px;
}
.comments-header {
  border-bottom: 1px solid #ececec;
  display: table;
  width: 100%;
  color:#878787;
  height: 40px;
  padding-top: 20px;
}
.comments-header fa {
  line-height: inherit;
}
.bar-standard .btn{
  background-color: #E03131;
  color: white;
  font-size: 17px;
}
[v-cloak] {
  display: none;
}
.post-title {
  padding-bottom: 20px;
}
pre {
  white-space: pre-wrap;
}
#postViewContainer {
  top:40px;
  width: 100%;
  margin-bottom: 60px;
  padding:20px 10px 70px 10px;
  overflow: auto;
}
.post-author {
  width: 100%;
  height: 50px;
}
.post-img {
  width: 40px;
  float: left;
  border-radius: 40px;
  margin-right: 20px;
  height: 40px;
  background-size: 100% 100%;
  background-color: #eaebec;
}
.name {
  font-size: 17px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
  padding-right: 5px;
}
.ts {
  font-size: 12px;
  float: left;
  width:100%;
  color:#a9a3a3;
}

.post-tags {
  padding-bottom: 10px;
  padding-top: 10px;
}
.post-tags .btn {
  margin-top:10px;
  margin-right: 10px;
  border-radius: 20px;
  width: 120px;
}
#forumViewModal{
  z-index: 14;
  height: 100%;
  overflow: hidden;
}
#forumViewModal.active{
  transform: translate3d(0,0,0px);
}
.comment-dialog textarea {
  height: 100%;
  padding: 12px;
  border-radius: 0px;
}
.comment-dialog {
  position: fixed;
  top: 50%;
  width: 100%;
  z-index: 20;
  height: 100%;
}
.post-comments {
  width: 100%;
}
.post-comments .icon {
  float: right;
  padding:10px;
}
.sticky-comments{
  background-color: #fdfafb;
  margin-top:20px;
  margin-bottom: 20px;
}
.cross {
  text-decoration: line-through;
}
.deleted {
  background-color: #E03131;
  text-decoration: line-through;
}
.comment-input-div {
  padding: 7px;
  width: 50%;
  margin-top: 8px;
  border-radius: 60px;
  float: left;
  border: 1px solid;
  border-color: #a29a9a;
  font-size: 14px;
  height: 35px;
  background-color: white;
}
#recent_cmnts_container{
  min-height: 20px;
  overflow-y: hidden;
  padding-bottom: 10px;
}
#shareDialog #share-content{
  bottom: 0px !important;
}
.hide {
  display: none;
}
.post-photo{
  padding-top:20px;
}
.post-photo img {
  max-width: 100%;
  max-height: 500px;
}
.img-wrapper {
}
.mask {
  background: rgba(0,0,0,0.8);
  opacity: 0.9;
  z-index: 18;
}
.comment-dialog-footer {
  z-index: 20;
}
#commentTextarea {
  padding-top:5px;
  font-size: 14px;
  resize: none;
  height: 180px;
  width: 100%;
  border: 1px solid #efefef;
}
.wDlBottom {
  bottom: 50px;
}
.post-tags-admin .btn {
  background-color: #E03131;
  color: white;
}
.post-content {
  position: relative;
  background-color: #fff;
  font-size: 16px;
  word-wrap: break-word;
  width: 100%;
  padding: 10px;
}
.post-content img {
  max-width: 100% !important;
}
.post-content iframe {
  max-width: 100% !important;
}
.comment-dialog-footer span {
  padding: 10px 10px 20px 10px;
}
.fa-long-arrow-down, .fa-long-arrow-up {
  padding-top: 2px;
}

.img-wrapper .search {
  background-color: rgba(0,0,0,.2);
  position: relative;
  right: 10px;
  padding: 5px 20px;
  border-radius: 20px;
  bottom: 50px;
  width: 115px;
  float: right;
}
.img-wrapper .search .details {
  display: inline;
  float: left;
  padding-right: 10px;
  padding-top: 2px;
  color: white;
}
.img-wrapper .search .fa {
  display: inline;
  color: #fff;
  font-size:24px;
}
.bar-footer .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
.bar-footer a {
  text-align: center;
  vertical-align: middle;
  height: 50px;
  cursor: pointer;
  float: right;
}
.comment-number {
  font-size: 9px;
  position: absolute;
  border: none;
  padding: 1px 1px;
  width: 15px;
  margin-left: -6px;
  border-radius: 5px;
  color: white !important;
  background: #e03131;
}
.col-sm-3 a{
  width: 48%;
  margin-right: 2px;
}

@media screen and (width: 768px){
  .col-sm-3 a{
    padding-left: 5px;
  }
}
@media screen and (max-width: 767px) {
  .col-sm-3 a {
    margin-top: 10px;
  }
}
</style>
