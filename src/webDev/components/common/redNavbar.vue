<template>
  <span></span>
  <!-- <nav>
    <div class="container-fluid" id="navbar">
      <div>
        <div class="logo">
          <a :href="'http://'+dispVar.reqHost">
            <img src="/web/imgs/logo2.png" width="100" alt="RealMaster">
          </a>
        </div>
        <div class="sub-left-bar">
          <a class="top-right-banner-bar-element" :class="{active: curmenu=='resale'}" @click="goToPropList()">{{_('RESALE','web-menu')}}</a>
          <a class="top-right-banner-bar-element" :class="{active: curmenu=='project'}" :href="'http://'+dispVar.reqHost+'/projects/list?listMode=new'">{{_('PROJECTS','web-menu')}}</a>
          <a class="top-right-banner-bar-element" @click="goToPropList('assignment')" :class="{active: curmenu=='assignment'}" >{{_('ASSIGNMENTS','web-menu')}}</a>
          <a class="top-right-banner-bar-element" @click="goToPropList('exlisting')" :class="{active: curmenu=='exlisting'}" >{{_('EXCLUSIVE','web-menu')}}</a>
          <a class="top-right-banner-bar-element" :class="{active: curmenu=='forum'}" :href="'http://'+dispVar.reqHost+'/forum/list?1=1'">{{_('FORUM','web-menu')}}</a>
          <a class="top-right-banner-bar-element" :class="{active: curmenu=='news'}" :href="'http://'+dispVar.reqHost+'/wpnews'">{{_('BLOG','web-menu')}}</a>
        </div>
        <div class="sub-right-bar">
          <a v-show="dispVar.isLoggedIn" class="top-right-banner-bar-element" @click="logout">{{_('LOGOUT','web-menu')}}</a>
          <a v-show="!dispVar.isLoggedIn" :class="{active: curmenu=='signup'}" class="top-right-banner-bar-element" @click="showSignup">{{_('SIGN UP','web-menu')}}</a>
          <a v-show="!dispVar.isLoggedIn" :class="{active: curmenu=='login'}" class="top-right-banner-bar-element" @click="showLogin">{{_('LOGIN','web-menu')}}</a>
          <a v-show="dispVar.lang=='en'" class="top-right-banner-bar-element" @click="updateLang('zh-cn')">中文</a>
          <a v-show="dispVar.lang!='en'" class="top-right-banner-bar-element" @click="updateLang('en')">ENGLISH</a>
          <a class="top-right-banner-bar-element"  :href="'http://'+dispVar.reqHost+'/app-download?lang='+dispVar.lang"  target="_blank">{{_('APP','web-menu')}}</a>
          <a v-show="dispVar.webBackEnd" class="btn btn-primary top-right-banner-bar-element" href="/index" target="_blank">{{_('VIP Console','web-menu')}}</a>
        </div>
      </div>
    </div>
  </nav> -->
</template>

<script>
import pagedata_mixins from '../../../coffee4client/components/pagedata_mixins.js'

export default{
  mixins: [pagedata_mixins],
  props:{
    dispVar: {
      type:Object,
      default:function () {
        return {
          isLoggedIn:  false,
          lang: '',
          isVipUser: false,
          webBackEnd: false,
          reqHost:'',
          isCip:false,
          userCity:{city:'Toronto'}
        };
      }
    },
    popUpLogin:{
      type:Boolean,
      default:false
    },
    curmenu: {
      type:String,
      default: ''
    },
  },
  data(){
    return {
      datas:[
        'isLoggedIn',
        'lang',
        'isVipUser',
        'webBackEnd',
        'reqHost',
        'isCip',
        'hideDownloadAppInWeb'
      ]
    }
  },
  mounted: function() {
    var self = this;
    if(!self.dispVar.lang) {
      self.getPageData(self.datas, {}, true);
      window.bus.$on('pagedata-retrieved', function (d) {
        self.dispVar = Object.assign(self.dispVar, d);
      });
    }

  },
  methods:{
    goToPropList: function (type) {
      var city = 'Toronto';
      var prov = 'ON'
      if (this.dispVar.userCity && this.dispVar.userCity.city) {
        city = this.dispVar.userCity.city;
        prov = this.dispVar.userCity.prov;
      }
      var url = '/prop/list/saletp=Sale.ptype=Residential.city=' + city + '.prov=' + prov;
      if (type) {
        url +='.src=rm.ltp='+type;
      } else {
        url +='.mlsonly=1';
      }
      document.location = url;
    },
    showLogin: function(){
      // if (this.popUpLogin && !this.dispVar.isCip)
      //   window.bus.$emit('showLogin');
      // else
      // 文件已删除
      document.location = "/web/login.html";
    },
    showSignup: function(){
      // if (this.popUpLogin && !this.dispVar.isCip)
      //   window.bus.$emit('showSignup');
      // else
      // 文件已删除
      document.location = "/web/register.html";
    },
    updateLang:function(lang) {
      var url = document.location.href;
      var connect = '';
      var prefix = url;
      if (url.indexOf('prop/list')>=0) {
        connect = '.'
      } else if(url.indexOf('?')>=0){
        connect = '&'
      } else {
        prefix = url + '?1=1'
        connect = '&'
      }
      if(/lang/.test(url)) {
        var arr = url.split(connect);
        arr = arr.filter(function(el){
          return el.indexOf('lang')<0;
        });
        prefix = arr.join(connect);
      }
      document.location = prefix+connect+'lang=' + lang;
    },
    logout: function() {
      this.$http.post('/logout',{}).then(
        function(ret) {
          if (ret.data.ok == 1) {
            setTimeout(function() {
              window.location.href = "/";
            }, 2000);
          }
        });
    }
  }
}
</script>
